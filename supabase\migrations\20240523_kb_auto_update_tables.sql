-- Add auto-update fields to kb_articles
ALTER TABLE kb_articles 
ADD COLUMN IF NOT EXISTS last_auto_update TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS auto_update_enabled BOOLEAN DEFAULT true;

-- Create feedback statistics function
CREATE OR REPLACE FUNCTION get_article_feedback_stats(
  p_article_id UUID,
  p_days INTEGER DEFAULT 7
)
RETURNS TABLE (
  total_count INTEGER,
  helpful_count INTEGER,
  not_helpful_count INTEGER,
  outdated_count INTEGER,
  incorrect_count INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_count,
    COUNT(*) FILTER (WHERE feedback_type = 'helpful')::INTEGER as helpful_count,
    COUNT(*) FILTER (WHERE feedback_type = 'not_helpful')::INTEGER as not_helpful_count,
    COUNT(*) FILTER (WHERE feedback_type = 'outdated')::INTEGER as outdated_count,
    COUNT(*) FILTER (WHERE feedback_type = 'incorrect')::INTEGER as incorrect_count
  FROM kb_article_feedback
  WHERE article_id = p_article_id
    AND created_at >= NOW() - INTERVAL '1 day' * p_days;
END;
$$;

-- Create update logs table
CREATE TABLE IF NOT EXISTS kb_update_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  update_type TEXT CHECK (update_type IN ('auto', 'feedback', 'scheduled', 'manual')),
  changes_made TEXT[],
  confidence_score NUMERIC(3,2),
  approved BOOLEAN DEFAULT false,
  approved_by UUID REFERENCES staff(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create update queue table for manual review
CREATE TABLE IF NOT EXISTS kb_update_queue (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  suggested_updates JSONB NOT NULL,
  confidence_score NUMERIC(3,2),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'applied')),
  reviewed_by UUID REFERENCES staff(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create content gaps table
CREATE TABLE IF NOT EXISTS kb_content_gaps (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  topic TEXT NOT NULL,
  query_count INTEGER NOT NULL,
  suggested_category TEXT,
  priority TEXT CHECK (priority IN ('high', 'medium', 'low')),
  status TEXT DEFAULT 'identified' CHECK (status IN ('identified', 'in_progress', 'resolved', 'dismissed')),
  assigned_to UUID REFERENCES staff(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- Create function to update article embeddings
CREATE OR REPLACE FUNCTION update_article_embeddings(p_article_id UUID)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- This will be called by the edge function after content updates
  -- The actual embedding generation happens in the edge function
  UPDATE kb_articles 
  SET updated_at = NOW()
  WHERE id = p_article_id;
END;
$$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_kb_update_logs_article_id ON kb_update_logs(article_id);
CREATE INDEX IF NOT EXISTS idx_kb_update_logs_created_at ON kb_update_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_kb_update_queue_status ON kb_update_queue(status);
CREATE INDEX IF NOT EXISTS idx_kb_update_queue_article_id ON kb_update_queue(article_id);
CREATE INDEX IF NOT EXISTS idx_kb_content_gaps_status ON kb_content_gaps(status);
CREATE INDEX IF NOT EXISTS idx_kb_content_gaps_priority ON kb_content_gaps(priority);

-- Add RLS policies
ALTER TABLE kb_update_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_update_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_content_gaps ENABLE ROW LEVEL SECURITY;

-- Update logs visible to admins and helpdesk support
CREATE POLICY "view_update_logs" ON kb_update_logs
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Update queue manageable by admins and helpdesk support
CREATE POLICY "manage_update_queue" ON kb_update_queue
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Content gaps manageable by admins and helpdesk support
CREATE POLICY "manage_content_gaps" ON kb_content_gaps
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );
