"use strict";exports.id=5690,exports.ids=[5690],exports.modules={55131:(e,a,t)=>{t.d(a,{A:()=>c});var s=t(45512);t(58009);var r=t(79334),n=t(43433),l=t(22417),i=t(97643),d=t(86235);function c({children:e,requiredRole:a,requiredPermission:t,fallback:c}){let{user:o,loading:m}=(0,n.A)(),{checkPermission:h}=(0,l.S)();return((0,r.useRouter)(),m)?(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{children:"読み込み中... / Loading..."})]})})})}):o?t&&!h(t.resource,t.action,t.scope)?c||(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"アクセス拒否 / Access Denied"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["このページにアクセスする権限がありません。",(0,s.jsx)("br",{}),"You don't have permission to access this page."]})]})})}):(0,s.jsx)(s.Fragment,{children:e}):null}},97971:(e,a,t)=>{t.d(a,{Q:()=>A});var s=t(45512),r=t(58009),n=t(97643),l=t(25409),i=t(87021),d=t(666),c=t(77252),o=t(69193),m=t(75339),h=t(39154),g=t(37133),u=t(55511);let x=process.env.OPENAI_API_KEY;class p{constructor(){}static getInstance(){return p.instance||(p.instance=new p),p.instance}async generateEmbedding(e){if(!x)throw Error("OpenAI API key is not configured");let a=this.preprocessText(e);try{let e=await fetch("https://api.openai.com/v1/embeddings",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${x}`},body:JSON.stringify({model:"text-embedding-ada-002",input:a})});if(!e.ok){let a=await e.json();throw Error(`OpenAI API error: ${a.error?.message||"Unknown error"}`)}let t=await e.json();return{embedding:t.data[0].embedding,model:t.model,usage:t.usage}}catch(e){throw console.error("Error generating embedding:",e),e}}async generateArticleEmbeddings(e,a,t){try{let[s,r]=await Promise.all([this.generateEmbedding(a),this.generateEmbedding(t)]),n=this.calculateContentHash(a),l=this.calculateContentHash(t);await Promise.all([this.storeEmbedding({article_id:e,language:"en",embedding:s.embedding,content_hash:n,model_version:s.model}),this.storeEmbedding({article_id:e,language:"jp",embedding:r.embedding,content_hash:l,model_version:r.model})])}catch(e){throw console.error("Error generating article embeddings:",e),e}}async storeEmbedding(e){let{error:a}=await g.N.from("kb_embeddings").upsert({article_id:e.article_id,language:e.language,embedding:`[${e.embedding.join(",")}]`,content_hash:e.content_hash,model_version:e.model_version,updated_at:new Date().toISOString()},{onConflict:"article_id,language"});if(a)throw console.error("Error storing embedding:",a),Error(`Failed to store embedding: ${a.message}`)}async semanticSearch(e,a,t={}){let{matchThreshold:s=.7,matchCount:r=10}=t;try{let t=await this.generateEmbedding(e),{data:n,error:l}=await g.N.rpc("kb_semantic_search",{query_embedding:`[${t.embedding.join(",")}]`,search_language:a,match_threshold:s,match_count:r});if(l)throw console.error("Semantic search error:",l),Error(`Search failed: ${l.message}`);return n||[]}catch(e){throw console.error("Error performing semantic search:",e),e}}async updateStaleEmbeddings(){try{let{data:e,error:a}=await g.N.from("kb_articles").select(`
          id,
          content_en,
          content_jp,
          kb_embeddings!inner(
            article_id,
            language,
            content_hash
          )
        `).eq("status","published");if(a)throw Error(`Failed to fetch articles: ${a.message}`);if(!e||0===e.length){console.log("No articles to update");return}let t=[];for(let a of e){let e=this.calculateContentHash(a.content_en),s=this.calculateContentHash(a.content_jp),r=a.kb_embeddings.find(e=>"en"===e.language),n=a.kb_embeddings.find(e=>"jp"===e.language);r&&r.content_hash===e||t.push(this.generateAndStoreEmbedding(a.id,a.content_en,"en")),n&&n.content_hash===s||t.push(this.generateAndStoreEmbedding(a.id,a.content_jp,"jp"))}t.length>0?(await Promise.all(t),console.log(`Updated ${t.length} embeddings`)):console.log("All embeddings are up to date")}catch(e){throw console.error("Error updating stale embeddings:",e),e}}async generateAndStoreEmbedding(e,a,t){let s=await this.generateEmbedding(a),r=this.calculateContentHash(a);await this.storeEmbedding({article_id:e,language:t,embedding:s.embedding,content_hash:r,model_version:s.model})}preprocessText(e){let a=e.replace(/\s+/g," ").trim();return(a=a.replace(/[\u0000-\u001F\u007F-\u009F]/g,"")).length>3e4&&(a=a.substring(0,3e4)+"..."),a}calculateContentHash(e){return(0,u.createHash)("sha256").update(e).digest("hex")}async findRelatedArticles(e,a,t=5){try{let{data:s,error:r}=await g.N.from("kb_embeddings").select("embedding").eq("article_id",e).eq("language",a).single();if(r||!s)throw Error("Article embedding not found");let{data:n,error:l}=await g.N.rpc("kb_semantic_search",{query_embedding:s.embedding,search_language:a,match_threshold:.5,match_count:t+1});if(l)throw Error(`Related articles search failed: ${l.message}`);let i=(n||[]).filter(a=>a.article_id!==e);if(i.length>0){let a=i.map(a=>({article_id:e,related_article_id:a.article_id,relevance_score:a.similarity,relation_type:"similar"}));await g.N.from("kb_related_articles").upsert(a,{onConflict:"article_id,related_article_id"})}return i.slice(0,t)}catch(e){throw console.error("Error finding related articles:",e),e}}}p.getInstance();var f=t(4269),j=t(16873),N=t(39094),b=t(6841),v=t(4643),_=t(61075),w=t(42163),y=t(10617);function A({context:e,onArticleClick:a,className:t}){let{language:c}=(0,h.ok)(),[u,x]=(0,r.useState)(""),[_,w]=(0,r.useState)(!1),[y,A]=(0,r.useState)([]),[k,C]=(0,r.useState)([]),[R,F]=(0,r.useState)([]),[q,T]=(0,r.useState)([]),[I,P]=(0,r.useState)("contextual"),H=p.getInstance(),L=async()=>{if(u.trim()){w(!0);try{let e=await H.semanticSearch(u,{matchThreshold:.7,matchCount:10});A(e),P("search")}catch(e){console.error("Search error:",e)}finally{w(!1)}}},$=e=>{a?a(e):window.open(`/knowledge-base/article/${e.id}`,"_blank")},D=async e=>{await g.N.from("chatbot_faq").update({usage_count:1}).eq("id",e)};return(0,s.jsxs)(n.Zp,{className:t,children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),"ja"===c?"ナレッジベース":"Knowledge Base"]})}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsxs)("div",{className:"flex gap-2 mb-4",children:[(0,s.jsx)(l.p,{placeholder:"ja"===c?"検索...":"Search...",value:u,onChange:e=>x(e.target.value),onKeyPress:e=>"Enter"===e.key&&L(),className:"flex-1"}),(0,s.jsx)(i.$,{onClick:L,disabled:_,size:"icon",children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)(o.tU,{value:I,onValueChange:P,children:[(0,s.jsxs)(o.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(o.Xi,{value:"contextual",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-1"}),"ja"===c?"関連":"Related"]}),(0,s.jsxs)(o.Xi,{value:"faq",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"FAQ"]}),(0,s.jsxs)(o.Xi,{value:"recent",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"ja"===c?"最新":"Recent"]}),(0,s.jsxs)(o.Xi,{value:"search",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"ja"===c?"検索":"Search"]})]}),(0,s.jsx)(o.av,{value:"contextual",children:(0,s.jsx)(d.F,{className:"h-[300px] mt-4",children:k.length>0?(0,s.jsx)("div",{className:"space-y-2",children:k.map(e=>(0,s.jsx)(E,{article:e,language:c,onClick:()=>$(e)},e.id))}):(0,s.jsx)(m.Fc,{children:(0,s.jsx)(m.TN,{children:"ja"===c?"現在のコンテキストに関連する記事はありません。":"No articles related to current context."})})})}),(0,s.jsx)(o.av,{value:"faq",children:(0,s.jsx)(d.F,{className:"h-[300px] mt-4",children:R.length>0?(0,s.jsx)("div",{className:"space-y-3",children:R.map(e=>(0,s.jsx)(S,{faq:e,language:c,onClick:()=>D(e.id)},e.id))}):(0,s.jsx)(m.Fc,{children:(0,s.jsx)(m.TN,{children:"ja"===c?"よくある質問はまだありません。":"No FAQs available yet."})})})}),(0,s.jsx)(o.av,{value:"recent",children:(0,s.jsx)(d.F,{className:"h-[300px] mt-4",children:(0,s.jsx)("div",{className:"space-y-2",children:q.map(e=>(0,s.jsx)(E,{article:e,language:c,onClick:()=>$(e)},e.id))})})}),(0,s.jsx)(o.av,{value:"search",children:(0,s.jsx)(d.F,{className:"h-[300px] mt-4",children:y.length>0?(0,s.jsx)("div",{className:"space-y-2",children:y.map(e=>(0,s.jsx)(E,{article:e,language:c,onClick:()=>$(e),showRelevance:!0},e.id))}):(0,s.jsx)(m.Fc,{children:(0,s.jsx)(m.TN,{children:"ja"===c?"検索結果がありません。":"No search results found."})})})})]})]})]})}function E({article:e,language:a,onClick:t,showRelevance:r=!1}){let l="ja"===a?e.title_jp:e.title_en,i=("ja"===a?e.content_jp:e.content_en).substring(0,100)+"...";return(0,s.jsx)(n.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:t,children:(0,s.jsx)(n.Wu,{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("h4",{className:"font-medium text-sm flex items-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4"}),l]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:i}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)(c.E,{variant:"outline",className:"text-xs",children:e.category}),r&&e.relevance&&(0,s.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:[Math.round(100*e.relevance),"% match"]})]})]}),(0,s.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"})]})})})}function S({faq:e,language:a,onClick:t}){let[l,i]=(0,r.useState)(!1),d="ja"===a?e.question_ja:e.question_en,o="ja"===a?e.answer_ja:e.answer_en;return(0,s.jsx)(n.Zp,{className:"cursor-pointer hover:shadow-sm transition-all",onClick:e=>{e.stopPropagation(),i(!l),l||t()},children:(0,s.jsx)(n.Wu,{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 text-primary mt-0.5"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-sm",children:d}),l&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:o}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)(c.E,{variant:"outline",className:"text-xs",children:e.category}),e.usage_count>0&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[(0,s.jsx)(y.A,{className:"h-3 w-3"}),e.usage_count]})]})]})]})})})}},75339:(e,a,t)=>{t.d(a,{Fc:()=>d,TN:()=>o,XL:()=>c});var s=t(45512),r=t(58009),n=t(21643),l=t(59462);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef(({className:e,variant:a,...t},r)=>(0,s.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(i({variant:a}),e),...t}));d.displayName="Alert";let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...a}));c.displayName="AlertTitle";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...a}));o.displayName="AlertDescription"},71965:(e,a,t)=>{t.d(a,{Cf:()=>m,L3:()=>g,c7:()=>h,lG:()=>d,rr:()=>u});var s=t(45512),r=t(58009),n=t(27553),l=t(59462),i=t(80986);let d=n.bL;n.l9;let c=n.ZL;n.bm;let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(n.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));o.displayName=n.hJ.displayName;let m=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(c,{children:[(0,s.jsx)(o,{}),(0,s.jsxs)(n.UC,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.MKb,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=n.UC.displayName;let h=({className:e,...a})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});h.displayName="DialogHeader";let g=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(n.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));g.displayName=n.hE.displayName;let u=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(n.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));u.displayName=n.VY.displayName},47699:(e,a,t)=>{t.d(a,{J:()=>c});var s=t(45512),r=t(58009),n=t(92405),l=t(21643),i=t(59462);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(n.b,{ref:t,className:(0,i.cn)(d(),e),...a}));c.displayName=n.b.displayName},3328:(e,a,t)=>{t.d(a,{k:()=>i});var s=t(45512),r=t(58009),n=t(41022),l=t(59462);let i=r.forwardRef(({className:e,value:a,...t},r)=>(0,s.jsx)(n.bL,{ref:r,className:(0,l.cn)("relative h-2 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:(0,s.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})}));i.displayName=n.bL.displayName},39154:(e,a,t)=>{t.d(a,{ok:()=>n}),t(45512);var s=t(58009);let r=(0,s.createContext)(void 0);function n(){let e=(0,s.useContext)(r);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}},22417:(e,a,t)=>{t.d(a,{S:()=>n});var s=t(43433),r=t(58957);function n(){let{userRole:e,staff:a}=(0,s.A)(),t=(a,t,s)=>!!e&&(0,r.H1)(e,a,t,s),n=()=>e===r.gG.GLOBAL_ADMIN,l=()=>e===r.gG.SYSTEM_ADMIN;return{checkPermission:t,checkDepartmentAccess:t=>!!e&&!!a&&(0,r.Z6)(e,a.division_id,t),isGlobalAdmin:n,isSystemAdmin:l,isDepartmentAdmin:()=>e===r.gG.DEPT_ADMIN,isHRStaff:()=>e===r.gG.HR_STAFF,isITSupport:()=>e===r.gG.IT_SUPPORT,isRegularUser:()=>e===r.gG.REGULAR_USER,canManageUsers:()=>t("users","create")||t("users","update"),canViewAllRequests:()=>t("requests","read","all"),canCreateRequests:()=>t("requests","create"),canAccessHRFunctions:()=>t("hr_requests","create")||t("hr_requests","read"),canAccessSystemSettings:()=>n()||l()}}}};