"use client";

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X } from 'lucide-react';

interface User {
  id: string;
  email: string;
  full_name?: string;
}

interface UserSearchInputProps {
  onUserSelect: (user: User | null) => void;
  placeholder?: string;
  initialValue?: string;
}

export function UserSearchInput({
  onUserSelect,
  placeholder = 'メールアドレスまたは名前で検索',
  initialValue = '',
}: UserSearchInputProps) {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  useEffect(() => {
    if (searchTerm.length < 2) {
      setSearchResults([]);
      return;
    }

    const delaySearch = setTimeout(async () => {
      setIsSearching(true);
      try {
        // This would typically call your API
        // For now, we'll just simulate a search
        const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchTerm)}`);
        if (response.ok) {
          const data = await response.json();
          setSearchResults(data.users || []);
        }
      } catch (error) {
        console.error('Error searching users:', error);
      } finally {
        setIsSearching(false);
      }
    }, 300);

    return () => clearTimeout(delaySearch);
  }, [searchTerm]);

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    setSearchTerm(user.email);
    setSearchResults([]);
    onUserSelect(user);
  };

  const clearSelection = () => {
    setSelectedUser(null);
    setSearchTerm('');
    onUserSelect(null);
  };

  return (
    <div className="relative">
      <div className="flex">
        <div className="relative flex-grow">
          <Input
            type="text"
            placeholder={placeholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-8"
          />
          {selectedUser && (
            <button
              type="button"
              onClick={clearSelection}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <X className="h-4 w-4 text-gray-400" />
            </button>
          )}
        </div>
        <Button
          type="button"
          variant="outline"
          size="icon"
          className="ml-2"
          disabled={isSearching}
        >
          <Search className="h-4 w-4" />
        </Button>
      </div>

      {searchResults.length > 0 && !selectedUser && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border">
          <ul className="py-1 max-h-60 overflow-auto">
            {searchResults.map((user) => (
              <li
                key={user.id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => handleUserSelect(user)}
              >
                <div className="font-medium">{user.full_name || 'Unknown'}</div>
                <div className="text-sm text-gray-500">{user.email}</div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
