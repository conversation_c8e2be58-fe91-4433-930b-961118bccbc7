// Japanese UI Test Page
'use client';

import { LanguageProvider } from '@/lib/i18n/language-provider';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { LocalizedRequestForm } from '@/components/forms/localized-request-form';
import { useLanguage } from '@/lib/i18n/language-provider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatJapaneseDate, isJapaneseBusinessDay } from '@/lib/i18n/config';

function TestContent() {
  const { t, locale } = useLanguage();
  const today = new Date();
  const isBusinessDay = isJapaneseBusinessDay(today);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{t('common.appName')}</h1>
        <LanguageSwitcher />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('dashboard.overview')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground">
              {locale === 'ja' ? '現在の言語' : 'Current Language'}: 
              <span className="font-semibold ml-2">{locale === 'ja' ? '日本語' : 'English'}</span>
            </p>
          </div>
          
          <div>
            <p className="text-sm text-muted-foreground">
              {locale === 'ja' ? '今日の日付' : "Today's Date"}:
              <span className="font-semibold ml-2">
                {locale === 'ja' ? formatJapaneseDate(today) : today.toLocaleDateString('en-US')}
              </span>
            </p>
          </div>

          <div>
            <p className="text-sm text-muted-foreground">
              {locale === 'ja' ? '営業日' : 'Business Day'}:
              <span className={`font-semibold ml-2 ${isBusinessDay ? 'text-green-600' : 'text-red-600'}`}>
                {isBusinessDay 
                  ? (locale === 'ja' ? '営業日' : 'Business Day')
                  : (locale === 'ja' ? '休日' : 'Holiday/Weekend')
                }
              </span>
            </p>
          </div>
        </CardContent>
      </Card>

      <LocalizedRequestForm />
    </div>
  );
}

export default function JapaneseTestPage() {
  return (
    <LanguageProvider>
      <TestContent />
    </LanguageProvider>
  );
}