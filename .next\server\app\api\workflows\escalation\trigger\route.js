"use strict";(()=>{var e={};e.id=2349,e.ids=[2349],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},97437:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>_,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var i={};a.r(i);var s={};a.r(s),a.d(s,{POST:()=>h});var r=a(42706),n=a(28203),o=a(45994),l=a(39187);class c{constructor(e,t,a,i,s){this.monitoringInterval=null,this.supabase=e,this.notificationService=t,this.auditService=a,this.workflowEngine=i,this.slaManager=s}async startMonitoring(e=5){console.log(`Starting escalation monitoring with ${e} minute interval`),this.monitoringInterval&&clearInterval(this.monitoringInterval),await this.checkEscalationTriggers(),this.monitoringInterval=setInterval(async()=>{await this.checkEscalationTriggers()},6e4*e)}stopMonitoring(){this.monitoringInterval&&(clearInterval(this.monitoringInterval),this.monitoringInterval=null,console.log("Escalation monitoring stopped"))}async checkEscalationTriggers(){try{console.log("Checking escalation triggers...");let{data:e,error:t}=await this.supabase.from("escalation_rules").select("*").eq("is_active",!0);if(t)throw t;for(let t of e||[])await this.processEscalationRule(t);await this.retryFailedEscalations()}catch(e){console.error("Error checking escalation triggers:",e),await this.auditService.logActivity({userId:"system",action:"escalation_check_failed",resourceType:"escalation",details:{error:e.message}})}}async processEscalationRule(e){try{let t=[];switch(e.triggerType){case"sla_breach":t=await this.getSLABreachCandidates(e);break;case"overdue":t=await this.getOverdueCandidates(e);break;case"approval_timeout":t=await this.getApprovalTimeoutCandidates(e)}for(let a of t)await this.checkExistingEscalation(e.id,a.id)||await this.createEscalationInstance(e,a)}catch(t){console.error(`Error processing escalation rule ${e.id}:`,t)}}async getSLABreachCandidates(e){let t=await this.slaManager.getAtRiskItems(),a=[];for(let i of t)"breached"===i.status&&this.evaluateConditions(e.conditions,i)&&a.push(i);return a}async getOverdueCandidates(e){let{data:t,error:a}=await this.supabase.from("request_forms").select(`
        *,
        request_items(*),
        workflow_instances(*)
      `).in("status",["pending","in-progress"]).not("completed_at","is",null);if(a)throw a;let i=[],s=new Date;for(let a of t||[]){let t=new Date(a.created_at),r=(s.getTime()-t.getTime())/36e5,n={...a,age_hours:r,age_days:r/24};this.evaluateConditions(e.conditions,n)&&i.push(a)}return i}async getApprovalTimeoutCandidates(e){let{data:t,error:a}=await this.supabase.from("approval_chains").select(`
        *,
        approval_levels(*),
        workflow_instances(
          *,
          request_forms(*)
        )
      `).eq("status","pending").not("current_level","is",null);if(a)throw a;let i=[],s=new Date;for(let a of t||[]){let t=a.approval_levels?.find(e=>e.level===a.current_level);if(t?.timeout_hours){let r=new Date(new Date(a.updated_at));r.setHours(r.getHours()+t.timeout_hours),s>r&&this.evaluateConditions(e.conditions,a)&&i.push(a)}}return i}evaluateConditions(e,t){if(!e||0===e.length)return!0;let a=!0,i="AND";for(let s of e){let e=this.evaluateCondition(s,t);a="AND"===i?a&&e:a||e,i=s.logicalOperator||"AND"}return a}evaluateCondition(e,t){let a=this.getFieldValue(t,e.field);switch(e.operator){case"equals":return a===e.value;case"not_equals":return a!==e.value;case"greater_than":return Number(a)>Number(e.value);case"less_than":return Number(a)<Number(e.value);case"contains":return String(a).includes(String(e.value));case"age_hours":return t.age_hours>Number(e.value);case"age_days":return t.age_days>Number(e.value);default:return!1}}getFieldValue(e,t){let a=t.split("."),i=e;for(let e of a)if(void 0===(i=i?.[e]))break;return i}async checkExistingEscalation(e,t){let{data:a,error:i}=await this.supabase.from("escalation_instances").select("id").eq("rule_id",e).eq("request_id",t).in("status",["pending","in_progress"]).single();return!i&&!!a}async createEscalationInstance(e,t){try{let a={ruleId:e.id,workflowInstanceId:t.workflow_instance_id||t.id,requestId:t.request_id||t.id,triggeredAt:new Date,triggeredBy:"system",status:"pending",retryCount:0,actionResults:[]},{data:i,error:s}=await this.supabase.from("escalation_instances").insert(a).select().single();if(s)throw s;await this.executeEscalationActions(i,e,t),await this.auditService.logActivity({userId:"system",action:"escalation_triggered",resourceType:"escalation",resourceId:i.id,details:{rule:e.name,triggerType:e.triggerType,candidateId:t.id}})}catch(e){throw console.error("Error creating escalation instance:",e),e}}async executeEscalationActions(e,t,a){try{await this.updateEscalationStatus(e.id,"in_progress");let i=[];for(let s of t.actions)try{let t=await this.executeAction(s,a,e);i.push({actionType:s.type,status:"success",executedAt:new Date,details:t})}catch(e){console.error(`Error executing action ${s.type}:`,e),i.push({actionType:s.type,status:"failed",executedAt:new Date,error:e.message})}let s=i.every(e=>"success"===e.status);await this.updateEscalationInstance(e.id,{status:s?"completed":"failed",completedAt:s?new Date:null,actionResults:i,failureReason:s?null:"Some actions failed"})}catch(t){console.error("Error executing escalation actions:",t),await this.updateEscalationInstance(e.id,{status:"failed",failureReason:t.message})}}async executeAction(e,t,a){switch(e.type){case"notify":return await this.executeNotifyAction(e,t,a);case"reassign":return await this.executeReassignAction(e,t,a);case"change_priority":return await this.executeChangePriorityAction(e,t,a);case"add_approver":return await this.executeAddApproverAction(e,t,a);case"auto_approve":return await this.executeAutoApproveAction(e,t,a);case"auto_reject":return await this.executeAutoRejectAction(e,t,a);case"custom_webhook":return await this.executeWebhookAction(e,t,a);default:throw Error(`Unknown action type: ${e.type}`)}}async executeNotifyAction(e,t,a){for(let i of(await this.resolveRecipients(e.target,t)))await this.notificationService.sendNotification({userId:i.id,type:"escalation",title:"Request Escalated",message:e.parameters?.message||`Request ${t.id} has been escalated due to ${a.ruleId}`,data:{requestId:t.id,escalationId:a.id,priority:e.parameters?.priority||"high"},channels:["in_app","email"]})}async executeReassignAction(e,t,a){let i=await this.resolveRecipients(e.target,t);i.length>0&&(await this.supabase.from("request_forms").update({assigned_to:i[0].id,updated_at:new Date().toISOString()}).eq("id",t.id),await this.notificationService.sendNotification({userId:i[0].id,type:"assignment",title:"New Request Assignment",message:`You have been assigned request ${t.id} through escalation`,data:{requestId:t.id},channels:["in_app","email"]}))}async executeChangePriorityAction(e,t,a){let i=e.parameters?.priority||"high";await this.supabase.from("request_forms").update({priority:i,updated_at:new Date().toISOString()}).eq("id",t.id),await this.slaManager.updatePriority(t.id,i)}async executeAddApproverAction(e,t,a){let i=await this.resolveRecipients(e.target,t);if(i.length>0&&t.approval_chain_id){let{data:e}=await this.supabase.from("approval_chains").select("*, approval_levels(*)").eq("id",t.approval_chain_id).single();if(e&&e.current_level){let t=e.approval_levels.find(t=>t.level===e.current_level);if(t){let e=[...t.approver_ids,...i.map(e=>e.id)];await this.supabase.from("approval_levels").update({approver_ids:e,updated_at:new Date().toISOString()}).eq("id",t.id)}}}}async executeAutoApproveAction(e,t,a){t.approval_chain_id&&await this.workflowEngine.approveRequest(t.approval_chain_id,"system","Auto-approved due to escalation",{escalationId:a.id})}async executeAutoRejectAction(e,t,a){t.approval_chain_id&&await this.workflowEngine.rejectRequest(t.approval_chain_id,"system",e.parameters?.reason||"Auto-rejected due to escalation timeout",{escalationId:a.id})}async executeWebhookAction(e,t,a){if(!e.target)throw Error("Webhook URL not specified");let i={escalationId:a.id,ruleId:a.ruleId,context:t,timestamp:new Date().toISOString(),...e.parameters},s=await fetch(e.target,{method:"POST",headers:{"Content-Type":"application/json","X-ITSync-Escalation":a.id},body:JSON.stringify(i)});if(!s.ok)throw Error(`Webhook failed: ${s.status} ${s.statusText}`)}async resolveRecipients(e,t){if(!e)return[];if(e.startsWith("user:")){let t=e.substring(5),{data:a}=await this.supabase.from("staff").select("*").eq("id",t).single();return a?[a]:[]}if(e.startsWith("role:")){let t=e.substring(5),{data:a}=await this.supabase.from("staff").select("*").eq("role",t);return a||[]}if(e.startsWith("department:")){let t=e.substring(11),{data:a}=await this.supabase.from("staff").select("*").eq("department_id",t);return a||[]}if("manager"===e){let e=t.requester_id||t.submitted_by;if(e){let{data:t}=await this.supabase.from("staff").select("department_id").eq("id",e).single();if(t?.department_id){let{data:e}=await this.supabase.from("staff").select("*").eq("department_id",t.department_id).eq("role","Department Administrator");return e||[]}}}return e.includes("@")?[{id:null,email:e}]:[]}async retryFailedEscalations(){let{data:e,error:t}=await this.supabase.from("escalation_instances").select(`
        *,
        escalation_rules(*)
      `).eq("status","failed").lt("retry_count",3);if(!t&&e)for(let t of e){let e=t.escalation_rules;if(!e?.retryInterval)continue;let a=new Date(new Date(t.last_retry_at?t.last_retry_at:t.triggered_at));a.setMinutes(a.getMinutes()+e.retryInterval),new Date>=a&&await this.retryEscalation(t,e)}}async retryEscalation(e,t){try{console.log(`Retrying escalation ${e.id}`),await this.updateEscalationInstance(e.id,{retryCount:e.retryCount+1,lastRetryAt:new Date,status:"in_progress"});let{data:a}=await this.supabase.from("request_forms").select("*").eq("id",e.requestId).single();a&&await this.executeEscalationActions(e,t,a)}catch(t){console.error(`Error retrying escalation ${e.id}:`,t)}}async updateEscalationStatus(e,t){await this.supabase.from("escalation_instances").update({status:t,updated_at:new Date().toISOString()}).eq("id",e)}async updateEscalationInstance(e,t){await this.supabase.from("escalation_instances").update({...t,updated_at:new Date().toISOString()}).eq("id",e)}async triggerManualEscalation(e,t,a,i){try{let{data:s,error:r}=await this.supabase.from("escalation_rules").select("*").eq("id",t).single();if(r||!s)throw Error("Escalation rule not found");let{data:n,error:o}=await this.supabase.from("request_forms").select("*").eq("id",e).single();if(o||!n)throw Error("Request not found");let l={ruleId:t,workflowInstanceId:n.workflow_instance_id||n.id,requestId:e,triggeredAt:new Date,triggeredBy:a,status:"pending",retryCount:0,actionResults:[]},{data:c,error:u}=await this.supabase.from("escalation_instances").insert(l).select().single();if(u)throw u;await this.executeEscalationActions(c,s,n),await this.auditService.logActivity({userId:a,action:"manual_escalation",resourceType:"escalation",resourceId:c.id,details:{requestId:e,ruleId:t,reason:i}})}catch(e){throw console.error("Error triggering manual escalation:",e),e}}async getEscalationHistory(e){let{data:t,error:a}=await this.supabase.from("escalation_instances").select(`
        *,
        escalation_rules(*)
      `).eq("request_id",e).order("triggered_at",{ascending:!1});if(a)throw a;return t||[]}async getActiveEscalations(){let{data:e,error:t}=await this.supabase.from("escalation_instances").select(`
        *,
        escalation_rules(*),
        request_forms(*)
      `).in("status",["pending","in_progress"]).order("triggered_at",{ascending:!1});if(t)throw t;return e||[]}}!function(){var e=Error("Cannot find module '@/lib/supabase/client'");throw e.code="MODULE_NOT_FOUND",e}();class u{constructor(){this.supabase=Object(function(){var e=Error("Cannot find module '@/lib/supabase/client'");throw e.code="MODULE_NOT_FOUND",e}())(),this.emailQueue=[],this.smsQueue=[],this.processingEmail=!1,this.processingSms=!1,this.startNotificationProcessing()}static getInstance(){return u.instance||(u.instance=new u),u.instance}async sendNotification(e){try{let t=await this.getUserPreferences(e.userId);if(this.isInQuietHours(t)){await this.scheduleNotification(e);return}let{data:a,error:i}=await this.supabase.from("notifications").insert({user_id:e.userId,title:e.title,title_jp:e.titleJp,message:e.message,message_jp:e.messageJp,type:e.type,priority:e.priority,channels:e.channels,metadata:e.metadata,action_url:e.actionUrl,scheduled_at:e.scheduledAt,expires_at:e.expiresAt,created_at:new Date().toISOString()}).select().single();if(i)throw i;for(let i of(e.id=a.id,e.channels.includes("all")?["in-app","email","sms"]:e.channels))await this.sendToChannel(i,e,t)}catch(e){throw console.error("Error sending notification:",e),e}}async sendToChannel(e,t,a){switch(e){case"in-app":a.inApp&&await this.sendInAppNotification(t);break;case"email":a.email&&a.emailAddress&&await this.queueEmailNotification(t,a);break;case"sms":a.sms&&a.phoneNumber&&await this.queueSmsNotification(t,a)}}async sendInAppNotification(e){let t=this.supabase.channel(`notifications:${e.userId}`);await t.send({type:"broadcast",event:"new-notification",payload:e})}async queueEmailNotification(e,t){let a={...e,emailAddress:t.emailAddress,language:t.language};await this.supabase.from("notification_email_queue").insert({notification_id:e.id,to_email:t.emailAddress,subject:"ja"===t.language&&e.titleJp?e.titleJp:e.title,body:"ja"===t.language&&e.messageJp?e.messageJp:e.message,priority:e.priority,metadata:e.metadata,status:"pending"}),this.emailQueue.push(a)}async queueSmsNotification(e,t){let a={...e,phoneNumber:t.phoneNumber,language:t.language};await this.supabase.from("notification_sms_queue").insert({notification_id:e.id,to_phone:t.phoneNumber,message:this.formatSmsMessage(e,t.language),priority:e.priority,metadata:e.metadata,status:"pending"}),this.smsQueue.push(a)}formatSmsMessage(e,t){let a="ja"===t&&e.titleJp?e.titleJp:e.title,i="ja"===t&&e.messageJp?e.messageJp:e.message;return`${a}: ${i}`.substring(0,160)}async getUserPreferences(e){let{data:t,error:a}=await this.supabase.from("notification_preferences").select("*").eq("user_id",e).single();return a||!t?{userId:e,email:!0,sms:!1,inApp:!0,language:"ja"}:t}isInQuietHours(e){if(!e.quietHours?.enabled)return!1;let t=new Date,a=60*t.getHours()+t.getMinutes(),[i,s]=e.quietHours.start.split(":").map(Number),[r,n]=e.quietHours.end.split(":").map(Number),o=60*i+s,l=60*r+n;return o<=l?a>=o&&a<=l:a>=o||a<=l}async scheduleNotification(e){await this.supabase.from("scheduled_notifications").insert({...e,scheduled_for:this.getNextAvailableTime(e.userId)})}async getNextAvailableTime(e){let t=await this.getUserPreferences(e);if(!t.quietHours?.enabled)return new Date;let a=new Date,[i,s]=t.quietHours.end.split(":").map(Number),r=new Date(a);return r.setHours(i,s,0,0),r<=a&&r.setDate(r.getDate()+1),r}startNotificationProcessing(){setInterval(()=>this.processEmailQueue(),3e4),setInterval(()=>this.processSmsQueue(),3e4),setInterval(()=>this.processScheduledNotifications(),6e4)}async processEmailQueue(){if(!this.processingEmail&&0!==this.emailQueue.length){this.processingEmail=!0;try{let{data:e}=await this.supabase.from("notification_email_queue").select("*").eq("status","pending").order("priority",{ascending:!1}).limit(10);if(e&&e.length>0){let{error:t}=await this.supabase.functions.invoke("send-email-notifications",{body:{emails:e}});if(!t){let t=e.map(e=>e.id);await this.supabase.from("notification_email_queue").update({status:"sent",sent_at:new Date().toISOString()}).in("id",t)}}}catch(e){console.error("Error processing email queue:",e)}finally{this.processingEmail=!1}}}async processSmsQueue(){if(!this.processingSms&&0!==this.smsQueue.length){this.processingSms=!0;try{let{data:e}=await this.supabase.from("notification_sms_queue").select("*").eq("status","pending").order("priority",{ascending:!1}).limit(10);if(e&&e.length>0){let{error:t}=await this.supabase.functions.invoke("send-sms-notifications",{body:{messages:e}});if(!t){let t=e.map(e=>e.id);await this.supabase.from("notification_sms_queue").update({status:"sent",sent_at:new Date().toISOString()}).in("id",t)}}}catch(e){console.error("Error processing SMS queue:",e)}finally{this.processingSms=!1}}}async processScheduledNotifications(){try{let{data:e}=await this.supabase.from("scheduled_notifications").select("*").lte("scheduled_for",new Date().toISOString()).eq("status","pending");if(e&&e.length>0)for(let t of e)await this.sendNotification(t),await this.supabase.from("scheduled_notifications").update({status:"sent"}).eq("id",t.id)}catch(e){console.error("Error processing scheduled notifications:",e)}}async sendFromTemplate(e,t,a){let{data:i}=await this.supabase.from("notification_templates").select("*").eq("id",e).single();if(!i)throw Error("Template not found");let s={userId:t,title:this.interpolateTemplate(i.title_template,a),titleJp:i.title_template_jp?this.interpolateTemplate(i.title_template_jp,a):void 0,message:this.interpolateTemplate(i.message_template,a),messageJp:i.message_template_jp?this.interpolateTemplate(i.message_template_jp,a):void 0,type:i.type||"info",priority:i.priority||"medium",channels:i.channels,metadata:{...i.metadata,...a}};await this.sendNotification(s)}interpolateTemplate(e,t){return e.replace(/\{\{(\w+)\}\}/g,(e,a)=>t[a]||e)}async updatePreferences(e){await this.supabase.from("notification_preferences").upsert(e)}async getNotificationHistory(e,t=50){let{data:a}=await this.supabase.from("notifications").select("*").eq("user_id",e).order("created_at",{ascending:!1}).limit(t);return a||[]}async markAsRead(e){await this.supabase.from("notifications").update({read_at:new Date().toISOString()}).eq("id",e)}async deleteNotification(e){await this.supabase.from("notifications").delete().eq("id",e)}}u.getInstance();var d=a(52054),p=a(55977);async function h(e){try{let t=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return l.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r}=await t.from("staff").select("*").eq("auth_id",a.id).single();if(!r)return l.NextResponse.json({error:"Staff not found"},{status:404});let{requestId:n,ruleId:o,reason:u}=await e.json();if(!n||!o)return l.NextResponse.json({error:"Request ID and Rule ID are required"},{status:400});let h=new i.NotificationService(t),g=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(t),f=new d.I(t),m=new p.y(t),w=new c(t,h,g,f,m);return await w.triggerManualEscalation(n,o,r.id,u),l.NextResponse.json({message:"Escalation triggered successfully",requestId:n,ruleId:o})}catch(e){return console.error("Error triggering manual escalation:",e),l.NextResponse.json({error:"Failed to trigger escalation"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let g=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/trigger/route",pathname:"/api/workflows/escalation/trigger",filename:"route",bundlePath:"app/api/workflows/escalation/trigger/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\trigger\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:w}=g;function _(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[8096,2076],()=>a(97437));module.exports=i})();