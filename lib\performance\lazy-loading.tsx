import dynamic from 'next/dynamic'
import { ComponentType, ReactElement } from 'react'

/**
 * Lazy load component with loading state
 */
export function lazyLoad<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options?: {
    loading?: ReactElement
    ssr?: boolean
  }
): T {
  return dynamic(factory, {
    loading: () => options?.loading || <div>Loading...</div>,
    ssr: options?.ssr ?? true,
  }) as T
}

/**
 * Preload component
 */
export async function preloadComponent(
  factory: () => Promise<{ default: ComponentType<any> }>
): Promise<void> {
  await factory()
}

/**
 * Lazy load heavy components
 */
export const LazyComponents = {
  // Workflow components
  WorkflowMonitoringDashboard: lazyLoad(
    () => import('@/components/workflows/monitoring/WorkflowMonitoringDashboard'),
    { ssr: false }
  ),
  
  WorkflowTemplateGallery: lazyLoad(
    () => import('@/components/workflows/WorkflowTemplateGallery'),
    { ssr: false }
  ),
  
  // Form components
  DynamicFormEngine: lazyLoad(
    () => import('@/components/forms/DynamicFormEngine'),
    { ssr: false }
  ),
  
  RequestConfirmation: lazyLoad(
    () => import('@/components/forms/RequestConfirmation'),
    { ssr: false }
  ),
  
  // Knowledge base components
  KBIntegrationWidget: lazyLoad(
    () => import('@/components/knowledge-base/kb-integration-widget'),
    { ssr: false }
  ),
  
  // Chart components
  LineChart: lazyLoad(
    () => import('recharts').then(mod => ({ default: mod.LineChart })),
    { ssr: false }
  ),
  
  BarChart: lazyLoad(
    () => import('recharts').then(mod => ({ default: mod.BarChart })),
    { ssr: false }
  ),
  
  PieChart: lazyLoad(
    () => import('recharts').then(mod => ({ default: mod.PieChart })),
    { ssr: false }
  ),
  
  // AI components
  ChatbotWidget: lazyLoad(
    () => import('@/components/chatbot/chatbot-widget'),
    { ssr: false }
  ),
  
  // Real-time components
  RealtimeDashboard: lazyLoad(
    () => import('@/components/real-time'),
    { ssr: false }
  ),
  
  // Notification components
  MultiChannelNotifications: lazyLoad(
    () => import('@/components/notifications/multi-channel-notifications'),
    { ssr: false }
  ),
}

/**
 * Intersection Observer hook for lazy loading
 */
import { useEffect, useRef, useState } from 'react'

export function useIntersectionObserver(
  options?: IntersectionObserverInit
): [React.RefObject<HTMLDivElement>, boolean] {
  const ref = useRef<HTMLDivElement>(null)
  const [isIntersecting, setIsIntersecting] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, options)

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      observer.disconnect()
    }
  }, [options])

  return [ref, isIntersecting]
}

/**
 * Lazy load images
 */
export function LazyImage({
  src,
  alt,
  className,
  placeholder,
}: {
  src: string
  alt: string
  className?: string
  placeholder?: string
}) {
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px',
  })
  const [isLoaded, setIsLoaded] = useState(false)

  return (
    <div ref={ref} className={className}>
      {(isIntersecting || isLoaded) && (
        <img
          src={src}
          alt={alt}
          className={className}
          onLoad={() => setIsLoaded(true)}
          loading="lazy"
        />
      )}
      {!isLoaded && placeholder && (
        <img src={placeholder} alt={alt} className={className} />
      )}
    </div>
  )
}
