'use client'

import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Send, MessageCircle, X, ThumbsUp, ThumbsDown, HelpCircle, BookOpen } from 'lucide-react'
import { cn } from '@/lib/utils'
import { chatbotService, type ChatMessage, type ChatbotResponse } from '@/lib/services/chatbot-service'
import { useAuth } from '@/lib/auth-context'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from '@/components/ui/use-toast'

interface ChatbotWidgetProps {
  className?: string
  defaultOpen?: boolean
  currentForm?: string
  currentPage?: string
}

export function ChatbotWidget({ 
  className, 
  defaultOpen = false,
  currentForm,
  currentPage 
}: ChatbotWidgetProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [faqs, setFaqs] = useState<any[]>([])
  const [tutorials, setTutorials] = useState<any[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { user } = useAuth()

  useEffect(() => {
    // Set context when component mounts or props change
    chatbotService.setContext({
      currentForm,
      currentPage,
      userRole: user?.role,
      department: user?.department,
      language: 'ja' // Default to Japanese
    })
  }, [currentForm, currentPage, user])

  useEffect(() => {
    scrollToBottom()
  }, [messages])
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (message: string = inputValue) => {
    if (!message.trim() || isLoading) return

    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      const response = await chatbotService.sendMessage(message)
      
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.response,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
      setSuggestions(response.suggestions || [])
      setFaqs(response.faqs || [])
      setTutorials(response.tutorials || [])
    } catch (error) {
      console.error('Failed to send message:', error)
      toast({
        title: 'エラー',
        description: 'メッセージの送信に失敗しました。',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion)
  }

  const handleFeedback = async (messageIndex: number, rating: number) => {
    // Implementation for feedback submission
    toast({
      title: 'フィードバックありがとうございます',
      description: 'あなたのフィードバックは改善に役立ちます。'
    })
  }
  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={cn(
          "fixed bottom-4 right-4 rounded-full w-14 h-14 shadow-lg",
          "bg-primary hover:bg-primary/90",
          className
        )}
        size="icon"
      >
        <MessageCircle className="h-6 w-6" />
      </Button>
    )
  }

  return (
    <Card className={cn(
      "fixed bottom-4 right-4 w-96 h-[600px] shadow-xl",
      "flex flex-col",
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-lg font-semibold">
          ITヘルプデスクアシスタント
        </CardTitle>
        <Button
          onClick={() => setIsOpen(false)}
          size="icon"
          variant="ghost"
          className="h-8 w-8"
        >
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden p-0">
        <Tabs defaultValue="chat" className="h-full">
          <TabsList className="grid w-full grid-cols-3 px-4">
            <TabsTrigger value="chat">チャット</TabsTrigger>
            <TabsTrigger value="faq">FAQ</TabsTrigger>
            <TabsTrigger value="tutorials">チュートリアル</TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="h-[calc(100%-48px)] p-0">
            <ScrollArea className="h-[calc(100%-120px)] px-4 py-2">
              {messages.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />                  <p className="text-sm">
                    こんにちは！ITヘルプデスクアシスタントです。
                    <br />
                    何かお手伝いできることはありますか？
                  </p>
                </div>
              )}

              {messages.map((message, index) => (
                <div
                  key={index}
                  className={cn(
                    "mb-4 flex",
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  <div
                    className={cn(
                      "rounded-lg px-4 py-2 max-w-[80%]",
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    )}
                  >
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    {message.role === 'assistant' && (
                      <div className="flex gap-2 mt-2">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6"
                          onClick={() => handleFeedback(index, 1)}
                        >
                          <ThumbsUp className="h-3 w-3" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6"
                          onClick={() => handleFeedback(index, -1)}
                        >
                          <ThumbsDown className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start mb-4">
                  <div className="bg-muted rounded-lg px-4 py-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </ScrollArea>

            {suggestions.length > 0 && (
              <div className="px-4 py-2 border-t">
                <p className="text-xs text-muted-foreground mb-2">提案:</p>
                <div className="flex flex-wrap gap-2">
                  {suggestions.map((suggestion, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer hover:bg-secondary/80"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="faq" className="h-[calc(100%-48px)] p-4">
            <ScrollArea className="h-full">
              {faqs.map((faq, index) => (
                <Card key={index} className="mb-3 cursor-pointer hover:bg-accent/50">
                  <CardContent className="p-3">
                    <p className="font-medium text-sm mb-1">{faq.question_ja}</p>
                    <p className="text-sm text-muted-foreground">{faq.answer_ja}</p>
                  </CardContent>
                </Card>
              ))}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="tutorials" className="h-[calc(100%-48px)] p-4">
            <ScrollArea className="h-full">
              {tutorials.map((tutorial, index) => (
                <Card key={index} className="mb-3">                  <CardContent className="p-3">
                    <div className="flex items-start gap-2">
                      <BookOpen className="h-4 w-4 mt-0.5 text-primary" />
                      <div className="flex-1">
                        <p className="font-medium text-sm">{tutorial.title_ja}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {tutorial.description_ja}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          所要時間: {tutorial.estimated_time}分
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>

      <CardFooter className="p-4 border-t">
        <form
          onSubmit={(e) => {
            e.preventDefault()
            handleSendMessage()
          }}
          className="flex w-full gap-2"
        >
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="メッセージを入力..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button type="submit" size="icon" disabled={isLoading || !inputValue.trim()}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>
      </CardFooter>
    </Card>
  )
}