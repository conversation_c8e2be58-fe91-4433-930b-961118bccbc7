      CASE WHEN v_failure_count >= 5 THEN 'high' ELSE 'warning' END,
      jsonb_build_object(
        'email', NEW.payload->>'email',
        'failure_count', v_failure_count,
        'ip_address', NEW.ip_address
      ),
      NULL,
      NULL,
      NULL,
      v_risk_score
    );
    
    -- Trigger account lockout if needed
    IF v_failure_count >= 5 THEN
      PERFORM log_security_event(
        'brute_force_detected',
        NULL,
        'critical',
        jsonb_build_object(
          'email', NEW.payload->>'email',
          'failure_count', v_failure_count,
          'action', 'account_locked'
        ),
        NULL,
        NULL,
        NULL,
        100
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Track sensitive data access
CREATE OR REPLACE FUNCTION track_sensitive_data_access()
RETURNS TRIGGER AS $$
BEGIN
  -- Log access to decrypted staff data
  IF TG_TABLE_NAME = 'staff_decrypted' AND TG_OP = 'SELECT' THEN
    PERFORM log_security_event(
      'sensitive_data_accessed',
      auth.uid(),
      'info',
      jsonb_build_object(
        'table', TG_TABLE_NAME,
        'operation', TG_OP,
        'accessed_fields', ARRAY['phone', 'personal_email', 'address', 'emergency_contact']
      ),
      'staff',
      NEW.id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Track permission changes
CREATE OR REPLACE FUNCTION track_permission_changes()
RETURNS TRIGGER AS $$
DECLARE
  v_severity VARCHAR;
  v_details JSONB;
BEGIN
  -- Determine severity based on role
  v_severity := CASE 
    WHEN NEW.role_id IN (
      SELECT id FROM roles WHERE name IN ('Global Administrator', 'Web App System Administrator')
    ) THEN 'high'
    ELSE 'warning'
  END;
  
  v_details := jsonb_build_object(
    'old_role_id', OLD.role_id,
    'new_role_id', NEW.role_id,
    'changed_by', auth.uid()
  );
  
  PERFORM log_security_event(
    'role_changed',
    NEW.auth_id,
    v_severity,
    v_details,
    'profiles',
    NEW.id
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Enable triggers
DROP TRIGGER IF EXISTS track_permission_changes_trigger ON profiles;
CREATE TRIGGER track_permission_changes_trigger
  AFTER UPDATE OF role_id ON profiles
  FOR EACH ROW
  WHEN (OLD.role_id IS DISTINCT FROM NEW.role_id)
  EXECUTE FUNCTION track_permission_changes();

-- Security anomaly detection view
CREATE OR REPLACE VIEW security_anomalies AS
WITH user_baseline AS (
  SELECT 
    user_id,
    COUNT(DISTINCT DATE(created_at)) as active_days,
    COUNT(DISTINCT ip_address) as unique_ips,
    MODE() WITHIN GROUP (ORDER BY EXTRACT(HOUR FROM created_at)) as typical_hour
  FROM security_audit_logs
  WHERE created_at > NOW() - INTERVAL '30 days'
  AND event_type IN ('login_success', 'sensitive_data_accessed')
  GROUP BY user_id
),
recent_activity AS (
  SELECT 
    s.*,
    b.unique_ips as baseline_ips,
    b.typical_hour as baseline_hour
  FROM security_audit_logs s
  LEFT JOIN user_baseline b ON s.user_id = b.user_id
  WHERE s.created_at > NOW() - INTERVAL '24 hours'
)
SELECT 
  *,
  CASE
    -- New IP address
    WHEN ip_address IS NOT NULL AND ip_address NOT IN (
      SELECT DISTINCT ip_address 
      FROM security_audit_logs 
      WHERE user_id = recent_activity.user_id 
      AND created_at < NOW() - INTERVAL '24 hours'
    ) THEN 'new_ip_address'
    
    -- Unusual time
    WHEN EXTRACT(HOUR FROM created_at) NOT BETWEEN baseline_hour - 3 AND baseline_hour + 3 
    THEN 'unusual_time'
    
    -- High volume
    WHEN event_type = 'sensitive_data_accessed' AND (
      SELECT COUNT(*) 
      FROM security_audit_logs 
      WHERE user_id = recent_activity.user_id 
      AND event_type = 'sensitive_data_accessed'
      AND created_at > NOW() - INTERVAL '1 hour'
    ) > 50 THEN 'high_volume_access'
    
    ELSE NULL
  END as anomaly_type
FROM recent_activity
WHERE risk_score > 30
OR severity IN ('high', 'critical');

-- Security compliance report view
CREATE OR REPLACE VIEW security_compliance_report AS
SELECT 
  DATE(created_at) as date,
  COUNT(*) FILTER (WHERE event_type = 'login_success') as successful_logins,
  COUNT(*) FILTER (WHERE event_type = 'login_failed') as failed_logins,
  COUNT(*) FILTER (WHERE event_type = 'mfa_verified') as mfa_verifications,
  COUNT(*) FILTER (WHERE event_type = 'sensitive_data_accessed') as sensitive_access,
  COUNT(*) FILTER (WHERE severity = 'critical') as critical_events,
  COUNT(*) FILTER (WHERE risk_score > 70) as high_risk_events,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(DISTINCT ip_address) as unique_ips
FROM security_audit_logs
WHERE created_at > NOW() - INTERVAL '90 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Function to generate security report
CREATE OR REPLACE FUNCTION generate_security_report(
  p_start_date TIMESTAMP WITH TIME ZONE,
  p_end_date TIMESTAMP WITH TIME ZONE
) RETURNS TABLE (
  metric VARCHAR,
  value BIGINT,
  severity VARCHAR,
  trend VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  WITH current_period AS (
    SELECT * FROM security_audit_logs
    WHERE created_at BETWEEN p_start_date AND p_end_date
  ),
  previous_period AS (
    SELECT * FROM security_audit_logs
    WHERE created_at BETWEEN 
      p_start_date - (p_end_date - p_start_date) 
      AND p_start_date
  ),
  metrics AS (
    SELECT 
      'Total Security Events' as metric,
      COUNT(*) as current_value,
      (SELECT COUNT(*) FROM previous_period) as previous_value,
      'info' as severity
    FROM current_period
    
    UNION ALL
    
    SELECT 
      'Failed Login Attempts',
      COUNT(*) FILTER (WHERE event_type = 'login_failed'),
      (SELECT COUNT(*) FILTER (WHERE event_type = 'login_failed') FROM previous_period),
      'warning'
    FROM current_period
    
    UNION ALL
    
    SELECT 
      'Critical Events',
      COUNT(*) FILTER (WHERE severity = 'critical'),
      (SELECT COUNT(*) FILTER (WHERE severity = 'critical') FROM previous_period),
      'critical'
    FROM current_period
    
    UNION ALL
    
    SELECT 
      'High Risk Events',
      COUNT(*) FILTER (WHERE risk_score > 70),
      (SELECT COUNT(*) FILTER (WHERE risk_score > 70) FROM previous_period),
      'high'
    FROM current_period
  )
  SELECT 
    metric,
    current_value as value,
    severity,
    CASE 
      WHEN previous_value = 0 AND current_value > 0 THEN 'increasing'
      WHEN current_value > previous_value * 1.1 THEN 'increasing'
      WHEN current_value < previous_value * 0.9 THEN 'decreasing'
      ELSE 'stable'
    END as trend
  FROM metrics;
END;
$$ LANGUAGE plpgsql;

-- RLS for security audit logs
ALTER TABLE security_audit_logs ENABLE ROW LEVEL SECURITY;

-- Only admins and security officers can view security logs
CREATE POLICY "Security logs viewable by admins" ON security_audit_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN roles r ON p.role_id = r.id
      WHERE p.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Users can view their own security events
CREATE POLICY "Users can view own security events" ON security_audit_logs
  FOR SELECT USING (
    user_id = auth.uid() 
    AND event_type IN ('login_success', 'password_changed', 'mfa_enabled', 'mfa_disabled')
  );

-- Archive old security logs
CREATE TABLE IF NOT EXISTS security_audit_logs_archive (
  LIKE security_audit_logs INCLUDING ALL
);

-- Function to archive old logs
CREATE OR REPLACE FUNCTION archive_old_security_logs()
RETURNS INTEGER AS $$
DECLARE
  v_archived_count INTEGER;
BEGIN
  -- Move logs older than 1 year to archive
  WITH archived AS (
    DELETE FROM security_audit_logs
    WHERE created_at < NOW() - INTERVAL '1 year'
    RETURNING *
  )
  INSERT INTO security_audit_logs_archive
  SELECT * FROM archived;
  
  GET DIAGNOSTICS v_archived_count = ROW_COUNT;
  
  -- Log the archival
  PERFORM log_security_event(
    'security_setting_changed',
    NULL,
    'info',
    jsonb_build_object(
      'action', 'logs_archived',
      'count', v_archived_count
    )
  );
  
  RETURN v_archived_count;
END;
$$ LANGUAGE plpgsql;

-- Schedule periodic archival (requires pg_cron extension)
-- SELECT cron.schedule('archive-security-logs', '0 2 * * 0', 'SELECT archive_old_security_logs();');

-- Comments
COMMENT ON TABLE security_audit_logs IS 'Comprehensive security event logging for compliance and threat detection';
COMMENT ON FUNCTION log_security_event IS 'Log security-related events with risk scoring and alerting';
COMMENT ON VIEW security_anomalies IS 'Real-time anomaly detection based on user behavior patterns';
COMMENT ON VIEW security_compliance_report IS 'Daily security metrics for compliance reporting';
