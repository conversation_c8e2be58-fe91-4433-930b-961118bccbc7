"use strict";exports.id=4808,exports.ids=[4808],exports.modules={24808:(e,t,r)=>{r.d(t,{Kq:()=>X,UC:()=>G,ZL:()=>z,bL:()=>Y,l9:()=>Z});var n=r(58009),o=r(31412),i=r(29952),l=r(6004),a=r(41675),s=r(30096),u=r(53337),c=r(80707),p=r(98060),d=r(30830),f=r(12705),x=r(13024),h=r(56441),g=r(45512),[v,y]=(0,l.A)("Tooltip",[u.Bk]),m=(0,u.Bk)(),b="TooltipProvider",w="tooltip.open",[C,T]=v(b),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,g.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};E.displayName=b;var k="Tooltip",[L,R]=v(k),j=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,p=T(k,e.__scopeTooltip),d=m(t),[f,h]=n.useState(null),v=(0,s.B)(),y=n.useRef(0),b=a??p.disableHoverableContent,C=c??p.delayDuration,E=n.useRef(!1),[R,j]=(0,x.i)({prop:o,defaultProp:i??!1,onChange:e=>{e?(p.onOpen(),document.dispatchEvent(new CustomEvent(w))):p.onClose(),l?.(e)},caller:k}),P=n.useMemo(()=>R?E.current?"delayed-open":"instant-open":"closed",[R]),M=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E.current=!1,j(!0)},[j]),_=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,j(!1)},[j]),D=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{E.current=!0,j(!0),y.current=0},C)},[C,j]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,g.jsx)(u.bL,{...d,children:(0,g.jsx)(L,{scope:t,contentId:v,open:R,stateAttribute:P,trigger:f,onTriggerChange:h,onTriggerEnter:n.useCallback(()=>{p.isOpenDelayedRef.current?D():M()},[p.isOpenDelayedRef,D,M]),onTriggerLeave:n.useCallback(()=>{b?_():(window.clearTimeout(y.current),y.current=0)},[_,b]),onOpen:M,onClose:_,disableHoverableContent:b,children:r})})};j.displayName=k;var P="TooltipTrigger",M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=R(P,r),s=T(P,r),c=m(r),p=n.useRef(null),f=(0,i.s)(t,p,a.onTriggerChange),x=n.useRef(!1),h=n.useRef(!1),v=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,g.jsx)(u.Mz,{asChild:!0,...c,children:(0,g.jsx)(d.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),x.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});M.displayName=P;var _="TooltipPortal",[D,O]=v(_,{forceMount:void 0}),B=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=R(_,t);return(0,g.jsx)(D,{scope:t,forceMount:r,children:(0,g.jsx)(p.C,{present:r||i.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};B.displayName=_;var I="TooltipContent",N=n.forwardRef((e,t)=>{let r=O(I,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=R(I,e.__scopeTooltip);return(0,g.jsx)(p.C,{present:n||l.open,children:l.disableHoverableContent?(0,g.jsx)(S,{side:o,...i,ref:t}):(0,g.jsx)(A,{side:o,...i,ref:t})})}),A=n.forwardRef((e,t)=>{let r=R(I,e.__scopeTooltip),o=T(I,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:p}=r,d=l.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{u(null),f(!1)},[f]),h=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&d){let e=e=>h(e,d),t=e=>h(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,h,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||d?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,p=a.y;u>n!=p>n&&r<(c-s)*(n-u)/(p-u)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,s,p,x]),(0,g.jsx)(S,{...e,ref:a})}),[F,H]=v(k,{isInside:!1}),q=(0,f.Dc)("TooltipContent"),S=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,p=R(I,r),d=m(r),{onClose:f}=p;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(p.trigger){let e=e=>{let t=e.target;t?.contains(p.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[p.trigger,f]),(0,g.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,g.jsxs)(u.UC,{"data-state":p.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,g.jsx)(q,{children:o}),(0,g.jsx)(F,{scope:r,isInside:!0,children:(0,g.jsx)(h.bL,{id:p.contentId,role:"tooltip",children:i||o})})]})})});N.displayName=I;var U="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=m(r);return H(U,r).isInside?null:(0,g.jsx)(u.i3,{...o,...n,ref:t})}).displayName=U;var X=E,Y=j,Z=M,z=B,G=N}};