/**
 * Database Query Optimizer
 * Provides query analysis, optimization, and performance monitoring
 */

import { createClient } from '@/lib/supabase/server'
import { metrics, trackDatabaseMetrics } from '@/lib/monitoring/metrics'
import { getCacheService } from '@/lib/cache/cache-service'

interface QueryOptions {
  cache?: boolean
  cacheTTL?: number
  cacheKey?: string
  cacheTags?: string[]
  timeout?: number
  retries?: number
}

interface QueryResult<T> {
  data: T | null
  error: any
  count?: number
  executionTime: number
  fromCache: boolean
  queryPlan?: any
}

interface SlowQuery {
  query: string
  duration: number
  timestamp: Date
  params?: any
  stackTrace?: string
}

class QueryOptimizer {
  private supabase = createClient()
  private cache = getCacheService()
  private slowQueries: SlowQuery[] = []
  private readonly slowQueryThreshold = 1000 // 1 second
  private readonly maxSlowQueries = 100

  /**
   * Execute optimized query with caching and monitoring
   */
  async executeQuery<T>(
    tableName: string,
    queryBuilder: (query: any) => any,
    options: QueryOptions = {}
  ): Promise<QueryResult<T>> {
    const {
      cache = false,
      cacheTTL = 300, // 5 minutes default
      cacheKey,
      cacheTags = [tableName],
      timeout = 30000, // 30 seconds
      retries = 2
    } = options

    const startTime = Date.now()
    const finalCacheKey = cacheKey || this.generateCacheKey(tableName, queryBuilder.toString())

    // Try cache first if enabled
    if (cache) {
      const cached = await this.cache.get<T>(finalCacheKey)
      if (cached !== null) {
        return {
          data: cached,
          error: null,
          executionTime: Date.now() - startTime,
          fromCache: true
        }
      }
    }

    // Execute query with retries
    let lastError: any = null
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const result = await this.executeWithTimeout(
          () => queryBuilder(this.supabase.from(tableName)),
          timeout
        )

        const executionTime = Date.now() - startTime
        const success = !result.error

        // Track metrics
        trackDatabaseMetrics(tableName, executionTime, success)

        // Log slow queries
        if (executionTime > this.slowQueryThreshold) {
          this.logSlowQuery(tableName, executionTime, queryBuilder.toString())
        }

        // Cache successful results
        if (cache && success && result.data) {
          await this.cache.set(finalCacheKey, result.data, {
            ttl: cacheTTL,
            tags: cacheTags
          })
        }

        return {
          data: result.data,
          error: result.error,
          count: result.count,
          executionTime,
          fromCache: false
        }
      } catch (error) {
        lastError = error
        if (attempt < retries) {
          // Exponential backoff
          await this.delay(Math.pow(2, attempt) * 100)
        }
      }
    }

    // All retries failed
    const executionTime = Date.now() - startTime
    trackDatabaseMetrics(tableName, executionTime, false)

    return {
      data: null,
      error: lastError,
      executionTime,
      fromCache: false
    }
  }

  /**
   * Optimized user profile query with caching
   */
  async getUserProfile(userId: string): Promise<QueryResult<any>> {
    return this.executeQuery(
      'user_profiles',
      (query) => query
        .select(`
          *,
          organizations (
            id,
            name,
            domain,
            settings
          ),
          divisions (
            id,
            name
          ),
          groups (
            id,
            name
          )
        `)
        .eq('user_id', userId)
        .single(),
      {
        cache: true,
        cacheTTL: 900, // 15 minutes
        cacheKey: `user_profile:${userId}`,
        cacheTags: ['user_profiles', 'users', userId]
      }
    )
  }

  /**
   * Optimized organization settings query
   */
  async getOrganizationSettings(orgId: string): Promise<QueryResult<any>> {
    return this.executeQuery(
      'organizations',
      (query) => query
        .select('id, name, domain, settings, created_at, updated_at')
        .eq('id', orgId)
        .single(),
      {
        cache: true,
        cacheTTL: 1800, // 30 minutes
        cacheKey: `org_settings:${orgId}`,
        cacheTags: ['organizations', orgId]
      }
    )
  }

  /**
   * Optimized form schema query with caching
   */
  async getFormSchema(formId: string): Promise<QueryResult<any>> {
    return this.executeQuery(
      'forms',
      (query) => query
        .select(`
          *,
          form_fields (
            id,
            name,
            type,
            required,
            options,
            validation_rules,
            order_index
          )
        `)
        .eq('id', formId)
        .eq('form_fields.active', true)
        .order('order_index', { foreignTable: 'form_fields' })
        .single(),
      {
        cache: true,
        cacheTTL: 3600, // 1 hour
        cacheKey: `form_schema:${formId}`,
        cacheTags: ['forms', 'form_fields', formId]
      }
    )
  }

  /**
   * Optimized tickets query with pagination
   */
  async getTickets(
    filters: any = {},
    pagination: { page: number; limit: number } = { page: 1, limit: 20 }
  ): Promise<QueryResult<any[]>> {
    const { page, limit } = pagination
    const offset = (page - 1) * limit

    return this.executeQuery(
      'tickets',
      (query) => {
        let q = query
          .select(`
            id,
            title,
            description,
            status,
            priority,
            category,
            created_at,
            updated_at,
            requester:user_profiles!requester_id (
              full_name,
              email
            ),
            assignee:user_profiles!assignee_id (
              full_name,
              email
            ),
            organization:organizations (
              name
            )
          `)
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1)

        // Apply filters
        if (filters.status) {
          q = q.eq('status', filters.status)
        }
        if (filters.priority) {
          q = q.eq('priority', filters.priority)
        }
        if (filters.category) {
          q = q.eq('category', filters.category)
        }
        if (filters.organization_id) {
          q = q.eq('organization_id', filters.organization_id)
        }
        if (filters.assignee_id) {
          q = q.eq('assignee_id', filters.assignee_id)
        }

        return q
      },
      {
        cache: true,
        cacheTTL: 60, // 1 minute for frequently changing data
        cacheKey: `tickets:${JSON.stringify(filters)}:${page}:${limit}`,
        cacheTags: ['tickets']
      }
    )
  }

  /**
   * Bulk insert with optimization
   */
  async bulkInsert<T>(
    tableName: string,
    records: T[],
    batchSize: number = 100
  ): Promise<QueryResult<T[]>> {
    const startTime = Date.now()
    const results: T[] = []
    let lastError: any = null

    // Process in batches to avoid timeout
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize)
      
      try {
        const { data, error } = await this.supabase
          .from(tableName)
          .insert(batch)
          .select()

        if (error) {
          lastError = error
          break
        }

        if (data) {
          results.push(...data)
        }
      } catch (error) {
        lastError = error
        break
      }
    }

    const executionTime = Date.now() - startTime
    const success = !lastError

    trackDatabaseMetrics(`${tableName}_bulk_insert`, executionTime, success)

    // Invalidate related cache
    await this.cache.invalidateByTags([tableName])

    return {
      data: success ? results : null,
      error: lastError,
      executionTime,
      fromCache: false
    }
  }

  /**
   * Get query performance statistics
   */
  getSlowQueries(): SlowQuery[] {
    return [...this.slowQueries].sort((a, b) => b.duration - a.duration)
  }

  /**
   * Get database performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    const cacheStats = await this.cache.getStats()
    
    return {
      slowQueries: {
        count: this.slowQueries.length,
        threshold: this.slowQueryThreshold,
        queries: this.getSlowQueries().slice(0, 10) // Top 10 slowest
      },
      cache: cacheStats,
      connectionPool: {
        // These would come from actual connection pool metrics
        active: 5,
        idle: 3,
        total: 8,
        maxConnections: 20
      }
    }
  }

  /**
   * Analyze query performance
   */
  async analyzeQuery(sql: string): Promise<any> {
    try {
      // This would use EXPLAIN ANALYZE in a real PostgreSQL setup
      // For Supabase, we'll simulate query analysis
      const result = await this.supabase.rpc('analyze_query', { query: sql })
      return result.data
    } catch (error) {
      console.error('Query analysis error:', error)
      return null
    }
  }

  /**
   * Invalidate cache for specific entities
   */
  async invalidateCache(tags: string[]): Promise<void> {
    await this.cache.invalidateByTags(tags)
    metrics.increment('database.cache_invalidations', { tags: tags.join(',') })
  }

  /**
   * Warm up frequently accessed data
   */
  async warmUpCache(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // Warm up common organization settings
      const { data: orgs } = await this.supabase
        .from('organizations')
        .select('id')
        .limit(10)

      if (orgs) {
        const warmupPromises = orgs.map(org => 
          this.getOrganizationSettings(org.id)
        )
        await Promise.all(warmupPromises)
      }

      // Warm up common form schemas
      const { data: forms } = await this.supabase
        .from('forms')
        .select('id')
        .eq('active', true)
        .limit(5)

      if (forms) {
        const formPromises = forms.map(form => 
          this.getFormSchema(form.id)
        )
        await Promise.all(formPromises)
      }

      const duration = Date.now() - startTime
      metrics.timing('database.warmup_time', duration)
      
    } catch (error) {
      console.error('Cache warmup error:', error)
    }
  }

  // Private helper methods
  private generateCacheKey(tableName: string, query: string): string {
    const hash = this.simpleHash(query)
    return `query:${tableName}:${hash}`
  }

  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout')), timeout)
      )
    ])
  }

  private logSlowQuery(table: string, duration: number, query: string): void {
    const slowQuery: SlowQuery = {
      query: `${table}: ${query.substring(0, 200)}...`,
      duration,
      timestamp: new Date(),
      stackTrace: new Error().stack
    }

    this.slowQueries.push(slowQuery)

    // Keep only the most recent slow queries
    if (this.slowQueries.length > this.maxSlowQueries) {
      this.slowQueries.shift()
    }

    // Log to metrics
    metrics.increment('database.slow_queries')
    metrics.timing('database.slow_query_duration', duration)

    console.warn(`Slow query detected: ${table} took ${duration}ms`)
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Singleton instance
let queryOptimizer: QueryOptimizer | null = null

export const getQueryOptimizer = (): QueryOptimizer => {
  if (!queryOptimizer) {
    queryOptimizer = new QueryOptimizer()
  }
  return queryOptimizer
}

export { QueryOptimizer }
export type { QueryOptions, QueryResult, SlowQuery }
