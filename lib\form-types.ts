// Form field types and interfaces
export interface FormField {
  name: string
  type: 'text' | 'select' | 'search' | 'multi_select' | 'checkbox' | 'textarea' | 'radio'
  label: string
  labelJp?: string
  required: boolean
  validation?: string
  defaultValue?: any
  options?: string[] | FieldOption[]
  source?: string
  searchable?: boolean
  placeholder?: string
  placeholderJp?: string
  helpText?: string
  helpTextJp?: string
  // For compatibility with existing code
  id?: string
}

export interface FieldOption {
  value: string
  label: string
  labelJp?: string
}

export interface FormSection {
  id: string
  title: string
  titleJp: string
  fields: FormField[]
  conditions?: FieldCondition[]
}

export interface FieldCondition {
  fieldId: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains'
  value: any
  showFields?: string[]
  hideFields?: string[]
}

export interface FormSchema {
  id?: string
  name?: string
  nameJp?: string
  description?: string
  descriptionJp?: string
  sections?: FormSection[]
  fields?: FormField[] // Direct fields from service category
  validation?: string
  aiEnabled?: boolean
  departmentSpecific?: boolean
}

export interface FormData {
  [fieldId: string]: any
}

export interface FormState {
  schema: FormSchema
  data: FormData
  errors: Record<string, string | undefined>
  loading: boolean
  aiSuggestions: Record<string, any>
}
