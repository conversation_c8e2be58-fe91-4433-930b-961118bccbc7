# Supabase MCP Server Setup Instructions

## Prerequisites Completed ✓
- Node.js is installed (verified)
- Directory created: `supabase-mcp-server`
- MCP settings configured in `mcp_settings.json`

## Next Steps

### 1. Get Your Supabase Personal Access Token
1. Go to https://supabase.com/dashboard/account/tokens
2. Click "Generate new token"
3. Give it a descriptive name like "Cursor MCP Server" or "Claude MCP Server"
4. Copy the token (you won't be able to see it again!)

### 2. Update the Configuration
Replace `<personal-access-token>` in `mcp_settings.json` with your actual token:

```json
"github.com/supabase-community/supabase-mcp": {
  "command": "cmd",
  "args": [
    "/c",
    "npx",
    "-y",
    "@supabase/mcp-server-supabase@latest",
    "--access-token",
    "YOUR_ACTUAL_TOKEN_HERE"
  ],
  "disabled": false,
  "autoApprove": []
}
```

### 3. Optional Configuration

#### Project Scoped Mode
To restrict the server to a specific project, add the project reference:
```json
"args": [
  "/c",
  "npx",
  "-y",
  "@supabase/mcp-server-supabase@latest",
  "--access-token",
  "YOUR_TOKEN",
  "--project-ref",
  "YOUR_PROJECT_REF"
]
```

#### Read-Only Mode
To restrict to read-only operations, add the `--read-only` flag:
```json
"args": [
  "/c",
  "npx",
  "-y",
  "@supabase/mcp-server-supabase@latest",
  "--access-token",
  "YOUR_TOKEN",
  "--read-only"
]
```

### 4. Restart Your MCP Client
After updating the configuration, restart your MCP client (Cursor, Claude, Windsurf, etc.) to load the new server.

## Available Tools
Once configured, you'll have access to:

### Project Management
- `list_projects`: Lists all Supabase projects
- `get_project`: Gets details for a project
- `create_project`: Creates a new Supabase project
- `pause_project`: Pauses a project
- `restore_project`: Restores a project
- `list_organizations`: Lists all organizations
- `get_organization`: Gets organization details

### Database Operations
- `list_tables`: Lists all tables within schemas
- `list_extensions`: Lists database extensions
- `list_migrations`: Lists database migrations
- `apply_migration`: Applies SQL migrations
- `execute_sql`: Executes raw SQL queries
- `get_logs`: Gets project logs by service type

### Edge Functions
- `list_edge_functions`: Lists all Edge Functions
- `deploy_edge_function`: Deploys Edge Functions

### Configuration
- `get_project_url`: Gets the API URL
- `get_anon_key`: Gets the anonymous API key

### Development
- `generate_typescript_types`: Generates TypeScript types from schema

### Branching (Experimental)
- `create_branch`: Creates development branch
- `list_branches`: Lists all branches
- `delete_branch`: Deletes a branch
- `merge_branch`: Merges branches
- `reset_branch`: Resets branch migrations
- `rebase_branch`: Rebases on production

## Testing the Connection
After setup, you can test by using tools like:
- `list_organizations` to see your organizations
- `list_projects` to see your projects