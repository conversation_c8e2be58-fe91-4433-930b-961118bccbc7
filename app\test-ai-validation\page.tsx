'use client'

import { useState } from 'react'
import { EnhancedDynamicForm } from '@/components/forms/enhanced-dynamic-form-with-validation'
import { FormSection } from '@/lib/form-types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { Badge } from '@/components/ui/badge'
import { Sparkles } from 'lucide-react'

// Sample form sections for testing AI validation
const testFormSections: FormSection[] = [
  {
    id: 'basic-info',
    title: '基本情報 / Basic Information',
    description: 'AIがリアルタイムで入力を検証し、提案を行います / AI validates and suggests in real-time',
    fields: [
      {
        id: 'email',
        type: 'text',
        label: 'Email Address',
        labelJp: 'メールアドレス',
        validation: 'email',
        required: true,
        placeholder: '例: <EMAIL>',
        helpText: 'Enter your company email address',
        helpTextJp: '会社のメールアドレスを入力してください'
      },
      {
        id: 'pc_id',
        type: 'text',
        label: 'PC ID',
        labelJp: 'PC ID',
        validation: 'pc_id',
        required: true,
        placeholder: '例: LT24123101',
        helpText: 'Enter PC ID (e.g., LT24123101 for laptop)',
        helpTextJp: 'PC IDを入力してください（例：ノートPCの場合 LT24123101）'
      },
      {
        id: 'reason',
        type: 'textarea',
        label: 'Reason for Request',
        labelJp: '申請理由',
        required: true,
        placeholder: '申請理由を日本語と英語で記載してください / Please describe the reason in Japanese and English',
        helpText: 'Provide a clear reason for your request',
        helpTextJp: '申請理由を明確に記載してください'
      }
    ]
  },
  {
    id: 'service-selection',
    title: 'サービス選択 / Service Selection',
    description: '必要なサービスを選択してください / Select required services',
    fields: [
      {
        id: 'service_type',
        type: 'select',
        label: 'Service Type',
        labelJp: 'サービスタイプ',
        required: true,
        options: [
          { value: 'group_mail', label: 'グループメール / Group Mail' },
          { value: 'sharepoint', label: 'SharePoint アクセス / SharePoint Access' },
          { value: 'pc_admin', label: 'PC管理者権限 / PC Admin Rights' },
          { value: 'password_reset', label: 'パスワードリセット / Password Reset' }
        ]
      },
      {
        id: 'urgency',
        type: 'radio',
        label: 'Urgency Level',
        labelJp: '緊急度',
        required: true,
        options: [
          { value: 'low', label: '低 / Low' },
          { value: 'medium', label: '中 / Medium' },
          { value: 'high', label: '高 / High' },
          { value: 'critical', label: '緊急 / Critical' }
        ]
      }
    ]
  }
]

export default function AIValidationTestPage() {
  const [submittedData, setSubmittedData] = useState<any>(null)

  const handleSubmit = (data: any) => {
    setSubmittedData(data)
    toast.success('フォームが送信されました / Form submitted successfully')
  }

  const handleCancel = () => {
    setSubmittedData(null)
    toast.info('フォームがキャンセルされました / Form cancelled')
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <Sparkles className="h-8 w-8 text-blue-600" />
            AI リアルタイム検証デモ
          </h1>
          <p className="text-muted-foreground">
            Real-time Form Validation with AI Feedback
          </p>
        </div>

        {/* Feature Badges */}
        <div className="flex flex-wrap gap-2 justify-center">
          <Badge variant="secondary">リアルタイム検証 / Real-time Validation</Badge>
          <Badge variant="secondary">AI提案 / AI Suggestions</Badge>
          <Badge variant="secondary">視覚的フィードバック / Visual Feedback</Badge>
          <Badge variant="secondary">コンテキスト認識 / Context Aware</Badge>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>使い方 / How to Use</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>• フィールドに入力すると、AIがリアルタイムで検証します / AI validates as you type</li>
              <li>• 緑のチェックマークは正しい入力を示します / Green checkmark indicates valid input</li>
              <li>• 黄色の警告は改善の余地があることを示します / Yellow warning suggests improvements</li>
              <li>• AIの提案がある場合は「採用」ボタンで適用できます / Click "Accept" to apply AI suggestions</li>
              <li>• 「自動入力」ボタンで空欄を自動補完できます / Use "Auto-fill" to complete empty fields</li>
            </ul>
          </CardContent>
        </Card>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>テストフォーム / Test Form</CardTitle>
            <CardDescription>
              AIアシスタントが入力をサポートします / AI assistant helps with form completion
            </CardDescription>
          </CardHeader>
          <CardContent>
            <EnhancedDynamicForm
              sections={testFormSections}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              context={{
                departmentId: 'it-systems',
                userRole: 'regular_user'
              }}
            />
          </CardContent>
        </Card>

        {/* Submitted Data Display */}
        {submittedData && (
          <Card>
            <CardHeader>
              <CardTitle>送信されたデータ / Submitted Data</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-auto">
                {JSON.stringify(submittedData, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
