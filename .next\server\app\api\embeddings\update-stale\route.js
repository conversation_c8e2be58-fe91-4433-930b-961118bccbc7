"use strict";(()=>{var e={};e.id=2880,e.ids=[2880],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{e.exports=require("crypto")},13901:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>u,runtime:()=>i});var a=t(42706),n=t(28203),o=t(45994),p=t(39187),d=t(75122);let i="nodejs";async function u(e){try{return await d.K.updateStaleEmbeddings(),p.NextResponse.json({success:!0,message:"Stale embeddings update initiated"})}catch(e){return console.error("Update stale embeddings error:",e),p.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Update failed"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/embeddings/update-stale/route",pathname:"/api/embeddings/update-stale",filename:"route",bundlePath:"app/api/embeddings/update-stale/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\update-stale\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=c;function g(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(13901));module.exports=s})();