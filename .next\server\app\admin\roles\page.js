(()=>{var e={};e.id=3848,e.ids=[3848],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},71308:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(70260),n=r(28203),a=r(25155),i=r.n(a),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["admin",{children:["roles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34350)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\roles\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\roles\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/roles/page",pathname:"/admin/roles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62271:(e,s,r)=>{Promise.resolve().then(r.bind(r,34350))},56695:(e,s,r)=>{Promise.resolve().then(r.bind(r,98509))},98509:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(45512),n=r(55131),a=r(22417),i=r(58957);function l({children:e,resource:s,action:r,scope:n,role:l,departmentId:o,fallback:d=null}){let{checkPermission:c,checkDepartmentAccess:p,isGlobalAdmin:m,isSystemAdmin:x,isDepartmentAdmin:u,isHRStaff:h,isITSupport:j,isRegularUser:g}=(0,a.S)();if(l){let e=!1;switch(l){case i.gG.GLOBAL_ADMIN:e=m();break;case i.gG.SYSTEM_ADMIN:e=x();break;case i.gG.DEPT_ADMIN:e=u();break;case i.gG.HR_STAFF:e=h();break;case i.gG.IT_SUPPORT:e=j();break;case i.gG.REGULAR_USER:e=g()}if(!e)return(0,t.jsx)(t.Fragment,{children:d})}return(!o||p(o))&&c(s,r,n)?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:d})}var o=r(97643),d=r(70384);function c(){return(0,t.jsx)(n.A,{children:(0,t.jsx)(l,{resource:"users",action:"update",fallback:(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,t.jsx)(o.Zp,{children:(0,t.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-400"}),(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"アクセス拒否 / Access Denied"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["ユーザー管理にアクセスする権限がありません。",(0,t.jsx)("br",{}),"You don't have permission to manage users."]})]})})}),children:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"ロール管理 / Role Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"スタッフのロールと権限を管理します / Manage staff roles and permissions"})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"mr-2 h-5 w-5"}),"開発中 / Under Development"]}),(0,t.jsxs)(o.BT,{children:["この機能は現在開発中です。後日実装予定です。",(0,t.jsx)("br",{}),"This feature is currently under development and will be implemented soon."]})]}),(0,t.jsxs)(o.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Role management functionality will include:"}),(0,t.jsxs)("ul",{className:"mt-2 text-sm text-gray-600 list-disc list-inside",children:[(0,t.jsx)("li",{children:"User role assignment"}),(0,t.jsx)("li",{children:"Permission management"}),(0,t.jsx)("li",{children:"Department-based access control"}),(0,t.jsx)("li",{children:"Audit trail for role changes"})]})]})]})]})})})})})}},34350:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\admin\\\\roles\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\roles\\page.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8096,2076],()=>r(71308));module.exports=t})();