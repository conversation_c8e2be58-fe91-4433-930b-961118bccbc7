// Edge Function: Learn from User Feedback
// Updates AI patterns based on user acceptance/rejection of suggestions

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface FeedbackRequest {
  userId: string
  fieldId: string
  originalValue: string
  suggestedValue: string
  wasAccepted: boolean
  finalValue?: string
  departmentId?: string
  confidence?: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const feedback = await req.json() as FeedbackRequest

    // Record the suggestion feedback
    const { data: suggestionRecord, error: suggestionError } = await supabase
      .from('ai_field_suggestions')
      .insert({
        user_id: feedback.userId,
        field_id: feedback.fieldId,
        original_value: feedback.originalValue,
        suggested_value: feedback.suggestedValue,
        was_accepted: feedback.wasAccepted,
        confidence_score: feedback.confidence || 0.5,
        suggestion_metadata: {
          final_value: feedback.finalValue || (feedback.wasAccepted ? feedback.suggestedValue : feedback.originalValue),
          department_id: feedback.departmentId
        }
      })
      .select()
      .single()

    if (suggestionError) {
      throw suggestionError
    }

    // Update pattern confidence based on feedback
    if (feedback.departmentId) {
      const patternKey = `department:${feedback.departmentId}:field:${feedback.fieldId}`
      
      // Get current pattern
      const { data: currentPattern } = await supabase
        .from('ai_form_patterns')
        .select('*')
        .eq('pattern_type', 'field_value')
        .eq('pattern_key', patternKey)
        .single()

      if (currentPattern) {
        // Adjust confidence score based on acceptance
        let newConfidence = currentPattern.confidence_score
        if (feedback.wasAccepted) {
          // Increase confidence (max 0.95)
          newConfidence = Math.min(newConfidence + 0.05, 0.95)
        } else {
          // Decrease confidence (min 0.10)
          newConfidence = Math.max(newConfidence - 0.10, 0.10)
        }

        // Update pattern
        await supabase
          .from('ai_form_patterns')
          .update({
            confidence_score: newConfidence,
            usage_count: currentPattern.usage_count + 1,
            last_used: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', currentPattern.id)
      }
    }

    // Calculate acceptance rate for this field
    const { data: acceptanceStats } = await supabase
      .from('ai_field_suggestions')
      .select('was_accepted')
      .eq('field_id', feedback.fieldId)
      .eq('user_id', feedback.userId)
      .order('created_at', { ascending: false })
      .limit(20)

    const acceptanceRate = acceptanceStats
      ? acceptanceStats.filter(s => s.was_accepted).length / acceptanceStats.length
      : 0

    return new Response(
      JSON.stringify({
        success: true,
        suggestionId: suggestionRecord.id,
        acceptanceRate,
        message: feedback.wasAccepted 
          ? 'Thank you for accepting the suggestion!'
          : 'Thank you for your feedback. We\'ll improve our suggestions.'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
