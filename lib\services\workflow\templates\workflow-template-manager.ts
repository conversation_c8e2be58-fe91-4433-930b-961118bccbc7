owEngine
  ) {
    this.supabase = supabase;
    this.auditService = auditService;
    this.workflowEngine = workflowEngine;
  }

  // Get all available templates
  async getTemplates(filters?: {
    category?: string;
    serviceCategoryId?: string;
    priority?: string;
  }): Promise<WorkflowTemplate[]> {
    let templates = [...workflowTemplates];

    if (filters?.category) {
      templates = templates.filter(t => t.category === filters.category);
    }
    if (filters?.serviceCategoryId) {
      templates = templates.filter(t => t.serviceCategoryId === filters.serviceCategoryId);
    }
    if (filters?.priority) {
      templates = templates.filter(t => t.priority === filters.priority);
    }

    return templates;
  }

  // Get template by ID
  async getTemplate(templateId: string): Promise<WorkflowTemplate | null> {
    return getWorkflowTemplate(templateId) || null;
  }

  // Create workflow from template
  async createWorkflowFromTemplate(
    templateId: string,
    requestId: string,
    userId: string,
    customizations?: {
      name?: string;
      description?: string;
      slaHours?: number;
      priority?: string;
    }
  ): Promise<string> {
    try {
      const template = await this.getTemplate(templateId);
      if (!template) {
        throw new Error('Template not found');
      }

      // Create workflow definition from template
      const workflowDefinition: WorkflowDefinition = {
        name: customizations?.name || template.name,
        description: customizations?.description || template.description,
        steps: template.steps.map(step => ({
          ...step,
          status: 'pending',
        })),
        triggers: [],
        variables: {},
        createdBy: userId,
      };

      // Create the workflow
      const workflow = await this.workflowEngine.createWorkflow(workflowDefinition);

      // Start workflow instance for the request
      const instanceId = await this.workflowEngine.startWorkflow(
        workflow.id,
        {
          requestId,
          templateId,
          priority: customizations?.priority || template.priority,
          slaHours: customizations?.slaHours || template.slaHours,
        }
      );

      // Log the template usage
      await this.auditService.logActivity({
        userId,
        action: 'create_workflow_from_template',
        resourceType: 'workflow',
        resourceId: instanceId,
        details: {
          templateId,
          templateName: template.name,
          requestId,
        },
      });

      return instanceId;
    } catch (error) {
      console.error('Error creating workflow from template:', error);
      throw error;
    }
  }

  // Apply template to existing request
  async applyTemplateToRequest(
    requestId: string,
    templateId: string,
    userId: string
  ): Promise<void> {
    try {
      const template = await this.getTemplate(templateId);
      if (!template) {
        throw new Error('Template not found');
      }

      // Get the request
      const { data: request, error } = await this.supabase
        .from('request_forms')
        .select('*')
        .eq('id', requestId)
        .single();

      if (error || !request) {
        throw new Error('Request not found');
      }

      // Check if workflow already exists
      const { data: existingWorkflow } = await this.supabase
        .from('workflow_instances')
        .select('id')
        .eq('context->requestId', requestId)
        .single();

      if (existingWorkflow) {
        throw new Error('Request already has a workflow');
      }

      // Create workflow from template
      await this.createWorkflowFromTemplate(
        templateId,
        requestId,
        userId,
        {
          priority: request.priority,
        }
      );

      // Update request with template info
      await this.supabase
        .from('request_forms')
        .update({
          workflow_template_id: templateId,
          updated_at: new Date().toISOString(),
        })
        .eq('id', requestId);

    } catch (error) {
      console.error('Error applying template to request:', error);
      throw error;
    }
  }

  // Get recommended templates for a request
  async getRecommendedTemplates(
    serviceCategoryId: string,
    requestContext?: {
      priority?: string;
      departmentId?: string;
      tags?: string[];
    }
  ): Promise<WorkflowTemplate[]> {
    // Start with templates matching the service category
    let templates = workflowTemplates.filter(
      t => t.serviceCategoryId === serviceCategoryId
    );

    // If no exact match, try to find by tags
    if (templates.length === 0 && requestContext?.tags) {
      templates = workflowTemplates.filter(t =>
        t.tags.some(tag => requestContext.tags?.includes(tag))
      );
    }

    // Sort by relevance (priority match, etc.)
    templates.sort((a, b) => {
      let scoreA = 0;
      let scoreB = 0;

      // Priority match
      if (requestContext?.priority) {
        if (a.priority === requestContext.priority) scoreA += 2;
        if (b.priority === requestContext.priority) scoreB += 2;
      }

      // Tag matches
      if (requestContext?.tags) {
        scoreA += a.tags.filter(tag => requestContext.tags?.includes(tag)).length;
        scoreB += b.tags.filter(tag => requestContext.tags?.includes(tag)).length;
      }

      return scoreB - scoreA;
    });

    return templates.slice(0, 5); // Return top 5 recommendations
  }

  // Clone and customize template
  async cloneTemplate(
    templateId: string,
    customizations: {
      name: string;
      description?: string;
      steps?: any[];
      slaHours?: number;
      priority?: string;
    },
    userId: string
  ): Promise<WorkflowTemplate> {
    const baseTemplate = await this.getTemplate(templateId);
    if (!baseTemplate) {
      throw new Error('Base template not found');
    }

    // Create custom template (in memory - could be saved to DB)
    const customTemplate: WorkflowTemplate = {
      ...baseTemplate,
      id: `custom-${Date.now()}`,
      name: customizations.name,
      description: customizations.description || baseTemplate.description,
      steps: customizations.steps || baseTemplate.steps,
      slaHours: customizations.slaHours || baseTemplate.slaHours,
      priority: customizations.priority as any || baseTemplate.priority,
    };

    // Log the cloning
    await this.auditService.logActivity({
      userId,
      action: 'clone_workflow_template',
      resourceType: 'workflow_template',
      resourceId: customTemplate.id,
      details: {
        baseTemplateId: templateId,
        customizations,
      },
    });

    return customTemplate;
  }

  // Get template usage statistics
  async getTemplateUsageStats(
    templateId?: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<{
    templateId: string;
    templateName: string;
    usageCount: number;
    avgCompletionTime: number;
    successRate: number;
  }[]> {
    try {
      let query = this.supabase
        .from('workflow_instances')
        .select('*')
        .not('context->templateId', 'is', null);

      if (templateId) {
        query = query.eq('context->templateId', templateId);
      }

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { data: instances, error } = await query;

      if (error) throw error;

      // Group by template and calculate stats
      const statsMap = new Map<string, any>();

      for (const instance of instances || []) {
        const tid = instance.context?.templateId;
        if (!tid) continue;

        if (!statsMap.has(tid)) {
          const template = await this.getTemplate(tid);
          statsMap.set(tid, {
            templateId: tid,
            templateName: template?.name || 'Unknown',
            usageCount: 0,
            totalCompletionTime: 0,
            completedCount: 0,
            successCount: 0,
          });
        }

        const stats = statsMap.get(tid);
        stats.usageCount++;

        if (instance.status === 'completed' && instance.completed_at) {
          stats.completedCount++;
          const completionTime = new Date(instance.completed_at).getTime() - 
                               new Date(instance.created_at).getTime();
          stats.totalCompletionTime += completionTime;
          stats.successCount++;
        } else if (instance.status === 'failed') {
          stats.completedCount++;
        }
      }

      // Calculate final stats
      const results = Array.from(statsMap.values()).map(stats => ({
        templateId: stats.templateId,
        templateName: stats.templateName,
        usageCount: stats.usageCount,
        avgCompletionTime: stats.completedCount > 0 
          ? Math.round(stats.totalCompletionTime / stats.completedCount / 1000 / 60) // minutes
          : 0,
        successRate: stats.completedCount > 0
          ? Math.round((stats.successCount / stats.completedCount) * 100)
          : 0,
      }));

      return results.sort((a, b) => b.usageCount - a.usageCount);
    } catch (error) {
      console.error('Error getting template usage stats:', error);
      throw error;
    }
  }

  // Initialize default templates in database (optional)
  async initializeDefaultTemplates(): Promise<void> {
    try {
      // Check if templates already exist
      const { data: existing } = await this.supabase
        .from('workflow_templates')
        .select('id')
        .limit(1);

      if (existing && existing.length > 0) {
        console.log('Templates already initialized');
        return;
      }

      // Insert default templates
      for (const template of workflowTemplates) {
        await this.supabase
          .from('workflow_templates')
          .insert({
            id: template.id,
            name: template.name,
            name_jp: template.nameJp,
            description: template.description,
            description_jp: template.descriptionJp,
            category: template.category,
            service_category_id: template.serviceCategoryId,
            steps: template.steps,
            sla_hours: template.slaHours,
            priority: template.priority,
            required_approvals: template.requiredApprovals,
            tags: template.tags,
            is_active: true,
            created_at: new Date().toISOString(),
          });
      }

      console.log('Default templates initialized successfully');
    } catch (error) {
      console.error('Error initializing templates:', error);
      throw error;
    }
  }
}
