"use strict";(()=>{var e={};e.id=3520,e.ids=[3520],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},48335:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(42706),a=t(28203),n=t(45994),p=t(39187),i=t(61487),u=t(74168);async function d(e){try{let r=(0,i.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let o=e.nextUrl.searchParams,a=o.get("startDate"),n=o.get("endDate"),d=o.get("format")||"json";if(!a||!n)return p.NextResponse.json({error:"Start date and end date are required"},{status:400});let l=await u.i.exportSLAReport(new Date(a),new Date(n),d);if("csv"===d)return new p.NextResponse(l,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="sla-report-${a}-${n}.csv"`}});return p.NextResponse.json(l)}catch(e){return console.error("Error exporting SLA report:",e),p.NextResponse.json({error:"Failed to export SLA report"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/sla/export/route",pathname:"/api/workflows/sla/export",filename:"route",bundlePath:"app/api/workflows/sla/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\export\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:c,serverHooks:w}=l;function f(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:c})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(48335));module.exports=s})();