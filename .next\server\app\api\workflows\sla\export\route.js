"use strict";(()=>{var e={};e.id=3520,e.ids=[3520],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},48335:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var o={};t.r(o),t.d(o,{GET:()=>u});var s=t(42706),a=t(28203),n=t(45994),p=t(39187),i=t(55977);async function u(e){try{let r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let s=e.nextUrl.searchParams,a=s.get("startDate"),n=s.get("endDate"),u=s.get("format")||"json";if(!a||!n)return p.NextResponse.json({error:"Start date and end date are required"},{status:400});let d=await i.i.exportSLAReport(new Date(a),new Date(n),u);if("csv"===u)return new p.NextResponse(d,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="sla-report-${a}-${n}.csv"`}});return p.NextResponse.json(d)}catch(e){return console.error("Error exporting SLA report:",e),p.NextResponse.json({error:"Failed to export SLA report"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let d=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/sla/export/route",pathname:"/api/workflows/sla/export",filename:"route",bundlePath:"app/api/workflows/sla/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\export\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:x}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(48335));module.exports=o})();