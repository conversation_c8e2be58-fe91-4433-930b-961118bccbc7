'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAI, useAICompletion, useAIFormValidation, useAITranslation } from '@/components/providers/ai-provider'
import { CheckCircle, XCircle, Loader2, AlertCircle } from 'lucide-react'
import { AI_MODELS } from '@/lib/config/ai-config'

export default function TestAIAPIPage() {
  const { config, isConfigured, errors, testConnection } = useAI()
  const { complete } = useAICompletion()
  const { validateField } = useAIFormValidation()
  const { translate } = useAITranslation()

  const [testResults, setTestResults] = useState<Record<string, boolean>>({})
  const [isTestingConnection, setIsTestingConnection] = useState<string | null>(null)
  
  // Completion test state
  const [completionPrompt, setCompletionPrompt] = useState('')
  const [completionResult, setCompletionResult] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  
  // Validation test state
  const [validationFieldType, setValidationFieldType] = useState('email')
  const [validationValue, setValidationValue] = useState('')
  const [validationResult, setValidationResult] = useState<any>(null)
  
  // Translation test state
  const [translationText, setTranslationText] = useState('')
  const [translationFrom, setTranslationFrom] = useState<'ja' | 'en'>('ja')
  const [translationTo, setTranslationTo] = useState<'ja' | 'en'>('en')
  const [translationResult, setTranslationResult] = useState('')

  const handleTestConnection = async (provider: 'openai' | 'anthropic') => {
    setIsTestingConnection(provider)
    const result = await testConnection(provider)
    setTestResults(prev => ({ ...prev, [provider]: result }))
    setIsTestingConnection(null)
  }

  const handleTestCompletion = async () => {
    if (!completionPrompt) return
    
    setIsProcessing(true)
    try {
      const result = await complete(completionPrompt, {
        systemPrompt: 'You are a helpful IT support assistant.',
        temperature: 0.7,
        maxTokens: 500
      })
      setCompletionResult(result)
    } catch (error) {
      console.error('Completion error:', error)
      setCompletionResult({ error: error.message })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleTestValidation = async () => {
    if (!validationValue) return
    
    setIsProcessing(true)
    try {
      const result = await validateField(validationFieldType, validationValue)
      setValidationResult(result)
    } catch (error) {
      console.error('Validation error:', error)
      setValidationResult({ error: error.message })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleTestTranslation = async () => {
    if (!translationText) return
    
    setIsProcessing(true)
    try {
      const result = await translate(translationText, translationFrom, translationTo)
      setTranslationResult(result)
    } catch (error) {
      console.error('Translation error:', error)
      setTranslationResult('Translation failed')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="container mx-auto py-10 max-w-6xl">
      <Card>
        <CardHeader>
          <CardTitle>AI API Integration Test</CardTitle>
          <CardDescription>
            Test and verify AI API connections and functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Configuration Status</h3>
            
            {isConfigured ? (
              <Alert className="border-green-500">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription>AI APIs are configured and ready to use</AlertDescription>
              </Alert>
            ) : (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  AI APIs are not properly configured. Errors:
                  <ul className="mt-2 list-disc list-inside">
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Provider Status */}
            <div className="grid grid-cols-2 gap-4">
              {['openai', 'anthropic'].map((provider) => (
                <Card key={provider}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium capitalize">{provider}</h4>
                        <p className="text-sm text-muted-foreground">
                          {config?.providers[provider as keyof typeof config.providers] 
                            ? 'Configured' 
                            : 'Not configured'}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => handleTestConnection(provider as 'openai' | 'anthropic')}
                        disabled={!config?.providers[provider as keyof typeof config.providers] || isTestingConnection === provider}
                      >
                        {isTestingConnection === provider ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : testResults[provider] === true ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : testResults[provider] === false ? (
                          <XCircle className="h-4 w-4 text-red-500" />
                        ) : (
                          'Test'
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Feature Tests */}
          {isConfigured && (
            <Tabs defaultValue="completion" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="completion">Completion</TabsTrigger>
                <TabsTrigger value="validation">Validation</TabsTrigger>
                <TabsTrigger value="translation">Translation</TabsTrigger>
              </TabsList>

              <TabsContent value="completion" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Test AI Completion</CardTitle>
                    <CardDescription>
                      Test basic text generation capabilities
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Prompt</Label>
                      <Textarea
                        value={completionPrompt}
                        onChange={(e) => setCompletionPrompt(e.target.value)}
                        placeholder="Enter a prompt for the AI..."
                        rows={3}
                      />
                    </div>
                    
                    <Button
                      onClick={handleTestCompletion}
                      disabled={!completionPrompt || isProcessing}
                      className="w-full"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        'Generate Completion'
                      )}
                    </Button>

                    {completionResult && (
                      <div className="space-y-2">
                        <h4 className="font-medium">Result:</h4>
                        <div className="p-4 bg-muted rounded-lg">
                          {completionResult.error ? (
                            <p className="text-red-500">Error: {completionResult.error}</p>
                          ) : (
                            <>
                              <p className="whitespace-pre-wrap">{completionResult.text}</p>
                              {completionResult.usage && (
                                <div className="mt-4 text-sm text-muted-foreground">
                                  <p>Provider: {completionResult.provider}</p>
                                  <p>Model: {completionResult.model}</p>
                                  <p>Tokens: {completionResult.usage.totalTokens}</p>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="validation" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Test Form Field Validation</CardTitle>
                    <CardDescription>
                      Test AI-powered form field validation
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Field Type</Label>
                        <Select
                          value={validationFieldType}
                          onValueChange={setValidationFieldType}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="staff_id">Staff ID</SelectItem>
                            <SelectItem value="pc_id">PC ID</SelectItem>
                            <SelectItem value="name_jp">Japanese Name</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Value to Validate</Label>
                        <Textarea
                          value={validationValue}
                          onChange={(e) => setValidationValue(e.target.value)}
                          placeholder="Enter value to validate..."
                          rows={1}
                        />
                      </div>
                    </div>
                    
                    <Button
                      onClick={handleTestValidation}
                      disabled={!validationValue || isProcessing}
                      className="w-full"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Validating...
                        </>
                      ) : (
                        'Validate Field'
                      )}
                    </Button>

                    {validationResult && (
                      <div className="space-y-2">
                        <h4 className="font-medium">Validation Result:</h4>
                        <div className="p-4 bg-muted rounded-lg space-y-2">
                          {validationResult.error ? (
                            <p className="text-red-500">Error: {validationResult.error}</p>
                          ) : (
                            <>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">Valid:</span>
                                <Badge variant={validationResult.isValid ? 'default' : 'destructive'}>
                                  {validationResult.isValid ? 'Yes' : 'No'}
                                </Badge>
                              </div>
                              
                              {validationResult.issues?.length > 0 && (
                                <div>
                                  <span className="font-medium">Issues:</span>
                                  <ul className="list-disc list-inside mt-1">
                                    {validationResult.issues.map((issue: string, index: number) => (
                                      <li key={index} className="text-sm">{issue}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              
                              {validationResult.suggestions?.length > 0 && (
                                <div>
                                  <span className="font-medium">Suggestions:</span>
                                  <ul className="list-disc list-inside mt-1">
                                    {validationResult.suggestions.map((suggestion: string, index: number) => (
                                      <li key={index} className="text-sm">{suggestion}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="translation" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Test Translation</CardTitle>
                    <CardDescription>
                      Test AI-powered translation between Japanese and English
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Text to Translate</Label>
                      <Textarea
                        value={translationText}
                        onChange={(e) => setTranslationText(e.target.value)}
                        placeholder="Enter text to translate..."
                        rows={3}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>From</Label>
                        <Select
                          value={translationFrom}
                          onValueChange={(v) => setTranslationFrom(v as 'ja' | 'en')}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ja">Japanese</SelectItem>
                            <SelectItem value="en">English</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>To</Label>
                        <Select
                          value={translationTo}
                          onValueChange={(v) => setTranslationTo(v as 'ja' | 'en')}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ja">Japanese</SelectItem>
                            <SelectItem value="en">English</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <Button
                      onClick={handleTestTranslation}
                      disabled={!translationText || isProcessing || translationFrom === translationTo}
                      className="w-full"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Translating...
                        </>
                      ) : (
                        'Translate'
                      )}
                    </Button>

                    {translationResult && (
                      <div className="space-y-2">
                        <h4 className="font-medium">Translation:</h4>
                        <div className="p-4 bg-muted rounded-lg">
                          <p className="whitespace-pre-wrap">{translationResult}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
