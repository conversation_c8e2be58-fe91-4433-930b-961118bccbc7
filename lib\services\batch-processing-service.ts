import { createClient } from '@/lib/supabase';
import type { 
  Batch<PERSON>peration, 
  BatchRequest, 
  BatchValidationResult, 
  BatchProcessingResult,
  BatchStatusUpdate,
  BatchItem
} from '@/lib/batch-processing-types';
import { realtimeService } from './realtime-service';

export class BatchProcessingService {
  private supabase = createClient();

  /**
   * Create a new batch operation
   */
  async createBatchOperation(
    requesterId: string,
    request: BatchRequest
  ): Promise<BatchProcessingResult> {
    try {
      // Validate the batch request
      const validation = await this.validateBatchRequest(requesterId, request);
      if (!validation.valid) {
        return {
          success: false,
          message: 'Batch validation failed',
          batch_id: '',
          processed_items: 0,
          failed_items: 0,
          errors: validation.errors.map(e => ({
            item_id: '',
            error_code: 'VALIDATION_ERROR',
            error_message: e.error,
            timestamp: new Date().toISOString()
          }))
        };
      }

      // Create batch operation
      const { data: batchOp, error: batchError } = await this.supabase
        .from('batch_operations')
        .insert({
          requester_id: requesterId,
          operation_type: request.operation_type,
          total_items: request.items.reduce((sum, item) => 
            sum + item.affected_users.length, 0
          ),
          status: 'pending'
        })
        .select()
        .single();

      if (batchError || !batchOp) {
        throw new Error('Failed to create batch operation');
      }

      // Create request items and batch items
      const requestItems = [];
      let itemOrder = 0;

      for (const item of request.items) {
        for (const userId of item.affected_users) {
          for (const resource of item.target_resources) {
            // Create request item
            const { data: reqItem, error: reqError } = await this.supabase
              .from('request_items')
              .insert({
                request_form_id: null, // Will be linked later
                service_category_id: item.service_category_id,
                affected_user_id: userId,
                action_type: item.action_type,
                target_resource: resource,
                request_details: item.request_details,
                status: 'pending'
              })
              .select()
              .single();

            if (reqError || !reqItem) {
              throw new Error('Failed to create request item');
            }

            requestItems.push(reqItem);

            // Create batch item
            await this.supabase
              .from('batch_items')
              .insert({
                batch_operation_id: batchOp.id,
                request_item_id: reqItem.id,
                item_order: itemOrder++,
                status: 'pending'
              });
          }
        }
      }

      // Start processing the batch
      this.processBatch(batchOp.id, requestItems.map(r => r.id));

      return {
        success: true,
        message: 'Batch operation created successfully',
        batch_id: batchOp.batch_id,
        processed_items: 0,
        failed_items: 0
      };
    } catch (error) {
      console.error('Error creating batch operation:', error);
      return {
        success: false,
        message: 'Failed to create batch operation',
        batch_id: '',
        processed_items: 0,
        failed_items: 0,
        errors: [{
          item_id: '',
          error_code: 'SYSTEM_ERROR',
          error_message: error.message,
          timestamp: new Date().toISOString()
        }]
      };
    }
  }

  /**
   * Validate batch request
   */
  private async validateBatchRequest(
    requesterId: string,
    request: BatchRequest
  ): Promise<BatchValidationResult> {
    const { data, error } = await this.supabase
      .rpc('validate_batch_request', {
        p_requester_id: requesterId,
        p_request_items: JSON.stringify(request.items)
      });

    if (error) {
      return {
        valid: false,
        errors: [{
          item: request.items[0],
          error: 'Validation failed: ' + error.message
        }]
      };
    }

    return data;
  }

  /**
   * Process batch operation
   */
  private async processBatch(
    batchOperationId: string,
    requestItemIds: string[]
  ): Promise<void> {
    try {
      // Call the database function to process the batch
      const { data, error } = await this.supabase
        .rpc('process_batch_transaction', {
          p_batch_operation_id: batchOperationId,
          p_request_items: requestItemIds
        });

      if (error) {
        console.error('Batch processing error:', error);
        
        // Update batch status to failed
        await this.supabase
          .from('batch_operations')
          .update({
            status: 'failed',
            completed_at: new Date().toISOString(),
            error_details: [{
              item_id: '',
              error_code: 'PROCESSING_ERROR',
              error_message: error.message,
              timestamp: new Date().toISOString()
            }]
          })
          .eq('id', batchOperationId);
      }
    } catch (error) {
      console.error('Batch processing exception:', error);
    }
  }

  /**
   * Get batch operation status
   */
  async getBatchStatus(batchId: string): Promise<BatchOperation | null> {
    const { data, error } = await this.supabase
      .from('batch_operations')
      .select('*')
      .eq('batch_id', batchId)
      .single();

    if (error) {
      console.error('Error fetching batch status:', error);
      return null;
    }

    return data;
  }

  /**
   * Get batch items
   */
  async getBatchItems(batchOperationId: string): Promise<BatchItem[]> {
    const { data, error } = await this.supabase
      .from('batch_items')
      .select('*')
      .eq('batch_operation_id', batchOperationId)
      .order('item_order');

    if (error) {
      console.error('Error fetching batch items:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Subscribe to batch status updates
   */
  subscribeToBatchUpdates(
    batchId: string,
    onUpdate: (update: BatchStatusUpdate) => void
  ): () => void {
    const channel = this.supabase
      .channel(`batch-${batchId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'batch_operations',
          filter: `batch_id=eq.${batchId}`
        },
        (payload) => {
          const batch = payload.new as BatchOperation;
          onUpdate({
            batch_id: batch.batch_id,
            status: batch.status,
            progress_percentage: batch.progress_percentage,
            completed_items: batch.completed_items,
            total_items: batch.total_items
          });
        }
      )
      .subscribe();

    return () => {
      this.supabase.removeChannel(channel);
    };
  }

  /**
   * Cancel batch operation
   */
  async cancelBatch(batchId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('batch_operations')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString()
      })
      .eq('batch_id', batchId)
      .eq('status', 'pending');

    if (error) {
      console.error('Error cancelling batch:', error);
      return false;
    }

    // Update all pending batch items
    await this.supabase
      .from('batch_items')
      .update({ status: 'cancelled' })
      .eq('batch_operation_id', batchId)
      .eq('status', 'pending');

    return true;
  }

  /**
   * Retry failed batch items
   */
  async retryFailedItems(batchId: string): Promise<BatchProcessingResult> {
    // Get failed items
    const { data: failedItems, error } = await this.supabase
      .from('batch_items')
      .select('request_item_id')
      .eq('batch_operation_id', batchId)
      .eq('status', 'failed');

    if (error || !failedItems || failedItems.length === 0) {
      return {
        success: false,
        message: 'No failed items to retry',
        batch_id: batchId,
        processed_items: 0,
        failed_items: 0
      };
    }

    // Reset status to pending
    await this.supabase
      .from('batch_items')
      .update({ status: 'pending', error_message: null })
      .eq('batch_operation_id', batchId)
      .eq('status', 'failed');

    // Reprocess
    await this.processBatch(
      batchId,
      failedItems.map(item => item.request_item_id)
    );

    return {
      success: true,
      message: 'Retrying failed items',
      batch_id: batchId,
      processed_items: 0,
      failed_items: failedItems.length
    };
  }
}

export const batchProcessingService = new BatchProcessingService();
