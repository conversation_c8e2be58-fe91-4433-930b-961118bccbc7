/**
 * Core Web Vitals Monitor
 * Tracks and analyzes Core Web Vitals performance metrics
 */

import { metrics } from '@/lib/monitoring/metrics'

interface WebVitalsMetric {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB' | 'INP'
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
  navigationType: string
  timestamp: number
}

interface PerformanceEntry {
  name: string
  entryType: string
  startTime: number
  duration: number
  [key: string]: any
}

interface WebVitalsReport {
  metrics: WebVitalsMetric[]
  summary: {
    score: number
    rating: 'good' | 'needs-improvement' | 'poor'
    recommendations: string[]
  }
  trends: {
    [key: string]: {
      current: number
      previous: number
      change: number
      trend: 'improving' | 'stable' | 'degrading'
    }
  }
}

class WebVitalsMonitor {
  private metrics: WebVitalsMetric[] = []
  private observers: PerformanceObserver[] = []
  private thresholds = {
    CLS: { good: 0.1, poor: 0.25 },
    FID: { good: 100, poor: 300 },
    FCP: { good: 1800, poor: 3000 },
    LCP: { good: 2500, poor: 4000 },
    TTFB: { good: 800, poor: 1800 },
    INP: { good: 200, poor: 500 }
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers()
      this.setupVisibilityChangeHandler()
    }
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    // Largest Contentful Paint (LCP)
    this.observeMetric('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1]
      this.recordMetric('LCP', lastEntry.startTime, lastEntry.id)
    })

    // First Input Delay (FID)
    this.observeMetric('first-input', (entries) => {
      const firstEntry = entries[0]
      const fid = firstEntry.processingStart - firstEntry.startTime
      this.recordMetric('FID', fid, firstEntry.id)
    })

    // Cumulative Layout Shift (CLS)
    this.observeMetric('layout-shift', (entries) => {
      let clsValue = 0
      for (const entry of entries) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      }
      this.recordMetric('CLS', clsValue, 'cls-session')
    })

    // First Contentful Paint (FCP)
    this.observeMetric('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        this.recordMetric('FCP', fcpEntry.startTime, fcpEntry.id)
      }
    })

    // Time to First Byte (TTFB)
    this.observeMetric('navigation', (entries) => {
      const navEntry = entries[0] as any
      if (navEntry) {
        const ttfb = navEntry.responseStart - navEntry.requestStart
        this.recordMetric('TTFB', ttfb, navEntry.id)
      }
    })

    // Interaction to Next Paint (INP) - newer metric
    if ('PerformanceEventTiming' in window) {
      this.observeMetric('event', (entries) => {
        for (const entry of entries) {
          if (entry.duration > 40) { // Only track slow interactions
            this.recordMetric('INP', entry.duration, entry.id)
          }
        }
      })
    }
  }

  /**
   * Observe specific performance metric
   */
  private observeMetric(
    entryType: string,
    callback: (entries: PerformanceEntry[]) => void
  ): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries() as PerformanceEntry[])
      })

      observer.observe({ 
        type: entryType, 
        buffered: true 
      })

      this.observers.push(observer)
    } catch (error) {
      console.warn(`Could not observe ${entryType}:`, error)
    }
  }

  /**
   * Record a web vital metric
   */
  private recordMetric(
    name: WebVitalsMetric['name'],
    value: number,
    id: string
  ): void {
    const rating = this.getRating(name, value)
    const navigationType = this.getNavigationType()
    
    const metric: WebVitalsMetric = {
      name,
      value,
      rating,
      delta: value, // For first measurement, delta equals value
      id,
      navigationType,
      timestamp: Date.now()
    }

    // Update delta for existing metrics
    const existingIndex = this.metrics.findIndex(m => m.name === name && m.id === id)
    if (existingIndex >= 0) {
      const existing = this.metrics[existingIndex]
      metric.delta = value - existing.value
      this.metrics[existingIndex] = metric
    } else {
      this.metrics.push(metric)
    }

    // Send to analytics
    this.sendMetricToAnalytics(metric)

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`${name}: ${value.toFixed(2)} (${rating})`)
    }
  }

  /**
   * Get rating for a metric value
   */
  private getRating(name: WebVitalsMetric['name'], value: number): WebVitalsMetric['rating'] {
    const threshold = this.thresholds[name]
    if (!threshold) return 'good'

    if (value <= threshold.good) return 'good'
    if (value <= threshold.poor) return 'needs-improvement'
    return 'poor'
  }

  /**
   * Get navigation type
   */
  private getNavigationType(): string {
    if (typeof window === 'undefined') return 'unknown'
    
    const navEntry = performance.getEntriesByType('navigation')[0] as any
    return navEntry?.type || 'unknown'
  }

  /**
   * Send metric to analytics service
   */
  private sendMetricToAnalytics(metric: WebVitalsMetric): void {
    // Send to internal metrics system
    metrics.timing(`web_vitals.${metric.name.toLowerCase()}`, metric.value, {
      rating: metric.rating,
      navigation_type: metric.navigationType
    })

    // Send to external analytics (Google Analytics, etc.)
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.value),
        custom_map: {
          metric_rating: metric.rating,
          metric_delta: metric.delta
        }
      })
    }
  }

  /**
   * Setup visibility change handler to send final metrics
   */
  private setupVisibilityChangeHandler(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.sendFinalMetrics()
      }
    })

    // Also send on page unload
    window.addEventListener('beforeunload', () => {
      this.sendFinalMetrics()
    })
  }

  /**
   * Send final metrics when page becomes hidden
   */
  private sendFinalMetrics(): void {
    // Send beacon with final metrics
    if (navigator.sendBeacon && this.metrics.length > 0) {
      const payload = JSON.stringify({
        url: window.location.href,
        metrics: this.metrics,
        timestamp: Date.now(),
        userAgent: navigator.userAgent
      })

      navigator.sendBeacon('/api/analytics/web-vitals', payload)
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): WebVitalsMetric[] {
    return [...this.metrics]
  }

  /**
   * Get performance report
   */
  getReport(): WebVitalsReport {
    const currentMetrics = this.getLatestMetrics()
    const score = this.calculateScore(currentMetrics)
    const rating = this.getOverallRating(score)
    const recommendations = this.generateRecommendations(currentMetrics)

    return {
      metrics: currentMetrics,
      summary: {
        score,
        rating,
        recommendations
      },
      trends: this.calculateTrends()
    }
  }

  /**
   * Get latest metrics for each type
   */
  private getLatestMetrics(): WebVitalsMetric[] {
    const latest: { [key: string]: WebVitalsMetric } = {}
    
    for (const metric of this.metrics) {
      if (!latest[metric.name] || metric.timestamp > latest[metric.name].timestamp) {
        latest[metric.name] = metric
      }
    }

    return Object.values(latest)
  }

  /**
   * Calculate overall performance score
   */
  private calculateScore(metrics: WebVitalsMetric[]): number {
    if (metrics.length === 0) return 0

    const weights = {
      LCP: 0.25,
      FID: 0.25,
      CLS: 0.25,
      FCP: 0.15,
      TTFB: 0.05,
      INP: 0.05
    }

    let totalScore = 0
    let totalWeight = 0

    for (const metric of metrics) {
      const weight = weights[metric.name] || 0
      const score = metric.rating === 'good' ? 100 : 
                   metric.rating === 'needs-improvement' ? 50 : 0
      
      totalScore += score * weight
      totalWeight += weight
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0
  }

  /**
   * Get overall rating based on score
   */
  private getOverallRating(score: number): WebVitalsMetric['rating'] {
    if (score >= 90) return 'good'
    if (score >= 50) return 'needs-improvement'
    return 'poor'
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(metrics: WebVitalsMetric[]): string[] {
    const recommendations: string[] = []

    for (const metric of metrics) {
      if (metric.rating === 'poor') {
        switch (metric.name) {
          case 'LCP':
            recommendations.push('Optimize images and reduce server response times to improve LCP')
            break
          case 'FID':
            recommendations.push('Reduce JavaScript execution time and optimize event handlers for better FID')
            break
          case 'CLS':
            recommendations.push('Set explicit dimensions for images and ads to prevent layout shifts')
            break
          case 'FCP':
            recommendations.push('Optimize critical rendering path and reduce render-blocking resources')
            break
          case 'TTFB':
            recommendations.push('Optimize server response times and consider using a CDN')
            break
          case 'INP':
            recommendations.push('Optimize JavaScript execution and reduce main thread blocking')
            break
        }
      }
    }

    return recommendations
  }

  /**
   * Calculate performance trends
   */
  private calculateTrends(): WebVitalsReport['trends'] {
    // This would compare with historical data
    // For now, return empty trends
    return {}
  }

  /**
   * Cleanup observers
   */
  cleanup(): void {
    for (const observer of this.observers) {
      observer.disconnect()
    }
    this.observers = []
  }
}

// Singleton instance
let webVitalsMonitor: WebVitalsMonitor | null = null

export const getWebVitalsMonitor = (): WebVitalsMonitor => {
  if (!webVitalsMonitor) {
    webVitalsMonitor = new WebVitalsMonitor()
  }
  return webVitalsMonitor
}

// Initialize monitoring on client side
if (typeof window !== 'undefined') {
  getWebVitalsMonitor()
}

export { WebVitalsMonitor }
export type { WebVitalsMetric, WebVitalsReport }
