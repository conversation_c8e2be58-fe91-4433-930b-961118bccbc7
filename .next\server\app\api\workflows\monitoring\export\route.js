"use strict";(()=>{var e={};e.id=5710,e.ids=[5710],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},61930:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>_});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(42706),o=r(28203),n=r(45994),i=r(39187),p=r(73865),d=r(44512);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("timeRange")||"7d",a=t.get("format")||"csv",s=await (0,d.UL)(),o=(0,p.createServerSupabase)(s),n=new Date,u=new Date;switch(r){case"24h":u.setDate(n.getDate()-1);break;case"7d":default:u.setDate(n.getDate()-7);break;case"30d":u.setDate(n.getDate()-30);break;case"90d":u.setDate(n.getDate()-90)}let{data:c,error:l}=await o.from("workflow_instances").select(`
        id,
        status,
        priority,
        created_at,
        updated_at,
        completed_at,
        request_forms!workflow_instances_context_fkey (
          title,
          staff!request_forms_requester_id_fkey (
            name_en,
            name_jp,
            divisions!staff_division_id_fkey (
              name_en,
              name_jp
            )
          ),
          service_categories!request_forms_service_category_id_fkey (
            name_en,
            name_jp
          )
        ),
        sla_tracking (
          sla_status,
          target_date
        )
      `).gte("created_at",u.toISOString()).lte("created_at",n.toISOString()).order("created_at",{ascending:!1});if(l)throw l;if("csv"!==a)return i.NextResponse.json(c);{let e=c?.map(e=>{let t=e.request_forms?.[0],r=t?.staff?.[0],a=r?.divisions?.[0],s=t?.service_categories?.[0],o=e.sla_tracking?.[0];return[e.id,t?.title||"",e.status,e.priority||"medium",a?.name_en||"",s?.name_en||"",r?.name_en||"",o?.sla_status||"on_track",e.created_at,e.updated_at,e.completed_at||""]})||[],t=["Workflow ID,Title,Status,Priority,Department,Category,Requester,SLA Status,Created At,Updated At,Completed At",...e.map(e=>e.map(e=>`"${e}"`).join(","))].join("\n");return new i.NextResponse(t,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="workflow-monitoring-${new Date().toISOString()}.csv"`}})}}catch(e){return console.error("Error exporting workflow data:",e),i.NextResponse.json({error:"Failed to export workflow data"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/export/route",pathname:"/api/workflows/monitoring/export",filename:"route",bundlePath:"app/api/workflows/monitoring/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\export\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:_,serverHooks:m}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:_})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>r(61930));module.exports=a})();