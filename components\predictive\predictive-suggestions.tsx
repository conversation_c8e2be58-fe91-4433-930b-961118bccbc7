'use client'

import React, { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { 
  <PERSON>rkles, 
  User, 
  Users, 
  Brain, 
  Check, 
  X,
  TrendingUp,
  Loader2 
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { 
  predictiveFormService, 
  type PredictionSuggestion,
  type FieldPrediction 
} from '@/lib/services/predictive-form-service'
import { toast } from '@/components/ui/use-toast'

interface PredictiveSuggestionsProps {
  formType: string
  fieldName: string
  currentValue: string
  formData: Record<string, any>
  onAcceptSuggestion: (value: string) => void
  className?: string
}

export function PredictiveSuggestions({
  formType,
  fieldName,
  currentValue,
  formData,
  onAcceptSuggestion,
  className
}: PredictiveSuggestionsProps) {
  const [predictions, setPredictions] = useState<FieldPrediction[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [acceptedSuggestions, setAcceptedSuggestions] = useState<Set<string>>(new Set())

  useEffect(() => {
    const fetchPredictions = async () => {
      if (!fieldName || currentValue.length > 0) return

      setIsLoading(true)
      try {
        const result = await predictiveFormService.getPredictions(
          formType,
          fieldName,
          formData,
          false
        )
        setPredictions(result.predictions)
      } catch (error) {
        console.error('Failed to fetch predictions:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPredictions()
  }, [formType, fieldName, formData, currentValue])

  const handleAcceptSuggestion = async (suggestion: PredictionSuggestion) => {
    onAcceptSuggestion(suggestion.value)
    setAcceptedSuggestions(prev => new Set(prev).add(suggestion.value))
    
    // Record usage
    await predictiveFormService.recordFieldUsage(
      formType,
      fieldName,
      suggestion.value,
      true
    )

    toast({
      title: '予測を適用しました',
      description: `${suggestion.value} が選択されました。`,
      duration: 2000
    })
  }

  const handleRejectSuggestion = async (suggestion: PredictionSuggestion) => {
    // Record rejection
    await predictiveFormService.recordFieldUsage(
      formType,
      fieldName,
      suggestion.value,
      false
    )
  }

  const getSourceIcon = (source: PredictionSuggestion['source']) => {
    switch (source) {
      case 'personal':
        return <User className="h-3 w-3" />
      case 'department':
        return <Users className="h-3 w-3" />
      case 'ai':
        return <Brain className="h-3 w-3" />
      default:
        return <Sparkles className="h-3 w-3" />
    }
  }
  const getSourceLabel = (source: PredictionSuggestion['source']) => {
    switch (source) {
      case 'personal':
        return '個人履歴'
      case 'department':
        return '部門パターン'
      case 'ai':
        return 'AI予測'
      default:
        return '予測'
    }
  }

  const currentPrediction = predictions.find(p => p.fieldName === fieldName)
  if (!currentPrediction || currentPrediction.suggestions.length === 0) {
    return null
  }

  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-2 text-sm text-muted-foreground", className)}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>予測を読み込み中...</span>
      </div>
    )
  }

  return (
    <Card className={cn("mt-2 p-3", className)}>
      <CardContent className="p-0">
        <div className="flex items-center gap-2 mb-2">
          <Sparkles className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">予測候補</span>
          <Badge variant="secondary" className="text-xs">
            <TrendingUp className="h-3 w-3 mr-1" />
            AI支援
          </Badge>
        </div>

        <div className="space-y-2">
          {currentPrediction.suggestions.map((suggestion, index) => (
            <div
              key={index}
              className={cn(
                "flex items-center justify-between p-2 rounded-md border",
                "hover:bg-accent/50 transition-colors",
                acceptedSuggestions.has(suggestion.value) && "bg-accent"
              )}
            >              <div className="flex items-center gap-2 flex-1">
                <div className="flex items-center gap-1">
                  {getSourceIcon(suggestion.source)}
                  <span className="text-xs text-muted-foreground">
                    {getSourceLabel(suggestion.source)}
                  </span>
                </div>
                <span className="text-sm font-medium">{suggestion.value}</span>
                <Badge variant="outline" className="text-xs">
                  {Math.round(suggestion.confidence * 100)}%
                </Badge>
              </div>

              <div className="flex items-center gap-1">
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-7 w-7"
                  onClick={() => handleAcceptSuggestion(suggestion)}
                  disabled={acceptedSuggestions.has(suggestion.value)}
                >
                  <Check className="h-4 w-4 text-green-600" />
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-7 w-7"
                  onClick={() => handleRejectSuggestion(suggestion)}
                >
                  <X className="h-4 w-4 text-red-600" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-2 pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            これらの予測は、あなたの過去の入力パターンと部門の傾向に基づいています。
          </p>
        </div>
      </CardContent>
    </Card>
  )
}