"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/auth-docs";
exports.ids = ["pages/auth-docs"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth-docs&preferredRegion=&absolutePagePath=.%2Fpages%5Cauth-docs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth-docs&preferredRegion=&absolutePagePath=.%2Fpages%5Cauth-docs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./node_modules/next/dist/pages/_app.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\auth-docs.tsx */ \"./pages/auth-docs.tsx\");\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\n// Import the app and document modules.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst PagesRouteModule = next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule;\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/auth-docs\",\n        pathname: \"/auth-docs\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: (private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default()),\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_auth_docs_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth-docs&preferredRegion=&absolutePagePath=.%2Fpages%5Cauth-docs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./lib/api/auth-documentation.tsx":
/*!****************************************!*\
  !*** ./lib/api/auth-documentation.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthFlowDocumentation: () => (/* binding */ AuthFlowDocumentation),\n/* harmony export */   AuthFlowType: () => (/* binding */ AuthFlowType)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * Authentication Documentation Component\r\n * \r\n * This component provides comprehensive documentation for authentication flows,\r\n * including OAuth2, JWT, and other security implementations.\r\n */ \n\nvar AuthFlowType;\n(function(AuthFlowType) {\n    AuthFlowType[\"OAUTH2_AUTHORIZATION_CODE\"] = \"oauth2_authorization_code\";\n    AuthFlowType[\"OAUTH2_IMPLICIT\"] = \"oauth2_implicit\";\n    AuthFlowType[\"OAUTH2_CLIENT_CREDENTIALS\"] = \"oauth2_client_credentials\";\n    AuthFlowType[\"OAUTH2_PASSWORD\"] = \"oauth2_password\";\n    AuthFlowType[\"JWT\"] = \"jwt\";\n    AuthFlowType[\"API_KEY\"] = \"api_key\";\n    AuthFlowType[\"BASIC_AUTH\"] = \"basic_auth\";\n    AuthFlowType[\"DIGEST_AUTH\"] = \"digest_auth\";\n    AuthFlowType[\"MFA\"] = \"mfa\";\n    AuthFlowType[\"SOCIAL_AUTH\"] = \"social_auth\";\n    AuthFlowType[\"CUSTOM\"] = \"custom\";\n})(AuthFlowType || (AuthFlowType = {}));\n/**\r\n * Default styles\r\n */ const defaultStyles = {\n    container: {\n        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n        maxWidth: \"100%\",\n        margin: \"0 auto\",\n        padding: \"1rem\"\n    },\n    header: {\n        marginBottom: \"2rem\"\n    },\n    title: {\n        fontSize: \"2rem\",\n        fontWeight: \"bold\",\n        margin: \"0 0 0.5rem 0\"\n    },\n    description: {\n        fontSize: \"1rem\",\n        margin: \"0 0 1rem 0\",\n        color: \"#666\"\n    },\n    section: {\n        marginBottom: \"2rem\"\n    },\n    sectionTitle: {\n        fontSize: \"1.5rem\",\n        fontWeight: \"bold\",\n        margin: \"0 0 0.5rem 0\"\n    },\n    sectionDescription: {\n        fontSize: \"1rem\",\n        margin: \"0 0 1rem 0\",\n        color: \"#666\"\n    },\n    codeBlock: {\n        fontFamily: \"monospace\",\n        backgroundColor: \"#f5f5f5\",\n        padding: \"1rem\",\n        borderRadius: \"4px\",\n        overflow: \"auto\",\n        fontSize: \"0.875rem\",\n        marginBottom: \"1rem\"\n    },\n    sequenceDiagram: {\n        width: \"100%\",\n        maxWidth: \"800px\",\n        margin: \"1rem 0\",\n        padding: \"1rem\",\n        backgroundColor: \"#f9f9f9\",\n        borderRadius: \"4px\",\n        overflow: \"auto\"\n    }\n};\n/**\r\n * Authentication flow documentation component\r\n */ const AuthFlowDocumentation = ({ flowType, config = {}, styles = {}, classNames = {} })=>{\n    // Merge styles with defaults\n    const mergedStyles = {\n        container: {\n            ...defaultStyles.container,\n            ...styles.container\n        },\n        header: {\n            ...defaultStyles.header,\n            ...styles.header\n        },\n        title: {\n            ...defaultStyles.title,\n            ...styles.title\n        },\n        description: {\n            ...defaultStyles.description,\n            ...styles.description\n        },\n        section: {\n            ...defaultStyles.section,\n            ...styles.section\n        },\n        sectionTitle: {\n            ...defaultStyles.sectionTitle,\n            ...styles.sectionTitle\n        },\n        sectionDescription: {\n            ...defaultStyles.sectionDescription,\n            ...styles.sectionDescription\n        },\n        codeBlock: {\n            ...defaultStyles.codeBlock,\n            ...styles.codeBlock\n        },\n        sequenceDiagram: {\n            ...defaultStyles.sequenceDiagram,\n            ...styles.sequenceDiagram\n        }\n    };\n    /**\r\n   * Get flow title\r\n   */ const getFlowTitle = ()=>{\n        switch(flowType){\n            case \"oauth2_authorization_code\":\n                return \"OAuth 2.0 Authorization Code Flow\";\n            case \"oauth2_implicit\":\n                return \"OAuth 2.0 Implicit Flow\";\n            case \"oauth2_client_credentials\":\n                return \"OAuth 2.0 Client Credentials Flow\";\n            case \"oauth2_password\":\n                return \"OAuth 2.0 Password Flow\";\n            case \"jwt\":\n                return \"JWT Authentication\";\n            case \"api_key\":\n                return \"API Key Authentication\";\n            case \"basic_auth\":\n                return \"Basic Authentication\";\n            case \"digest_auth\":\n                return \"Digest Authentication\";\n            case \"mfa\":\n                return \"Multi-Factor Authentication\";\n            case \"social_auth\":\n                return \"Social Authentication\";\n            case \"custom\":\n                return \"Custom Authentication\";\n            default:\n                return \"Authentication Documentation\";\n        }\n    };\n    /**\r\n   * Get flow description\r\n   */ const getFlowDescription = ()=>{\n        switch(flowType){\n            case \"oauth2_authorization_code\":\n                return \"The Authorization Code flow is used to obtain both access tokens and refresh tokens and is optimized for confidential clients.\";\n            case \"oauth2_implicit\":\n                return \"The Implicit flow is used to obtain access tokens and is optimized for public clients known to operate a particular redirection URI.\";\n            case \"oauth2_client_credentials\":\n                return \"The Client Credentials flow is used to obtain an access token outside the context of a user.\";\n            case \"oauth2_password\":\n                return \"The Password flow is used to exchange a username and password for an access token.\";\n            case \"jwt\":\n                return \"JSON Web Token (JWT) is a compact, URL-safe means of representing claims to be transferred between two parties.\";\n            case \"api_key\":\n                return \"API keys are simple encrypted strings that identify an application or project without any additional information.\";\n            case \"basic_auth\":\n                return \"Basic Authentication is a simple authentication scheme built into the HTTP protocol.\";\n            case \"digest_auth\":\n                return \"Digest Authentication is an authentication scheme built into the HTTP protocol that improves upon Basic Authentication by not sending passwords in plaintext.\";\n            case \"mfa\":\n                return \"Multi-Factor Authentication (MFA) is an authentication method that requires the user to provide two or more verification factors to gain access.\";\n            case \"social_auth\":\n                return \"Social Authentication allows users to log in using their existing social media accounts.\";\n            case \"custom\":\n                return \"Custom Authentication is a bespoke authentication scheme designed for specific requirements.\";\n            default:\n                return \"This document provides comprehensive documentation for authentication flows and security implementations.\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: mergedStyles.container,\n        className: classNames.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.header,\n                className: classNames.header,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: mergedStyles.title,\n                        className: classNames.title,\n                        children: getFlowTitle()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: mergedStyles.description,\n                        children: getFlowDescription()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined),\n            flowType === \"oauth2_authorization_code\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: mergedStyles.section,\n                        className: classNames.section,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: mergedStyles.sectionTitle,\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: mergedStyles.sectionDescription,\n                                children: \"The Authorization Code flow is the most secure OAuth 2.0 flow and is recommended for server-side applications. It involves the following steps:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"The client redirects the user to the authorization server\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"The user authenticates and grants permissions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"The authorization server redirects back to the client with an authorization code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"The client exchanges the authorization code for an access token and refresh token\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"The client uses the access token to access protected resources\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: mergedStyles.section,\n                        className: classNames.section,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: mergedStyles.sectionTitle,\n                                children: \"Sequence Diagram\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: mergedStyles.sequenceDiagram,\n                                className: classNames.sequenceDiagram,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    children: `Client                    Authorization Server                Resource Server\r\n  |                               |                                |\r\n  |---(A)- Authorization Request->|                                |\r\n  |                               |                                |\r\n  |<-(B)-- Authorization Code -----|                                |\r\n  |                               |                                |\r\n  |---(C)-- Authorization Code ---|                                |\r\n  |       & Client Credentials    |                                |\r\n  |                               |                                |\r\n  |<-(D)------ Access Token ------|                                |\r\n  |       & Refresh Token         |                                |\r\n  |                               |                                |\r\n  |---(E)------ Access Token ----------------------------->|       |\r\n  |                               |                                |\r\n  |<-(F)---- Protected Resource ----------------------------------|`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n                lineNumber: 462,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\auth-documentation.tsx\",\n        lineNumber: 450,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * import { AuthFlowDocumentation, AuthFlowType } from '../lib/api/auth-documentation';\r\n * \r\n * export default function AuthDocumentationPage() {\r\n *   return (\r\n *     <AuthFlowDocumentation\r\n *       flowType={AuthFlowType.OAUTH2_AUTHORIZATION_CODE}\r\n *       config={{\r\n *         oauth2: {\r\n *           authorizationUrl: 'https://auth.example.com/authorize',\r\n *           tokenUrl: 'https://auth.example.com/token',\r\n *           clientId: 'your-client-id',\r\n *           clientSecret: 'your-client-secret',\r\n *           redirectUri: 'https://your-app.com/callback',\r\n *           scopes: {\r\n *             'read': 'Read access',\r\n *             'write': 'Write access'\r\n *           },\r\n *           pkceMethod: 'S256'\r\n *         }\r\n *       }}\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api/auth-documentation.tsx\n");

/***/ }),

/***/ "./pages/auth-docs.tsx":
/*!*****************************!*\
  !*** ./pages/auth-docs.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthDocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/api/auth-documentation */ \"./lib/api/auth-documentation.tsx\");\n\n\n\n/**\r\n * Authentication Documentation Page\r\n * \r\n * This page displays documentation for various authentication flows.\r\n */ function AuthDocsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1200px\",\n            margin: \"0 auto\",\n            padding: \"2rem 1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    fontSize: \"2.5rem\",\n                    marginBottom: \"2rem\"\n                },\n                children: \"Authentication Documentation\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"3rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__.AuthFlowDocumentation, {\n                    flowType: _lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__.AuthFlowType.OAUTH2_AUTHORIZATION_CODE,\n                    config: {\n                        oauth2: {\n                            authorizationUrl: \"https://auth.example.com/authorize\",\n                            tokenUrl: \"https://auth.example.com/token\",\n                            clientId: \"your-client-id\",\n                            clientSecret: \"your-client-secret\",\n                            redirectUri: \"https://your-app.com/callback\",\n                            scopes: {\n                                \"read\": \"Read access\",\n                                \"write\": \"Write access\"\n                            },\n                            pkceMethod: \"S256\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"3rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__.AuthFlowDocumentation, {\n                    flowType: _lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__.AuthFlowType.JWT,\n                    config: {\n                        jwt: {\n                            tokenUrl: \"https://auth.example.com/token\",\n                            tokenLifetime: 3600,\n                            refreshTokenLifetime: 86400,\n                            tokenType: \"Bearer\",\n                            signingAlgorithm: \"RS256\",\n                            issuer: \"https://example.com\",\n                            audience: \"https://api.example.com\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"3rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__.AuthFlowDocumentation, {\n                    flowType: _lib_api_auth_documentation__WEBPACK_IMPORTED_MODULE_2__.AuthFlowType.API_KEY,\n                    config: {\n                        apiKey: {\n                            name: \"X-API-Key\",\n                            in: \"header\",\n                            prefix: \"Key\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\auth-docs.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/auth-docs.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth-docs&preferredRegion=&absolutePagePath=.%2Fpages%5Cauth-docs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();