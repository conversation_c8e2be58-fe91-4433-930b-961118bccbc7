-- Create roles table
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  permissions JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create staff/users table
CREATE TABLE staff (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  auth_id UUID UNIQUE, -- Reference to Supabase auth.users
  staff_id VARCHAR(10) UNIQUE, -- e.g. R000123
  name_jp TEXT NOT NULL,
  name_en TEXT NOT NULL,
  name_kana TEXT,
  email TEXT UNIQUE,
  pc_login_id TEXT UNIQUE,
  division_id UUID REFERENCES divisions(id),
  group_id UUID REFERENCES groups(id),
  role_id UUID REFERENCES roles(id),
  position TEXT,
  gender VARCHAR(10),
  birth_date DATE,
  employment_type VARCHAR(50),
  union_id UUID REFERENCES unions(id),
  is_active BOOLEAN DEFAULT TRUE,
  pc_id VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indices for better performance
CREATE INDEX idx_staff_division_id ON staff(division_id);
CREATE INDEX idx_staff_group_id ON staff(group_id);
CREATE INDEX idx_staff_role_id ON staff(role_id);
CREATE INDEX idx_staff_union_id ON staff(union_id);
CREATE INDEX idx_staff_email ON staff(email);
CREATE INDEX idx_staff_is_active ON staff(is_active);
