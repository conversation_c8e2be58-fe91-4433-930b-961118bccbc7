:text-blue-400" />
                  <div>
                    <p className="text-sm font-medium">AI アシスタント / AI Assistant</p>
                    <p className="text-xs text-muted-foreground">
                      空欄を自動で入力します / Auto-fill empty fields
                    </p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAIAutoFill}
                  disabled={isLoading}
                  className="bg-white dark:bg-gray-800"
                >
                  <Sparkles className="h-4 w-4 mr-1" />
                  自動入力 / Auto-fill
                </Button>
              </div>
            </div>
          )}

          <Separator />

          {/* Form Fields */}
          <div className="space-y-6">
            {currentFields.map((field) => (
              <EnhancedDynamicFieldWithValidation
                key={field.id}
                field={field}
                value={formData[field.id]}
                onChange={(value) => handleFieldChange(field.id, value)}
                onSuggestionAccept={(suggestion) => handleSuggestionAccept(field.id, suggestion)}
                validation={getFieldValidation(field.id)}
                isValidating={isFieldValidating(field.id)}
                context={context}
              />
            ))}
          </div>

          <Separator />

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <div>
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || isSubmitting}
                >
                  キャンセル / Cancel
                </Button>
              )}
            </div>

            <div className="flex gap-2">
              {currentSection > 0 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isLoading || isSubmitting}
                >
                  前へ / Previous
                </Button>
              )}

              {currentSection < sections.length - 1 ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={isLoading || isSubmitting || Object.values(isValidating).some(v => v)}
                >
                  次へ / Next
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isLoading || isSubmitting || Object.values(isValidating).some(v => v)}
                  className={cn(
                    "min-w-[120px]",
                    isSubmitting && "opacity-50"
                  )}
                >
                  {isSubmitting ? (
                    <>処理中... / Processing...</>
                  ) : (
                    <>送信 / Submit</>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="bg-gray-50 dark:bg-gray-900">
          <CardHeader>
            <CardTitle className="text-sm">Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs overflow-auto">
              {JSON.stringify({
                currentSection,
                formData,
                validationState,
                isValidating
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
