import React from 'react';
import { AuthFlowDocumentation, AuthFlowType } from '../lib/api/auth-documentation';

/**
 * Authentication Documentation Page
 * 
 * This page displays documentation for various authentication flows.
 */
export default function AuthDocsPage() {
  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem 1rem' }}>
      <h1 style={{ fontSize: '2.5rem', marginBottom: '2rem' }}>Authentication Documentation</h1>
      
      <div style={{ marginBottom: '3rem' }}>
        <AuthFlowDocumentation
          flowType={AuthFlowType.OAUTH2_AUTHORIZATION_CODE}
          config={{
            oauth2: {
              authorizationUrl: 'https://auth.example.com/authorize',
              tokenUrl: 'https://auth.example.com/token',
              clientId: 'your-client-id',
              clientSecret: 'your-client-secret',
              redirectUri: 'https://your-app.com/callback',
              scopes: {
                'read': 'Read access',
                'write': 'Write access'
              },
              pkceMethod: 'S256'
            }
          }}
        />
      </div>
      
      <div style={{ marginBottom: '3rem' }}>
        <AuthFlowDocumentation
          flowType={AuthFlowType.JWT}
          config={{
            jwt: {
              tokenUrl: 'https://auth.example.com/token',
              tokenLifetime: 3600,
              refreshTokenLifetime: 86400,
              tokenType: 'Bearer',
              signingAlgorithm: 'RS256',
              issuer: 'https://example.com',
              audience: 'https://api.example.com'
            }
          }}
        />
      </div>
      
      <div style={{ marginBottom: '3rem' }}>
        <AuthFlowDocumentation
          flowType={AuthFlowType.API_KEY}
          config={{
            apiKey: {
              name: 'X-API-Key',
              in: 'header',
              prefix: 'Key'
            }
          }}
        />
      </div>
    </div>
  );
}