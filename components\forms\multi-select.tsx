"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, ChevronDown, X, Check } from 'lucide-react';

interface Option {
  value: string;
  label: string;
}

interface MultiSelectProps {
  options: Option[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: string;
  maxItems?: number;
}

export function MultiSelect({
  options,
  value = [],
  onChange,
  placeholder = '選択してください',
  disabled = false,
  className = '',
  error,
  maxItems,
}: MultiSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState<Option[]>(options);
  const containerRef = useRef<HTMLDivElement>(null);

  // Get the currently selected options
  const selectedOptions = options.filter(option => value.includes(option.value));

  // Filter options when search term changes
  useEffect(() => {
    if (!searchTerm) {
      setFilteredOptions(options);
      return;
    }

    const filtered = options.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredOptions(filtered);
  }, [searchTerm, options]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleToggleOption = (option: Option) => {
    const isSelected = value.includes(option.value);
    
    if (isSelected) {
      // Remove the option
      onChange(value.filter(v => v !== option.value));
    } else {
      // Add the option, respecting maxItems if set
      if (maxItems && value.length >= maxItems) {
        return;
      }
      onChange([...value, option.value]);
    }
  };

  const handleClearAll = () => {
    onChange([]);
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <div
        className={`flex items-center border rounded-md ${
          error ? 'border-red-500' : 'border-gray-300'
        } ${disabled ? 'bg-gray-100 opacity-70' : ''}`}
      >
        <div
          className="flex-grow px-3 py-2 cursor-pointer flex items-center justify-between"
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          {selectedOptions.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {selectedOptions.map(option => (
                <div 
                  key={option.value} 
                  className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center"
                >
                  {option.label}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleOption(option);
                    }}
                    disabled={disabled}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <span className="text-gray-400">{placeholder}</span>
          )}
          <div className="flex items-center">
            {selectedOptions.length > 0 && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-4 w-4 mr-1"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearAll();
                }}
                disabled={disabled}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </div>
        </div>
      </div>

      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}

      {isOpen && !disabled && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="検索..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
                autoFocus
              />
            </div>
          </div>
          <div className="max-h-60 overflow-y-auto">
            {filteredOptions.length === 0 ? (
              <div className="p-3 text-center text-gray-500">結果がありません</div>
            ) : (
              <ul>
                {filteredOptions.map((option) => {
                  const isSelected = value.includes(option.value);
                  const isDisabled = maxItems && value.length >= maxItems && !isSelected;
                  
                  return (
                    <li
                      key={option.value}
                      className={`px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between ${
                        isSelected ? 'bg-blue-50 text-blue-600' : ''
                      } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                      onClick={() => !isDisabled && handleToggleOption(option)}
                    >
                      <span>{option.label}</span>
                      {isSelected && <Check className="h-4 w-4" />}
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
          {maxItems && (
            <div className="p-2 text-xs text-gray-500 border-t">
              {value.length} / {maxItems} 選択済み
            </div>
          )}
        </div>
      )}
    </div>
  );
}
