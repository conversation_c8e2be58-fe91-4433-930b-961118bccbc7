// @vitest-environment node
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { SpyInstance } from 'vitest';
import { getUserWithRole, createFallbackUser, createDevMockUser } from '../../../../lib/auth';
import { ErrorService } from '../../../../lib/services/error-service';
import * as supabaseModule from '../../../../lib/supabase/index';
import {
  createLegacySupabaseAdapter,
  legacyGetUserWithRole,
  updateTestToUseNewArchitecture
} from '../../../../lib/test-utils/legacy-adapters';

// Mock the supabase module
vi.mock('../../../../lib/supabase/index', () => {
  const mockSupabase = {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    maybeSingle: vi.fn()
  };
  
  return {
    browserClient: vi.fn(() => mockSupabase),
    serverClient: vi.fn(() => mockSupabase),
    serviceClient: vi.fn(() => mockSupabase),
    supabase: mockSupabase
  };
});

// Mock the error service
vi.mock('../../../../lib/services/error-service', () => {
  return {
    ErrorService: {
      createAuthError: vi.fn((message: string) => new Error(`Auth Error: ${message}`)),
      createDatabaseError: vi.fn((message: string) => new Error(`DB Error: ${message}`)),
      createAppError: vi.fn((message: string | Error) => new Error(`App Error: ${message}`)),
      logError: vi.fn()
    },
    ErrorSeverity: {
      ERROR: 'error',
      WARNING: 'warning',
      INFO: 'info'
    },
    ErrorCategory: {
      AUTH: 'auth',
      DATABASE: 'database',
      SECURITY: 'security'
    }
  };
});

// Create mock responses for testing
const createMockResponses = () => ({
  staff: {
    data: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      auth_id: '123e4567-e89b-12d3-a456-426614174000',
      name_jp: 'Test User',
      name_en: 'Test User',
      email: '<EMAIL>',
      division: { id: 'div1', name_en: 'Division 1' },
      group: { id: 'group1', name_en: 'Group 1' },
      role: { id: 'role1', name: 'admin' }
    },
    error: null
  },
  profiles: {
    data: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      email: '<EMAIL>',
      first_name: 'Profile',
      last_name: 'User',
      role: { name: 'regular_user' }
    },
    error: null
  }
});

describe('Auth Module', () => {
  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';
  let originalNodeEnv: string | undefined;
  
  beforeEach(() => {
    // Save original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV;
    
    // Reset all mocks
    vi.clearAllMocks();
  });
  
  afterEach(() => {
    // Restore original NODE_ENV
    if (originalNodeEnv) {
      Object.defineProperty(process.env, 'NODE_ENV', { value: originalNodeEnv });
    }
  });
  
  describe('getUserWithRole', () => {
    // Use the updateTestToUseNewArchitecture wrapper for all tests
    it('should return mock user in development environment', updateTestToUseNewArchitecture(async () => {
      // Set development environment
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'development' });
      
      // Create mock adapter with responses
      const mockResponses = createMockResponses();
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(mockAdapter);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toEqual(expect.objectContaining({
        id: mockUserId,
        auth_id: mockUserId,
        email: '<EMAIL>',
        role: { name: 'admin' }
      }));
      expect(result.error).toBeNull();
      expect(ErrorService.logError).toHaveBeenCalled();
    }));
    
    it('should return error when Supabase is not configured', updateTestToUseNewArchitecture(async () => {
      // Mock supabase as null
      const mockSupabase = vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(null as any);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(ErrorService.createAuthError).toHaveBeenCalledWith(
        'Supabase is not configured',
        'AUTH_CONFIG_ERROR',
        expect.objectContaining({ userId: mockUserId })
      );
      
      // Restore the mock
      mockSupabase.mockRestore();
    }));
    
    it('should return staff data when found', updateTestToUseNewArchitecture(async () => {
      // Set production environment
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });
      
      // Create mock adapter with responses
      const mockResponses = createMockResponses();
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(mockAdapter);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toEqual(mockResponses.staff.data);
      expect(result.error).toBeNull();
    }));
    
    it('should try profile as fallback when staff not found', updateTestToUseNewArchitecture(async () => {
      // Set production environment
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });
      
      // Create mock responses with staff not found
      const mockResponses = {
        staff: { data: null, error: null },
        profiles: {
          data: {
            id: mockUserId,
            email: '<EMAIL>',
            first_name: 'Profile',
            last_name: 'User',
            role: { name: 'regular_user' }
          },
          error: null
        }
      };
      
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(mockAdapter);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toEqual(mockResponses.profiles.data);
      expect(result.error).toBeNull();
    }));
    
    it('should handle database errors', updateTestToUseNewArchitecture(async () => {
      // Set production environment
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });
      
      // Create mock responses with database error
      const mockResponses = {
        staff: { data: null, error: { message: 'Database connection error' } }
      };
      
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(mockAdapter);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(ErrorService.createDatabaseError).toHaveBeenCalled();
    }));
    
    it('should handle RLS policy errors', updateTestToUseNewArchitecture(async () => {
      // Set production environment
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });
      
      // Create mock responses with RLS policy error
      const mockResponses = {
        staff: { data: null, error: null },
        profiles: { data: null, error: { message: 'permission denied for table profiles' } }
      };
      
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(mockAdapter);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(ErrorService.logError).toHaveBeenCalled();
    }));
    
    it('should provide fallback user in development when no data found', updateTestToUseNewArchitecture(async () => {
      // Set development environment
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'development' });
      
      // Create mock responses with no data found
      const mockResponses = {
        staff: { data: null, error: null },
        profiles: { data: null, error: null }
      };
      
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      vi.spyOn(supabaseModule, 'serverClient').mockReturnValue(mockAdapter);
      
      const result = await getUserWithRole(mockUserId);
      
      expect(result.data).toBeDefined();
      expect(result.data.id).toEqual(mockUserId);
      expect(result.data.role.name).toEqual('regular_user');
      expect(result.error).toBeNull();
      expect(ErrorService.logError).toHaveBeenCalled();
    }));
  });
  
  describe('Helper Functions', () => {
    it('should create fallback user with correct properties', updateTestToUseNewArchitecture(() => {
      const fallbackUser = createFallbackUser(mockUserId, 'custom_role');
      
      expect(fallbackUser).toEqual(expect.objectContaining({
        id: mockUserId,
        auth_id: mockUserId,
        role: { name: 'custom_role' }
      }));
    }));
    
    it('should create dev mock user with correct properties', updateTestToUseNewArchitecture(() => {
      const mockUser = createDevMockUser(mockUserId);
      
      expect(mockUser).toEqual(expect.objectContaining({
        id: mockUserId,
        auth_id: mockUserId,
        email: '<EMAIL>',
        role: { name: 'admin' }
      }));
    }));
    
    it('should use legacy adapter correctly', async () => {
      // Test the legacy adapter directly
      const mockResponses = createMockResponses();
      const mockAdapter = createLegacySupabaseAdapter(mockResponses);
      
      // Test a simple query
      const result = await mockAdapter.from('staff').select().eq('auth_id', mockUserId).maybeSingle();
      
      expect(result.data).toEqual(mockResponses.staff.data);
      expect(result.error).toBeNull();
    });
    
    it('should use legacyGetUserWithRole correctly', async () => {
      // Test the legacyGetUserWithRole adapter
      const mockUser = {
        id: mockUserId,
        email: '<EMAIL>',
        role: { name: 'legacy_role' }
      };
      
      const result = await legacyGetUserWithRole(mockUserId, mockUser);
      
      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });
  });
});