'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RefreshCw, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { format } from 'date-fns'
import { ja } from 'date-fns/locale'

interface UpdateQueueItem {
  id: string
  article_id: string
  suggested_updates: any[]
  confidence_score: number
  status: string
  created_at: string
}

interface UpdateLog {
  id: string
  article_id: string
  update_type: string
  changes_made: string[]
  confidence_score: number
  approved: boolean
  created_at: string
}

interface ContentGap {
  id: string
  topic: string
  query_count: number
  suggested_category: string
  priority: string
  status: string
  created_at: string
}

export function KnowledgeBaseAutoUpdate() {
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [updateQueue, setUpdateQueue] = useState<UpdateQueueItem[]>([])
  const [recentLogs, setRecentLogs] = useState<UpdateLog[]>([])
  const [contentGaps, setContentGaps] = useState<ContentGap[]>([])
  const { toast } = useToast()

  useEffect(() => {
    fetchUpdateStatus()
  }, [])

  const fetchUpdateStatus = async () => {
    try {
      setRefreshing(true)
      const response = await fetch('/api/knowledge-base/update-content')
      
      if (!response.ok) {
        throw new Error('Failed to fetch update status')
      }
      
      const data = await response.json()
      setUpdateQueue(data.updateQueue)
      setRecentLogs(data.recentLogs)
      setContentGaps(data.contentGaps)
    } catch (error) {
      console.error('Error fetching update status:', error)
      toast({
        title: 'エラー',
        description: '更新ステータスの取得に失敗しました',
        variant: 'destructive'
      })
    } finally {
      setRefreshing(false)
    }
  }

  const triggerPeriodicReview = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/knowledge-base/update-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })
      
      if (!response.ok) {
        throw new Error('Failed to trigger periodic review')
      }
      
      toast({
        title: '成功',
        description: '定期レビューを開始しました'
      })
      
      // Refresh status after a delay
      setTimeout(() => fetchUpdateStatus(), 3000)
    } catch (error) {
      console.error('Error triggering review:', error)
      toast({
        title: 'エラー',
        description: '定期レビューの開始に失敗しました',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive'
      case 'medium': return 'default' 
      case 'low': return 'secondary'
      default: return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected': return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">コンテンツ自動更新管理</h2>
          <p className="text-muted-foreground">
            AI駆動のコンテンツ更新システムの管理
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchUpdateStatus}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            更新
          </Button>
          <Button
            onClick={triggerPeriodicReview}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                処理中...
              </>
            ) : (
              '定期レビューを開始'
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="queue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="queue">
            更新キュー ({updateQueue.length})
          </TabsTrigger>
          <TabsTrigger value="logs">
            最近の更新
          </TabsTrigger>
          <TabsTrigger value="gaps">
            コンテンツギャップ ({contentGaps.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="queue" className="space-y-4">
          {updateQueue.length === 0 ? (
            <Alert>
              <AlertDescription>
                現在、レビュー待ちの更新はありません
              </AlertDescription>
            </Alert>
          ) : (
            updateQueue.map((item) => (
              <Card key={item.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        記事ID: {item.article_id}
                      </CardTitle>
                      <CardDescription>
                        {format(new Date(item.created_at), 'yyyy年MM月dd日 HH:mm', { locale: ja })}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        信頼度: {(item.confidence_score * 100).toFixed(0)}%
                      </Badge>
                      {getStatusIcon(item.status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">
                      提案された更新: {item.suggested_updates.length}件
                    </p>
                    <div className="space-y-1">
                      {item.suggested_updates.slice(0, 3).map((update, idx) => (
                        <p key={idx} className="text-sm text-muted-foreground">
                          • {update.description}
                        </p>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          {recentLogs.length === 0 ? (
            <Alert>
              <AlertDescription>
                最近の更新ログはありません
              </AlertDescription>
            </Alert>
          ) : (
            recentLogs.map((log) => (
              <Card key={log.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        記事ID: {log.article_id}
                      </CardTitle>
                      <CardDescription>
                        {format(new Date(log.created_at), 'yyyy年MM月dd日 HH:mm', { locale: ja })}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={log.approved ? 'default' : 'secondary'}>
                        {log.update_type}
                      </Badge>
                      {log.approved ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Clock className="h-4 w-4 text-yellow-500" />
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm">
                      変更内容: {log.changes_made.length}件
                    </p>
                    <div className="space-y-1">
                      {log.changes_made.map((change, idx) => (
                        <p key={idx} className="text-sm text-muted-foreground">
                          • {change}
                        </p>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="gaps" className="space-y-4">
          {contentGaps.length === 0 ? (
            <Alert>
              <AlertDescription>
                識別されたコンテンツギャップはありません
              </AlertDescription>
            </Alert>
          ) : (
            contentGaps.map((gap) => (
              <Card key={gap.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        {gap.topic}
                      </CardTitle>
                      <CardDescription>
                        カテゴリー: {gap.suggested_category}
                      </CardDescription>
                    </div>
                    <Badge variant={getPriorityColor(gap.priority)}>
                      {gap.priority}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">
                    検索クエリ数: {gap.query_count}回
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(gap.created_at), 'yyyy年MM月dd日', { locale: ja })}に識別
                  </p>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
