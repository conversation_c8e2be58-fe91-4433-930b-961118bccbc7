export const translations = {
  ja: {
    common: {
      welcome: 'ようこそ',
      logout: 'ログアウト',
      login: 'ログイン',
      save: '保存',
      cancel: 'キャンセル',
      delete: '削除',
      edit: '編集',
      create: '作成',
      search: '検索',
      loading: '読み込み中...',
      error: 'エラー',
      success: '成功',
      confirm: '確認',
      back: '戻る',
      next: '次へ',
      submit: '送信',
      required: '必須',
      optional: '任意',
      selectAll: 'すべて選択',
      clearAll: 'すべてクリア',
      noData: 'データがありません',
      actions: 'アクション'
    },
    auth: {
      signIn: 'サインイン',
      signOut: 'サインアウト',
      email: 'メールアドレス',
      password: 'パスワード',
      forgotPassword: 'パスワードをお忘れですか？',
      rememberMe: 'ログイン状態を保持',
      signInError: 'ログインに失敗しました',
      unauthorized: '権限がありません'
    },
    dashboard: {
      title: 'ダッシュボード',
      overview: '概要',
      recentRequests: '最近のリクエスト',
      pendingApprovals: '承認待ち',
      statistics: '統計',
      welcome: 'おかえりなさい、{name}さん'
    },
    navigation: {
      home: 'ホーム',
      dashboard: 'ダッシュボード',
      requests: 'リクエスト',
      itHelpdesk: 'ITヘルプデスク',
      groupMail: 'グループメール',
      mailbox: 'メールボックス',
      sharepoint: 'SharePoint',
      pcAdmin: 'PC管理者権限',
      passwordReset: 'パスワードリセット',
      hr: '人事',
      onboarding: 'オンボーディング',
      offboarding: 'オフボーディング',
      knowledgeBase: 'ナレッジベース',
      settings: '設定',
      profile: 'プロフィール',
      help: 'ヘルプ'
    },
    roles: {
      globalAdmin: 'グローバル管理者',
      systemAdmin: 'システム管理者',
      departmentAdmin: '部門管理者',
      itSupport: 'ITサポート',
      hrStaff: '人事スタッフ',
      regularUser: '一般ユーザー'
    },
    departments: {
      corporatePlanning: '経営企画部',
      humanResources: '人事部',
      financeAccounting: '財務・経理部',
      legalCompliance: '法務・コンプライアンス部',
      fleetOperations: 'フリートオペレーション部',
      technicalManagement: '工務部',
      itSystems: 'ITシステム部',
      commercialStrategies: '営業戦略部',
      guestExperience: 'ゲストエクスピリエンス部'
    },
    forms: {
      selectUser: 'ユーザーを選択',
      selectDepartment: '部門を選択',
      selectService: 'サービスを選択',
      reason: '理由',
      description: '説明',
      priority: '優先度',
      high: '高',
      medium: '中',
      low: '低',
      attachments: '添付ファイル',
      additionalNotes: '追加メモ',
      confirmationTitle: '確認',
      confirmationMessage: '以下の内容でリクエストを送信しますか？',
      affectedUsers: '対象ユーザー',
      requestDetails: 'リクエスト詳細',
      submittedSuccessfully: 'リクエストが正常に送信されました'
    },
    services: {
      groupMail: {
        title: 'グループメール管理',
        description: 'グループメールアドレスへのメンバー追加・削除',
        add: 'メンバー追加',
        remove: 'メンバー削除',
        addresses: 'グループメールアドレス'
      },
      mailbox: {
        title: 'メールボックス管理',
        description: 'メールボックスアドレスの管理',
        add: 'アドレス追加',
        remove: 'アドレス削除',
        addresses: 'メールボックスアドレス'
      },
      sharepoint: {
        title: 'SharePointライブラリ',
        description: 'SharePointライブラリへのアクセス管理',
        grant: 'アクセス許可',
        revoke: 'アクセス取消',
        libraries: 'ライブラリ',
        accessLevel: 'アクセスレベル',
        read: '読み取り',
        contribute: '投稿',
        full: 'フルコントロール'
      },
      pcAdmin: {
        title: 'PC管理者権限',
        description: 'PC管理者権限の付与・取消',
        grant: '権限付与',
        revoke: '権限取消',
        pcId: 'PC ID',
        softwareInstall: 'ソフトウェアインストール',
        softwareName: 'ソフトウェア名'
      },
      passwordReset: {
        title: 'パスワードリセット',
        description: 'パスワードのリセット要求',
        types: {
          m365: 'M365 Office',
          mfa: '多要素認証',
          windows: 'Windows'
        },
        temporaryPassword: '仮パスワード',
        resetInstructions: 'ユーザーは初回ログイン時にパスワードを変更する必要があります'
      }
    },
    status: {
      pending: '保留中',
      processing: '処理中',
      completed: '完了',
      failed: '失敗',
      approved: '承認済み',
      rejected: '却下',
      draft: '下書き',
      submitted: '送信済み'
    },
    date: {
      today: '今日',
      yesterday: '昨日',
      thisWeek: '今週',
      lastWeek: '先週',
      thisMonth: '今月',
      lastMonth: '先月',
      dateFormat: 'YYYY年MM月DD日',
      timeFormat: 'HH:mm',
      dateTimeFormat: 'YYYY年MM月DD日 HH:mm',
      wareki: '和暦'
    },
    errors: {
      generic: 'エラーが発生しました',
      notFound: 'ページが見つかりません',
      unauthorized: 'アクセス権限がありません',
      validation: {
        required: 'このフィールドは必須です',
        email: '有効なメールアドレスを入力してください',
        minLength: '最低{min}文字必要です',
        maxLength: '最大{max}文字まで入力できます'
      }
    },
    notifications: {
      newRequest: '新しいリクエストがあります',
      requestApproved: 'リクエストが承認されました',
      requestRejected: 'リクエストが却下されました',
      requestCompleted: 'リクエストが完了しました',
      passwordReset: 'パスワードがリセットされました'
    }
  },
  en: {
    common: {
      welcome: 'Welcome',
      logout: 'Logout',
      login: 'Login',
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      create: 'Create',
      search: 'Search',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      confirm: 'Confirm',
      back: 'Back',
      next: 'Next',
      submit: 'Submit',
      required: 'Required',
      optional: 'Optional',
      selectAll: 'Select All',
      clearAll: 'Clear All',
      noData: 'No data available',
      actions: 'Actions'
    },
    auth: {
      signIn: 'Sign In',
      signOut: 'Sign Out',
      email: 'Email',
      password: 'Password',
      forgotPassword: 'Forgot Password?',
      rememberMe: 'Remember Me',
      signInError: 'Failed to sign in',
      unauthorized: 'Unauthorized'
    },
    dashboard: {
      title: 'Dashboard',
      overview: 'Overview',
      recentRequests: 'Recent Requests',
      pendingApprovals: 'Pending Approvals',
      statistics: 'Statistics',
      welcome: 'Welcome back, {name}'
    },
    navigation: {
      home: 'Home',
      dashboard: 'Dashboard',
      requests: 'Requests',
      itHelpdesk: 'IT Helpdesk',
      groupMail: 'Group Mail',
      mailbox: 'Mailbox',
      sharepoint: 'SharePoint',
      pcAdmin: 'PC Admin Rights',
      passwordReset: 'Password Reset',
      hr: 'Human Resources',
      onboarding: 'Onboarding',
      offboarding: 'Offboarding',
      knowledgeBase: 'Knowledge Base',
      settings: 'Settings',
      profile: 'Profile',
      help: 'Help'
    },
    roles: {
      globalAdmin: 'Global Administrator',
      systemAdmin: 'System Administrator',
      departmentAdmin: 'Department Administrator',
      itSupport: 'IT Support',
      hrStaff: 'HR Staff',
      regularUser: 'Regular User'
    },
    departments: {
      corporatePlanning: 'Corporate Planning Division',
      humanResources: 'Human Resources Division',
      financeAccounting: 'Finance and Accounting Division',
      legalCompliance: 'Legal, Ethics and Compliance Division',
      fleetOperations: 'Fleet Operations Division',
      technicalManagement: 'Technical Management Division',
      itSystems: 'IT Systems Division',
      commercialStrategies: 'Commercial Strategies Division',
      guestExperience: 'Guest Experience Division'
    },
    forms: {
      selectUser: 'Select User',
      selectDepartment: 'Select Department',
      selectService: 'Select Service',
      reason: 'Reason',
      description: 'Description',
      priority: 'Priority',
      high: 'High',
      medium: 'Medium',
      low: 'Low',
      attachments: 'Attachments',
      additionalNotes: 'Additional Notes',
      confirmationTitle: 'Confirmation',
      confirmationMessage: 'Do you want to submit the request with the following details?',
      affectedUsers: 'Affected Users',
      requestDetails: 'Request Details',
      submittedSuccessfully: 'Request submitted successfully'
    },
    services: {
      groupMail: {
        title: 'Group Mail Management',
        description: 'Add or remove members from group mail addresses',
        add: 'Add Member',
        remove: 'Remove Member',
        addresses: 'Group Mail Addresses'
      },
      mailbox: {
        title: 'Mailbox Management',
        description: 'Manage mailbox addresses',
        add: 'Add Address',
        remove: 'Remove Address',
        addresses: 'Mailbox Addresses'
      },
      sharepoint: {
        title: 'SharePoint Library',
        description: 'Manage access to SharePoint libraries',
        grant: 'Grant Access',
        revoke: 'Revoke Access',
        libraries: 'Libraries',
        accessLevel: 'Access Level',
        read: 'Read',
        contribute: 'Contribute',
        full: 'Full Control'
      },
      pcAdmin: {
        title: 'PC Administrative Rights',
        description: 'Grant or revoke PC administrative privileges',
        grant: 'Grant Rights',
        revoke: 'Revoke Rights',
        pcId: 'PC ID',
        softwareInstall: 'Software Installation',
        softwareName: 'Software Name'
      },
      passwordReset: {
        title: 'Password Reset',
        description: 'Request password reset',
        types: {
          m365: 'M365 Office',
          mfa: 'Multi Factor Authenticator',
          windows: 'Windows'
        },
        temporaryPassword: 'Temporary Password',
        resetInstructions: 'User must change password on first login'
      }
    },
    status: {
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed',
      failed: 'Failed',
      approved: 'Approved',
      rejected: 'Rejected',
      draft: 'Draft',
      submitted: 'Submitted'
    },
    date: {
      today: 'Today',
      yesterday: 'Yesterday',
      thisWeek: 'This Week',
      lastWeek: 'Last Week',
      thisMonth: 'This Month',
      lastMonth: 'Last Month',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      dateTimeFormat: 'MM/DD/YYYY HH:mm',
      wareki: 'Japanese Era'
    },
    errors: {
      generic: 'An error occurred',
      notFound: 'Page not found',
      unauthorized: 'You do not have permission to access this',
      validation: {
        required: 'This field is required',
        email: 'Please enter a valid email address',
        minLength: 'Must be at least {min} characters',
        maxLength: 'Must be at most {max} characters'
      }
    },
    notifications: {
      newRequest: 'New request received',
      requestApproved: 'Request has been approved',
      requestRejected: 'Request has been rejected',
      requestCompleted: 'Request has been completed',
      passwordReset: 'Password has been reset'
    }
  }
} as const

export type TranslationKeys = typeof translations.ja
