"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/docs";
exports.ids = ["pages/api/docs"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdocs.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdocs.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_docs_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\docs.ts */ \"(api)/./pages/api/docs.ts\");\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\nconst PagesAPIRouteModule = next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule;\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_docs_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_docs_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/docs\",\n        pathname: \"/api/docs\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_docs_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdocs.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/api/openapi-documentation.ts":
/*!******************************************!*\
  !*** ./lib/api/openapi-documentation.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiController: () => (/* binding */ ApiController),\n/* harmony export */   ApiRoute: () => (/* binding */ ApiRoute),\n/* harmony export */   ApiSchema: () => (/* binding */ ApiSchema),\n/* harmony export */   ApiSecurityScheme: () => (/* binding */ ApiSecurityScheme),\n/* harmony export */   ApiTag: () => (/* binding */ ApiTag),\n/* harmony export */   OPENAPI_VERSION: () => (/* binding */ OPENAPI_VERSION),\n/* harmony export */   OpenAPIDocumentGenerator: () => (/* binding */ OpenAPIDocumentGenerator),\n/* harmony export */   openApiMiddleware: () => (/* binding */ openApiMiddleware)\n/* harmony export */ });\n/**\r\n * OpenAPI Documentation Service\r\n * \r\n * This module provides tools for generating OpenAPI 3.0 documentation\r\n * with custom route decorators for automatic schema generation.\r\n */ /**\r\n * OpenAPI specification version\r\n */ const OPENAPI_VERSION = \"3.0.3\";\n/**\r\n * API documentation registry\r\n */ class APIDocumentationRegistry {\n    constructor(){\n        this.routes = [];\n        this.schemas = {};\n        this.securitySchemes = {};\n        this.tags = [];\n    }\n    /**\r\n   * Get singleton instance\r\n   */ static getInstance() {\n        if (!APIDocumentationRegistry.instance) {\n            APIDocumentationRegistry.instance = new APIDocumentationRegistry();\n        }\n        return APIDocumentationRegistry.instance;\n    }\n    /**\r\n   * Register a route\r\n   */ registerRoute(metadata) {\n        // Check if route already exists\n        const existingRouteIndex = this.routes.findIndex((route)=>route.path === metadata.path && route.method === metadata.method);\n        if (existingRouteIndex >= 0) {\n            // Update existing route\n            this.routes[existingRouteIndex] = {\n                ...this.routes[existingRouteIndex],\n                ...metadata\n            };\n        } else {\n            // Add new route\n            this.routes.push(metadata);\n        }\n    }\n    /**\r\n   * Register a schema\r\n   */ registerSchema(name, schema) {\n        this.schemas[name] = schema;\n    }\n    /**\r\n   * Register a security scheme\r\n   */ registerSecurityScheme(name, scheme) {\n        this.securitySchemes[name] = scheme;\n    }\n    /**\r\n   * Register a tag\r\n   */ registerTag(name, description) {\n        // Check if tag already exists\n        const existingTagIndex = this.tags.findIndex((tag)=>tag.name === name);\n        if (existingTagIndex >= 0) {\n            // Update existing tag\n            this.tags[existingTagIndex] = {\n                name,\n                description: description || this.tags[existingTagIndex].description\n            };\n        } else {\n            // Add new tag\n            this.tags.push({\n                name,\n                description\n            });\n        }\n    }\n    /**\r\n   * Get all routes\r\n   */ getRoutes() {\n        return [\n            ...this.routes\n        ];\n    }\n    /**\r\n   * Get all schemas\r\n   */ getSchemas() {\n        return {\n            ...this.schemas\n        };\n    }\n    /**\r\n   * Get all security schemes\r\n   */ getSecuritySchemes() {\n        return {\n            ...this.securitySchemes\n        };\n    }\n    /**\r\n   * Get all tags\r\n   */ getTags() {\n        return [\n            ...this.tags\n        ];\n    }\n    /**\r\n   * Clear all routes\r\n   */ clearRoutes() {\n        this.routes = [];\n    }\n    /**\r\n   * Clear all schemas\r\n   */ clearSchemas() {\n        this.schemas = {};\n    }\n    /**\r\n   * Clear all security schemes\r\n   */ clearSecuritySchemes() {\n        this.securitySchemes = {};\n    }\n    /**\r\n   * Clear all tags\r\n   */ clearTags() {\n        this.tags = [];\n    }\n    /**\r\n   * Clear all data\r\n   */ clear() {\n        this.clearRoutes();\n        this.clearSchemas();\n        this.clearSecuritySchemes();\n        this.clearTags();\n    }\n}\n/**\r\n * OpenAPI document generator\r\n */ class OpenAPIDocumentGenerator {\n    constructor(config){\n        this.registry = APIDocumentationRegistry.getInstance();\n        this.config = config;\n    }\n    /**\r\n   * Generate OpenAPI document\r\n   */ generate() {\n        const routes = this.registry.getRoutes();\n        const schemas = this.registry.getSchemas();\n        const securitySchemes = this.registry.getSecuritySchemes();\n        const tags = this.registry.getTags();\n        // Build paths object\n        const paths = {};\n        routes.forEach((route)=>{\n            const { path, method, ...operation } = route;\n            // Normalize path\n            const normalizedPath = this.normalizePath(path);\n            // Initialize path item if it doesn't exist\n            if (!paths[normalizedPath]) {\n                paths[normalizedPath] = {};\n            }\n            // Add operation to path item\n            paths[normalizedPath][method] = {\n                ...operation,\n                responses: operation.responses || {\n                    \"200\": {\n                        description: \"Successful operation\"\n                    }\n                }\n            };\n        });\n        // Build OpenAPI document\n        const document = {\n            openapi: OPENAPI_VERSION,\n            info: {\n                title: this.config.title,\n                description: this.config.description,\n                version: this.config.version,\n                termsOfService: this.config.termsOfService,\n                contact: this.config.contact,\n                license: this.config.license\n            },\n            servers: this.config.servers,\n            paths,\n            components: {\n                schemas,\n                securitySchemes\n            },\n            tags,\n            externalDocs: this.config.externalDocs,\n            security: this.config.security\n        };\n        return document;\n    }\n    /**\r\n   * Normalize path\r\n   */ normalizePath(path) {\n        // Remove trailing slash\n        let normalizedPath = path.endsWith(\"/\") ? path.slice(0, -1) : path;\n        // Add leading slash\n        normalizedPath = normalizedPath.startsWith(\"/\") ? normalizedPath : `/${normalizedPath}`;\n        // Replace Next.js dynamic route syntax with OpenAPI path parameter syntax\n        normalizedPath = normalizedPath.replace(/\\[([^\\]]+)\\]/g, \"{$1}\");\n        // Add base path if provided\n        if (this.config.basePath) {\n            const basePathWithoutTrailingSlash = this.config.basePath.endsWith(\"/\") ? this.config.basePath.slice(0, -1) : this.config.basePath;\n            normalizedPath = `${basePathWithoutTrailingSlash}${normalizedPath}`;\n        }\n        return normalizedPath;\n    }\n}\n/**\r\n * API route decorator\r\n */ function ApiRoute(metadata) {\n    return function(target, propertyKey, descriptor) {\n        const originalMethod = descriptor.value;\n        const registry = APIDocumentationRegistry.getInstance();\n        // Extract path and method from target\n        const path = target.constructor.path || \"/\";\n        const method = propertyKey.toLowerCase();\n        // Register route\n        registry.registerRoute({\n            path,\n            method,\n            ...metadata\n        });\n        // Return original method\n        return descriptor;\n    };\n}\n/**\r\n * API controller decorator\r\n */ function ApiController(path) {\n    return function(target) {\n        // Set path on controller\n        target.path = path;\n        return target;\n    };\n}\n/**\r\n * API tag decorator\r\n */ function ApiTag(name, description) {\n    return function(target) {\n        const registry = APIDocumentationRegistry.getInstance();\n        registry.registerTag(name, description);\n        return target;\n    };\n}\n/**\r\n * API schema decorator\r\n */ function ApiSchema(name, schema) {\n    return function(target) {\n        const registry = APIDocumentationRegistry.getInstance();\n        registry.registerSchema(name, schema);\n        return target;\n    };\n}\n/**\r\n * API security scheme decorator\r\n */ function ApiSecurityScheme(name, scheme) {\n    return function(target) {\n        const registry = APIDocumentationRegistry.getInstance();\n        registry.registerSecurityScheme(name, scheme);\n        return target;\n    };\n}\n/**\r\n * OpenAPI documentation middleware\r\n */ function openApiMiddleware(config) {\n    return function(req, res) {\n        const generator = new OpenAPIDocumentGenerator(config);\n        const document = generator.generate();\n        res.status(200).json(document);\n    };\n} /**\r\n * Example usage:\r\n * \r\n * ```typescript\r\n * // Define API schemas\r\n * @ApiSchema('User', {\r\n *   type: 'object',\r\n *   properties: {\r\n *     id: { type: 'string', format: 'uuid' },\r\n *     name: { type: 'string' },\r\n *     email: { type: 'string', format: 'email' }\r\n *   },\r\n *   required: ['id', 'name', 'email']\r\n * })\r\n * \r\n * // Define API security schemes\r\n * @ApiSecurityScheme('bearerAuth', {\r\n *   type: 'http',\r\n *   scheme: 'bearer',\r\n *   bearerFormat: 'JWT'\r\n * })\r\n * \r\n * // Define API tags\r\n * @ApiTag('Users', 'User management endpoints')\r\n * \r\n * // Define API controller\r\n * @ApiController('/api/users')\r\n * class UsersController {\r\n *   // Define API routes\r\n *   @ApiRoute({\r\n *     summary: 'Get all users',\r\n *     description: 'Returns a list of all users',\r\n *     tags: ['Users'],\r\n *     operationId: 'getUsers',\r\n *     responses: {\r\n *       '200': {\r\n *         description: 'List of users',\r\n *         content: {\r\n *           'application/json': {\r\n *             schema: {\r\n *               type: 'array',\r\n *               items: {\r\n *                 $ref: '#/components/schemas/User'\r\n *               }\r\n *             }\r\n *           }\r\n *         }\r\n *       }\r\n *     },\r\n *     security: [{ bearerAuth: [] }]\r\n *   })\r\n *   async get(req: NextApiRequest, res: NextApiResponse) {\r\n *     // Implementation\r\n *   }\r\n * \r\n *   @ApiRoute({\r\n *     summary: 'Create a new user',\r\n *     description: 'Creates a new user',\r\n *     tags: ['Users'],\r\n *     operationId: 'createUser',\r\n *     requestBody: {\r\n *       description: 'User data',\r\n *       required: true,\r\n *       content: {\r\n *         'application/json': {\r\n *           schema: {\r\n *             type: 'object',\r\n *             properties: {\r\n *               name: { type: 'string' },\r\n *               email: { type: 'string', format: 'email' }\r\n *             },\r\n *             required: ['name', 'email']\r\n *           }\r\n *         }\r\n *       }\r\n *     },\r\n *     responses: {\r\n *       '201': {\r\n *         description: 'User created',\r\n *         content: {\r\n *           'application/json': {\r\n *             schema: {\r\n *               $ref: '#/components/schemas/User'\r\n *             }\r\n *           }\r\n *         }\r\n *       }\r\n *     },\r\n *     security: [{ bearerAuth: [] }]\r\n *   })\r\n *   async post(req: NextApiRequest, res: NextApiResponse) {\r\n *     // Implementation\r\n *   }\r\n * }\r\n * \r\n * // Create OpenAPI documentation endpoint\r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * ```\r\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/api/openapi-documentation.ts\n");

/***/ }),

/***/ "(api)/./pages/api/docs.ts":
/*!***************************!*\
  !*** ./pages/api/docs.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_api_openapi_documentation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/api/openapi-documentation */ \"(api)/./lib/api/openapi-documentation.ts\");\n\n/**\r\n * API Documentation endpoint\r\n * \r\n * This endpoint serves the OpenAPI documentation for the API.\r\n */ function handler(req, res) {\n    return (0,_lib_api_openapi_documentation__WEBPACK_IMPORTED_MODULE_0__.openApiMiddleware)({\n        title: \"Enterprise IT Helpdesk API\",\n        description: \"API documentation for the Enterprise IT Helpdesk application\",\n        version: \"1.0.0\",\n        servers: [\n            {\n                url: \"https://api.example.com\",\n                description: \"Production server\"\n            },\n            {\n                url: \"http://localhost:3000\",\n                description: \"Development server\"\n            }\n        ],\n        basePath: \"/api\",\n        termsOfService: \"https://example.com/terms\",\n        contact: {\n            name: \"API Support\",\n            url: \"https://example.com/support\",\n            email: \"<EMAIL>\"\n        },\n        license: {\n            name: \"MIT\",\n            url: \"https://opensource.org/licenses/MIT\"\n        },\n        externalDocs: {\n            description: \"Find more info here\",\n            url: \"https://example.com/docs\"\n        },\n        security: [\n            {\n                bearerAuth: []\n            }\n        ]\n    })(req, res);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./pages/api/docs.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdocs.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();