import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { params, format, templateId } = await req.json()
    
    // Build query based on params
    let query = supabaseClient
      .from('audit_logs_user_view')
      .select('*')
      .order('created_at', { ascending: false })

    if (params.event_types?.length) {
      query = query.in('event_type', params.event_types)
    }
    if (params.severity?.length) {
      query = query.in('severity', params.severity)
    }
    if (params.start_date) {
      query = query.gte('created_at', params.start_date)
    }
    if (params.end_date) {
      query = query.lte('created_at', params.end_date)
    }
    
    const { data: logs, error } = await query
    
    if (error) throw error

    let content: string
    let contentType: string
    let fileName: string

    const timestamp = new Date().toISOString().split('T')[0]

    switch (format) {
      case 'CSV':
        content = convertToCSV(logs)
        contentType = 'text/csv'
        fileName = `audit-logs-${timestamp}.csv`
        break
      
      case 'JSON':
        content = JSON.stringify(logs, null, 2)
        contentType = 'application/json'
        fileName = `audit-logs-${timestamp}.json`
        break
      
      case 'PDF':
        content = await generatePDF(logs, templateId)
        contentType = 'application/pdf'
        fileName = `audit-logs-${timestamp}.pdf`
        break
      
      case 'XLSX':
        content = await generateExcel(logs)
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        fileName = `audit-logs-${timestamp}.xlsx`
        break
      
      default:
        throw new Error('Unsupported format')
    }

    // Store the file in Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseClient
      .storage
      .from('audit-exports')
      .upload(fileName, content, {
        contentType,
        cacheControl: '3600'
      })

    if (uploadError) throw uploadError

    // Get public URL
    const { data: { publicUrl } } = supabaseClient
      .storage
      .from('audit-exports')
      .getPublicUrl(fileName)

    return new Response(
      JSON.stringify({ 
        url: publicUrl,
        count: logs.length 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

function convertToCSV(logs: any[]): string {
  if (logs.length === 0) return ''
  
  const headers = Object.keys(logs[0]).join(',')
  const rows = logs.map(log => 
    Object.values(log).map(value => 
      typeof value === 'string' && value.includes(',') 
        ? `"${value}"` 
        : value
    ).join(',')
  )
  
  return [headers, ...rows].join('\n')
}

async function generatePDF(logs: any[], templateId?: string): Promise<string> {
  // This would integrate with a PDF generation service
  // For now, return a placeholder
  return 'PDF generation not implemented'
}

async function generateExcel(logs: any[]): Promise<string> {
  // This would integrate with an Excel generation library
  // For now, return a placeholder
  return 'Excel generation not implemented'
}