(()=>{var e={};e.id=1952,e.ids=[1952],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},31544:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>u,tree:()=>c});var t=r(70260),a=r(28203),i=r(25155),n=r.n(i),l=r(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let c=["",{children:["test-embeddings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90606)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-embeddings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-embeddings\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-embeddings/page",pathname:"/test-embeddings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},72770:(e,s,r)=>{Promise.resolve().then(r.bind(r,90606))},90922:(e,s,r)=>{Promise.resolve().then(r.bind(r,69877))},69877:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(45512),a=r(58009),i=r(97643),n=r(87021),l=r(25409),d=r(48859),c=r(47699),o=r(69193),h=r(4890),u=r(77252),m=r(666),p=r(12784),x=r(65518),g=r(37133),j=r(88976),v=r(39497),b=r(86235),f=r(92557),y=r(16873);function w(){let[e,s]=(0,a.useState)(""),[r,w]=(0,a.useState)("en"),[C,N]=(0,a.useState)(""),{toast:E}=(0,x.d)(),{loading:S,results:F,generateEmbedding:P,semanticSearch:k,updateStaleEmbeddings:_,findRelatedArticles:A}=function(e={}){let[s,r]=(0,a.useState)(!1),[t,i]=(0,a.useState)([]),{toast:n}=(0,x.d)(),l=(0,a.useCallback)(async(e,s)=>{r(!0);try{let{data:r,error:t}=await g.N.functions.invoke("generate-article-embeddings",{body:{articleId:e,language:s}});if(t)throw t;return n({title:"成功",description:"Embeddings generated successfully"}),r}catch(e){throw console.error("Error generating embeddings:",e),n({title:"エラー",description:e instanceof Error?e.message:"Failed to generate embeddings",variant:"destructive"}),e}finally{r(!1)}},[n]);return{loading:s,results:t,generateEmbedding:l,semanticSearch:(0,a.useCallback)(async(s,t)=>{r(!0);try{let r=await fetch("/api/embeddings/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:s,language:t,matchThreshold:e.matchThreshold,matchCount:e.matchCount})});if(!r.ok)throw Error("Search failed");let a=await r.json();return i(a.results||[]),await g.N.from("kb_search_history").insert({query:s,language:t,results_count:a.results?.length||0,search_type:"semantic"}),a.results}catch(e){throw console.error("Semantic search error:",e),n({title:"エラー",description:e instanceof Error?e.message:"Search failed",variant:"destructive"}),e}finally{r(!1)}},[e.matchThreshold,e.matchCount,n]),updateStaleEmbeddings:(0,a.useCallback)(async()=>{r(!0);try{let e=await fetch("/api/embeddings/update-stale",{method:"POST"});if(!e.ok)throw Error("Update failed");let s=await e.json();return n({title:"成功",description:`Updated ${s.updated} embeddings`}),s}catch(e){throw console.error("Error updating embeddings:",e),n({title:"エラー",description:e instanceof Error?e.message:"Failed to update embeddings",variant:"destructive"}),e}finally{r(!1)}},[n]),findRelatedArticles:(0,a.useCallback)(async(e,s,t=5)=>{r(!0);try{let r=await fetch("/api/embeddings/related",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({articleId:e,language:s,limit:t})});if(!r.ok)throw Error("Failed to find related articles");return(await r.json()).related||[]}catch(e){throw console.error("Error finding related articles:",e),n({title:"エラー",description:e instanceof Error?e.message:"Failed to find related articles",variant:"destructive"}),e}finally{r(!1)}},[n])}}({matchThreshold:.7,matchCount:10}),q=async()=>{if(!C){E({title:"エラー",description:"Please enter an article ID",variant:"destructive"});return}try{await P(C,"both")}catch(e){}},T=async()=>{if(!e){E({title:"エラー",description:"Please enter a search query",variant:"destructive"});return}try{await k(e,r)}catch(e){}},U=async()=>{try{await _()}catch(e){}},R=async()=>{if(!C){E({title:"エラー",description:"Please enter an article ID",variant:"destructive"});return}try{let e=await A(C,r);E({title:"成功",description:`Found ${e.length} related articles`})}catch(e){}};return(0,t.jsxs)("div",{className:"container mx-auto p-6 max-w-6xl",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Vector Embeddings Test Page"}),(0,t.jsxs)(o.tU,{defaultValue:"generate",className:"space-y-4",children:[(0,t.jsxs)(o.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsx)(o.Xi,{value:"generate",children:"Generate Embeddings"}),(0,t.jsx)(o.Xi,{value:"search",children:"Semantic Search"}),(0,t.jsx)(o.Xi,{value:"update",children:"Update Stale"}),(0,t.jsx)(o.Xi,{value:"related",children:"Find Related"})]}),(0,t.jsx)(o.av,{value:"generate",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Generate Article Embeddings"}),(0,t.jsx)(i.BT,{children:"Generate vector embeddings for an article in both English and Japanese"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"article-id",children:"Article ID"}),(0,t.jsx)(l.p,{id:"article-id",placeholder:"Enter article UUID",value:C,onChange:e=>N(e.target.value)})]}),(0,t.jsx)(n.$,{onClick:q,disabled:S||!C,className:"w-full",children:S?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Generate Embeddings"]})})]})]})}),(0,t.jsx)(o.av,{value:"search",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Semantic Search"}),(0,t.jsx)(i.BT,{children:"Search for articles using natural language queries"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"search-query",children:"Search Query"}),(0,t.jsx)(d.T,{id:"search-query",placeholder:"Enter your search query...",value:e,onChange:e=>s(e.target.value),rows:3})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"search-language",children:"Language"}),(0,t.jsxs)(h.l6,{value:r,onValueChange:e=>w(e),children:[(0,t.jsx)(h.bq,{id:"search-language",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"en",children:"English"}),(0,t.jsx)(h.eb,{value:"jp",children:"日本語"})]})]})]}),(0,t.jsx)(n.$,{onClick:T,disabled:S||!e,className:"w-full",children:S?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Searching..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Search"]})}),F.length>0&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Search Results"}),(0,t.jsx)(m.F,{className:"h-[400px] w-full rounded-md border p-4",children:(0,t.jsx)("div",{className:"space-y-4",children:F.map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsxs)(u.E,{variant:"secondary",children:[(100*e.similarity).toFixed(1),"% match"]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.summary}),s<F.length-1&&(0,t.jsx)(p.w,{})]},e.article_id))})})]})]})]})}),(0,t.jsx)(o.av,{value:"update",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Update Stale Embeddings"}),(0,t.jsx)(i.BT,{children:"Regenerate embeddings for articles that have been modified"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)(n.$,{onClick:U,disabled:S,className:"w-full",children:S?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Update Stale Embeddings"]})})})]})}),(0,t.jsx)(o.av,{value:"related",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Find Related Articles"}),(0,t.jsx)(i.BT,{children:"Find articles similar to a given article"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"related-article-id",children:"Article ID"}),(0,t.jsx)(l.p,{id:"related-article-id",placeholder:"Enter article UUID",value:C,onChange:e=>N(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"related-language",children:"Language"}),(0,t.jsxs)(h.l6,{value:r,onValueChange:e=>w(e),children:[(0,t.jsx)(h.bq,{id:"related-language",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"en",children:"English"}),(0,t.jsx)(h.eb,{value:"jp",children:"日本語"})]})]})]}),(0,t.jsx)(n.$,{onClick:R,disabled:S||!C,className:"w-full",children:S?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Finding..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Find Related Articles"]})})]})]})})]})]})}},90606:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-embeddings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-embeddings\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8096,2076],()=>r(31544));module.exports=t})();