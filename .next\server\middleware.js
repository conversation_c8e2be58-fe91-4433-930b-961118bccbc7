(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{521:e=>{"use strict";e.exports=require("node:async_hooks")},356:e=>{"use strict";e.exports=require("node:buffer")},42:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a={};t.r(a),t.d(a,{config:()=>o,middleware:()=>u});var d=t(861),i=t(777),n=(t(819),t(321));async function u(e){let r=n.Rp.next();return"/setup-required"!==e.nextUrl.pathname?n.Rp.redirect(new URL("/setup-required",e.url)):r}let o={matcher:["/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"]};t(248);let s={...a},l=s.middleware||s.default,c="/middleware";if("function"!=typeof l)throw Error(`The Middleware "${c}" must export a \`middleware\` or a \`default\` function`);function p(e){return(0,i.O)({...e,page:c,handler:async(...e)=>{try{return await l(...e)}catch(i){let r=e[0],t=new URL(r.url),a=t.pathname+t.search;throw await (0,d.Ek)(i,{path:a,method:r.method,headers:Object.fromEntries(r.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}}},e=>{var r=r=>e(e.s=r);e.O(0,[96],()=>r(42));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map