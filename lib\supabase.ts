import { createClient } from '@supabase/supabase-js'
import { Database } from './database.types'

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Export types for TypeScript support
export type { Database } from './database.types'
