/**
 * Validation Middleware
 * 
 * This middleware validates incoming request data against Zod schemas
 * to ensure data integrity and prevent security issues.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { ZodSchema, ZodError } from 'zod';
import { ErrorService, ErrorSeverity, ErrorCategory } from '../services/error-service';

// Extend NextApiRequest to include validated data
declare module 'next' {
  interface NextApiRequest {
    validatedData?: any;
  }
}

/**
 * Validation locations in the request
 */
export enum ValidationTarget {
  BODY = 'body',
  QUERY = 'query',
  PARAMS = 'params'
}

/**
 * Options for the validation middleware
 */
export interface ValidationOptions {
  target?: ValidationTarget;
  stripUnknown?: boolean;
  abortEarly?: boolean;
}

/**
 * Default validation options
 */
const defaultOptions: ValidationOptions = {
  target: ValidationTarget.BODY,
  stripUnknown: true,
  abortEarly: false
};

/**
 * Format Zod validation errors into a user-friendly format
 */
function formatZodError(error: ZodError) {
  return {
    message: 'Validation failed',
    errors: error.errors.map(err => ({
      path: err.path.join('.'),
      message: err.message
    }))
  };
}

/**
 * Middleware to validate request data against a schema
 */
export function validateRequest(schema: ZodSchema, options: ValidationOptions = {}) {
  const { target, stripUnknown, abortEarly } = { ...defaultOptions, ...options };
  
  return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    try {
      // Get the data to validate based on the target
      const dataToValidate = req[target as keyof NextApiRequest];
      
      if (!dataToValidate) {
        const error = ErrorService.createValidationError(
          `No data found in request ${target}`,
          { target },
          { path: req.url, action: 'validate_request' }
        );
        
        return res.status(400).json(error.toResponse());
      }
      
      // Validate the data against the schema
      const validatedData = await schema.parseAsync(dataToValidate);
      
      // Attach the validated data to the request
      req.validatedData = validatedData;
      
      // Continue to the next middleware or handler
      return next();
    } catch (error) {
      // Handle Zod validation errors
      if (error instanceof ZodError) {
        const formattedError = formatZodError(error);
        
        const appError = ErrorService.createValidationError(
          'Request validation failed',
          { details: formattedError.errors },
          { path: req.url, action: 'validate_request' }
        );
        
        return res.status(400).json(appError.toResponse());
      }
      
      // Handle other errors
      const appError = ErrorService.createAppError(
        'Unexpected error during validation',
        {
          severity: ErrorSeverity.ERROR,
          category: ErrorCategory.VALIDATION,
          code: 'VALIDATION_ERROR',
          context: { path: req.url, action: 'validate_request' },
          originalError: error instanceof Error ? error : undefined
        }
      );
      
      return res.status(500).json(appError.toResponse());
    }
  };
}

/**
 * Middleware to validate request body
 */
export function validateBody(schema: ZodSchema) {
  return validateRequest(schema, { target: ValidationTarget.BODY });
}

/**
 * Middleware to validate query parameters
 */
export function validateQuery(schema: ZodSchema) {
  return validateRequest(schema, { target: ValidationTarget.QUERY });
}

/**
 * Middleware to validate route parameters
 */
export function validateParams(schema: ZodSchema) {
  return validateRequest(schema, { target: ValidationTarget.PARAMS });
}

/**
 * Example usage:
 * 
 * import { validateBody } from '../middleware/validate';
 * import { authSchemas } from '../validation/schemas';
 * 
 * // In your API route
 * export default function handler(req: NextApiRequest, res: NextApiResponse) {
 *   // Apply validation middleware
 *   return validateBody(authSchemas.login)(req, res, () => {
 *     // Access validated data
 *     const { email, password } = req.validatedData;
 *     
 *     // Process the request
 *     // ...
 *   });
 * }
 */