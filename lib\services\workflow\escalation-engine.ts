import { createClient } from '@/lib/supabase/server';
import { EscalationConfig, WorkflowTask } from './types';
import { addMinutes } from 'date-fns';

export class EscalationEngine {
  private escalationTimers: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Schedule escalation for a task
   */
  async scheduleEscalation(
    taskId: string,
    escalationConfig: EscalationConfig
  ): Promise<void> {
    const supabase = createClient();

    // Get task details
    const { data: task } = await supabase
      .from('workflow_tasks')
      .select('*')
      .eq('id', taskId)
      .single();

    if (!task || task.status !== 'pending') {
      return;
    }

    // Schedule escalations for each level
    for (const level of escalationConfig.levels) {
      const escalationTime = addMinutes(new Date(task.created_at), level.minutes);
      const delay = escalationTime.getTime() - Date.now();

      if (delay > 0) {
        const timerId = setTimeout(async () => {
          await this.executeEscalation(taskId, level, escalationConfig.levels.indexOf(level) + 1);
        }, delay);

        // Store timer reference
        this.escalationTimers.set(`${taskId}-${level.minutes}`, timerId);
      }
    }
  }

  /**
   * Execute escalation for a task
   */
  private async executeEscalation(
    taskId: string,
    escalationLevel: any,
    levelNumber: number
  ): Promise<void> {
    const supabase = createClient();

    // Check if task is still pending
    const { data: task } = await supabase
      .from('workflow_tasks')
      .select('*')
      .eq('id', taskId)
      .single();

    if (!task || task.status !== 'pending') {
      this.clearEscalationTimers(taskId);
      return;
    }

    // Find new assignee based on escalation config
    const newAssignee = await this.resolveEscalationAssignee(
      escalationLevel.assignee,
      task
    );

    // Update task assignment
    await supabase
      .from('workflow_tasks')
      .update({
        assigned_to: newAssignee.userId,
        assigned_role: newAssignee.role,
        status: 'escalated',
        updated_at: new Date().toISOString(),
      })
      .eq('id', taskId);

    // Log escalation
    await supabase.from('escalation_log').insert({
      workflow_task_id: taskId,
      escalated_from: task.assigned_to,
      escalated_to: newAssignee.userId,
      escalation_reason: `Task not completed within ${escalationLevel.minutes} minutes`,
      escalation_level: levelNumber,
    });

    // Send escalation notification
    await this.sendEscalationNotification(task, newAssignee, levelNumber);
  }

  /**
   * Cancel escalation for a task
   */
  async cancelEscalation(taskId: string): Promise<void> {
    this.clearEscalationTimers(taskId);
  }

  /**
   * Clear escalation timers for a task
   */
  private clearEscalationTimers(taskId: string): void {
    // Clear all timers for this task
    for (const [key, timerId] of this.escalationTimers.entries()) {
      if (key.startsWith(taskId)) {
        clearTimeout(timerId);
        this.escalationTimers.delete(key);
      }
    }
  }

  /**
   * Resolve escalation assignee
   */
  private async resolveEscalationAssignee(
    assigneeConfig: any,
    task: WorkflowTask
  ): Promise<{ userId?: string; role?: string }> {
    const supabase = createClient();

    if (assigneeConfig.userId) {
      return { userId: assigneeConfig.userId };
    }

    if (assigneeConfig.role === 'manager') {
      // Find manager of current assignee
      if (task.assigned_to) {
        const { data: currentUser } = await supabase
          .from('staff')
          .select('*, departments!inner(*)')
          .eq('id', task.assigned_to)
          .single();

        if (currentUser) {
          // Find department manager
          const { data: manager } = await supabase
            .from('staff')
            .select('*')
            .eq('department_id', currentUser.department_id)
            .eq('role', 'Department Administrator')
            .single();

          if (manager) {
            return { userId: manager.id };
          }
        }
      }
    }

    if (assigneeConfig.role) {
      return { role: assigneeConfig.role };
    }

    // Default to system admin
    const { data: admin } = await supabase
      .from('staff')
      .select('*')
      .eq('role', 'Web App System Administrator')
      .limit(1)
      .single();

    return { userId: admin?.id };
  }

  /**
   * Send escalation notification
   */
  private async sendEscalationNotification(
    task: WorkflowTask,
    newAssignee: any,
    level: number
  ): Promise<void> {
    // Integrate with notification service
    console.log(`Escalation notification: Task ${task.id} escalated to level ${level}`);
  }

  /**
   * Get active escalations
   */
  async getActiveEscalations(): Promise<any[]> {
    const supabase = createClient();

    const { data: escalations } = await supabase
      .from('workflow_tasks')
      .select(`
        *,
        escalation_log!inner(*),
        workflow_instances!inner(*)
      `)
      .eq('status', 'escalated')
      .order('escalation_log.created_at', { ascending: false });

    return escalations || [];
  }

  /**
   * Get escalation metrics
   */
  async getEscalationMetrics(
    startDate: Date,
    endDate: Date
  ): Promise<any> {
    const supabase = createClient();

    const { data: logs } = await supabase
      .from('escalation_log')
      .select('*, workflow_tasks!inner(*)')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (!logs) return null;

    const metrics = {
      total_escalations: logs.length,
      by_level: {} as Record<number, number>,
      by_reason: {} as Record<string, number>,
      average_time_to_escalation: 0,
      resolution_after_escalation: 0,
    };

    // Calculate metrics
    logs.forEach(log => {
      // Count by level
      if (!metrics.by_level[log.escalation_level]) {
        metrics.by_level[log.escalation_level] = 0;
      }
      metrics.by_level[log.escalation_level]++;

      // Count by reason
      const reason = log.escalation_reason || 'Unknown';
      if (!metrics.by_reason[reason]) {
        metrics.by_reason[reason] = 0;
      }
      metrics.by_reason[reason]++;
    });

    return metrics;
  }
}
