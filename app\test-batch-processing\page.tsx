"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { batchProcessingService } from '@/lib/services/batch-processing-service';
import type { BatchRequest, BatchScenario } from '@/lib/batch-processing-types';
import { useAuth } from '@/lib/auth-context';
import { toast } from 'sonner';
import { 
  PlayCircle, 
  FileText, 
  Users, 
  Settings, 
  CheckCircle,
  Loader2,
  AlertCircle
} from 'lucide-react';

// Test scenarios based on PRD requirements
const testScenarios = [
  {
    id: 'scenario1',
    name: 'シナリオ 1: 単一ユーザー・複数リクエスト（追加）',
    description: '一人のユーザーに対して、グループメール、メールボックス、SPOアクセスを追加',
    scenario: 'single_user_multiple_requests_add' as BatchScenario,
    setup: {
      users: 1,
      services: ['GM', 'MB', 'SPO'],
      action: 'add'
    }
  },
  {
    id: 'scenario2',
    name: 'シナリオ 2: 複数ユーザー・複数リクエスト（追加、PC管理者権限）',
    description: '複数のユーザーに対して、グループメール、メールボックス、PC管理者権限、SPOアクセスを追加',
    scenario: 'multiple_users_multiple_requests_add' as BatchScenario,
    setup: {
      users: 3,
      services: ['GM', 'MB', 'PCA', 'SPO'],
      action: 'add'
    }
  },
  {
    id: 'scenario3',
    name: 'シナリオ 3: 複数ユーザー・単一リクエスト（追加）',
    description: '複数のユーザーに対して、同じグループメールアドレスへの追加',
    scenario: 'multiple_users_single_request_add' as BatchScenario,
    setup: {
      users: 5,
      services: ['GM'],
      action: 'add'
    }
  },  {
    id: 'scenario4',
    name: 'シナリオ 4: 単一ユーザー・複数リクエスト（削除）',
    description: '一人のユーザーから、グループメール、メールボックス、SPOアクセスを削除',
    scenario: 'single_user_multiple_requests_delete' as BatchScenario,
    setup: {
      users: 1,
      services: ['GM', 'MB', 'SPO'],
      action: 'remove'
    }
  },
  {
    id: 'scenario5',
    name: 'シナリオ 5: 単一ユーザー・複数リクエスト（削除と追加）',
    description: '一人のユーザーの、グループメール（削除と追加）、メールボックス（削除と追加）、SPOアクセス',
    scenario: 'single_user_multiple_requests_mixed' as BatchScenario,
    setup: {
      users: 1,
      services: ['GM', 'MB', 'SPO'],
      action: 'mixed'
    }
  },
  {
    id: 'scenario6',
    name: 'シナリオ 6/7: 単一ユーザー・複数カテゴリ（削除と追加、PC管理者権限）',
    description: '一人のユーザーの、グループメール、メールボックス、PC管理者権限の混合操作',
    scenario: 'single_user_multiple_category_mixed' as BatchScenario,
    setup: {
      users: 1,
      services: ['GM', 'MB', 'PCA'],
      action: 'mixed'
    }
  }
];

export default function TestBatchProcessingPage() {
  const { user, staff } = useAuth();
  const [runningTests, setRunningTests] = useState<Set<string>>(new Set());
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  const runTestScenario = async (scenario: typeof testScenarios[0]) => {
    if (!user || !staff) {
      toast.error('テストを実行するにはログインが必要です');
      return;
    }

    setRunningTests(prev => new Set(prev).add(scenario.id));

    try {
      // Create mock batch request based on scenario
      const mockRequest = await createMockBatchRequest(scenario, staff);
      
      // Submit batch request      const result = await batchProcessingService.createBatchOperation(
        staff.id,
        mockRequest
      );

      setTestResults(prev => ({
        ...prev,
        [scenario.id]: {
          success: result.success,
          batchId: result.batch_id,
          message: result.message,
          timestamp: new Date().toISOString()
        }
      }));

      if (result.success) {
        toast.success(`${scenario.name} のテストを開始しました`);
      } else {
        toast.error(`${scenario.name} のテストに失敗しました: ${result.message}`);
      }
    } catch (error: any) {
      toast.error(`エラーが発生しました: ${error.message}`);
      setTestResults(prev => ({
        ...prev,
        [scenario.id]: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setRunningTests(prev => {
        const newSet = new Set(prev);
        newSet.delete(scenario.id);
        return newSet;
      });
    }
  };

  const createMockBatchRequest = async (
    scenario: typeof testScenarios[0],
    currentStaff: any
  ): Promise<BatchRequest> => {
    // This would create a proper batch request based on the scenario
    // For now, returning a simplified mock
    return {
      operation_type: getOperationType(scenario.scenario),
      items: []
    };
  };

  const getOperationType = (scenario: BatchScenario): BatchRequest['operation_type'] => {    if (scenario.includes('single_user')) {
      return 'single_user_multi_service';
    } else if (scenario.includes('multiple_users') && scenario.includes('single_request')) {
      return 'multi_user_single_service';
    } else {
      return 'multi_user_multi_service';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">バッチ処理テストページ</h1>
        <p className="text-muted-foreground mt-2">
          PRDで定義されたシナリオをテストします
        </p>
      </div>

      <Alert className="mb-6">
        <AlertDescription>
          このページは開発・テスト用です。実際のデータに影響を与える可能性があります。
        </AlertDescription>
      </Alert>

      <div className="grid gap-6">
        {testScenarios.map((scenario) => (
          <Card key={scenario.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{scenario.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {scenario.description}
                  </CardDescription>
                </div>
                <Badge variant="outline">{scenario.scenario}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">ユーザー数:</span>
                    <span className="ml-2 font-medium">{scenario.setup.users}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">サービス:</span>
                    <span className="ml-2 font-medium">
                      {scenario.setup.services.join(', ')}
                    </span>
                  </div>                  <div>
                    <span className="text-muted-foreground">アクション:</span>
                    <span className="ml-2 font-medium">{scenario.setup.action}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Button
                    onClick={() => runTestScenario(scenario)}
                    disabled={runningTests.has(scenario.id)}
                  >
                    {runningTests.has(scenario.id) ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        実行中...
                      </>
                    ) : (
                      <>
                        <PlayCircle className="w-4 h-4 mr-2" />
                        テストを実行
                      </>
                    )}
                  </Button>

                  {testResults[scenario.id] && (
                    <div className="flex items-center gap-2">
                      {testResults[scenario.id].success ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-600" />
                      )}
                      <span className="text-sm">
                        {testResults[scenario.id].batchId || testResults[scenario.id].error}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}