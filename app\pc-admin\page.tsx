"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { PcAdminRequestForm } from '@/components/pc-admin/pc-admin-request-form';
import { PcAdminRequestsList } from '@/components/pc-admin/pc-admin-requests-list';
import AuthGuard from '@/components/auth/auth-guard';
import { useAuth } from '@/lib/auth-context';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

export default function PcAdminPage() {
  const { isITSupport, isGlobalAdmin, isSystemAdmin } = useAuth();
  const [refreshKey, setRefreshKey] = useState(0);
  
  const canViewAllRequests = isITSupport() || isGlobalAdmin() || isSystemAdmin();

  const handleRefresh = () => {
    setR<PERSON><PERSON><PERSON><PERSON>(prev => prev + 1);
  };

  return (
    <AuthGuard>
      <div className="container mx-auto py-8">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">PC管理者権限管理</h1>
            <p className="text-muted-foreground mt-2">
              PC管理者権限の申請と管理を行います
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            更新
          </Button>
        </div>

        <Tabs defaultValue="request" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="request">新規申請</TabsTrigger>
            <TabsTrigger value="my-requests">自分のリクエスト</TabsTrigger>
            {canViewAllRequests && (
              <TabsTrigger value="all-requests">すべてのリクエスト</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="request">
            <PcAdminRequestForm />