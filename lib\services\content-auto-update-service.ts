import { supabase } from '@/lib/supabase'
import { embeddingsService } from './embeddings-service'
import { aiApiService } from './ai-api-service'

interface FeedbackData {
  article_id: string
  feedback_type: 'helpful' | 'not_helpful' | 'outdated' | 'incorrect'
  suggestion?: string
  user_id: string
}

interface ContentUpdateLog {
  article_id: string
  update_type: 'auto' | 'feedback' | 'scheduled'
  changes_made: string[]
  confidence_score: number
  approved: boolean
  approved_by?: string
  created_at: string
}

interface RelatedUpdate {
  article_id: string
  related_articles: string[]
  update_reason: string
}

export class ContentAutoUpdateService {
  private static instance: ContentAutoUpdateService
  
  private constructor() {}
  
  static getInstance(): ContentAutoUpdateService {
    if (!ContentAutoUpdateService.instance) {
      ContentAutoUpdateService.instance = new ContentAutoUpdateService()
    }
    return ContentAutoUpdateService.instance
  }

  /**
   * Process user feedback and update content if needed
   */
  async processFeedback(feedback: FeedbackData): Promise<void> {
    try {
      // Store feedback in database
      const { error: feedbackError } = await supabase
        .from('kb_article_feedback')
        .insert({
          article_id: feedback.article_id,
          user_id: feedback.user_id,
          feedback_type: feedback.feedback_type,
          suggestion: feedback.suggestion,
          created_at: new Date().toISOString()
        })

      if (feedbackError) {
        throw new Error(`Failed to store feedback: ${feedbackError.message}`)
      }

      // Check if article needs update based on feedback patterns
      const shouldUpdate = await this.shouldUpdateArticle(feedback.article_id)
      
      if (shouldUpdate) {
        await this.updateArticleContent(feedback.article_id, 'feedback')
      }
    } catch (error) {
      console.error('Error processing feedback:', error)
      throw error
    }
  }

  /**
   * Check if an article should be updated based on feedback patterns
   */
  private async shouldUpdateArticle(articleId: string): Promise<boolean> {
    const { data: feedbackStats, error } = await supabase
      .rpc('get_article_feedback_stats', {
        p_article_id: articleId,
        p_days: 7
      })

    if (error || !feedbackStats) {
      console.error('Error getting feedback stats:', error)
      return false
    }

    // Update if:
    // - More than 5 negative feedbacks in the last 7 days
    // - Negative feedback rate > 30%
    // - Multiple "outdated" or "incorrect" reports
    const negativeCount = feedbackStats.not_helpful_count + 
                         feedbackStats.outdated_count + 
                         feedbackStats.incorrect_count
    
    const totalCount = feedbackStats.total_count
    const negativeRate = totalCount > 0 ? negativeCount / totalCount : 0

    return negativeCount > 5 || negativeRate > 0.3 || 
           feedbackStats.outdated_count > 2 || 
           feedbackStats.incorrect_count > 2
  }

  /**
   * Update article content based on feedback and AI analysis
   */
  async updateArticleContent(
    articleId: string, 
    updateType: 'auto' | 'feedback' | 'scheduled' = 'auto'
  ): Promise<void> {
    try {
      // Get current article content
      const { data: article, error } = await supabase
        .from('kb_articles')
        .select('*')
        .eq('id', articleId)
        .single()

      if (error || !article) {
        throw new Error('Article not found')
      }

      // Get recent feedback
      const { data: feedback } = await supabase
        .from('kb_article_feedback')
        .select('*')
        .eq('article_id', articleId)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })

      // Get search queries that led to this article
      const { data: searchHistory } = await supabase
        .from('kb_search_history')
        .select('query')
        .contains('clicked_results', [articleId])
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())

      // Prepare prompt for AI to suggest updates
      const updatePrompt = this.buildUpdatePrompt(article, feedback || [], searchHistory || [])
      
      // Get AI suggestions for content updates
      const suggestions = await aiApiService.generateText(updatePrompt, {
        temperature: 0.3,
        maxTokens: 2000
      })

      // Parse and validate suggestions
      const updates = this.parseSuggestions(suggestions)
      
      if (updates.length === 0) {
        console.log('No updates suggested for article:', articleId)
        return
      }

      // Apply updates with confidence scoring
      const confidenceScore = await this.calculateUpdateConfidence(updates, article)
      
      if (confidenceScore >= 0.7) {
        // High confidence - apply automatically
        await this.applyUpdates(article, updates)
        
        // Log the update
        await this.logContentUpdate({
          article_id: articleId,
          update_type: updateType,
          changes_made: updates.map(u => u.description),
          confidence_score: confidenceScore,
          approved: true,
          created_at: new Date().toISOString()
        })
        
        // Update embeddings for the new content
        await embeddingsService.generateArticleEmbeddings(
          articleId,
          article.content_en,
          article.content_jp
        )
        
        // Update related articles
        await this.updateRelatedArticles(articleId)
      } else {
        // Low confidence - queue for manual review
        await this.queueForReview(articleId, updates, confidenceScore)
      }
    } catch (error) {
      console.error('Error updating article content:', error)
      throw error
    }
  }

  /**
   * Build prompt for AI to suggest content updates
   */
  private buildUpdatePrompt(
    article: any,
    feedback: any[],
    searchHistory: any[]
  ): string {
    const feedbackSummary = feedback.map(f => 
      `- ${f.feedback_type}: ${f.suggestion || 'No suggestion provided'}`
    ).join('\n')

    const commonQueries = this.extractCommonQueries(searchHistory)

    return `
You are an expert content editor for an IT helpdesk knowledge base. 
Analyze the following article and suggest updates based on user feedback and search patterns.

Article Title: ${article.title_en} / ${article.title_jp}
Category: ${article.category}
Current Content (English):
${article.content_en}

Current Content (Japanese):
${article.content_jp}

Recent User Feedback:
${feedbackSummary || 'No recent feedback'}

Common Search Queries Leading to This Article:
${commonQueries.join(', ') || 'No search data available'}

Based on this information, suggest specific updates to improve the article's accuracy, clarity, and usefulness.
Format your response as JSON:
{
  "updates": [
    {
      "type": "content_addition" | "content_revision" | "example_addition" | "clarification",
      "location": "beginning" | "middle" | "end" | "specific_section",
      "content_en": "English content to add/revise",
      "content_jp": "Japanese content to add/revise",
      "reason": "Explanation of why this update is needed",
      "description": "Brief description of the change"
    }
  ],
  "metadata_updates": {
    "tags": ["new", "tags"],
    "related_articles": ["article_ids"]
  }
}
`
  }

  /**
   * Extract common queries from search history
   */
  private extractCommonQueries(searchHistory: any[]): string[] {
    const queries = searchHistory.map(s => s.query.toLowerCase())
    const queryCount = new Map<string, number>()
    
    queries.forEach(query => {
      queryCount.set(query, (queryCount.get(query) || 0) + 1)
    })
    
    return Array.from(queryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([query]) => query)
  }

  /**
   * Parse AI suggestions from response
   */
  private parseSuggestions(aiResponse: string): any[] {
    try {
      const parsed = JSON.parse(aiResponse)
      return parsed.updates || []
    } catch (error) {
      console.error('Error parsing AI suggestions:', error)
      return []
    }
  }

  /**
   * Calculate confidence score for proposed updates
   */
  private async calculateUpdateConfidence(updates: any[], article: any): Promise<number> {
    let score = 0.5 // Base score
    
    // Increase confidence based on update types
    updates.forEach(update => {
      switch (update.type) {
        case 'clarification':
        case 'example_addition':
          score += 0.1
          break
        case 'content_addition':
          score += 0.05
          break
        case 'content_revision':
          score -= 0.05 // More risky
          break
      }
    })
    
    // Check if updates align with category best practices
    const categoryGuidelines = await this.getCategoryGuidelines(article.category)
    if (categoryGuidelines) {
      const alignmentScore = this.checkGuidelineAlignment(updates, categoryGuidelines)
      score += alignmentScore * 0.2
    }
    
    // Cap at 1.0
    return Math.min(score, 1.0)
  }

  /**
   * Apply approved updates to article
   */
  private async applyUpdates(article: any, updates: any[]): Promise<void> {
    let updatedContentEn = article.content_en
    let updatedContentJp = article.content_jp
    
    // Apply each update
    updates.forEach(update => {
      switch (update.type) {
        case 'content_addition':
          // Add content to specified location
          if (update.location === 'end') {
            updatedContentEn += '\n\n' + update.content_en
            updatedContentJp += '\n\n' + update.content_jp
          } else if (update.location === 'beginning') {
            updatedContentEn = update.content_en + '\n\n' + updatedContentEn
            updatedContentJp = update.content_jp + '\n\n' + updatedContentJp
          }
          break
          
        case 'content_revision':
          // This would need more sophisticated text replacement logic
          // For now, append as a note
          updatedContentEn += '\n\n**Updated Information:**\n' + update.content_en
          updatedContentJp += '\n\n**更新情報:**\n' + update.content_jp
          break
          
        case 'example_addition':
          updatedContentEn += '\n\n**Example:**\n' + update.content_en
          updatedContentJp += '\n\n**例:**\n' + update.content_jp
          break
          
        case 'clarification':
          updatedContentEn += '\n\n**Note:**\n' + update.content_en
          updatedContentJp += '\n\n**注記:**\n' + update.content_jp
          break
      }
    })
    
    // Update article in database
    const { error } = await supabase
      .from('kb_articles')
      .update({
        content_en: updatedContentEn,
        content_jp: updatedContentJp,
        updated_at: new Date().toISOString(),
        last_auto_update: new Date().toISOString()
      })
      .eq('id', article.id)
    
    if (error) {
      throw new Error(`Failed to update article: ${error.message}`)
    }
  }

  /**
   * Queue updates for manual review
   */
  private async queueForReview(
    articleId: string, 
    updates: any[], 
    confidenceScore: number
  ): Promise<void> {
    const { error } = await supabase
      .from('kb_update_queue')
      .insert({
        article_id: articleId,
        suggested_updates: updates,
        confidence_score: confidenceScore,
        status: 'pending',
        created_at: new Date().toISOString()
      })
    
    if (error) {
      console.error('Error queuing for review:', error)
    }
  }

  /**
   * Log content update for audit trail
   */
  private async logContentUpdate(log: ContentUpdateLog): Promise<void> {
    const { error } = await supabase
      .from('kb_update_logs')
      .insert(log)
    
    if (error) {
      console.error('Error logging update:', error)
    }
  }

  /**
   * Update related articles based on content changes
   */
  private async updateRelatedArticles(articleId: string): Promise<void> {
    try {
      // Find new related articles based on updated content
      await embeddingsService.findRelatedArticles(articleId, 'en', 5)
      await embeddingsService.findRelatedArticles(articleId, 'jp', 5)
    } catch (error) {
      console.error('Error updating related articles:', error)
    }
  }

  /**
   * Get category-specific content guidelines
   */
  private async getCategoryGuidelines(category: string): Promise<any> {
    const guidelines = {
      'group_mail': {
        required_sections: ['purpose', 'access_levels', 'request_process'],
        tone: 'professional',
        min_examples: 1
      },
      'sharepoint': {
        required_sections: ['overview', 'permissions', 'best_practices'],
        tone: 'technical',
        min_examples: 2
      },
      'password_reset': {
        required_sections: ['steps', 'requirements', 'troubleshooting'],
        tone: 'helpful',
        min_examples: 0
      },
      'pc_admin': {
        required_sections: ['justification', 'security_notes', 'process'],
        tone: 'security-focused',
        min_examples: 1
      }
    }
    
    return guidelines[category] || null
  }

  /**
   * Check if updates align with category guidelines
   */
  private checkGuidelineAlignment(updates: any[], guidelines: any): number {
    if (!guidelines) return 0.5
    
    let alignmentScore = 0
    let checks = 0
    
    // Check if updates improve required sections
    updates.forEach(update => {
      checks++
      if (update.type === 'content_addition' || update.type === 'clarification') {
        alignmentScore += 0.8
      } else if (update.type === 'example_addition' && guidelines.min_examples > 0) {
        alignmentScore += 1.0
      } else {
        alignmentScore += 0.5
      }
    })
    
    return checks > 0 ? alignmentScore / checks : 0.5
  }

  /**
   * Schedule periodic content review and updates
   */
  async schedulePeriodicReview(): Promise<void> {
    try {
      // Get all articles that haven't been updated in 30 days
      const { data: articles, error } = await supabase
        .from('kb_articles')
        .select('id, updated_at')
        .eq('status', 'published')
        .or(`last_auto_update.is.null,last_auto_update.lt.${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()}`)
      
      if (error) {
        throw new Error(`Failed to fetch articles for review: ${error.message}`)
      }
      
      if (!articles || articles.length === 0) {
        console.log('No articles need periodic review')
        return
      }
      
      // Process articles in batches to avoid overwhelming the system
      const batchSize = 5
      for (let i = 0; i < articles.length; i += batchSize) {
        const batch = articles.slice(i, i + batchSize)
        await Promise.all(
          batch.map(article => this.updateArticleContent(article.id, 'scheduled'))
        )
        
        // Wait between batches to avoid rate limits
        if (i + batchSize < articles.length) {
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      }
      
      console.log(`Completed periodic review of ${articles.length} articles`)
    } catch (error) {
      console.error('Error in periodic review:', error)
      throw error
    }
  }

  /**
   * Analyze search patterns to identify content gaps
   */
  async analyzeContentGaps(): Promise<void> {
    try {
      // Get search queries with no results or low click-through
      const { data: unsuccessfulSearches, error } = await supabase
        .from('kb_search_history')
        .select('query, search_type')
        .or('clicked_results.is.null,clicked_results.eq.{}')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      
      if (error) {
        throw new Error(`Failed to fetch search history: ${error.message}`)
      }
      
      if (!unsuccessfulSearches || unsuccessfulSearches.length === 0) {
        console.log('No content gaps identified')
        return
      }
      
      // Group queries by similarity
      const queryGroups = this.groupSimilarQueries(unsuccessfulSearches)
      
      // Create content gap report
      const gaps = queryGroups
        .filter(group => group.count >= 3) // At least 3 similar queries
        .map(group => ({
          topic: group.representative,
          query_count: group.count,
          suggested_category: this.suggestCategory(group.representative),
          priority: this.calculateGapPriority(group)
        }))
      
      // Store content gaps for review
      if (gaps.length > 0) {
        const { error: insertError } = await supabase
          .from('kb_content_gaps')
          .insert(gaps.map(gap => ({
            ...gap,
            status: 'identified',
            created_at: new Date().toISOString()
          })))
        
        if (insertError) {
          console.error('Error storing content gaps:', insertError)
        } else {
          console.log(`Identified ${gaps.length} content gaps`)
        }
      }
    } catch (error) {
      console.error('Error analyzing content gaps:', error)
      throw error
    }
  }

  /**
   * Group similar search queries
   */
  private groupSimilarQueries(queries: any[]): any[] {
    const groups: Map<string, { queries: string[], count: number }> = new Map()
    
    queries.forEach(({ query }) => {
      const normalized = query.toLowerCase().trim()
      let foundGroup = false
      
      // Check if query fits in existing group
      for (const [rep, group] of groups.entries()) {
        if (this.queriesAreSimilar(normalized, rep)) {
          group.queries.push(query)
          group.count++
          foundGroup = true
          break
        }
      }
      
      // Create new group if no match found
      if (!foundGroup) {
        groups.set(normalized, { queries: [query], count: 1 })
      }
    })
    
    return Array.from(groups.entries()).map(([rep, group]) => ({
      representative: rep,
      queries: group.queries,
      count: group.count
    }))
  }

  /**
   * Check if two queries are similar
   */
  private queriesAreSimilar(q1: string, q2: string): boolean {
    // Simple similarity check - can be enhanced with better NLP
    const words1 = new Set(q1.split(/\s+/))
    const words2 = new Set(q2.split(/\s+/))
    
    const intersection = new Set([...words1].filter(w => words2.has(w)))
    const union = new Set([...words1, ...words2])
    
    const similarity = intersection.size / union.size
    return similarity > 0.5
  }

  /**
   * Suggest category for a query
   */
  private suggestCategory(query: string): string {
    const categoryKeywords = {
      'group_mail': ['group', 'mail', 'email', 'distribution', 'list'],
      'sharepoint': ['sharepoint', 'spo', 'library', 'document', 'folder'],
      'password_reset': ['password', 'reset', 'forgot', 'locked', 'account'],
      'pc_admin': ['admin', 'administrator', 'privilege', 'install', 'software']
    }
    
    const normalizedQuery = query.toLowerCase()
    
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => normalizedQuery.includes(keyword))) {
        return category
      }
    }
    
    return 'general'
  }

  /**
   * Calculate priority for content gap
   */
  private calculateGapPriority(group: any): 'high' | 'medium' | 'low' {
    if (group.count >= 10) return 'high'
    if (group.count >= 5) return 'medium'
    return 'low'
  }
}

// Export singleton instance
export const contentAutoUpdateService = ContentAutoUpdateService.getInstance()
