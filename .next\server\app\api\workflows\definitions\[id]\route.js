"use strict";(()=>{var e={};e.id=2115,e.ids=[2115],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},98662:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var o={};t.r(o),t.d(o,{DELETE:()=>c,GET:()=>p,PUT:()=>f});var s=t(42706),n=t(28203),i=t(45994),a=t(39187),d=t(61487),u=t(98837);let l=u.z.object({name:u.z.string().min(1).optional(),description:u.z.string().optional(),workflow_json:u.z.object({id:u.z.string(),name:u.z.string(),version:u.z.number(),triggers:u.z.record(u.z.any()),states:u.z.record(u.z.any())}).optional(),is_active:u.z.boolean().optional()});async function p(e,{params:r}){try{let e=await (0,d.createServerClient)(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:n}=await e.from("workflow_definitions").select("*").eq("id",r.id).single();if(n||!s)return a.NextResponse.json({error:"Workflow definition not found"},{status:404});return a.NextResponse.json({data:s})}catch(e){return console.error("Error fetching workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:r}){try{let t=await (0,d.createServerClient)(),{data:{user:o},error:s}=await t.auth.getUser();if(s||!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:n}=await t.from("staff").select("role:roles(name)").eq("auth_id",o.id).single(),i=n?.role?.name;if(!["Global Administrator","Web App System Administrator"].includes(i))return a.NextResponse.json({error:"Forbidden"},{status:403});let u=await e.json(),p=l.safeParse(u);if(!p.success)return a.NextResponse.json({error:"Invalid request data",details:p.error.errors},{status:400});let{data:f,error:c}=await t.from("workflow_definitions").update({...p.data,updated_at:new Date().toISOString()}).eq("id",r.id).select().single();if(c)return a.NextResponse.json({error:c.message},{status:500});if(!f)return a.NextResponse.json({error:"Workflow definition not found"},{status:404});return a.NextResponse.json({data:f})}catch(e){return console.error("Error updating workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e,{params:r}){try{let e=await (0,d.createServerClient)(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await e.from("staff").select("role:roles(name)").eq("auth_id",t.id).single(),n=s?.role?.name;if(!["Global Administrator","Web App System Administrator"].includes(n))return a.NextResponse.json({error:"Forbidden"},{status:403});let{data:i}=await e.from("workflow_instances").select("id").eq("workflow_definition_id",r.id).eq("status","active").limit(1);if(i&&i.length>0)return a.NextResponse.json({error:"Cannot delete workflow definition that has active instances"},{status:409});let{data:u,error:l}=await e.from("workflow_definitions").update({is_active:!1,updated_at:new Date().toISOString()}).eq("id",r.id).select().single();if(l)return a.NextResponse.json({error:l.message},{status:500});if(!u)return a.NextResponse.json({error:"Workflow definition not found"},{status:404});return a.NextResponse.json({message:"Workflow definition deleted successfully"})}catch(e){return console.error("Error deleting workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/definitions/[id]/route",pathname:"/api/workflows/definitions/[id]",filename:"route",bundlePath:"app/api/workflows/definitions/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\[id]\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:g}=w;function j(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(98662));module.exports=o})();