"use strict";(()=>{var e={};e.id=2115,e.ids=[2115],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},98662:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var o={};t.r(o),t.d(o,{DELETE:()=>p,GET:()=>l,PUT:()=>f});var n=t(42706),s=t(28203),i=t(45994),a=t(39187),d=t(59913);!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let u=d.z.object({name:d.z.string().min(1).optional(),description:d.z.string().optional(),workflow_json:d.z.object({id:d.z.string(),name:d.z.string(),version:d.z.number(),triggers:d.z.record(d.z.any()),states:d.z.record(d.z.any())}).optional(),is_active:d.z.boolean().optional()});async function l(e,{params:r}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:n,error:s}=await e.from("workflow_definitions").select("*").eq("id",r.id).single();if(s||!n)return a.NextResponse.json({error:"Workflow definition not found"},{status:404});return a.NextResponse.json({data:n})}catch(e){return console.error("Error fetching workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:r}){try{let t=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:o},error:n}=await t.auth.getUser();if(n||!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await t.from("staff").select("role:roles(name)").eq("auth_id",o.id).single(),i=s?.role?.name;if(!["Global Administrator","Web App System Administrator"].includes(i))return a.NextResponse.json({error:"Forbidden"},{status:403});let d=await e.json(),l=u.safeParse(d);if(!l.success)return a.NextResponse.json({error:"Invalid request data",details:l.error.errors},{status:400});let{data:f,error:p}=await t.from("workflow_definitions").update({...l.data,updated_at:new Date().toISOString()}).eq("id",r.id).select().single();if(p)return a.NextResponse.json({error:p.message},{status:500});if(!f)return a.NextResponse.json({error:"Workflow definition not found"},{status:404});return a.NextResponse.json({data:f})}catch(e){return console.error("Error updating workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:r}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:n}=await e.from("staff").select("role:roles(name)").eq("auth_id",t.id).single(),s=n?.role?.name;if(!["Global Administrator","Web App System Administrator"].includes(s))return a.NextResponse.json({error:"Forbidden"},{status:403});let{data:i}=await e.from("workflow_instances").select("id").eq("workflow_definition_id",r.id).eq("status","active").limit(1);if(i&&i.length>0)return a.NextResponse.json({error:"Cannot delete workflow definition that has active instances"},{status:409});let{data:d,error:u}=await e.from("workflow_definitions").update({is_active:!1,updated_at:new Date().toISOString()}).eq("id",r.id).select().single();if(u)return a.NextResponse.json({error:u.message},{status:500});if(!d)return a.NextResponse.json({error:"Workflow definition not found"},{status:404});return a.NextResponse.json({message:"Workflow definition deleted successfully"})}catch(e){return console.error("Error deleting workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/definitions/[id]/route",pathname:"/api/workflows/definitions/[id]",filename:"route",bundlePath:"app/api/workflows/definitions/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\[id]\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:x}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(98662));module.exports=o})();