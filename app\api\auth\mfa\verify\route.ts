import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { MFAService } from '@/lib/services/mfa-service';

export async function POST(request: Request) {
  try {
    const { sessionToken, code } = await request.json();
    
    if (!sessionToken || !code) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });
    const mfaService = new MFAService(supabase);
    
    const result = await mfaService.verifyCode(sessionToken, code);

    if (result.success) {
      // Set MFA verified cookie
      const response = NextResponse.json(result);
      response.cookies.set('mfa_verified', 'true', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 12 * 60 * 60, // 12 hours
      });
      return response;
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('MFA verify error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to verify MFA code' },
      { status: 500 }
    );
  }
}
