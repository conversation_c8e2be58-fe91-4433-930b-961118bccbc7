import { createClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';
import { MFAService } from '@/lib/services/mfa-service';

export async function POST(request: Request) {
  try {
    console.log('[MFA_VERIFY_API] Received request to /api/auth/mfa/verify.');
    const { sessionToken, code } = await request.json();
    console.log(`[MFA_VERIFY_API] Request details - SessionToken provided: ${!!sessionToken}, Code provided: ${!!code}`);
    
    if (!sessionToken || !code) {
      console.warn('[MFA_VERIFY_API_WARN] Missing required fields: sessionToken or code.');
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createClient();
    const mfaService = new MFAService(supabase);
    
    console.log(`[MFA_VERIFY_API] Attempting to verify MFA code for session token (length: ${sessionToken?.length}).`);
    const result = await mfaService.verifyCode(sessionToken, code);

    if (result.success) {
      console.log(`[MFA_VERIFY_API_SUCCESS] MFA code verified successfully for session token (length: ${sessionToken?.length}). Setting mfa_verified cookie.`);
      // Set MFA verified cookie
      const response = NextResponse.json(result); // result already contains success: true here
      response.cookies.set('mfa_verified', 'true', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 12 * 60 * 60, // 12 hours
      });
      return response;
    } else {
      // If verification failed, log the entire result object to understand its structure for errors.
      console.warn(`[MFA_VERIFY_API_FAIL] MFA code verification failed for session token (length: ${sessionToken?.length}). Result:`, JSON.stringify(result, null, 2));
    }

    return NextResponse.json(result);
  } catch (error) {
    let errorMessage = 'Failed to verify MFA code';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    console.error('[MFA_VERIFY_API_ERROR] Unexpected error during MFA verification:', errorMessage, error);
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
