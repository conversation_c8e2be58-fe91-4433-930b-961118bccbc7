/**
 * Response Optimization Middleware
 * Provides compression, caching, and response time optimization
 */

import { NextRequest, NextResponse } from 'next/server'
import { metrics } from '@/lib/monitoring/metrics'
import { getCacheService } from '@/lib/cache/cache-service'

interface CompressionOptions {
  threshold: number // Minimum size to compress (bytes)
  level: number // Compression level (1-9)
  types: string[] // MIME types to compress
}

interface CacheHeaders {
  maxAge: number
  staleWhileRevalidate?: number
  mustRevalidate?: boolean
  noCache?: boolean
  private?: boolean
}

interface OptimizationConfig {
  compression: CompressionOptions
  caching: {
    enabled: boolean
    defaultTTL: number
    varyHeaders: string[]
  }
  monitoring: {
    enabled: boolean
    slowResponseThreshold: number
  }
}

class ResponseOptimizer {
  private config: OptimizationConfig
  private cache = getCacheService()

  constructor(config?: Partial<OptimizationConfig>) {
    this.config = {
      compression: {
        threshold: 1024, // 1KB
        level: 6,
        types: [
          'application/json',
          'text/html',
          'text/css',
          'text/javascript',
          'application/javascript',
          'text/xml',
          'application/xml',
          'text/plain'
        ]
      },
      caching: {
        enabled: true,
        defaultTTL: 300, // 5 minutes
        varyHeaders: ['Accept-Encoding', 'User-Agent']
      },
      monitoring: {
        enabled: true,
        slowResponseThreshold: 1000 // 1 second
      },
      ...config
    }
  }

  /**
   * Optimize API response with caching and compression
   */
  async optimizeResponse(
    request: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse>,
    options: {
      cache?: boolean
      cacheTTL?: number
      cacheKey?: string
      compress?: boolean
      headers?: CacheHeaders
    } = {}
  ): Promise<NextResponse> {
    const startTime = Date.now()
    const {
      cache = this.config.caching.enabled,
      cacheTTL = this.config.caching.defaultTTL,
      cacheKey,
      compress = true,
      headers
    } = options

    try {
      // Generate cache key if not provided
      const finalCacheKey = cacheKey || this.generateCacheKey(request)

      // Try to serve from cache
      if (cache && this.isCacheable(request)) {
        const cachedResponse = await this.getCachedResponse(finalCacheKey)
        if (cachedResponse) {
          const duration = Date.now() - startTime
          metrics.timing('api.response_time', duration)
          metrics.increment('api.cache_hits')
          
          return this.addCacheHeaders(cachedResponse, { 
            ...headers, 
            maxAge: cacheTTL 
          })
        }
      }

      // Execute handler
      const response = await handler(request)
      const duration = Date.now() - startTime

      // Track metrics
      this.trackResponseMetrics(request, response, duration)

      // Optimize response
      let optimizedResponse = response

      // Apply compression if enabled
      if (compress && this.shouldCompress(response)) {
        optimizedResponse = await this.compressResponse(optimizedResponse)
      }

      // Add performance headers
      optimizedResponse = this.addPerformanceHeaders(optimizedResponse, duration)

      // Add cache headers
      if (headers) {
        optimizedResponse = this.addCacheHeaders(optimizedResponse, headers)
      }

      // Cache successful responses
      if (cache && this.isCacheable(request) && response.ok) {
        await this.cacheResponse(finalCacheKey, optimizedResponse, cacheTTL)
      }

      return optimizedResponse

    } catch (error) {
      const duration = Date.now() - startTime
      metrics.timing('api.response_time', duration)
      metrics.increment('api.errors')
      
      console.error('Response optimization error:', error)
      throw error
    }
  }

  /**
   * Create optimized JSON response
   */
  createOptimizedJsonResponse(
    data: any,
    options: {
      status?: number
      headers?: Record<string, string>
      cache?: CacheHeaders
      compress?: boolean
    } = {}
  ): NextResponse {
    const {
      status = 200,
      headers = {},
      cache,
      compress = true
    } = options

    // Optimize data serialization
    const optimizedData = this.optimizeJsonData(data)
    const response = NextResponse.json(optimizedData, {
      status,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        ...headers
      }
    })

    // Add cache headers if specified
    if (cache) {
      return this.addCacheHeaders(response, cache)
    }

    return response
  }

  /**
   * Create streaming response for large datasets
   */
  createStreamingResponse(
    dataGenerator: AsyncGenerator<any>,
    options: {
      headers?: Record<string, string>
      compress?: boolean
    } = {}
  ): NextResponse {
    const { headers = {}, compress = true } = options

    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder()
        
        try {
          controller.enqueue(encoder.encode('{"data":['))
          
          let first = true
          for await (const item of dataGenerator) {
            if (!first) {
              controller.enqueue(encoder.encode(','))
            }
            controller.enqueue(encoder.encode(JSON.stringify(item)))
            first = false
          }
          
          controller.enqueue(encoder.encode(']}'))
          controller.close()
        } catch (error) {
          controller.error(error)
        }
      }
    })

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'application/json',
        'Transfer-Encoding': 'chunked',
        ...headers
      }
    })
  }

  /**
   * Middleware for automatic response optimization
   */
  middleware() {
    return async (request: NextRequest, handler: (req: NextRequest) => Promise<NextResponse>) => {
      // Skip optimization for certain paths
      if (this.shouldSkipOptimization(request)) {
        return handler(request)
      }

      // Determine optimization options based on path
      const options = this.getOptimizationOptions(request)
      
      return this.optimizeResponse(request, handler, options)
    }
  }

  // Private helper methods
  private generateCacheKey(request: NextRequest): string {
    const url = new URL(request.url)
    const method = request.method
    const acceptEncoding = request.headers.get('accept-encoding') || ''
    
    // Include relevant headers in cache key
    const keyParts = [
      method,
      url.pathname,
      url.search,
      acceptEncoding
    ]
    
    return `api_response:${keyParts.join(':')}`
  }

  private isCacheable(request: NextRequest): boolean {
    const method = request.method
    const url = new URL(request.url)
    
    // Only cache GET requests
    if (method !== 'GET') return false
    
    // Don't cache admin or user-specific endpoints
    if (url.pathname.includes('/admin/') || url.pathname.includes('/user/')) {
      return false
    }
    
    // Don't cache real-time endpoints
    if (url.pathname.includes('/realtime') || url.pathname.includes('/stream')) {
      return false
    }
    
    return true
  }

  private async getCachedResponse(cacheKey: string): Promise<NextResponse | null> {
    try {
      const cached = await this.cache.get<{
        body: string
        headers: Record<string, string>
        status: number
      }>(cacheKey)
      
      if (cached) {
        return new NextResponse(cached.body, {
          status: cached.status,
          headers: {
            ...cached.headers,
            'X-Cache': 'HIT'
          }
        })
      }
      
      return null
    } catch (error) {
      console.error('Cache retrieval error:', error)
      return null
    }
  }

  private async cacheResponse(
    cacheKey: string,
    response: NextResponse,
    ttl: number
  ): Promise<void> {
    try {
      const body = await response.text()
      const headers: Record<string, string> = {}
      
      response.headers.forEach((value, key) => {
        headers[key] = value
      })
      
      await this.cache.set(cacheKey, {
        body,
        headers,
        status: response.status
      }, {
        ttl,
        tags: ['api_responses']
      })
    } catch (error) {
      console.error('Cache storage error:', error)
    }
  }

  private shouldCompress(response: NextResponse): boolean {
    const contentType = response.headers.get('content-type') || ''
    const contentLength = parseInt(response.headers.get('content-length') || '0')
    
    return (
      this.config.compression.types.some(type => contentType.includes(type)) &&
      contentLength >= this.config.compression.threshold
    )
  }

  private async compressResponse(response: NextResponse): Promise<NextResponse> {
    // In a real implementation, you would use a compression library
    // For now, we'll just add the header to indicate compression intent
    const newHeaders = new Headers(response.headers)
    newHeaders.set('Content-Encoding', 'gzip')
    newHeaders.set('Vary', 'Accept-Encoding')
    
    return new NextResponse(response.body, {
      status: response.status,
      headers: newHeaders
    })
  }

  private addCacheHeaders(response: NextResponse, headers: CacheHeaders): NextResponse {
    const newHeaders = new Headers(response.headers)
    
    if (headers.noCache) {
      newHeaders.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      newHeaders.set('Pragma', 'no-cache')
      newHeaders.set('Expires', '0')
    } else {
      const cacheControl = []
      
      if (headers.private) {
        cacheControl.push('private')
      } else {
        cacheControl.push('public')
      }
      
      cacheControl.push(`max-age=${headers.maxAge}`)
      
      if (headers.staleWhileRevalidate) {
        cacheControl.push(`stale-while-revalidate=${headers.staleWhileRevalidate}`)
      }
      
      if (headers.mustRevalidate) {
        cacheControl.push('must-revalidate')
      }
      
      newHeaders.set('Cache-Control', cacheControl.join(', '))
    }
    
    return new NextResponse(response.body, {
      status: response.status,
      headers: newHeaders
    })
  }

  private addPerformanceHeaders(response: NextResponse, duration: number): NextResponse {
    const newHeaders = new Headers(response.headers)
    newHeaders.set('X-Response-Time', `${duration}ms`)
    newHeaders.set('X-Powered-By', 'ITSync')
    
    return new NextResponse(response.body, {
      status: response.status,
      headers: newHeaders
    })
  }

  private optimizeJsonData(data: any): any {
    // Remove null values and optimize structure
    if (Array.isArray(data)) {
      return data.map(item => this.optimizeJsonData(item))
    }
    
    if (data && typeof data === 'object') {
      const optimized: any = {}
      for (const [key, value] of Object.entries(data)) {
        if (value !== null && value !== undefined) {
          optimized[key] = this.optimizeJsonData(value)
        }
      }
      return optimized
    }
    
    return data
  }

  private trackResponseMetrics(
    request: NextRequest,
    response: NextResponse,
    duration: number
  ): void {
    const url = new URL(request.url)
    const method = request.method
    const status = response.status
    
    const tags = {
      method,
      path: url.pathname,
      status: status.toString()
    }
    
    metrics.timing('api.response_time', duration, tags)
    metrics.increment('api.requests', tags)
    
    if (status >= 400) {
      metrics.increment('api.errors', tags)
    }
    
    if (duration > this.config.monitoring.slowResponseThreshold) {
      metrics.increment('api.slow_responses', tags)
    }
  }

  private shouldSkipOptimization(request: NextRequest): boolean {
    const url = new URL(request.url)
    
    // Skip for health checks
    if (url.pathname.includes('/health')) return true
    
    // Skip for WebSocket upgrades
    if (request.headers.get('upgrade') === 'websocket') return true
    
    // Skip for streaming endpoints
    if (url.pathname.includes('/stream')) return true
    
    return false
  }

  private getOptimizationOptions(request: NextRequest): any {
    const url = new URL(request.url)
    
    // Static content - long cache
    if (url.pathname.includes('/static/') || url.pathname.includes('/_next/')) {
      return {
        cache: true,
        cacheTTL: 86400, // 24 hours
        headers: { maxAge: 86400, staleWhileRevalidate: 604800 } // 7 days SWR
      }
    }
    
    // API endpoints - short cache
    if (url.pathname.startsWith('/api/')) {
      return {
        cache: true,
        cacheTTL: 300, // 5 minutes
        headers: { maxAge: 300, staleWhileRevalidate: 600 } // 10 minutes SWR
      }
    }
    
    // Default options
    return {
      cache: false,
      compress: true
    }
  }
}

// Singleton instance
let responseOptimizer: ResponseOptimizer | null = null

export const getResponseOptimizer = (): ResponseOptimizer => {
  if (!responseOptimizer) {
    responseOptimizer = new ResponseOptimizer()
  }
  return responseOptimizer
}

export { ResponseOptimizer }
export type { CompressionOptions, CacheHeaders, OptimizationConfig }
