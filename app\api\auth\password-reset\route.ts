import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { RateLimiter } from '@/lib/security/rate-limiter';
import { sendPasswordResetEmail } from '@/lib/email/password-reset';
import { auditLogger } from '@/lib/services/audit';

const requestSchema = z.object({
  email: z.string().email(),
});

// Initialize rate limiter: 5 requests per hour per IP
const rateLimiter = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5,
  message: 'Too many password reset requests. Please try again later.',
  keyGenerator: (req: NextRequest) => {
    // Use IP address for rate limiting
    const forwarded = req.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : req.ip || 'unknown';
    return `password-reset:${ip}`;
  },
});

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimiter.check(req);
    if (!rateLimitResult.allowed) {
      await auditLogger.logSecurityEvent({
        eventType: 'RATE_LIMIT_EXCEEDED',
        severity: 'medium',
        userId: null,
        details: {
          endpoint: '/api/auth/password-reset',
          ip: rateLimitResult.key,
          attempts: rateLimitResult.current,
        },
      });

      return NextResponse.json(
        { error: rateLimitResult.message },
        { status: 429 }
      );
    }

    const body = await req.json();
    const validationResult = requestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;
    const supabase = createClient();

    // Check if user exists (but don't reveal this in response)
    const { data: user } = await supabase
      .from('staff')
      .select('id, name_en, name_jp')
      .eq('email', email)
      .single();

    if (user) {
      // Generate reset token
      const { data: resetData, error: resetError } = await supabase.auth
        .resetPasswordForEmail(email, {
          redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
        });

      if (!resetError) {
        // Send email
        await sendPasswordResetEmail({
          email,
          name: user.name_en || user.name_jp,
          resetUrl: resetData.url,
        });

        // Log successful request
        await auditLogger.logSecurityEvent({
          eventType: 'PASSWORD_RESET_REQUESTED',
          severity: 'info',
          userId: user.id,
          details: { email },
        });
      }
    }

    // Always return success to prevent email enumeration
    return NextResponse.json({
      message: 'If an account exists with this email, you will receive a password reset link.',
    });

  } catch (error) {
    console.error('Password reset error:', error);
    
    // Don't expose internal errors
    return NextResponse.json(
      { error: 'An error occurred. Please try again later.' },
      { status: 500 }
    );
  }
}

// Add OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, { status: 200 });
}
