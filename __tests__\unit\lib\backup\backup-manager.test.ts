/**
 * Unit tests for Backup Manager
 * Tests backup creation, restoration, and data integrity
 */

import { BackupManager, defaultBackupConfig } from '@/lib/backup/backup-manager'
import { createClient } from '@/lib/supabase/server'

// Mock Supabase
jest.mock('@/lib/supabase/server')

// Mock crypto for checksum calculation
Object.defineProperty(global, 'crypto', {
  value: {
    subtle: {
      digest: jest.fn().mockResolvedValue(new ArrayBuffer(32))
    }
  }
})

describe('BackupManager', () => {
  let backupManager: BackupManager
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock Supabase client
    mockSupabase = {
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        neq: jest.fn().mockReturnThis(),
        lt: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        single: jest.fn(),
        then: jest.fn()
      }))
    }

    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)

    backupManager = new BackupManager(defaultBackupConfig)
  })

  describe('createBackup', () => {
    it('should create a successful backup', async () => {
      // Mock successful table queries
      const mockTableData = {
        users: [
          { id: '1', email: '<EMAIL>', created_at: '2024-01-01' },
          { id: '2', email: '<EMAIL>', created_at: '2024-01-02' }
        ],
        tickets: [
          { id: '1', title: 'Test Ticket', status: 'open', created_at: '2024-01-01' }
        ]
      }

      // Mock information_schema query to return table names
      mockSupabase.from().select().eq().neq().then
        .mockResolvedValueOnce({
          data: [
            { table_name: 'users' },
            { table_name: 'tickets' }
          ],
          error: null
        })

      // Mock table data queries
      mockSupabase.from().select().then
        .mockResolvedValueOnce({ data: mockTableData.users, error: null })
        .mockResolvedValueOnce({ data: mockTableData.tickets, error: null })

      // Mock backup metadata insertion
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      // Mock backup logs insertion
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      const result = await backupManager.createBackup('Test backup')

      expect(result.status).toBe('completed')
      expect(result.tables_count).toBe(2)
      expect(result.records_count).toBe(3) // 2 users + 1 ticket
      expect(result.checksum).toBeDefined()
      expect(result.size_bytes).toBeGreaterThan(0)
    })

    it('should handle backup failure gracefully', async () => {
      // Mock table query failure
      mockSupabase.from().select().eq().neq().then
        .mockRejectedValue(new Error('Database connection failed'))

      // Mock backup logs insertion
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      const result = await backupManager.createBackup('Failed backup')

      expect(result.status).toBe('failed')
      expect(result.error_message).toBe('Database connection failed')
      expect(result.size_bytes).toBe(0)
      expect(result.tables_count).toBe(0)
      expect(result.records_count).toBe(0)
    })

    it('should exclude configured tables from backup', async () => {
      const configWithExclusions = {
        ...defaultBackupConfig,
        exclude_tables: ['rate_limit_logs', 'backup_logs']
      }

      const backupManagerWithExclusions = new BackupManager(configWithExclusions)

      // Mock information_schema query
      mockSupabase.from().select().eq().neq().then
        .mockResolvedValueOnce({
          data: [
            { table_name: 'users' },
            { table_name: 'rate_limit_logs' }, // Should be excluded
            { table_name: 'tickets' }
          ],
          error: null
        })

      // Mock table data queries (only for non-excluded tables)
      mockSupabase.from().select().then
        .mockResolvedValueOnce({ data: [{ id: '1' }], error: null })
        .mockResolvedValueOnce({ data: [{ id: '1' }], error: null })

      // Mock backup metadata insertion
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      const result = await backupManagerWithExclusions.createBackup()

      expect(result.status).toBe('completed')
      expect(result.tables_count).toBe(2) // Only users and tickets
    })

    it('should calculate correct checksum', async () => {
      // Mock crypto.subtle.digest to return predictable result
      const mockDigest = new Uint8Array([1, 2, 3, 4]).buffer
      ;(crypto.subtle.digest as jest.Mock).mockResolvedValue(mockDigest)

      mockSupabase.from().select().eq().neq().then
        .mockResolvedValueOnce({
          data: [{ table_name: 'users' }],
          error: null
        })

      mockSupabase.from().select().then
        .mockResolvedValueOnce({ data: [{ id: '1' }], error: null })

      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      const result = await backupManager.createBackup()

      expect(result.checksum).toBe('01020304')
      expect(crypto.subtle.digest).toHaveBeenCalledWith('SHA-256', expect.any(Uint8Array))
    })
  })

  describe('restoreFromBackup', () => {
    const mockBackupId = 'backup_123'
    const mockBackupMetadata = {
      id: mockBackupId,
      timestamp: '2024-01-01T00:00:00Z',
      checksum: 'abc123',
      status: 'completed'
    }

    it('should restore backup successfully', async () => {
      // Mock backup metadata retrieval
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockBackupMetadata,
        error: null
      })

      // Mock backup data (would normally come from external storage)
      jest.spyOn(backupManager as any, 'getBackupData').mockResolvedValue({
        users: [{ id: '1', email: '<EMAIL>' }],
        tickets: [{ id: '1', title: 'Test Ticket' }]
      })

      // Mock table deletion and insertion
      mockSupabase.from().delete().neq().then.mockResolvedValue({ error: null })
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      // Mock backup logs insertion
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      const result = await backupManager.restoreFromBackup(mockBackupId)

      expect(result.success).toBe(true)
      expect(result.restored_records).toBe(2)
      expect(result.message).toContain('Restore completed successfully')
    })

    it('should perform dry run without making changes', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockBackupMetadata,
        error: null
      })

      jest.spyOn(backupManager as any, 'getBackupData').mockResolvedValue({
        users: [{ id: '1' }, { id: '2' }],
        tickets: [{ id: '1' }]
      })

      const result = await backupManager.restoreFromBackup(mockBackupId, { dry_run: true })

      expect(result.success).toBe(true)
      expect(result.restored_records).toBe(3)
      expect(result.message).toContain('Dry run completed')
      
      // Verify no actual database modifications were made
      expect(mockSupabase.from().delete).not.toHaveBeenCalled()
      expect(mockSupabase.from().insert).not.toHaveBeenCalled()
    })

    it('should restore only specified tables', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockBackupMetadata,
        error: null
      })

      jest.spyOn(backupManager as any, 'getBackupData').mockResolvedValue({
        users: [{ id: '1' }],
        tickets: [{ id: '1' }],
        forms: [{ id: '1' }]
      })

      mockSupabase.from().delete().neq().then.mockResolvedValue({ error: null })
      mockSupabase.from().insert().then.mockResolvedValue({ error: null })

      const result = await backupManager.restoreFromBackup(mockBackupId, {
        tables: ['users', 'tickets']
      })

      expect(result.success).toBe(true)
      expect(result.restored_records).toBe(2) // Only users and tickets
    })

    it('should verify backup integrity when requested', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockBackupMetadata,
        error: null
      })

      // Mock integrity verification failure
      jest.spyOn(backupManager as any, 'verifyBackupIntegrity').mockResolvedValue(false)

      const result = await backupManager.restoreFromBackup(mockBackupId, {
        verify_checksum: true
      })

      expect(result.success).toBe(false)
      expect(result.message).toContain('Backup integrity check failed')
    })

    it('should handle missing backup gracefully', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: null
      })

      const result = await backupManager.restoreFromBackup('nonexistent_backup')

      expect(result.success).toBe(false)
      expect(result.message).toContain('Backup nonexistent_backup not found')
    })
  })

  describe('listBackups', () => {
    it('should return list of backups', async () => {
      const mockBackups = [
        { id: 'backup_1', timestamp: '2024-01-01', status: 'completed' },
        { id: 'backup_2', timestamp: '2024-01-02', status: 'completed' }
      ]

      mockSupabase.from().select().order().limit().then.mockResolvedValue({
        data: mockBackups,
        error: null
      })

      const result = await backupManager.listBackups()

      expect(result).toEqual(mockBackups)
      expect(mockSupabase.from).toHaveBeenCalledWith('backup_metadata')
      expect(mockSupabase.from().order).toHaveBeenCalledWith('timestamp', { ascending: false })
    })

    it('should handle database errors', async () => {
      mockSupabase.from().select().order().limit().then.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      await expect(backupManager.listBackups()).rejects.toThrow('Failed to list backups: Database error')
    })

    it('should respect limit parameter', async () => {
      mockSupabase.from().select().order().limit().then.mockResolvedValue({
        data: [],
        error: null
      })

      await backupManager.listBackups(10)

      expect(mockSupabase.from().limit).toHaveBeenCalledWith(10)
    })
  })

  describe('verifyBackupIntegrity', () => {
    it('should return true for valid backup', async () => {
      const mockMetadata = { checksum: 'abc123' }
      const mockData = { users: [{ id: '1' }] }

      jest.spyOn(backupManager as any, 'getBackupMetadata').mockResolvedValue(mockMetadata)
      jest.spyOn(backupManager as any, 'getBackupData').mockResolvedValue(mockData)
      jest.spyOn(backupManager as any, 'calculateChecksum').mockResolvedValue('abc123')

      const result = await backupManager.verifyBackupIntegrity('backup_123')

      expect(result).toBe(true)
    })

    it('should return false for corrupted backup', async () => {
      const mockMetadata = { checksum: 'abc123' }
      const mockData = { users: [{ id: '1' }] }

      jest.spyOn(backupManager as any, 'getBackupMetadata').mockResolvedValue(mockMetadata)
      jest.spyOn(backupManager as any, 'getBackupData').mockResolvedValue(mockData)
      jest.spyOn(backupManager as any, 'calculateChecksum').mockResolvedValue('different_checksum')

      const result = await backupManager.verifyBackupIntegrity('backup_123')

      expect(result).toBe(false)
    })

    it('should return false for missing backup', async () => {
      jest.spyOn(backupManager as any, 'getBackupMetadata').mockResolvedValue(null)

      const result = await backupManager.verifyBackupIntegrity('nonexistent_backup')

      expect(result).toBe(false)
    })
  })

  describe('cleanupOldBackups', () => {
    it('should delete old backups based on retention policy', async () => {
      const oldDate = new Date()
      oldDate.setDate(oldDate.getDate() - 35) // 35 days ago (older than 30-day retention)

      const oldBackups = [
        { id: 'old_backup_1', size_bytes: 1000, timestamp: oldDate.toISOString() },
        { id: 'old_backup_2', size_bytes: 2000, timestamp: oldDate.toISOString() }
      ]

      mockSupabase.from().select().lt().then.mockResolvedValue({
        data: oldBackups,
        error: null
      })

      // Mock successful deletion
      jest.spyOn(backupManager as any, 'deleteBackup').mockResolvedValue(undefined)

      const result = await backupManager.cleanupOldBackups()

      expect(result.deleted_count).toBe(2)
      expect(result.freed_bytes).toBe(3000)
    })

    it('should handle deletion errors gracefully', async () => {
      const oldBackups = [
        { id: 'old_backup_1', size_bytes: 1000 }
      ]

      mockSupabase.from().select().lt().then.mockResolvedValue({
        data: oldBackups,
        error: null
      })

      // Mock deletion failure
      jest.spyOn(backupManager as any, 'deleteBackup').mockRejectedValue(new Error('Deletion failed'))
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      const result = await backupManager.cleanupOldBackups()

      expect(result.deleted_count).toBe(0)
      expect(result.freed_bytes).toBe(0)
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to delete backup old_backup_1')
      )

      consoleSpy.mockRestore()
    })
  })

  describe('Configuration', () => {
    it('should use default configuration correctly', () => {
      expect(defaultBackupConfig.enabled).toBe(true)
      expect(defaultBackupConfig.retention_days).toBe(30)
      expect(defaultBackupConfig.encryption_enabled).toBe(true)
      expect(defaultBackupConfig.exclude_tables).toContain('rate_limit_logs')
      expect(defaultBackupConfig.exclude_tables).toContain('backup_logs')
    })

    it('should accept custom configuration', () => {
      const customConfig = {
        enabled: false,
        schedule: '0 3 * * *',
        retention_days: 60,
        encryption_enabled: false,
        compression_enabled: false,
        include_files: true,
        exclude_tables: ['custom_table']
      }

      const customBackupManager = new BackupManager(customConfig)
      expect(customBackupManager).toBeInstanceOf(BackupManager)
    })
  })
})
