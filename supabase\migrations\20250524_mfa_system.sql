-- MFA System Tables Migration
-- This migration adds Multi-Factor Authentication support to ITSync

-- MFA Configuration Table
CREATE TABLE IF NOT EXISTS mfa_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  method VARCHAR(20) NOT NULL CHECK (method IN ('totp', 'sms', 'email')),
  is_primary BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  secret_encrypted TEXT, -- For TOTP, encrypted with Supabase Vault
  phone_number TEXT, -- For SMS, encrypted
  backup_codes TEXT[], -- Encrypted backup codes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, method)
);

-- MFA Sessions Table for temporary verification
CREATE TABLE IF NOT EXISTS mfa_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  challenge_code TEXT, -- Encrypted
  method VARCHAR(20) NOT NULL,
  attempts INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MFA Audit Log for security tracking
CREATE TABLE IF NOT EXISTS mfa_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
    'setup_initiated', 'setup_completed', 'setup_failed',
    'verify_success', 'verify_failed', 'method_changed',
    'disabled', 'backup_used', 'backup_regenerated'
  )),
  method VARCHAR(20),
  success BOOLEAN NOT NULL,
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User MFA settings in profiles
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS mfa_enabled BOOLEAN DEFAULT false;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS mfa_enforced_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS require_mfa_for_role BOOLEAN DEFAULT false;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_mfa_config_user ON mfa_configurations(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_config_verified ON mfa_configurations(user_id, is_verified) WHERE is_verified = true;
CREATE INDEX IF NOT EXISTS idx_mfa_sessions_token ON mfa_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_mfa_sessions_expires ON mfa_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_mfa_audit_user ON mfa_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_audit_created ON mfa_audit_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_mfa_audit_event ON mfa_audit_logs(event_type, created_at DESC);

-- RLS Policies
ALTER TABLE mfa_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_audit_logs ENABLE ROW LEVEL SECURITY;

-- Users can only access their own MFA configurations
CREATE POLICY "Users can view own MFA config" ON mfa_configurations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own MFA config" ON mfa_configurations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own MFA config" ON mfa_configurations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own MFA config" ON mfa_configurations
  FOR DELETE USING (auth.uid() = user_id);

-- MFA sessions are user-specific
CREATE POLICY "Users can access own MFA sessions" ON mfa_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Audit logs are read-only for users, full access for admins
CREATE POLICY "Users can view own audit logs" ON mfa_audit_logs
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN roles r ON p.role_id = r.id
      WHERE p.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Function to enforce MFA for specific roles
CREATE OR REPLACE FUNCTION enforce_mfa_for_role()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if user's role requires MFA
  IF EXISTS (
    SELECT 1 FROM roles r
    WHERE r.id = NEW.role_id
    AND r.name IN ('Global Administrator', 'Web App System Administrator', 'Department Administrator', 'IT Helpdesk Support')
  ) THEN
    NEW.require_mfa_for_role := true;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to enforce MFA for sensitive roles
CREATE TRIGGER enforce_mfa_for_sensitive_roles
  BEFORE INSERT OR UPDATE OF role_id ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION enforce_mfa_for_role();

-- Function to clean expired MFA sessions
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_sessions()
RETURNS void AS $$
BEGIN
  DELETE FROM mfa_sessions WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE mfa_configurations IS 'Stores MFA methods configured for each user';
COMMENT ON TABLE mfa_sessions IS 'Temporary MFA challenge sessions';
COMMENT ON TABLE mfa_audit_logs IS 'Audit trail for all MFA-related events';
COMMENT ON COLUMN mfa_configurations.secret_encrypted IS 'TOTP secret encrypted using Supabase Vault';
COMMENT ON COLUMN mfa_configurations.backup_codes IS 'Array of encrypted backup codes';
COMMENT ON COLUMN profiles.require_mfa_for_role IS 'Whether the user role requires MFA enforcement';
