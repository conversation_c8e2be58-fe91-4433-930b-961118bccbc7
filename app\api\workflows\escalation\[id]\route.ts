import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { EscalationRulesManager } from '@/lib/services/workflow/escalation/escalation-rules-manager';
import { auditService } from '@/lib/services/audit';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const rulesManager = new EscalationRulesManager(supabase, auditService);

    const rule = await rulesManager.getRule(params.id);
    if (!rule) {
      return NextResponse.json({ error: 'Rule not found' }, { status: 404 });
    }

    return NextResponse.json({ rule });
  } catch (error) {
    console.error('Error fetching escalation rule:', error);
    return NextResponse.json(
      { error: 'Failed to fetch escalation rule' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: staff } = await supabase
      .from('staff')
      .select('*, roles(*)')
      .eq('auth_id', user.id)
      .single();

    if (!staff || !['Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support'].includes(staff.roles.name)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const rulesManager = new EscalationRulesManager(supabase, auditService);

    const rule = await rulesManager.updateRule(params.id, body, staff.id);

    return NextResponse.json({ rule });
  } catch (error) {
    console.error('Error updating escalation rule:', error);
    return NextResponse.json(
      { error: 'Failed to update escalation rule' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: staff } = await supabase
      .from('staff')
      .select('*, roles(*)')
      .eq('auth_id', user.id)
      .single();

    if (!staff || !['Global Administrator', 'Web App System Administrator'].includes(staff.roles.name)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const rulesManager = new EscalationRulesManager(supabase, auditService);

    await rulesManager.deleteRule(params.id, staff.id);

    return NextResponse.json({ message: 'Rule deleted successfully' });
  } catch (error) {
    console.error('Error deleting escalation rule:', error);
    return NextResponse.json(
      { error: 'Failed to delete escalation rule' },
      { status: 500 }
    );
  }
}
