"use strict";(()=>{var e={};e.id=9446,e.ids=[9446],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},98207:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var a={};s.r(a),s.d(a,{OPTIONS:()=>c,POST:()=>m});var r=s(42706),o=s(28203),n=s(45994),i=s(39187),d=s(61487),l=s(98837);class u{constructor(e){this.options={windowMs:e.windowMs,max:e.max,message:e.message||"Too many requests, please try again later.",keyGenerator:e.keyGenerator||(e=>e.ip||"unknown"),skipSuccessfulRequests:e.skipSuccessfulRequests||!1,skipFailedRequests:e.skipFailedRequests||!1}}async check(e){let t=this.options.keyGenerator(e),s=(0,d.U)(),a=new Date,r=new Date(a.getTime()-this.options.windowMs),{data:o,error:n}=await s.from("rate_limit_logs").select("id").eq("key",t).gte("created_at",r.toISOString()).order("created_at",{ascending:!1}),i=o?.length||0;if(i>=this.options.max){let e=o?.[o.length-1],s=new Date(e?new Date(e.created_at).getTime()+this.options.windowMs:a.getTime()+this.options.windowMs);return{allowed:!1,current:i,max:this.options.max,message:this.options.message,resetAt:s,key:t}}await s.from("rate_limit_logs").insert({key:t,endpoint:e.url,method:e.method,ip:e.ip});let l=new Date(a.getTime()-864e5);return await s.from("rate_limit_logs").delete().lt("created_at",l.toISOString()),{allowed:!0,current:i+1,max:this.options.max,message:"Request allowed",resetAt:new Date(a.getTime()+this.options.windowMs),key:t}}async reset(e){let t=(0,d.U)();await t.from("rate_limit_logs").delete().eq("key",e)}}new u({windowMs:9e5,max:5,message:"Too many authentication attempts. Please try again later."}),new u({windowMs:6e4,max:60,message:"API rate limit exceeded. Please slow down."}),new u({windowMs:36e5,max:100,message:"Upload limit exceeded. Please try again later."}),new u({windowMs:6e4,max:10,message:"AI service rate limit exceeded. Please try again later."}),new u({windowMs:6e4,max:30,message:"AI validation rate limit exceeded. Please slow down."}),new u({windowMs:9e5,max:3,message:"Too many MFA attempts. Please try again later."}),new u({windowMs:36e5,max:3,message:"Too many password reset attempts. Please try again later."}),function(){var e=Error("Cannot find module '@/lib/email/password-reset'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();let p=l.z.object({email:l.z.string().email()}),w=new u({windowMs:36e5,max:5,message:"Too many password reset requests. Please try again later.",keyGenerator:e=>{let t=e.headers.get("x-forwarded-for"),s=t?t.split(",")[0]:e.ip||"unknown";return`password-reset:${s}`}});async function m(e){try{let t=await w.check(e);if(!t.allowed)return await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).logSecurityEvent({eventType:"RATE_LIMIT_EXCEEDED",severity:"medium",userId:null,details:{endpoint:"/api/auth/password-reset",ip:t.key,attempts:t.current}}),i.NextResponse.json({error:t.message},{status:429});let s=await e.json(),a=p.safeParse(s);if(!a.success)return i.NextResponse.json({error:"Invalid email address"},{status:400});let{email:r}=a.data,o=(0,d.U)(),{data:n}=await o.from("staff").select("id, name_en, name_jp").eq("email",r).single();if(n){let{data:e,error:t}=await o.auth.resetPasswordForEmail(r,{redirectTo:"http://localhost:3000/auth/reset-password"});t||(await Object(function(){var e=Error("Cannot find module '@/lib/email/password-reset'");throw e.code="MODULE_NOT_FOUND",e}())({email:r,name:n.name_en||n.name_jp,resetUrl:e.url}),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).logSecurityEvent({eventType:"PASSWORD_RESET_REQUESTED",severity:"info",userId:n.id,details:{email:r}}))}return i.NextResponse.json({message:"If an account exists with this email, you will receive a password reset link."})}catch(e){return console.error("Password reset error:",e),i.NextResponse.json({error:"An error occurred. Please try again later."},{status:500})}}async function c(){return new i.NextResponse(null,{status:200})}let g=new r.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/password-reset/route",pathname:"/api/auth/password-reset",filename:"route",bundlePath:"app/api/auth/password-reset/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\password-reset\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:y}=g;function f(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>s(98207));module.exports=a})();