(()=>{var e={};e.id=9446,e.ids=[9446],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},34822:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>_,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var r={};s.r(r),s.d(r,{OPTIONS:()=>m,POST:()=>w});var a=s(42706),o=s(28203),i=s(45994),n=s(39187),l=s(61487),d=s(98837);class u{constructor(e){this.options={windowMs:e.windowMs,max:e.max,message:e.message||"Too many requests, please try again later.",keyGenerator:e.keyGenerator||(e=>e.ip||"unknown"),skipSuccessfulRequests:e.skipSuccessfulRequests||!1,skipFailedRequests:e.skipFailedRequests||!1}}async check(e){let t=this.options.keyGenerator(e),s=(0,l.U)(),r=new Date,a=new Date(r.getTime()-this.options.windowMs),{data:o,error:i}=await s.from("rate_limit_logs").select("id").eq("key",t).gte("created_at",a.toISOString()).order("created_at",{ascending:!1}),n=o?.length||0;if(n>=this.options.max){let e=o?.[o.length-1],s=new Date(e?new Date(e.created_at).getTime()+this.options.windowMs:r.getTime()+this.options.windowMs);return{allowed:!1,current:n,max:this.options.max,message:this.options.message,resetAt:s,key:t}}await s.from("rate_limit_logs").insert({key:t,endpoint:e.url,method:e.method,ip:e.ip});let d=new Date(r.getTime()-864e5);return await s.from("rate_limit_logs").delete().lt("created_at",d.toISOString()),{allowed:!0,current:n+1,max:this.options.max,message:"Request allowed",resetAt:new Date(r.getTime()+this.options.windowMs),key:t}}async reset(e){let t=(0,l.U)();await t.from("rate_limit_logs").delete().eq("key",e)}}new u({windowMs:9e5,max:5,message:"Too many authentication attempts. Please try again later."}),new u({windowMs:6e4,max:60,message:"API rate limit exceeded. Please slow down."}),new u({windowMs:36e5,max:100,message:"Upload limit exceeded. Please try again later."}),new u({windowMs:6e4,max:10,message:"AI service rate limit exceeded. Please try again later."}),new u({windowMs:6e4,max:30,message:"AI validation rate limit exceeded. Please slow down."}),new u({windowMs:9e5,max:3,message:"Too many MFA attempts. Please try again later."}),new u({windowMs:36e5,max:3,message:"Too many password reset attempts. Please try again later."}),function(){var e=Error("Cannot find module '@/lib/email/password-reset'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();let p=d.z.object({email:d.z.string().email()}),c=new u({windowMs:36e5,max:5,message:"Too many password reset requests. Please try again later.",keyGenerator:e=>{let t=e.headers.get("x-forwarded-for"),s=t?t.split(",")[0]:e.ip||"unknown";return`password-reset:${s}`}});async function w(e){try{let t=await c.check(e);if(!t.allowed)return await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).logSecurityEvent({eventType:"RATE_LIMIT_EXCEEDED",severity:"medium",userId:null,details:{endpoint:"/api/auth/password-reset",ip:t.key,attempts:t.current}}),n.NextResponse.json({error:t.message},{status:429});let s=await e.json(),r=p.safeParse(s);if(!r.success)return n.NextResponse.json({error:"Invalid email address"},{status:400});let{email:a}=r.data,o=(0,l.U)(),{data:i}=await o.from("staff").select("id, name_en, name_jp").eq("email",a).single();if(i){let{data:e,error:t}=await o.auth.resetPasswordForEmail(a,{redirectTo:"http://localhost:3000/auth/reset-password"});t||(await Object(function(){var e=Error("Cannot find module '@/lib/email/password-reset'");throw e.code="MODULE_NOT_FOUND",e}())({email:a,name:i.name_en||i.name_jp,resetUrl:e.url}),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).logSecurityEvent({eventType:"PASSWORD_RESET_REQUESTED",severity:"info",userId:i.id,details:{email:a}}))}return n.NextResponse.json({message:"If an account exists with this email, you will receive a password reset link."})}catch(e){return console.error("Password reset error:",e),n.NextResponse.json({error:"An error occurred. Please try again later."},{status:500})}}async function m(){return new n.NextResponse(null,{status:200})}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/password-reset/route",pathname:"/api/auth/password-reset",filename:"route",bundlePath:"app/api/auth/password-reset/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\password-reset\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:y}=g;function _(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},61487:(e,t,s)=>{"use strict";s.d(t,{U:()=>o});var r=s(49064),a=s(44512);let o=()=>{let e=(0,a.UL)();return(0,r.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:s,options:r})=>e.set(t,s,r))}catch{}}}})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5994,5452,4512,9064,8837],()=>s(34822));module.exports=r})();