(()=>{var e={};e.id=9446,e.ids=[9446],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},52968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>P,routeModule:()=>_,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>k});var r={};s.r(r),s.d(r,{OPTIONS:()=>h,POST:()=>y});var a=s(42706),o=s(28203),i=s(45994),n=s(39187),l=s(61487),d=s(98837);class c{constructor(e){this.options={windowMs:e.windowMs,max:e.max,message:e.message||"Too many requests, please try again later.",keyGenerator:e.keyGenerator||(e=>e.ip||"unknown"),skipSuccessfulRequests:e.skipSuccessfulRequests||!1,skipFailedRequests:e.skipFailedRequests||!1}}async check(e){let t=this.options.keyGenerator(e),s=(0,l.U)(),r=new Date,a=new Date(r.getTime()-this.options.windowMs),{data:o,error:i}=await s.from("rate_limit_logs").select("id").eq("key",t).gte("created_at",a.toISOString()).order("created_at",{ascending:!1}),n=o?.length||0;if(n>=this.options.max){let e=o?.[o.length-1],s=new Date(e?new Date(e.created_at).getTime()+this.options.windowMs:r.getTime()+this.options.windowMs);return{allowed:!1,current:n,max:this.options.max,message:this.options.message,resetAt:s,key:t}}await s.from("rate_limit_logs").insert({key:t,endpoint:e.url,method:e.method,ip:e.ip});let d=new Date(r.getTime()-864e5);return await s.from("rate_limit_logs").delete().lt("created_at",d.toISOString()),{allowed:!0,current:n+1,max:this.options.max,message:"Request allowed",resetAt:new Date(r.getTime()+this.options.windowMs),key:t}}async reset(e){let t=(0,l.U)();await t.from("rate_limit_logs").delete().eq("key",e)}}new c({windowMs:9e5,max:5,message:"Too many authentication attempts. Please try again later."}),new c({windowMs:6e4,max:60,message:"API rate limit exceeded. Please slow down."}),new c({windowMs:36e5,max:100,message:"Upload limit exceeded. Please try again later."}),new c({windowMs:6e4,max:10,message:"AI service rate limit exceeded. Please try again later."}),new c({windowMs:6e4,max:30,message:"AI validation rate limit exceeded. Please slow down."}),new c({windowMs:9e5,max:3,message:"Too many MFA attempts. Please try again later."}),new c({windowMs:36e5,max:3,message:"Too many password reset attempts. Please try again later."});class u{async sendPasswordResetEmail(e){try{return console.log("Sending password reset email to:",e.email),console.log("Reset token:",e.resetToken),await new Promise(e=>setTimeout(e,1e3)),{success:!0,messageId:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}catch(e){return console.error("Failed to send password reset email:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async sendPasswordResetConfirmation(e){try{return console.log("Sending password reset confirmation to:",e),await new Promise(e=>setTimeout(e,500)),{success:!0,messageId:`conf_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}catch(e){return console.error("Failed to send password reset confirmation:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}generateResetUrl(e){return`http://localhost:3000/auth/reset-password?token=${e}`}}let w=new u;var p=s(68967);let m=d.z.object({email:d.z.string().email()}),g=new c({windowMs:36e5,max:5,message:"Too many password reset requests. Please try again later.",keyGenerator:e=>{let t=e.headers.get("x-forwarded-for"),s=t?t.split(",")[0]:e.ip||"unknown";return`password-reset:${s}`}});async function y(e){try{let t=await g.check(e);if(!t.allowed)return await p.H.logAction({user_id:"system",action:"rate_limit_exceeded",resource_type:"auth",details:{endpoint:"/api/auth/password-reset",ip:t.key,attempts:t.current}}),n.NextResponse.json({error:t.message},{status:429});let s=await e.json(),r=m.safeParse(s);if(!r.success)return n.NextResponse.json({error:"Invalid email address"},{status:400});let{email:a}=r.data,o=(0,l.U)(),{data:i}=await o.from("staff").select("id, name_en, name_jp").eq("email",a).single();if(i){let{data:e,error:t}=await o.auth.resetPasswordForEmail(a,{redirectTo:"http://localhost:3000/auth/reset-password"});t||(await w.sendPasswordResetEmail({email:a,resetToken:"token",userName:i.name_en||i.name_jp}),await p.H.logPasswordReset(i.id))}return n.NextResponse.json({message:"If an account exists with this email, you will receive a password reset link."})}catch(e){return console.error("Password reset error:",e),n.NextResponse.json({error:"An error occurred. Please try again later."},{status:500})}}async function h(){return new n.NextResponse(null,{status:200})}let _=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/password-reset/route",pathname:"/api/auth/password-reset",filename:"route",bundlePath:"app/api/auth/password-reset/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\password-reset\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:k,serverHooks:f}=_;function P(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:k})}},96487:()=>{},78335:()=>{},68967:(e,t,s)=>{"use strict";s.d(t,{H:()=>a});class r{async logAction(e){try{console.log("Audit log entry:",{...e,timestamp:e.timestamp||new Date}),await new Promise(e=>setTimeout(e,100))}catch(e){console.error("Failed to log audit entry:",e)}}async getAuditLogs(e={}){try{return await new Promise(e=>setTimeout(e,200)),[{id:"1",user_id:"user1",action:"login",resource_type:"auth",timestamp:new Date,ip_address:"***********"}]}catch(e){return console.error("Failed to fetch audit logs:",e),[]}}async getUserActivity(e,t=50){return this.getAuditLogs({userId:e,limit:t})}async getResourceActivity(e,t){return this.getAuditLogs({resourceType:e})}async logLogin(e,t,s){await this.logAction({user_id:e,action:"login",resource_type:"auth",ip_address:t,user_agent:s})}async logLogout(e,t){await this.logAction({user_id:e,action:"logout",resource_type:"auth",ip_address:t})}async logPasswordReset(e,t){await this.logAction({user_id:e,action:"password_reset",resource_type:"auth",ip_address:t})}async logTicketCreated(e,t){await this.logAction({user_id:e,action:"create",resource_type:"ticket",resource_id:t})}async logTicketUpdated(e,t,s){await this.logAction({user_id:e,action:"update",resource_type:"ticket",resource_id:t,details:s})}async logWorkflowAction(e,t,s,r){await this.logAction({user_id:e,action:s,resource_type:"workflow",resource_id:t,details:r})}}let a=new r},61487:(e,t,s)=>{"use strict";s.d(t,{U:()=>o});var r=s(49064),a=s(44512);let o=()=>{let e=(0,a.UL)();return(0,r.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:s,options:r})=>e.set(t,s,r))}catch{}}}})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5994,5452,4512,9064,8837],()=>s(52968));module.exports=r})();