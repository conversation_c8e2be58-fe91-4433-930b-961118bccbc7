"use strict";(()=>{var e={};e.id=9446,e.ids=[9446],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},98207:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>O,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{OPTIONS:()=>m,POST:()=>p});var s=r(42706),o=r(28203),n=r(45994),i=r(39187),d=r(59913);!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();class l{constructor(e){this.options={windowMs:e.windowMs,max:e.max,message:e.message||"Too many requests, please try again later.",keyGenerator:e.keyGenerator||(e=>e.ip||"unknown"),skipSuccessfulRequests:e.skipSuccessfulRequests||!1,skipFailedRequests:e.skipFailedRequests||!1}}async check(e){let t=this.options.keyGenerator(e),r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),a=new Date,s=new Date(a.getTime()-this.options.windowMs),{data:o,error:n}=await r.from("rate_limit_logs").select("id").eq("key",t).gte("created_at",s.toISOString()).order("created_at",{ascending:!1}),i=o?.length||0;if(i>=this.options.max){let e=o?.[o.length-1],r=new Date(e?new Date(e.created_at).getTime()+this.options.windowMs:a.getTime()+this.options.windowMs);return{allowed:!1,current:i,max:this.options.max,message:this.options.message,resetAt:r,key:t}}await r.from("rate_limit_logs").insert({key:t,endpoint:e.url,method:e.method,ip:e.ip});let d=new Date(a.getTime()-864e5);return await r.from("rate_limit_logs").delete().lt("created_at",d.toISOString()),{allowed:!0,current:i+1,max:this.options.max,message:"Request allowed",resetAt:new Date(a.getTime()+this.options.windowMs),key:t}}async reset(e){let t=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())();await t.from("rate_limit_logs").delete().eq("key",e)}}new l({windowMs:9e5,max:5,message:"Too many authentication attempts. Please try again later."}),new l({windowMs:6e4,max:60,message:"API rate limit exceeded. Please slow down."}),new l({windowMs:36e5,max:100,message:"Upload limit exceeded. Please try again later."}),new l({windowMs:6e4,max:10,message:"AI service rate limit exceeded. Please try again later."}),new l({windowMs:6e4,max:30,message:"AI validation rate limit exceeded. Please slow down."}),new l({windowMs:9e5,max:3,message:"Too many MFA attempts. Please try again later."}),new l({windowMs:36e5,max:3,message:"Too many password reset attempts. Please try again later."}),function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/email/password-reset'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();let u=d.z.object({email:d.z.string().email()}),w=new l({windowMs:36e5,max:5,message:"Too many password reset requests. Please try again later.",keyGenerator:e=>{let t=e.headers.get("x-forwarded-for"),r=t?t.split(",")[0]:e.ip||"unknown";return`password-reset:${r}`}});async function p(e){try{let t=await w.check(e);if(!t.allowed)return await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).logSecurityEvent({eventType:"RATE_LIMIT_EXCEEDED",severity:"medium",userId:null,details:{endpoint:"/api/auth/password-reset",ip:t.key,attempts:t.current}}),i.NextResponse.json({error:t.message},{status:429});let r=await e.json(),a=u.safeParse(r);if(!a.success)return i.NextResponse.json({error:"Invalid email address"},{status:400});let{email:s}=a.data,o=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:n}=await o.from("staff").select("id, name_en, name_jp").eq("email",s).single();if(n){let{data:e,error:t}=await o.auth.resetPasswordForEmail(s,{redirectTo:"http://localhost:3000/auth/reset-password"});t||(await Object(function(){var e=Error("Cannot find module '@/lib/email/password-reset'");throw e.code="MODULE_NOT_FOUND",e}())({email:s,name:n.name_en||n.name_jp,resetUrl:e.url}),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).logSecurityEvent({eventType:"PASSWORD_RESET_REQUESTED",severity:"info",userId:n.id,details:{email:s}}))}return i.NextResponse.json({message:"If an account exists with this email, you will receive a password reset link."})}catch(e){return console.error("Password reset error:",e),i.NextResponse.json({error:"An error occurred. Please try again later."},{status:500})}}async function m(){return new i.NextResponse(null,{status:200})}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/password-reset/route",pathname:"/api/auth/password-reset",filename:"route",bundlePath:"app/api/auth/password-reset/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\password-reset\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:f}=c;function O(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>r(98207));module.exports=a})();