/**
 * AI Usage Tracker
 * Comprehensive tracking of AI API usage, costs, and quotas
 */

import { createClient } from '@/lib/supabase/server'
import { metrics } from '@/lib/monitoring/metrics'
import { getCacheService } from '@/lib/cache/cache-service'

interface UsageRecord {
  id: string
  user_id: string
  organization_id: string
  provider: 'openai' | 'anthropic' | 'google' | 'azure'
  model: string
  feature: string
  request_type: 'chat' | 'completion' | 'embedding' | 'image' | 'audio'
  input_tokens: number
  output_tokens: number
  total_tokens: number
  estimated_cost: number
  actual_cost?: number
  request_duration: number
  success: boolean
  error_type?: string
  metadata: any
  created_at: string
}

interface UsageQuota {
  id: string
  entity_type: 'user' | 'organization' | 'global'
  entity_id: string
  period: 'daily' | 'weekly' | 'monthly'
  quota_type: 'requests' | 'tokens' | 'cost'
  limit_value: number
  current_usage: number
  reset_at: string
  created_at: string
  updated_at: string
}

interface BudgetAlert {
  id: string
  entity_type: 'user' | 'organization' | 'global'
  entity_id: string
  alert_type: 'warning' | 'limit' | 'exceeded'
  threshold_percentage: number
  current_percentage: number
  period: 'daily' | 'weekly' | 'monthly'
  notification_sent: boolean
  created_at: string
}

interface UsageStats {
  totalRequests: number
  totalTokens: number
  totalCost: number
  successRate: number
  avgResponseTime: number
  topModels: Array<{ model: string; usage: number; cost: number }>
  topFeatures: Array<{ feature: string; usage: number; cost: number }>
  costTrend: Array<{ date: string; cost: number }>
}

class AIUsageTracker {
  private supabase = createClient()
  private cache = getCacheService()
  private readonly cachePrefix = 'ai_usage:'
  private readonly quotaCachePrefix = 'ai_quota:'

  /**
   * Record AI usage with comprehensive tracking
   */
  async recordUsage(
    userId: string,
    organizationId: string,
    provider: string,
    model: string,
    feature: string,
    requestType: string,
    inputTokens: number,
    outputTokens: number,
    cost: number,
    duration: number,
    success: boolean,
    errorType?: string,
    metadata: any = {}
  ): Promise<void> {
    const usageRecord: Partial<UsageRecord> = {
      user_id: userId,
      organization_id: organizationId,
      provider: provider as any,
      model,
      feature,
      request_type: requestType as any,
      input_tokens: inputTokens,
      output_tokens: outputTokens,
      total_tokens: inputTokens + outputTokens,
      estimated_cost: cost,
      request_duration: duration,
      success,
      error_type: errorType,
      metadata,
      created_at: new Date().toISOString()
    }

    try {
      // Insert usage record
      const { error } = await this.supabase
        .from('ai_usage_logs')
        .insert(usageRecord)

      if (error) {
        console.error('Failed to record AI usage:', error)
        return
      }

      // Update quota usage
      await this.updateQuotaUsage(userId, organizationId, cost, inputTokens + outputTokens)

      // Update cached statistics
      await this.updateCachedStats(userId, organizationId, cost, inputTokens + outputTokens)

      // Check budget alerts
      await this.checkBudgetAlerts(userId, organizationId)

      // Track metrics
      metrics.increment('ai.usage_recorded', {
        provider,
        model,
        feature,
        success: success.toString()
      })

    } catch (error) {
      console.error('Error recording AI usage:', error)
      metrics.increment('ai.usage_recording_errors')
    }
  }

  /**
   * Check if user/organization can make AI request
   */
  async canMakeRequest(
    userId: string,
    organizationId: string,
    estimatedCost: number,
    estimatedTokens: number
  ): Promise<{
    allowed: boolean
    reason?: string
    quotaInfo?: any
  }> {
    try {
      // Check organization quotas first
      const orgQuotaCheck = await this.checkQuotas('organization', organizationId, estimatedCost, estimatedTokens)
      if (!orgQuotaCheck.allowed) {
        return orgQuotaCheck
      }

      // Check user quotas
      const userQuotaCheck = await this.checkQuotas('user', userId, estimatedCost, estimatedTokens)
      if (!userQuotaCheck.allowed) {
        return userQuotaCheck
      }

      // Check global quotas
      const globalQuotaCheck = await this.checkQuotas('global', 'global', estimatedCost, estimatedTokens)
      if (!globalQuotaCheck.allowed) {
        return globalQuotaCheck
      }

      return { allowed: true }

    } catch (error) {
      console.error('Error checking AI request permission:', error)
      // Fail open in case of errors
      return { allowed: true }
    }
  }

  /**
   * Get usage statistics for entity
   */
  async getUsageStats(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<UsageStats> {
    const cacheKey = `${this.cachePrefix}stats:${entityType}:${entityId}:${period}`
    
    // Try cache first
    const cached = await this.cache.get<UsageStats>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const startDate = this.getStartDate(period)
      let query = this.supabase
        .from('ai_usage_logs')
        .select('*')
        .gte('created_at', startDate.toISOString())

      if (entityType === 'user') {
        query = query.eq('user_id', entityId)
      } else if (entityType === 'organization') {
        query = query.eq('organization_id', entityId)
      }

      const { data: usageData, error } = await query

      if (error) {
        throw error
      }

      const stats = this.calculateStats(usageData || [])

      // Cache for 5 minutes
      await this.cache.set(cacheKey, stats, {
        ttl: 300,
        tags: ['ai_usage', entityType, entityId]
      })

      return stats

    } catch (error) {
      console.error('Error getting usage stats:', error)
      return this.getEmptyStats()
    }
  }

  /**
   * Get current quota usage
   */
  async getQuotaUsage(
    entityType: 'user' | 'organization' | 'global',
    entityId: string
  ): Promise<UsageQuota[]> {
    try {
      const { data, error } = await this.supabase
        .from('ai_usage_quotas')
        .select('*')
        .eq('entity_type', entityType)
        .eq('entity_id', entityId)

      if (error) {
        throw error
      }

      return data || []

    } catch (error) {
      console.error('Error getting quota usage:', error)
      return []
    }
  }

  /**
   * Set usage quota
   */
  async setQuota(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    period: 'daily' | 'weekly' | 'monthly',
    quotaType: 'requests' | 'tokens' | 'cost',
    limitValue: number
  ): Promise<void> {
    try {
      const quota: Partial<UsageQuota> = {
        entity_type: entityType,
        entity_id: entityId,
        period,
        quota_type: quotaType,
        limit_value: limitValue,
        current_usage: 0,
        reset_at: this.calculateResetDate(period).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { error } = await this.supabase
        .from('ai_usage_quotas')
        .upsert(quota, {
          onConflict: 'entity_type,entity_id,period,quota_type'
        })

      if (error) {
        throw error
      }

      // Clear cache
      await this.cache.invalidateByTags([`quota:${entityType}:${entityId}`])

    } catch (error) {
      console.error('Error setting quota:', error)
      throw error
    }
  }

  /**
   * Get budget alerts
   */
  async getBudgetAlerts(
    entityType: 'user' | 'organization' | 'global',
    entityId: string
  ): Promise<BudgetAlert[]> {
    try {
      const { data, error } = await this.supabase
        .from('ai_budget_alerts')
        .select('*')
        .eq('entity_type', entityType)
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) {
        throw error
      }

      return data || []

    } catch (error) {
      console.error('Error getting budget alerts:', error)
      return []
    }
  }

  // Private helper methods
  private async checkQuotas(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    estimatedCost: number,
    estimatedTokens: number
  ): Promise<{ allowed: boolean; reason?: string; quotaInfo?: any }> {
    const quotas = await this.getQuotaUsage(entityType, entityId)
    
    for (const quota of quotas) {
      if (this.isQuotaExpired(quota)) {
        await this.resetQuota(quota)
        continue
      }

      let wouldExceed = false
      let quotaValue = 0

      switch (quota.quota_type) {
        case 'cost':
          wouldExceed = (quota.current_usage + estimatedCost) > quota.limit_value
          quotaValue = estimatedCost
          break
        case 'tokens':
          wouldExceed = (quota.current_usage + estimatedTokens) > quota.limit_value
          quotaValue = estimatedTokens
          break
        case 'requests':
          wouldExceed = (quota.current_usage + 1) > quota.limit_value
          quotaValue = 1
          break
      }

      if (wouldExceed) {
        return {
          allowed: false,
          reason: `${quota.quota_type} quota exceeded for ${entityType}`,
          quotaInfo: {
            type: quota.quota_type,
            current: quota.current_usage,
            limit: quota.limit_value,
            period: quota.period,
            resetAt: quota.reset_at
          }
        }
      }
    }

    return { allowed: true }
  }

  private async updateQuotaUsage(
    userId: string,
    organizationId: string,
    cost: number,
    tokens: number
  ): Promise<void> {
    const updates = [
      { entityType: 'user', entityId: userId },
      { entityType: 'organization', entityId: organizationId },
      { entityType: 'global', entityId: 'global' }
    ]

    for (const { entityType, entityId } of updates) {
      const quotas = await this.getQuotaUsage(entityType as any, entityId)
      
      for (const quota of quotas) {
        let incrementValue = 0
        
        switch (quota.quota_type) {
          case 'cost':
            incrementValue = cost
            break
          case 'tokens':
            incrementValue = tokens
            break
          case 'requests':
            incrementValue = 1
            break
        }

        if (incrementValue > 0) {
          await this.supabase
            .from('ai_usage_quotas')
            .update({
              current_usage: quota.current_usage + incrementValue,
              updated_at: new Date().toISOString()
            })
            .eq('id', quota.id)
        }
      }
    }
  }

  private async updateCachedStats(
    userId: string,
    organizationId: string,
    cost: number,
    tokens: number
  ): Promise<void> {
    // Update cached counters for real-time stats
    const today = new Date().toISOString().split('T')[0]
    
    const updates = [
      { type: 'user', id: userId },
      { type: 'organization', id: organizationId },
      { type: 'global', id: 'global' }
    ]

    for (const { type, id } of updates) {
      const costKey = `${this.cachePrefix}cost:${type}:${id}:${today}`
      const tokensKey = `${this.cachePrefix}tokens:${type}:${id}:${today}`
      const requestsKey = `${this.cachePrefix}requests:${type}:${id}:${today}`

      // Increment counters (expire at end of day)
      const endOfDay = new Date()
      endOfDay.setHours(23, 59, 59, 999)
      const ttl = Math.floor((endOfDay.getTime() - Date.now()) / 1000)

      await Promise.all([
        this.incrementCounter(costKey, cost, ttl),
        this.incrementCounter(tokensKey, tokens, ttl),
        this.incrementCounter(requestsKey, 1, ttl)
      ])
    }
  }

  private async incrementCounter(key: string, value: number, ttl: number): Promise<void> {
    try {
      const current = await this.cache.get<number>(key) || 0
      await this.cache.set(key, current + value, { ttl })
    } catch (error) {
      console.error('Error incrementing counter:', error)
    }
  }

  private async checkBudgetAlerts(userId: string, organizationId: string): Promise<void> {
    // Implementation for budget alert checking
    // This would check current usage against thresholds and send alerts
  }

  private calculateStats(usageData: any[]): UsageStats {
    const totalRequests = usageData.length
    const totalTokens = usageData.reduce((sum, record) => sum + record.total_tokens, 0)
    const totalCost = usageData.reduce((sum, record) => sum + record.estimated_cost, 0)
    const successfulRequests = usageData.filter(record => record.success).length
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0
    const avgResponseTime = usageData.length > 0 
      ? usageData.reduce((sum, record) => sum + record.request_duration, 0) / usageData.length 
      : 0

    // Calculate top models and features
    const modelStats = this.aggregateByField(usageData, 'model')
    const featureStats = this.aggregateByField(usageData, 'feature')

    // Calculate cost trend (last 30 days)
    const costTrend = this.calculateCostTrend(usageData)

    return {
      totalRequests,
      totalTokens,
      totalCost,
      successRate,
      avgResponseTime,
      topModels: modelStats.slice(0, 5),
      topFeatures: featureStats.slice(0, 5),
      costTrend
    }
  }

  private aggregateByField(data: any[], field: string): Array<{ model?: string; feature?: string; usage: number; cost: number }> {
    const aggregated = data.reduce((acc, record) => {
      const key = record[field]
      if (!acc[key]) {
        acc[key] = { usage: 0, cost: 0 }
      }
      acc[key].usage += 1
      acc[key].cost += record.estimated_cost
      return acc
    }, {})

    return Object.entries(aggregated)
      .map(([key, stats]: [string, any]) => ({
        [field]: key,
        usage: stats.usage,
        cost: stats.cost
      }))
      .sort((a, b) => b.cost - a.cost)
  }

  private calculateCostTrend(data: any[]): Array<{ date: string; cost: number }> {
    const dailyCosts = data.reduce((acc, record) => {
      const date = record.created_at.split('T')[0]
      acc[date] = (acc[date] || 0) + record.estimated_cost
      return acc
    }, {})

    return Object.entries(dailyCosts)
      .map(([date, cost]) => ({ date, cost: cost as number }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }

  private getStartDate(period: string): Date {
    const now = new Date()
    switch (period) {
      case 'day':
        return new Date(now.getFullYear(), now.getMonth(), now.getDate())
      case 'week':
        const weekStart = new Date(now)
        weekStart.setDate(now.getDate() - now.getDay())
        return weekStart
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1)
      case 'year':
        return new Date(now.getFullYear(), 0, 1)
      default:
        return new Date(now.getFullYear(), now.getMonth(), 1)
    }
  }

  private calculateResetDate(period: string): Date {
    const now = new Date()
    switch (period) {
      case 'daily':
        const tomorrow = new Date(now)
        tomorrow.setDate(now.getDate() + 1)
        tomorrow.setHours(0, 0, 0, 0)
        return tomorrow
      case 'weekly':
        const nextWeek = new Date(now)
        nextWeek.setDate(now.getDate() + (7 - now.getDay()))
        nextWeek.setHours(0, 0, 0, 0)
        return nextWeek
      case 'monthly':
        return new Date(now.getFullYear(), now.getMonth() + 1, 1)
      default:
        return new Date(now.getFullYear(), now.getMonth() + 1, 1)
    }
  }

  private isQuotaExpired(quota: UsageQuota): boolean {
    return new Date(quota.reset_at) <= new Date()
  }

  private async resetQuota(quota: UsageQuota): Promise<void> {
    const newResetDate = this.calculateResetDate(quota.period)
    
    await this.supabase
      .from('ai_usage_quotas')
      .update({
        current_usage: 0,
        reset_at: newResetDate.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', quota.id)
  }

  private getEmptyStats(): UsageStats {
    return {
      totalRequests: 0,
      totalTokens: 0,
      totalCost: 0,
      successRate: 0,
      avgResponseTime: 0,
      topModels: [],
      topFeatures: [],
      costTrend: []
    }
  }
}

// Singleton instance
let usageTracker: AIUsageTracker | null = null

export const getUsageTracker = (): AIUsageTracker => {
  if (!usageTracker) {
    usageTracker = new AIUsageTracker()
  }
  return usageTracker
}

export { AIUsageTracker }
export type { UsageRecord, UsageQuota, BudgetAlert, UsageStats }
