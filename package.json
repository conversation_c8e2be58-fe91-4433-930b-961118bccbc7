{"name": "enterprise-it-helpdesk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.8.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "^13.5.11", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "redoc": "^2.5.0", "sonner": "^2.0.3", "speakeasy": "^2.0.0", "styled-components": "^6.1.18", "swagger-ui-react": "^3.29.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.8.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@vitejs/plugin-react": "^4.1.0", "@vitest/coverage-v8": "^3.1.4", "autoprefixer": "^10.4.16", "eslint": "^8.51.0", "eslint-config-next": "13.5.4", "jsdom": "^22.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "vitest": "^3.1.4"}}