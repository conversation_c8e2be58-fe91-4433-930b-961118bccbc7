{"name": "enterprise-it-helpdesk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "supabase gen types typescript --project-id $NEXT_PUBLIC_SUPABASE_PROJECT_ID --schema public > lib/database.types.ts", "db:reset": "supabase db reset", "db:push": "supabase db push", "shadcn": "shadcn-ui", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest __tests__/unit", "test:integration": "jest __tests__/integration", "test:security": "jest __tests__/security", "test:performance": "jest __tests__/performance", "test:ci": "jest --ci --coverage --watchAll=false", "test:security-ci": "jest __tests__/security --ci --watchAll=false", "test:performance-ci": "jest __tests__/performance --ci --watchAll=false", "validate-env": "node -e \"require('./lib/config/env-validator').validateOnStartup()\"", "lint:fix": "next lint --fix", "test:all": "npm run type-check && npm run lint && npm run test:ci", "build:analyze": "ANALYZE=true npm run build", "perf:bundle": "npm run build:analyze && open bundle-analysis.html", "perf:test": "npm run test:performance-ci", "perf:monitor": "node scripts/performance-monitor.js", "cache:stats": "node scripts/cache-stats.js", "cache:clear": "node scripts/cache-clear.js", "db:analyze": "node scripts/db-analyze.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.43.4", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.14.4", "input-otp": "^1.4.1", "ioredis": "^5.4.1", "lucide-react": "^0.462.0", "next": "^15.1.6", "next-themes": "^0.4.4", "qrcode": "^1.5.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.13.3", "sonner": "^1.7.4", "speakeasy": "^2.0.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "^15.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.17", "typescript": "^5.6.3", "webpack-bundle-analyzer": "^4.10.1"}, "engines": {"node": ">=18.17.0"}}