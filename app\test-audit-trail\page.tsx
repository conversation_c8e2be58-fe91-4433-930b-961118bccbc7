'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { AuditLogViewer } from '@/components/audit/enhanced-audit-log-viewer';
import { useAuditLog } from '@/hooks/use-audit-log';
import { logCollector } from '@/lib/services/log-collector-service';
import { Shield, Activity, Database, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

export default function TestAuditTrailPage() {
  const audit = useAuditLog();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testUserAction = async () => {
    try {
      await audit.logAction({
        event_type: 'DATA_ACCESSED',
        severity: 'INFO',
        action: 'Test user action',
        description: 'User performed a test action in the audit trail test page'
      });
      addTestResult('✅ User action logged successfully');
      toast({ title: 'Success', description: 'User action logged' });
    } catch (error) {
      addTestResult('❌ Failed to log user action');
      toast({ title: 'Error', description: 'Failed to log user action', variant: 'destructive' });
    }
  };

  const testFormSubmission = async () => {
    try {
      await logCollector.collectFormSubmission(
        'test_form',
        { field1: 'value1', field2: 'value2' },
        true
      );
      addTestResult('✅ Form submission logged successfully');
      toast({ title: 'Success', description: 'Form submission logged' });
    } catch (error) {
      addTestResult('❌ Failed to log form submission');
      toast({ title: 'Error', description: 'Failed to log form submission', variant: 'destructive' });
    }
  };

  const testSecurityEvent = async () => {
    try {
      await audit.logSecurityEvent('Unauthorized access attempt detected', 'WARNING');
      addTestResult('✅ Security event logged successfully');
      toast({ title: 'Success', description: 'Security event logged' });
    } catch (error) {
      addTestResult('❌ Failed to log security event');
      toast({ title: 'Error', description: 'Failed to log security event', variant: 'destructive' });
    }
  };

  const testErrorLogging = async () => {
    try {
      await audit.logError(new Error('Test error for audit trail'), { context: 'test_page' });
      addTestResult('✅ Error logged successfully');
      toast({ title: 'Success', description: 'Error logged' });
    } catch (error) {
      addTestResult('❌ Failed to log error');
      toast({ title: 'Error', description: 'Failed to log error', variant: 'destructive' });
    }
  };

  const testDataExport = async () => {
    try {
      await audit.logDataExport('test_data', 'CSV', 100);
      addTestResult('✅ Data export logged successfully');
      toast({ title: 'Success', description: 'Data export logged' });
    } catch (error) {
      addTestResult('❌ Failed to log data export');
      toast({ title: 'Error', description: 'Failed to log data export', variant: 'destructive' });
    }
  };

  const testPermissionChange = async () => {
    try {
      await audit.logPermissionChange('test-user-123', 'admin_access', 'grant');
      addTestResult('✅ Permission change logged successfully');
      toast({ title: 'Success', description: 'Permission change logged' });
    } catch (error) {
      addTestResult('❌ Failed to log permission change');
      toast({ title: 'Error', description: 'Failed to log permission change', variant: 'destructive' });
    }
  };

  const features = [
    {
      icon: Shield,
      title: 'Immutable Logging',
      description: 'Logs cannot be modified or deleted once written'
    },
    {
      icon: Activity,
      title: 'Real-time Collection',
      description: 'Automatic collection from all system sources'
    },
    {
      icon: Database,
      title: 'Comprehensive Storage',
      description: 'Secure storage with encryption and archival'
    },
    {
      icon: FileText,
      title: 'Compliance Reporting',
      description: 'Export logs in multiple formats for compliance'
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Audit Trail System Test</h1>
          <p className="text-muted-foreground">
            Test and demonstrate the enterprise-grade audit trail functionality
          </p>
        </div>
      </div>

      {/* Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {features.map((feature) => (
          <Card key={feature.title}>
            <CardHeader className="space-y-1">
              <div className="flex items-center gap-2">
                <feature.icon className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="test" className="space-y-4">
        <TabsList>
          <TabsTrigger value="test">Test Actions</TabsTrigger>
          <TabsTrigger value="viewer">Log Viewer</TabsTrigger>
          <TabsTrigger value="results">Test Results</TabsTrigger>
        </TabsList>

        <TabsContent value="test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit Log Test Actions</CardTitle>
              <CardDescription>
                Click the buttons below to test different audit logging scenarios
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button onClick={testUserAction} className="w-full">
                  <Activity className="mr-2 h-4 w-4" />
                  Log User Action
                </Button>
                
                <Button onClick={testFormSubmission} className="w-full">
                  <FileText className="mr-2 h-4 w-4" />
                  Log Form Submission
                </Button>
                
                <Button onClick={testSecurityEvent} variant="warning" className="w-full">
                  <Shield className="mr-2 h-4 w-4" />
                  Log Security Event
                </Button>
                
                <Button onClick={testErrorLogging} variant="destructive" className="w-full">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  Log Error Event
                </Button>
                
                <Button onClick={testDataExport} className="w-full">
                  <Database className="mr-2 h-4 w-4" />
                  Log Data Export
                </Button>
                
                <Button onClick={testPermissionChange} className="w-full">
                  <Shield className="mr-2 h-4 w-4" />
                  Log Permission Change
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Automatic Log Collection</CardTitle>
              <CardDescription>
                The following events are automatically logged by the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Page navigation and access</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Database changes (INSERT, UPDATE, DELETE)</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">API requests and responses</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">System errors and exceptions</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Authentication events</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="viewer">
          <AuditLogViewer maxHeight="500px" />
        </TabsContent>

        <TabsContent value="results">
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                Results from test actions performed
              </CardDescription>
            </CardHeader>
            <CardContent>
              {testResults.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No tests have been run yet. Go to the Test Actions tab to run tests.
                </p>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}