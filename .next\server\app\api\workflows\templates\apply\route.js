(()=>{var e={};e.id=3029,e.ids=[3029],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},46027:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(42706),o=t(28203),n=t(45994),i=t(39187),p=t(61487),u=t(6804),l=t(2924);async function d(e){try{let r=(0,p.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await r.from("staff").select("*").eq("auth_id",t.id).single();if(!a)return i.NextResponse.json({error:"Staff not found"},{status:404});let{requestId:o,templateId:n,customizations:d}=await e.json();if(!o||!n)return i.NextResponse.json({error:"Request ID and Template ID are required"},{status:400});let c=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),m=new l.I(r),x=new u.WorkflowTemplateManager(r,c,m);if(!d)return await x.applyTemplateToRequest(o,n,a.id),i.NextResponse.json({message:"Template applied to request successfully"});{let e=await x.createWorkflowFromTemplate(n,o,a.id,d);return i.NextResponse.json({message:"Workflow created from template",workflowInstanceId:e})}}catch(e){return console.error("Error applying template:",e),i.NextResponse.json({error:e.message||"Failed to apply template"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/templates/apply/route",pathname:"/api/workflows/templates/apply",filename:"route",bundlePath:"app/api/workflows/templates/apply/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\apply\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:w}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},6804:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Expression expected\n   ,-[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-template-manager.ts\x1b[0m:2:1]\n \x1b[2m1\x1b[0m | owEngine\n \x1b[2m2\x1b[0m |   ) {\n   : \x1b[35;1m  ^\x1b[0m\n \x1b[2m3\x1b[0m |     this.supabase = supabase;\n \x1b[2m4\x1b[0m |     this.auditService = auditService;\n \x1b[2m5\x1b[0m |     this.workflowEngine = workflowEngine;\n   `----\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064,3744,9389,2924],()=>t(46027));module.exports=s})();