"use strict";(()=>{var e={};e.id=3029,e.ids=[3029],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},26515:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{POST:()=>l});var o=t(42706),s=t(28203),n=t(45994),i=t(39187),p=t(6804),u=t(52054);async function l(e){try{let r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("*").eq("auth_id",t.id).single();if(!o)return i.NextResponse.json({error:"Staff not found"},{status:404});let{requestId:s,templateId:n,customizations:l}=await e.json();if(!s||!n)return i.NextResponse.json({error:"Request ID and Template ID are required"},{status:400});let d=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),c=new u.I(r),f=new p.WorkflowTemplateManager(r,d,c);if(!l)return await f.applyTemplateToRequest(s,n,o.id),i.NextResponse.json({message:"Template applied to request successfully"});{let e=await f.createWorkflowFromTemplate(n,s,o.id,l);return i.NextResponse.json({message:"Workflow created from template",workflowInstanceId:e})}}catch(e){return console.error("Error applying template:",e),i.NextResponse.json({error:e.message||"Failed to apply template"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let d=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/templates/apply/route",pathname:"/api/workflows/templates/apply",filename:"route",bundlePath:"app/api/workflows/templates/apply/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\apply\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:f,serverHooks:m}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:f})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8096,2076],()=>t(26515));module.exports=a})();