"use strict";(()=>{var e={};e.id=3029,e.ids=[3029],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},26515:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>d});var s=t(42706),o=t(28203),n=t(45994),p=t(39187),i=t(61487),u=t(6804),l=t(2924);async function d(e){try{let r=(0,i.U)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await r.from("staff").select("*").eq("auth_id",t.id).single();if(!s)return p.NextResponse.json({error:"Staff not found"},{status:404});let{requestId:o,templateId:n,customizations:d}=await e.json();if(!o||!n)return p.NextResponse.json({error:"Request ID and Template ID are required"},{status:400});let c=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),f=new l.I(r),m=new u.WorkflowTemplateManager(r,c,f);if(!d)return await m.applyTemplateToRequest(o,n,s.id),p.NextResponse.json({message:"Template applied to request successfully"});{let e=await m.createWorkflowFromTemplate(n,o,s.id,d);return p.NextResponse.json({message:"Workflow created from template",workflowInstanceId:e})}}catch(e){return console.error("Error applying template:",e),p.NextResponse.json({error:e.message||"Failed to apply template"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/templates/apply/route",pathname:"/api/workflows/templates/apply",filename:"route",bundlePath:"app/api/workflows/templates/apply/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\apply\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:w}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8096,2076],()=>t(26515));module.exports=a})();