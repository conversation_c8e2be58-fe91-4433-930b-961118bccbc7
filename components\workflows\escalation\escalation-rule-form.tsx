'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, X } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface EscalationRule {
  id?: string;
  name: string;
  description: string;
  trigger_type: 'sla_breach' | 'overdue' | 'manual' | 'approval_timeout';
  priority: 'low' | 'medium' | 'high' | 'critical';
  is_active: boolean;
  conditions: EscalationCondition[];
  actions: EscalationAction[];
  retry_interval?: number;
  max_retries?: number;
}

interface EscalationCondition {
  field: string;
  operator: string;
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

interface EscalationAction {
  type: string;
  target?: string;
  parameters?: Record<string, any>;
}

interface EscalationRuleFormProps {
  rule?: EscalationRule | null;
  onSave: () => void;
  onCancel: () => void;
}

export function EscalationRuleForm({ rule, onSave, onCancel }: EscalationRuleFormProps) {
  const [formData, setFormData] = useState<EscalationRule>({
    name: rule?.name || '',
    description: rule?.description || '',
    trigger_type: rule?.trigger_type || 'sla_breach',
    priority: rule?.priority || 'medium',
    is_active: rule?.is_active ?? true,
    conditions: rule?.conditions || [],
    actions: rule?.actions || [],
    retry_interval: rule?.retry_interval || 30,
    max_retries: rule?.max_retries || 3,
  });

  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const method = rule ? 'PUT' : 'POST';
      const url = rule 
        ? `/api/workflows/escalation/${rule.id}`
        : '/api/workflows/escalation';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.details?.join(', ') || 'Failed to save rule');
      }

      toast({
        title: 'Success',
        description: `Rule ${rule ? 'updated' : 'created'} successfully`,
      });

      onSave();
    } catch (error) {
      console.error('Error saving rule:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to save rule',
        variant: 'destructive',
      });
    }
  };

  const addCondition = () => {
    setFormData({
      ...formData,
      conditions: [
        ...formData.conditions,
        { field: '', operator: 'equals', value: '', logicalOperator: 'AND' },
      ],
    });
  };

  const removeCondition = (index: number) => {
    setFormData({
      ...formData,
      conditions: formData.conditions.filter((_, i) => i !== index),
    });
  };

  const updateCondition = (index: number, updates: Partial<EscalationCondition>) => {
    setFormData({
      ...formData,
      conditions: formData.conditions.map((cond, i) =>
        i === index ? { ...cond, ...updates } : cond
      ),
    });
  };

  const addAction = () => {
    setFormData({
      ...formData,
      actions: [
        ...formData.actions,
        { type: 'notify', target: '', parameters: {} },
      ],
    });
  };

  const removeAction = (index: number) => {
    setFormData({
      ...formData,
      actions: formData.actions.filter((_, i) => i !== index),
    });
  };

  const updateAction = (index: number, updates: Partial<EscalationAction>) => {
    setFormData({
      ...formData,
      actions: formData.actions.map((action, i) =>
        i === index ? { ...action, ...updates } : action
      ),
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Rule Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="trigger_type">Trigger Type</Label>
          <Select
            value={formData.trigger_type}
            onValueChange={(value) => setFormData({ ...formData, trigger_type: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sla_breach">SLA Breach</SelectItem>
              <SelectItem value="overdue">Overdue Request</SelectItem>
              <SelectItem value="approval_timeout">Approval Timeout</SelectItem>
              <SelectItem value="manual">Manual Trigger</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={2}
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="priority">Priority</Label>
          <Select
            value={formData.priority}
            onValueChange={(value) => setFormData({ ...formData, priority: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="retry_interval">Retry Interval (min)</Label>
          <Input
            id="retry_interval"
            type="number"
            value={formData.retry_interval}
            onChange={(e) => setFormData({ ...formData, retry_interval: parseInt(e.target.value) })}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="max_retries">Max Retries</Label>
          <Input
            id="max_retries"
            type="number"
            value={formData.max_retries}
            onChange={(e) => setFormData({ ...formData, max_retries: parseInt(e.target.value) })}
          />
        </div>
      </div>

      {/* Conditions */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Conditions</Label>
          <Button type="button" variant="outline" size="sm" onClick={addCondition}>
            <Plus className="mr-1 h-3 w-3" />
            Add Condition
          </Button>
        </div>
        
        <div className="space-y-2">
          {formData.conditions.map((condition, index) => (
            <div key={index} className="flex items-center gap-2 p-2 border rounded">
              <Select
                value={condition.field}
                onValueChange={(value) => updateCondition(index, { field: value })}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Field" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="priority">Priority</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="age_hours">Age (Hours)</SelectItem>
                  <SelectItem value="age_days">Age (Days)</SelectItem>
                  <SelectItem value="service_category">Service Category</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={condition.operator}
                onValueChange={(value) => updateCondition(index, { operator: value })}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="not_equals">Not Equals</SelectItem>
                  <SelectItem value="greater_than">Greater Than</SelectItem>
                  <SelectItem value="less_than">Less Than</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                </SelectContent>
              </Select>

              <Input
                value={condition.value}
                onChange={(e) => updateCondition(index, { value: e.target.value })}
                placeholder="Value"
                className="flex-1"
              />

              {index > 0 && (
                <Select
                  value={condition.logicalOperator || 'AND'}
                  onValueChange={(value) => updateCondition(index, { logicalOperator: value as any })}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AND">AND</SelectItem>
                    <SelectItem value="OR">OR</SelectItem>
                  </SelectContent>
                </Select>
              )}

              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeCondition(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Actions</Label>
          <Button type="button" variant="outline" size="sm" onClick={addAction}>
            <Plus className="mr-1 h-3 w-3" />
            Add Action
          </Button>
        </div>
        
        <div className="space-y-2">
          {formData.actions.map((action, index) => (
            <div key={index} className="flex items-center gap-2 p-2 border rounded">
              <Select
                value={action.type}
                onValueChange={(value) => updateAction(index, { type: value })}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="notify">Notify</SelectItem>
                  <SelectItem value="reassign">Reassign</SelectItem>
                  <SelectItem value="change_priority">Change Priority</SelectItem>
                  <SelectItem value="add_approver">Add Approver</SelectItem>
                  <SelectItem value="auto_approve">Auto Approve</SelectItem>
                  <SelectItem value="auto_reject">Auto Reject</SelectItem>
                </SelectContent>
              </Select>

              {['notify', 'reassign', 'add_approver'].includes(action.type) && (
                <Input
                  value={action.target || ''}
                  onChange={(e) => updateAction(index, { target: e.target.value })}
                  placeholder="Target (user:id, role:name, email)"
                  className="flex-1"
                />
              )}

              {action.type === 'change_priority' && (
                <Select
                  value={action.parameters?.priority || 'high'}
                  onValueChange={(value) => 
                    updateAction(index, { 
                      parameters: { ...action.parameters, priority: value } 
                    })
                  }
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              )}

              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeAction(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {rule ? 'Update Rule' : 'Create Rule'}
        </Button>
      </div>
    </form>
  );
}
