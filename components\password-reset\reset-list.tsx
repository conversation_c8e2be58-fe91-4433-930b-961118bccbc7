" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => handleProcessRequest(request)}
                    disabled={request.status !== 'pending'}
                  >
                    <Key className="mr-2 h-4 w-4" />
                    Process Reset
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onStatusUpdate(request.id, 'completed')}
                    disabled={request.status === 'completed'}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Mark as Completed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onStatusUpdate(request.id, 'failed')}
                    disabled={request.status === 'completed' || request.status === 'failed'}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Mark as Failed
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </Card>
        ))}
      </div>

      {/* Password Reset Dialog */}
      <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Password Reset</DialogTitle>
            <DialogDescription>
              Generate and provide temporary password to the user
            </DialogDescription>
          </DialogHeader>
          
          {selectedRequest && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>Password Type</Label>
                <div>{selectedRequest.parameters.password_type}</div>
              </div>
              
              <div className="space-y-2">
                <Label>Affected User(s)</Label>
                <div className="flex flex-wrap gap-1">
                  {selectedRequest.affected_staff?.map((staff) => (
                    <Badge key={staff.id} variant="secondary">
                      {staff.name_en} ({staff.email})
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="temp-password">Temporary Password</Label>
                <div className="flex gap-2">
                  <Input
                    id="temp-password"
                    value={temporaryPassword}
                    readOnly
                    className="font-mono"
                  />
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => copyToClipboard(temporaryPassword)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => setTemporaryPassword(generateTemporaryPassword())}
                  >
                    <Key className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  This temporary password must be changed on first login
                </p>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPasswordDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCompleteReset}>
              Complete Reset
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
