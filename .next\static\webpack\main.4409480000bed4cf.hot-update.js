"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/build/deployment-id.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/build/deployment-id.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDeploymentIdQueryOrEmptyString\", ({\n    enumerable: true,\n    get: function() {\n        return getDeploymentIdQueryOrEmptyString;\n    }\n}));\nfunction getDeploymentIdQueryOrEmptyString() {\n    if (false) {}\n    return \"\";\n}\n\n//# sourceMappingURL=deployment-id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL2RlcGxveW1lbnQtaWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixxRUFBb0U7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBLFFBQVEsS0FBOEIsRUFBRSxFQUVuQztBQUNMO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC9kZXBsb3ltZW50LWlkLmpzP2FhZDgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJnZXREZXBsb3ltZW50SWRRdWVyeU9yRW1wdHlTdHJpbmdcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZztcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZygpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTkVYVF9ERVBMT1lNRU5UX0lEKSB7XG4gICAgICAgIHJldHVybiBgP2RwbD0ke3Byb2Nlc3MuZW52Lk5FWFRfREVQTE9ZTUVOVF9JRH1gO1xuICAgIH1cbiAgICByZXR1cm4gXCJcIjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVwbG95bWVudC1pZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/deployment-id.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/dev/error-overlay/websocket.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/error-overlay/websocket.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addMessageListener: function() {\n        return addMessageListener;\n    },\n    sendMessage: function() {\n        return sendMessage;\n    },\n    connectHMR: function() {\n        return connectHMR;\n    }\n});\nlet source;\nconst eventCallbacks = [];\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === \"http:\" ? \"ws\" : \"wss\";\n}\nfunction addMessageListener(callback) {\n    eventCallbacks.push(callback);\n}\nfunction sendMessage(data) {\n    if (!source || source.readyState !== source.OPEN) return;\n    return source.send(data);\n}\nlet reconnections = 0;\nfunction connectHMR(options) {\n    function init() {\n        if (source) source.close();\n        function handleOnline() {\n            reconnections = 0;\n            window.console.log(\"[HMR] connected\");\n        }\n        function handleMessage(event) {\n            // Coerce into HMR_ACTION_TYPES as that is the format.\n            const msg = JSON.parse(event.data);\n            for (const eventCallback of eventCallbacks){\n                eventCallback(msg);\n            }\n        }\n        let timer;\n        function handleDisconnect() {\n            source.onerror = null;\n            source.onclose = null;\n            source.close();\n            reconnections++;\n            // After 25 reconnects we'll want to reload the page as it indicates the dev server is no longer running.\n            if (reconnections > 25) {\n                window.location.reload();\n                return;\n            }\n            clearTimeout(timer);\n            // Try again after 5 seconds\n            timer = setTimeout(init, reconnections > 5 ? 5000 : 1000);\n        }\n        const { hostname, port } = location;\n        const protocol = getSocketProtocol(options.assetPrefix || \"\");\n        const assetPrefix = options.assetPrefix.replace(/^\\/+/, \"\");\n        let url = protocol + \"://\" + hostname + \":\" + port + (assetPrefix ? \"/\" + assetPrefix : \"\");\n        if (assetPrefix.startsWith(\"http\")) {\n            url = protocol + \"://\" + assetPrefix.split(\"://\")[1];\n        }\n        source = new window.WebSocket(\"\" + url + options.path);\n        source.onopen = handleOnline;\n        source.onerror = handleDisconnect;\n        source.onclose = handleDisconnect;\n        source.onmessage = handleMessage;\n    }\n    init();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/dev/error-overlay/websocket.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/index.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/client/index.js ***!
  \************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* global location */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    version: function() {\n        return version;\n    },\n    router: function() {\n        return router;\n    },\n    emitter: function() {\n        return emitter;\n    },\n    initialize: function() {\n        return initialize;\n    },\n    hydrate: function() {\n        return hydrate;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"./node_modules/next/dist/build/polyfills/polyfill-module.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"./node_modules/react-dom/client.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../shared/lib/router/utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _isdynamic = __webpack_require__(/*! ../shared/lib/router/utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _querystring = __webpack_require__(/*! ../shared/lib/router/utils/querystring */ \"./node_modules/next/dist/shared/lib/router/utils/querystring.js\");\nconst _runtimeconfigexternal = __webpack_require__(/*! ../shared/lib/runtime-config.external */ \"./node_modules/next/dist/shared/lib/runtime-config.external.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _portal = __webpack_require__(/*! ./portal */ \"./node_modules/next/dist/client/portal/index.js\");\nconst _headmanager = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./head-manager */ \"./node_modules/next/dist/client/head-manager.js\"));\nconst _pageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./page-loader */ \"./node_modules/next/dist/client/page-loader.js\"));\nconst _performancerelayer = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./performance-relayer */ \"./node_modules/next/dist/client/performance-relayer.js\"));\nconst _routeannouncer = __webpack_require__(/*! ./route-announcer */ \"./node_modules/next/dist/client/route-announcer.js\");\nconst _router = __webpack_require__(/*! ./router */ \"./node_modules/next/dist/client/router.js\");\nconst _iserror = __webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\");\nconst _imageconfigcontextsharedruntime = __webpack_require__(/*! ../shared/lib/image-config-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\");\nconst _removebasepath = __webpack_require__(/*! ./remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ./has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _adapters = __webpack_require__(/*! ../shared/lib/router/adapters */ \"./node_modules/next/dist/shared/lib/router/adapters.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../shared/lib/hooks-client-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _onrecoverableerror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./on-recoverable-error */ \"./node_modules/next/dist/client/on-recoverable-error.js\"));\nconst _tracer = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./tracing/tracer */ \"./node_modules/next/dist/client/tracing/tracer.js\"));\nconst _reporttosocket = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./tracing/report-to-socket */ \"./node_modules/next/dist/client/tracing/report-to-socket.js\"));\nconst version = \"13.5.11\";\nlet router;\nconst emitter = (0, _mitt.default)();\nconst looseToArray = (input)=>[].slice.call(input);\nlet initialData;\nlet defaultLocale = undefined;\nlet asPath;\nlet pageLoader;\nlet appElement;\nlet headManager;\nlet initialMatchesMiddleware = false;\nlet lastAppProps;\nlet lastRenderReject;\nlet devClient;\nlet CachedApp, onPerfEntry;\nlet CachedComponent;\nclass Container extends _react.default.Component {\n    componentDidCatch(componentErr, info) {\n        this.props.fn(componentErr, info);\n    }\n    componentDidMount() {\n        this.scrollToHash();\n        // We need to replace the router state if:\n        // - the page was (auto) exported and has a query string or search (hash)\n        // - it was auto exported and is a dynamic route (to provide params)\n        // - if it is a client-side skeleton (fallback render)\n        // - if middleware matches the current page (may have rewrite params)\n        // - if rewrites in next.config.js match (may have rewrite params)\n        if (router.isSsr && (initialData.isFallback || initialData.nextExport && ((0, _isdynamic.isDynamicRoute)(router.pathname) || location.search || false || initialMatchesMiddleware) || initialData.props && initialData.props.__N_SSG && (location.search || false || initialMatchesMiddleware))) {\n            // update query on mount for exported pages\n            router.replace(router.pathname + \"?\" + String((0, _querystring.assign)((0, _querystring.urlQueryToSearchParams)(router.query), new URLSearchParams(location.search))), asPath, {\n                // @ts-ignore\n                // WARNING: `_h` is an internal option for handing Next.js\n                // client-side hydration. Your app should _never_ use this property.\n                // It may change at any time without notice.\n                _h: 1,\n                // Fallback pages must trigger the data fetch, so the transition is\n                // not shallow.\n                // Other pages (strictly updating query) happens shallowly, as data\n                // requirements would already be present.\n                shallow: !initialData.isFallback && !initialMatchesMiddleware\n            }).catch((err)=>{\n                if (!err.cancelled) throw err;\n            });\n        }\n    }\n    componentDidUpdate() {\n        this.scrollToHash();\n    }\n    scrollToHash() {\n        let { hash } = location;\n        hash = hash && hash.substring(1);\n        if (!hash) return;\n        const el = document.getElementById(hash);\n        if (!el) return;\n        // If we call scrollIntoView() in here without a setTimeout\n        // it won't scroll properly.\n        setTimeout(()=>el.scrollIntoView(), 0);\n    }\n    render() {\n        if (false) {} else {\n            const { ReactDevOverlay } = __webpack_require__(/*! next/dist/compiled/@next/react-dev-overlay/dist/client */ \"./node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js\");\n            return /*#__PURE__*/ _react.default.createElement(ReactDevOverlay, null, this.props.children);\n        }\n    }\n}\nasync function initialize(opts) {\n    if (opts === void 0) opts = {};\n    _tracer.default.onSpanEnd(_reporttosocket.default);\n    // This makes sure this specific lines are removed in production\n    if (true) {\n        devClient = opts.devClient;\n    }\n    initialData = JSON.parse(document.getElementById(\"__NEXT_DATA__\").textContent);\n    window.__NEXT_DATA__ = initialData;\n    defaultLocale = initialData.defaultLocale;\n    const prefix = initialData.assetPrefix || \"\";\n    self.__next_set_public_path__(\"\" + prefix + \"/_next/\") //eslint-disable-line\n    ;\n    // Initialize next/config with the environment configuration\n    (0, _runtimeconfigexternal.setConfig)({\n        serverRuntimeConfig: {},\n        publicRuntimeConfig: initialData.runtimeConfig || {}\n    });\n    asPath = (0, _utils.getURL)();\n    // make sure not to attempt stripping basePath for 404s\n    if ((0, _hasbasepath.hasBasePath)(asPath)) {\n        asPath = (0, _removebasepath.removeBasePath)(asPath);\n    }\n    if (false) {}\n    if (initialData.scriptLoader) {\n        const { initScriptLoader } = __webpack_require__(/*! ./script */ \"./node_modules/next/dist/client/script.js\");\n        initScriptLoader(initialData.scriptLoader);\n    }\n    pageLoader = new _pageloader.default(initialData.buildId, prefix);\n    const register = (param)=>{\n        let [r, f] = param;\n        return pageLoader.routeLoader.onEntrypoint(r, f);\n    };\n    if (window.__NEXT_P) {\n        // Defer page registration for another tick. This will increase the overall\n        // latency in hydrating the page, but reduce the total blocking time.\n        window.__NEXT_P.map((p)=>setTimeout(()=>register(p), 0));\n    }\n    window.__NEXT_P = [];\n    window.__NEXT_P.push = register;\n    headManager = (0, _headmanager.default)();\n    headManager.getIsSsr = ()=>{\n        return router.isSsr;\n    };\n    appElement = document.getElementById(\"__next\");\n    return {\n        assetPrefix: prefix\n    };\n}\nfunction renderApp(App, appProps) {\n    return /*#__PURE__*/ _react.default.createElement(App, appProps);\n}\nfunction AppContainer(param) {\n    _s();\n    let { children } = param;\n    // Create a memoized value for next/navigation router context.\n    const adaptedForAppRouter = _react.default.useMemo(()=>{\n        return (0, _adapters.adaptForAppRouterInstance)(router);\n    }, []);\n    var _self___NEXT_DATA___autoExport;\n    return /*#__PURE__*/ _react.default.createElement(Container, {\n        fn: (error)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            renderError({\n                App: CachedApp,\n                err: error\n            }).catch((err)=>console.error(\"Error rendering page: \", err))\n    }, /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.AppRouterContext.Provider, {\n        value: adaptedForAppRouter\n    }, /*#__PURE__*/ _react.default.createElement(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n        value: (0, _adapters.adaptForSearchParams)(router)\n    }, /*#__PURE__*/ _react.default.createElement(_adapters.PathnameContextProviderAdapter, {\n        router: router,\n        isAutoExport: (_self___NEXT_DATA___autoExport = self.__NEXT_DATA__.autoExport) != null ? _self___NEXT_DATA___autoExport : false\n    }, /*#__PURE__*/ _react.default.createElement(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n        value: (0, _adapters.adaptForPathParams)(router)\n    }, /*#__PURE__*/ _react.default.createElement(_routercontextsharedruntime.RouterContext.Provider, {\n        value: (0, _router.makePublicRouterInstance)(router)\n    }, /*#__PURE__*/ _react.default.createElement(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n        value: headManager\n    }, /*#__PURE__*/ _react.default.createElement(_imageconfigcontextsharedruntime.ImageConfigContext.Provider, {\n        value: {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[]}\n    }, children))))))));\n}\n_s(AppContainer, \"F6BSfrFQNeqenuPnUMVY/6gI8uE=\");\n_c = AppContainer;\nconst wrapApp = (App)=>(wrappedAppProps)=>{\n        const appProps = {\n            ...wrappedAppProps,\n            Component: CachedComponent,\n            err: initialData.err,\n            router\n        };\n        return /*#__PURE__*/ _react.default.createElement(AppContainer, null, renderApp(App, appProps));\n    };\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps) {\n    let { App, err } = renderErrorProps;\n    // In development runtime errors are caught by our overlay\n    // In production we catch runtime errors using componentDidCatch which will trigger renderError\n    if (true) {\n        // A Next.js rendering runtime error is always unrecoverable\n        // FIXME: let's make this recoverable (error in GIP client-transition)\n        devClient.onUnrecoverableError();\n        // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n        // render itself.\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        return doRender({\n            App: ()=>null,\n            props: {},\n            Component: ()=>null,\n            styleSheets: []\n        });\n    }\n    // Make sure we log the error to the console, otherwise users can't track down issues.\n    console.error(err);\n    console.error(\"A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred\");\n    return pageLoader.loadPage(\"/_error\").then((param)=>{\n        let { page: ErrorComponent, styleSheets } = param;\n        return (lastAppProps == null ? void 0 : lastAppProps.Component) === ErrorComponent ? Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../pages/_error */ \"./node_modules/next/dist/pages/_error.js\"))).then((errorModule)=>{\n            return Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../pages/_app */ \"./node_modules/next/dist/pages/_app.js\"))).then((appModule)=>{\n                App = appModule.default;\n                renderErrorProps.App = App;\n                return errorModule;\n            });\n        }).then((m)=>({\n                ErrorComponent: m.default,\n                styleSheets: []\n            })) : {\n            ErrorComponent,\n            styleSheets\n        };\n    }).then((param)=>{\n        let { ErrorComponent, styleSheets } = param;\n        var _renderErrorProps_props;\n        // In production we do a normal render with the `ErrorComponent` as component.\n        // If we've gotten here upon initial render, we can use the props from the server.\n        // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n        const AppTree = wrapApp(App);\n        const appCtx = {\n            Component: ErrorComponent,\n            AppTree,\n            router,\n            ctx: {\n                err,\n                pathname: initialData.page,\n                query: initialData.query,\n                asPath,\n                AppTree\n            }\n        };\n        return Promise.resolve(((_renderErrorProps_props = renderErrorProps.props) == null ? void 0 : _renderErrorProps_props.err) ? renderErrorProps.props : (0, _utils.loadGetInitialProps)(App, appCtx)).then((initProps)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            doRender({\n                ...renderErrorProps,\n                err,\n                Component: ErrorComponent,\n                styleSheets,\n                props: initProps\n            }));\n    });\n}\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head(param) {\n    _s1();\n    let { callback } = param;\n    // We use `useLayoutEffect` to guarantee the callback is executed\n    // as soon as React flushes the update.\n    _react.default.useLayoutEffect(()=>callback(), [\n        callback\n    ]);\n    return null;\n}\n_s1(Head, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c1 = Head;\nconst performanceMarks = {\n    navigationStart: \"navigationStart\",\n    beforeRender: \"beforeRender\",\n    afterRender: \"afterRender\",\n    afterHydrate: \"afterHydrate\",\n    routeChange: \"routeChange\"\n};\nconst performanceMeasures = {\n    hydration: \"Next.js-hydration\",\n    beforeHydration: \"Next.js-before-hydration\",\n    routeChangeToRender: \"Next.js-route-change-to-render\",\n    render: \"Next.js-render\"\n};\nlet reactRoot = null;\n// On initial render a hydrate should always happen\nlet shouldHydrate = true;\nfunction clearMarks() {\n    [\n        performanceMarks.beforeRender,\n        performanceMarks.afterHydrate,\n        performanceMarks.afterRender,\n        performanceMarks.routeChange\n    ].forEach((mark)=>performance.clearMarks(mark));\n}\nfunction markHydrateComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n    ;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, \"mark\").length;\n    if (hasBeforeRenderMark) {\n        const beforeHydrationMeasure = performance.measure(performanceMeasures.beforeHydration, performanceMarks.navigationStart, performanceMarks.beforeRender);\n        const hydrationMeasure = performance.measure(performanceMeasures.hydration, performanceMarks.beforeRender, performanceMarks.afterHydrate);\n        if ( true && // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n        beforeHydrationMeasure !== undefined && hydrationMeasure !== undefined) {\n            _tracer.default.startSpan(\"navigation-to-hydration\", {\n                startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n                attributes: {\n                    pathname: location.pathname,\n                    query: location.search\n                }\n            }).end(performance.timeOrigin + hydrationMeasure.startTime + hydrationMeasure.duration);\n        }\n    }\n    if (onPerfEntry) {\n        performance.getEntriesByName(performanceMeasures.hydration).forEach(onPerfEntry);\n    }\n    clearMarks();\n}\nfunction markRenderComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterRender) // mark end of render\n    ;\n    const navStartEntries = performance.getEntriesByName(performanceMarks.routeChange, \"mark\");\n    if (!navStartEntries.length) return;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, \"mark\").length;\n    if (hasBeforeRenderMark) {\n        performance.measure(performanceMeasures.routeChangeToRender, navStartEntries[0].name, performanceMarks.beforeRender);\n        performance.measure(performanceMeasures.render, performanceMarks.beforeRender, performanceMarks.afterRender);\n        if (onPerfEntry) {\n            performance.getEntriesByName(performanceMeasures.render).forEach(onPerfEntry);\n            performance.getEntriesByName(performanceMeasures.routeChangeToRender).forEach(onPerfEntry);\n        }\n    }\n    clearMarks();\n    [\n        performanceMeasures.routeChangeToRender,\n        performanceMeasures.render\n    ].forEach((measure)=>performance.clearMeasures(measure));\n}\nfunction renderReactElement(domEl, fn) {\n    // mark start of hydrate/render\n    if (_utils.ST) {\n        performance.mark(performanceMarks.beforeRender);\n    }\n    const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete);\n    if (!reactRoot) {\n        // Unlike with createRoot, you don't need a separate root.render() call here\n        reactRoot = _client.default.hydrateRoot(domEl, reactEl, {\n            onRecoverableError: _onrecoverableerror.default\n        });\n        // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n        shouldHydrate = false;\n    } else {\n        const startTransition = _react.default.startTransition;\n        startTransition(()=>{\n            reactRoot.render(reactEl);\n        });\n    }\n}\nfunction Root(param) {\n    _s2();\n    let { callbacks, children } = param;\n    // We use `useLayoutEffect` to guarantee the callbacks are executed\n    // as soon as React flushes the update\n    _react.default.useLayoutEffect(()=>callbacks.forEach((callback)=>callback()), [\n        callbacks\n    ]);\n    // We should ask to measure the Web Vitals after rendering completes so we\n    // don't cause any hydration delay:\n    _react.default.useEffect(()=>{\n        (0, _performancerelayer.default)(onPerfEntry);\n    }, []);\n    if (false) {}\n    return children;\n}\n_s2(Root, \"Gjgl5rfcc2T4sFnfEMfRvL6K4Q4=\");\n_c2 = Root;\nfunction doRender(input) {\n    let { App, Component, props, err } = input;\n    let styleSheets = \"initial\" in input ? undefined : input.styleSheets;\n    Component = Component || lastAppProps.Component;\n    props = props || lastAppProps.props;\n    const appProps = {\n        ...props,\n        Component,\n        err,\n        router\n    };\n    // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n    lastAppProps = appProps;\n    let canceled = false;\n    let resolvePromise;\n    const renderPromise = new Promise((resolve, reject)=>{\n        if (lastRenderReject) {\n            lastRenderReject();\n        }\n        resolvePromise = ()=>{\n            lastRenderReject = null;\n            resolve();\n        };\n        lastRenderReject = ()=>{\n            canceled = true;\n            lastRenderReject = null;\n            const error = new Error(\"Cancel rendering route\");\n            error.cancelled = true;\n            reject(error);\n        };\n    });\n    // This function has a return type to ensure it doesn't start returning a\n    // Promise. It should remain synchronous.\n    function onStart() {\n        if (!styleSheets || // We use `style-loader` in development, so we don't need to do anything\n        // unless we're in production:\n        \"development\" !== \"production\") {\n            return false;\n        }\n        const currentStyleTags = looseToArray(document.querySelectorAll(\"style[data-n-href]\"));\n        const currentHrefs = new Set(currentStyleTags.map((tag)=>tag.getAttribute(\"data-n-href\")));\n        const noscript = document.querySelector(\"noscript[data-n-css]\");\n        const nonce = noscript == null ? void 0 : noscript.getAttribute(\"data-n-css\");\n        styleSheets.forEach((param)=>{\n            let { href, text } = param;\n            if (!currentHrefs.has(href)) {\n                const styleTag = document.createElement(\"style\");\n                styleTag.setAttribute(\"data-n-href\", href);\n                styleTag.setAttribute(\"media\", \"x\");\n                if (nonce) {\n                    styleTag.setAttribute(\"nonce\", nonce);\n                }\n                document.head.appendChild(styleTag);\n                styleTag.appendChild(document.createTextNode(text));\n            }\n        });\n        return true;\n    }\n    function onHeadCommit() {\n        if (// unless we're in production:\n        false) {}\n        if (input.scroll) {\n            const { x, y } = input.scroll;\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                window.scrollTo(x, y);\n            });\n        }\n    }\n    function onRootCommit() {\n        resolvePromise();\n    }\n    onStart();\n    const elem = /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(Head, {\n        callback: onHeadCommit\n    }), /*#__PURE__*/ _react.default.createElement(AppContainer, null, renderApp(App, appProps), /*#__PURE__*/ _react.default.createElement(_portal.Portal, {\n        type: \"next-route-announcer\"\n    }, /*#__PURE__*/ _react.default.createElement(_routeannouncer.RouteAnnouncer, null))));\n    // We catch runtime errors using componentDidCatch which will trigger renderError\n    renderReactElement(appElement, (callback)=>/*#__PURE__*/ _react.default.createElement(Root, {\n            callbacks: [\n                callback,\n                onRootCommit\n            ]\n        },  true ? /*#__PURE__*/ _react.default.createElement(_react.default.StrictMode, null, elem) : 0));\n    return renderPromise;\n}\nasync function render(renderingProps) {\n    if (renderingProps.err) {\n        await renderError(renderingProps);\n        return;\n    }\n    try {\n        await doRender(renderingProps);\n    } catch (err) {\n        const renderErr = (0, _iserror.getProperError)(err);\n        // bubble up cancelation errors\n        if (renderErr.cancelled) {\n            throw renderErr;\n        }\n        if (true) {\n            // Ensure this error is displayed in the overlay in development\n            setTimeout(()=>{\n                throw renderErr;\n            });\n        }\n        await renderError({\n            ...renderingProps,\n            err: renderErr\n        });\n    }\n}\nasync function hydrate(opts) {\n    let initialErr = initialData.err;\n    try {\n        const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint(\"/_app\");\n        if (\"error\" in appEntrypoint) {\n            throw appEntrypoint.error;\n        }\n        const { component: app, exports: mod } = appEntrypoint;\n        CachedApp = app;\n        if (mod && mod.reportWebVitals) {\n            onPerfEntry = (param)=>{\n                let { id, name, startTime, value, duration, entryType, entries, attribution } = param;\n                // Combines timestamp with random number for unique ID\n                const uniqueID = Date.now() + \"-\" + (Math.floor(Math.random() * (9e12 - 1)) + 1e12);\n                let perfStartEntry;\n                if (entries && entries.length) {\n                    perfStartEntry = entries[0].startTime;\n                }\n                const webVitals = {\n                    id: id || uniqueID,\n                    name,\n                    startTime: startTime || perfStartEntry,\n                    value: value == null ? duration : value,\n                    label: entryType === \"mark\" || entryType === \"measure\" ? \"custom\" : \"web-vital\"\n                };\n                if (attribution) {\n                    webVitals.attribution = attribution;\n                }\n                mod.reportWebVitals(webVitals);\n            };\n        }\n        const pageEntrypoint = // error, so we need to skip waiting for the entrypoint.\n         true && initialData.err ? {\n            error: initialData.err\n        } : await pageLoader.routeLoader.whenEntrypoint(initialData.page);\n        if (\"error\" in pageEntrypoint) {\n            throw pageEntrypoint.error;\n        }\n        CachedComponent = pageEntrypoint.component;\n        if (true) {\n            const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n            if (!isValidElementType(CachedComponent)) {\n                throw new Error('The default export is not a React Component in page: \"' + initialData.page + '\"');\n            }\n        }\n    } catch (error) {\n        // This catches errors like throwing in the top level of a module\n        initialErr = (0, _iserror.getProperError)(error);\n    }\n    if (true) {\n        const { getServerError } = __webpack_require__(/*! next/dist/compiled/@next/react-dev-overlay/dist/client */ \"./node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js\");\n        // Server-side runtime errors need to be re-thrown on the client-side so\n        // that the overlay is rendered.\n        if (initialErr) {\n            if (initialErr === initialData.err) {\n                setTimeout(()=>{\n                    let error;\n                    try {\n                        // Generate a new error object. We `throw` it because some browsers\n                        // will set the `stack` when thrown, and we want to ensure ours is\n                        // not overridden when we re-throw it below.\n                        throw new Error(initialErr.message);\n                    } catch (e) {\n                        error = e;\n                    }\n                    error.name = initialErr.name;\n                    error.stack = initialErr.stack;\n                    throw getServerError(error, initialErr.source);\n                });\n            } else {\n                setTimeout(()=>{\n                    throw initialErr;\n                });\n            }\n        }\n    }\n    if (window.__NEXT_PRELOADREADY) {\n        await window.__NEXT_PRELOADREADY(initialData.dynamicIds);\n    }\n    router = (0, _router.createRouter)(initialData.page, initialData.query, asPath, {\n        initialProps: initialData.props,\n        pageLoader,\n        App: CachedApp,\n        Component: CachedComponent,\n        wrapApp,\n        err: initialErr,\n        isFallback: Boolean(initialData.isFallback),\n        subscription: (info, App, scroll)=>render(Object.assign({}, info, {\n                App,\n                scroll\n            })),\n        locale: initialData.locale,\n        locales: initialData.locales,\n        defaultLocale,\n        domainLocales: initialData.domainLocales,\n        isPreview: initialData.isPreview\n    });\n    initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise;\n    const renderCtx = {\n        App: CachedApp,\n        initial: true,\n        Component: CachedComponent,\n        props: initialData.props,\n        err: initialErr\n    };\n    if (opts == null ? void 0 : opts.beforeRender) {\n        await opts.beforeRender();\n    }\n    render(renderCtx);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsR0FBZ0I7Ozs7OztBQUNuQ0EsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0YsS0FBTUMsQ0FBQUEsQ0FNTjtBQUNBLFNBQVNNLFFBQVFDLE1BQU0sRUFBRUMsR0FBRztJQUN4QixJQUFJLElBQUlDLFFBQVFELElBQUlaLE9BQU9DLGNBQWMsQ0FBQ1UsUUFBUUUsTUFBTTtRQUNwREMsWUFBWTtRQUNaQyxLQUFLSCxHQUFHLENBQUNDLEtBQUs7SUFDbEI7QUFDSjtBQUNBSCxRQUFRUixTQUFTO0lBQ2JHLFNBQVM7UUFDTCxPQUFPQTtJQUNYO0lBQ0FDLFFBQVE7UUFDSixPQUFPQTtJQUNYO0lBQ0FDLFNBQVM7UUFDTCxPQUFPQTtJQUNYO0lBQ0FDLFlBQVk7UUFDUixPQUFPQTtJQUNYO0lBQ0FDLFNBQVM7UUFDTCxPQUFPQTtJQUNYO0FBQ0o7QUFDQSxNQUFNTywyQkFBMkJDLG1CQUFPQSxDQUFDLDRHQUF5QztBQUNsRkEsbUJBQU9BLENBQUMsdUdBQW9DO0FBQzVDLE1BQU1DLFNBQVMsV0FBVyxHQUFHRix5QkFBeUJHLENBQUMsQ0FBQ0YsbUJBQU9BLENBQUMsNENBQU87QUFDdkUsTUFBTUcsVUFBVSxXQUFXLEdBQUdKLHlCQUF5QkcsQ0FBQyxDQUFDRixtQkFBT0EsQ0FBQyw0REFBa0I7QUFDbkYsTUFBTUksbUNBQW1DSixtQkFBT0EsQ0FBQyxxSUFBbUQ7QUFDcEcsTUFBTUssUUFBUSxXQUFXLEdBQUdOLHlCQUF5QkcsQ0FBQyxDQUFDRixtQkFBT0EsQ0FBQyx1RUFBb0I7QUFDbkYsTUFBTU0sOEJBQThCTixtQkFBT0EsQ0FBQyx5SEFBNkM7QUFDekYsTUFBTU8sc0JBQXNCUCxtQkFBT0EsQ0FBQyxpSUFBaUQ7QUFDckYsTUFBTVEsYUFBYVIsbUJBQU9BLENBQUMsNkdBQXVDO0FBQ2xFLE1BQU1TLGVBQWVULG1CQUFPQSxDQUFDLCtHQUF3QztBQUNyRSxNQUFNVSx5QkFBeUJWLG1CQUFPQSxDQUFDLDZHQUF1QztBQUM5RSxNQUFNVyxTQUFTWCxtQkFBT0EsQ0FBQyx5RUFBcUI7QUFDNUMsTUFBTVksVUFBVVosbUJBQU9BLENBQUMsaUVBQVU7QUFDbEMsTUFBTWEsZUFBZSxXQUFXLEdBQUdkLHlCQUF5QkcsQ0FBQyxDQUFDRixtQkFBT0EsQ0FBQyx1RUFBZ0I7QUFDdEYsTUFBTWMsY0FBYyxXQUFXLEdBQUdmLHlCQUF5QkcsQ0FBQyxDQUFDRixtQkFBT0EsQ0FBQyxxRUFBZTtBQUNwRixNQUFNZSxzQkFBc0IsV0FBVyxHQUFHaEIseUJBQXlCRyxDQUFDLENBQUNGLG1CQUFPQSxDQUFDLHFGQUF1QjtBQUNwRyxNQUFNZ0Isa0JBQWtCaEIsbUJBQU9BLENBQUMsNkVBQW1CO0FBQ25ELE1BQU1pQixVQUFVakIsbUJBQU9BLENBQUMsMkRBQVU7QUFDbEMsTUFBTWtCLFdBQVdsQixtQkFBT0EsQ0FBQyxpRUFBaUI7QUFDMUMsTUFBTW1CLG1DQUFtQ25CLG1CQUFPQSxDQUFDLHFJQUFtRDtBQUNwRyxNQUFNb0Isa0JBQWtCcEIsbUJBQU9BLENBQUMsK0VBQW9CO0FBQ3BELE1BQU1xQixlQUFlckIsbUJBQU9BLENBQUMseUVBQWlCO0FBQzlDLE1BQU1zQixpQ0FBaUN0QixtQkFBT0EsQ0FBQyxpSUFBaUQ7QUFDaEcsTUFBTXVCLFlBQVl2QixtQkFBT0EsQ0FBQyw2RkFBK0I7QUFDekQsTUFBTXdCLG1DQUFtQ3hCLG1CQUFPQSxDQUFDLHFJQUFtRDtBQUNwRyxNQUFNeUIsc0JBQXNCLFdBQVcsR0FBRzFCLHlCQUF5QkcsQ0FBQyxDQUFDRixtQkFBT0EsQ0FBQyx1RkFBd0I7QUFDckcsTUFBTTBCLFVBQVUsV0FBVyxHQUFHM0IseUJBQXlCRyxDQUFDLENBQUNGLG1CQUFPQSxDQUFDLDJFQUFrQjtBQUNuRixNQUFNMkIsa0JBQWtCLFdBQVcsR0FBRzVCLHlCQUF5QkcsQ0FBQyxDQUFDRixtQkFBT0EsQ0FBQywrRkFBNEI7QUFDckcsTUFBTVosVUFBVTtBQUNoQixJQUFJQztBQUNKLE1BQU1DLFVBQVUsQ0FBQyxHQUFHZSxNQUFNdUIsT0FBTztBQUNqQyxNQUFNQyxlQUFlLENBQUNDLFFBQVEsRUFBRSxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQ0Y7QUFDNUMsSUFBSUc7QUFDSixJQUFJQyxnQkFBZ0JDO0FBQ3BCLElBQUlDO0FBQ0osSUFBSUM7QUFDSixJQUFJQztBQUNKLElBQUlDO0FBQ0osSUFBSUMsMkJBQTJCO0FBQy9CLElBQUlDO0FBQ0osSUFBSUM7QUFDSixJQUFJQztBQUNKLElBQUlDLFdBQVdDO0FBQ2YsSUFBSUM7QUFDSixNQUFNQyxrQkFBa0I5QyxPQUFPMkIsT0FBTyxDQUFDb0IsU0FBUztJQUM1Q0Msa0JBQWtCQyxZQUFZLEVBQUVDLElBQUksRUFBRTtRQUNsQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsRUFBRSxDQUFDSCxjQUFjQztJQUNoQztJQUNBRyxvQkFBb0I7UUFDaEIsSUFBSSxDQUFDQyxZQUFZO1FBQ2pCLDBDQUEwQztRQUMxQyx5RUFBeUU7UUFDekUsb0VBQW9FO1FBQ3BFLHNEQUFzRDtRQUN0RCxxRUFBcUU7UUFDckUsa0VBQWtFO1FBQ2xFLElBQUlsRSxPQUFPbUUsS0FBSyxJQUFLdkIsQ0FBQUEsWUFBWXdCLFVBQVUsSUFBSXhCLFlBQVl5QixVQUFVLElBQUssRUFBQyxHQUFHbEQsV0FBV21ELGNBQWMsRUFBRXRFLE9BQU91RSxRQUFRLEtBQUtDLFNBQVNDLE1BQU0sSUFBSUMsS0FBK0IsSUFBSXZCLHdCQUF1QixLQUFNUCxZQUFZbUIsS0FBSyxJQUFJbkIsWUFBWW1CLEtBQUssQ0FBQ2MsT0FBTyxJQUFLTCxDQUFBQSxTQUFTQyxNQUFNLElBQUlDLEtBQStCLElBQUl2Qix3QkFBdUIsQ0FBQyxHQUFJO1lBQ2pWLDJDQUEyQztZQUMzQ25ELE9BQU84RSxPQUFPLENBQUM5RSxPQUFPdUUsUUFBUSxHQUFHLE1BQU1RLE9BQU8sQ0FBQyxHQUFHM0QsYUFBYTRELE1BQU0sRUFBRSxDQUFDLEdBQUc1RCxhQUFhNkQsc0JBQXNCLEVBQUVqRixPQUFPa0YsS0FBSyxHQUFHLElBQUlDLGdCQUFnQlgsU0FBU0MsTUFBTSxLQUFLMUIsUUFBUTtnQkFDM0ssYUFBYTtnQkFDYiwwREFBMEQ7Z0JBQzFELG9FQUFvRTtnQkFDcEUsNENBQTRDO2dCQUM1Q3FDLElBQUk7Z0JBQ0osbUVBQW1FO2dCQUNuRSxlQUFlO2dCQUNmLG1FQUFtRTtnQkFDbkUseUNBQXlDO2dCQUN6Q0MsU0FBUyxDQUFDekMsWUFBWXdCLFVBQVUsSUFBSSxDQUFDakI7WUFDekMsR0FBR21DLEtBQUssQ0FBQyxDQUFDQztnQkFDTixJQUFJLENBQUNBLElBQUlDLFNBQVMsRUFBRSxNQUFNRDtZQUM5QjtRQUNKO0lBQ0o7SUFDQUUscUJBQXFCO1FBQ2pCLElBQUksQ0FBQ3ZCLFlBQVk7SUFDckI7SUFDQUEsZUFBZTtRQUNYLElBQUksRUFBRXdCLElBQUksRUFBRSxHQUFHbEI7UUFDZmtCLE9BQU9BLFFBQVFBLEtBQUtDLFNBQVMsQ0FBQztRQUM5QixJQUFJLENBQUNELE1BQU07UUFDWCxNQUFNRSxLQUFLQyxTQUFTQyxjQUFjLENBQUNKO1FBQ25DLElBQUksQ0FBQ0UsSUFBSTtRQUNULDJEQUEyRDtRQUMzRCw0QkFBNEI7UUFDNUJHLFdBQVcsSUFBSUgsR0FBR0ksY0FBYyxJQUFJO0lBQ3hDO0lBQ0FDLFNBQVM7UUFDTCxJQUFJdkIsS0FBcUMsRUFBRSxFQUUxQyxNQUFNO1lBQ0gsTUFBTSxFQUFFeUIsZUFBZSxFQUFFLEdBQUd4RixtQkFBT0EsQ0FBQyx3SUFBd0Q7WUFDNUYsT0FBTyxXQUFXLEdBQUdDLE9BQU8yQixPQUFPLENBQUM2RCxhQUFhLENBQUNELGlCQUFpQixNQUFNLElBQUksQ0FBQ3BDLEtBQUssQ0FBQ21DLFFBQVE7UUFDaEc7SUFDSjtBQUNKO0FBQ0EsZUFBZWhHLFdBQVdtRyxJQUFJO0lBQzFCLElBQUlBLFNBQVMsS0FBSyxHQUFHQSxPQUFPLENBQUM7SUFDN0JoRSxRQUFRRSxPQUFPLENBQUMrRCxTQUFTLENBQUNoRSxnQkFBZ0JDLE9BQU87SUFDakQsZ0VBQWdFO0lBQ2hFLElBQUltQyxJQUFzQyxFQUFFO1FBQ3hDcEIsWUFBWStDLEtBQUsvQyxTQUFTO0lBQzlCO0lBQ0FWLGNBQWMyRCxLQUFLQyxLQUFLLENBQUNYLFNBQVNDLGNBQWMsQ0FBQyxpQkFBaUJXLFdBQVc7SUFDN0VDLE9BQU9DLGFBQWEsR0FBRy9EO0lBQ3ZCQyxnQkFBZ0JELFlBQVlDLGFBQWE7SUFDekMsTUFBTStELFNBQVNoRSxZQUFZaUUsV0FBVyxJQUFJO0lBQzFDQyxLQUFLQyx3QkFBd0IsQ0FBQyxLQUFLSCxTQUFTLFdBQVcscUJBQXFCOztJQUU1RSw0REFBNEQ7SUFDM0QsSUFBR3ZGLHVCQUF1QjJGLFNBQVMsRUFBRTtRQUNsQ0MscUJBQXFCLENBQUM7UUFDdEJDLHFCQUFxQnRFLFlBQVl1RSxhQUFhLElBQUksQ0FBQztJQUN2RDtJQUNBcEUsU0FBUyxDQUFDLEdBQUd6QixPQUFPOEYsTUFBTTtJQUMxQix1REFBdUQ7SUFDdkQsSUFBSSxDQUFDLEdBQUdwRixhQUFhcUYsV0FBVyxFQUFFdEUsU0FBUztRQUN2Q0EsU0FBUyxDQUFDLEdBQUdoQixnQkFBZ0J1RixjQUFjLEVBQUV2RTtJQUNqRDtJQUNBLElBQUkyQixLQUErQixFQUFFLEVBeUJwQztJQUNELElBQUk5QixZQUFZd0YsWUFBWSxFQUFFO1FBQzFCLE1BQU0sRUFBRUMsZ0JBQWdCLEVBQUUsR0FBRzFILG1CQUFPQSxDQUFDLDJEQUFVO1FBQy9DMEgsaUJBQWlCekYsWUFBWXdGLFlBQVk7SUFDN0M7SUFDQXBGLGFBQWEsSUFBSXZCLFlBQVljLE9BQU8sQ0FBQ0ssWUFBWTBGLE9BQU8sRUFBRTFCO0lBQzFELE1BQU0yQixXQUFXLENBQUNDO1FBQ2QsSUFBSSxDQUFDQyxHQUFHQyxFQUFFLEdBQUdGO1FBQ2IsT0FBT3hGLFdBQVcyRixXQUFXLENBQUNDLFlBQVksQ0FBQ0gsR0FBR0M7SUFDbEQ7SUFDQSxJQUFJaEMsT0FBT21DLFFBQVEsRUFBRTtRQUNqQiwyRUFBMkU7UUFDM0UscUVBQXFFO1FBQ3JFbkMsT0FBT21DLFFBQVEsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLElBQUloRCxXQUFXLElBQUl3QyxTQUFTUSxJQUFJO0lBQ3pEO0lBQ0FyQyxPQUFPbUMsUUFBUSxHQUFHLEVBQUU7SUFDcEJuQyxPQUFPbUMsUUFBUSxDQUFDRyxJQUFJLEdBQUdUO0lBQ3ZCckYsY0FBYyxDQUFDLEdBQUcxQixhQUFhZSxPQUFPO0lBQ3RDVyxZQUFZK0YsUUFBUSxHQUFHO1FBQ25CLE9BQU9qSixPQUFPbUUsS0FBSztJQUN2QjtJQUNBbEIsYUFBYTRDLFNBQVNDLGNBQWMsQ0FBQztJQUNyQyxPQUFPO1FBQ0hlLGFBQWFEO0lBQ2pCO0FBQ0o7QUFDQSxTQUFTc0MsVUFBVUMsR0FBRyxFQUFFQyxRQUFRO0lBQzVCLE9BQU8sV0FBVyxHQUFHeEksT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQytDLEtBQUtDO0FBQzNEO0FBQ0EsU0FBU0MsYUFBYWIsS0FBSzs7SUFDdkIsSUFBSSxFQUFFdEMsUUFBUSxFQUFFLEdBQUdzQztJQUNuQiw4REFBOEQ7SUFDOUQsTUFBTWMsc0JBQXNCMUksT0FBTzJCLE9BQU8sQ0FBQ2dILE9BQU8sQ0FBQztRQUMvQyxPQUFPLENBQUMsR0FBR3JILFVBQVVzSCx5QkFBeUIsRUFBRXhKO0lBQ3BELEdBQUcsRUFBRTtJQUNMLElBQUl5SjtJQUNKLE9BQU8sV0FBVyxHQUFHN0ksT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQzFDLFdBQVc7UUFDekRNLElBQUksQ0FBQzBGLFFBQ0QsbUVBQW1FO1lBQ25FQyxZQUFZO2dCQUNSUixLQUFLNUY7Z0JBQ0xnQyxLQUFLbUU7WUFDVCxHQUFHcEUsS0FBSyxDQUFDLENBQUNDLE1BQU1xRSxRQUFRRixLQUFLLENBQUMsMEJBQTBCbkU7SUFDaEUsR0FBRyxXQUFXLEdBQUczRSxPQUFPMkIsT0FBTyxDQUFDNkQsYUFBYSxDQUFDbkUsK0JBQStCNEgsZ0JBQWdCLENBQUNDLFFBQVEsRUFBRTtRQUNwR2pLLE9BQU95SjtJQUNYLEdBQUcsV0FBVyxHQUFHMUksT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ2pFLGlDQUFpQzRILG1CQUFtQixDQUFDRCxRQUFRLEVBQUU7UUFDekdqSyxPQUFPLENBQUMsR0FBR3FDLFVBQVU4SCxvQkFBb0IsRUFBRWhLO0lBQy9DLEdBQUcsV0FBVyxHQUFHWSxPQUFPMkIsT0FBTyxDQUFDNkQsYUFBYSxDQUFDbEUsVUFBVStILDhCQUE4QixFQUFFO1FBQ3BGakssUUFBUUE7UUFDUmtLLGNBQWMsQ0FBQ1QsaUNBQWlDM0MsS0FBS0gsYUFBYSxDQUFDd0QsVUFBVSxLQUFLLE9BQU9WLGlDQUFpQztJQUM5SCxHQUFHLFdBQVcsR0FBRzdJLE9BQU8yQixPQUFPLENBQUM2RCxhQUFhLENBQUNqRSxpQ0FBaUNpSSxpQkFBaUIsQ0FBQ04sUUFBUSxFQUFFO1FBQ3ZHakssT0FBTyxDQUFDLEdBQUdxQyxVQUFVbUksa0JBQWtCLEVBQUVySztJQUM3QyxHQUFHLFdBQVcsR0FBR1ksT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ25GLDRCQUE0QnFKLGFBQWEsQ0FBQ1IsUUFBUSxFQUFFO1FBQzlGakssT0FBTyxDQUFDLEdBQUcrQixRQUFRMkksd0JBQXdCLEVBQUV2SztJQUNqRCxHQUFHLFdBQVcsR0FBR1ksT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ3JGLGlDQUFpQ3lKLGtCQUFrQixDQUFDVixRQUFRLEVBQUU7UUFDeEdqSyxPQUFPcUQ7SUFDWCxHQUFHLFdBQVcsR0FBR3RDLE9BQU8yQixPQUFPLENBQUM2RCxhQUFhLENBQUN0RSxpQ0FBaUMySSxrQkFBa0IsQ0FBQ1gsUUFBUSxFQUFFO1FBQ3hHakssT0FBTzZFLDBOQUE2QjtJQUN4QyxHQUFHd0I7QUFDUDtHQTlCU21EO0tBQUFBO0FBK0JULE1BQU1zQixVQUFVLENBQUN4QixNQUFNLENBQUN5QjtRQUNoQixNQUFNeEIsV0FBVztZQUNiLEdBQUd3QixlQUFlO1lBQ2xCakgsV0FBV0Y7WUFDWDhCLEtBQUszQyxZQUFZMkMsR0FBRztZQUNwQnZGO1FBQ0o7UUFDQSxPQUFPLFdBQVcsR0FBR1ksT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ2lELGNBQWMsTUFBTUgsVUFBVUMsS0FBS0M7SUFDekY7QUFDSixvREFBb0Q7QUFDcEQsZ0RBQWdEO0FBQ2hELHdEQUF3RDtBQUN4RCxTQUFTTyxZQUFZa0IsZ0JBQWdCO0lBQ2pDLElBQUksRUFBRTFCLEdBQUcsRUFBRTVELEdBQUcsRUFBRSxHQUFHc0Y7SUFDbkIsMERBQTBEO0lBQzFELCtGQUErRjtJQUMvRixJQUFJbkcsSUFBcUMsRUFBRTtRQUN2Qyw0REFBNEQ7UUFDNUQsc0VBQXNFO1FBQ3RFcEIsVUFBVXdILG9CQUFvQjtRQUM5Qix1RUFBdUU7UUFDdkUsaUJBQWlCO1FBQ2pCLGlDQUFpQztRQUNqQyxtRUFBbUU7UUFDbkUsT0FBT0MsU0FBUztZQUNaNUIsS0FBSyxJQUFJO1lBQ1RwRixPQUFPLENBQUM7WUFDUkosV0FBVyxJQUFJO1lBQ2ZxSCxhQUFhLEVBQUU7UUFDbkI7SUFDSjtJQUNBLHNGQUFzRjtJQUN0RnBCLFFBQVFGLEtBQUssQ0FBQ25FO0lBQ2RxRSxRQUFRRixLQUFLLENBQUM7SUFDZCxPQUFPMUcsV0FBV2lJLFFBQVEsQ0FBQyxXQUFXQyxJQUFJLENBQUMsQ0FBQzFDO1FBQ3hDLElBQUksRUFBRTJDLE1BQU1DLGNBQWMsRUFBRUosV0FBVyxFQUFFLEdBQUd4QztRQUM1QyxPQUFPLENBQUNwRixnQkFBZ0IsT0FBTyxLQUFLLElBQUlBLGFBQWFPLFNBQVMsTUFBTXlILGlCQUFpQix5RkFBTyxpRUFBaUIsSUFBRUYsSUFBSSxDQUFDLENBQUNHO1lBQ2pILE9BQU8seUZBQU8sNkRBQWUsSUFBRUgsSUFBSSxDQUFDLENBQUNJO2dCQUNqQ25DLE1BQU1tQyxVQUFVL0ksT0FBTztnQkFDdkJzSSxpQkFBaUIxQixHQUFHLEdBQUdBO2dCQUN2QixPQUFPa0M7WUFDWDtRQUNKLEdBQUdILElBQUksQ0FBQyxDQUFDSyxJQUFLO2dCQUNOSCxnQkFBZ0JHLEVBQUVoSixPQUFPO2dCQUN6QnlJLGFBQWEsRUFBRTtZQUNuQixNQUFNO1lBQ05JO1lBQ0FKO1FBQ0o7SUFDSixHQUFHRSxJQUFJLENBQUMsQ0FBQzFDO1FBQ0wsSUFBSSxFQUFFNEMsY0FBYyxFQUFFSixXQUFXLEVBQUUsR0FBR3hDO1FBQ3RDLElBQUlnRDtRQUNKLDhFQUE4RTtRQUM5RSxrRkFBa0Y7UUFDbEYseUVBQXlFO1FBQ3pFLE1BQU1DLFVBQVVkLFFBQVF4QjtRQUN4QixNQUFNdUMsU0FBUztZQUNYL0gsV0FBV3lIO1lBQ1hLO1lBQ0F6TDtZQUNBMkwsS0FBSztnQkFDRHBHO2dCQUNBaEIsVUFBVTNCLFlBQVl1SSxJQUFJO2dCQUMxQmpHLE9BQU90QyxZQUFZc0MsS0FBSztnQkFDeEJuQztnQkFDQTBJO1lBQ0o7UUFDSjtRQUNBLE9BQU9HLFFBQVFDLE9BQU8sQ0FBQyxDQUFDLENBQUNMLDBCQUEwQlgsaUJBQWlCOUcsS0FBSyxLQUFLLE9BQU8sS0FBSyxJQUFJeUgsd0JBQXdCakcsR0FBRyxJQUFJc0YsaUJBQWlCOUcsS0FBSyxHQUFHLENBQUMsR0FBR3pDLE9BQU93SyxtQkFBbUIsRUFBRTNDLEtBQUt1QyxTQUFTUixJQUFJLENBQUMsQ0FBQ2EsWUFDdE0sbUVBQW1FO1lBQ25FaEIsU0FBUztnQkFDTCxHQUFHRixnQkFBZ0I7Z0JBQ25CdEY7Z0JBQ0E1QixXQUFXeUg7Z0JBQ1hKO2dCQUNBakgsT0FBT2dJO1lBQ1g7SUFDUjtBQUNKO0FBQ0EsbUVBQW1FO0FBQ25FLHlEQUF5RDtBQUN6RCxTQUFTQyxLQUFLeEQsS0FBSzs7SUFDZixJQUFJLEVBQUV5RCxRQUFRLEVBQUUsR0FBR3pEO0lBQ25CLGlFQUFpRTtJQUNqRSx1Q0FBdUM7SUFDdkM1SCxPQUFPMkIsT0FBTyxDQUFDMkosZUFBZSxDQUFDLElBQUlELFlBQVk7UUFDM0NBO0tBQ0g7SUFDRCxPQUFPO0FBQ1g7SUFSU0Q7TUFBQUE7QUFTVCxNQUFNRyxtQkFBbUI7SUFDckJDLGlCQUFpQjtJQUNqQkMsY0FBYztJQUNkQyxhQUFhO0lBQ2JDLGNBQWM7SUFDZEMsYUFBYTtBQUNqQjtBQUNBLE1BQU1DLHNCQUFzQjtJQUN4QkMsV0FBVztJQUNYQyxpQkFBaUI7SUFDakJDLHFCQUFxQjtJQUNyQjNHLFFBQVE7QUFDWjtBQUNBLElBQUk0RyxZQUFZO0FBQ2hCLG1EQUFtRDtBQUNuRCxJQUFJQyxnQkFBZ0I7QUFDcEIsU0FBU0M7SUFDTDtRQUNJWixpQkFBaUJFLFlBQVk7UUFDN0JGLGlCQUFpQkksWUFBWTtRQUM3QkosaUJBQWlCRyxXQUFXO1FBQzVCSCxpQkFBaUJLLFdBQVc7S0FDL0IsQ0FBQ1EsT0FBTyxDQUFDLENBQUNDLE9BQU9DLFlBQVlILFVBQVUsQ0FBQ0U7QUFDN0M7QUFDQSxTQUFTRTtJQUNMLElBQUksQ0FBQzdMLE9BQU84TCxFQUFFLEVBQUU7SUFDaEJGLFlBQVlELElBQUksQ0FBQ2QsaUJBQWlCSSxZQUFZLEVBQUUsd0JBQXdCOztJQUV4RSxNQUFNYyxzQkFBc0JILFlBQVlJLGdCQUFnQixDQUFDbkIsaUJBQWlCRSxZQUFZLEVBQUUsUUFBUWtCLE1BQU07SUFDdEcsSUFBSUYscUJBQXFCO1FBQ3JCLE1BQU1HLHlCQUF5Qk4sWUFBWU8sT0FBTyxDQUFDaEIsb0JBQW9CRSxlQUFlLEVBQUVSLGlCQUFpQkMsZUFBZSxFQUFFRCxpQkFBaUJFLFlBQVk7UUFDdkosTUFBTXFCLG1CQUFtQlIsWUFBWU8sT0FBTyxDQUFDaEIsb0JBQW9CQyxTQUFTLEVBQUVQLGlCQUFpQkUsWUFBWSxFQUFFRixpQkFBaUJJLFlBQVk7UUFDeEksSUFBSTdILEtBQXNDLElBQUkseUZBQXlGO1FBQ3ZJOEksMkJBQTJCMUssYUFBYTRLLHFCQUFxQjVLLFdBQVc7WUFDcEVULFFBQVFFLE9BQU8sQ0FBQ29MLFNBQVMsQ0FBQywyQkFBMkI7Z0JBQ2pEQyxXQUFXVixZQUFZVyxVQUFVLEdBQUdMLHVCQUF1QkksU0FBUztnQkFDcEVFLFlBQVk7b0JBQ1J2SixVQUFVQyxTQUFTRCxRQUFRO29CQUMzQlcsT0FBT1YsU0FBU0MsTUFBTTtnQkFDMUI7WUFDSixHQUFHc0osR0FBRyxDQUFDYixZQUFZVyxVQUFVLEdBQUdILGlCQUFpQkUsU0FBUyxHQUFHRixpQkFBaUJNLFFBQVE7UUFDMUY7SUFDSjtJQUNBLElBQUl4SyxhQUFhO1FBQ2IwSixZQUFZSSxnQkFBZ0IsQ0FBQ2Isb0JBQW9CQyxTQUFTLEVBQUVNLE9BQU8sQ0FBQ3hKO0lBQ3hFO0lBQ0F1SjtBQUNKO0FBQ0EsU0FBU2tCO0lBQ0wsSUFBSSxDQUFDM00sT0FBTzhMLEVBQUUsRUFBRTtJQUNoQkYsWUFBWUQsSUFBSSxDQUFDZCxpQkFBaUJHLFdBQVcsRUFBRSxxQkFBcUI7O0lBRXBFLE1BQU00QixrQkFBa0JoQixZQUFZSSxnQkFBZ0IsQ0FBQ25CLGlCQUFpQkssV0FBVyxFQUFFO0lBQ25GLElBQUksQ0FBQzBCLGdCQUFnQlgsTUFBTSxFQUFFO0lBQzdCLE1BQU1GLHNCQUFzQkgsWUFBWUksZ0JBQWdCLENBQUNuQixpQkFBaUJFLFlBQVksRUFBRSxRQUFRa0IsTUFBTTtJQUN0RyxJQUFJRixxQkFBcUI7UUFDckJILFlBQVlPLE9BQU8sQ0FBQ2hCLG9CQUFvQkcsbUJBQW1CLEVBQUVzQixlQUFlLENBQUMsRUFBRSxDQUFDM04sSUFBSSxFQUFFNEwsaUJBQWlCRSxZQUFZO1FBQ25IYSxZQUFZTyxPQUFPLENBQUNoQixvQkFBb0J4RyxNQUFNLEVBQUVrRyxpQkFBaUJFLFlBQVksRUFBRUYsaUJBQWlCRyxXQUFXO1FBQzNHLElBQUk5SSxhQUFhO1lBQ2IwSixZQUFZSSxnQkFBZ0IsQ0FBQ2Isb0JBQW9CeEcsTUFBTSxFQUFFK0csT0FBTyxDQUFDeEo7WUFDakUwSixZQUFZSSxnQkFBZ0IsQ0FBQ2Isb0JBQW9CRyxtQkFBbUIsRUFBRUksT0FBTyxDQUFDeEo7UUFDbEY7SUFDSjtJQUNBdUo7SUFDQTtRQUNJTixvQkFBb0JHLG1CQUFtQjtRQUN2Q0gsb0JBQW9CeEcsTUFBTTtLQUM3QixDQUFDK0csT0FBTyxDQUFDLENBQUNTLFVBQVVQLFlBQVlpQixhQUFhLENBQUNWO0FBQ25EO0FBQ0EsU0FBU1csbUJBQW1CQyxLQUFLLEVBQUVySyxFQUFFO0lBQ2pDLCtCQUErQjtJQUMvQixJQUFJMUMsT0FBTzhMLEVBQUUsRUFBRTtRQUNYRixZQUFZRCxJQUFJLENBQUNkLGlCQUFpQkUsWUFBWTtJQUNsRDtJQUNBLE1BQU1pQyxVQUFVdEssR0FBRzhJLGdCQUFnQkssc0JBQXNCYztJQUN6RCxJQUFJLENBQUNwQixXQUFXO1FBQ1osNEVBQTRFO1FBQzVFQSxZQUFZL0wsUUFBUXlCLE9BQU8sQ0FBQ2dNLFdBQVcsQ0FBQ0YsT0FBT0MsU0FBUztZQUNwREUsb0JBQW9CcE0sb0JBQW9CRyxPQUFPO1FBQ25EO1FBQ0EsdUdBQXVHO1FBQ3ZHdUssZ0JBQWdCO0lBQ3BCLE9BQU87UUFDSCxNQUFNMkIsa0JBQWtCN04sT0FBTzJCLE9BQU8sQ0FBQ2tNLGVBQWU7UUFDdERBLGdCQUFnQjtZQUNaNUIsVUFBVTVHLE1BQU0sQ0FBQ3FJO1FBQ3JCO0lBQ0o7QUFDSjtBQUNBLFNBQVNJLEtBQUtsRyxLQUFLOztJQUNmLElBQUksRUFBRW1HLFNBQVMsRUFBRXpJLFFBQVEsRUFBRSxHQUFHc0M7SUFDOUIsbUVBQW1FO0lBQ25FLHNDQUFzQztJQUN0QzVILE9BQU8yQixPQUFPLENBQUMySixlQUFlLENBQUMsSUFBSXlDLFVBQVUzQixPQUFPLENBQUMsQ0FBQ2YsV0FBV0EsYUFBYTtRQUMxRTBDO0tBQ0g7SUFDRCwwRUFBMEU7SUFDMUUsbUNBQW1DO0lBQ25DL04sT0FBTzJCLE9BQU8sQ0FBQ3FNLFNBQVMsQ0FBQztRQUNwQixJQUFHbE4sb0JBQW9CYSxPQUFPLEVBQUVpQjtJQUNyQyxHQUFHLEVBQUU7SUFDTCxJQUFJa0IsS0FBNEIsRUFBRSxFQVFqQztJQUNELE9BQU93QjtBQUNYO0lBdEJTd0k7TUFBQUE7QUF1QlQsU0FBUzNELFNBQVN0SSxLQUFLO0lBQ25CLElBQUksRUFBRTBHLEdBQUcsRUFBRXhGLFNBQVMsRUFBRUksS0FBSyxFQUFFd0IsR0FBRyxFQUFFLEdBQUc5QztJQUNyQyxJQUFJdUksY0FBYyxhQUFhdkksUUFBUUssWUFBWUwsTUFBTXVJLFdBQVc7SUFDcEVySCxZQUFZQSxhQUFhUCxhQUFhTyxTQUFTO0lBQy9DSSxRQUFRQSxTQUFTWCxhQUFhVyxLQUFLO0lBQ25DLE1BQU1xRixXQUFXO1FBQ2IsR0FBR3JGLEtBQUs7UUFDUko7UUFDQTRCO1FBQ0F2RjtJQUNKO0lBQ0EsK0ZBQStGO0lBQy9Gb0QsZUFBZWdHO0lBQ2YsSUFBSTRGLFdBQVc7SUFDZixJQUFJQztJQUNKLE1BQU1DLGdCQUFnQixJQUFJdEQsUUFBUSxDQUFDQyxTQUFTc0Q7UUFDeEMsSUFBSTlMLGtCQUFrQjtZQUNsQkE7UUFDSjtRQUNBNEwsaUJBQWlCO1lBQ2I1TCxtQkFBbUI7WUFDbkJ3STtRQUNKO1FBQ0F4SSxtQkFBbUI7WUFDZjJMLFdBQVc7WUFDWDNMLG1CQUFtQjtZQUNuQixNQUFNcUcsUUFBUSxJQUFJMEYsTUFBTTtZQUN4QjFGLE1BQU1sRSxTQUFTLEdBQUc7WUFDbEIySixPQUFPekY7UUFDWDtJQUNKO0lBQ0EseUVBQXlFO0lBQ3pFLHlDQUF5QztJQUN6QyxTQUFTMkY7UUFDTCxJQUFJLENBQUNyRSxlQUFlLHdFQUF3RTtRQUM1Riw4QkFBOEI7UUEzZHRDLGtCQTRkaUMsY0FBYztZQUNuQyxPQUFPO1FBQ1g7UUFDQSxNQUFNc0UsbUJBQW1COU0sYUFBYXFELFNBQVMwSixnQkFBZ0IsQ0FBQztRQUNoRSxNQUFNQyxlQUFlLElBQUlDLElBQUlILGlCQUFpQnhHLEdBQUcsQ0FBQyxDQUFDNEcsTUFBTUEsSUFBSUMsWUFBWSxDQUFDO1FBQzFFLE1BQU1DLFdBQVcvSixTQUFTZ0ssYUFBYSxDQUFDO1FBQ3hDLE1BQU1DLFFBQVFGLFlBQVksT0FBTyxLQUFLLElBQUlBLFNBQVNELFlBQVksQ0FBQztRQUNoRTNFLFlBQVlnQyxPQUFPLENBQUMsQ0FBQ3hFO1lBQ2pCLElBQUksRUFBRXVILElBQUksRUFBRUMsSUFBSSxFQUFFLEdBQUd4SDtZQUNyQixJQUFJLENBQUNnSCxhQUFhUyxHQUFHLENBQUNGLE9BQU87Z0JBQ3pCLE1BQU1HLFdBQVdySyxTQUFTTyxhQUFhLENBQUM7Z0JBQ3hDOEosU0FBU0MsWUFBWSxDQUFDLGVBQWVKO2dCQUNyQ0csU0FBU0MsWUFBWSxDQUFDLFNBQVM7Z0JBQy9CLElBQUlMLE9BQU87b0JBQ1BJLFNBQVNDLFlBQVksQ0FBQyxTQUFTTDtnQkFDbkM7Z0JBQ0FqSyxTQUFTdUssSUFBSSxDQUFDQyxXQUFXLENBQUNIO2dCQUMxQkEsU0FBU0csV0FBVyxDQUFDeEssU0FBU3lLLGNBQWMsQ0FBQ047WUFDakQ7UUFDSjtRQUNBLE9BQU87SUFDWDtJQUNBLFNBQVNPO1FBQ0wsSUFDQSw4QkFBOEI7UUFwZnRDLEtBd2ZpQnZCLEVBQUUsRUE4QlY7UUFDRCxJQUFJdk0sTUFBTXlPLE1BQU0sRUFBRTtZQUNkLE1BQU0sRUFBRUMsQ0FBQyxFQUFFQyxDQUFDLEVBQUUsR0FBRzNPLE1BQU15TyxNQUFNO1lBQzVCLElBQUdoUSxvQkFBb0JtUSxrQkFBa0IsRUFBRTtnQkFDeEMzSyxPQUFPNEssUUFBUSxDQUFDSCxHQUFHQztZQUN2QjtRQUNKO0lBQ0o7SUFDQSxTQUFTRztRQUNMdEM7SUFDSjtJQUNBSTtJQUNBLE1BQU1tQyxPQUFPLFdBQVcsR0FBRzVRLE9BQU8yQixPQUFPLENBQUM2RCxhQUFhLENBQUN4RixPQUFPMkIsT0FBTyxDQUFDa1AsUUFBUSxFQUFFLE1BQU0sV0FBVyxHQUFHN1EsT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQzRGLE1BQU07UUFDcElDLFVBQVVzRTtJQUNkLElBQUksV0FBVyxHQUFHM1AsT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ2lELGNBQWMsTUFBTUgsVUFBVUMsS0FBS0MsV0FBVyxXQUFXLEdBQUd4SSxPQUFPMkIsT0FBTyxDQUFDNkQsYUFBYSxDQUFDN0UsUUFBUW1RLE1BQU0sRUFBRTtRQUNwSkMsTUFBTTtJQUNWLEdBQUcsV0FBVyxHQUFHL1EsT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ3pFLGdCQUFnQmlRLGNBQWMsRUFBRTtJQUM5RSxpRkFBaUY7SUFDakZ4RCxtQkFBbUJuTCxZQUFZLENBQUNnSixXQUFXLFdBQVcsR0FBR3JMLE9BQU8yQixPQUFPLENBQUM2RCxhQUFhLENBQUNzSSxNQUFNO1lBQ3BGQyxXQUFXO2dCQUNQMUM7Z0JBQ0FzRjthQUNIO1FBQ0wsR0FBRzdNLEtBQThCLEdBQUcsV0FBVyxHQUFHOUQsT0FBTzJCLE9BQU8sQ0FBQzZELGFBQWEsQ0FBQ3hGLE9BQU8yQixPQUFPLENBQUN1UCxVQUFVLEVBQUUsTUFBTU4sUUFBUUEsQ0FBSUE7SUFDaEksT0FBT3RDO0FBQ1g7QUFDQSxlQUFlakosT0FBTzhMLGNBQWM7SUFDaEMsSUFBSUEsZUFBZXhNLEdBQUcsRUFBRTtRQUNwQixNQUFNb0UsWUFBWW9JO1FBQ2xCO0lBQ0o7SUFDQSxJQUFJO1FBQ0EsTUFBTWhILFNBQVNnSDtJQUNuQixFQUFFLE9BQU94TSxLQUFLO1FBQ1YsTUFBTXlNLFlBQVksQ0FBQyxHQUFHblEsU0FBU29RLGNBQWMsRUFBRTFNO1FBQy9DLCtCQUErQjtRQUMvQixJQUFJeU0sVUFBVXhNLFNBQVMsRUFBRTtZQUNyQixNQUFNd007UUFDVjtRQUNBLElBQUl0TixJQUFzQyxFQUFFO1lBQ3hDLCtEQUErRDtZQUMvRHFCLFdBQVc7Z0JBQ1AsTUFBTWlNO1lBQ1Y7UUFDSjtRQUNBLE1BQU1ySSxZQUFZO1lBQ2QsR0FBR29JLGNBQWM7WUFDakJ4TSxLQUFLeU07UUFDVDtJQUNKO0FBQ0o7QUFDQSxlQUFlN1IsUUFBUWtHLElBQUk7SUFDdkIsSUFBSTZMLGFBQWF0UCxZQUFZMkMsR0FBRztJQUNoQyxJQUFJO1FBQ0EsTUFBTTRNLGdCQUFnQixNQUFNblAsV0FBVzJGLFdBQVcsQ0FBQ3lKLGNBQWMsQ0FBQztRQUNsRSxJQUFJLFdBQVdELGVBQWU7WUFDMUIsTUFBTUEsY0FBY3pJLEtBQUs7UUFDN0I7UUFDQSxNQUFNLEVBQUUySSxXQUFXQyxHQUFHLEVBQUUxUyxTQUFTMlMsR0FBRyxFQUFFLEdBQUdKO1FBQ3pDNU8sWUFBWStPO1FBQ1osSUFBSUMsT0FBT0EsSUFBSUMsZUFBZSxFQUFFO1lBQzVCaFAsY0FBYyxDQUFDZ0Y7Z0JBQ1gsSUFBSSxFQUFFaUssRUFBRSxFQUFFbFMsSUFBSSxFQUFFcU4sU0FBUyxFQUFFL04sS0FBSyxFQUFFbU8sUUFBUSxFQUFFMEUsU0FBUyxFQUFFQyxPQUFPLEVBQUVDLFdBQVcsRUFBRSxHQUFHcEs7Z0JBQ2hGLHNEQUFzRDtnQkFDdEQsTUFBTXFLLFdBQVdDLEtBQUtDLEdBQUcsS0FBSyxNQUFPQyxDQUFBQSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBTSxRQUFPLE1BQU0sSUFBRztnQkFDakYsSUFBSUM7Z0JBQ0osSUFBSVIsV0FBV0EsUUFBUXBGLE1BQU0sRUFBRTtvQkFDM0I0RixpQkFBaUJSLE9BQU8sQ0FBQyxFQUFFLENBQUMvRSxTQUFTO2dCQUN6QztnQkFDQSxNQUFNd0YsWUFBWTtvQkFDZFgsSUFBSUEsTUFBTUk7b0JBQ1Z0UztvQkFDQXFOLFdBQVdBLGFBQWF1RjtvQkFDeEJ0VCxPQUFPQSxTQUFTLE9BQU9tTyxXQUFXbk87b0JBQ2xDd1QsT0FBT1gsY0FBYyxVQUFVQSxjQUFjLFlBQVksV0FBVztnQkFDeEU7Z0JBQ0EsSUFBSUUsYUFBYTtvQkFDYlEsVUFBVVIsV0FBVyxHQUFHQTtnQkFDNUI7Z0JBQ0FMLElBQUlDLGVBQWUsQ0FBQ1k7WUFDeEI7UUFDSjtRQUNBLE1BQU1FLGlCQUNOLHdEQUF3RDtRQXptQmhFLEtBMG1COEMsSUFBSTFRLFlBQVkyQyxHQUFHLEdBQUc7WUFDeERtRSxPQUFPOUcsWUFBWTJDLEdBQUc7UUFDMUIsSUFBSSxNQUFNdkMsV0FBVzJGLFdBQVcsQ0FBQ3lKLGNBQWMsQ0FBQ3hQLFlBQVl1SSxJQUFJO1FBQ2hFLElBQUksV0FBV21JLGdCQUFnQjtZQUMzQixNQUFNQSxlQUFlNUosS0FBSztRQUM5QjtRQUNBakcsa0JBQWtCNlAsZUFBZWpCLFNBQVM7UUFDMUMsSUFBSTNOLElBQXFDLEVBQUU7WUFDdkMsTUFBTSxFQUFFNk8sa0JBQWtCLEVBQUUsR0FBRzVTLG1CQUFPQSxDQUFDLHdGQUE2QjtZQUNwRSxJQUFJLENBQUM0UyxtQkFBbUI5UCxrQkFBa0I7Z0JBQ3RDLE1BQU0sSUFBSTJMLE1BQU0sMkRBQTJEeE0sWUFBWXVJLElBQUksR0FBRztZQUNsRztRQUNKO0lBQ0osRUFBRSxPQUFPekIsT0FBTztRQUNaLGlFQUFpRTtRQUNqRXdJLGFBQWEsQ0FBQyxHQUFHclEsU0FBU29RLGNBQWMsRUFBRXZJO0lBQzlDO0lBQ0EsSUFBSWhGLElBQXNDLEVBQUU7UUFDeEMsTUFBTSxFQUFFOE8sY0FBYyxFQUFFLEdBQUc3UyxtQkFBT0EsQ0FBQyx3SUFBd0Q7UUFDM0Ysd0VBQXdFO1FBQ3hFLGdDQUFnQztRQUNoQyxJQUFJdVIsWUFBWTtZQUNaLElBQUlBLGVBQWV0UCxZQUFZMkMsR0FBRyxFQUFFO2dCQUNoQ1EsV0FBVztvQkFDUCxJQUFJMkQ7b0JBQ0osSUFBSTt3QkFDQSxtRUFBbUU7d0JBQ25FLGtFQUFrRTt3QkFDbEUsNENBQTRDO3dCQUM1QyxNQUFNLElBQUkwRixNQUFNOEMsV0FBV3VCLE9BQU87b0JBQ3RDLEVBQUUsT0FBT0MsR0FBRzt3QkFDUmhLLFFBQVFnSztvQkFDWjtvQkFDQWhLLE1BQU1uSixJQUFJLEdBQUcyUixXQUFXM1IsSUFBSTtvQkFDNUJtSixNQUFNaUssS0FBSyxHQUFHekIsV0FBV3lCLEtBQUs7b0JBQzlCLE1BQU1ILGVBQWU5SixPQUFPd0ksV0FBVzBCLE1BQU07Z0JBQ2pEO1lBQ0osT0FBTztnQkFDSDdOLFdBQVc7b0JBQ1AsTUFBTW1NO2dCQUNWO1lBQ0o7UUFDSjtJQUNKO0lBQ0EsSUFBSXhMLE9BQU9tTixtQkFBbUIsRUFBRTtRQUM1QixNQUFNbk4sT0FBT21OLG1CQUFtQixDQUFDalIsWUFBWWtSLFVBQVU7SUFDM0Q7SUFDQTlULFNBQVMsQ0FBQyxHQUFHNEIsUUFBUW1TLFlBQVksRUFBRW5SLFlBQVl1SSxJQUFJLEVBQUV2SSxZQUFZc0MsS0FBSyxFQUFFbkMsUUFBUTtRQUM1RWlSLGNBQWNwUixZQUFZbUIsS0FBSztRQUMvQmY7UUFDQW1HLEtBQUs1RjtRQUNMSSxXQUFXRjtRQUNYa0g7UUFDQXBGLEtBQUsyTTtRQUNMOU4sWUFBWTZQLFFBQVFyUixZQUFZd0IsVUFBVTtRQUMxQzhQLGNBQWMsQ0FBQ3BRLE1BQU1xRixLQUFLK0gsU0FBU2pMLE9BQU92RyxPQUFPc0YsTUFBTSxDQUFDLENBQUMsR0FBR2xCLE1BQU07Z0JBQzFEcUY7Z0JBQ0ErSDtZQUNKO1FBQ0psSixRQUFRcEYsWUFBWW9GLE1BQU07UUFDMUJKLFNBQVNoRixZQUFZZ0YsT0FBTztRQUM1Qi9FO1FBQ0FzUixlQUFldlIsWUFBWXVSLGFBQWE7UUFDeENDLFdBQVd4UixZQUFZd1IsU0FBUztJQUNwQztJQUNBalIsMkJBQTJCLE1BQU1uRCxPQUFPcVUsZ0NBQWdDO0lBQ3hFLE1BQU1DLFlBQVk7UUFDZG5MLEtBQUs1RjtRQUNMZ1IsU0FBUztRQUNUNVEsV0FBV0Y7UUFDWE0sT0FBT25CLFlBQVltQixLQUFLO1FBQ3hCd0IsS0FBSzJNO0lBQ1Q7SUFDQSxJQUFJN0wsUUFBUSxPQUFPLEtBQUssSUFBSUEsS0FBS2dHLFlBQVksRUFBRTtRQUMzQyxNQUFNaEcsS0FBS2dHLFlBQVk7SUFDM0I7SUFDQXBHLE9BQU9xTztBQUNYO0FBRUEsSUFBSSxDQUFDLE9BQU8xVSxRQUFRMkMsT0FBTyxLQUFLLGNBQWUsT0FBTzNDLFFBQVEyQyxPQUFPLEtBQUssWUFBWTNDLFFBQVEyQyxPQUFPLEtBQUssSUFBSSxLQUFNLE9BQU8zQyxRQUFRMkMsT0FBTyxDQUFDaVMsVUFBVSxLQUFLLGFBQWE7SUFDcks5VSxPQUFPQyxjQUFjLENBQUNDLFFBQVEyQyxPQUFPLEVBQUUsY0FBYztRQUFFMUMsT0FBTztJQUFLO0lBQ25FSCxPQUFPc0YsTUFBTSxDQUFDcEYsUUFBUTJDLE9BQU8sRUFBRTNDO0lBQy9CRSxPQUFPRixPQUFPLEdBQUdBLFFBQVEyQyxPQUFPO0FBQ2xDLEVBRUEsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2luZGV4LmpzPzQ2Y2IiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZ2xvYmFsIGxvY2F0aW9uICovIFwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgdmVyc2lvbjogbnVsbCxcbiAgICByb3V0ZXI6IG51bGwsXG4gICAgZW1pdHRlcjogbnVsbCxcbiAgICBpbml0aWFsaXplOiBudWxsLFxuICAgIGh5ZHJhdGU6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgdmVyc2lvbjogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB2ZXJzaW9uO1xuICAgIH0sXG4gICAgcm91dGVyOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHJvdXRlcjtcbiAgICB9LFxuICAgIGVtaXR0ZXI6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZW1pdHRlcjtcbiAgICB9LFxuICAgIGluaXRpYWxpemU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gaW5pdGlhbGl6ZTtcbiAgICB9LFxuICAgIGh5ZHJhdGU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gaHlkcmF0ZTtcbiAgICB9XG59KTtcbmNvbnN0IF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdCA9IHJlcXVpcmUoXCJAc3djL2hlbHBlcnMvXy9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHRcIik7XG5yZXF1aXJlKFwiLi4vYnVpbGQvcG9seWZpbGxzL3BvbHlmaWxsLW1vZHVsZVwiKTtcbmNvbnN0IF9yZWFjdCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcInJlYWN0XCIpKTtcbmNvbnN0IF9jbGllbnQgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCJyZWFjdC1kb20vY2xpZW50XCIpKTtcbmNvbnN0IF9oZWFkbWFuYWdlcmNvbnRleHRzaGFyZWRydW50aW1lID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvaGVhZC1tYW5hZ2VyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWVcIik7XG5jb25zdCBfbWl0dCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcIi4uL3NoYXJlZC9saWIvbWl0dFwiKSk7XG5jb25zdCBfcm91dGVyY29udGV4dHNoYXJlZHJ1bnRpbWUgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZVwiKTtcbmNvbnN0IF9oYW5kbGVzbW9vdGhzY3JvbGwgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaGFuZGxlLXNtb290aC1zY3JvbGxcIik7XG5jb25zdCBfaXNkeW5hbWljID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWR5bmFtaWNcIik7XG5jb25zdCBfcXVlcnlzdHJpbmcgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvcXVlcnlzdHJpbmdcIik7XG5jb25zdCBfcnVudGltZWNvbmZpZ2V4dGVybmFsID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvcnVudGltZS1jb25maWcuZXh0ZXJuYWxcIik7XG5jb25zdCBfdXRpbHMgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi91dGlsc1wiKTtcbmNvbnN0IF9wb3J0YWwgPSByZXF1aXJlKFwiLi9wb3J0YWxcIik7XG5jb25zdCBfaGVhZG1hbmFnZXIgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCIuL2hlYWQtbWFuYWdlclwiKSk7XG5jb25zdCBfcGFnZWxvYWRlciA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcIi4vcGFnZS1sb2FkZXJcIikpO1xuY29uc3QgX3BlcmZvcm1hbmNlcmVsYXllciA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcIi4vcGVyZm9ybWFuY2UtcmVsYXllclwiKSk7XG5jb25zdCBfcm91dGVhbm5vdW5jZXIgPSByZXF1aXJlKFwiLi9yb3V0ZS1hbm5vdW5jZXJcIik7XG5jb25zdCBfcm91dGVyID0gcmVxdWlyZShcIi4vcm91dGVyXCIpO1xuY29uc3QgX2lzZXJyb3IgPSByZXF1aXJlKFwiLi4vbGliL2lzLWVycm9yXCIpO1xuY29uc3QgX2ltYWdlY29uZmlnY29udGV4dHNoYXJlZHJ1bnRpbWUgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9pbWFnZS1jb25maWctY29udGV4dC5zaGFyZWQtcnVudGltZVwiKTtcbmNvbnN0IF9yZW1vdmViYXNlcGF0aCA9IHJlcXVpcmUoXCIuL3JlbW92ZS1iYXNlLXBhdGhcIik7XG5jb25zdCBfaGFzYmFzZXBhdGggPSByZXF1aXJlKFwiLi9oYXMtYmFzZS1wYXRoXCIpO1xuY29uc3QgX2FwcHJvdXRlcmNvbnRleHRzaGFyZWRydW50aW1lID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvYXBwLXJvdXRlci1jb250ZXh0LnNoYXJlZC1ydW50aW1lXCIpO1xuY29uc3QgX2FkYXB0ZXJzID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvcm91dGVyL2FkYXB0ZXJzXCIpO1xuY29uc3QgX2hvb2tzY2xpZW50Y29udGV4dHNoYXJlZHJ1bnRpbWUgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZVwiKTtcbmNvbnN0IF9vbnJlY292ZXJhYmxlZXJyb3IgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCIuL29uLXJlY292ZXJhYmxlLWVycm9yXCIpKTtcbmNvbnN0IF90cmFjZXIgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCIuL3RyYWNpbmcvdHJhY2VyXCIpKTtcbmNvbnN0IF9yZXBvcnR0b3NvY2tldCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcIi4vdHJhY2luZy9yZXBvcnQtdG8tc29ja2V0XCIpKTtcbmNvbnN0IHZlcnNpb24gPSBcIjEzLjUuMTFcIjtcbmxldCByb3V0ZXI7XG5jb25zdCBlbWl0dGVyID0gKDAsIF9taXR0LmRlZmF1bHQpKCk7XG5jb25zdCBsb29zZVRvQXJyYXkgPSAoaW5wdXQpPT5bXS5zbGljZS5jYWxsKGlucHV0KTtcbmxldCBpbml0aWFsRGF0YTtcbmxldCBkZWZhdWx0TG9jYWxlID0gdW5kZWZpbmVkO1xubGV0IGFzUGF0aDtcbmxldCBwYWdlTG9hZGVyO1xubGV0IGFwcEVsZW1lbnQ7XG5sZXQgaGVhZE1hbmFnZXI7XG5sZXQgaW5pdGlhbE1hdGNoZXNNaWRkbGV3YXJlID0gZmFsc2U7XG5sZXQgbGFzdEFwcFByb3BzO1xubGV0IGxhc3RSZW5kZXJSZWplY3Q7XG5sZXQgZGV2Q2xpZW50O1xubGV0IENhY2hlZEFwcCwgb25QZXJmRW50cnk7XG5sZXQgQ2FjaGVkQ29tcG9uZW50O1xuY2xhc3MgQ29udGFpbmVyIGV4dGVuZHMgX3JlYWN0LmRlZmF1bHQuQ29tcG9uZW50IHtcbiAgICBjb21wb25lbnREaWRDYXRjaChjb21wb25lbnRFcnIsIGluZm8pIHtcbiAgICAgICAgdGhpcy5wcm9wcy5mbihjb21wb25lbnRFcnIsIGluZm8pO1xuICAgIH1cbiAgICBjb21wb25lbnREaWRNb3VudCgpIHtcbiAgICAgICAgdGhpcy5zY3JvbGxUb0hhc2goKTtcbiAgICAgICAgLy8gV2UgbmVlZCB0byByZXBsYWNlIHRoZSByb3V0ZXIgc3RhdGUgaWY6XG4gICAgICAgIC8vIC0gdGhlIHBhZ2Ugd2FzIChhdXRvKSBleHBvcnRlZCBhbmQgaGFzIGEgcXVlcnkgc3RyaW5nIG9yIHNlYXJjaCAoaGFzaClcbiAgICAgICAgLy8gLSBpdCB3YXMgYXV0byBleHBvcnRlZCBhbmQgaXMgYSBkeW5hbWljIHJvdXRlICh0byBwcm92aWRlIHBhcmFtcylcbiAgICAgICAgLy8gLSBpZiBpdCBpcyBhIGNsaWVudC1zaWRlIHNrZWxldG9uIChmYWxsYmFjayByZW5kZXIpXG4gICAgICAgIC8vIC0gaWYgbWlkZGxld2FyZSBtYXRjaGVzIHRoZSBjdXJyZW50IHBhZ2UgKG1heSBoYXZlIHJld3JpdGUgcGFyYW1zKVxuICAgICAgICAvLyAtIGlmIHJld3JpdGVzIGluIG5leHQuY29uZmlnLmpzIG1hdGNoIChtYXkgaGF2ZSByZXdyaXRlIHBhcmFtcylcbiAgICAgICAgaWYgKHJvdXRlci5pc1NzciAmJiAoaW5pdGlhbERhdGEuaXNGYWxsYmFjayB8fCBpbml0aWFsRGF0YS5uZXh0RXhwb3J0ICYmICgoMCwgX2lzZHluYW1pYy5pc0R5bmFtaWNSb3V0ZSkocm91dGVyLnBhdGhuYW1lKSB8fCBsb2NhdGlvbi5zZWFyY2ggfHwgcHJvY2Vzcy5lbnYuX19ORVhUX0hBU19SRVdSSVRFUyB8fCBpbml0aWFsTWF0Y2hlc01pZGRsZXdhcmUpIHx8IGluaXRpYWxEYXRhLnByb3BzICYmIGluaXRpYWxEYXRhLnByb3BzLl9fTl9TU0cgJiYgKGxvY2F0aW9uLnNlYXJjaCB8fCBwcm9jZXNzLmVudi5fX05FWFRfSEFTX1JFV1JJVEVTIHx8IGluaXRpYWxNYXRjaGVzTWlkZGxld2FyZSkpKSB7XG4gICAgICAgICAgICAvLyB1cGRhdGUgcXVlcnkgb24gbW91bnQgZm9yIGV4cG9ydGVkIHBhZ2VzXG4gICAgICAgICAgICByb3V0ZXIucmVwbGFjZShyb3V0ZXIucGF0aG5hbWUgKyBcIj9cIiArIFN0cmluZygoMCwgX3F1ZXJ5c3RyaW5nLmFzc2lnbikoKDAsIF9xdWVyeXN0cmluZy51cmxRdWVyeVRvU2VhcmNoUGFyYW1zKShyb3V0ZXIucXVlcnkpLCBuZXcgVVJMU2VhcmNoUGFyYW1zKGxvY2F0aW9uLnNlYXJjaCkpKSwgYXNQYXRoLCB7XG4gICAgICAgICAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICAgICAgICAgIC8vIFdBUk5JTkc6IGBfaGAgaXMgYW4gaW50ZXJuYWwgb3B0aW9uIGZvciBoYW5kaW5nIE5leHQuanNcbiAgICAgICAgICAgICAgICAvLyBjbGllbnQtc2lkZSBoeWRyYXRpb24uIFlvdXIgYXBwIHNob3VsZCBfbmV2ZXJfIHVzZSB0aGlzIHByb3BlcnR5LlxuICAgICAgICAgICAgICAgIC8vIEl0IG1heSBjaGFuZ2UgYXQgYW55IHRpbWUgd2l0aG91dCBub3RpY2UuXG4gICAgICAgICAgICAgICAgX2g6IDEsXG4gICAgICAgICAgICAgICAgLy8gRmFsbGJhY2sgcGFnZXMgbXVzdCB0cmlnZ2VyIHRoZSBkYXRhIGZldGNoLCBzbyB0aGUgdHJhbnNpdGlvbiBpc1xuICAgICAgICAgICAgICAgIC8vIG5vdCBzaGFsbG93LlxuICAgICAgICAgICAgICAgIC8vIE90aGVyIHBhZ2VzIChzdHJpY3RseSB1cGRhdGluZyBxdWVyeSkgaGFwcGVucyBzaGFsbG93bHksIGFzIGRhdGFcbiAgICAgICAgICAgICAgICAvLyByZXF1aXJlbWVudHMgd291bGQgYWxyZWFkeSBiZSBwcmVzZW50LlxuICAgICAgICAgICAgICAgIHNoYWxsb3c6ICFpbml0aWFsRGF0YS5pc0ZhbGxiYWNrICYmICFpbml0aWFsTWF0Y2hlc01pZGRsZXdhcmVcbiAgICAgICAgICAgIH0pLmNhdGNoKChlcnIpPT57XG4gICAgICAgICAgICAgICAgaWYgKCFlcnIuY2FuY2VsbGVkKSB0aHJvdyBlcnI7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjb21wb25lbnREaWRVcGRhdGUoKSB7XG4gICAgICAgIHRoaXMuc2Nyb2xsVG9IYXNoKCk7XG4gICAgfVxuICAgIHNjcm9sbFRvSGFzaCgpIHtcbiAgICAgICAgbGV0IHsgaGFzaCB9ID0gbG9jYXRpb247XG4gICAgICAgIGhhc2ggPSBoYXNoICYmIGhhc2guc3Vic3RyaW5nKDEpO1xuICAgICAgICBpZiAoIWhhc2gpIHJldHVybjtcbiAgICAgICAgY29uc3QgZWwgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChoYXNoKTtcbiAgICAgICAgaWYgKCFlbCkgcmV0dXJuO1xuICAgICAgICAvLyBJZiB3ZSBjYWxsIHNjcm9sbEludG9WaWV3KCkgaW4gaGVyZSB3aXRob3V0IGEgc2V0VGltZW91dFxuICAgICAgICAvLyBpdCB3b24ndCBzY3JvbGwgcHJvcGVybHkuXG4gICAgICAgIHNldFRpbWVvdXQoKCk9PmVsLnNjcm9sbEludG9WaWV3KCksIDApO1xuICAgIH1cbiAgICByZW5kZXIoKSB7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnByb3BzLmNoaWxkcmVuO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgeyBSZWFjdERldk92ZXJsYXkgfSA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvQG5leHQvcmVhY3QtZGV2LW92ZXJsYXkvZGlzdC9jbGllbnRcIik7XG4gICAgICAgICAgICByZXR1cm4gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFJlYWN0RGV2T3ZlcmxheSwgbnVsbCwgdGhpcy5wcm9wcy5jaGlsZHJlbik7XG4gICAgICAgIH1cbiAgICB9XG59XG5hc3luYyBmdW5jdGlvbiBpbml0aWFsaXplKG9wdHMpIHtcbiAgICBpZiAob3B0cyA9PT0gdm9pZCAwKSBvcHRzID0ge307XG4gICAgX3RyYWNlci5kZWZhdWx0Lm9uU3BhbkVuZChfcmVwb3J0dG9zb2NrZXQuZGVmYXVsdCk7XG4gICAgLy8gVGhpcyBtYWtlcyBzdXJlIHRoaXMgc3BlY2lmaWMgbGluZXMgYXJlIHJlbW92ZWQgaW4gcHJvZHVjdGlvblxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgIGRldkNsaWVudCA9IG9wdHMuZGV2Q2xpZW50O1xuICAgIH1cbiAgICBpbml0aWFsRGF0YSA9IEpTT04ucGFyc2UoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXCJfX05FWFRfREFUQV9fXCIpLnRleHRDb250ZW50KTtcbiAgICB3aW5kb3cuX19ORVhUX0RBVEFfXyA9IGluaXRpYWxEYXRhO1xuICAgIGRlZmF1bHRMb2NhbGUgPSBpbml0aWFsRGF0YS5kZWZhdWx0TG9jYWxlO1xuICAgIGNvbnN0IHByZWZpeCA9IGluaXRpYWxEYXRhLmFzc2V0UHJlZml4IHx8IFwiXCI7XG4gICAgc2VsZi5fX25leHRfc2V0X3B1YmxpY19wYXRoX18oXCJcIiArIHByZWZpeCArIFwiL19uZXh0L1wiKSAvL2VzbGludC1kaXNhYmxlLWxpbmVcbiAgICA7XG4gICAgLy8gSW5pdGlhbGl6ZSBuZXh0L2NvbmZpZyB3aXRoIHRoZSBlbnZpcm9ubWVudCBjb25maWd1cmF0aW9uXG4gICAgKDAsIF9ydW50aW1lY29uZmlnZXh0ZXJuYWwuc2V0Q29uZmlnKSh7XG4gICAgICAgIHNlcnZlclJ1bnRpbWVDb25maWc6IHt9LFxuICAgICAgICBwdWJsaWNSdW50aW1lQ29uZmlnOiBpbml0aWFsRGF0YS5ydW50aW1lQ29uZmlnIHx8IHt9XG4gICAgfSk7XG4gICAgYXNQYXRoID0gKDAsIF91dGlscy5nZXRVUkwpKCk7XG4gICAgLy8gbWFrZSBzdXJlIG5vdCB0byBhdHRlbXB0IHN0cmlwcGluZyBiYXNlUGF0aCBmb3IgNDA0c1xuICAgIGlmICgoMCwgX2hhc2Jhc2VwYXRoLmhhc0Jhc2VQYXRoKShhc1BhdGgpKSB7XG4gICAgICAgIGFzUGF0aCA9ICgwLCBfcmVtb3ZlYmFzZXBhdGgucmVtb3ZlQmFzZVBhdGgpKGFzUGF0aCk7XG4gICAgfVxuICAgIGlmIChwcm9jZXNzLmVudi5fX05FWFRfSTE4Tl9TVVBQT1JUKSB7XG4gICAgICAgIGNvbnN0IHsgbm9ybWFsaXplTG9jYWxlUGF0aCB9ID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvaTE4bi9ub3JtYWxpemUtbG9jYWxlLXBhdGhcIik7XG4gICAgICAgIGNvbnN0IHsgZGV0ZWN0RG9tYWluTG9jYWxlIH0gPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9pMThuL2RldGVjdC1kb21haW4tbG9jYWxlXCIpO1xuICAgICAgICBjb25zdCB7IHBhcnNlUmVsYXRpdmVVcmwgfSA9IHJlcXVpcmUoXCIuLi9zaGFyZWQvbGliL3JvdXRlci91dGlscy9wYXJzZS1yZWxhdGl2ZS11cmxcIik7XG4gICAgICAgIGNvbnN0IHsgZm9ybWF0VXJsIH0gPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvZm9ybWF0LXVybFwiKTtcbiAgICAgICAgaWYgKGluaXRpYWxEYXRhLmxvY2FsZXMpIHtcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlZEFzID0gcGFyc2VSZWxhdGl2ZVVybChhc1BhdGgpO1xuICAgICAgICAgICAgY29uc3QgbG9jYWxlUGF0aFJlc3VsdCA9IG5vcm1hbGl6ZUxvY2FsZVBhdGgocGFyc2VkQXMucGF0aG5hbWUsIGluaXRpYWxEYXRhLmxvY2FsZXMpO1xuICAgICAgICAgICAgaWYgKGxvY2FsZVBhdGhSZXN1bHQuZGV0ZWN0ZWRMb2NhbGUpIHtcbiAgICAgICAgICAgICAgICBwYXJzZWRBcy5wYXRobmFtZSA9IGxvY2FsZVBhdGhSZXN1bHQucGF0aG5hbWU7XG4gICAgICAgICAgICAgICAgYXNQYXRoID0gZm9ybWF0VXJsKHBhcnNlZEFzKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gZGVyaXZlIHRoZSBkZWZhdWx0IGxvY2FsZSBpZiBpdCB3YXNuJ3QgZGV0ZWN0ZWQgaW4gdGhlIGFzUGF0aFxuICAgICAgICAgICAgICAgIC8vIHNpbmNlIHdlIGRvbid0IHByZXJlbmRlciBzdGF0aWMgcGFnZXMgd2l0aCBhbGwgcG9zc2libGUgZGVmYXVsdFxuICAgICAgICAgICAgICAgIC8vIGxvY2FsZXNcbiAgICAgICAgICAgICAgICBkZWZhdWx0TG9jYWxlID0gaW5pdGlhbERhdGEubG9jYWxlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gYXR0ZW1wdCBkZXRlY3RpbmcgZGVmYXVsdCBsb2NhbGUgYmFzZWQgb24gaG9zdG5hbWVcbiAgICAgICAgICAgIGNvbnN0IGRldGVjdGVkRG9tYWluID0gZGV0ZWN0RG9tYWluTG9jYWxlKHByb2Nlc3MuZW52Ll9fTkVYVF9JMThOX0RPTUFJTlMsIHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZSk7XG4gICAgICAgICAgICAvLyBUT0RPOiBpbnZlc3RpZ2F0ZSBpZiBkZWZhdWx0TG9jYWxlIG5lZWRzIHRvIGJlIHBvcHVsYXRlZCBhZnRlclxuICAgICAgICAgICAgLy8gaHlkcmF0aW9uIHRvIHByZXZlbnQgbWlzbWF0Y2hlZCByZW5kZXJzXG4gICAgICAgICAgICBpZiAoZGV0ZWN0ZWREb21haW4pIHtcbiAgICAgICAgICAgICAgICBkZWZhdWx0TG9jYWxlID0gZGV0ZWN0ZWREb21haW4uZGVmYXVsdExvY2FsZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoaW5pdGlhbERhdGEuc2NyaXB0TG9hZGVyKSB7XG4gICAgICAgIGNvbnN0IHsgaW5pdFNjcmlwdExvYWRlciB9ID0gcmVxdWlyZShcIi4vc2NyaXB0XCIpO1xuICAgICAgICBpbml0U2NyaXB0TG9hZGVyKGluaXRpYWxEYXRhLnNjcmlwdExvYWRlcik7XG4gICAgfVxuICAgIHBhZ2VMb2FkZXIgPSBuZXcgX3BhZ2Vsb2FkZXIuZGVmYXVsdChpbml0aWFsRGF0YS5idWlsZElkLCBwcmVmaXgpO1xuICAgIGNvbnN0IHJlZ2lzdGVyID0gKHBhcmFtKT0+e1xuICAgICAgICBsZXQgW3IsIGZdID0gcGFyYW07XG4gICAgICAgIHJldHVybiBwYWdlTG9hZGVyLnJvdXRlTG9hZGVyLm9uRW50cnlwb2ludChyLCBmKTtcbiAgICB9O1xuICAgIGlmICh3aW5kb3cuX19ORVhUX1ApIHtcbiAgICAgICAgLy8gRGVmZXIgcGFnZSByZWdpc3RyYXRpb24gZm9yIGFub3RoZXIgdGljay4gVGhpcyB3aWxsIGluY3JlYXNlIHRoZSBvdmVyYWxsXG4gICAgICAgIC8vIGxhdGVuY3kgaW4gaHlkcmF0aW5nIHRoZSBwYWdlLCBidXQgcmVkdWNlIHRoZSB0b3RhbCBibG9ja2luZyB0aW1lLlxuICAgICAgICB3aW5kb3cuX19ORVhUX1AubWFwKChwKT0+c2V0VGltZW91dCgoKT0+cmVnaXN0ZXIocCksIDApKTtcbiAgICB9XG4gICAgd2luZG93Ll9fTkVYVF9QID0gW107XG4gICAgd2luZG93Ll9fTkVYVF9QLnB1c2ggPSByZWdpc3RlcjtcbiAgICBoZWFkTWFuYWdlciA9ICgwLCBfaGVhZG1hbmFnZXIuZGVmYXVsdCkoKTtcbiAgICBoZWFkTWFuYWdlci5nZXRJc1NzciA9ICgpPT57XG4gICAgICAgIHJldHVybiByb3V0ZXIuaXNTc3I7XG4gICAgfTtcbiAgICBhcHBFbGVtZW50ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXCJfX25leHRcIik7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYXNzZXRQcmVmaXg6IHByZWZpeFxuICAgIH07XG59XG5mdW5jdGlvbiByZW5kZXJBcHAoQXBwLCBhcHBQcm9wcykge1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoQXBwLCBhcHBQcm9wcyk7XG59XG5mdW5jdGlvbiBBcHBDb250YWluZXIocGFyYW0pIHtcbiAgICBsZXQgeyBjaGlsZHJlbiB9ID0gcGFyYW07XG4gICAgLy8gQ3JlYXRlIGEgbWVtb2l6ZWQgdmFsdWUgZm9yIG5leHQvbmF2aWdhdGlvbiByb3V0ZXIgY29udGV4dC5cbiAgICBjb25zdCBhZGFwdGVkRm9yQXBwUm91dGVyID0gX3JlYWN0LmRlZmF1bHQudXNlTWVtbygoKT0+e1xuICAgICAgICByZXR1cm4gKDAsIF9hZGFwdGVycy5hZGFwdEZvckFwcFJvdXRlckluc3RhbmNlKShyb3V0ZXIpO1xuICAgIH0sIFtdKTtcbiAgICB2YXIgX3NlbGZfX19ORVhUX0RBVEFfX19hdXRvRXhwb3J0O1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoQ29udGFpbmVyLCB7XG4gICAgICAgIGZuOiAoZXJyb3IpPT4vLyBUT0RPOiBGaXggZGlzYWJsZWQgZXNsaW50IHJ1bGVcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdXNlLWJlZm9yZS1kZWZpbmVcbiAgICAgICAgICAgIHJlbmRlckVycm9yKHtcbiAgICAgICAgICAgICAgICBBcHA6IENhY2hlZEFwcCxcbiAgICAgICAgICAgICAgICBlcnI6IGVycm9yXG4gICAgICAgICAgICB9KS5jYXRjaCgoZXJyKT0+Y29uc29sZS5lcnJvcihcIkVycm9yIHJlbmRlcmluZyBwYWdlOiBcIiwgZXJyKSlcbiAgICB9LCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX2FwcHJvdXRlcmNvbnRleHRzaGFyZWRydW50aW1lLkFwcFJvdXRlckNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICAgICAgdmFsdWU6IGFkYXB0ZWRGb3JBcHBSb3V0ZXJcbiAgICB9LCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX2hvb2tzY2xpZW50Y29udGV4dHNoYXJlZHJ1bnRpbWUuU2VhcmNoUGFyYW1zQ29udGV4dC5Qcm92aWRlciwge1xuICAgICAgICB2YWx1ZTogKDAsIF9hZGFwdGVycy5hZGFwdEZvclNlYXJjaFBhcmFtcykocm91dGVyKVxuICAgIH0sIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfYWRhcHRlcnMuUGF0aG5hbWVDb250ZXh0UHJvdmlkZXJBZGFwdGVyLCB7XG4gICAgICAgIHJvdXRlcjogcm91dGVyLFxuICAgICAgICBpc0F1dG9FeHBvcnQ6IChfc2VsZl9fX05FWFRfREFUQV9fX2F1dG9FeHBvcnQgPSBzZWxmLl9fTkVYVF9EQVRBX18uYXV0b0V4cG9ydCkgIT0gbnVsbCA/IF9zZWxmX19fTkVYVF9EQVRBX19fYXV0b0V4cG9ydCA6IGZhbHNlXG4gICAgfSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9ob29rc2NsaWVudGNvbnRleHRzaGFyZWRydW50aW1lLlBhdGhQYXJhbXNDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgICAgIHZhbHVlOiAoMCwgX2FkYXB0ZXJzLmFkYXB0Rm9yUGF0aFBhcmFtcykocm91dGVyKVxuICAgIH0sIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcm91dGVyY29udGV4dHNoYXJlZHJ1bnRpbWUuUm91dGVyQ29udGV4dC5Qcm92aWRlciwge1xuICAgICAgICB2YWx1ZTogKDAsIF9yb3V0ZXIubWFrZVB1YmxpY1JvdXRlckluc3RhbmNlKShyb3V0ZXIpXG4gICAgfSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9oZWFkbWFuYWdlcmNvbnRleHRzaGFyZWRydW50aW1lLkhlYWRNYW5hZ2VyQ29udGV4dC5Qcm92aWRlciwge1xuICAgICAgICB2YWx1ZTogaGVhZE1hbmFnZXJcbiAgICB9LCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX2ltYWdlY29uZmlnY29udGV4dHNoYXJlZHJ1bnRpbWUuSW1hZ2VDb25maWdDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgICAgIHZhbHVlOiBwcm9jZXNzLmVudi5fX05FWFRfSU1BR0VfT1BUU1xuICAgIH0sIGNoaWxkcmVuKSkpKSkpKSk7XG59XG5jb25zdCB3cmFwQXBwID0gKEFwcCk9Pih3cmFwcGVkQXBwUHJvcHMpPT57XG4gICAgICAgIGNvbnN0IGFwcFByb3BzID0ge1xuICAgICAgICAgICAgLi4ud3JhcHBlZEFwcFByb3BzLFxuICAgICAgICAgICAgQ29tcG9uZW50OiBDYWNoZWRDb21wb25lbnQsXG4gICAgICAgICAgICBlcnI6IGluaXRpYWxEYXRhLmVycixcbiAgICAgICAgICAgIHJvdXRlclxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KEFwcENvbnRhaW5lciwgbnVsbCwgcmVuZGVyQXBwKEFwcCwgYXBwUHJvcHMpKTtcbiAgICB9O1xuLy8gVGhpcyBtZXRob2QgaGFuZGxlcyBhbGwgcnVudGltZSBhbmQgZGVidWcgZXJyb3JzLlxuLy8gNDA0IGFuZCA1MDAgZXJyb3JzIGFyZSBzcGVjaWFsIGtpbmQgb2YgZXJyb3JzXG4vLyBhbmQgdGhleSBhcmUgc3RpbGwgaGFuZGxlIHZpYSB0aGUgbWFpbiByZW5kZXIgbWV0aG9kLlxuZnVuY3Rpb24gcmVuZGVyRXJyb3IocmVuZGVyRXJyb3JQcm9wcykge1xuICAgIGxldCB7IEFwcCwgZXJyIH0gPSByZW5kZXJFcnJvclByb3BzO1xuICAgIC8vIEluIGRldmVsb3BtZW50IHJ1bnRpbWUgZXJyb3JzIGFyZSBjYXVnaHQgYnkgb3VyIG92ZXJsYXlcbiAgICAvLyBJbiBwcm9kdWN0aW9uIHdlIGNhdGNoIHJ1bnRpbWUgZXJyb3JzIHVzaW5nIGNvbXBvbmVudERpZENhdGNoIHdoaWNoIHdpbGwgdHJpZ2dlciByZW5kZXJFcnJvclxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgICAgLy8gQSBOZXh0LmpzIHJlbmRlcmluZyBydW50aW1lIGVycm9yIGlzIGFsd2F5cyB1bnJlY292ZXJhYmxlXG4gICAgICAgIC8vIEZJWE1FOiBsZXQncyBtYWtlIHRoaXMgcmVjb3ZlcmFibGUgKGVycm9yIGluIEdJUCBjbGllbnQtdHJhbnNpdGlvbilcbiAgICAgICAgZGV2Q2xpZW50Lm9uVW5yZWNvdmVyYWJsZUVycm9yKCk7XG4gICAgICAgIC8vIFdlIG5lZWQgdG8gcmVuZGVyIGFuIGVtcHR5IDxBcHA+IHNvIHRoYXQgdGhlIGA8UmVhY3REZXZPdmVybGF5PmAgY2FuXG4gICAgICAgIC8vIHJlbmRlciBpdHNlbGYuXG4gICAgICAgIC8vIFRPRE86IEZpeCBkaXNhYmxlZCBlc2xpbnQgcnVsZVxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVzZS1iZWZvcmUtZGVmaW5lXG4gICAgICAgIHJldHVybiBkb1JlbmRlcih7XG4gICAgICAgICAgICBBcHA6ICgpPT5udWxsLFxuICAgICAgICAgICAgcHJvcHM6IHt9LFxuICAgICAgICAgICAgQ29tcG9uZW50OiAoKT0+bnVsbCxcbiAgICAgICAgICAgIHN0eWxlU2hlZXRzOiBbXVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgLy8gTWFrZSBzdXJlIHdlIGxvZyB0aGUgZXJyb3IgdG8gdGhlIGNvbnNvbGUsIG90aGVyd2lzZSB1c2VycyBjYW4ndCB0cmFjayBkb3duIGlzc3Vlcy5cbiAgICBjb25zb2xlLmVycm9yKGVycik7XG4gICAgY29uc29sZS5lcnJvcihcIkEgY2xpZW50LXNpZGUgZXhjZXB0aW9uIGhhcyBvY2N1cnJlZCwgc2VlIGhlcmUgZm9yIG1vcmUgaW5mbzogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvY2xpZW50LXNpZGUtZXhjZXB0aW9uLW9jY3VycmVkXCIpO1xuICAgIHJldHVybiBwYWdlTG9hZGVyLmxvYWRQYWdlKFwiL19lcnJvclwiKS50aGVuKChwYXJhbSk9PntcbiAgICAgICAgbGV0IHsgcGFnZTogRXJyb3JDb21wb25lbnQsIHN0eWxlU2hlZXRzIH0gPSBwYXJhbTtcbiAgICAgICAgcmV0dXJuIChsYXN0QXBwUHJvcHMgPT0gbnVsbCA/IHZvaWQgMCA6IGxhc3RBcHBQcm9wcy5Db21wb25lbnQpID09PSBFcnJvckNvbXBvbmVudCA/IGltcG9ydChcIi4uL3BhZ2VzL19lcnJvclwiKS50aGVuKChlcnJvck1vZHVsZSk9PntcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoXCIuLi9wYWdlcy9fYXBwXCIpLnRoZW4oKGFwcE1vZHVsZSk9PntcbiAgICAgICAgICAgICAgICBBcHAgPSBhcHBNb2R1bGUuZGVmYXVsdDtcbiAgICAgICAgICAgICAgICByZW5kZXJFcnJvclByb3BzLkFwcCA9IEFwcDtcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3JNb2R1bGU7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSkudGhlbigobSk9Pih7XG4gICAgICAgICAgICAgICAgRXJyb3JDb21wb25lbnQ6IG0uZGVmYXVsdCxcbiAgICAgICAgICAgICAgICBzdHlsZVNoZWV0czogW11cbiAgICAgICAgICAgIH0pKSA6IHtcbiAgICAgICAgICAgIEVycm9yQ29tcG9uZW50LFxuICAgICAgICAgICAgc3R5bGVTaGVldHNcbiAgICAgICAgfTtcbiAgICB9KS50aGVuKChwYXJhbSk9PntcbiAgICAgICAgbGV0IHsgRXJyb3JDb21wb25lbnQsIHN0eWxlU2hlZXRzIH0gPSBwYXJhbTtcbiAgICAgICAgdmFyIF9yZW5kZXJFcnJvclByb3BzX3Byb3BzO1xuICAgICAgICAvLyBJbiBwcm9kdWN0aW9uIHdlIGRvIGEgbm9ybWFsIHJlbmRlciB3aXRoIHRoZSBgRXJyb3JDb21wb25lbnRgIGFzIGNvbXBvbmVudC5cbiAgICAgICAgLy8gSWYgd2UndmUgZ290dGVuIGhlcmUgdXBvbiBpbml0aWFsIHJlbmRlciwgd2UgY2FuIHVzZSB0aGUgcHJvcHMgZnJvbSB0aGUgc2VydmVyLlxuICAgICAgICAvLyBPdGhlcndpc2UsIHdlIG5lZWQgdG8gY2FsbCBgZ2V0SW5pdGlhbFByb3BzYCBvbiBgQXBwYCBiZWZvcmUgbW91bnRpbmcuXG4gICAgICAgIGNvbnN0IEFwcFRyZWUgPSB3cmFwQXBwKEFwcCk7XG4gICAgICAgIGNvbnN0IGFwcEN0eCA9IHtcbiAgICAgICAgICAgIENvbXBvbmVudDogRXJyb3JDb21wb25lbnQsXG4gICAgICAgICAgICBBcHBUcmVlLFxuICAgICAgICAgICAgcm91dGVyLFxuICAgICAgICAgICAgY3R4OiB7XG4gICAgICAgICAgICAgICAgZXJyLFxuICAgICAgICAgICAgICAgIHBhdGhuYW1lOiBpbml0aWFsRGF0YS5wYWdlLFxuICAgICAgICAgICAgICAgIHF1ZXJ5OiBpbml0aWFsRGF0YS5xdWVyeSxcbiAgICAgICAgICAgICAgICBhc1BhdGgsXG4gICAgICAgICAgICAgICAgQXBwVHJlZVxuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCgoX3JlbmRlckVycm9yUHJvcHNfcHJvcHMgPSByZW5kZXJFcnJvclByb3BzLnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogX3JlbmRlckVycm9yUHJvcHNfcHJvcHMuZXJyKSA/IHJlbmRlckVycm9yUHJvcHMucHJvcHMgOiAoMCwgX3V0aWxzLmxvYWRHZXRJbml0aWFsUHJvcHMpKEFwcCwgYXBwQ3R4KSkudGhlbigoaW5pdFByb3BzKT0+Ly8gVE9ETzogRml4IGRpc2FibGVkIGVzbGludCBydWxlXG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVzZS1iZWZvcmUtZGVmaW5lXG4gICAgICAgICAgICBkb1JlbmRlcih7XG4gICAgICAgICAgICAgICAgLi4ucmVuZGVyRXJyb3JQcm9wcyxcbiAgICAgICAgICAgICAgICBlcnIsXG4gICAgICAgICAgICAgICAgQ29tcG9uZW50OiBFcnJvckNvbXBvbmVudCxcbiAgICAgICAgICAgICAgICBzdHlsZVNoZWV0cyxcbiAgICAgICAgICAgICAgICBwcm9wczogaW5pdFByb3BzXG4gICAgICAgICAgICB9KSk7XG4gICAgfSk7XG59XG4vLyBEdW1teSBjb21wb25lbnQgdGhhdCB3ZSByZW5kZXIgYXMgYSBjaGlsZCBvZiBSb290IHNvIHRoYXQgd2UgY2FuXG4vLyB0b2dnbGUgdGhlIGNvcnJlY3Qgc3R5bGVzIGJlZm9yZSB0aGUgcGFnZSBpcyByZW5kZXJlZC5cbmZ1bmN0aW9uIEhlYWQocGFyYW0pIHtcbiAgICBsZXQgeyBjYWxsYmFjayB9ID0gcGFyYW07XG4gICAgLy8gV2UgdXNlIGB1c2VMYXlvdXRFZmZlY3RgIHRvIGd1YXJhbnRlZSB0aGUgY2FsbGJhY2sgaXMgZXhlY3V0ZWRcbiAgICAvLyBhcyBzb29uIGFzIFJlYWN0IGZsdXNoZXMgdGhlIHVwZGF0ZS5cbiAgICBfcmVhY3QuZGVmYXVsdC51c2VMYXlvdXRFZmZlY3QoKCk9PmNhbGxiYWNrKCksIFtcbiAgICAgICAgY2FsbGJhY2tcbiAgICBdKTtcbiAgICByZXR1cm4gbnVsbDtcbn1cbmNvbnN0IHBlcmZvcm1hbmNlTWFya3MgPSB7XG4gICAgbmF2aWdhdGlvblN0YXJ0OiBcIm5hdmlnYXRpb25TdGFydFwiLFxuICAgIGJlZm9yZVJlbmRlcjogXCJiZWZvcmVSZW5kZXJcIixcbiAgICBhZnRlclJlbmRlcjogXCJhZnRlclJlbmRlclwiLFxuICAgIGFmdGVySHlkcmF0ZTogXCJhZnRlckh5ZHJhdGVcIixcbiAgICByb3V0ZUNoYW5nZTogXCJyb3V0ZUNoYW5nZVwiXG59O1xuY29uc3QgcGVyZm9ybWFuY2VNZWFzdXJlcyA9IHtcbiAgICBoeWRyYXRpb246IFwiTmV4dC5qcy1oeWRyYXRpb25cIixcbiAgICBiZWZvcmVIeWRyYXRpb246IFwiTmV4dC5qcy1iZWZvcmUtaHlkcmF0aW9uXCIsXG4gICAgcm91dGVDaGFuZ2VUb1JlbmRlcjogXCJOZXh0LmpzLXJvdXRlLWNoYW5nZS10by1yZW5kZXJcIixcbiAgICByZW5kZXI6IFwiTmV4dC5qcy1yZW5kZXJcIlxufTtcbmxldCByZWFjdFJvb3QgPSBudWxsO1xuLy8gT24gaW5pdGlhbCByZW5kZXIgYSBoeWRyYXRlIHNob3VsZCBhbHdheXMgaGFwcGVuXG5sZXQgc2hvdWxkSHlkcmF0ZSA9IHRydWU7XG5mdW5jdGlvbiBjbGVhck1hcmtzKCkge1xuICAgIFtcbiAgICAgICAgcGVyZm9ybWFuY2VNYXJrcy5iZWZvcmVSZW5kZXIsXG4gICAgICAgIHBlcmZvcm1hbmNlTWFya3MuYWZ0ZXJIeWRyYXRlLFxuICAgICAgICBwZXJmb3JtYW5jZU1hcmtzLmFmdGVyUmVuZGVyLFxuICAgICAgICBwZXJmb3JtYW5jZU1hcmtzLnJvdXRlQ2hhbmdlXG4gICAgXS5mb3JFYWNoKChtYXJrKT0+cGVyZm9ybWFuY2UuY2xlYXJNYXJrcyhtYXJrKSk7XG59XG5mdW5jdGlvbiBtYXJrSHlkcmF0ZUNvbXBsZXRlKCkge1xuICAgIGlmICghX3V0aWxzLlNUKSByZXR1cm47XG4gICAgcGVyZm9ybWFuY2UubWFyayhwZXJmb3JtYW5jZU1hcmtzLmFmdGVySHlkcmF0ZSkgLy8gbWFyayBlbmQgb2YgaHlkcmF0aW9uXG4gICAgO1xuICAgIGNvbnN0IGhhc0JlZm9yZVJlbmRlck1hcmsgPSBwZXJmb3JtYW5jZS5nZXRFbnRyaWVzQnlOYW1lKHBlcmZvcm1hbmNlTWFya3MuYmVmb3JlUmVuZGVyLCBcIm1hcmtcIikubGVuZ3RoO1xuICAgIGlmIChoYXNCZWZvcmVSZW5kZXJNYXJrKSB7XG4gICAgICAgIGNvbnN0IGJlZm9yZUh5ZHJhdGlvbk1lYXN1cmUgPSBwZXJmb3JtYW5jZS5tZWFzdXJlKHBlcmZvcm1hbmNlTWVhc3VyZXMuYmVmb3JlSHlkcmF0aW9uLCBwZXJmb3JtYW5jZU1hcmtzLm5hdmlnYXRpb25TdGFydCwgcGVyZm9ybWFuY2VNYXJrcy5iZWZvcmVSZW5kZXIpO1xuICAgICAgICBjb25zdCBoeWRyYXRpb25NZWFzdXJlID0gcGVyZm9ybWFuY2UubWVhc3VyZShwZXJmb3JtYW5jZU1lYXN1cmVzLmh5ZHJhdGlvbiwgcGVyZm9ybWFuY2VNYXJrcy5iZWZvcmVSZW5kZXIsIHBlcmZvcm1hbmNlTWFya3MuYWZ0ZXJIeWRyYXRlKTtcbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcImRldmVsb3BtZW50XCIgJiYgLy8gT2xkIHZlcnNpb25zIG9mIFNhZmFyaSBkb24ndCByZXR1cm4gYFBlcmZvcm1hbmNlTWVhc3VyZWBzIGZyb20gYHBlcmZvcm1hbmNlLm1lYXN1cmUoKWBcbiAgICAgICAgYmVmb3JlSHlkcmF0aW9uTWVhc3VyZSAhPT0gdW5kZWZpbmVkICYmIGh5ZHJhdGlvbk1lYXN1cmUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgX3RyYWNlci5kZWZhdWx0LnN0YXJ0U3BhbihcIm5hdmlnYXRpb24tdG8taHlkcmF0aW9uXCIsIHtcbiAgICAgICAgICAgICAgICBzdGFydFRpbWU6IHBlcmZvcm1hbmNlLnRpbWVPcmlnaW4gKyBiZWZvcmVIeWRyYXRpb25NZWFzdXJlLnN0YXJ0VGltZSxcbiAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiB7XG4gICAgICAgICAgICAgICAgICAgIHBhdGhuYW1lOiBsb2NhdGlvbi5wYXRobmFtZSxcbiAgICAgICAgICAgICAgICAgICAgcXVlcnk6IGxvY2F0aW9uLnNlYXJjaFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pLmVuZChwZXJmb3JtYW5jZS50aW1lT3JpZ2luICsgaHlkcmF0aW9uTWVhc3VyZS5zdGFydFRpbWUgKyBoeWRyYXRpb25NZWFzdXJlLmR1cmF0aW9uKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAob25QZXJmRW50cnkpIHtcbiAgICAgICAgcGVyZm9ybWFuY2UuZ2V0RW50cmllc0J5TmFtZShwZXJmb3JtYW5jZU1lYXN1cmVzLmh5ZHJhdGlvbikuZm9yRWFjaChvblBlcmZFbnRyeSk7XG4gICAgfVxuICAgIGNsZWFyTWFya3MoKTtcbn1cbmZ1bmN0aW9uIG1hcmtSZW5kZXJDb21wbGV0ZSgpIHtcbiAgICBpZiAoIV91dGlscy5TVCkgcmV0dXJuO1xuICAgIHBlcmZvcm1hbmNlLm1hcmsocGVyZm9ybWFuY2VNYXJrcy5hZnRlclJlbmRlcikgLy8gbWFyayBlbmQgb2YgcmVuZGVyXG4gICAgO1xuICAgIGNvbnN0IG5hdlN0YXJ0RW50cmllcyA9IHBlcmZvcm1hbmNlLmdldEVudHJpZXNCeU5hbWUocGVyZm9ybWFuY2VNYXJrcy5yb3V0ZUNoYW5nZSwgXCJtYXJrXCIpO1xuICAgIGlmICghbmF2U3RhcnRFbnRyaWVzLmxlbmd0aCkgcmV0dXJuO1xuICAgIGNvbnN0IGhhc0JlZm9yZVJlbmRlck1hcmsgPSBwZXJmb3JtYW5jZS5nZXRFbnRyaWVzQnlOYW1lKHBlcmZvcm1hbmNlTWFya3MuYmVmb3JlUmVuZGVyLCBcIm1hcmtcIikubGVuZ3RoO1xuICAgIGlmIChoYXNCZWZvcmVSZW5kZXJNYXJrKSB7XG4gICAgICAgIHBlcmZvcm1hbmNlLm1lYXN1cmUocGVyZm9ybWFuY2VNZWFzdXJlcy5yb3V0ZUNoYW5nZVRvUmVuZGVyLCBuYXZTdGFydEVudHJpZXNbMF0ubmFtZSwgcGVyZm9ybWFuY2VNYXJrcy5iZWZvcmVSZW5kZXIpO1xuICAgICAgICBwZXJmb3JtYW5jZS5tZWFzdXJlKHBlcmZvcm1hbmNlTWVhc3VyZXMucmVuZGVyLCBwZXJmb3JtYW5jZU1hcmtzLmJlZm9yZVJlbmRlciwgcGVyZm9ybWFuY2VNYXJrcy5hZnRlclJlbmRlcik7XG4gICAgICAgIGlmIChvblBlcmZFbnRyeSkge1xuICAgICAgICAgICAgcGVyZm9ybWFuY2UuZ2V0RW50cmllc0J5TmFtZShwZXJmb3JtYW5jZU1lYXN1cmVzLnJlbmRlcikuZm9yRWFjaChvblBlcmZFbnRyeSk7XG4gICAgICAgICAgICBwZXJmb3JtYW5jZS5nZXRFbnRyaWVzQnlOYW1lKHBlcmZvcm1hbmNlTWVhc3VyZXMucm91dGVDaGFuZ2VUb1JlbmRlcikuZm9yRWFjaChvblBlcmZFbnRyeSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2xlYXJNYXJrcygpO1xuICAgIFtcbiAgICAgICAgcGVyZm9ybWFuY2VNZWFzdXJlcy5yb3V0ZUNoYW5nZVRvUmVuZGVyLFxuICAgICAgICBwZXJmb3JtYW5jZU1lYXN1cmVzLnJlbmRlclxuICAgIF0uZm9yRWFjaCgobWVhc3VyZSk9PnBlcmZvcm1hbmNlLmNsZWFyTWVhc3VyZXMobWVhc3VyZSkpO1xufVxuZnVuY3Rpb24gcmVuZGVyUmVhY3RFbGVtZW50KGRvbUVsLCBmbikge1xuICAgIC8vIG1hcmsgc3RhcnQgb2YgaHlkcmF0ZS9yZW5kZXJcbiAgICBpZiAoX3V0aWxzLlNUKSB7XG4gICAgICAgIHBlcmZvcm1hbmNlLm1hcmsocGVyZm9ybWFuY2VNYXJrcy5iZWZvcmVSZW5kZXIpO1xuICAgIH1cbiAgICBjb25zdCByZWFjdEVsID0gZm4oc2hvdWxkSHlkcmF0ZSA/IG1hcmtIeWRyYXRlQ29tcGxldGUgOiBtYXJrUmVuZGVyQ29tcGxldGUpO1xuICAgIGlmICghcmVhY3RSb290KSB7XG4gICAgICAgIC8vIFVubGlrZSB3aXRoIGNyZWF0ZVJvb3QsIHlvdSBkb24ndCBuZWVkIGEgc2VwYXJhdGUgcm9vdC5yZW5kZXIoKSBjYWxsIGhlcmVcbiAgICAgICAgcmVhY3RSb290ID0gX2NsaWVudC5kZWZhdWx0Lmh5ZHJhdGVSb290KGRvbUVsLCByZWFjdEVsLCB7XG4gICAgICAgICAgICBvblJlY292ZXJhYmxlRXJyb3I6IF9vbnJlY292ZXJhYmxlZXJyb3IuZGVmYXVsdFxuICAgICAgICB9KTtcbiAgICAgICAgLy8gVE9ETzogUmVtb3ZlIHNob3VsZEh5ZHJhdGUgdmFyaWFibGUgd2hlbiBSZWFjdCAxOCBpcyBzdGFibGUgYXMgaXQgY2FuIGRlcGVuZCBvbiBgcmVhY3RSb290YCBleGlzdGluZ1xuICAgICAgICBzaG91bGRIeWRyYXRlID0gZmFsc2U7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3Qgc3RhcnRUcmFuc2l0aW9uID0gX3JlYWN0LmRlZmF1bHQuc3RhcnRUcmFuc2l0aW9uO1xuICAgICAgICBzdGFydFRyYW5zaXRpb24oKCk9PntcbiAgICAgICAgICAgIHJlYWN0Um9vdC5yZW5kZXIocmVhY3RFbCk7XG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmZ1bmN0aW9uIFJvb3QocGFyYW0pIHtcbiAgICBsZXQgeyBjYWxsYmFja3MsIGNoaWxkcmVuIH0gPSBwYXJhbTtcbiAgICAvLyBXZSB1c2UgYHVzZUxheW91dEVmZmVjdGAgdG8gZ3VhcmFudGVlIHRoZSBjYWxsYmFja3MgYXJlIGV4ZWN1dGVkXG4gICAgLy8gYXMgc29vbiBhcyBSZWFjdCBmbHVzaGVzIHRoZSB1cGRhdGVcbiAgICBfcmVhY3QuZGVmYXVsdC51c2VMYXlvdXRFZmZlY3QoKCk9PmNhbGxiYWNrcy5mb3JFYWNoKChjYWxsYmFjayk9PmNhbGxiYWNrKCkpLCBbXG4gICAgICAgIGNhbGxiYWNrc1xuICAgIF0pO1xuICAgIC8vIFdlIHNob3VsZCBhc2sgdG8gbWVhc3VyZSB0aGUgV2ViIFZpdGFscyBhZnRlciByZW5kZXJpbmcgY29tcGxldGVzIHNvIHdlXG4gICAgLy8gZG9uJ3QgY2F1c2UgYW55IGh5ZHJhdGlvbiBkZWxheTpcbiAgICBfcmVhY3QuZGVmYXVsdC51c2VFZmZlY3QoKCk9PntcbiAgICAgICAgKDAsIF9wZXJmb3JtYW5jZXJlbGF5ZXIuZGVmYXVsdCkob25QZXJmRW50cnkpO1xuICAgIH0sIFtdKTtcbiAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX1RFU1RfTU9ERSkge1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvcnVsZXMtb2YtaG9va3NcbiAgICAgICAgX3JlYWN0LmRlZmF1bHQudXNlRWZmZWN0KCgpPT57XG4gICAgICAgICAgICB3aW5kb3cuX19ORVhUX0hZRFJBVEVEID0gdHJ1ZTtcbiAgICAgICAgICAgIGlmICh3aW5kb3cuX19ORVhUX0hZRFJBVEVEX0NCKSB7XG4gICAgICAgICAgICAgICAgd2luZG93Ll9fTkVYVF9IWURSQVRFRF9DQigpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCBbXSk7XG4gICAgfVxuICAgIHJldHVybiBjaGlsZHJlbjtcbn1cbmZ1bmN0aW9uIGRvUmVuZGVyKGlucHV0KSB7XG4gICAgbGV0IHsgQXBwLCBDb21wb25lbnQsIHByb3BzLCBlcnIgfSA9IGlucHV0O1xuICAgIGxldCBzdHlsZVNoZWV0cyA9IFwiaW5pdGlhbFwiIGluIGlucHV0ID8gdW5kZWZpbmVkIDogaW5wdXQuc3R5bGVTaGVldHM7XG4gICAgQ29tcG9uZW50ID0gQ29tcG9uZW50IHx8IGxhc3RBcHBQcm9wcy5Db21wb25lbnQ7XG4gICAgcHJvcHMgPSBwcm9wcyB8fCBsYXN0QXBwUHJvcHMucHJvcHM7XG4gICAgY29uc3QgYXBwUHJvcHMgPSB7XG4gICAgICAgIC4uLnByb3BzLFxuICAgICAgICBDb21wb25lbnQsXG4gICAgICAgIGVycixcbiAgICAgICAgcm91dGVyXG4gICAgfTtcbiAgICAvLyBsYXN0QXBwUHJvcHMgaGFzIHRvIGJlIHNldCBiZWZvcmUgUmVhY3REb20ucmVuZGVyIHRvIGFjY291bnQgZm9yIFJlYWN0RG9tIHRocm93aW5nIGFuIGVycm9yLlxuICAgIGxhc3RBcHBQcm9wcyA9IGFwcFByb3BzO1xuICAgIGxldCBjYW5jZWxlZCA9IGZhbHNlO1xuICAgIGxldCByZXNvbHZlUHJvbWlzZTtcbiAgICBjb25zdCByZW5kZXJQcm9taXNlID0gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCk9PntcbiAgICAgICAgaWYgKGxhc3RSZW5kZXJSZWplY3QpIHtcbiAgICAgICAgICAgIGxhc3RSZW5kZXJSZWplY3QoKTtcbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlUHJvbWlzZSA9ICgpPT57XG4gICAgICAgICAgICBsYXN0UmVuZGVyUmVqZWN0ID0gbnVsbDtcbiAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgfTtcbiAgICAgICAgbGFzdFJlbmRlclJlamVjdCA9ICgpPT57XG4gICAgICAgICAgICBjYW5jZWxlZCA9IHRydWU7XG4gICAgICAgICAgICBsYXN0UmVuZGVyUmVqZWN0ID0gbnVsbDtcbiAgICAgICAgICAgIGNvbnN0IGVycm9yID0gbmV3IEVycm9yKFwiQ2FuY2VsIHJlbmRlcmluZyByb3V0ZVwiKTtcbiAgICAgICAgICAgIGVycm9yLmNhbmNlbGxlZCA9IHRydWU7XG4gICAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICB9O1xuICAgIH0pO1xuICAgIC8vIFRoaXMgZnVuY3Rpb24gaGFzIGEgcmV0dXJuIHR5cGUgdG8gZW5zdXJlIGl0IGRvZXNuJ3Qgc3RhcnQgcmV0dXJuaW5nIGFcbiAgICAvLyBQcm9taXNlLiBJdCBzaG91bGQgcmVtYWluIHN5bmNocm9ub3VzLlxuICAgIGZ1bmN0aW9uIG9uU3RhcnQoKSB7XG4gICAgICAgIGlmICghc3R5bGVTaGVldHMgfHwgLy8gV2UgdXNlIGBzdHlsZS1sb2FkZXJgIGluIGRldmVsb3BtZW50LCBzbyB3ZSBkb24ndCBuZWVkIHRvIGRvIGFueXRoaW5nXG4gICAgICAgIC8vIHVubGVzcyB3ZSdyZSBpbiBwcm9kdWN0aW9uOlxuICAgICAgICBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjdXJyZW50U3R5bGVUYWdzID0gbG9vc2VUb0FycmF5KGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCJzdHlsZVtkYXRhLW4taHJlZl1cIikpO1xuICAgICAgICBjb25zdCBjdXJyZW50SHJlZnMgPSBuZXcgU2V0KGN1cnJlbnRTdHlsZVRhZ3MubWFwKCh0YWcpPT50YWcuZ2V0QXR0cmlidXRlKFwiZGF0YS1uLWhyZWZcIikpKTtcbiAgICAgICAgY29uc3Qgbm9zY3JpcHQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKFwibm9zY3JpcHRbZGF0YS1uLWNzc11cIik7XG4gICAgICAgIGNvbnN0IG5vbmNlID0gbm9zY3JpcHQgPT0gbnVsbCA/IHZvaWQgMCA6IG5vc2NyaXB0LmdldEF0dHJpYnV0ZShcImRhdGEtbi1jc3NcIik7XG4gICAgICAgIHN0eWxlU2hlZXRzLmZvckVhY2goKHBhcmFtKT0+e1xuICAgICAgICAgICAgbGV0IHsgaHJlZiwgdGV4dCB9ID0gcGFyYW07XG4gICAgICAgICAgICBpZiAoIWN1cnJlbnRIcmVmcy5oYXMoaHJlZikpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBzdHlsZVRhZyA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgICAgICAgICAgICAgICBzdHlsZVRhZy5zZXRBdHRyaWJ1dGUoXCJkYXRhLW4taHJlZlwiLCBocmVmKTtcbiAgICAgICAgICAgICAgICBzdHlsZVRhZy5zZXRBdHRyaWJ1dGUoXCJtZWRpYVwiLCBcInhcIik7XG4gICAgICAgICAgICAgICAgaWYgKG5vbmNlKSB7XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlVGFnLnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChzdHlsZVRhZyk7XG4gICAgICAgICAgICAgICAgc3R5bGVUYWcuYXBwZW5kQ2hpbGQoZG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUodGV4dCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIG9uSGVhZENvbW1pdCgpIHtcbiAgICAgICAgaWYgKC8vIFdlIHVzZSBgc3R5bGUtbG9hZGVyYCBpbiBkZXZlbG9wbWVudCwgc28gd2UgZG9uJ3QgbmVlZCB0byBkbyBhbnl0aGluZ1xuICAgICAgICAvLyB1bmxlc3Mgd2UncmUgaW4gcHJvZHVjdGlvbjpcbiAgICAgICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiICYmIC8vIFdlIGNhbiBza2lwIHRoaXMgZHVyaW5nIGh5ZHJhdGlvbi4gUnVubmluZyBpdCB3b250IGNhdXNlIGFueSBoYXJtLCBidXRcbiAgICAgICAgLy8gd2UgbWF5IGFzIHdlbGwgc2F2ZSB0aGUgQ1BVIGN5Y2xlczpcbiAgICAgICAgc3R5bGVTaGVldHMgJiYgLy8gRW5zdXJlIHRoaXMgcmVuZGVyIHdhcyBub3QgY2FuY2VsZWRcbiAgICAgICAgIWNhbmNlbGVkKSB7XG4gICAgICAgICAgICBjb25zdCBkZXNpcmVkSHJlZnMgPSBuZXcgU2V0KHN0eWxlU2hlZXRzLm1hcCgocyk9PnMuaHJlZikpO1xuICAgICAgICAgICAgY29uc3QgY3VycmVudFN0eWxlVGFncyA9IGxvb3NlVG9BcnJheShkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKFwic3R5bGVbZGF0YS1uLWhyZWZdXCIpKTtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRIcmVmcyA9IGN1cnJlbnRTdHlsZVRhZ3MubWFwKCh0YWcpPT50YWcuZ2V0QXR0cmlidXRlKFwiZGF0YS1uLWhyZWZcIikpO1xuICAgICAgICAgICAgLy8gVG9nZ2xlIGA8c3R5bGU+YCB0YWdzIG9uIG9yIG9mZiBkZXBlbmRpbmcgb24gaWYgdGhleSdyZSBuZWVkZWQ6XG4gICAgICAgICAgICBmb3IobGV0IGlkeCA9IDA7IGlkeCA8IGN1cnJlbnRIcmVmcy5sZW5ndGg7ICsraWR4KXtcbiAgICAgICAgICAgICAgICBpZiAoZGVzaXJlZEhyZWZzLmhhcyhjdXJyZW50SHJlZnNbaWR4XSkpIHtcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFN0eWxlVGFnc1tpZHhdLnJlbW92ZUF0dHJpYnV0ZShcIm1lZGlhXCIpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRTdHlsZVRhZ3NbaWR4XS5zZXRBdHRyaWJ1dGUoXCJtZWRpYVwiLCBcInhcIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gUmVvcmRlciBzdHlsZXMgaW50byBpbnRlbmRlZCBvcmRlcjpcbiAgICAgICAgICAgIGxldCByZWZlcmVuY2VOb2RlID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihcIm5vc2NyaXB0W2RhdGEtbi1jc3NdXCIpO1xuICAgICAgICAgICAgaWYgKC8vIFRoaXMgc2hvdWxkIGJlIGFuIGludmFyaWFudDpcbiAgICAgICAgICAgIHJlZmVyZW5jZU5vZGUpIHtcbiAgICAgICAgICAgICAgICBzdHlsZVNoZWV0cy5mb3JFYWNoKChwYXJhbSk9PntcbiAgICAgICAgICAgICAgICAgICAgbGV0IHsgaHJlZiB9ID0gcGFyYW07XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldFRhZyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ3N0eWxlW2RhdGEtbi1ocmVmPVwiJyArIGhyZWYgKyAnXCJdJyk7XG4gICAgICAgICAgICAgICAgICAgIGlmICgvLyBUaGlzIHNob3VsZCBiZSBhbiBpbnZhcmlhbnQ6XG4gICAgICAgICAgICAgICAgICAgIHRhcmdldFRhZykge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVmZXJlbmNlTm9kZS5wYXJlbnROb2RlLmluc2VydEJlZm9yZSh0YXJnZXRUYWcsIHJlZmVyZW5jZU5vZGUubmV4dFNpYmxpbmcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVmZXJlbmNlTm9kZSA9IHRhcmdldFRhZztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRmluYWxseSwgY2xlYW4gdXAgc2VydmVyIHJlbmRlcmVkIHN0eWxlc2hlZXRzOlxuICAgICAgICAgICAgbG9vc2VUb0FycmF5KGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCJsaW5rW2RhdGEtbi1wXVwiKSkuZm9yRWFjaCgoZWwpPT57XG4gICAgICAgICAgICAgICAgZWwucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChlbCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaW5wdXQuc2Nyb2xsKSB7XG4gICAgICAgICAgICBjb25zdCB7IHgsIHkgfSA9IGlucHV0LnNjcm9sbDtcbiAgICAgICAgICAgICgwLCBfaGFuZGxlc21vb3Roc2Nyb2xsLmhhbmRsZVNtb290aFNjcm9sbCkoKCk9PntcbiAgICAgICAgICAgICAgICB3aW5kb3cuc2Nyb2xsVG8oeCwgeSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBvblJvb3RDb21taXQoKSB7XG4gICAgICAgIHJlc29sdmVQcm9taXNlKCk7XG4gICAgfVxuICAgIG9uU3RhcnQoKTtcbiAgICBjb25zdCBlbGVtID0gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9yZWFjdC5kZWZhdWx0LkZyYWdtZW50LCBudWxsLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSGVhZCwge1xuICAgICAgICBjYWxsYmFjazogb25IZWFkQ29tbWl0XG4gICAgfSksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChBcHBDb250YWluZXIsIG51bGwsIHJlbmRlckFwcChBcHAsIGFwcFByb3BzKSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9wb3J0YWwuUG9ydGFsLCB7XG4gICAgICAgIHR5cGU6IFwibmV4dC1yb3V0ZS1hbm5vdW5jZXJcIlxuICAgIH0sIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcm91dGVhbm5vdW5jZXIuUm91dGVBbm5vdW5jZXIsIG51bGwpKSkpO1xuICAgIC8vIFdlIGNhdGNoIHJ1bnRpbWUgZXJyb3JzIHVzaW5nIGNvbXBvbmVudERpZENhdGNoIHdoaWNoIHdpbGwgdHJpZ2dlciByZW5kZXJFcnJvclxuICAgIHJlbmRlclJlYWN0RWxlbWVudChhcHBFbGVtZW50LCAoY2FsbGJhY2spPT4vKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoUm9vdCwge1xuICAgICAgICAgICAgY2FsbGJhY2tzOiBbXG4gICAgICAgICAgICAgICAgY2FsbGJhY2ssXG4gICAgICAgICAgICAgICAgb25Sb290Q29tbWl0XG4gICAgICAgICAgICBdXG4gICAgICAgIH0sIHByb2Nlc3MuZW52Ll9fTkVYVF9TVFJJQ1RfTU9ERSA/IC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5TdHJpY3RNb2RlLCBudWxsLCBlbGVtKSA6IGVsZW0pKTtcbiAgICByZXR1cm4gcmVuZGVyUHJvbWlzZTtcbn1cbmFzeW5jIGZ1bmN0aW9uIHJlbmRlcihyZW5kZXJpbmdQcm9wcykge1xuICAgIGlmIChyZW5kZXJpbmdQcm9wcy5lcnIpIHtcbiAgICAgICAgYXdhaXQgcmVuZGVyRXJyb3IocmVuZGVyaW5nUHJvcHMpO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGRvUmVuZGVyKHJlbmRlcmluZ1Byb3BzKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgY29uc3QgcmVuZGVyRXJyID0gKDAsIF9pc2Vycm9yLmdldFByb3BlckVycm9yKShlcnIpO1xuICAgICAgICAvLyBidWJibGUgdXAgY2FuY2VsYXRpb24gZXJyb3JzXG4gICAgICAgIGlmIChyZW5kZXJFcnIuY2FuY2VsbGVkKSB7XG4gICAgICAgICAgICB0aHJvdyByZW5kZXJFcnI7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcImRldmVsb3BtZW50XCIpIHtcbiAgICAgICAgICAgIC8vIEVuc3VyZSB0aGlzIGVycm9yIGlzIGRpc3BsYXllZCBpbiB0aGUgb3ZlcmxheSBpbiBkZXZlbG9wbWVudFxuICAgICAgICAgICAgc2V0VGltZW91dCgoKT0+e1xuICAgICAgICAgICAgICAgIHRocm93IHJlbmRlckVycjtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGF3YWl0IHJlbmRlckVycm9yKHtcbiAgICAgICAgICAgIC4uLnJlbmRlcmluZ1Byb3BzLFxuICAgICAgICAgICAgZXJyOiByZW5kZXJFcnJcbiAgICAgICAgfSk7XG4gICAgfVxufVxuYXN5bmMgZnVuY3Rpb24gaHlkcmF0ZShvcHRzKSB7XG4gICAgbGV0IGluaXRpYWxFcnIgPSBpbml0aWFsRGF0YS5lcnI7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgYXBwRW50cnlwb2ludCA9IGF3YWl0IHBhZ2VMb2FkZXIucm91dGVMb2FkZXIud2hlbkVudHJ5cG9pbnQoXCIvX2FwcFwiKTtcbiAgICAgICAgaWYgKFwiZXJyb3JcIiBpbiBhcHBFbnRyeXBvaW50KSB7XG4gICAgICAgICAgICB0aHJvdyBhcHBFbnRyeXBvaW50LmVycm9yO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHsgY29tcG9uZW50OiBhcHAsIGV4cG9ydHM6IG1vZCB9ID0gYXBwRW50cnlwb2ludDtcbiAgICAgICAgQ2FjaGVkQXBwID0gYXBwO1xuICAgICAgICBpZiAobW9kICYmIG1vZC5yZXBvcnRXZWJWaXRhbHMpIHtcbiAgICAgICAgICAgIG9uUGVyZkVudHJ5ID0gKHBhcmFtKT0+e1xuICAgICAgICAgICAgICAgIGxldCB7IGlkLCBuYW1lLCBzdGFydFRpbWUsIHZhbHVlLCBkdXJhdGlvbiwgZW50cnlUeXBlLCBlbnRyaWVzLCBhdHRyaWJ1dGlvbiB9ID0gcGFyYW07XG4gICAgICAgICAgICAgICAgLy8gQ29tYmluZXMgdGltZXN0YW1wIHdpdGggcmFuZG9tIG51bWJlciBmb3IgdW5pcXVlIElEXG4gICAgICAgICAgICAgICAgY29uc3QgdW5pcXVlSUQgPSBEYXRlLm5vdygpICsgXCItXCIgKyAoTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogKDllMTIgLSAxKSkgKyAxZTEyKTtcbiAgICAgICAgICAgICAgICBsZXQgcGVyZlN0YXJ0RW50cnk7XG4gICAgICAgICAgICAgICAgaWYgKGVudHJpZXMgJiYgZW50cmllcy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgcGVyZlN0YXJ0RW50cnkgPSBlbnRyaWVzWzBdLnN0YXJ0VGltZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgd2ViVml0YWxzID0ge1xuICAgICAgICAgICAgICAgICAgICBpZDogaWQgfHwgdW5pcXVlSUQsXG4gICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0VGltZTogc3RhcnRUaW1lIHx8IHBlcmZTdGFydEVudHJ5LFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdmFsdWUgPT0gbnVsbCA/IGR1cmF0aW9uIDogdmFsdWUsXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsOiBlbnRyeVR5cGUgPT09IFwibWFya1wiIHx8IGVudHJ5VHlwZSA9PT0gXCJtZWFzdXJlXCIgPyBcImN1c3RvbVwiIDogXCJ3ZWItdml0YWxcIlxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgaWYgKGF0dHJpYnV0aW9uKSB7XG4gICAgICAgICAgICAgICAgICAgIHdlYlZpdGFscy5hdHRyaWJ1dGlvbiA9IGF0dHJpYnV0aW9uO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBtb2QucmVwb3J0V2ViVml0YWxzKHdlYlZpdGFscyk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHBhZ2VFbnRyeXBvaW50ID0gLy8gVGhlIGRldiBzZXJ2ZXIgZmFpbHMgdG8gc2VydmUgc2NyaXB0IGFzc2V0cyB3aGVuIHRoZXJlJ3MgYSBoeWRyYXRpb25cbiAgICAgICAgLy8gZXJyb3IsIHNvIHdlIG5lZWQgdG8gc2tpcCB3YWl0aW5nIGZvciB0aGUgZW50cnlwb2ludC5cbiAgICAgICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIiAmJiBpbml0aWFsRGF0YS5lcnIgPyB7XG4gICAgICAgICAgICBlcnJvcjogaW5pdGlhbERhdGEuZXJyXG4gICAgICAgIH0gOiBhd2FpdCBwYWdlTG9hZGVyLnJvdXRlTG9hZGVyLndoZW5FbnRyeXBvaW50KGluaXRpYWxEYXRhLnBhZ2UpO1xuICAgICAgICBpZiAoXCJlcnJvclwiIGluIHBhZ2VFbnRyeXBvaW50KSB7XG4gICAgICAgICAgICB0aHJvdyBwYWdlRW50cnlwb2ludC5lcnJvcjtcbiAgICAgICAgfVxuICAgICAgICBDYWNoZWRDb21wb25lbnQgPSBwYWdlRW50cnlwb2ludC5jb21wb25lbnQ7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgaXNWYWxpZEVsZW1lbnRUeXBlIH0gPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0LWlzXCIpO1xuICAgICAgICAgICAgaWYgKCFpc1ZhbGlkRWxlbWVudFR5cGUoQ2FjaGVkQ29tcG9uZW50KSkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignVGhlIGRlZmF1bHQgZXhwb3J0IGlzIG5vdCBhIFJlYWN0IENvbXBvbmVudCBpbiBwYWdlOiBcIicgKyBpbml0aWFsRGF0YS5wYWdlICsgJ1wiJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBUaGlzIGNhdGNoZXMgZXJyb3JzIGxpa2UgdGhyb3dpbmcgaW4gdGhlIHRvcCBsZXZlbCBvZiBhIG1vZHVsZVxuICAgICAgICBpbml0aWFsRXJyID0gKDAsIF9pc2Vycm9yLmdldFByb3BlckVycm9yKShlcnJvcik7XG4gICAgfVxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgIGNvbnN0IHsgZ2V0U2VydmVyRXJyb3IgfSA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvQG5leHQvcmVhY3QtZGV2LW92ZXJsYXkvZGlzdC9jbGllbnRcIik7XG4gICAgICAgIC8vIFNlcnZlci1zaWRlIHJ1bnRpbWUgZXJyb3JzIG5lZWQgdG8gYmUgcmUtdGhyb3duIG9uIHRoZSBjbGllbnQtc2lkZSBzb1xuICAgICAgICAvLyB0aGF0IHRoZSBvdmVybGF5IGlzIHJlbmRlcmVkLlxuICAgICAgICBpZiAoaW5pdGlhbEVycikge1xuICAgICAgICAgICAgaWYgKGluaXRpYWxFcnIgPT09IGluaXRpYWxEYXRhLmVycikge1xuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCk9PntcbiAgICAgICAgICAgICAgICAgICAgbGV0IGVycm9yO1xuICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gR2VuZXJhdGUgYSBuZXcgZXJyb3Igb2JqZWN0LiBXZSBgdGhyb3dgIGl0IGJlY2F1c2Ugc29tZSBicm93c2Vyc1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gd2lsbCBzZXQgdGhlIGBzdGFja2Agd2hlbiB0aHJvd24sIGFuZCB3ZSB3YW50IHRvIGVuc3VyZSBvdXJzIGlzXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBub3Qgb3ZlcnJpZGRlbiB3aGVuIHdlIHJlLXRocm93IGl0IGJlbG93LlxuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGluaXRpYWxFcnIubWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yID0gZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlcnJvci5uYW1lID0gaW5pdGlhbEVyci5uYW1lO1xuICAgICAgICAgICAgICAgICAgICBlcnJvci5zdGFjayA9IGluaXRpYWxFcnIuc3RhY2s7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGdldFNlcnZlckVycm9yKGVycm9yLCBpbml0aWFsRXJyLnNvdXJjZSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCk9PntcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgaW5pdGlhbEVycjtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAod2luZG93Ll9fTkVYVF9QUkVMT0FEUkVBRFkpIHtcbiAgICAgICAgYXdhaXQgd2luZG93Ll9fTkVYVF9QUkVMT0FEUkVBRFkoaW5pdGlhbERhdGEuZHluYW1pY0lkcyk7XG4gICAgfVxuICAgIHJvdXRlciA9ICgwLCBfcm91dGVyLmNyZWF0ZVJvdXRlcikoaW5pdGlhbERhdGEucGFnZSwgaW5pdGlhbERhdGEucXVlcnksIGFzUGF0aCwge1xuICAgICAgICBpbml0aWFsUHJvcHM6IGluaXRpYWxEYXRhLnByb3BzLFxuICAgICAgICBwYWdlTG9hZGVyLFxuICAgICAgICBBcHA6IENhY2hlZEFwcCxcbiAgICAgICAgQ29tcG9uZW50OiBDYWNoZWRDb21wb25lbnQsXG4gICAgICAgIHdyYXBBcHAsXG4gICAgICAgIGVycjogaW5pdGlhbEVycixcbiAgICAgICAgaXNGYWxsYmFjazogQm9vbGVhbihpbml0aWFsRGF0YS5pc0ZhbGxiYWNrKSxcbiAgICAgICAgc3Vic2NyaXB0aW9uOiAoaW5mbywgQXBwLCBzY3JvbGwpPT5yZW5kZXIoT2JqZWN0LmFzc2lnbih7fSwgaW5mbywge1xuICAgICAgICAgICAgICAgIEFwcCxcbiAgICAgICAgICAgICAgICBzY3JvbGxcbiAgICAgICAgICAgIH0pKSxcbiAgICAgICAgbG9jYWxlOiBpbml0aWFsRGF0YS5sb2NhbGUsXG4gICAgICAgIGxvY2FsZXM6IGluaXRpYWxEYXRhLmxvY2FsZXMsXG4gICAgICAgIGRlZmF1bHRMb2NhbGUsXG4gICAgICAgIGRvbWFpbkxvY2FsZXM6IGluaXRpYWxEYXRhLmRvbWFpbkxvY2FsZXMsXG4gICAgICAgIGlzUHJldmlldzogaW5pdGlhbERhdGEuaXNQcmV2aWV3XG4gICAgfSk7XG4gICAgaW5pdGlhbE1hdGNoZXNNaWRkbGV3YXJlID0gYXdhaXQgcm91dGVyLl9pbml0aWFsTWF0Y2hlc01pZGRsZXdhcmVQcm9taXNlO1xuICAgIGNvbnN0IHJlbmRlckN0eCA9IHtcbiAgICAgICAgQXBwOiBDYWNoZWRBcHAsXG4gICAgICAgIGluaXRpYWw6IHRydWUsXG4gICAgICAgIENvbXBvbmVudDogQ2FjaGVkQ29tcG9uZW50LFxuICAgICAgICBwcm9wczogaW5pdGlhbERhdGEucHJvcHMsXG4gICAgICAgIGVycjogaW5pdGlhbEVyclxuICAgIH07XG4gICAgaWYgKG9wdHMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdHMuYmVmb3JlUmVuZGVyKSB7XG4gICAgICAgIGF3YWl0IG9wdHMuYmVmb3JlUmVuZGVyKCk7XG4gICAgfVxuICAgIHJlbmRlcihyZW5kZXJDdHgpO1xufVxuXG5pZiAoKHR5cGVvZiBleHBvcnRzLmRlZmF1bHQgPT09ICdmdW5jdGlvbicgfHwgKHR5cGVvZiBleHBvcnRzLmRlZmF1bHQgPT09ICdvYmplY3QnICYmIGV4cG9ydHMuZGVmYXVsdCAhPT0gbnVsbCkpICYmIHR5cGVvZiBleHBvcnRzLmRlZmF1bHQuX19lc01vZHVsZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMuZGVmYXVsdCwgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuICBPYmplY3QuYXNzaWduKGV4cG9ydHMuZGVmYXVsdCwgZXhwb3J0cyk7XG4gIG1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJtb2R1bGUiLCJ2ZXJzaW9uIiwicm91dGVyIiwiZW1pdHRlciIsImluaXRpYWxpemUiLCJoeWRyYXRlIiwiX2V4cG9ydCIsInRhcmdldCIsImFsbCIsIm5hbWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdCIsIl8iLCJfY2xpZW50IiwiX2hlYWRtYW5hZ2VyY29udGV4dHNoYXJlZHJ1bnRpbWUiLCJfbWl0dCIsIl9yb3V0ZXJjb250ZXh0c2hhcmVkcnVudGltZSIsIl9oYW5kbGVzbW9vdGhzY3JvbGwiLCJfaXNkeW5hbWljIiwiX3F1ZXJ5c3RyaW5nIiwiX3J1bnRpbWVjb25maWdleHRlcm5hbCIsIl91dGlscyIsIl9wb3J0YWwiLCJfaGVhZG1hbmFnZXIiLCJfcGFnZWxvYWRlciIsIl9wZXJmb3JtYW5jZXJlbGF5ZXIiLCJfcm91dGVhbm5vdW5jZXIiLCJfcm91dGVyIiwiX2lzZXJyb3IiLCJfaW1hZ2Vjb25maWdjb250ZXh0c2hhcmVkcnVudGltZSIsIl9yZW1vdmViYXNlcGF0aCIsIl9oYXNiYXNlcGF0aCIsIl9hcHByb3V0ZXJjb250ZXh0c2hhcmVkcnVudGltZSIsIl9hZGFwdGVycyIsIl9ob29rc2NsaWVudGNvbnRleHRzaGFyZWRydW50aW1lIiwiX29ucmVjb3ZlcmFibGVlcnJvciIsIl90cmFjZXIiLCJfcmVwb3J0dG9zb2NrZXQiLCJkZWZhdWx0IiwibG9vc2VUb0FycmF5IiwiaW5wdXQiLCJzbGljZSIsImNhbGwiLCJpbml0aWFsRGF0YSIsImRlZmF1bHRMb2NhbGUiLCJ1bmRlZmluZWQiLCJhc1BhdGgiLCJwYWdlTG9hZGVyIiwiYXBwRWxlbWVudCIsImhlYWRNYW5hZ2VyIiwiaW5pdGlhbE1hdGNoZXNNaWRkbGV3YXJlIiwibGFzdEFwcFByb3BzIiwibGFzdFJlbmRlclJlamVjdCIsImRldkNsaWVudCIsIkNhY2hlZEFwcCIsIm9uUGVyZkVudHJ5IiwiQ2FjaGVkQ29tcG9uZW50IiwiQ29udGFpbmVyIiwiQ29tcG9uZW50IiwiY29tcG9uZW50RGlkQ2F0Y2giLCJjb21wb25lbnRFcnIiLCJpbmZvIiwicHJvcHMiLCJmbiIsImNvbXBvbmVudERpZE1vdW50Iiwic2Nyb2xsVG9IYXNoIiwiaXNTc3IiLCJpc0ZhbGxiYWNrIiwibmV4dEV4cG9ydCIsImlzRHluYW1pY1JvdXRlIiwicGF0aG5hbWUiLCJsb2NhdGlvbiIsInNlYXJjaCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfSEFTX1JFV1JJVEVTIiwiX19OX1NTRyIsInJlcGxhY2UiLCJTdHJpbmciLCJhc3NpZ24iLCJ1cmxRdWVyeVRvU2VhcmNoUGFyYW1zIiwicXVlcnkiLCJVUkxTZWFyY2hQYXJhbXMiLCJfaCIsInNoYWxsb3ciLCJjYXRjaCIsImVyciIsImNhbmNlbGxlZCIsImNvbXBvbmVudERpZFVwZGF0ZSIsImhhc2giLCJzdWJzdHJpbmciLCJlbCIsImRvY3VtZW50IiwiZ2V0RWxlbWVudEJ5SWQiLCJzZXRUaW1lb3V0Iiwic2Nyb2xsSW50b1ZpZXciLCJyZW5kZXIiLCJjaGlsZHJlbiIsIlJlYWN0RGV2T3ZlcmxheSIsImNyZWF0ZUVsZW1lbnQiLCJvcHRzIiwib25TcGFuRW5kIiwiSlNPTiIsInBhcnNlIiwidGV4dENvbnRlbnQiLCJ3aW5kb3ciLCJfX05FWFRfREFUQV9fIiwicHJlZml4IiwiYXNzZXRQcmVmaXgiLCJzZWxmIiwiX19uZXh0X3NldF9wdWJsaWNfcGF0aF9fIiwic2V0Q29uZmlnIiwic2VydmVyUnVudGltZUNvbmZpZyIsInB1YmxpY1J1bnRpbWVDb25maWciLCJydW50aW1lQ29uZmlnIiwiZ2V0VVJMIiwiaGFzQmFzZVBhdGgiLCJyZW1vdmVCYXNlUGF0aCIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwiZGV0ZWN0RG9tYWluTG9jYWxlIiwicGFyc2VSZWxhdGl2ZVVybCIsImZvcm1hdFVybCIsImxvY2FsZXMiLCJwYXJzZWRBcyIsImxvY2FsZVBhdGhSZXN1bHQiLCJkZXRlY3RlZExvY2FsZSIsImxvY2FsZSIsImRldGVjdGVkRG9tYWluIiwiX19ORVhUX0kxOE5fRE9NQUlOUyIsImhvc3RuYW1lIiwic2NyaXB0TG9hZGVyIiwiaW5pdFNjcmlwdExvYWRlciIsImJ1aWxkSWQiLCJyZWdpc3RlciIsInBhcmFtIiwiciIsImYiLCJyb3V0ZUxvYWRlciIsIm9uRW50cnlwb2ludCIsIl9fTkVYVF9QIiwibWFwIiwicCIsInB1c2giLCJnZXRJc1NzciIsInJlbmRlckFwcCIsIkFwcCIsImFwcFByb3BzIiwiQXBwQ29udGFpbmVyIiwiYWRhcHRlZEZvckFwcFJvdXRlciIsInVzZU1lbW8iLCJhZGFwdEZvckFwcFJvdXRlckluc3RhbmNlIiwiX3NlbGZfX19ORVhUX0RBVEFfX19hdXRvRXhwb3J0IiwiZXJyb3IiLCJyZW5kZXJFcnJvciIsImNvbnNvbGUiLCJBcHBSb3V0ZXJDb250ZXh0IiwiUHJvdmlkZXIiLCJTZWFyY2hQYXJhbXNDb250ZXh0IiwiYWRhcHRGb3JTZWFyY2hQYXJhbXMiLCJQYXRobmFtZUNvbnRleHRQcm92aWRlckFkYXB0ZXIiLCJpc0F1dG9FeHBvcnQiLCJhdXRvRXhwb3J0IiwiUGF0aFBhcmFtc0NvbnRleHQiLCJhZGFwdEZvclBhdGhQYXJhbXMiLCJSb3V0ZXJDb250ZXh0IiwibWFrZVB1YmxpY1JvdXRlckluc3RhbmNlIiwiSGVhZE1hbmFnZXJDb250ZXh0IiwiSW1hZ2VDb25maWdDb250ZXh0IiwiX19ORVhUX0lNQUdFX09QVFMiLCJ3cmFwQXBwIiwid3JhcHBlZEFwcFByb3BzIiwicmVuZGVyRXJyb3JQcm9wcyIsIm9uVW5yZWNvdmVyYWJsZUVycm9yIiwiZG9SZW5kZXIiLCJzdHlsZVNoZWV0cyIsImxvYWRQYWdlIiwidGhlbiIsInBhZ2UiLCJFcnJvckNvbXBvbmVudCIsImVycm9yTW9kdWxlIiwiYXBwTW9kdWxlIiwibSIsIl9yZW5kZXJFcnJvclByb3BzX3Byb3BzIiwiQXBwVHJlZSIsImFwcEN0eCIsImN0eCIsIlByb21pc2UiLCJyZXNvbHZlIiwibG9hZEdldEluaXRpYWxQcm9wcyIsImluaXRQcm9wcyIsIkhlYWQiLCJjYWxsYmFjayIsInVzZUxheW91dEVmZmVjdCIsInBlcmZvcm1hbmNlTWFya3MiLCJuYXZpZ2F0aW9uU3RhcnQiLCJiZWZvcmVSZW5kZXIiLCJhZnRlclJlbmRlciIsImFmdGVySHlkcmF0ZSIsInJvdXRlQ2hhbmdlIiwicGVyZm9ybWFuY2VNZWFzdXJlcyIsImh5ZHJhdGlvbiIsImJlZm9yZUh5ZHJhdGlvbiIsInJvdXRlQ2hhbmdlVG9SZW5kZXIiLCJyZWFjdFJvb3QiLCJzaG91bGRIeWRyYXRlIiwiY2xlYXJNYXJrcyIsImZvckVhY2giLCJtYXJrIiwicGVyZm9ybWFuY2UiLCJtYXJrSHlkcmF0ZUNvbXBsZXRlIiwiU1QiLCJoYXNCZWZvcmVSZW5kZXJNYXJrIiwiZ2V0RW50cmllc0J5TmFtZSIsImxlbmd0aCIsImJlZm9yZUh5ZHJhdGlvbk1lYXN1cmUiLCJtZWFzdXJlIiwiaHlkcmF0aW9uTWVhc3VyZSIsInN0YXJ0U3BhbiIsInN0YXJ0VGltZSIsInRpbWVPcmlnaW4iLCJhdHRyaWJ1dGVzIiwiZW5kIiwiZHVyYXRpb24iLCJtYXJrUmVuZGVyQ29tcGxldGUiLCJuYXZTdGFydEVudHJpZXMiLCJjbGVhck1lYXN1cmVzIiwicmVuZGVyUmVhY3RFbGVtZW50IiwiZG9tRWwiLCJyZWFjdEVsIiwiaHlkcmF0ZVJvb3QiLCJvblJlY292ZXJhYmxlRXJyb3IiLCJzdGFydFRyYW5zaXRpb24iLCJSb290IiwiY2FsbGJhY2tzIiwidXNlRWZmZWN0IiwiX19ORVhUX1RFU1RfTU9ERSIsIl9fTkVYVF9IWURSQVRFRCIsIl9fTkVYVF9IWURSQVRFRF9DQiIsImNhbmNlbGVkIiwicmVzb2x2ZVByb21pc2UiLCJyZW5kZXJQcm9taXNlIiwicmVqZWN0IiwiRXJyb3IiLCJvblN0YXJ0IiwiY3VycmVudFN0eWxlVGFncyIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJjdXJyZW50SHJlZnMiLCJTZXQiLCJ0YWciLCJnZXRBdHRyaWJ1dGUiLCJub3NjcmlwdCIsInF1ZXJ5U2VsZWN0b3IiLCJub25jZSIsImhyZWYiLCJ0ZXh0IiwiaGFzIiwic3R5bGVUYWciLCJzZXRBdHRyaWJ1dGUiLCJoZWFkIiwiYXBwZW5kQ2hpbGQiLCJjcmVhdGVUZXh0Tm9kZSIsIm9uSGVhZENvbW1pdCIsImRlc2lyZWRIcmVmcyIsInMiLCJpZHgiLCJyZW1vdmVBdHRyaWJ1dGUiLCJyZWZlcmVuY2VOb2RlIiwidGFyZ2V0VGFnIiwicGFyZW50Tm9kZSIsImluc2VydEJlZm9yZSIsIm5leHRTaWJsaW5nIiwicmVtb3ZlQ2hpbGQiLCJzY3JvbGwiLCJ4IiwieSIsImhhbmRsZVNtb290aFNjcm9sbCIsInNjcm9sbFRvIiwib25Sb290Q29tbWl0IiwiZWxlbSIsIkZyYWdtZW50IiwiUG9ydGFsIiwidHlwZSIsIlJvdXRlQW5ub3VuY2VyIiwiX19ORVhUX1NUUklDVF9NT0RFIiwiU3RyaWN0TW9kZSIsInJlbmRlcmluZ1Byb3BzIiwicmVuZGVyRXJyIiwiZ2V0UHJvcGVyRXJyb3IiLCJpbml0aWFsRXJyIiwiYXBwRW50cnlwb2ludCIsIndoZW5FbnRyeXBvaW50IiwiY29tcG9uZW50IiwiYXBwIiwibW9kIiwicmVwb3J0V2ViVml0YWxzIiwiaWQiLCJlbnRyeVR5cGUiLCJlbnRyaWVzIiwiYXR0cmlidXRpb24iLCJ1bmlxdWVJRCIsIkRhdGUiLCJub3ciLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJwZXJmU3RhcnRFbnRyeSIsIndlYlZpdGFscyIsImxhYmVsIiwicGFnZUVudHJ5cG9pbnQiLCJpc1ZhbGlkRWxlbWVudFR5cGUiLCJnZXRTZXJ2ZXJFcnJvciIsIm1lc3NhZ2UiLCJlIiwic3RhY2siLCJzb3VyY2UiLCJfX05FWFRfUFJFTE9BRFJFQURZIiwiZHluYW1pY0lkcyIsImNyZWF0ZVJvdXRlciIsImluaXRpYWxQcm9wcyIsIkJvb2xlYW4iLCJzdWJzY3JpcHRpb24iLCJkb21haW5Mb2NhbGVzIiwiaXNQcmV2aWV3IiwiX2luaXRpYWxNYXRjaGVzTWlkZGxld2FyZVByb21pc2UiLCJyZW5kZXJDdHgiLCJpbml0aWFsIiwiX19lc01vZHVsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/route-loader.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/route-loader.js ***!
  \*******************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    markAssetError: function() {\n        return markAssetError;\n    },\n    isAssetError: function() {\n        return isAssetError;\n    },\n    getClientBuildManifest: function() {\n        return getClientBuildManifest;\n    },\n    createRouteLoader: function() {\n        return createRouteLoader;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _getassetpathfromroute = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/router/utils/get-asset-path-from-route */ \"./node_modules/next/dist/shared/lib/router/utils/get-asset-path-from-route.js\"));\nconst _trustedtypes = __webpack_require__(/*! ./trusted-types */ \"./node_modules/next/dist/client/trusted-types.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst _deploymentid = __webpack_require__(/*! ../build/deployment-id */ \"./node_modules/next/dist/build/deployment-id.js\");\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800;\nfunction withFuture(key, map, generator) {\n    let entry = map.get(key);\n    if (entry) {\n        if (\"future\" in entry) {\n            return entry.future;\n        }\n        return Promise.resolve(entry);\n    }\n    let resolver;\n    const prom = new Promise((resolve)=>{\n        resolver = resolve;\n    });\n    map.set(key, entry = {\n        resolve: resolver,\n        future: prom\n    });\n    return generator ? generator() // eslint-disable-next-line no-sequences\n    .then((value)=>(resolver(value), value)).catch((err)=>{\n        map.delete(key);\n        throw err;\n    }) : prom;\n}\nconst ASSET_LOAD_ERROR = Symbol(\"ASSET_LOAD_ERROR\");\nfunction markAssetError(err) {\n    return Object.defineProperty(err, ASSET_LOAD_ERROR, {});\n}\nfunction isAssetError(err) {\n    return err && ASSET_LOAD_ERROR in err;\n}\nfunction hasPrefetch(link) {\n    try {\n        link = document.createElement(\"link\");\n        return(// with relList.support\n        !!window.MSInputMethodContext && !!document.documentMode || link.relList.supports(\"prefetch\"));\n    } catch (e) {\n        return false;\n    }\n}\nconst canPrefetch = hasPrefetch();\nconst getAssetQueryString = ()=>{\n    return (0, _deploymentid.getDeploymentIdQueryOrEmptyString)();\n};\nfunction prefetchViaDom(href, as, link) {\n    return new Promise((resolve, reject)=>{\n        const selector = '\\n      link[rel=\"prefetch\"][href^=\"' + href + '\"],\\n      link[rel=\"preload\"][href^=\"' + href + '\"],\\n      script[src^=\"' + href + '\"]';\n        if (document.querySelector(selector)) {\n            return resolve();\n        }\n        link = document.createElement(\"link\");\n        // The order of property assignment here is intentional:\n        if (as) link.as = as;\n        link.rel = \"prefetch\";\n        link.crossOrigin = undefined;\n        link.onload = resolve;\n        link.onerror = ()=>reject(markAssetError(new Error(\"Failed to prefetch: \" + href)));\n        // `href` should always be last:\n        link.href = href;\n        document.head.appendChild(link);\n    });\n}\nfunction appendScript(src, script) {\n    return new Promise((resolve, reject)=>{\n        script = document.createElement(\"script\");\n        // The order of property assignment here is intentional.\n        // 1. Setup success/failure hooks in case the browser synchronously\n        //    executes when `src` is set.\n        script.onload = resolve;\n        script.onerror = ()=>reject(markAssetError(new Error(\"Failed to load script: \" + src)));\n        // 2. Configure the cross-origin attribute before setting `src` in case the\n        //    browser begins to fetch.\n        script.crossOrigin = undefined;\n        // 3. Finally, set the source and inject into the DOM in case the child\n        //    must be appended for fetching to start.\n        script.src = src;\n        document.body.appendChild(script);\n    });\n}\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise;\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout(p, ms, err) {\n    return new Promise((resolve, reject)=>{\n        let cancelled = false;\n        p.then((r)=>{\n            // Resolved, cancel the timeout\n            cancelled = true;\n            resolve(r);\n        }).catch(reject);\n        // We wrap these checks separately for better dead-code elimination in\n        // production bundles.\n        if (true) {\n            (devBuildPromise || Promise.resolve()).then(()=>{\n                (0, _requestidlecallback.requestIdleCallback)(()=>setTimeout(()=>{\n                        if (!cancelled) {\n                            reject(err);\n                        }\n                    }, ms));\n            });\n        }\n        if (false) {}\n    });\n}\nfunction getClientBuildManifest() {\n    if (self.__BUILD_MANIFEST) {\n        return Promise.resolve(self.__BUILD_MANIFEST);\n    }\n    const onBuildManifest = new Promise((resolve)=>{\n        // Mandatory because this is not concurrent safe:\n        const cb = self.__BUILD_MANIFEST_CB;\n        self.__BUILD_MANIFEST_CB = ()=>{\n            resolve(self.__BUILD_MANIFEST);\n            cb && cb();\n        };\n    });\n    return resolvePromiseWithTimeout(onBuildManifest, MS_MAX_IDLE_DELAY, markAssetError(new Error(\"Failed to load client build manifest\")));\n}\nfunction getFilesForRoute(assetPrefix, route) {\n    if (true) {\n        const scriptUrl = assetPrefix + \"/_next/static/chunks/pages\" + encodeURI((0, _getassetpathfromroute.default)(route, \".js\")) + getAssetQueryString();\n        return Promise.resolve({\n            scripts: [\n                (0, _trustedtypes.__unsafeCreateTrustedScriptURL)(scriptUrl)\n            ],\n            // Styles are handled by `style-loader` in development:\n            css: []\n        });\n    }\n    return getClientBuildManifest().then((manifest)=>{\n        if (!(route in manifest)) {\n            throw markAssetError(new Error(\"Failed to lookup route: \" + route));\n        }\n        const allFiles = manifest[route].map((entry)=>assetPrefix + \"/_next/\" + encodeURI(entry));\n        return {\n            scripts: allFiles.filter((v)=>v.endsWith(\".js\")).map((v)=>(0, _trustedtypes.__unsafeCreateTrustedScriptURL)(v) + getAssetQueryString()),\n            css: allFiles.filter((v)=>v.endsWith(\".css\")).map((v)=>v + getAssetQueryString())\n        };\n    });\n}\nfunction createRouteLoader(assetPrefix) {\n    const entrypoints = new Map();\n    const loadedScripts = new Map();\n    const styleSheets = new Map();\n    const routes = new Map();\n    function maybeExecuteScript(src) {\n        // With HMR we might need to \"reload\" scripts when they are\n        // disposed and readded. Executing scripts twice has no functional\n        // differences\n        if (false) {} else {\n            return appendScript(src);\n        }\n    }\n    function fetchStyleSheet(href) {\n        let prom = styleSheets.get(href);\n        if (prom) {\n            return prom;\n        }\n        styleSheets.set(href, prom = fetch(href).then((res)=>{\n            if (!res.ok) {\n                throw new Error(\"Failed to load stylesheet: \" + href);\n            }\n            return res.text().then((text)=>({\n                    href: href,\n                    content: text\n                }));\n        }).catch((err)=>{\n            throw markAssetError(err);\n        }));\n        return prom;\n    }\n    return {\n        whenEntrypoint (route) {\n            return withFuture(route, entrypoints);\n        },\n        onEntrypoint (route, execute) {\n            (execute ? Promise.resolve().then(()=>execute()).then((exports1)=>({\n                    component: exports1 && exports1.default || exports1,\n                    exports: exports1\n                }), (err)=>({\n                    error: err\n                })) : Promise.resolve(undefined)).then((input)=>{\n                const old = entrypoints.get(route);\n                if (old && \"resolve\" in old) {\n                    if (input) {\n                        entrypoints.set(route, input);\n                        old.resolve(input);\n                    }\n                } else {\n                    if (input) {\n                        entrypoints.set(route, input);\n                    } else {\n                        entrypoints.delete(route);\n                    }\n                    // when this entrypoint has been resolved before\n                    // the route is outdated and we want to invalidate\n                    // this cache entry\n                    routes.delete(route);\n                }\n            });\n        },\n        loadRoute (route, prefetch) {\n            return withFuture(route, routes, ()=>{\n                let devBuildPromiseResolve;\n                if (true) {\n                    devBuildPromise = new Promise((resolve)=>{\n                        devBuildPromiseResolve = resolve;\n                    });\n                }\n                return resolvePromiseWithTimeout(getFilesForRoute(assetPrefix, route).then((param)=>{\n                    let { scripts, css } = param;\n                    return Promise.all([\n                        entrypoints.has(route) ? [] : Promise.all(scripts.map(maybeExecuteScript)),\n                        Promise.all(css.map(fetchStyleSheet))\n                    ]);\n                }).then((res)=>{\n                    return this.whenEntrypoint(route).then((entrypoint)=>({\n                            entrypoint,\n                            styles: res[1]\n                        }));\n                }), MS_MAX_IDLE_DELAY, markAssetError(new Error(\"Route did not complete loading: \" + route))).then((param)=>{\n                    let { entrypoint, styles } = param;\n                    const res = Object.assign({\n                        styles: styles\n                    }, entrypoint);\n                    return \"error\" in entrypoint ? entrypoint : res;\n                }).catch((err)=>{\n                    if (prefetch) {\n                        // we don't want to cache errors during prefetch\n                        throw err;\n                    }\n                    return {\n                        error: err\n                    };\n                }).finally(()=>devBuildPromiseResolve == null ? void 0 : devBuildPromiseResolve());\n            });\n        },\n        prefetch (route) {\n            // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n            // License: Apache 2.0\n            let cn;\n            if (cn = navigator.connection) {\n                // Don't prefetch if using 2G or if Save-Data is enabled.\n                if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve();\n            }\n            return getFilesForRoute(assetPrefix, route).then((output)=>Promise.all(canPrefetch ? output.scripts.map((script)=>prefetchViaDom(script.toString(), \"script\")) : [])).then(()=>{\n                (0, _requestidlecallback.requestIdleCallback)(()=>this.loadRoute(route, true).catch(()=>{}));\n            }).catch(()=>{});\n        }\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=route-loader.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/route-loader.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/webpack.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/client/webpack.js ***!
  \**************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _deploymentid = __webpack_require__(/*! ../build/deployment-id */ \"./node_modules/next/dist/build/deployment-id.js\");\n// If we have a deployment ID, we need to append it to the webpack chunk names\n// I am keeping the process check explicit so this can be statically optimized\nif (false) {}\nself.__next_set_public_path__ = (path)=>{\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    __webpack_require__.p = path;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=webpack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/webpack.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst isServer = \"object\" === \"undefined\";\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    _s();\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect(()=>{\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        return ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n        };\n    });\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect(()=>{\n        if (headManager) {\n            headManager._pendingUpdate = emitChange;\n        }\n        return ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n        };\n    });\n    useClientOnlyEffect(()=>{\n        if (headManager && headManager._pendingUpdate) {\n            headManager._pendingUpdate();\n            headManager._pendingUpdate = null;\n        }\n        return ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n        };\n    });\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function() {\n    return [\n        useClientOnlyLayoutEffect,\n        useClientOnlyLayoutEffect,\n        useClientOnlyEffect\n    ];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ })

});