{"/_not-found/page": "app/_not-found/page.js", "/api/admin/ai-cost/route": "app/api/admin/ai-cost/route.js", "/api/admin/backup/[id]/restore/route": "app/api/admin/backup/[id]/restore/route.js", "/api/admin/backup/route": "app/api/admin/backup/route.js", "/api/admin/performance/optimize/route": "app/api/admin/performance/optimize/route.js", "/api/admin/performance/route": "app/api/admin/performance/route.js", "/api/ai-agents/route": "app/api/ai-agents/route.js", "/api/auth/mfa/challenge/route": "app/api/auth/mfa/challenge/route.js", "/api/auth/mfa/verify/route": "app/api/auth/mfa/verify/route.js", "/api/auth/password-reset/route": "app/api/auth/password-reset/route.js", "/api/health/route": "app/api/health/route.js", "/api/health/simple/route": "app/api/health/simple/route.js", "/api/integrations/[integration]/route": "app/api/integrations/[integration]/route.js", "/api/knowledge-base/faq/route": "app/api/knowledge-base/faq/route.js", "/api/knowledge-base/update-content/route": "app/api/knowledge-base/update-content/route.js", "/api/metrics/route": "app/api/metrics/route.js", "/api/workflows/approval-chains/[id]/route": "app/api/workflows/approval-chains/[id]/route.js", "/api/workflows/approvals/decision/route": "app/api/workflows/approvals/decision/route.js", "/api/workflows/approvals/delegate/route": "app/api/workflows/approvals/delegate/route.js", "/api/workflows/definitions/[id]/route": "app/api/workflows/definitions/[id]/route.js", "/api/workflows/definitions/route": "app/api/workflows/definitions/route.js", "/api/workflows/escalation/[id]/route": "app/api/workflows/escalation/[id]/route.js", "/api/workflows/escalation/route": "app/api/workflows/escalation/route.js", "/api/workflows/escalation/trigger/route": "app/api/workflows/escalation/trigger/route.js", "/api/workflows/instances/[id]/cancel/route": "app/api/workflows/instances/[id]/cancel/route.js", "/api/workflows/instances/[id]/route": "app/api/workflows/instances/[id]/route.js", "/api/workflows/instances/route": "app/api/workflows/instances/route.js", "/api/workflows/monitoring/categories/route": "app/api/workflows/monitoring/categories/route.js", "/api/workflows/monitoring/export/route": "app/api/workflows/monitoring/export/route.js", "/api/workflows/monitoring/instances/route": "app/api/workflows/monitoring/instances/route.js", "/api/workflows/monitoring/metrics/route": "app/api/workflows/monitoring/metrics/route.js", "/api/workflows/monitoring/trends/route": "app/api/workflows/monitoring/trends/route.js", "/api/workflows/rules/route": "app/api/workflows/rules/route.js", "/api/workflows/sla/at-risk/route": "app/api/workflows/sla/at-risk/route.js", "/api/workflows/sla/export/route": "app/api/workflows/sla/export/route.js", "/api/workflows/sla/metrics/route": "app/api/workflows/sla/metrics/route.js", "/api/workflows/sla/route": "app/api/workflows/sla/route.js", "/api/workflows/tasks/route": "app/api/workflows/tasks/route.js", "/api/workflows/templates/[id]/route": "app/api/workflows/templates/[id]/route.js", "/api/workflows/templates/apply/route": "app/api/workflows/templates/apply/route.js", "/api/workflows/templates/route": "app/api/workflows/templates/route.js", "/api/embeddings/update-stale/route": "app/api/embeddings/update-stale/route.js", "/api/embeddings/related/route": "app/api/embeddings/related/route.js", "/api/embeddings/search/route": "app/api/embeddings/search/route.js", "/admin/performance/page": "app/admin/performance/page.js", "/admin/audit/page": "app/admin/audit/page.js", "/admin/roles/page": "app/admin/roles/page.js", "/dashboard/it-helpdesk/pc-admin-requests/page": "app/dashboard/it-helpdesk/pc-admin-requests/page.js", "/batch-processing/page": "app/batch-processing/page.js", "/dashboard/it-helpdesk/password-reset/page": "app/dashboard/it-helpdesk/password-reset/page.js", "/dashboard/workflows/monitoring/page": "app/dashboard/workflows/monitoring/page.js", "/dashboard/page": "app/dashboard/page.js", "/group-mail-management/page": "app/group-mail-management/page.js", "/login/page": "app/login/page.js", "/mailbox-management/page": "app/mailbox-management/page.js", "/mfa-verify/page": "app/mfa-verify/page.js", "/page": "app/page.js", "/pc-admin/page": "app/pc-admin/page.js", "/request/enhanced/page": "app/request/enhanced/page.js", "/requests/[id]/page": "app/requests/[id]/page.js", "/request/page": "app/request/page.js", "/setup-required/page": "app/setup-required/page.js", "/test-ai-api/page": "app/test-ai-api/page.js", "/sharepoint-management/page": "app/sharepoint-management/page.js", "/test-ai-agents/page": "app/test-ai-agents/page.js", "/test-ai-validation/page": "app/test-ai-validation/page.js", "/test-audit-trail/page": "app/test-audit-trail/page.js", "/test-auto-update/page": "app/test-auto-update/page.js", "/test-batch-processing/page": "app/test-batch-processing/page.js", "/test-chatbot/page": "app/test-chatbot/page.js", "/test-embeddings/page": "app/test-embeddings/page.js", "/test-error-detection/page": "app/test-error-detection/page.js", "/test-faq-generation/page": "app/test-faq-generation/page.js", "/test-historical-analysis/page": "app/test-historical-analysis/page.js", "/test-nlp/page": "app/test-nlp/page.js", "/test-japanese/page": "app/test-japanese/page.js", "/test-notifications/page": "app/test-notifications/page.js", "/test-predictive-form/page": "app/test-predictive-form/page.js", "/test/page": "app/test/page.js", "/knowledge-base/page": "app/knowledge-base/page.js", "/knowledge-base/article/[slug]/page": "app/knowledge-base/article/[slug]/page.js"}