import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type Article = Database['public']['Tables']['kb_articles']['Row'];
type ArticlePermission = Database['public']['Tables']['kb_article_permissions']['Row'];

export interface ArticleWithPermissions extends Article {
  permissions?: ArticlePermission[];
}

export class KnowledgeBasePermissionsService {
  /**
   * Check if a user has permission to view an article
   */
  static async canUserViewArticle(articleId: string, userId: string): Promise<boolean> {
    try {
      // First check if the article is public
      const { data: article, error: articleError } = await supabase
        .from('kb_articles')
        .select('visibility, status')
        .eq('id', articleId)
        .single();

      if (articleError || !article) {
        console.error('Error fetching article:', articleError);
        return false;
      }

      // Draft and archived articles are not viewable unless user has edit permissions
      if (article.status !== 'published') {
        return await this.canUserEditArticle(articleId, userId);
      }

      // Public articles are viewable by all
      if (article.visibility === 'public') {
        return true;
      }

      // Get user's role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, division_id, group_id')
        .eq('id', userId)
        .single();

      if (profileError || !profile) {
        console.error('Error fetching user profile:', profileError);
        return false;
      }

      // Internal articles are viewable by all authenticated users
      if (article.visibility === 'internal') {
        return true;
      }

      // Restricted articles require specific permissions
      if (article.visibility === 'restricted') {
        // Check if user's role has permission
        const { data: permission, error: permError } = await supabase
          .from('kb_article_permissions')
          .select('can_view')
          .eq('article_id', articleId)
          .eq('role_id', profile.role)
          .single();

        if (!permError && permission?.can_view) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking article view permission:', error);
      return false;
    }
  }

  /**
   * Check if a user has permission to edit an article
   */
  static async canUserEditArticle(articleId: string, userId: string): Promise<boolean> {
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single();

      if (profileError || !profile) {
        console.error('Error fetching user profile:', profileError);
        return false;
      }

      // Global and system admins can edit all articles
      if (['global_admin', 'system_admin', 'helpdesk_support'].includes(profile.role)) {
        return true;
      }

      // Check specific edit permissions
      const { data: permission, error: permError } = await supabase
        .from('kb_article_permissions')
        .select('can_edit')
        .eq('article_id', articleId)
        .eq('role_id', profile.role)
        .single();

      return !permError && permission?.can_edit === true;
    } catch (error) {
      console.error('Error checking article edit permission:', error);
      return false;
    }
  }

  /**
   * Get filtered articles based on user's role and permissions
   */
  static async getFilteredArticles(userId: string, options?: {
    categoryId?: string;
    searchQuery?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ articles: ArticleWithPermissions[]; count: number }> {
    try {
      // Get user's role and profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, division_id, group_id')
        .eq('id', userId)
        .single();

      if (profileError || !profile) {
        console.error('Error fetching user profile:', profileError);
        return { articles: [], count: 0 };
      }

      let query = supabase
        .from('kb_articles')
        .select(`
          *,
          kb_article_permissions (
            role_id,
            can_view,
            can_edit
          )
        `, { count: 'exact' });

      // Apply filters
      if (options?.categoryId) {
        query = query.eq('category_id', options.categoryId);
      }

      if (options?.status) {
        query = query.eq('status', options.status);
      } else {
        // Default to published articles for non-admin users
        if (!['global_admin', 'system_admin', 'helpdesk_support'].includes(profile.role)) {
          query = query.eq('status', 'published');
        }
      }

      // Apply search if provided
      if (options?.searchQuery) {
        query = query.or(`title_en.ilike.%${options.searchQuery}%,title_jp.ilike.%${options.searchQuery}%,content_en.ilike.%${options.searchQuery}%,content_jp.ilike.%${options.searchQuery}%`);
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data: articles, error, count } = await query;

      if (error) {
        console.error('Error fetching articles:', error);
        return { articles: [], count: 0 };
      }

      // Filter articles based on visibility and permissions
      const filteredArticles = articles?.filter(article => {
        // Public articles are visible to all
        if (article.visibility === 'public' && article.status === 'published') {
          return true;
        }

        // Internal articles are visible to authenticated users
        if (article.visibility === 'internal' && article.status === 'published') {
          return true;
        }

        // Admins can see all articles
        if (['global_admin', 'system_admin', 'helpdesk_support'].includes(profile.role)) {
          return true;
        }

        // Restricted articles require specific permissions
        if (article.visibility === 'restricted') {
          const permission = article.kb_article_permissions?.find(
            p => p.role_id === profile.role && p.can_view
          );
          return !!permission;
        }

        return false;
      }) || [];

      return { articles: filteredArticles, count: filteredArticles.length };
    } catch (error) {
      console.error('Error getting filtered articles:', error);
      return { articles: [], count: 0 };
    }
  }

  /**
   * Set article permissions for a specific role
   */
  static async setArticlePermissions(
    articleId: string,
    roleId: string,
    permissions: { can_view: boolean; can_edit: boolean }
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('kb_article_permissions')
        .upsert({
          article_id: articleId,
          role_id: roleId,
          can_view: permissions.can_view,
          can_edit: permissions.can_edit
        }, {
          onConflict: 'article_id,role_id'
        });

      if (error) {
        console.error('Error setting article permissions:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in setArticlePermissions:', error);
      return false;
    }
  }

  /**
   * Get all permissions for an article
   */
  static async getArticlePermissions(articleId: string): Promise<ArticlePermission[]> {
    try {
      const { data, error } = await supabase
        .from('kb_article_permissions')
        .select(`
          *,
          roles (
            id,
            name,
            description
          )
        `)
        .eq('article_id', articleId);

      if (error) {
        console.error('Error fetching article permissions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getArticlePermissions:', error);
      return [];
    }
  }

  /**
   * Remove permissions for an article and role
   */
  static async removeArticlePermissions(articleId: string, roleId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('kb_article_permissions')
        .delete()
        .eq('article_id', articleId)
        .eq('role_id', roleId);

      if (error) {
        console.error('Error removing article permissions:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in removeArticlePermissions:', error);
      return false;
    }
  }

  /**
   * Get articles by permission level for a specific role
   */
  static async getArticlesByPermission(roleId: string, permissionType: 'view' | 'edit'): Promise<Article[]> {
    try {
      const column = permissionType === 'view' ? 'can_view' : 'can_edit';
      
      const { data, error } = await supabase
        .from('kb_article_permissions')
        .select(`
          kb_articles (*)
        `)
        .eq('role_id', roleId)
        .eq(column, true);

      if (error) {
        console.error('Error fetching articles by permission:', error);
        return [];
      }

      return data?.map(item => item.kb_articles).filter(Boolean) as Article[] || [];
    } catch (error) {
      console.error('Error in getArticlesByPermission:', error);
      return [];
    }
  }
}
