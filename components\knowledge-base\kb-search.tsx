'use client'

/**
 * Knowledge Base Search Component
 * Advanced search with AI-powered semantic search capabilities
 */

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Search, Sparkles } from 'lucide-react'
import { useLanguage } from '@/contexts/language-context'

interface SearchResult {
  article_id: string
  title: string
  summary: string
  similarity: number
  category: string
}

interface KBSearchProps {
  onResultSelect?: (articleId: string) => void
  placeholder?: string
  enableAI?: boolean
}

export function KBSearch({ onResultSelect, placeholder, enableAI = true }: KBSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const { language } = useLanguage()

  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsSearching(true)
    try {
      // Simulate search API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const mockResults: SearchResult[] = [
        {
          article_id: '1',
          title: 'Password Reset Guide',
          summary: 'Step-by-step instructions for resetting your password',
          similarity: 0.95,
          category: 'Account'
        },
        {
          article_id: '2',
          title: 'Software Installation',
          summary: 'How to request and install new software',
          similarity: 0.87,
          category: 'Software'
        }
      ]

      setResults(mockResults)
    } catch (error) {
      console.error('Search failed:', error)
      setResults([])
    } finally {
      setIsSearching(false)
    }
  }

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      handleSearch(query)
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query])

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder={placeholder || (language === 'en' ? 'Search knowledge base...' : 'ナレッジベースを検索...')}
              className="pl-10"
            />
          </div>
          {enableAI && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Sparkles className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    {language === 'en'
                      ? 'AI-powered semantic search finds articles based on meaning, not just keywords'
                      : 'AI搭載のセマンティック検索は、キーワードだけでなく意味に基づいて記事を検索します'
                    }
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* Search Results */}
      {results.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold">
                {language === 'en' ? 'Search Results' : '検索結果'}
              </h3>
              <Badge variant="secondary">
                {results.length} {language === 'en' ? 'results' : '件'}
              </Badge>
            </div>
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div
                    key={result.article_id}
                    className="group cursor-pointer rounded-lg border p-3 transition-colors hover:bg-accent"
                    onClick={() => {
                      // Navigate to article or trigger article view
                      window.location.href = `/knowledge-base/article/${result.article_id}`
                    }}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 space-y-1">
                        <h4 className="text-sm font-medium line-clamp-1 group-hover:text-primary">
                          {result.title}
                        </h4>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {result.summary}
                        </p>
                      </div>
                      <Badge
                        variant="outline"
                        className="ml-2 shrink-0"
                      >
                        {(result.similarity * 100).toFixed(0)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
}