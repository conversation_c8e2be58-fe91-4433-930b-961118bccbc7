 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Sparkles className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  {language === 'en' 
                    ? 'AI-powered semantic search finds articles based on meaning, not just keywords'
                    : 'AI搭載のセマンティック検索は、キーワードだけでなく意味に基づいて記事を検索します'
                  }
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* Search Results */}
      {results.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold">
                {language === 'en' ? 'Search Results' : '検索結果'}
              </h3>
              <Badge variant="secondary">
                {results.length} {language === 'en' ? 'results' : '件'}
              </Badge>
            </div>
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div
                    key={result.article_id}
                    className="group cursor-pointer rounded-lg border p-3 transition-colors hover:bg-accent"
                    onClick={() => {
                      // Navigate to article or trigger article view
                      window.location.href = `/knowledge-base/article/${result.article_id}`
                    }}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 space-y-1">
                        <h4 className="text-sm font-medium line-clamp-1 group-hover:text-primary">
                          {result.title}
                        </h4>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {result.summary}
                        </p>
                      </div>
                      <Badge 
                        variant="outline" 
                        className="ml-2 shrink-0"
                      >
                        {(result.similarity * 100).toFixed(0)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
}