/**
 * Health Check API Endpoint
 * Provides comprehensive system health monitoring
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  version: string
  uptime: number
  checks: {
    database: HealthCheck
    ai_services: HealthCheck
    redis: HealthCheck
    external_apis: HealthCheck
    disk_space: HealthCheck
    memory: HealthCheck
  }
  metadata: {
    environment: string
    node_version: string
    next_version: string
    deployment_id?: string
  }
}

interface HealthCheck {
  status: 'pass' | 'fail' | 'warn'
  responseTime?: number
  message?: string
  details?: any
}

const startTime = Date.now()

/**
 * Check database connectivity and performance
 */
async function checkDatabase(): Promise<HealthCheck> {
  const start = Date.now()
  
  try {
    const supabase = createClient()
    
    // Test basic connectivity
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1)
    
    if (error) {
      return {
        status: 'fail',
        responseTime: Date.now() - start,
        message: 'Database query failed',
        details: { error: error.message }
      }
    }

    const responseTime = Date.now() - start
    
    return {
      status: responseTime > 1000 ? 'warn' : 'pass',
      responseTime,
      message: responseTime > 1000 ? 'Slow database response' : 'Database healthy',
      details: { 
        connection: 'active',
        query_time: `${responseTime}ms`
      }
    }
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - start,
      message: 'Database connection failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

/**
 * Check AI services availability
 */
async function checkAIServices(): Promise<HealthCheck> {
  const start = Date.now()
  
  try {
    const openaiKey = process.env.OPENAI_API_KEY
    const anthropicKey = process.env.ANTHROPIC_API_KEY
    
    if (!openaiKey && !anthropicKey) {
      return {
        status: 'warn',
        responseTime: Date.now() - start,
        message: 'No AI API keys configured',
        details: { openai: 'not_configured', anthropic: 'not_configured' }
      }
    }

    // Test OpenAI if configured
    let openaiStatus = 'not_configured'
    if (openaiKey && openaiKey !== 'your_openai_api_key_here') {
      try {
        const response = await fetch('https://api.openai.com/v1/models', {
          headers: { 'Authorization': `Bearer ${openaiKey}` },
          signal: AbortSignal.timeout(5000)
        })
        openaiStatus = response.ok ? 'healthy' : 'error'
      } catch {
        openaiStatus = 'error'
      }
    }

    // Test Anthropic if configured
    let anthropicStatus = 'not_configured'
    if (anthropicKey && anthropicKey !== 'your_anthropic_api_key_here') {
      try {
        const response = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: { 
            'x-api-key': anthropicKey,
            'content-type': 'application/json',
            'anthropic-version': '2023-06-01'
          },
          body: JSON.stringify({
            model: 'claude-3-haiku-20240307',
            max_tokens: 1,
            messages: [{ role: 'user', content: 'test' }]
          }),
          signal: AbortSignal.timeout(5000)
        })
        anthropicStatus = response.status === 200 || response.status === 400 ? 'healthy' : 'error'
      } catch {
        anthropicStatus = 'error'
      }
    }

    const hasHealthyService = openaiStatus === 'healthy' || anthropicStatus === 'healthy'
    const responseTime = Date.now() - start

    return {
      status: hasHealthyService ? 'pass' : 'warn',
      responseTime,
      message: hasHealthyService ? 'AI services available' : 'AI services unavailable',
      details: { openai: openaiStatus, anthropic: anthropicStatus }
    }
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - start,
      message: 'AI services check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

/**
 * Check Redis connectivity (if configured)
 */
async function checkRedis(): Promise<HealthCheck> {
  const start = Date.now()
  
  try {
    const redisUrl = process.env.REDIS_URL
    
    if (!redisUrl) {
      return {
        status: 'warn',
        responseTime: Date.now() - start,
        message: 'Redis not configured',
        details: { status: 'not_configured' }
      }
    }

    // TODO: Implement Redis health check when Redis client is added
    return {
      status: 'warn',
      responseTime: Date.now() - start,
      message: 'Redis health check not implemented',
      details: { status: 'not_implemented' }
    }
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - start,
      message: 'Redis check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

/**
 * Check external API dependencies
 */
async function checkExternalAPIs(): Promise<HealthCheck> {
  const start = Date.now()
  
  try {
    // Check Supabase API
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    if (!supabaseUrl) {
      return {
        status: 'fail',
        responseTime: Date.now() - start,
        message: 'Supabase URL not configured'
      }
    }

    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      headers: { 'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '' },
      signal: AbortSignal.timeout(5000)
    })

    const responseTime = Date.now() - start
    
    return {
      status: response.ok ? 'pass' : 'fail',
      responseTime,
      message: response.ok ? 'External APIs healthy' : 'External API error',
      details: { 
        supabase: response.ok ? 'healthy' : 'error',
        status_code: response.status
      }
    }
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - start,
      message: 'External API check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

/**
 * Check system resources
 */
function checkSystemResources(): { disk_space: HealthCheck; memory: HealthCheck } {
  const memoryUsage = process.memoryUsage()
  const memoryUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024)
  const memoryTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024)
  const memoryUsagePercent = (memoryUsedMB / memoryTotalMB) * 100

  return {
    disk_space: {
      status: 'pass', // TODO: Implement actual disk space check
      message: 'Disk space check not implemented',
      details: { status: 'not_implemented' }
    },
    memory: {
      status: memoryUsagePercent > 90 ? 'warn' : 'pass',
      message: `Memory usage: ${memoryUsagePercent.toFixed(1)}%`,
      details: {
        used_mb: memoryUsedMB,
        total_mb: memoryTotalMB,
        usage_percent: memoryUsagePercent.toFixed(1)
      }
    }
  }
}

/**
 * Main health check handler
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now()
  
  try {
    // Run all health checks in parallel
    const [database, aiServices, redis, externalAPIs] = await Promise.all([
      checkDatabase(),
      checkAIServices(),
      checkRedis(),
      checkExternalAPIs()
    ])

    const systemResources = checkSystemResources()
    
    // Determine overall status
    const checks = {
      database,
      ai_services: aiServices,
      redis,
      external_apis: externalAPIs,
      disk_space: systemResources.disk_space,
      memory: systemResources.memory
    }

    const hasFailures = Object.values(checks).some(check => check.status === 'fail')
    const hasWarnings = Object.values(checks).some(check => check.status === 'warn')
    
    const overallStatus = hasFailures ? 'unhealthy' : hasWarnings ? 'degraded' : 'healthy'
    
    const result: HealthCheckResult = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: Date.now() - startTime,
      checks,
      metadata: {
        environment: process.env.NODE_ENV || 'development',
        node_version: process.version,
        next_version: '15.0.0', // TODO: Get from package.json
        deployment_id: process.env.DEPLOYMENT_ID
      }
    }

    const statusCode = overallStatus === 'healthy' ? 200 : overallStatus === 'degraded' ? 200 : 503
    
    return NextResponse.json(result, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      uptime: Date.now() - startTime
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    })
  }
}
