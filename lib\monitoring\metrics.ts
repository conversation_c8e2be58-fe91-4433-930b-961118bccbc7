/**
 * Application Metrics and Monitoring
 * Collects and reports system metrics for observability
 */

interface MetricData {
  name: string
  value: number
  timestamp: number
  tags?: Record<string, string>
  unit?: string
}

interface SystemMetrics {
  requests: {
    total: number
    success: number
    errors: number
    response_time_avg: number
  }
  database: {
    connections: number
    query_time_avg: number
    slow_queries: number
  }
  ai: {
    requests_total: number
    cost_estimate: number
    errors: number
    response_time_avg: number
  }
  security: {
    failed_logins: number
    rate_limit_hits: number
    mfa_failures: number
  }
  system: {
    memory_usage: number
    cpu_usage: number
    uptime: number
  }
}

class MetricsCollector {
  private metrics: Map<string, MetricData[]> = new Map()
  private readonly maxMetricsPerType = 1000
  private readonly retentionPeriod = 24 * 60 * 60 * 1000 // 24 hours

  /**
   * Record a metric
   */
  record(name: string, value: number, tags?: Record<string, string>, unit?: string): void {
    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      unit
    }

    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const metricArray = this.metrics.get(name)!
    metricArray.push(metric)

    // Keep only recent metrics
    const cutoff = Date.now() - this.retentionPeriod
    const filtered = metricArray.filter(m => m.timestamp > cutoff)
    
    // Limit array size
    if (filtered.length > this.maxMetricsPerType) {
      filtered.splice(0, filtered.length - this.maxMetricsPerType)
    }

    this.metrics.set(name, filtered)
  }

  /**
   * Increment a counter
   */
  increment(name: string, tags?: Record<string, string>): void {
    this.record(name, 1, tags, 'count')
  }

  /**
   * Record timing information
   */
  timing(name: string, duration: number, tags?: Record<string, string>): void {
    this.record(name, duration, tags, 'ms')
  }

  /**
   * Record gauge value
   */
  gauge(name: string, value: number, tags?: Record<string, string>): void {
    this.record(name, value, tags, 'gauge')
  }

  /**
   * Get metrics for a specific name
   */
  getMetrics(name: string): MetricData[] {
    return this.metrics.get(name) || []
  }

  /**
   * Get all metric names
   */
  getMetricNames(): string[] {
    return Array.from(this.metrics.keys())
  }

  /**
   * Get aggregated metrics
   */
  getAggregatedMetrics(name: string, windowMs: number = 60000): {
    count: number
    sum: number
    avg: number
    min: number
    max: number
  } {
    const cutoff = Date.now() - windowMs
    const metrics = this.getMetrics(name).filter(m => m.timestamp > cutoff)

    if (metrics.length === 0) {
      return { count: 0, sum: 0, avg: 0, min: 0, max: 0 }
    }

    const values = metrics.map(m => m.value)
    const sum = values.reduce((a, b) => a + b, 0)

    return {
      count: metrics.length,
      sum,
      avg: sum / metrics.length,
      min: Math.min(...values),
      max: Math.max(...values)
    }
  }

  /**
   * Get system metrics summary
   */
  getSystemMetrics(): SystemMetrics {
    const now = Date.now()
    const oneHour = 60 * 60 * 1000

    return {
      requests: {
        total: this.getAggregatedMetrics('http.requests', oneHour).count,
        success: this.getAggregatedMetrics('http.requests.success', oneHour).count,
        errors: this.getAggregatedMetrics('http.requests.error', oneHour).count,
        response_time_avg: this.getAggregatedMetrics('http.response_time', oneHour).avg
      },
      database: {
        connections: this.getAggregatedMetrics('db.connections', oneHour).avg,
        query_time_avg: this.getAggregatedMetrics('db.query_time', oneHour).avg,
        slow_queries: this.getAggregatedMetrics('db.slow_queries', oneHour).count
      },
      ai: {
        requests_total: this.getAggregatedMetrics('ai.requests', oneHour).count,
        cost_estimate: this.getAggregatedMetrics('ai.cost', oneHour).sum,
        errors: this.getAggregatedMetrics('ai.errors', oneHour).count,
        response_time_avg: this.getAggregatedMetrics('ai.response_time', oneHour).avg
      },
      security: {
        failed_logins: this.getAggregatedMetrics('auth.failed_logins', oneHour).count,
        rate_limit_hits: this.getAggregatedMetrics('rate_limit.hits', oneHour).count,
        mfa_failures: this.getAggregatedMetrics('mfa.failures', oneHour).count
      },
      system: {
        memory_usage: this.getAggregatedMetrics('system.memory', 5 * 60 * 1000).avg,
        cpu_usage: this.getAggregatedMetrics('system.cpu', 5 * 60 * 1000).avg,
        uptime: now - (this.getMetrics('system.start_time')[0]?.timestamp || now)
      }
    }
  }

  /**
   * Clear old metrics
   */
  cleanup(): void {
    const cutoff = Date.now() - this.retentionPeriod

    for (const [name, metrics] of this.metrics.entries()) {
      const filtered = metrics.filter(m => m.timestamp > cutoff)
      this.metrics.set(name, filtered)
    }
  }
}

// Global metrics collector instance
export const metrics = new MetricsCollector()

// Record system start time
metrics.record('system.start_time', Date.now())

// Cleanup old metrics every hour
if (typeof window === 'undefined') {
  setInterval(() => {
    metrics.cleanup()
  }, 60 * 60 * 1000)
}

/**
 * Middleware to track HTTP metrics
 */
export function trackHttpMetrics(req: Request, res: Response, duration: number): void {
  const url = new URL(req.url)
  const method = req.method
  const status = res.status

  const tags = {
    method,
    path: url.pathname,
    status: status.toString()
  }

  metrics.increment('http.requests', tags)
  metrics.timing('http.response_time', duration, tags)

  if (status >= 200 && status < 400) {
    metrics.increment('http.requests.success', tags)
  } else {
    metrics.increment('http.requests.error', tags)
  }
}

/**
 * Track database metrics
 */
export function trackDatabaseMetrics(operation: string, duration: number, success: boolean): void {
  const tags = { operation, success: success.toString() }

  metrics.timing('db.query_time', duration, tags)
  metrics.increment('db.queries', tags)

  if (duration > 1000) {
    metrics.increment('db.slow_queries', tags)
  }
}

/**
 * Track AI service metrics
 */
export function trackAIMetrics(provider: string, model: string, duration: number, tokens: number, cost: number): void {
  const tags = { provider, model }

  metrics.increment('ai.requests', tags)
  metrics.timing('ai.response_time', duration, tags)
  metrics.record('ai.tokens', tokens, tags, 'count')
  metrics.record('ai.cost', cost, tags, 'usd')
}

/**
 * Track security events
 */
export function trackSecurityEvent(event: string, userId?: string, success: boolean = true): void {
  const tags = { event, success: success.toString() }
  if (userId) tags.user_id = userId

  metrics.increment('security.events', tags)

  // Track specific security metrics
  switch (event) {
    case 'login':
      if (!success) metrics.increment('auth.failed_logins', tags)
      break
    case 'mfa':
      if (!success) metrics.increment('mfa.failures', tags)
      break
    case 'rate_limit':
      metrics.increment('rate_limit.hits', tags)
      break
  }
}

/**
 * Record system resource usage
 */
export function recordSystemMetrics(): void {
  const memoryUsage = process.memoryUsage()
  const memoryUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024)

  metrics.gauge('system.memory', memoryUsedMB)
  
  // TODO: Add CPU usage tracking
  // metrics.gauge('system.cpu', cpuUsage)
}
