(()=>{var e={};e.id=4163,e.ids=[4163],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},93994:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(70260),r=t(28203),n=t(25155),i=t.n(n),l=t(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c=["",{children:["test-faq-generation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23465)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-faq-generation\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-faq-generation\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-faq-generation/page",pathname:"/test-faq-generation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92667:(e,s,t)=>{Promise.resolve().then(t.bind(t,23465))},3283:(e,s,t)=>{Promise.resolve().then(t.bind(t,99130))},99130:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(45512);t(58009);var r=t(33936),n=t(97643),i=t(69193),l=t(75339),d=t(77252),c=t(4269),o=t(50236),x=t(46583),m=t(1372),h=t(16873);function u(){return(0,a.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-8 w-8"}),"AI-Powered FAQ Generation System"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Generate intelligent FAQs from knowledge base articles and user search patterns"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-3",children:(0,a.jsxs)(n.ZB,{className:"text-lg flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"KB-Based Generation"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Analyze knowledge base articles to generate relevant FAQs automatically"})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-3",children:(0,a.jsxs)(n.ZB,{className:"text-lg flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"Search Pattern Analysis"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Create FAQs from failed searches and identified content gaps"})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-3",children:(0,a.jsxs)(n.ZB,{className:"text-lg flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Bilingual Support"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Generate FAQs in both Japanese and English for all users"})})]})]}),(0,a.jsxs)(i.tU,{defaultValue:"generate",className:"space-y-4",children:[(0,a.jsxs)(i.j7,{children:[(0,a.jsx)(i.Xi,{value:"generate",children:"Generate FAQs"}),(0,a.jsx)(i.Xi,{value:"features",children:"Features"}),(0,a.jsx)(i.Xi,{value:"integration",children:"Integration"})]}),(0,a.jsx)(i.av,{value:"generate",children:(0,a.jsx)(r.FAQGenerationManager,{})}),(0,a.jsxs)(i.av,{value:"features",className:"space-y-4",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Key Features"}),(0,a.jsx)(n.BT,{children:"Advanced capabilities of the FAQ generation system"})]}),(0,a.jsx)(n.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"AI-Powered Analysis"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Uses OpenAI to analyze content and generate natural, helpful FAQs"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Confidence Scoring"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Each FAQ is scored for quality and relevance, allowing filtering"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Duplicate Detection"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically merges similar FAQs to avoid redundancy"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Usage Analytics"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tracks FAQ usage and automatically suggests improvements"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Category Management"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Organize FAQs by service categories for better discoverability"})]})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Generation Sources"})}),(0,a.jsxs)(n.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Knowledge Base Articles"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)(d.E,{children:"Group Mail Management"}),(0,a.jsx)(d.E,{children:"Mailbox Configuration"}),(0,a.jsx)(d.E,{children:"SharePoint Access"}),(0,a.jsx)(d.E,{children:"PC Administration"}),(0,a.jsx)(d.E,{children:"Password Reset"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Search Patterns"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)(d.E,{variant:"secondary",children:"Failed Searches"}),(0,a.jsx)(d.E,{variant:"secondary",children:"Content Gaps"}),(0,a.jsx)(d.E,{variant:"secondary",children:"Common Queries"}),(0,a.jsx)(d.E,{variant:"secondary",children:"User Feedback"})]})]})]})]})]}),(0,a.jsxs)(i.av,{value:"integration",className:"space-y-4",children:[(0,a.jsxs)(l.Fc,{children:[(0,a.jsx)(l.XL,{children:"Integration with IT Helpdesk"}),(0,a.jsx)(l.TN,{children:"The FAQ generation system is fully integrated with the IT Helpdesk platform, enhancing user support through intelligent content creation."})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"How It Works"})}),(0,a.jsxs)(n.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"1. Content Analysis"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"The system analyzes existing knowledge base articles to identify common questions"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"2. Pattern Recognition"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed searches and content gaps are analyzed to identify missing FAQs"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"3. AI Generation"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"OpenAI generates natural language questions and answers in both languages"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"4. Quality Filtering"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"FAQs are scored and filtered based on confidence and relevance"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"5. Chatbot Integration"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Approved FAQs are automatically available in the AI chatbot for instant help"})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Database Schema"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("pre",{className:"text-xs bg-muted p-4 rounded-lg overflow-x-auto",children:`chatbot_faq
├── id (UUID)
├── question_ja (TEXT)
├── question_en (TEXT)
├── answer_ja (TEXT)
├── answer_en (TEXT)
├── category (VARCHAR)
├── keywords (TEXT[])
├── usage_count (INTEGER)
├── is_active (BOOLEAN)
├── source_type (VARCHAR)
├── source_article_ids (UUID[])
├── confidence_score (DECIMAL)
└── timestamps`})})]})]})]})]})}},33936:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ';', '}' or <eof>\n   ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\faq-generation-manager.tsx\x1b[0m:1:1]\n \x1b[2m1\x1b[0m │ てを保存' : 'Save All'}\n   \xb7 \x1b[35;1m────┬───\x1b[0m\x1b[33;1m─────\x1b[0m\n   \xb7     \x1b[35;1m╰── \x1b[35;1mThis is the expression part of an expression statement\x1b[0m\x1b[0m\n \x1b[2m2\x1b[0m │               </Button>\n \x1b[2m3\x1b[0m │             </div>\n \x1b[2m4\x1b[0m │           </CardHeader>\n   ╰────\n\n\nCaused by:\n    Syntax Error")},23465:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-faq-generation\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-faq-generation\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8096,2076],()=>t(93994));module.exports=a})();