import { serve } from 'https://deno.land/std@0.131.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { OpenAI } from 'https://esm.sh/openai@4'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  conversationId?: string
  message: string
  context?: {
    currentForm?: string
    currentPage?: string
    userRole?: string
    department?: string
    language?: 'ja' | 'en'
  }
}

interface ChatResponse {
  response: string
  conversationId: string
  suggestions?: string[]
  tutorials?: any[]
  faqs?: any[]
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!

    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const openai = new OpenAI({ apiKey: openaiApiKey })

    const { conversationId, message, context = {} } = await req.json() as ChatRequest
    const authHeader = req.headers.get('Authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      throw new Error('No authorization token')
    }

    // Get user from token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Invalid token')
    }

    const language = context.language || 'ja'

    // Get or create conversation
    let conversation
    if (conversationId) {
      const { data, error } = await supabase
        .from('chatbot_conversations')
        .select('*')
        .eq('id', conversationId)
        .eq('user_id', user.id)
        .single()
      
      if (error) throw error
      conversation = data
    } else {
      const { data, error } = await supabase
        .from('chatbot_conversations')
        .insert({
          user_id: user.id,
          title: message.substring(0, 50),
          context,
          language
        })
        .select()
        .single()
      
      if (error) throw error
      conversation = data
    }

    // Store user message
    const { error: messageError } = await supabase
      .from('chatbot_messages')
      .insert({
        conversation_id: conversation.id,
        role: 'user',
        content: message,
        metadata: { context }
      })

    if (messageError) throw messageError

    // Get conversation history
    const { data: messages } = await supabase
      .from('chatbot_messages')
      .select('*')
      .eq('conversation_id', conversation.id)
      .order('created_at', { ascending: true })
      .limit(10)

    // Search FAQs
    const { data: faqs } = await supabase
      .from('chatbot_faq')
      .select('*')
      .eq('is_active', true)
      .textSearch('keywords', message.split(' ').join(' | '))
      .limit(3)

    // Build system prompt
    const systemPrompt = `You are an AI assistant for the ITSync IT Helpdesk system. Your role is to help users navigate the system, fill out forms, and answer questions about IT services.

Context:
- Current form: ${context.currentForm || 'None'}
- Current page: ${context.currentPage || 'None'}
- User role: ${context.userRole || 'Unknown'}
- Department: ${context.department || 'Unknown'}
- Language preference: ${language === 'ja' ? 'Japanese' : 'English'}

Available services:
1. Group Mail Management (グループメール管理)
2. Mailbox Email Addresses (メールボックス管理)
3. SharePoint Library Access (SharePointライブラリアクセス)
4. PC Administrative Status (PC管理者権限)
5. Password Reset (パスワードリセット)
6. Software Installation (ソフトウェアインストール)

${language === 'ja' ? 'ユーザーの質問に日本語で答えてください。' : 'Please respond in English.'}
Be helpful, concise, and guide users step-by-step when needed.`

    // Prepare conversation history for AI
    const conversationHistory = messages?.map(msg => ({
      role: msg.role as 'user' | 'assistant' | 'system',
      content: msg.content
    })) || []

    // Generate AI response
    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [
        { role: 'system', content: systemPrompt },
        ...conversationHistory.slice(-8), // Keep last 8 messages for context
        { role: 'user', content: message }
      ],
      temperature: 0.7,
      max_tokens: 500
    })

    const aiResponse = completion.choices[0].message.content || 'I apologize, but I could not generate a response.'

    // Store AI response
    await supabase
      .from('chatbot_messages')
      .insert({
        conversation_id: conversation.id,
        role: 'assistant',
        content: aiResponse,
        metadata: { faqs: faqs?.map(f => f.id) }
      })

    // Generate suggestions based on context
    const suggestions = []
    if (context.currentForm === 'password-reset') {
      suggestions.push(
        language === 'ja' ? 'M365のパスワードをリセットする' : 'Reset M365 password',
        language === 'ja' ? 'MFAをリセットする' : 'Reset MFA'
      )
    } else if (!context.currentForm) {
      suggestions.push(
        language === 'ja' ? 'ITサービスを申請する' : 'Request IT service',
        language === 'ja' ? '申請状況を確認する' : 'Check request status',
        language === 'ja' ? 'ヘルプが必要です' : 'I need help'
      )
    }

    // Get relevant tutorials
    const { data: tutorials } = await supabase
      .from('chatbot_tutorials')
      .select('*')
      .eq('is_active', true)
      .limit(2)

    const response: ChatResponse = {
      response: aiResponse,
      conversationId: conversation.id,
      suggestions,
      tutorials: tutorials || [],
      faqs: faqs || []
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Chatbot error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
