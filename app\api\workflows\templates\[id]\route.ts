import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { WorkflowTemplateManager } from '@/lib/services/workflow/templates/workflow-template-manager';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';
import { auditService } from '@/lib/services/audit';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const workflowEngine = new WorkflowEngine(supabase);
    const templateManager = new WorkflowTemplateManager(supabase, auditService, workflowEngine);

    const template = await templateManager.getTemplate(params.id);
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    return NextResponse.json({ template });
  } catch (error) {
    console.error('Error fetching workflow template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow template' },
      { status: 500 }
    );
  }
}
