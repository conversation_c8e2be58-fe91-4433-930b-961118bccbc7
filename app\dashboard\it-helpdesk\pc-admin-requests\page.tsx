'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/components/providers/supabase-provider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { format } from 'date-fns'
import { PlusCircle, Shield, Download, Settings, Monitor, AlertCircle } from 'lucide-react'
import { useDepartmentAccess } from '@/lib/auth/hooks'
import { PcAdminRequestDialog } from '@/components/pc-admin/request-dialog'
import { PcAdminRequestList } from '@/components/pc-admin/request-list'
import { Loading } from '@/components/ui/loading'

export interface PcAdminRequest {
  id: string
  submission_id: string
  service_type: string
  action_type: 'grant' | 'revoke' | 'software_install'
  parameters: {
    pc_id: string
    reason: string
    software_name?: string
    urgency?: 'low' | 'medium' | 'high'
  }
  affected_users: string[]
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
  execution_status?: any
  form_submission?: {
    id: string
    approval_status: string
    assigned_to?: string
    processed_by?: string
  }
  affected_staff?: Array<{
    id: string
    name_jp: string
    name_en: string
    email: string
    staff_id: string
    pc_id?: string
  }>
}

export default function PcAdminRequestsPage() {
  const [requests, setRequests] = useState<PcAdminRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { supabase, user } = useSupabase()
  const { departmentId, hasFullAccess } = useDepartmentAccess()

  useEffect(() => {
    fetchRequests()
  }, [departmentId, selectedStatus])

  const fetchRequests = async () => {
    try {
      setLoading(true)

      let query = supabase
        .from('request_items')
        .select(`
          *,
          form_submission:form_submissions!inner(
            id,
            approval_status,
            assigned_to,
            processed_by
          )
        `)
        .eq('service_type', 'PC Administrative Status')
        .order('created_at', { ascending: false })

      // Apply status filter
      if (selectedStatus !== 'all') {
        query = query.eq('status', selectedStatus)
      }

      const { data, error } = await query

      if (error) throw error

      // Fetch affected users details
      const requestsWithUsers = await Promise.all(
        (data || []).map(async (request) => {
          if (request.affected_users && request.affected_users.length > 0) {
            const { data: staffData } = await supabase
              .from('staff')
              .select('id, name_jp, name_en, email, staff_id, pc_id')
              .in('id', request.affected_users)

            return {
              ...request,
              affected_staff: staffData || []
            }
          }
          return {
            ...request,
            affected_staff: []
          }
        })
      )

      setRequests(requestsWithUsers)
    } catch (error) {
      console.error('Error fetching PC admin requests:', error)
      toast.error('Failed to fetch PC admin requests')
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (requestId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('request_items')
        .update({
          status: newStatus,
          completed_at: newStatus === 'completed' ? new Date().toISOString() : null
        })
        .eq('id', requestId)

      if (error) throw error

      toast.success('Request status updated successfully')
      fetchRequests()
    } catch (error) {
      console.error('Error updating request status:', error)
      toast.error('Failed to update request status')
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      pending: { color: 'secondary', icon: AlertCircle },
      processing: { color: 'default', icon: Settings },
      completed: { color: 'success', icon: Shield },
      failed: { color: 'destructive', icon: AlertCircle }
    }

    const variant = variants[status] || variants.pending
    const Icon = variant.icon

    return (
      <Badge variant={variant.color}>
        <Icon className="mr-1 h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getActionTypeBadge = (actionType: string) => {
    const variants: Record<string, any> = {
      grant: { color: 'success', icon: Shield, label: 'Grant Access' },
      revoke: { color: 'destructive', icon: Shield, label: 'Revoke Access' },
      software_install: { color: 'default', icon: Download, label: 'Software Install' }
    }

    const variant = variants[actionType] || variants.grant
    const Icon = variant.icon

    return (
      <Badge variant={variant.color}>
        <Icon className="mr-1 h-3 w-3" />
        {variant.label}
      </Badge>
    )
  }

  const getUrgencyBadge = (urgency?: string) => {
    if (!urgency) return null

    const variants: Record<string, any> = {
      low: 'secondary',
      medium: 'default',
      high: 'destructive'
    }

    return (
      <Badge variant={variants[urgency] || 'secondary'}>
        {urgency.charAt(0).toUpperCase() + urgency.slice(1)} Priority
      </Badge>
    )
  }

  if (loading) {
    return <Loading />
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">PC Administrative Request Management</h1>
        <p className="text-muted-foreground">
          Manage PC administrative access requests and software installation approvals
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Request Overview</CardTitle>
                <CardDescription>
                  Monitor and process PC administrative requests
                </CardDescription>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="status-filter">Status:</Label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger id="status-filter" className="w-[150px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Requests</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={() => setIsDialogOpen(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  New Request
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <PcAdminRequestList
              requests={requests}
              onStatusUpdate={handleStatusUpdate}
              getStatusBadge={getStatusBadge}
              getActionTypeBadge={getActionTypeBadge}
              getUrgencyBadge={getUrgencyBadge}
            />
          </CardContent>
        </Card>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{requests.length}</div>
              <p className="text-xs text-muted-foreground">
                All PC admin requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <AlertCircle className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {requests.filter(r => r.status === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Awaiting processing
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processing</CardTitle>
              <Settings className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {requests.filter(r => r.status === 'processing').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently being handled
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <Shield className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {requests.filter(r => r.status === 'completed').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Successfully processed
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <PcAdminRequestDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSuccess={() => {
          setIsDialogOpen(false)
          fetchRequests()
        }}
      />
    </div>
  )
}
