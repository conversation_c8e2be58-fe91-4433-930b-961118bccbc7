'use client'

import { useState } from 'react'
import { NLPInput } from '@/components/forms/nlp-input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

export default function TestNLPPage() {
  const [selectedForm, setSelectedForm] = useState<string | null>(null)
  const [extractedData, setExtractedData] = useState<Record<string, any>>({})
  const [language, setLanguage] = useState<'ja' | 'en'>('ja')

  const availableForms = [
    'group_mail',
    'mailbox',
    'sharepoint',
    'pc_admin',
    'password_reset',
    'mxp_registration',
    'software_install',
    'web_browsing'
  ]

  const handleFormSuggested = (formType: string, data: Record<string, any>) => {
    setSelectedForm(formType)
    setExtractedData(data)
  }

  const handleReset = () => {
    setSelectedForm(null)
    setExtractedData({})
  }

  const formDisplayNames: Record<string, { ja: string; en: string }> = {
    group_mail: { ja: 'グループメール', en: 'Group Mail' },
    mailbox: { ja: 'メールボックス', en: 'Mailbox' },
    sharepoint: { ja: 'SharePoint', en: 'SharePoint' },
    pc_admin: { ja: 'PC管理者権限', en: 'PC Admin' },
    password_reset: { ja: 'パスワードリセット', en: 'Password Reset' },
    mxp_registration: { ja: 'MXP登録', en: 'MXP Registration' },
    software_install: { ja: 'ソフトウェアインストール', en: 'Software Install' },
    web_browsing: { ja: 'Web閲覧', en: 'Web Browsing' }
  }

  return (
    <div className="container mx-auto py-10 max-w-6xl">
      <Card>
        <CardHeader>
          <CardTitle>Natural Language Processing Test</CardTitle>
          <CardDescription>
            Test the NLP capabilities for parsing IT helpdesk requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={language} onValueChange={(v) => setLanguage(v as 'ja' | 'en')}>
            <TabsList className="grid w-full grid-cols-2 max-w-[200px]">
              <TabsTrigger value="ja">日本語</TabsTrigger>
              <TabsTrigger value="en">English</TabsTrigger>
            </TabsList>

            <TabsContent value={language} className="space-y-6">
              {!selectedForm ? (
                <>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">
                      {language === 'ja' ? '使い方' : 'How to use'}
                    </h3>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      {language === 'ja' ? (
                        <>
                          <li>• 自然な日本語でITサポートリクエストを入力してください</li>
                          <li>• システムが意図を解析し、適切なフォームを提案します</li>
                          <li>• 人名、アクション、対象などが自動的に抽出されます</li>
                          <li>• 例文をクリックして試すこともできます</li>
                        </>
                      ) : (
                        <>
                          <li>• Enter IT support requests in natural language</li>
                          <li>• The system will parse intent and suggest appropriate forms</li>
                          <li>• Names, actions, and targets will be extracted automatically</li>
                          <li>• Click example requests to try them out</li>
                        </>
                      )}
                    </ul>
                  </div>

                  <NLPInput
                    onFormSuggested={handleFormSuggested}
                    availableForms={availableForms}
                    language={language}
                  />

                  <div className="space-y-3">
                    <h3 className="font-semibold">
                      {language === 'ja' ? '利用可能なフォーム' : 'Available Forms'}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {availableForms.map((form) => (
                        <Badge key={form} variant="secondary">
                          {formDisplayNames[form]?.[language] || form}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">
                        {language === 'ja' ? '選択されたフォーム' : 'Selected Form'}
                      </h3>
                      <Badge className="mt-1">
                        {formDisplayNames[selectedForm]?.[language] || selectedForm}
                      </Badge>
                    </div>
                    <Button variant="outline" onClick={handleReset} className="gap-2">
                      <ArrowLeft className="h-4 w-4" />
                      {language === 'ja' ? '戻る' : 'Back'}
                    </Button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">
                        {language === 'ja' ? '抽出されたデータ' : 'Extracted Data'}
                      </h4>
                      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                        <pre className="text-sm overflow-auto">
                          {JSON.stringify(extractedData, null, 2)}
                        </pre>
                      </div>
                    </div>

                    <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                      <p className="text-sm">
                        {language === 'ja' 
                          ? '実際のアプリケーションでは、このデータが対応するフォームフィールドに自動的に入力されます。'
                          : 'In the actual application, this data would be automatically populated into the corresponding form fields.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
