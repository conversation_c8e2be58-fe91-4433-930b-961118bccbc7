'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useLanguage } from '@/lib/LanguageContext'
import { useAuth } from '@/hooks/use-auth'
import { useKBPermissions } from '@/hooks/use-kb-permissions'
import { createClient } from '@/lib/supabase/client'
import { 
  ChevronLeft, 
  Calendar, 
  Eye, 
  ThumbsUp, 
  ThumbsDown,
  BookOpen,
  Share2,
  Printer,
  Lock,
  Loader2,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/lib/hooks/use-toast'
import { ArticlePermissions } from './article-permissions'
import type { Database } from '@/lib/database.types'

type Article = Database['public']['Tables']['kb_articles']['Row']

interface ArticleViewProps {
  slug: string
}

export function ArticleView({ slug }: ArticleViewProps) {
  const { language } = useLanguage()
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const [article, setArticle] = useState<Article | null>(null)
  const [loading, setLoading] = useState(true)
  const [feedback, setFeedback] = useState<boolean | null>(null)
  const [showPermissions, setShowPermissions] = useState(false)
  const supabase = createClient()

  // Use the permissions hook
  const { 
    canView, 
    canEdit, 
    loading: permissionsLoading,
    checkPermission 
  } = useKBPermissions()

  useEffect(() => {
    loadArticle()
  }, [slug])

  const loadArticle = async () => {
    setLoading(true)
    try {
      // First, get the article basic info
      const { data, error } = await supabase
        .from('kb_articles')
        .select(`
          *,
          category:kb_categories(*),
          author:profiles(name, email)
        `)
        .eq('slug', slug)
        .single()

      if (error || !data) {
        console.error('Article not found:', error)
        toast({
          variant: 'destructive',
          title: language === 'en' ? 'Article not found' : '記事が見つかりません'
        })
        router.push('/knowledge-base')
        return
      }

      setArticle(data)

      // Check permissions
      if (user?.id) {
        await checkPermission(data.id)
      }

      // Increment view count if user can view
      if (data.status === 'published') {
        await supabase
          .from('kb_articles')
          .update({ view_count: (data.view_count || 0) + 1 })
          .eq('id', data.id)
      }
    } catch (error) {
      console.error('Error loading article:', error)
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error loading article' : '記事の読み込みエラー'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFeedback = async (isHelpful: boolean) => {
    if (!article || !user?.id) return

    setFeedback(isHelpful)
    
    try {
      const { error } = await supabase
        .from('kb_feedback')
        .insert({
          article_id: article.id,
          user_id: user.id,
          is_helpful: isHelpful,
          feedback_type: isHelpful ? 'positive' : 'negative'
        })

      if (error) throw error

      toast({
        title: language === 'en' ? 'Thank you for your feedback!' : 'フィードバックありがとうございます！'
      })
    } catch (error) {
      console.error('Error submitting feedback:', error)
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Failed to submit feedback' : 'フィードバックの送信に失敗しました'
      })
    }
  }

  const handlePrint = () => {
    window.print()
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: language === 'en' ? article?.title_en : article?.title_jp,
          url: window.location.href
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: language === 'en' ? 'Link copied to clipboard' : 'リンクをクリップボードにコピーしました'
      })
    }
  }

  if (loading || permissionsLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!article) {
    return null
  }

  // Check if user has permission to view
  if (!canView && article.status === 'published') {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Lock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-2xl font-bold mb-2">
                {language === 'en' ? 'Access Restricted' : 'アクセス制限'}
              </h2>
              <p className="text-muted-foreground mb-6">
                {language === 'en' 
                  ? 'You do not have permission to view this article.'
                  : 'この記事を表示する権限がありません。'}
              </p>
              <Button onClick={() => router.push('/knowledge-base')}>
                {language === 'en' ? 'Back to Knowledge Base' : 'ナレッジベースに戻る'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show draft/archived notice if not published
  if (article.status !== 'published' && !canEdit) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>
            {language === 'en' ? 'Article Not Available' : '記事は利用できません'}
          </AlertTitle>
          <AlertDescription>
            {language === 'en' 
              ? 'This article is not currently published.'
              : 'この記事は現在公開されていません。'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Back button and admin controls */}
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/knowledge-base')}
          className="mb-4"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Back to Knowledge Base' : 'ナレッジベースに戻る'}
        </Button>

        {canEdit && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPermissions(!showPermissions)}
            >
              <Lock className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Permissions' : '権限'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/knowledge-base/edit/${article.id}`)}
            >
              {language === 'en' ? 'Edit' : '編集'}
            </Button>
          </div>
        )}
      </div>

      {/* Permissions editor for admins */}
      {canEdit && showPermissions && (
        <div className="mb-6">
          <ArticlePermissions
            articleId={article.id}
            currentVisibility={article.visibility as any}
            onVisibilityChange={(visibility) => {
              setArticle({ ...article, visibility })
            }}
          />
        </div>
      )}

      {/* Article content */}
      <Card>
        <CardHeader className="space-y-4">
          <div className="flex items-center gap-2 flex-wrap">
            {article.category && (
              <Badge variant="secondary">
                {language === 'en' ? article.category.name_en : article.category.name_jp}
              </Badge>
            )}
            {article.status !== 'published' && (
              <Badge variant="outline">
                {article.status}
              </Badge>
            )}
            {article.visibility !== 'public' && (
              <Badge variant="outline">
                <Lock className="h-3 w-3 mr-1" />
                {article.visibility}
              </Badge>
            )}
          </div>

          <h1 className="text-3xl font-bold">
            {language === 'en' ? article.title_en : article.title_jp}
          </h1>

          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {new Date(article.published_at || article.created_at).toLocaleDateString(
                language === 'en' ? 'en-US' : 'ja-JP'
              )}
            </div>
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              {article.view_count || 0} {language === 'en' ? 'views' : '閲覧'}
            </div>
            {article.author && (
              <div className="flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                {article.author.name}
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="prose prose-sm max-w-none dark:prose-invert">
          <div dangerouslySetInnerHTML={{ 
            __html: language === 'en' ? article.content_en : article.content_jp 
          }} />
        </CardContent>

        <Separator />

        {/* Article actions */}
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {language === 'en' ? 'Was this article helpful?' : 'この記事は役に立ちましたか？'}
              </span>
              <Button
                variant={feedback === true ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleFeedback(true)}
              >
                <ThumbsUp className="h-4 w-4 mr-1" />
                {language === 'en' ? 'Yes' : 'はい'}
              </Button>
              <Button
                variant={feedback === false ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleFeedback(false)}
              >
                <ThumbsDown className="h-4 w-4 mr-1" />
                {language === 'en' ? 'No' : 'いいえ'}
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
              >
                <Share2 className="h-4 w-4 mr-1" />
                {language === 'en' ? 'Share' : '共有'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrint}
              >
                <Printer className="h-4 w-4 mr-1" />
                {language === 'en' ? 'Print' : '印刷'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
