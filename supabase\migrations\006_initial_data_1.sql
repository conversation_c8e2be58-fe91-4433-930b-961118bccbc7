-- Insert divisions
INSERT INTO divisions (name_jp, name_en, code) VALUES
('経営企画部', 'Corporate Planning Division', 'CPD'),
('人事部', 'Human Resources Division', 'HRD'),
('財務、經理部', 'Finance and Accounting Division', 'FAD'),
('法格・コンプライアンス部', 'Legal, Ethics and Compliance Division', 'LED'),
('フリートオペレーション部', 'Fleet Operations Division', 'FOD'),
('工務部', 'Technical Management Division', 'TMD'),
('ITシステム部', 'IT Systems Division', 'ISD'),
('営業戦略部', 'Commercial Strategies Division', 'CSD'),
('ゲストエクスピリエンス部', 'Guest Experience Division', 'GED');

-- Insert unions
INSERT INTO unions (name_jp, name_en) VALUES
('海上組合', 'Maritime Union'),
('地上組合', 'Land Union');

-- Insert roles
INSERT INTO roles (name, description, permissions) VALUES
('Global Administrator', 'Sole authority! Can register, Update, Delete, ban all users, can add new features, and have access to all features, and functions. All settings etc', '{"admin": true, "all_access": true}'),
('Web App System Administrator', 'Can register, update users, assign roles. Monitor the whole system functionality', '{"user_management": true, "system_monitoring": true}'),
('Department Administrator', 'Can register staff members within the department and groups, assign roles to staffs within department and group. Monitors all request from the department and groups within the department ONLY', '{"department_admin": true}'),
('HR Staff', 'Manages the AI Powered, DATABASE DRIVEN Onboarding and Offboarding processes system', '{"hr_admin": true}'),
('IT Helpdesk Support', 'Receives all requests, and inquiries for services / support from the web app', '{"helpdesk_admin": true}'),
('Regular User', 'Non tech savvy. Access to AI powered knowledge base', '{"regular_user": true}');
