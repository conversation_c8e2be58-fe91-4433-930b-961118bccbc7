/**
 * OpenAPI Documentation Service
 * 
 * This module provides tools for generating OpenAPI 3.0 documentation
 * with custom route decorators for automatic schema generation.
 */

import { NextApiRequest, NextApiResponse } from 'next';

/**
 * OpenAPI specification version
 */
export const OPENAPI_VERSION = '3.0.3';

/**
 * OpenAPI document structure
 */
export interface OpenAPIDocument {
  openapi: string;
  info: {
    title: string;
    description?: string;
    version: string;
    termsOfService?: string;
    contact?: {
      name?: string;
      url?: string;
      email?: string;
    };
    license?: {
      name: string;
      url?: string;
    };
  };
  servers: Array<{
    url: string;
    description?: string;
    variables?: Record<string, {
      enum?: string[];
      default: string;
      description?: string;
    }>;
  }>;
  paths: Record<string, PathItemObject>;
  components?: {
    schemas?: Record<string, SchemaObject>;
    responses?: Record<string, ResponseObject>;
    parameters?: Record<string, ParameterObject>;
    examples?: Record<string, ExampleObject>;
    requestBodies?: Record<string, RequestBodyObject>;
    headers?: Record<string, HeaderObject>;
    securitySchemes?: Record<string, SecuritySchemeObject>;
    links?: Record<string, LinkObject>;
    callbacks?: Record<string, CallbackObject>;
  };
  tags?: Array<{
    name: string;
    description?: string;
    externalDocs?: {
      description?: string;
      url: string;
    };
  }>;
  externalDocs?: {
    description?: string;
    url: string;
  };
  security?: Array<Record<string, string[]>>;
}

/**
 * Path item object
 */
export interface PathItemObject {
  summary?: string;
  description?: string;
  get?: OperationObject;
  put?: OperationObject;
  post?: OperationObject;
  delete?: OperationObject;
  options?: OperationObject;
  head?: OperationObject;
  patch?: OperationObject;
  trace?: OperationObject;
  servers?: Array<{
    url: string;
    description?: string;
    variables?: Record<string, {
      enum?: string[];
      default: string;
      description?: string;
    }>;
  }>;
  parameters?: Array<ParameterObject>;
}

/**
 * Operation object
 */
export interface OperationObject {
  tags?: string[];
  summary?: string;
  description?: string;
  externalDocs?: {
    description?: string;
    url: string;
  };
  operationId?: string;
  parameters?: Array<ParameterObject>;
  requestBody?: RequestBodyObject;
  responses: Record<string, ResponseObject>;
  callbacks?: Record<string, CallbackObject>;
  deprecated?: boolean;
  security?: Array<Record<string, string[]>>;
  servers?: Array<{
    url: string;
    description?: string;
    variables?: Record<string, {
      enum?: string[];
      default: string;
      description?: string;
    }>;
  }>;
}

/**
 * Parameter object
 */
export interface ParameterObject {
  name: string;
  in: 'query' | 'header' | 'path' | 'cookie';
  description?: string;
  required?: boolean;
  deprecated?: boolean;
  allowEmptyValue?: boolean;
  style?: string;
  explode?: boolean;
  allowReserved?: boolean;
  schema?: SchemaObject;
  example?: any;
  examples?: Record<string, ExampleObject>;
  content?: Record<string, MediaTypeObject>;
}

/**
 * Request body object
 */
export interface RequestBodyObject {
  description?: string;
  content: Record<string, MediaTypeObject>;
  required?: boolean;
}

/**
 * Media type object
 */
export interface MediaTypeObject {
  schema?: SchemaObject;
  example?: any;
  examples?: Record<string, ExampleObject>;
  encoding?: Record<string, EncodingObject>;
}

/**
 * Encoding object
 */
export interface EncodingObject {
  contentType?: string;
  headers?: Record<string, HeaderObject>;
  style?: string;
  explode?: boolean;
  allowReserved?: boolean;
}

/**
 * Response object
 */
export interface ResponseObject {
  description: string;
  headers?: Record<string, HeaderObject>;
  content?: Record<string, MediaTypeObject>;
  links?: Record<string, LinkObject>;
}

/**
 * Callback object
 */
export interface CallbackObject {
  [expression: string]: PathItemObject;
}

/**
 * Example object
 */
export interface ExampleObject {
  summary?: string;
  description?: string;
  value?: any;
  externalValue?: string;
}

/**
 * Link object
 */
export interface LinkObject {
  operationRef?: string;
  operationId?: string;
  parameters?: Record<string, any>;
  requestBody?: any;
  description?: string;
  server?: {
    url: string;
    description?: string;
    variables?: Record<string, {
      enum?: string[];
      default: string;
      description?: string;
    }>;
  };
}

/**
 * Header object
 */
export interface HeaderObject {
  description?: string;
  required?: boolean;
  deprecated?: boolean;
  allowEmptyValue?: boolean;
  style?: string;
  explode?: boolean;
  allowReserved?: boolean;
  schema?: SchemaObject;
  example?: any;
  examples?: Record<string, ExampleObject>;
  content?: Record<string, MediaTypeObject>;
}

/**
 * Schema object
 */
export interface SchemaObject {
  title?: string;
  multipleOf?: number;
  maximum?: number;
  exclusiveMaximum?: boolean;
  minimum?: number;
  exclusiveMinimum?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  maxItems?: number;
  minItems?: number;
  uniqueItems?: boolean;
  maxProperties?: number;
  minProperties?: number;
  required?: string[];
  enum?: any[];
  type?: string | string[];
  allOf?: SchemaObject[];
  oneOf?: SchemaObject[];
  anyOf?: SchemaObject[];
  not?: SchemaObject;
  items?: SchemaObject;
  properties?: Record<string, SchemaObject>;
  additionalProperties?: boolean | SchemaObject;
  description?: string;
  format?: string;
  default?: any;
  nullable?: boolean;
  discriminator?: {
    propertyName: string;
    mapping?: Record<string, string>;
  };
  readOnly?: boolean;
  writeOnly?: boolean;
  xml?: {
    name?: string;
    namespace?: string;
    prefix?: string;
    attribute?: boolean;
    wrapped?: boolean;
  };
  externalDocs?: {
    description?: string;
    url: string;
  };
  example?: any;
  deprecated?: boolean;
}

/**
 * Security scheme object
 */
export interface SecuritySchemeObject {
  type: 'apiKey' | 'http' | 'oauth2' | 'openIdConnect';
  description?: string;
  name?: string;
  in?: 'query' | 'header' | 'cookie';
  scheme?: string;
  bearerFormat?: string;
  flows?: {
    implicit?: {
      authorizationUrl: string;
      refreshUrl?: string;
      scopes: Record<string, string>;
    };
    password?: {
      tokenUrl: string;
      refreshUrl?: string;
      scopes: Record<string, string>;
    };
    clientCredentials?: {
      tokenUrl: string;
      refreshUrl?: string;
      scopes: Record<string, string>;
    };
    authorizationCode?: {
      authorizationUrl: string;
      tokenUrl: string;
      refreshUrl?: string;
      scopes: Record<string, string>;
    };
  };
  openIdConnectUrl?: string;
}

/**
 * API route metadata
 */
export interface RouteMetadata {
  path: string;
  method: 'get' | 'post' | 'put' | 'delete' | 'patch' | 'options' | 'head';
  summary?: string;
  description?: string;
  tags?: string[];
  operationId?: string;
  parameters?: ParameterObject[];
  requestBody?: RequestBodyObject;
  responses: Record<string, ResponseObject>;
  security?: Array<Record<string, string[]>>;
  deprecated?: boolean;
}

/**
 * API documentation registry
 */
class APIDocumentationRegistry {
  private static instance: APIDocumentationRegistry;
  private routes: RouteMetadata[] = [];
  private schemas: Record<string, SchemaObject> = {};
  private securitySchemes: Record<string, SecuritySchemeObject> = {};
  private tags: Array<{ name: string; description?: string }> = [];

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): APIDocumentationRegistry {
    if (!APIDocumentationRegistry.instance) {
      APIDocumentationRegistry.instance = new APIDocumentationRegistry();
    }
    return APIDocumentationRegistry.instance;
  }

  /**
   * Register a route
   */
  public registerRoute(metadata: RouteMetadata): void {
    // Check if route already exists
    const existingRouteIndex = this.routes.findIndex(
      route => route.path === metadata.path && route.method === metadata.method
    );

    if (existingRouteIndex >= 0) {
      // Update existing route
      this.routes[existingRouteIndex] = {
        ...this.routes[existingRouteIndex],
        ...metadata
      };
    } else {
      // Add new route
      this.routes.push(metadata);
    }
  }

  /**
   * Register a schema
   */
  public registerSchema(name: string, schema: SchemaObject): void {
    this.schemas[name] = schema;
  }

  /**
   * Register a security scheme
   */
  public registerSecurityScheme(name: string, scheme: SecuritySchemeObject): void {
    this.securitySchemes[name] = scheme;
  }

  /**
   * Register a tag
   */
  public registerTag(name: string, description?: string): void {
    // Check if tag already exists
    const existingTagIndex = this.tags.findIndex(tag => tag.name === name);

    if (existingTagIndex >= 0) {
      // Update existing tag
      this.tags[existingTagIndex] = {
        name,
        description: description || this.tags[existingTagIndex].description
      };
    } else {
      // Add new tag
      this.tags.push({ name, description });
    }
  }

  /**
   * Get all routes
   */
  public getRoutes(): RouteMetadata[] {
    return [...this.routes];
  }

  /**
   * Get all schemas
   */
  public getSchemas(): Record<string, SchemaObject> {
    return { ...this.schemas };
  }

  /**
   * Get all security schemes
   */
  public getSecuritySchemes(): Record<string, SecuritySchemeObject> {
    return { ...this.securitySchemes };
  }

  /**
   * Get all tags
   */
  public getTags(): Array<{ name: string; description?: string }> {
    return [...this.tags];
  }

  /**
   * Clear all routes
   */
  public clearRoutes(): void {
    this.routes = [];
  }

  /**
   * Clear all schemas
   */
  public clearSchemas(): void {
    this.schemas = {};
  }

  /**
   * Clear all security schemes
   */
  public clearSecuritySchemes(): void {
    this.securitySchemes = {};
  }

  /**
   * Clear all tags
   */
  public clearTags(): void {
    this.tags = [];
  }

  /**
   * Clear all data
   */
  public clear(): void {
    this.clearRoutes();
    this.clearSchemas();
    this.clearSecuritySchemes();
    this.clearTags();
  }
}

/**
 * OpenAPI document generator
 */
export class OpenAPIDocumentGenerator {
  private registry: APIDocumentationRegistry;
  private config: {
    title: string;
    description?: string;
    version: string;
    servers: Array<{
      url: string;
      description?: string;
    }>;
    basePath?: string;
    termsOfService?: string;
    contact?: {
      name?: string;
      url?: string;
      email?: string;
    };
    license?: {
      name: string;
      url?: string;
    };
    externalDocs?: {
      description?: string;
      url: string;
    };
    security?: Array<Record<string, string[]>>;
  };

  constructor(config: {
    title: string;
    description?: string;
    version: string;
    servers: Array<{
      url: string;
      description?: string;
    }>;
    basePath?: string;
    termsOfService?: string;
    contact?: {
      name?: string;
      url?: string;
      email?: string;
    };
    license?: {
      name: string;
      url?: string;
    };
    externalDocs?: {
      description?: string;
      url: string;
    };
    security?: Array<Record<string, string[]>>;
  }) {
    this.registry = APIDocumentationRegistry.getInstance();
    this.config = config;
  }

  /**
   * Generate OpenAPI document
   */
  public generate(): OpenAPIDocument {
    const routes = this.registry.getRoutes();
    const schemas = this.registry.getSchemas();
    const securitySchemes = this.registry.getSecuritySchemes();
    const tags = this.registry.getTags();

    // Build paths object
    const paths: Record<string, PathItemObject> = {};
    routes.forEach(route => {
      const { path, method, ...operation } = route;

      // Normalize path
      const normalizedPath = this.normalizePath(path);

      // Initialize path item if it doesn't exist
      if (!paths[normalizedPath]) {
        paths[normalizedPath] = {};
      }

      // Add operation to path item
      paths[normalizedPath][method] = {
        ...operation,
        responses: operation.responses || {
          '200': {
            description: 'Successful operation'
          }
        }
      };
    });

    // Build OpenAPI document
    const document: OpenAPIDocument = {
      openapi: OPENAPI_VERSION,
      info: {
        title: this.config.title,
        description: this.config.description,
        version: this.config.version,
        termsOfService: this.config.termsOfService,
        contact: this.config.contact,
        license: this.config.license
      },
      servers: this.config.servers,
      paths,
      components: {
        schemas,
        securitySchemes
      },
      tags,
      externalDocs: this.config.externalDocs,
      security: this.config.security
    };

    return document;
  }

  /**
   * Normalize path
   */
  private normalizePath(path: string): string {
    // Remove trailing slash
    let normalizedPath = path.endsWith('/') ? path.slice(0, -1) : path;

    // Add leading slash
    normalizedPath = normalizedPath.startsWith('/') ? normalizedPath : `/${normalizedPath}`;

    // Replace Next.js dynamic route syntax with OpenAPI path parameter syntax
    normalizedPath = normalizedPath.replace(/\[([^\]]+)\]/g, '{$1}');

    // Add base path if provided
    if (this.config.basePath) {
      const basePathWithoutTrailingSlash = this.config.basePath.endsWith('/')
        ? this.config.basePath.slice(0, -1)
        : this.config.basePath;
      normalizedPath = `${basePathWithoutTrailingSlash}${normalizedPath}`;
    }

    return normalizedPath;
  }
}

/**
 * API route decorator
 */
export function ApiRoute(metadata: Omit<RouteMetadata, 'path' | 'method'>) {
  return function(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const registry = APIDocumentationRegistry.getInstance();

    // Extract path and method from target
    const path = target.constructor.path || '/';
    const method = propertyKey.toLowerCase() as 'get' | 'post' | 'put' | 'delete' | 'patch' | 'options' | 'head';

    // Register route
    registry.registerRoute({
      path,
      method,
      ...metadata
    });

    // Return original method
    return descriptor;
  };
}

/**
 * API controller decorator
 */
export function ApiController(path: string) {
  return function(target: any) {
    // Set path on controller
    target.path = path;
    return target;
  };
}

/**
 * API tag decorator
 */
export function ApiTag(name: string, description?: string) {
  return function(target: any) {
    const registry = APIDocumentationRegistry.getInstance();
    registry.registerTag(name, description);
    return target;
  };
}

/**
 * API schema decorator
 */
export function ApiSchema(name: string, schema: SchemaObject) {
  return function(target: any) {
    const registry = APIDocumentationRegistry.getInstance();
    registry.registerSchema(name, schema);
    return target;
  };
}

/**
 * API security scheme decorator
 */
export function ApiSecurityScheme(name: string, scheme: SecuritySchemeObject) {
  return function(target: any) {
    const registry = APIDocumentationRegistry.getInstance();
    registry.registerSecurityScheme(name, scheme);
    return target;
  };
}

/**
 * OpenAPI documentation middleware
 */
export function openApiMiddleware(config: {
  title: string;
  description?: string;
  version: string;
  servers: Array<{
    url: string;
    description?: string;
  }>;
  basePath?: string;
  termsOfService?: string;
  contact?: {
    name?: string;
    url?: string;
    email?: string;
  };
  license?: {
    name: string;
    url?: string;
  };
  externalDocs?: {
    description?: string;
    url: string;
  };
  security?: Array<Record<string, string[]>>;
}) {
  return function(req: NextApiRequest, res: NextApiResponse) {
    const generator = new OpenAPIDocumentGenerator(config);
    const document = generator.generate();
    res.status(200).json(document);
  };
}

/**
 * Example usage:
 * 
 * ```typescript
 * // Define API schemas
 * @ApiSchema('User', {
 *   type: 'object',
 *   properties: {
 *     id: { type: 'string', format: 'uuid' },
 *     name: { type: 'string' },
 *     email: { type: 'string', format: 'email' }
 *   },
 *   required: ['id', 'name', 'email']
 * })
 * 
 * // Define API security schemes
 * @ApiSecurityScheme('bearerAuth', {
 *   type: 'http',
 *   scheme: 'bearer',
 *   bearerFormat: 'JWT'
 * })
 * 
 * // Define API tags
 * @ApiTag('Users', 'User management endpoints')
 * 
 * // Define API controller
 * @ApiController('/api/users')
 * class UsersController {
 *   // Define API routes
 *   @ApiRoute({
 *     summary: 'Get all users',
 *     description: 'Returns a list of all users',
 *     tags: ['Users'],
 *     operationId: 'getUsers',
 *     responses: {
 *       '200': {
 *         description: 'List of users',
 *         content: {
 *           'application/json': {
 *             schema: {
 *               type: 'array',
 *               items: {
 *                 $ref: '#/components/schemas/User'
 *               }
 *             }
 *           }
 *         }
 *       }
 *     },
 *     security: [{ bearerAuth: [] }]
 *   })
 *   async get(req: NextApiRequest, res: NextApiResponse) {
 *     // Implementation
 *   }
 * 
 *   @ApiRoute({
 *     summary: 'Create a new user',
 *     description: 'Creates a new user',
 *     tags: ['Users'],
 *     operationId: 'createUser',
 *     requestBody: {
 *       description: 'User data',
 *       required: true,
 *       content: {
 *         'application/json': {
 *           schema: {
 *             type: 'object',
 *             properties: {
 *               name: { type: 'string' },
 *               email: { type: 'string', format: 'email' }
 *             },
 *             required: ['name', 'email']
 *           }
 *         }
 *       }
 *     },
 *     responses: {
 *       '201': {
 *         description: 'User created',
 *         content: {
 *           'application/json': {
 *             schema: {
 *               $ref: '#/components/schemas/User'
 *             }
 *           }
 *         }
 *       }
 *     },
 *     security: [{ bearerAuth: [] }]
 *   })
 *   async post(req: NextApiRequest, res: NextApiResponse) {
 *     // Implementation
 *   }
 * }
 * 
 * // Create OpenAPI documentation endpoint
 * export default function handler(req: NextApiRequest, res: NextApiResponse) {
 *   return openApiMiddleware({
 *     title: 'My API',
 *     description: 'API documentation',
 *     version: '1.0.0',
 *     servers: [
 *       {
 *         url: 'https://api.example.com',
 *         description: 'Production server'
 *       },
 *       {
 *         url: 'https://staging.example.com',
 *         description: 'Staging server'
 *       }
 *     ]
 *   })(req, res);
 * }
 * ```
 */