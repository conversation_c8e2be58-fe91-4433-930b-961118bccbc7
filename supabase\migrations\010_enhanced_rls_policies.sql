-- Enhanced Row Level Security Policies for RBAC

-- Enable RLS on staff table if not already enabled
ALTER TABLE staff ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "global_admin_staff_policy" ON staff;
DROP POLICY IF EXISTS "system_admin_staff_policy" ON staff;
DROP POLICY IF EXISTS "dept_admin_staff_policy" ON staff;
DROP POLICY IF EXISTS "hr_staff_policy" ON staff;
DROP POLICY IF EXISTS "it_support_staff_policy" ON staff;
DROP POLICY IF EXISTS "regular_user_staff_policy" ON staff;

-- Policy for Global Administrators - Full access to all staff
CREATE POLICY "global_admin_staff_policy" ON staff
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'Global Administrator'
    )
  );

-- Policy for System Administrators - Full access to all staff
CREATE POLICY "system_admin_staff_policy" ON staff
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'Web App System Administrator'
    )
  );

-- Policy for Department Administrators - Access to their department only
CREATE POLICY "dept_admin_staff_policy" ON staff
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'Department Administrator'
      AND s.division_id = staff.division_id
    )
  );

-- Policy for HR Staff - Access to all staff
CREATE POLICY "hr_staff_policy" ON staff
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'HR Staff'
    )
  );

-- Policy for IT Support - Read access to all staff
CREATE POLICY "it_support_staff_policy" ON staff
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'IT Helpdesk Support'
    )
  );

-- Policy for Regular Users - Access to their own record and department colleagues
CREATE POLICY "regular_user_staff_policy" ON staff
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() = auth_id
    OR EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'Regular User'
      AND s.division_id = staff.division_id
    )
  );

-- RLS policies for request_forms table
ALTER TABLE request_forms ENABLE ROW LEVEL SECURITY;

-- Global/System admins can see all requests
CREATE POLICY "admin_request_forms_policy" ON request_forms
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Department admins can see requests from their department
CREATE POLICY "dept_admin_request_forms_policy" ON request_forms
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      JOIN staff requester ON request_forms.requester_id = requester.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'Department Administrator'
      AND s.division_id = requester.division_id
    )
  );

-- IT Support can see all requests
CREATE POLICY "it_support_request_forms_policy" ON request_forms
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'IT Helpdesk Support'
    )
  );

-- Regular users can see their own requests
CREATE POLICY "regular_user_request_forms_policy" ON request_forms
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      WHERE s.auth_id = auth.uid()
      AND s.id = request_forms.requester_id
    )
  );
