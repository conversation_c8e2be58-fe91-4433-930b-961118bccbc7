"use strict";(()=>{var e={};e.id=3289,e.ids=[3289],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},28581:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>u});var o=t(42706),n=t(28203),a=t(45994),i=t(39187);async function u(e,{params:r}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o,error:n}=await e.from("workflow_instances").select(`
        *,
        workflow_definition:workflow_definitions(*),
        request:request_forms(*),
        tasks:workflow_tasks(*),
        sla_tracking:sla_tracking(*)
      `).eq("id",r.id).single();if(n||!o)return i.NextResponse.json({error:"Workflow instance not found"},{status:404});return i.NextResponse.json({data:o})}catch(e){return console.error("Error fetching workflow instance:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/instances/[id]/route",pathname:"/api/workflows/instances/[id]",filename:"route",bundlePath:"app/api/workflows/instances/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:c,serverHooks:l}=d;function w(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:c})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(28581));module.exports=s})();