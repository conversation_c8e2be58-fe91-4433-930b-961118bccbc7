import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface GenerateFAQRequest {
  articleIds?: string[];
  categories?: string[];
  fromSearchPatterns?: boolean;
  timeRange?: {
    start: string;
    end: string;
  };
  options?: {
    minConfidence?: number;
    maxFAQsPerArticle?: number;
    language?: 'ja' | 'en' | 'both';
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { data: { user } } = await supabaseClient.auth.getUser()
    if (!user) {
      throw new Error('Not authenticated')
    }

    const requestData: GenerateFAQRequest = await req.json()
    const {
      articleIds,
      categories,
      fromSearchPatterns,
      timeRange,
      options = {}
    } = requestData

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    let generatedFAQs = []

    if (fromSearchPatterns && timeRange) {
      // Generate FAQs from search patterns
      generatedFAQs = await generateFAQsFromSearchPatterns(
        supabaseClient,
        openAIApiKey,
        timeRange
      )
    } else {
      // Generate FAQs from articles
      generatedFAQs = await generateFAQsFromArticles(
        supabaseClient,
        openAIApiKey,
        { articleIds, categories, ...options }
      )
    }

    // Save FAQs to database
    if (generatedFAQs.length > 0) {
      await saveFAQs(supabaseClient, generatedFAQs)
    }

    return new Response(
      JSON.stringify({
        success: true,
        faqs: generatedFAQs,
        count: generatedFAQs.length
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function generateFAQsFromArticles(
  supabaseClient: any,
  apiKey: string,
  options: any
) {
  const { articleIds, categories, minConfidence = 0.7, maxFAQsPerArticle = 3, language = 'both' } = options

  // Fetch articles
  let query = supabaseClient
    .from('kb_articles')
    .select('*')
    .eq('is_published', true)

  if (articleIds && articleIds.length > 0) {
    query = query.in('id', articleIds)
  } else if (categories && categories.length > 0) {
    query = query.in('category', categories)
  }

  const { data: articles, error } = await query
  if (error) throw error

  const allFAQs = []

  // Process each article
  for (const article of articles || []) {
    const faqs = await generateFAQsForArticle(apiKey, article, maxFAQsPerArticle, language)
    const filteredFAQs = faqs.filter((faq: any) => faq.confidence_score >= minConfidence)
    allFAQs.push(...filteredFAQs)
  }

  return deduplicateFAQs(allFAQs)
}

async function generateFAQsForArticle(
  apiKey: string,
  article: any,
  maxFAQs: number,
  language: string
) {
  const prompt = `Analyze this article and generate ${maxFAQs} FAQs.

Title (EN): ${article.title_en}
Title (JP): ${article.title_jp}
Category: ${article.category}
Content (EN): ${article.content_en}
Content (JP): ${article.content_jp}

Generate FAQs that address common user questions. ${language === 'both' ? 'Provide in both Japanese and English.' : language === 'ja' ? 'Provide in Japanese.' : 'Provide in English.'}

Return JSON array:
[{
  "question_ja": "...",
  "question_en": "...",
  "answer_ja": "...",
  "answer_en": "...",
  "keywords": ["..."],
  "confidence_score": 0.85
}]`

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
    }),
  })

  const data = await response.json()
  const faqs = JSON.parse(data.choices[0].message.content)

  return faqs.map((faq: any) => ({
    ...faq,
    category: article.category,
    source_article_ids: [article.id],
  }))
}

async function generateFAQsFromSearchPatterns(
  supabaseClient: any,
  apiKey: string,
  timeRange: { start: string; end: string }
) {
  // Fetch content gaps
  const { data: gaps, error: gapsError } = await supabaseClient
    .from('kb_content_gaps')
    .select('*')
    .gte('created_at', timeRange.start)
    .lte('created_at', timeRange.end)
    .order('occurrence_count', { ascending: false })
    .limit(10)

  if (gapsError) throw gapsError

  const faqs = []

  for (const gap of gaps || []) {
    const faq = await generateFAQFromGap(apiKey, gap)
    if (faq) faqs.push(faq)
  }

  return faqs
}

async function generateFAQFromGap(apiKey: string, gap: any) {
  const prompt = `Generate an FAQ for this common user query that didn't find results:

Query: ${gap.gap_description}
Category: ${gap.suggested_category}
Occurrences: ${gap.occurrence_count}

Create a helpful FAQ in both Japanese and English. Return JSON:
{
  "question_ja": "...",
  "question_en": "...",
  "answer_ja": "...",
  "answer_en": "...",
  "category": "...",
  "keywords": ["..."],
  "confidence_score": 0.8
}`

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
    }),
  })

  const data = await response.json()
  return JSON.parse(data.choices[0].message.content)
}

function deduplicateFAQs(faqs: any[]) {
  const unique = new Map()
  
  for (const faq of faqs) {
    const key = `${faq.question_en}|${faq.question_ja}`
    if (!unique.has(key) || faq.confidence_score > unique.get(key).confidence_score) {
      unique.set(key, faq)
    }
  }

  return Array.from(unique.values())
}

async function saveFAQs(supabaseClient: any, faqs: any[]) {
  // Check existing FAQs
  const { data: existing } = await supabaseClient
    .from('chatbot_faq')
    .select('question_en, question_ja')

  const existingKeys = new Set(
    (existing || []).map((f: any) => `${f.question_en}|${f.question_ja}`)
  )

  // Filter new FAQs
  const newFAQs = faqs.filter(
    faq => !existingKeys.has(`${faq.question_en}|${faq.question_ja}`)
  )

  if (newFAQs.length > 0) {
    const { error } = await supabaseClient
      .from('chatbot_faq')
      .insert(newFAQs.map(faq => ({
        question_ja: faq.question_ja,
        question_en: faq.question_en,
        answer_ja: faq.answer_ja,
        answer_en: faq.answer_en,
        category: faq.category,
        keywords: faq.keywords,
        is_active: true
      })))

    if (error) throw error
  }

  return newFAQs.length
}