(()=>{var e={};e.id=2772,e.ids=[2772],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79052:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>h,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var a={};s.r(a),s.d(a,{GET:()=>m});var r=s(42706),n=s(28203),o=s(45994),i=s(39187),u=s(61487);async function c(){let e=Date.now();try{let t=(0,u.U)(),{data:s,error:a}=await t.from("users").select("id").limit(1);if(a)return{status:"fail",responseTime:Date.now()-e,message:"Database query failed",details:{error:a.message}};let r=Date.now()-e;return{status:r>1e3?"warn":"pass",responseTime:r,message:r>1e3?"Slow database response":"Database healthy",details:{connection:"active",query_time:`${r}ms`}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"Database connection failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function l(){let e=Date.now();try{let t=process.env.OPENAI_API_KEY,s=process.env.ANTHROPIC_API_KEY;if(!t&&!s)return{status:"warn",responseTime:Date.now()-e,message:"No AI API keys configured",details:{openai:"not_configured",anthropic:"not_configured"}};let a="not_configured";if(t&&"your_openai_api_key_here"!==t)try{a=(await fetch("https://api.openai.com/v1/models",{headers:{Authorization:`Bearer ${t}`},signal:AbortSignal.timeout(5e3)})).ok?"healthy":"error"}catch{a="error"}let r="not_configured";if(s&&"your_anthropic_api_key_here"!==s)try{let e=await fetch("https://api.anthropic.com/v1/messages",{method:"POST",headers:{"x-api-key":s,"content-type":"application/json","anthropic-version":"2023-06-01"},body:JSON.stringify({model:"claude-3-haiku-20240307",max_tokens:1,messages:[{role:"user",content:"test"}]}),signal:AbortSignal.timeout(5e3)});r=200===e.status||400===e.status?"healthy":"error"}catch{r="error"}let n="healthy"===a||"healthy"===r,o=Date.now()-e;return{status:n?"pass":"warn",responseTime:o,message:n?"AI services available":"AI services unavailable",details:{openai:a,anthropic:r}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"AI services check failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function p(){let e=Date.now();try{if(!process.env.REDIS_URL)return{status:"warn",responseTime:Date.now()-e,message:"Redis not configured",details:{status:"not_configured"}};return{status:"warn",responseTime:Date.now()-e,message:"Redis health check not implemented",details:{status:"not_implemented"}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"Redis check failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function d(){let e=Date.now();try{let t="your_supabase_project_url";if(!t)return{status:"fail",responseTime:Date.now()-e,message:"Supabase URL not configured"};let s=await fetch(`${t}/rest/v1/`,{headers:{apikey:"your_supabase_anon_key"},signal:AbortSignal.timeout(5e3)}),a=Date.now()-e;return{status:s.ok?"pass":"fail",responseTime:a,message:s.ok?"External APIs healthy":"External API error",details:{supabase:s.ok?"healthy":"error",status_code:s.status}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"External API check failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function m(e){let t=Date.now();try{let[e,s,a,r]=await Promise.all([c(),l(),p(),d()]),n=function(){let e=process.memoryUsage(),t=Math.round(e.heapUsed/1024/1024),s=Math.round(e.heapTotal/1024/1024),a=t/s*100;return{disk_space:{status:"pass",message:"Disk space check not implemented",details:{status:"not_implemented"}},memory:{status:a>90?"warn":"pass",message:`Memory usage: ${a.toFixed(1)}%`,details:{used_mb:t,total_mb:s,usage_percent:a.toFixed(1)}}}}(),o={database:e,ai_services:s,redis:a,external_apis:r,disk_space:n.disk_space,memory:n.memory},u=Object.values(o).some(e=>"fail"===e.status),m=Object.values(o).some(e=>"warn"===e.status),h=u?"unhealthy":m?"degraded":"healthy",g={status:h,timestamp:new Date().toISOString(),version:process.env.npm_package_version||"1.0.0",uptime:Date.now()-t,checks:o,metadata:{environment:"production",node_version:process.version,next_version:"15.0.0",deployment_id:process.env.DEPLOYMENT_ID}};return i.NextResponse.json(g,{status:"healthy"===h?200:"degraded"===h?200:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}catch(e){return i.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",uptime:Date.now()-t},{status:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}}Date.now();let h=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:_}=h;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},96487:()=>{},78335:()=>{},61487:(e,t,s)=>{"use strict";s.d(t,{U:()=>n});var a=s(49064),r=s(44512);let n=()=>{let e=(0,r.UL)();return(0,a.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:s,options:a})=>e.set(t,s,a))}catch{}}}})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[5994,5452,4512,9064],()=>s(79052));module.exports=a})();