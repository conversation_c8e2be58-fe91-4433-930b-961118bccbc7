"use strict";(()=>{var e={};e.id=2772,e.ids=[2772],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},61343:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var s={};a.r(s),a.d(s,{GET:()=>m});var r=a(42706),n=a(28203),o=a(45994),i=a(39187),u=a(61487);async function p(){let e=Date.now();try{let t=(0,u.U)(),{data:a,error:s}=await t.from("users").select("id").limit(1);if(s)return{status:"fail",responseTime:Date.now()-e,message:"Database query failed",details:{error:s.message}};let r=Date.now()-e;return{status:r>1e3?"warn":"pass",responseTime:r,message:r>1e3?"Slow database response":"Database healthy",details:{connection:"active",query_time:`${r}ms`}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"Database connection failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function c(){let e=Date.now();try{let t=process.env.OPENAI_API_KEY,a=process.env.ANTHROPIC_API_KEY;if(!t&&!a)return{status:"warn",responseTime:Date.now()-e,message:"No AI API keys configured",details:{openai:"not_configured",anthropic:"not_configured"}};let s="not_configured";if(t&&"your_openai_api_key_here"!==t)try{s=(await fetch("https://api.openai.com/v1/models",{headers:{Authorization:`Bearer ${t}`},signal:AbortSignal.timeout(5e3)})).ok?"healthy":"error"}catch{s="error"}let r="not_configured";if(a&&"your_anthropic_api_key_here"!==a)try{let e=await fetch("https://api.anthropic.com/v1/messages",{method:"POST",headers:{"x-api-key":a,"content-type":"application/json","anthropic-version":"2023-06-01"},body:JSON.stringify({model:"claude-3-haiku-20240307",max_tokens:1,messages:[{role:"user",content:"test"}]}),signal:AbortSignal.timeout(5e3)});r=200===e.status||400===e.status?"healthy":"error"}catch{r="error"}let n="healthy"===s||"healthy"===r,o=Date.now()-e;return{status:n?"pass":"warn",responseTime:o,message:n?"AI services available":"AI services unavailable",details:{openai:s,anthropic:r}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"AI services check failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function l(){let e=Date.now();try{if(!process.env.REDIS_URL)return{status:"warn",responseTime:Date.now()-e,message:"Redis not configured",details:{status:"not_configured"}};return{status:"warn",responseTime:Date.now()-e,message:"Redis health check not implemented",details:{status:"not_implemented"}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"Redis check failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function d(){let e=Date.now();try{let t="your_supabase_project_url";if(!t)return{status:"fail",responseTime:Date.now()-e,message:"Supabase URL not configured"};let a=await fetch(`${t}/rest/v1/`,{headers:{apikey:"your_supabase_anon_key"},signal:AbortSignal.timeout(5e3)}),s=Date.now()-e;return{status:a.ok?"pass":"fail",responseTime:s,message:a.ok?"External APIs healthy":"External API error",details:{supabase:a.ok?"healthy":"error",status_code:a.status}}}catch(t){return{status:"fail",responseTime:Date.now()-e,message:"External API check failed",details:{error:t instanceof Error?t.message:"Unknown error"}}}}async function m(e){let t=Date.now();try{let[e,a,s,r]=await Promise.all([p(),c(),l(),d()]),n=function(){let e=process.memoryUsage(),t=Math.round(e.heapUsed/1024/1024),a=Math.round(e.heapTotal/1024/1024),s=t/a*100;return{disk_space:{status:"pass",message:"Disk space check not implemented",details:{status:"not_implemented"}},memory:{status:s>90?"warn":"pass",message:`Memory usage: ${s.toFixed(1)}%`,details:{used_mb:t,total_mb:a,usage_percent:s.toFixed(1)}}}}(),o={database:e,ai_services:a,redis:s,external_apis:r,disk_space:n.disk_space,memory:n.memory},u=Object.values(o).some(e=>"fail"===e.status),m=Object.values(o).some(e=>"warn"===e.status),h=u?"unhealthy":m?"degraded":"healthy",g={status:h,timestamp:new Date().toISOString(),version:process.env.npm_package_version||"1.0.0",uptime:Date.now()-t,checks:o,metadata:{environment:"production",node_version:process.version,next_version:"15.0.0",deployment_id:process.env.DEPLOYMENT_ID}};return i.NextResponse.json(g,{status:"healthy"===h?200:"degraded"===h?200:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}catch(e){return i.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",uptime:Date.now()-t},{status:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}}Date.now();let h=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:f}=h;function w(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>a(61343));module.exports=s})();