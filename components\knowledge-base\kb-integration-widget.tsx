import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useLanguage } from '@/contexts/language-context';
import { supabase } from '@/lib/supabase';
import { EmbeddingsService } from '@/lib/services/embeddings-service';
import { 
  Search, 
  BookOpen, 
  HelpCircle, 
  FileText, 
  Lightbulb,
  ExternalLink,
  Star,
  Clock
} from 'lucide-react';

interface KBIntegrationWidgetProps {
  context?: {
    serviceCategory?: string;
    currentStep?: string;
    formType?: string;
  };
  onArticleClick?: (article: any) => void;
  className?: string;
}

interface Article {
  id: string;
  title_en: string;
  title_jp: string;
  content_en: string;
  content_jp: string;
  category: string;
  relevance?: number;
}

interface FAQ {
  id: string;
  question_ja: string;
  question_en: string;
  answer_ja: string;
  answer_en: string;
  category: string;
  usage_count: number;
}

export function KBIntegrationWidget({ 
  context, 
  onArticleClick,
  className 
}: KBIntegrationWidgetProps) {
  const { language } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<Article[]>([]);
  const [contextualArticles, setContextualArticles] = useState<Article[]>([]);
  const [relevantFAQs, setRelevantFAQs] = useState<FAQ[]>([]);
  const [recentArticles, setRecentArticles] = useState<Article[]>([]);
  const [activeTab, setActiveTab] = useState('contextual');
  
  const embeddingsService = EmbeddingsService.getInstance();

  // Load contextual content based on current form/step
  useEffect(() => {
    if (context?.serviceCategory) {
      loadContextualContent();
    }
  }, [context]);

  // Load recent articles on mount
  useEffect(() => {
    loadRecentArticles();
  }, []);

  const loadContextualContent = async () => {
    try {
      // Load articles related to current context
      const { data: articles, error: articlesError } = await supabase
        .from('kb_articles')
        .select('*')
        .eq('category', context?.serviceCategory)
        .eq('is_published', true)
        .limit(5);

      if (!articlesError && articles) {
        setContextualArticles(articles);
      }

      // Load relevant FAQs
      const { data: faqs, error: faqsError } = await supabase
        .from('chatbot_faq')
        .select('*')
        .eq('category', context?.serviceCategory)
        .eq('is_active', true)
        .order('usage_count', { ascending: false })
        .limit(5);

      if (!faqsError && faqs) {
        setRelevantFAQs(faqs);
      }
    } catch (error) {
      console.error('Error loading contextual content:', error);
    }
  };

  const loadRecentArticles = async () => {
    try {
      const { data, error } = await supabase
        .from('kb_articles')
        .select('*')
        .eq('is_published', true)
        .order('updated_at', { ascending: false })
        .limit(5);

      if (!error && data) {
        setRecentArticles(data);
      }
    } catch (error) {
      console.error('Error loading recent articles:', error);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    try {
      // Use semantic search
      const results = await embeddingsService.semanticSearch(searchQuery, {
        matchThreshold: 0.7,
        matchCount: 10
      });

      setSearchResults(results);
      setActiveTab('search');
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleArticleOpen = (article: Article) => {
    if (onArticleClick) {
      onArticleClick(article);
    } else {
      // Open in new tab
      window.open(`/knowledge-base/article/${article.id}`, '_blank');
    }
  };

  const incrementFAQUsage = async (faqId: string) => {
    await supabase
      .from('chatbot_faq')
      .update({ usage_count: 1 }) // This will be incremented by RLS
      .eq('id', faqId);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          {language === 'ja' ? 'ナレッジベース' : 'Knowledge Base'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Search Bar */}
        <div className="flex gap-2 mb-4">
          <Input
            placeholder={language === 'ja' ? '検索...' : 'Search...'}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="flex-1"
          />
          <Button onClick={handleSearch} disabled={isSearching} size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </div>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="contextual">
              <Lightbulb className="h-4 w-4 mr-1" />
              {language === 'ja' ? '関連' : 'Related'}
            </TabsTrigger>
            <TabsTrigger value="faq">
              <HelpCircle className="h-4 w-4 mr-1" />
              FAQ
            </TabsTrigger>
            <TabsTrigger value="recent">
              <Clock className="h-4 w-4 mr-1" />
              {language === 'ja' ? '最新' : 'Recent'}
            </TabsTrigger>
            <TabsTrigger value="search">
              <Search className="h-4 w-4 mr-1" />
              {language === 'ja' ? '検索' : 'Search'}
            </TabsTrigger>
          </TabsList>

          {/* Contextual Articles */}
          <TabsContent value="contextual">
            <ScrollArea className="h-[300px] mt-4">
              {contextualArticles.length > 0 ? (
                <div className="space-y-2">
                  {contextualArticles.map((article) => (
                    <ArticleCard
                      key={article.id}
                      article={article}
                      language={language}
                      onClick={() => handleArticleOpen(article)}
                    />
                  ))}
                </div>
              ) : (
                <Alert>
                  <AlertDescription>
                    {language === 'ja' 
                      ? '現在のコンテキストに関連する記事はありません。'
                      : 'No articles related to current context.'}
                  </AlertDescription>
                </Alert>
              )}
            </ScrollArea>
          </TabsContent>

          {/* FAQs */}
          <TabsContent value="faq">
            <ScrollArea className="h-[300px] mt-4">
              {relevantFAQs.length > 0 ? (
                <div className="space-y-3">
                  {relevantFAQs.map((faq) => (
                    <FAQCard
                      key={faq.id}
                      faq={faq}
                      language={language}
                      onClick={() => incrementFAQUsage(faq.id)}
                    />
                  ))}
                </div>
              ) : (
                <Alert>
                  <AlertDescription>
                    {language === 'ja' 
                      ? 'よくある質問はまだありません。'
                      : 'No FAQs available yet.'}
                  </AlertDescription>
                </Alert>
              )}
            </ScrollArea>
          </TabsContent>

          {/* Recent Articles */}
          <TabsContent value="recent">
            <ScrollArea className="h-[300px] mt-4">
              <div className="space-y-2">
                {recentArticles.map((article) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    language={language}
                    onClick={() => handleArticleOpen(article)}
                  />
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Search Results */}
          <TabsContent value="search">
            <ScrollArea className="h-[300px] mt-4">
              {searchResults.length > 0 ? (
                <div className="space-y-2">
                  {searchResults.map((article) => (
                    <ArticleCard
                      key={article.id}
                      article={article}
                      language={language}
                      onClick={() => handleArticleOpen(article)}
                      showRelevance={true}
                    />
                  ))}
                </div>
              ) : (
                <Alert>
                  <AlertDescription>
                    {language === 'ja' 
                      ? '検索結果がありません。'
                      : 'No search results found.'}
                  </AlertDescription>
                </Alert>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

// Article Card Component
function ArticleCard({ 
  article, 
  language, 
  onClick,
  showRelevance = false 
}: {
  article: Article;
  language: string;
  onClick: () => void;
  showRelevance?: boolean;
}) {
  const title = language === 'ja' ? article.title_jp : article.title_en;
  const content = language === 'ja' ? article.content_jp : article.content_en;
  const preview = content.substring(0, 100) + '...';

  return (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={onClick}
    >
      <CardContent className="p-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <FileText className="h-4 w-4" />
              {title}
            </h4>
            <p className="text-xs text-muted-foreground mt-1">{preview}</p>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline" className="text-xs">
                {article.category}
              </Badge>
              {showRelevance && article.relevance && (
                <Badge variant="secondary" className="text-xs">
                  {Math.round(article.relevance * 100)}% match
                </Badge>
              )}
            </div>
          </div>
          <ExternalLink className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardContent>
    </Card>
  );
}

// FAQ Card Component
function FAQCard({ 
  faq, 
  language,
  onClick 
}: {
  faq: FAQ;
  language: string;
  onClick: () => void;
}) {
  const [expanded, setExpanded] = useState(false);
  const question = language === 'ja' ? faq.question_ja : faq.question_en;
  const answer = language === 'ja' ? faq.answer_ja : faq.answer_en;

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpanded(!expanded);
    if (!expanded) {
      onClick();
    }
  };

  return (
    <Card 
      className="cursor-pointer hover:shadow-sm transition-all"
      onClick={handleClick}
    >
      <CardContent className="p-3">
        <div className="flex items-start gap-2">
          <HelpCircle className="h-4 w-4 text-primary mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-sm">{question}</h4>
            {expanded && (
              <p className="text-sm text-muted-foreground mt-2">{answer}</p>
            )}
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline" className="text-xs">
                {faq.category}
              </Badge>
              {faq.usage_count > 0 && (
                <span className="text-xs text-muted-foreground flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {faq.usage_count}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}