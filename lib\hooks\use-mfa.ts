'use client';

import { useState, useCallback } from 'react';
import { useSupabase } from '@/lib/supabase';
import { MFAService } from '@/lib/services/mfa-service';

export interface MFAStatus {
  enabled: boolean;
  required: boolean;
  methods: string[];
}

export function useMFA() {
  const { supabase } = useSupabase();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const mfaService = new MFAService(supabase);

  const checkMFAStatus = useCallback(async (userId: string): Promise<MFAStatus> => {
    try {
      return await mfaService.checkMFAStatus(userId);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, [mfaService]);

  const setupTOTP = useCallback(async (userId: string, userEmail: string) => {
    setLoading(true);
    setError(null);
    try {
      return await mfaService.setupTOTP(userId, userEmail);
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [mfaService]);

  const verifyTOTPSetup = useCallback(async (userId: string, code: string) => {
    setLoading(true);
    setError(null);
    try {
      return await mfaService.verifyTOTPSetup(userId, code);
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [mfaService]);

  const createChallenge = useCallback(async (userId: string) => {
    setLoading(true);
    setError(null);
    try {
      return await mfaService.createChallenge(userId);
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [mfaService]);

  const verifyCode = useCallback(async (sessionToken: string, code: string) => {
    setLoading(true);
    setError(null);
    try {
      return await mfaService.verifyCode(sessionToken, code);
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [mfaService]);

  const setupSMS = useCallback(async (userId: string, phoneNumber: string) => {
    // TODO: Implement SMS setup
    setLoading(true);
    setError(null);
    try {
      // Implementation would go here
      throw new Error('SMS setup not yet implemented');
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const setupEmail = useCallback(async (userId: string) => {
    // TODO: Implement Email setup
    setLoading(true);
    setError(null);
    try {
      // Implementation would go here
      throw new Error('Email setup not yet implemented');
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    checkMFAStatus,
    setupTOTP,
    verifyTOTPSetup,
    createChallenge,
    verifyCode,
    setupSMS,
    setupEmail,
  };
}
