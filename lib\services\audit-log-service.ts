import { supabase } from '@/lib/supabase';

export type AuditLogEventType = 
  | 'USER_LOGIN' | 'USER_LOGOUT' | 'REQUEST_CREATED' | 'REQUEST_UPDATED' | 'REQUEST_DELETED'
  | 'USER_CREATED' | 'USER_UPDATED' | 'USER_DELETED' | 'ROLE_CHANGED'
  | 'PERMISSION_GRANTED' | 'PERMISSION_REVOKED' | 'DATA_EXPORTED'
  | 'SYSTEM_CONFIG_CHANGED' | 'ERROR_OCCURRED';

export type AuditLogSeverity = 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';

export interface AuditLogEntry {
  id?: string;
  event_type: AuditLogEventType;
  severity: AuditLogSeverity;
  user_id: string | null;
  staff_id: string | null;
  action: string;
  entity_type?: string;
  entity_id?: string;
  old_value?: any;
  new_value?: any;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
  session_id?: string;
  department_id?: string;
}

export interface AuditSearchParams {
  event_type?: AuditLogEventType[];
  severity?: AuditLogSeverity[];
  user_id?: string;
  entity_type?: string;
  entity_id?: string;
  start_date?: Date;
  end_date?: Date;
  limit?: number;
  offset?: number;
}

class AuditLogService {
  private static instance: AuditLogService;
  private queue: AuditLogEntry[] = [];
  private flushInterval: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 10;
  private readonly FLUSH_INTERVAL = 5000;
  private constructor() { this.startBatchProcessing(); }

  public static getInstance(): AuditLogService {
    if (!AuditLogService.instance) {
      AuditLogService.instance = new AuditLogService();
    }
    return AuditLogService.instance;
  }

  private startBatchProcessing() {
    this.flushInterval = setInterval(() => {
      this.flush();
    }, this.FLUSH_INTERVAL);
  }

  private async flush() {
    if (this.queue.length === 0) return;
    
    const batch = this.queue.splice(0, this.BATCH_SIZE);
    
    try {
      // Create a table for audit logs if it doesn't exist
      const { error } = await supabase
        .from('audit_logs')
        .insert(batch);
      
      if (error) {
        console.error('Failed to write audit logs:', error);
        // Re-queue failed logs
        this.queue.unshift(...batch);
      }
    } catch (error) {
      console.error('Error writing audit logs:', error);
      this.queue.unshift(...batch);
    }
  }

  public async log(entry: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> {
    const logEntry: AuditLogEntry = {
      ...entry,
      timestamp: new Date().toISOString(),
    };

    // Get current user context if available
    const { data: { user } } = await supabase.auth.getUser();
    if (user && !logEntry.user_id) {
      logEntry.user_id = user.id;
    }

    // Add to queue for batch processing
    this.queue.push(logEntry);

    // Flush immediately if queue is full
    if (this.queue.length >= this.BATCH_SIZE) {
      this.flush();
    }
  }

  public async logUserAction(
    action: string,
    entityType?: string,
    entityId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      event_type: 'USER_LOGIN',
      severity: 'INFO',
      user_id: null,
      staff_id: null,
      action,
      entity_type: entityType,
      entity_id: entityId,
      metadata,
    });
  }

  public async logError(
    action: string,
    error: Error,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      event_type: 'ERROR_OCCURRED',
      severity: 'ERROR',
      user_id: null,
      staff_id: null,
      action,
      metadata: {
        ...metadata,
        error_message: error.message,
        error_stack: error.stack,
      },
    });
  }

  public async search(params: AuditSearchParams): Promise<AuditLogEntry[]> {
    let query = supabase.from('audit_logs').select('*');

    if (params.event_type && params.event_type.length > 0) {
      query = query.in('event_type', params.event_type);
    }

    if (params.severity && params.severity.length > 0) {
      query = query.in('severity', params.severity);
    }

    if (params.user_id) {
      query = query.eq('user_id', params.user_id);
    }

    if (params.entity_type) {
      query = query.eq('entity_type', params.entity_type);
    }

    if (params.entity_id) {
      query = query.eq('entity_id', params.entity_id);
    }

    if (params.start_date) {
      query = query.gte('timestamp', params.start_date.toISOString());
    }

    if (params.end_date) {
      query = query.lte('timestamp', params.end_date.toISOString());
    }

    query = query.order('timestamp', { ascending: false });

    if (params.limit) {
      query = query.limit(params.limit);
    }

    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error searching audit logs:', error);
      return [];
    }

    return data || [];
  }

  public async exportLogs(params: AuditSearchParams): Promise<Blob> {
    const logs = await this.search(params);
    const csv = this.convertToCSV(logs);
    return new Blob([csv], { type: 'text/csv' });
  }

  private convertToCSV(logs: AuditLogEntry[]): string {
    const headers = [
      'Timestamp', 'Event Type', 'Severity', 'User ID', 'Action',
      'Entity Type', 'Entity ID', 'IP Address', 'User Agent'
    ].join(',');

    const rows = logs.map(log => [
      log.timestamp,
      log.event_type,
      log.severity,
      log.user_id || '',
      log.action,
      log.entity_type || '',
      log.entity_id || '',
      log.ip_address || '',
      log.user_agent || ''
    ].map(field => `"${field}"`).join(','));

    return [headers, ...rows].join('\n');
  }

  public destroy() {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    this.flush(); // Final flush
  }
}

// Export singleton instance
export const auditLogService = AuditLogService.getInstance();

// Audit log helper functions
export const logUserLogin = (userId: string, metadata?: Record<string, any>) => {
  return auditLogService.log({
    event_type: 'USER_LOGIN',
    severity: 'INFO',
    user_id: userId,
    staff_id: null,
    action: 'User logged in',
    metadata
  });
};

export const logRequestCreated = (requestId: string, requestTitle: string, userId: string) => {
  return auditLogService.log({
    event_type: 'REQUEST_CREATED',
    severity: 'INFO',
    user_id: userId,
    staff_id: null,
    action: `Created request: ${requestTitle}`,
    entity_type: 'request',
    entity_id: requestId
  });
};
