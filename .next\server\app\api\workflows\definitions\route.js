"use strict";(()=>{var e={};e.id=3271,e.ids=[3271],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},42342:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>f});var o=t(42706),n=t(28203),i=t(45994),a=t(39187),d=t(52054),u=t(59913);!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let l=u.z.object({name:u.z.string().min(1),description:u.z.string().optional(),workflow_json:u.z.object({id:u.z.string(),name:u.z.string(),version:u.z.number(),triggers:u.z.record(u.z.any()),states:u.z.record(u.z.any())})});async function p(){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:o}=await e.from("workflow_definitions").select("*").eq("is_active",!0).order("name",{ascending:!0});if(o)return a.NextResponse.json({error:o.message},{status:500});return a.NextResponse.json({data:s})}catch(e){return console.error("Error fetching workflow definitions:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("role:roles(name)").eq("auth_id",t.id).single(),n=o?.role?.name;if(!["Global Administrator","Web App System Administrator"].includes(n))return a.NextResponse.json({error:"Forbidden"},{status:403});let i=await e.json(),u=l.safeParse(i);if(!u.success)return a.NextResponse.json({error:"Invalid request data",details:u.error.errors},{status:400});let{name:p,description:f,workflow_json:c}=u.data,{data:w}=await r.from("workflow_definitions").select("id").eq("name",p).eq("is_active",!0).single();if(w)return a.NextResponse.json({error:"Workflow definition with this name already exists"},{status:409});let{data:m,error:x}=await r.from("workflow_definitions").insert({name:p,description:f,workflow_json:c,version:1}).select().single();if(x)return a.NextResponse.json({error:x.message},{status:500});let v=new d.I;try{await v.validateWorkflowDefinition(m)}catch(e){return await r.from("workflow_definitions").delete().eq("id",m.id),a.NextResponse.json({error:"Invalid workflow definition",details:e.message},{status:400})}return a.NextResponse.json({data:m},{status:201})}catch(e){return console.error("Error creating workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/definitions/route",pathname:"/api/workflows/definitions",filename:"route",bundlePath:"app/api/workflows/definitions/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:x}=c;function v(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(42342));module.exports=s})();