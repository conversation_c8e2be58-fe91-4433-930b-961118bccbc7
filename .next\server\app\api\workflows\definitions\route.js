"use strict";(()=>{var e={};e.id=3271,e.ids=[3271],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},42342:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>f,POST:()=>c});var o=t(42706),n=t(28203),i=t(45994),a=t(39187),d=t(61487),u=t(2924),l=t(98837);let p=l.z.object({name:l.z.string().min(1),description:l.z.string().optional(),workflow_json:l.z.object({id:l.z.string(),name:l.z.string(),version:l.z.number(),triggers:l.z.record(l.z.any()),states:l.z.record(l.z.any())})});async function f(){try{let e=await (0,d.createServerClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:o}=await e.from("workflow_definitions").select("*").eq("is_active",!0).order("name",{ascending:!0});if(o)return a.NextResponse.json({error:o.message},{status:500});return a.NextResponse.json({data:s})}catch(e){return console.error("Error fetching workflow definitions:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e){try{let r=await (0,d.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("role:roles(name)").eq("auth_id",t.id).single(),n=o?.role?.name;if(!["Global Administrator","Web App System Administrator"].includes(n))return a.NextResponse.json({error:"Forbidden"},{status:403});let i=await e.json(),l=p.safeParse(i);if(!l.success)return a.NextResponse.json({error:"Invalid request data",details:l.error.errors},{status:400});let{name:f,description:c,workflow_json:w}=l.data,{data:x}=await r.from("workflow_definitions").select("id").eq("name",f).eq("is_active",!0).single();if(x)return a.NextResponse.json({error:"Workflow definition with this name already exists"},{status:409});let{data:m,error:g}=await r.from("workflow_definitions").insert({name:f,description:c,workflow_json:w,version:1}).select().single();if(g)return a.NextResponse.json({error:g.message},{status:500});let j=new u.I;try{await j.validateWorkflowDefinition(m)}catch(e){return await r.from("workflow_definitions").delete().eq("id",m.id),a.NextResponse.json({error:"Invalid workflow definition",details:e.message},{status:400})}return a.NextResponse.json({data:m},{status:201})}catch(e){return console.error("Error creating workflow definition:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/definitions/route",pathname:"/api/workflows/definitions",filename:"route",bundlePath:"app/api/workflows/definitions/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:g}=w;function j(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(42342));module.exports=s})();