exports.id=2963,exports.ids=[2963],exports.modules={19644:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},77788:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},42125:()=>{},66093:()=>{},97643:(e,r,s)=>{"use strict";s.d(r,{BT:()=>l,Wu:()=>u,ZB:()=>c,Zp:()=>n,aR:()=>i,wL:()=>p});var t=s(45512),a=s(58009),o=s(59462);let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));n.displayName="Card";let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...r}));c.displayName="CardTitle";let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("p-6 pt-0",e),...r}));u.displayName="CardContent";let p=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,o.cn)("flex items-center p-6 pt-0",e),...r}));p.displayName="CardFooter"},43433:(e,r,s)=>{"use strict";s.d(r,{A:()=>o}),s(45512);var t=s(58009);s(58957),s(20460);let a=(0,t.createContext)(void 0),o=()=>{let e=(0,t.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},58957:(e,r,s)=>{"use strict";s.d(r,{H1:()=>c,ND:()=>o,Z6:()=>l,gG:()=>n});var t=s(93939),a=s(20460);let o=(0,a.hV)().isValid?(0,t.createClient)(a.$W.supabase.url,a.$W.supabase.anonKey):null;var n=function(e){return e.GLOBAL_ADMIN="Global Administrator",e.SYSTEM_ADMIN="Web App System Administrator",e.DEPT_ADMIN="Department Administrator",e.HR_STAFF="HR Staff",e.IT_SUPPORT="IT Helpdesk Support",e.REGULAR_USER="Regular User",e}({});let i={"Global Administrator":[{resource:"*",action:"create",scope:"all"},{resource:"*",action:"read",scope:"all"},{resource:"*",action:"update",scope:"all"},{resource:"*",action:"delete",scope:"all"}],"Web App System Administrator":[{resource:"users",action:"create",scope:"all"},{resource:"users",action:"read",scope:"all"},{resource:"users",action:"update",scope:"all"},{resource:"roles",action:"create",scope:"all"},{resource:"roles",action:"read",scope:"all"},{resource:"roles",action:"update",scope:"all"},{resource:"system",action:"read",scope:"all"}],"Department Administrator":[{resource:"users",action:"create",scope:"department"},{resource:"users",action:"read",scope:"department"},{resource:"users",action:"update",scope:"department"},{resource:"requests",action:"read",scope:"department"},{resource:"requests",action:"update",scope:"department"}],"HR Staff":[{resource:"hr_requests",action:"create",scope:"all"},{resource:"hr_requests",action:"read",scope:"all"},{resource:"hr_requests",action:"update",scope:"all"},{resource:"users",action:"create",scope:"all"},{resource:"users",action:"read",scope:"all"},{resource:"users",action:"update",scope:"all"}],"IT Helpdesk Support":[{resource:"requests",action:"read",scope:"all"},{resource:"requests",action:"update",scope:"all"},{resource:"knowledge_base",action:"create",scope:"all"},{resource:"knowledge_base",action:"read",scope:"all"},{resource:"knowledge_base",action:"update",scope:"all"}],"Regular User":[{resource:"requests",action:"create",scope:"own"},{resource:"requests",action:"read",scope:"own"},{resource:"knowledge_base",action:"read",scope:"all"}]},c=(e,r,s,t)=>{let a=i[e];return!!a&&a.some(e=>"*"===e.resource||e.resource===r&&e.action===s&&(!t||e.scope===t||"all"===e.scope))},l=(e,r,s)=>"Global Administrator"===e||"Web App System Administrator"===e||("Department Administrator"===e?r===s:"HR Staff"===e||"IT Helpdesk Support"===e||"Regular User"===e&&r===s)},20460:(e,r,s)=>{"use strict";s.d(r,{$W:()=>o,hV:()=>a});let t=[{key:"NEXT_PUBLIC_SUPABASE_URL",required:!0,pattern:/^https:\/\/[a-z0-9-]+\.supabase\.co$/,description:"Supabase project URL"},{key:"NEXT_PUBLIC_SUPABASE_ANON_KEY",required:!0,pattern:/^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/,description:"Supabase anonymous key (JWT format)"},{key:"SUPABASE_SERVICE_ROLE_KEY",required:!0,pattern:/^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/,description:"Supabase service role key (JWT format)",securityCheck:e=>e.includes("anon")?"Service role key appears to be an anon key - this is a security risk":null},{key:"OPENAI_API_KEY",required:!1,pattern:/^sk-proj-[A-Za-z0-9-_]{48,}$/,description:"OpenAI API key",securityCheck:e=>"your_openai_api_key_here"===e||e.length<50?"OpenAI API key appears to be a placeholder or too short":null},{key:"ANTHROPIC_API_KEY",required:!1,pattern:/^sk-ant-api03-[A-Za-z0-9-_]{48,}$/,description:"Anthropic API key",securityCheck:e=>"your_anthropic_api_key_here"===e||e.length<50?"Anthropic API key appears to be a placeholder or too short":null},{key:"ENCRYPTION_KEY",required:!0,pattern:/^[A-Za-z0-9]{32,}$/,description:"Encryption key for sensitive data (minimum 32 characters)",securityCheck:e=>e.length<32?"Encryption key must be at least 32 characters long":"your_32_character_encryption_key_here"===e?"Encryption key is still using placeholder value":null},{key:"JWT_SECRET",required:!0,pattern:/^[A-Za-z0-9]{32,}$/,description:"JWT secret for session management (minimum 32 characters)",securityCheck:e=>e.length<32?"JWT secret must be at least 32 characters long":"your_jwt_secret_here"===e?"JWT secret is still using placeholder value":null}];!function(){let e=function(){let e={isValid:!0,errors:[],warnings:[],securityIssues:[]};for(let r of t){let s=process.env[r.key];if(r.required&&!s){e.errors.push(`Missing required environment variable: ${r.key} - ${r.description}`),e.isValid=!1;continue}if(s&&(r.pattern&&!r.pattern.test(s)&&(e.errors.push(`Invalid format for ${r.key}: ${r.description}`),e.isValid=!1),r.securityCheck)){let t=r.securityCheck(s);t&&(e.securityIssues.push(`${r.key}: ${t}`),e.isValid=!1)}}{"true"===process.env.DEBUG&&e.securityIssues.push("DEBUG mode is enabled in production"),"0"===process.env.NODE_TLS_REJECT_UNAUTHORIZED&&(e.securityIssues.push("TLS certificate validation is disabled in production"),e.isValid=!1);let r="http://localhost:3000";r&&!r.startsWith("https://")&&(e.securityIssues.push("App URL must use HTTPS in production"),e.isValid=!1)}let r=["your_","placeholder","example","test_key","demo_"];return Object.entries(process.env).forEach(([s,t])=>{(s.includes("KEY")||s.includes("SECRET")||s.includes("PASSWORD"))&&r.forEach(r=>{t?.toLowerCase().includes(r)&&e.warnings.push(`${s} appears to contain placeholder value`)})}),e}();e.errors.length>0&&(console.error("❌ Environment Validation Errors:"),e.errors.forEach(e=>console.error(`  - ${e}`))),e.securityIssues.length>0&&(console.warn("\uD83D\uDD12 Security Issues:"),e.securityIssues.forEach(e=>console.warn(`  - ${e}`))),e.warnings.length>0&&(console.warn("⚠️  Warnings:"),e.warnings.forEach(e=>console.warn(`  - ${e}`))),e.isValid&&0===e.errors.length&&0===e.securityIssues.length&&console.log("✅ Environment validation passed"),e.isValid||(console.error("❌ Environment validation failed. Please check your .env.local file."),process.exit(1))}();let a=()=>{try{let e="your_supabase_project_url",r="your_supabase_anon_key";if(!e||!r)return{url:e,anonKey:r,isValid:!1,error:"Missing Supabase configuration"};if(!e.includes("supabase.co"))return{url:e,anonKey:r,isValid:!1,error:"Invalid Supabase URL format"};if(r.length<20)return{url:e,anonKey:r,isValid:!1,error:"Invalid Supabase anon key format"};return{url:e,anonKey:r,isValid:!0}}catch(e){return{url:"",anonKey:"",isValid:!1,error:e instanceof Error?e.message:"Unknown error validating Supabase config"}}},o={supabase:{url:"your_supabase_project_url",anonKey:"your_supabase_anon_key",serviceRoleKey:process.env.SUPABASE_SERVICE_ROLE_KEY||""},ai:{openaiKey:process.env.OPENAI_API_KEY||"",anthropicKey:process.env.ANTHROPIC_API_KEY||""},app:{url:"http://localhost:3000",name:"ITSync",env:"production"}}},59462:(e,r,s)=>{"use strict";s.d(r,{cn:()=>o});var t=s(82281),a=s(94805);function o(...e){return(0,a.QP)((0,t.$)(e))}},19611:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>n});var t=s(62740),a=s(61421),o=s.n(a);s(82704);let n={title:"ITSync - Enterprise IT Helpdesk",description:"AI-powered IT Helpdesk & Support Platform"};function i({children:e}){return(0,t.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsx)("body",{className:o().className,children:e})})}},82704:()=>{}};