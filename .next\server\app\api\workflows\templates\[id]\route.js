(()=>{var e={};e.id=1242,e.ids=[1242],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82225:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>c,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>l});var o=t(42706),n=t(28203),a=t(45994),i=t(39187),u=t(61487),p=t(6804),d=t(2924);async function l(e,{params:r}){try{let e=(0,u.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(e),n=new d.I(e),a=new p.WorkflowTemplateManager(e,o,n),l=await a.getTemplate(r.id);if(!l)return i.NextResponse.json({error:"Template not found"},{status:404});return i.NextResponse.json({template:l})}catch(e){return console.error("Error fetching workflow template:",e),i.NextResponse.json({error:"Failed to fetch workflow template"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/templates/[id]/route",pathname:"/api/workflows/templates/[id]",filename:"route",bundlePath:"app/api/workflows/templates/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:w}=c;function b(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},6804:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Expression expected\n   ,-[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-template-manager.ts\x1b[0m:2:1]\n \x1b[2m1\x1b[0m | owEngine\n \x1b[2m2\x1b[0m |   ) {\n   : \x1b[35;1m  ^\x1b[0m\n \x1b[2m3\x1b[0m |     this.supabase = supabase;\n \x1b[2m4\x1b[0m |     this.auditService = auditService;\n \x1b[2m5\x1b[0m |     this.workflowEngine = workflowEngine;\n   `----\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064,3744,9389,2924],()=>t(82225));module.exports=s})();