"use strict";(()=>{var e={};e.id=1242,e.ids=[1242],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63530:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>w});var o={};t.r(o),t.d(o,{GET:()=>d});var s=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(6804),p=t(52054);async function d(e,{params:r}){try{let e=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let s=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(e),a=new p.I(e),n=new u.WorkflowTemplateManager(e,s,a),d=await n.getTemplate(r.id);if(!d)return i.NextResponse.json({error:"Template not found"},{status:404});return i.NextResponse.json({template:d})}catch(e){return console.error("Error fetching workflow template:",e),i.NextResponse.json({error:"Failed to fetch workflow template"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let l=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/templates/[id]/route",pathname:"/api/workflows/templates/[id]",filename:"route",bundlePath:"app/api/workflows/templates/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\[id]\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:w,serverHooks:f}=l;function m(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:w})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(63530));module.exports=o})();