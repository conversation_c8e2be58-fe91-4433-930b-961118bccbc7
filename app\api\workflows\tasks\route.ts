import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user data
    const { data: userData } = await supabase
      .from('staff')
      .select('*, role:roles(*)')
      .eq('auth_id', user.id)
      .single();

    if (!userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') || 'pending';

    // Build query based on user role
    let query = supabase
      .from('workflow_tasks')
      .select(`
        *,
        workflow_instance:workflow_instances(
          *,
          workflow_definition:workflow_definitions(*),
          request:request_forms(*)
        )
      `)
      .eq('status', status)
      .order('due_date', { ascending: true });

    // Filter tasks based on assignment
    query = query.or(`assigned_to.eq.${userData.id},assigned_role.eq.${userData.role?.name}`);

    const { data, error } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error fetching workflow tasks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
