import { BaseAIAgent, AgentSpecialization, EducationalContent, EducationalStep } from './base-agent'

/**
 * Specialized AI Agent for PC Administrative Requests
 */
export class PCAdminAgent extends BaseAIAgent {
  constructor() {
    const specialization: AgentSpecialization = {
      serviceCategory: 'pc_admin',
      expertise: [
        'Windows administration',
        'software installation',
        'admin privileges',
        'security policies',
        'software deployment',
        'troubleshooting Windows issues'
      ],
      languages: ['en', 'jp'],
      targetAudience: 'non-technical staff',
      capabilities: {
        webScraping: true,
        contentTranslation: true,
        interactiveGuides: true,
        videoTutorials: true,
        stepByStepWizards: true,
        troubleshooting: true,
        realTimeAssistance: true
      }
    }
    
    super('pc-admin-agent', specialization)
  }

  protected buildEducationalPrompt(topic: string, content: string): string {
    return `
You are a Windows PC expert helping non-technical office workers understand administrative tasks.

Topic: ${topic}

Reference Content:
${content}

Create an educational guide that:
1. Explains why admin rights are needed in simple terms
2. Shows how to properly request admin access
3. Covers security best practices
4. Provides safe software installation steps
5. Includes what to do if something goes wrong

Use office analogies (like keys to locked cabinets) to explain concepts.
Emphasize security without scaring users.

Format as JSON with interactive steps that guide users safely through PC admin tasks.
`
  }

  protected parseEducationalContent(aiResponse: string, topic: string): EducationalContent {
    try {
      const parsed = JSON.parse(aiResponse)
      return {
        type: parsed.type || 'wizard',
        title: parsed.title || `PC Admin Guide: ${topic}`,
        titleJp: parsed.titleJp || `PC管理ガイド: ${topic}`,
        content: parsed.content || '',
        contentJp: parsed.contentJp || '',
        difficulty: 'intermediate',
        estimatedTime: parsed.estimatedTime || 20,
        interactive: true,
        steps: parsed.steps || [],
        relatedTopics: parsed.relatedTopics || ['security', 'software', 'permissions']
      }
    } catch (error) {
      return this.createFallbackContent(topic)
    }
  }

  private createFallbackContent(topic: string): EducationalContent {
    return {
      type: 'wizard',
      title: `PC Administration: ${topic}`,
      titleJp: `PC管理: ${topic}`,
      content: `Learn about PC administrative tasks for ${topic}`,
      contentJp: `${topic}のPC管理タスクについて学ぶ`,
      difficulty: 'intermediate',
      estimatedTime: 15,
      interactive: true,
      steps: []
    }
  }

  protected shouldHaveVisual(step: EducationalStep): boolean {
    // PC admin tasks always need visuals for clarity
    return true
  }

  protected async generateVisualAid(step: EducationalStep): Promise<string> {
    // Generate contextual image paths
    if (step.description.includes('Control Panel')) {
      return '/images/pc-admin/control-panel-guide.png'
    }
    if (step.description.includes('UAC')) {
      return '/images/pc-admin/uac-prompt.png'
    }
    return `/images/pc-admin/step-${step.order}.png`
  }

  protected async generateValidation(step: EducationalStep): Promise<string> {
    if (step.userAction?.includes('install')) {
      return 'Verify software installed correctly and appears in Programs list'
    }
    if (step.userAction?.includes('permission')) {
      return 'Check if admin rights are active (shield icon appears)'
    }
    if (step.userAction?.includes('security')) {
      return 'Confirm security settings are properly configured'
    }
    return 'Verify the action completed without errors'
  }

  protected async generateTroubleshooting(step: EducationalStep): Promise<string[]> {
    const tips: string[] = [
      'If you see "Access Denied", you need to request admin rights first',
      'For installation errors, check if you have enough disk space',
      'If the software doesn\'t work, try restarting your PC',
      'Contact IT support if you see any security warnings'
    ]
    
    if (step.description.includes('install')) {
      tips.push('Some software requires specific versions of .NET Framework')
      tips.push('Antivirus may block installations - contact IT if this happens')
    }
    
    return tips
  }

  protected analyzeFeedbackPatterns(feedback: any[]): Map<string, any> {
    const patterns = new Map<string, any>()
    
    // Track specific PC admin issues
    const securityConcerns = feedback.filter(f => 
      f.suggestion?.toLowerCase().includes('security') ||
      f.suggestion?.toLowerCase().includes('virus')
    ).length
    
    const installationIssues = feedback.filter(f => 
      f.suggestion?.toLowerCase().includes('install') ||
      f.suggestion?.toLowerCase().includes('software')
    ).length
    
    patterns.set('securityConcerns', securityConcerns)
    patterns.set('installationIssues', installationIssues)
    
    return patterns
  }

  protected async adjustBehavior(patterns: Map<string, any>): Promise<void> {
    const securityConcerns = patterns.get('securityConcerns') || 0
    
    if (securityConcerns > 3) {
      console.log(`[${this.agentId}] Enhancing security explanations in content`)
      // Future content will include more security reassurances
    }
  }
}

/**
 * Specialized AI Agent for Mailbox Management
 */
export class MailboxAgent extends BaseAIAgent {
  constructor() {
    const specialization: AgentSpecialization = {
      serviceCategory: 'mailbox',
      expertise: [
        'email account management',
        'mailbox configuration',
        'email signatures',
        'out of office settings',
        'email forwarding',
        'mailbox permissions'
      ],
      languages: ['en', 'jp'],
      targetAudience: 'all staff',
      capabilities: {
        webScraping: true,
        contentTranslation: true,
        interactiveGuides: true,
        videoTutorials: false,
        stepByStepWizards: true,
        troubleshooting: true,
        realTimeAssistance: true
      }
    }
    
    super('mailbox-agent', specialization)
  }

  protected buildEducationalPrompt(topic: string, content: string): string {
    return `
You are an email expert helping users manage their mailboxes effectively.

Topic: ${topic}

Reference Content:
${content}

Create an educational guide that:
1. Explains mailbox concepts using everyday analogies
2. Shows proper email etiquette for Japanese business culture
3. Covers common mailbox management tasks
4. Includes tips for organizing emails efficiently
5. Addresses privacy and security concerns

Remember that users may be managing both personal and shared mailboxes.

Format as JSON with clear, step-by-step instructions suitable for all staff levels.
`
  }

  protected parseEducationalContent(aiResponse: string, topic: string): EducationalContent {
    try {
      const parsed = JSON.parse(aiResponse)
      return {
        type: parsed.type || 'guide',
        title: parsed.title || `Mailbox Guide: ${topic}`,
        titleJp: parsed.titleJp || `メールボックスガイド: ${topic}`,
        content: parsed.content || '',
        contentJp: parsed.contentJp || '',
        difficulty: 'beginner',
        estimatedTime: parsed.estimatedTime || 10,
        interactive: true,
        steps: parsed.steps || [],
        relatedTopics: parsed.relatedTopics || ['email', 'communication', 'organization']
      }
    } catch (error) {
      return this.createFallbackContent(topic)
    }
  }

  private createFallbackContent(topic: string): EducationalContent {
    return {
      type: 'guide',
      title: `Mailbox Management: ${topic}`,
      titleJp: `メールボックス管理: ${topic}`,
      content: `Learn how to manage your mailbox for ${topic}`,
      contentJp: `${topic}のためのメールボックス管理方法を学ぶ`,
      difficulty: 'beginner',
      estimatedTime: 10,
      interactive: false
    }
  }

  protected shouldHaveVisual(step: EducationalStep): boolean {
    // Visual aids for UI elements
    const visualKeywords = ['button', 'menu', 'settings', 'option', 'screen']
    return visualKeywords.some(keyword => 
      step.description.toLowerCase().includes(keyword)
    )
  }

  protected async generateVisualAid(step: EducationalStep): Promise<string> {
    if (step.description.includes('signature')) {
      return '/images/mailbox/email-signature-setup.png'
    }
    if (step.description.includes('forward')) {
      return '/images/mailbox/forwarding-rules.png'
    }
    return `/images/mailbox/step-${step.order}.png`
  }

  protected async generateValidation(step: EducationalStep): Promise<string> {
    if (step.userAction?.includes('signature')) {
      return 'Send a test email to verify signature appears correctly'
    }
    if (step.userAction?.includes('forward')) {
      return 'Check if forwarding rule is active and working'
    }
    if (step.userAction?.includes('folder')) {
      return 'Verify folder was created and is visible'
    }
    return 'Confirm settings were saved successfully'
  }

  protected async generateTroubleshooting(step: EducationalStep): Promise<string[]> {
    return [
      'If changes don\'t appear, try logging out and back in',
      'For signature issues, check the formatting is not too complex',
      'Forwarding may take a few minutes to activate',
      'Some features may require IT approval first'
    ]
  }

  protected analyzeFeedbackPatterns(feedback: any[]): Map<string, any> {
    const patterns = new Map<string, any>()
    
    const organizationIssues = feedback.filter(f => 
      f.suggestion?.toLowerCase().includes('organize') ||
      f.suggestion?.toLowerCase().includes('folder')
    ).length
    
    patterns.set('organizationIssues', organizationIssues)
    
    return patterns
  }

  protected async adjustBehavior(patterns: Map<string, any>): Promise<void> {
    const organizationIssues = patterns.get('organizationIssues') || 0
    
    if (organizationIssues > 3) {
      console.log(`[${this.agentId}] Creating more email organization content`)
      // Focus on email organization strategies
    }
  }
}

/**
 * Master AI Agent that coordinates all other agents
 */
export class MasterKnowledgeAgent extends BaseAIAgent {
  constructor() {
    const specialization: AgentSpecialization = {
      serviceCategory: 'general',
      expertise: [
        'IT helpdesk overview',
        'service coordination',
        'best practices',
        'troubleshooting methodology',
        'user guidance'
      ],
      languages: ['en', 'jp'],
      targetAudience: 'all users',
      capabilities: {
        webScraping: true,
        contentTranslation: true,
        interactiveGuides: true,
        videoTutorials: true,
        stepByStepWizards: true,
        troubleshooting: true,
        realTimeAssistance: true
      }
    }
    
    super('master-knowledge-agent', specialization)
  }

  protected buildEducationalPrompt(topic: string, content: string): string {
    return `
You are the master IT helpdesk expert who provides comprehensive guidance.

Topic: ${topic}

Create a guide that:
1. Provides an overview of all available IT services
2. Helps users identify which service they need
3. Explains how different services work together
4. Offers general troubleshooting strategies
5. Includes contact information and escalation paths

Make it a complete resource for users who aren't sure where to start.
`
  }

  protected parseEducationalContent(aiResponse: string, topic: string): EducationalContent {
    try {
      return JSON.parse(aiResponse)
    } catch {
      return {
        type: 'guide',
        title: 'IT Helpdesk Overview',
        titleJp: 'ITヘルプデスク概要',
        content: 'Your complete guide to IT services',
        contentJp: 'ITサービスの完全ガイド',
        difficulty: 'beginner',
        estimatedTime: 5,
        interactive: true
      }
    }
  }

  protected shouldHaveVisual(step: EducationalStep): boolean {
    return true
  }

  protected async generateVisualAid(step: EducationalStep): Promise<string> {
    return '/images/general/it-services-overview.png'
  }

  protected async generateValidation(step: EducationalStep): Promise<string> {
    return 'Verify user understands which service to use'
  }

  protected async generateTroubleshooting(step: EducationalStep): Promise<string[]> {
    return [
      'If unsure which service you need, contact IT support',
      'For urgent issues, call the IT helpdesk directly',
      'Check the knowledge base for common solutions'
    ]
  }

  protected analyzeFeedbackPatterns(feedback: any[]): Map<string, any> {
    return new Map<string, any>()
  }

  protected async adjustBehavior(patterns: Map<string, any>): Promise<void> {
    // Master agent adjusts based on overall system feedback
  }
}
