'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useLanguage } from '@/contexts/language-context'
import { supabase } from '@/lib/supabase'
import { 
  BookOpen, 
  HelpCircle, 
  Lightbulb,
  MessageCircle,
  Search,
  ExternalLink,
  Info
} from 'lucide-react'

interface HelpDeskKBIntegrationProps {
  serviceCategory?: string
  currentStep?: string
  className?: string
}

interface FAQ {
  id: string
  question_ja: string
  question_en: string
  answer_ja: string
  answer_en: string
  category: string
  confidence_score: number
}

interface QuickLink {
  title: string
  titleJp: string
  description: string
  descriptionJp: string
  url: string
  icon: React.ReactNode
}

export function HelpDeskKBIntegration({ 
  serviceCategory, 
  currentStep,
  className 
}: HelpDeskKBIntegrationProps) {
  const { language } = useLanguage()
  const [faqs, setFaqs] = useState<FAQ[]>([])
  const [quickLinks, setQuickLinks] = useState<QuickLink[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadContent()
    setupQuickLinks()
  }, [serviceCategory, currentStep])

  const loadContent = async () => {
    setIsLoading(true)
    try {
      // Load contextual FAQs
      const { data: faqData, error } = await supabase
        .from('chatbot_faq')
        .select('*')
        .eq('is_active', true)
        .gte('confidence_score', 0.7)
        .order('usage_count', { ascending: false })
        .limit(5)

      if (!error && faqData) {
        // Filter by service category if provided
        const filtered = serviceCategory 
          ? faqData.filter(faq => faq.category === serviceCategory)
          : faqData
        setFaqs(filtered)
      }
    } catch (error) {
      console.error('Error loading FAQs:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const setupQuickLinks = () => {
    const links: QuickLink[] = [
      {
        title: 'Knowledge Base',
        titleJp: 'ナレッジベース',
        description: 'Browse all help articles',
        descriptionJp: 'すべてのヘルプ記事を閲覧',
        url: '/knowledge-base',
        icon: <BookOpen className="h-4 w-4" />
      },
      {
        title: 'Video Tutorials',
        titleJp: 'ビデオチュートリアル',
        description: 'Watch step-by-step guides',
        descriptionJp: 'ステップバイステップガイドを視聴',
        url: '/tutorials',
        icon: <MessageCircle className="h-4 w-4" />
      },
      {
        title: 'Contact IT Support',
        titleJp: 'ITサポートに連絡',
        description: 'Get help from support team',
        descriptionJp: 'サポートチームからヘルプを取得',
        url: '/contact-support',
        icon: <HelpCircle className="h-4 w-4" />
      }
    ]
    setQuickLinks(links)
  }

  const handleFAQClick = async (faq: FAQ) => {
    // Increment usage count
    await supabase
      .from('chatbot_faq')
      .update({ usage_count: faq.usage_count + 1 })
      .eq('id', faq.id)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <Lightbulb className="h-5 w-5" />
          {language === 'ja' ? 'クイックヘルプ' : 'Quick Help'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="faqs" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="faqs">
              {language === 'ja' ? 'よくある質問' : 'FAQs'}
            </TabsTrigger>
            <TabsTrigger value="tips">
              {language === 'ja' ? 'ヒント' : 'Tips'}
            </TabsTrigger>
            <TabsTrigger value="links">
              {language === 'ja' ? 'リンク' : 'Links'}
            </TabsTrigger>
          </TabsList>

          {/* FAQs Tab */}
          <TabsContent value="faqs">
            <ScrollArea className="h-[250px]">
              {isLoading ? (
                <div className="text-center py-4 text-muted-foreground">
                  {language === 'ja' ? '読み込み中...' : 'Loading...'}
                </div>
              ) : faqs.length > 0 ? (
                <div className="space-y-3">
                  {faqs.map((faq) => (
                    <div
                      key={faq.id}
                      className="p-3 rounded-lg border hover:bg-accent cursor-pointer transition-colors"
                      onClick={() => handleFAQClick(faq)}
                    >
                      <h4 className="font-medium text-sm flex items-start gap-2">
                        <HelpCircle className="h-4 w-4 mt-0.5 text-primary" />
                        {language === 'ja' ? faq.question_ja : faq.question_en}
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1 ml-6">
                        {language === 'ja' ? faq.answer_ja : faq.answer_en}
                      </p>
                      <Badge variant="secondary" className="mt-2 ml-6 text-xs">
                        {Math.round(faq.confidence_score * 100)}% {language === 'ja' ? '関連性' : 'Relevant'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>
                    {language === 'ja' ? 'FAQがありません' : 'No FAQs Available'}
                  </AlertTitle>
                  <AlertDescription>
                    {language === 'ja' 
                      ? '現在のコンテキストに関連するFAQはありません。'
                      : 'No FAQs found for the current context.'}
                  </AlertDescription>
                </Alert>
              )}
            </ScrollArea>
          </TabsContent>

          {/* Tips Tab */}
          <TabsContent value="tips">
            <ScrollArea className="h-[250px]">
              <div className="space-y-3">
                <Alert>
                  <Lightbulb className="h-4 w-4" />
                  <AlertTitle>
                    {language === 'ja' ? 'フォーム入力のヒント' : 'Form Filling Tips'}
                  </AlertTitle>
                  <AlertDescription>
                    {language === 'ja' 
                      ? 'ユーザー名を入力すると、関連フィールドが自動的に入力されます。'
                      : 'Enter a user name to auto-populate related fields.'}
                  </AlertDescription>
                </Alert>
                
                <Alert>
                  <Search className="h-4 w-4" />
                  <AlertTitle>
                    {language === 'ja' ? '検索機能' : 'Search Feature'}
                  </AlertTitle>
                  <AlertDescription>
                    {language === 'ja' 
                      ? 'PC IDは検索可能です。部分的なIDでも検索できます。'
                      : 'PC IDs are searchable. You can search with partial IDs.'}
                  </AlertDescription>
                </Alert>

                {serviceCategory === 'SPO' && (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>
                      {language === 'ja' ? 'SharePointアクセス' : 'SharePoint Access'}
                    </AlertTitle>
                    <AlertDescription>
                      {language === 'ja' 
                        ? '複数のライブラリへのアクセスを一度にリクエストできます。'
                        : 'You can request access to multiple libraries at once.'}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Quick Links Tab */}
          <TabsContent value="links">
            <ScrollArea className="h-[250px]">
              <div className="space-y-2">
                {quickLinks.map((link, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => window.open(link.url, '_blank')}
                  >
                    {link.icon}
                    <div className="ml-2 text-left">
                      <div className="font-medium">
                        {language === 'ja' ? link.titleJp : link.title}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {language === 'ja' ? link.descriptionJp : link.description}
                      </div>
                    </div>
                    <ExternalLink className="h-4 w-4 ml-auto" />
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
