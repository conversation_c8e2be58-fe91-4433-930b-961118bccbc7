'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Smartphone, Mail, Shield, Copy, Check } from 'lucide-react';
import { useTranslation } from '@/lib/i18n/use-translation';
import { useMFA } from '@/lib/hooks/use-mfa';
import Image from 'next/image';

interface MFASetupProps {
  userId: string;
  userEmail: string;
  onComplete: () => void;
  onCancel?: () => void;
}

export function MFASetup({ userId, userEmail, onComplete, onCancel }: MFASetupProps) {
  const { t } = useTranslation();
  const { setupTOTP, verifyTOTPSetup, setupSMS, setupEmail } = useMFA();
  
  const [activeTab, setActiveTab] = useState<'totp' | 'sms' | 'email'>('totp');
  const [step, setStep] = useState<'choose' | 'setup' | 'verify' | 'backup'>('choose');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // TOTP specific state
  const [totpData, setTOTPData] = useState<{
    secret: string;
    qrCode: string;
    backupCodes: string[];
  } | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [copiedSecret, setCopiedSecret] = useState(false);
  const [copiedCodes, setCopiedCodes] = useState<Set<number>>(new Set());
  
  // SMS/Email specific state
  const [phoneNumber, setPhoneNumber] = useState('');
  const [emailVerified, setEmailVerified] = useState(false);

  const handleTOTPSetup = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await setupTOTP(userId, userEmail);
      setTOTPData(data);
      setStep('setup');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTOTPVerification = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError(t('mfa.errors.invalidCode'));
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const verified = await verifyTOTPSetup(userId, verificationCode);
      if (verified) {
        setStep('backup');
      } else {
        setError(t('mfa.errors.verificationFailed'));
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, index?: number) => {
    try {
      await navigator.clipboard.writeText(text);
      if (index !== undefined) {
        setCopiedCodes(new Set([...copiedCodes, index]));
        setTimeout(() => {
          setCopiedCodes(prev => {
            const next = new Set(prev);
            next.delete(index);
            return next;
          });
        }, 2000);
      } else {
        setCopiedSecret(true);
        setTimeout(() => setCopiedSecret(false), 2000);
      }
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const downloadBackupCodes = () => {
    if (!totpData) return;
    
    const content = `ITSync Backup Codes\n${new Date().toISOString()}\n\n${totpData.backupCodes.join('\n')}`;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'itsync-backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t('mfa.setup.title')}
        </CardTitle>
        <CardDescription>
          {t('mfa.setup.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === 'choose' && (
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="totp" className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                {t('mfa.methods.totp')}
              </TabsTrigger>
              <TabsTrigger value="sms" className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                {t('mfa.methods.sms')}
              </TabsTrigger>
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                {t('mfa.methods.email')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="totp" className="mt-6">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {t('mfa.totp.description')}
                </p>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Google Authenticator</li>
                  <li>• Microsoft Authenticator</li>
                  <li>• Authy</li>
                  <li>• 1Password</li>
                </ul>
                <Button 
                  onClick={handleTOTPSetup} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('common.loading')}
                    </>
                  ) : (
                    t('mfa.setup.continue')
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="sms" className="mt-6">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {t('mfa.sms.description')}
                </p>
                <div className="space-y-2">
                  <Label htmlFor="phone">{t('mfa.sms.phoneNumber')}</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="+81 90-1234-5678"
                  />
                </div>
                <Button 
                  onClick={() => {/* Handle SMS setup */}} 
                  disabled={loading || !phoneNumber}
                  className="w-full"
                >
                  {t('mfa.setup.continue')}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="email" className="mt-6">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {t('mfa.email.description')}
                </p>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm font-medium">{userEmail}</p>
                </div>
                <Button 
                  onClick={() => {/* Handle Email setup */}} 
                  disabled={loading}
                  className="w-full"
                >
                  {t('mfa.setup.continue')}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        )}

        {step === 'setup' && activeTab === 'totp' && totpData && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">
                {t('mfa.totp.scanQR')}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {t('mfa.totp.scanInstructions')}
              </p>
              <div className="bg-white p-4 rounded-lg inline-block">
                <Image
                  src={totpData.qrCode}
                  alt="QR Code"
                  width={200}
                  height={200}
                  className="mx-auto"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>{t('mfa.totp.manualEntry')}</Label>
              <div className="flex gap-2">
                <Input
                  value={totpData.secret}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  size="icon"
                  variant="outline"
                  onClick={() => copyToClipboard(totpData.secret)}
                >
                  {copiedSecret ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="code">{t('mfa.totp.enterCode')}</Label>
              <Input
                id="code"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={6}
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                placeholder="000000"
                className="text-center text-2xl font-mono"
              />
            </div>

            <Button
              onClick={handleTOTPVerification}
              disabled={loading || verificationCode.length !== 6}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('common.loading')}
                </>
              ) : (
                t('mfa.verify')
              )}
            </Button>
          </div>
        )}

        {step === 'backup' && totpData && (
          <div className="space-y-6">
            <Alert>
              <AlertDescription>
                {t('mfa.backup.warning')}
              </AlertDescription>
            </Alert>

            <div>
              <h3 className="text-lg font-semibold mb-2">
                {t('mfa.backup.title')}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {t('mfa.backup.description')}
              </p>

              <div className="grid grid-cols-2 gap-2">
                {totpData.backupCodes.map((code, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 p-2 bg-muted rounded-md"
                  >
                    <code className="font-mono text-sm flex-1">{code}</code>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-6 w-6"
                      onClick={() => copyToClipboard(code, index)}
                    >
                      {copiedCodes.has(index) ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={downloadBackupCodes}
                className="flex-1"
              >
                {t('mfa.backup.download')}
              </Button>
              <Button
                onClick={onComplete}
                className="flex-1"
              >
                {t('mfa.setup.complete')}
              </Button>
            </div>
          </div>
        )}

        {onCancel && step === 'choose' && (
          <div className="mt-6 pt-6 border-t">
            <Button
              variant="ghost"
              onClick={onCancel}
              className="w-full"
            >
              {t('common.cancel')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
