// Environment variable validation and type safety

const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key] || defaultValue
  
  if (!value) {
    console.warn(`Missing environment variable: ${key}`)
    return ''
  }
  
  return value
}

// Validate Supabase configuration
export const validateSupabaseConfig = () => {
  try {
    // Get the values directly from process.env to avoid throwing errors
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
    
    // Basic validation - just check that we have values
    if (!url || !anonKey) {
      return { url, anonKey, isValid: false, error: 'Missing Supabase configuration' }
    }
    
    // Simple format checks
    if (!url.includes('supabase.co')) {
      return { url, anonKey, isValid: false, error: 'Invalid Supabase URL format' }
    }
    
    // Anon key should be reasonably long
    if (anonKey.length < 20) {
      return { url, anon<PERSON>ey, isValid: false, error: 'Invalid Supabase anon key format' }
    }
    
    return { url, anonKey, isValid: true }
  } catch (error) {
    // Ensure error is properly typed for TypeScript
    const errorMessage = error instanceof Error ? error.message : 'Unknown error validating Supabase config';
    return { url: '', anonKey: '', isValid: false, error: errorMessage }
  }
}

// Environment configuration with defaults
export const config = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  },
  ai: {
    openaiKey: process.env.OPENAI_API_KEY || '',
    anthropicKey: process.env.ANTHROPIC_API_KEY || '',
  },
  app: {
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    name: process.env.NEXT_PUBLIC_SITE_NAME || 'ITSync',
    env: process.env.NODE_ENV || 'development',
  },
}

// Check if running in development without proper config
export const isDevelopmentWithoutConfig = () => {
  return config.app.env === 'development' && 
         (!config.supabase.url || 
          !config.supabase.url.includes('supabase.co') ||
          !config.supabase.anonKey ||
          config.supabase.anonKey.length < 20)
}
