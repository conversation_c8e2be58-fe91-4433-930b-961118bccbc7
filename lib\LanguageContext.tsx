"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'ja';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, string>) => string;
}

const translations: Record<Language, Record<string, string>> = {
  en: {
    // Common UI elements
    'common.submit': 'Submit',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.search': 'Search',
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.back': 'Back',
    'common.help': 'Help',
    
    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.requests': 'Requests',
    'nav.knowledge_base': 'Knowledge Base',
    'nav.admin': 'Admin',
    'nav.settings': 'Settings',
    'nav.logout': 'Logout',
    
    // Auth
    'auth.login': 'Login',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.forgot_password': 'Forgot Password?',
    'auth.reset_password': 'Reset Password',
    
    // Knowledge Base
    'kb.search': 'Search Knowledge Base',
    'kb.categories': 'Categories',
    'kb.recent': 'Recent Articles',
    'kb.popular': 'Popular Articles',
    
    // Requests
    'request.new': 'New Request',
    'request.my_requests': 'My Requests',
    'request.all_requests': 'All Requests',
    'request.status': 'Status',
    'request.priority': 'Priority',
    'request.details': 'Request Details',
    
    // Status
    'status.pending': 'Pending',
    'status.in_progress': 'In Progress',
    'status.completed': 'Completed',
    'status.cancelled': 'Cancelled',
    
    // Priority
    'priority.low': 'Low',
    'priority.medium': 'Medium',
    'priority.high': 'High',
    'priority.urgent': 'Urgent',
    
    // Form validation
    'validation.required': 'This field is required',
    'validation.email': 'Please enter a valid email address',
    'validation.min_length': 'Must be at least {{length}} characters',
    'validation.max_length': 'Must be at most {{length}} characters',
  },
  ja: {
    // Common UI elements
    'common.submit': '送信',
    'common.cancel': 'キャンセル',
    'common.save': '保存',
    'common.delete': '削除',
    'common.edit': '編集',
    'common.search': '検索',
    'common.loading': '読み込み中...',
    'common.error': 'エラーが発生しました',
    'common.success': '成功',
    'common.next': '次へ',
    'common.previous': '前へ',
    'common.back': '戻る',
    'common.help': 'ヘルプ',
    
    // Navigation
    'nav.dashboard': 'ダッシュボード',
    'nav.requests': 'リクエスト',
    'nav.knowledge_base': 'ナレッジベース',
    'nav.admin': '管理',
    'nav.settings': '設定',
    'nav.logout': 'ログアウト',
    
    // Auth
    'auth.login': 'ログイン',
    'auth.email': 'メールアドレス',
    'auth.password': 'パスワード',
    'auth.forgot_password': 'パスワードをお忘れですか？',
    'auth.reset_password': 'パスワードをリセット',
    
    // Knowledge Base
    'kb.search': 'ナレッジベースを検索',
    'kb.categories': 'カテゴリー',
    'kb.recent': '最近の記事',
    'kb.popular': '人気の記事',
    
    // Requests
    'request.new': '新規リクエスト',
    'request.my_requests': '自分のリクエスト',
    'request.all_requests': 'すべてのリクエスト',
    'request.status': 'ステータス',
    'request.priority': '優先度',
    'request.details': 'リクエスト詳細',
    
    // Status
    'status.pending': '保留中',
    'status.in_progress': '処理中',
    'status.completed': '完了',
    'status.cancelled': 'キャンセル済み',
    
    // Priority
    'priority.low': '低',
    'priority.medium': '中',
    'priority.high': '高',
    'priority.urgent': '緊急',
    
    // Form validation
    'validation.required': 'この項目は必須です',
    'validation.email': '有効なメールアドレスを入力してください',
    'validation.min_length': '{{length}}文字以上で入力してください',
    'validation.max_length': '{{length}}文字以下で入力してください',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>('ja');

  useEffect(() => {
    // Check for stored language preference
    const storedLanguage = localStorage.getItem('language') as Language;
    if (storedLanguage && (storedLanguage === 'en' || storedLanguage === 'ja')) {
      setLanguage(storedLanguage);
    }
  }, []);

  const changeLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
  };

  const t = (key: string, params?: Record<string, string>): string => {
    const translation = translations[language][key] || translations.en[key] || key;
    
    if (params) {
      return Object.entries(params).reduce((acc, [paramKey, paramValue]) => {
        return acc.replace(`{{${paramKey}}}`, paramValue);
      }, translation);
    }
    
    return translation;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

export default LanguageContext;
