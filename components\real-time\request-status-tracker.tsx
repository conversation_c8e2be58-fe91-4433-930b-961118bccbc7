"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Clock, CheckCircle2, XCircle, AlertCircle, Loader2 } from 'lucide-react';
import { enhancedRealtimeService } from '@/lib/services/enhanced-realtime-service';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

type RequestForm = Database['public']['Tables']['request_forms']['Row'];

interface RequestStatusTrackerProps {
  requestId: string;
  initialData?: RequestForm;
}

const statusConfig = {
  draft: { label: '下書き / Draft', color: 'bg-gray-500', icon: <Clock className="h-4 w-4" /> },
  pending: { label: '保留中 / Pending', color: 'bg-yellow-500', icon: <AlertCircle className="h-4 w-4" /> },
  processing: { label: '処理中 / Processing', color: 'bg-blue-500', icon: <Loader2 className="h-4 w-4 animate-spin" /> },
  approved: { label: '承認済み / Approved', color: 'bg-green-500', icon: <CheckCircle2 className="h-4 w-4" /> },
  rejected: { label: '却下 / Rejected', color: 'bg-red-500', icon: <XCircle className="h-4 w-4" /> },
  completed: { label: '完了 / Completed', color: 'bg-green-600', icon: <CheckCircle2 className="h-4 w-4" /> },
};

const statusOrder = ['draft', 'pending', 'processing', 'approved', 'completed'];

export function RequestStatusTracker({ requestId, initialData }: RequestStatusTrackerProps) {
  const [requestData, setRequestData] = useState<RequestForm | null>(initialData || null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    const subscriptionKey = enhancedRealtimeService.subscribeToRequestStatus(
      requestId,
      (payload: RealtimePostgresChangesPayload<RequestForm>) => {
        if (payload.eventType === 'UPDATE' && payload.new) {
          setRequestData(payload.new);
          setLastUpdate(new Date());
        }
      }
    );

    return () => {
      enhancedRealtimeService.unsubscribe(subscriptionKey, `request-status-${requestId}`);
    };
  }, [requestId]);

  const getProgressPercentage = () => {
    if (!requestData) return 0;
    const currentIndex = statusOrder.indexOf(requestData.status);
    if (currentIndex === -1) return 0;
    return ((currentIndex + 1) / statusOrder.length) * 100;
  };

  if (!requestData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">読み込み中... / Loading...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentStatus = statusConfig[requestData.status as keyof typeof statusConfig] || statusConfig.draft;

  return (
    <Card>
      <CardHeader>
        <CardTitle>リクエストステータス / Request Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {currentStatus.icon}
            <span className="font-medium">{currentStatus.label}</span>
          </div>
          <Badge className={currentStatus.color}>
            {requestData.status.toUpperCase()}
          </Badge>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>進捗 / Progress</span>
            <span>{Math.round(getProgressPercentage())}%</span>
          </div>
          <Progress value={getProgressPercentage()} className="h-2" />
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">作成日 / Created</p>
            <p className="font-medium">
              {new Date(requestData.created_at).toLocaleString('ja-JP')}
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">最終更新 / Last Update</p>
            <p className="font-medium">
              {lastUpdate.toLocaleString('ja-JP')}
            </p>
          </div>
        </div>

        {requestData.completed_at && (
          <div className="pt-2 border-t">
            <p className="text-sm text-muted-foreground">完了日 / Completed</p>
            <p className="text-sm font-medium">
              {new Date(requestData.completed_at).toLocaleString('ja-JP')}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
