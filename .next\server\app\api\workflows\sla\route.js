"use strict";(()=>{var e={};e.id=8367,e.ids=[8367],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76584:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>c,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>u});var a=t(42706),o=t(28203),n=t(45994),i=t(39187);async function u(e){try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let a=e.nextUrl.searchParams.get("type")||"status";if("status"===a){let{data:e,error:t}=await r.from("sla_tracking").select(`
          *,
          workflow_instance:workflow_instances(
            *,
            request:request_forms(*)
          ),
          sla_definition:sla_definitions(*)
        `).order("created_at",{ascending:!1});if(t)return i.NextResponse.json({error:t.message},{status:500});let s=e.length,a=e.filter(e=>"on_track"===e.status).length,o=e.filter(e=>"at_risk"===e.status).length,n=e.filter(e=>"breached"===e.status).length;return i.NextResponse.json({summary:{total:s,onTrack:a,atRisk:o,breached:n,complianceRate:s>0?(a/s*100).toFixed(2):0},data:e})}if("at-risk"===a){let{data:e,error:t}=await r.from("sla_tracking").select(`
          *,
          workflow_instance:workflow_instances(
            *,
            request:request_forms(*),
            tasks:workflow_tasks(*)
          ),
          sla_definition:sla_definitions(*)
        `).eq("status","at_risk").order("target_resolution_date",{ascending:!0});if(t)return i.NextResponse.json({error:t.message},{status:500});return i.NextResponse.json({data:e})}return i.NextResponse.json({error:"Invalid type parameter"},{status:400})}catch(e){return console.error("Error fetching SLA data:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/sla/route",pathname:"/api/workflows/sla",filename:"route",bundlePath:"app/api/workflows/sla/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:c}=l;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(76584));module.exports=s})();