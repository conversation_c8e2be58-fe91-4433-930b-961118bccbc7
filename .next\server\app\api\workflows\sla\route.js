(()=>{var e={};e.id=8367,e.ids=[8367],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},94877:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>l});var a=t(42706),o=t(28203),n=t(45994),i=t(39187),u=t(61487);async function l(e){try{let r=await (0,u.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let a=e.nextUrl.searchParams.get("type")||"status";if("status"===a){let{data:e,error:t}=await r.from("sla_tracking").select(`
          *,
          workflow_instance:workflow_instances(
            *,
            request:request_forms(*)
          ),
          sla_definition:sla_definitions(*)
        `).order("created_at",{ascending:!1});if(t)return i.NextResponse.json({error:t.message},{status:500});let s=e.length,a=e.filter(e=>"on_track"===e.status).length,o=e.filter(e=>"at_risk"===e.status).length,n=e.filter(e=>"breached"===e.status).length;return i.NextResponse.json({summary:{total:s,onTrack:a,atRisk:o,breached:n,complianceRate:s>0?(a/s*100).toFixed(2):0},data:e})}if("at-risk"===a){let{data:e,error:t}=await r.from("sla_tracking").select(`
          *,
          workflow_instance:workflow_instances(
            *,
            request:request_forms(*),
            tasks:workflow_tasks(*)
          ),
          sla_definition:sla_definitions(*)
        `).eq("status","at_risk").order("target_resolution_date",{ascending:!0});if(t)return i.NextResponse.json({error:t.message},{status:500});return i.NextResponse.json({data:e})}return i.NextResponse.json({error:"Invalid type parameter"},{status:400})}catch(e){return console.error("Error fetching SLA data:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/sla/route",pathname:"/api/workflows/sla",filename:"route",bundlePath:"app/api/workflows/sla/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:f}=p;function w(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},96487:()=>{},78335:()=>{},61487:(e,r,t)=>{"use strict";t.d(r,{U:()=>o});var s=t(49064),a=t(44512);let o=()=>{let e=(0,a.UL)();return(0,s.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064],()=>t(94877));module.exports=s})();