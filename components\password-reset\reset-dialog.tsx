'use client'

import { useState } from 'react'
import { useSupabase } from '@/components/providers/supabase-provider'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { UserMultiSelect } from '@/components/ui/user-multi-select'
import { useDepartmentAccess } from '@/lib/auth/hooks'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Info } from 'lucide-react'

interface PasswordResetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function PasswordResetDialog({ open, onOpenChange, onSuccess }: PasswordResetDialogProps) {
  const [loading, setLoading] = useState(false)
  const [passwordType, setPasswordType] = useState<string>('M365 Office')
  const [reason, setReason] = useState('')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const { supabase, user } = useSupabase()
  const { departmentId } = useDepartmentAccess()

  const handleSubmit = async () => {
    if (!reason || selectedUsers.length === 0) {
      toast.error('Please fill in all required fields')
      return
    }

    setLoading(true)
    try {
      // Create a new request form
      const { data: requestForm, error: formError } = await supabase
        .from('request_forms')
        .insert({
          requester_id: user?.id,
          title: \`Password Reset Request - \${passwordType}\`,
          description: reason,
          priority: 'medium',
          status: 'submitted',
          submitted_at: new Date().toISOString()
        })
        .select()
        .single()

      if (formError) throw formError

      // Create request items for each selected user
      const requestItems = selectedUsers.map(userId => ({
        request_form_id: requestForm.id,
        service_category_id: null,
        affected_user_id: userId,
        action_type: 'reset',
        target_resource: passwordType,
        service_type: 'Password Reset',
        request_details: {
          password_type: passwordType,
          reason: reason
        },
        parameters: {
          password_type: passwordType
        },
        affected_users: [userId],
        status: 'pending'
      }))

      const { error: itemsError } = await supabase
        .from('request_items')
        .insert(requestItems)

      if (itemsError) throw itemsError

      toast.success('Password reset request submitted successfully')
      onSuccess()
      resetForm()
    } catch (error) {
      console.error('Error submitting password reset request:', error)
      toast.error('Failed to submit request')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setPasswordType('M365 Office')
    setReason('')
    setSelectedUsers([])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>New Password Reset Request</DialogTitle>
          <DialogDescription>
            Submit a request to reset user passwords
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="password-type">Password Type</Label>
            <Select value={passwordType} onValueChange={setPasswordType}>
              <SelectTrigger id="password-type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="M365 Office">M365 Office</SelectItem>
                <SelectItem value="Multi Factor Authenticator">Multi Factor Authenticator</SelectItem>
                <SelectItem value="Windows">Windows Password</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              {passwordType === 'M365 Office' && 
                'This will reset the user\'s Microsoft 365 password. A temporary password will be generated and must be changed on first login.'
              }
              {passwordType === 'Multi Factor Authenticator' && 
                'This will reset the user\'s multi-factor authentication settings. They will need to reconfigure their authenticator app.'
              }
              {passwordType === 'Windows' && 
                'This will reset the user\'s Windows login password. Access to the physical machine may be required.'
              }
            </AlertDescription>
          </Alert>

          <div className="grid gap-2">
            <Label htmlFor="reason">Reason for Reset *</Label>
            <Textarea
              id="reason"
              placeholder="Provide justification for password reset (e.g., forgotten password, security breach)"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="users">Affected Users *</Label>
            <UserMultiSelect
              selectedUsers={selectedUsers}
              onSelectionChange={setSelectedUsers}
              departmentId={departmentId}
              placeholder="Select users who need password reset"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Submitting...' : 'Submit Request'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
