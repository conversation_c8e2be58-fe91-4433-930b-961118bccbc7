/**
 * Integration tests for Health Check API
 * Tests health endpoints, monitoring, and system status
 */

import { NextRequest } from 'next/server'
import { GET } from '@/app/api/health/route'
import { GET as getSimpleHealth } from '@/app/api/health/simple/route'

// Mock external dependencies
jest.mock('@/lib/supabase/server')

describe('/api/health', () => {
  let mockRequest: NextRequest

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Create mock request
    mockRequest = new NextRequest('http://localhost:3000/api/health')
    
    // Reset environment variables
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
    process.env.OPENAI_API_KEY = 'sk-test-openai-key'
    process.env.ANTHROPIC_API_KEY = 'sk-ant-test-anthropic-key'
  })

  describe('GET /api/health', () => {
    it('should return healthy status when all systems are operational', async () => {
      // Mock successful database connection
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      // Mock successful external API calls
      global.fetch = jest.fn()
        .mockResolvedValueOnce({ ok: true, status: 200 }) // Supabase API
        .mockResolvedValueOnce({ ok: true, status: 200 }) // OpenAI API
        .mockResolvedValueOnce({ ok: true, status: 200 }) // Anthropic API

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.status).toBe('healthy')
      expect(data.checks.database.status).toBe('pass')
      expect(data.checks.ai_services.status).toBe('pass')
      expect(data.checks.external_apis.status).toBe('pass')
      expect(data.timestamp).toBeDefined()
      expect(data.version).toBeDefined()
      expect(data.metadata.environment).toBe('test')
    })

    it('should return degraded status when some systems have warnings', async () => {
      // Mock slow database response
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => {
              // Simulate slow response
              return new Promise(resolve => {
                setTimeout(() => {
                  resolve({ data: [], error: null })
                }, 1500) // 1.5 seconds - should trigger warning
              })
            })
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      // Mock external API calls
      global.fetch = jest.fn()
        .mockResolvedValueOnce({ ok: true, status: 200 })

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.status).toBe('degraded')
      expect(data.checks.database.status).toBe('warn')
      expect(data.checks.database.message).toContain('Slow database response')
    })

    it('should return unhealthy status when critical systems fail', async () => {
      // Mock database connection failure
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ 
              data: null, 
              error: { message: 'Connection failed' } 
            }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.status).toBe('unhealthy')
      expect(data.checks.database.status).toBe('fail')
      expect(data.checks.database.message).toBe('Database query failed')
    })

    it('should handle missing environment variables', async () => {
      // Remove required environment variables
      delete process.env.NEXT_PUBLIC_SUPABASE_URL
      delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.status).toBe('unhealthy')
      expect(data.checks.external_apis.status).toBe('fail')
    })

    it('should check AI services availability', async () => {
      // Mock database success
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      // Mock AI API failures
      global.fetch = jest.fn()
        .mockResolvedValueOnce({ ok: true, status: 200 }) // Supabase API
        .mockRejectedValueOnce(new Error('OpenAI API timeout')) // OpenAI failure
        .mockRejectedValueOnce(new Error('Anthropic API timeout')) // Anthropic failure

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(data.checks.ai_services.status).toBe('warn')
      expect(data.checks.ai_services.details.openai).toBe('error')
      expect(data.checks.ai_services.details.anthropic).toBe('error')
    })

    it('should include system resource information', async () => {
      // Mock successful checks
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      global.fetch = jest.fn().mockResolvedValue({ ok: true, status: 200 })

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(data.checks.memory).toBeDefined()
      expect(data.checks.memory.details.used_mb).toBeDefined()
      expect(data.checks.memory.details.total_mb).toBeDefined()
      expect(data.checks.memory.details.usage_percent).toBeDefined()
    })

    it('should handle unexpected errors gracefully', async () => {
      // Mock an unexpected error
      const { createClient } = require('@/lib/supabase/server')
      createClient.mockImplementation(() => {
        throw new Error('Unexpected error')
      })

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.status).toBe('unhealthy')
      expect(data.error).toBe('Unexpected error')
    })

    it('should include proper cache headers', async () => {
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      global.fetch = jest.fn().mockResolvedValue({ ok: true, status: 200 })

      const response = await GET(mockRequest)

      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate')
      expect(response.headers.get('Content-Type')).toBe('application/json')
    })
  })

  describe('GET /api/health/simple', () => {
    it('should return simple OK response', async () => {
      const response = await getSimpleHealth()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.status).toBe('ok')
      expect(data.timestamp).toBeDefined()
    })

    it('should include proper cache headers', async () => {
      const response = await getSimpleHealth()

      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate')
    })

    it('should support HEAD requests', async () => {
      // Note: This would need to be tested with actual HEAD request in real integration test
      const response = await getSimpleHealth()
      expect(response.status).toBe(200)
    })
  })

  describe('Health Check Response Format', () => {
    it('should include all required fields in health response', async () => {
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      global.fetch = jest.fn().mockResolvedValue({ ok: true, status: 200 })

      const response = await GET(mockRequest)
      const data = await response.json()

      // Verify required top-level fields
      expect(data).toHaveProperty('status')
      expect(data).toHaveProperty('timestamp')
      expect(data).toHaveProperty('version')
      expect(data).toHaveProperty('uptime')
      expect(data).toHaveProperty('checks')
      expect(data).toHaveProperty('metadata')

      // Verify checks structure
      expect(data.checks).toHaveProperty('database')
      expect(data.checks).toHaveProperty('ai_services')
      expect(data.checks).toHaveProperty('redis')
      expect(data.checks).toHaveProperty('external_apis')
      expect(data.checks).toHaveProperty('disk_space')
      expect(data.checks).toHaveProperty('memory')

      // Verify each check has required fields
      Object.values(data.checks).forEach((check: any) => {
        expect(check).toHaveProperty('status')
        expect(['pass', 'warn', 'fail']).toContain(check.status)
      })

      // Verify metadata structure
      expect(data.metadata).toHaveProperty('environment')
      expect(data.metadata).toHaveProperty('node_version')
      expect(data.metadata).toHaveProperty('next_version')
    })

    it('should have consistent timestamp format', async () => {
      const response = await getSimpleHealth()
      const data = await response.json()

      expect(data.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
    })
  })

  describe('Performance', () => {
    it('should respond within reasonable time', async () => {
      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      global.fetch = jest.fn().mockResolvedValue({ ok: true, status: 200 })

      const startTime = Date.now()
      await GET(mockRequest)
      const endTime = Date.now()

      const responseTime = endTime - startTime
      expect(responseTime).toBeLessThan(5000) // Should respond within 5 seconds
    })

    it('should handle timeout scenarios', async () => {
      // Mock slow external API
      global.fetch = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ ok: true }), 10000))
      )

      const mockSupabase = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }

      const { createClient } = require('@/lib/supabase/server')
      createClient.mockReturnValue(mockSupabase)

      const response = await GET(mockRequest)
      const data = await response.json()

      // Should still return a response even if external calls timeout
      expect(response.status).toBeGreaterThanOrEqual(200)
      expect(data.status).toBeDefined()
    })
  })
})
