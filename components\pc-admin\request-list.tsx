'use client'

import { format } from 'date-fns'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Eye, CheckCircle, XCircle, Clock } from 'lucide-react'
import { PcAdminRequest } from '@/app/dashboard/it-helpdesk/pc-admin-requests/page'

interface PcAdminRequestListProps {
  requests: PcAdminRequest[]
  onStatusUpdate: (requestId: string, status: string) => void
  getStatusBadge: (status: string) => JSX.Element
  getActionTypeBadge: (actionType: string) => JSX.Element
  getUrgencyBadge: (urgency?: string) => JSX.Element | null
}

export function PcAdminRequestList({
  requests,
  onStatusUpdate,
  getStatusBadge,
  getActionTypeBadge,
  getUrgencyBadge,
}: PcAdminRequestListProps) {
  if (requests.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No PC administrative requests found.
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {requests.map((request) => (
        <Card key={request.id} className="p-4">
          <div className="flex items-start justify-between">
            <div className="space-y-3 flex-1">
              <div className="flex items-center gap-2">
                {getActionTypeBadge(request.action_type)}
                {getStatusBadge(request.status)}
                {getUrgencyBadge(request.parameters.urgency)}
              </div>
              
              <div className="grid gap-2">
                <div className="flex items-center gap-2 text-sm">
                  <span className="font-medium">PC ID:</span>
                  <span className="font-mono">{request.parameters.pc_id}</span>
                </div>
                
                {request.parameters.software_name && (
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">Software:</span>
                    <span>{request.parameters.software_name}</span>
                  </div>
                )}
                
                <div className="text-sm">
                  <span className="font-medium">Reason:</span>
                  <p className="mt-1 text-muted-foreground">{request.parameters.reason}</p>
                </div>
                
                <div className="text-sm">
                  <span className="font-medium">Affected Users:</span>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {request.affected_staff?.map((staff) => (
                      <Badge key={staff.id} variant="secondary">
                        {staff.name_en} ({staff.staff_id})
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>Created: {format(new Date(request.created_at), 'PPP')}</span>
                  {request.completed_at && (
                    <span>Completed: {format(new Date(request.completed_at), 'PPP')}</span>
                  )}
                </div>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onStatusUpdate(request.id, 'processing')}
                  disabled={request.status !== 'pending'}
                >
                  <Clock className="mr-2 h-4 w-4" />
                  Mark as Processing
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onStatusUpdate(request.id, 'completed')}
                  disabled={request.status === 'completed'}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Mark as Completed
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onStatusUpdate(request.id, 'failed')}
                  disabled={request.status === 'completed' || request.status === 'failed'}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Mark as Failed
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Card>
      ))}
    </div>
  )
}
