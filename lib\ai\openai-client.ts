/**
 * OpenAI Client Configuration
 * Centralized OpenAI API client setup
 */

// Mock OpenAI client for development
// In production, replace with actual OpenAI SDK

interface OpenAIMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

interface OpenAICompletionRequest {
  model: string
  messages: OpenAIMessage[]
  max_tokens?: number
  temperature?: number
  top_p?: number
  frequency_penalty?: number
  presence_penalty?: number
}

interface OpenAIUsage {
  prompt_tokens: number
  completion_tokens: number
  total_tokens: number
}

interface OpenAIChoice {
  index: number
  message: OpenAIMessage
  finish_reason: string
}

interface OpenAICompletionResponse {
  id: string
  object: string
  created: number
  model: string
  choices: OpenAIChoice[]
  usage: OpenAIUsage
}

class MockOpenAIClient {
  chat = {
    completions: {
      create: async (request: OpenAICompletionRequest): Promise<OpenAICompletionResponse> => {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500))
        
        // Simulate occasional failures (5% rate)
        if (Math.random() < 0.05) {
          throw new Error('OpenAI API Error: Rate limit exceeded')
        }

        // Generate mock response
        const promptTokens = Math.floor(request.messages[0].content.length / 4)
        const completionTokens = Math.floor(Math.random() * 500) + 50
        
        return {
          id: `chatcmpl-${Math.random().toString(36).substr(2, 9)}`,
          object: 'chat.completion',
          created: Math.floor(Date.now() / 1000),
          model: request.model,
          choices: [{
            index: 0,
            message: {
              role: 'assistant',
              content: `Mock response from ${request.model} for: "${request.messages[0].content.substring(0, 50)}..."`
            },
            finish_reason: 'stop'
          }],
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: promptTokens + completionTokens
          }
        }
      }
    }
  }
}

// Export mock client for development
export const openaiClient = new MockOpenAIClient()

// In production, use this instead:
// import OpenAI from 'openai'
// export const openaiClient = new OpenAI({
//   apiKey: process.env.OPENAI_API_KEY,
// })
