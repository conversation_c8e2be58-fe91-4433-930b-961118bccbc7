"use strict";(()=>{var e={};e.id=5501,e.ids=[5501],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},94735:e=>{e.exports=require("events")},81630:e=>{e.exports=require("http")},55591:e=>{e.exports=require("https")},28354:e=>{e.exports=require("util")},74075:e=>{e.exports=require("zlib")},75780:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(42706),o=t(28203),n=t(45994),u=t(33406),i=t(44512),p=t(39187),l=t(44053);async function c(){try{let e=(0,u.createRouteHandlerClient)({cookies:i.UL}),{data:{session:r}}=await e.auth.getSession();if(!r)return p.NextResponse.json({error:"Unauthorized"},{status:401});let t=new l.q(e),a=await t.createChallenge(r.user.id);return p.NextResponse.json(a)}catch(e){return console.error("MFA challenge error:",e),p.NextResponse.json({error:e.message||"Failed to create MFA challenge"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/mfa/challenge/route",pathname:"/api/auth/mfa/challenge",filename:"route",bundlePath:"app/api/auth/mfa/challenge/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\challenge\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:g}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8096,2076],()=>t(75780));module.exports=a})();