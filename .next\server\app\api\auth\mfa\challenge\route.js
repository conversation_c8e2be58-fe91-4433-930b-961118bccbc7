(()=>{var e={};e.id=5501,e.ids=[5501],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},29021:e=>{"use strict";e.exports=require("fs")},27910:e=>{"use strict";e.exports=require("stream")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},90171:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{POST:()=>p});var s=r(42706),i=r(28203),o=r(45994),n=r(33406),u=r(44512),c=r(39187),d=r(44053);async function p(){try{let e=(0,n.createRouteHandlerClient)({cookies:u.UL}),{data:{session:t}}=await e.auth.getSession();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let r=new d.q(e),a=await r.createChallenge(t.user.id);return c.NextResponse.json(a)}catch(e){return console.error("MFA challenge error:",e),c.NextResponse.json({error:e.message||"Failed to create MFA challenge"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/mfa/challenge/route",pathname:"/api/auth/mfa/challenge",filename:"route",bundlePath:"app/api/auth/mfa/challenge/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\challenge\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:m}=l;function _(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},96487:()=>{},78335:()=>{},44053:(e,t,r)=>{"use strict";r.d(t,{q:()=>p});var a=r(46063),s=r(12959),i=r(55511);let o=(0,r(28354).promisify)(i.scrypt),n="aes-256-gcm",u=async()=>{let e=process.env.ENCRYPTION_KEY||process.env.SUPABASE_ANON_KEY;if(!e)throw Error("Encryption key not configured");let t=Buffer.from("itsync-mfa-salt-v1","utf8");return await o(e,t,32)};async function c(e){try{let t=await u(),r=(0,i.randomBytes)(16),a=(0,i.createCipheriv)(n,t,r),s=Buffer.concat([a.update(e,"utf8"),a.final()]),o=a.getAuthTag();return Buffer.concat([r,o,s]).toString("base64")}catch(e){throw Error(`Encryption failed: ${e.message}`)}}async function d(e){try{let t=await u(),r=Buffer.from(e,"base64"),a=r.slice(0,16),s=r.slice(16,32),o=r.slice(32),c=(0,i.createDecipheriv)(n,t,a);return c.setAuthTag(s),Buffer.concat([c.update(o),c.final()]).toString("utf8")}catch(e){throw Error(`Decryption failed: ${e.message}`)}}class p{constructor(e){this.MAX_ATTEMPTS=5,this.SESSION_DURATION=6e5,this.CODE_LENGTH=6,this.BACKUP_CODE_LENGTH=8,this.BACKUP_CODE_COUNT=10,this.supabase=e}async setupTOTP(e,t){try{let r=a.generateSecret({name:`ITSync (${t})`,issuer:"ITSync",length:32}),i=a.otpauthURL({secret:r.ascii,label:`ITSync:${t}`,issuer:"ITSync",algorithm:"sha256"}),o=await s.toDataURL(i),n=this.generateBackupCodes(),u=await c(r.base32),d=await Promise.all(n.map(e=>c(e)));return await this.supabase.from("mfa_configurations").upsert({user_id:e,method:"totp",secret_encrypted:u,backup_codes:d,is_verified:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),await this.logMFAEvent(e,"setup_initiated","totp",!0),{secret:r.base32,qrCode:o,backupCodes:n}}catch(t){throw await this.logMFAEvent(e,"setup_initiated","totp",!1,t.message),t}}async verifyTOTPSetup(e,t){try{let{data:r}=await this.supabase.from("mfa_configurations").select("*").eq("user_id",e).eq("method","totp").single();if(!r||!r.secret_encrypted)throw Error("TOTP configuration not found");let s=await d(r.secret_encrypted);if(a.totp.verify({secret:s,encoding:"base32",token:t,algorithm:"sha256",window:2}))return await this.supabase.from("mfa_configurations").update({is_verified:!0,is_primary:!0,verified_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",r.id),await this.supabase.from("profiles").update({mfa_enabled:!0,updated_at:new Date().toISOString()}).eq("auth_id",e),await this.logMFAEvent(e,"setup_completed","totp",!0),!0;return await this.logMFAEvent(e,"setup_failed","totp",!1,"Invalid code"),!1}catch(t){throw await this.logMFAEvent(e,"setup_failed","totp",!1,t.message),t}}async createChallenge(e){try{let{data:t}=await this.supabase.from("mfa_configurations").select("method, is_primary").eq("user_id",e).eq("is_verified",!0);if(!t||0===t.length)throw Error("No MFA methods configured");let r=this.generateSessionToken(),a=new Date(Date.now()+this.SESSION_DURATION);return await this.supabase.from("mfa_sessions").insert({user_id:e,session_token:r,method:t.find(e=>e.is_primary)?.method||t[0].method,expires_at:a.toISOString(),attempts:0,is_verified:!1}),{sessionToken:r,methods:t.map(e=>({method:e.method,isPrimary:e.is_primary}))}}catch(e){throw e}}async verifyCode(e,t){try{let{data:r}=await this.supabase.from("mfa_sessions").select("*").eq("session_token",e).single();if(!r)throw Error("Invalid session");if(new Date(r.expires_at)<new Date)throw Error("Session expired");if(r.attempts>=this.MAX_ATTEMPTS)return await this.logMFAEvent(r.user_id,"verify_failed",r.method,!1,"Locked out"),{success:!1,lockedOut:!0};let a=!1;if("totp"===r.method?a=await this.verifyTOTP(r.user_id,t):("sms"===r.method||"email"===r.method)&&(a=await this.verifyOTP(r,t)),a)return await this.supabase.from("mfa_sessions").update({is_verified:!0}).eq("id",r.id),await this.supabase.from("mfa_configurations").update({last_used_at:new Date().toISOString()}).eq("user_id",r.user_id).eq("method",r.method),await this.logMFAEvent(r.user_id,"verify_success",r.method,!0),{success:!0};{let e=r.attempts+1;return await this.supabase.from("mfa_sessions").update({attempts:e}).eq("id",r.id),await this.logMFAEvent(r.user_id,"verify_failed",r.method,!1),{success:!1,attemptsRemaining:this.MAX_ATTEMPTS-e}}}catch(e){throw e}}async verifyTOTP(e,t){let{data:r}=await this.supabase.from("mfa_configurations").select("secret_encrypted").eq("user_id",e).eq("method","totp").eq("is_verified",!0).single();if(!r||!r.secret_encrypted)return!1;let s=await d(r.secret_encrypted);return a.totp.verify({secret:s,encoding:"base32",token:t,algorithm:"sha256",window:2})}async verifyOTP(e,t){return!!e.challenge_code&&await d(e.challenge_code)===t}generateBackupCodes(){let e=[];for(let t=0;t<this.BACKUP_CODE_COUNT;t++)e.push(this.generateRandomCode(this.BACKUP_CODE_LENGTH));return e}generateRandomCode(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="";for(let a=0;a<e;a++)r+=t.charAt(Math.floor(Math.random()*t.length));return r}generateSessionToken(){return`mfa_${Date.now()}_${this.generateRandomCode(32)}`}async logMFAEvent(e,t,r,a,s,i){try{await this.supabase.from("mfa_audit_logs").insert({user_id:e,event_type:t,method:r,success:a,error_message:s,metadata:i,created_at:new Date().toISOString()})}catch(e){console.error("Failed to log MFA event:",e)}}async checkMFAStatus(e){let{data:t}=await this.supabase.from("profiles").select("mfa_enabled, require_mfa_for_role").eq("auth_id",e).single(),{data:r}=await this.supabase.from("mfa_configurations").select("method").eq("user_id",e).eq("is_verified",!0);return{enabled:t?.mfa_enabled||!1,required:t?.require_mfa_for_role||!1,methods:r?.map(e=>e.method)||[]}}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5994,5452,4512,3406,1073],()=>r(90171));module.exports=a})();