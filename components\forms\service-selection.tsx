'use client'

import { useState, useEffect } from 'react'
import { ServiceCategory } from '@/lib/request-types'
import { useAuth } from '@/lib/auth-context'
import { useDepartmentFilter } from '@/lib/use-department-filter'
import { supabase } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, FileText, Mail, FolderOpen, Monitor, Key, Plus } from 'lucide-react'

interface ServiceSelectionProps {
  onServiceSelect: (services: ServiceCategory[]) => void
  selectedServices: ServiceCategory[]
  disabled?: boolean
}

const serviceIcons: Record<string, any> = {
  'GM': Mail,
  'MB': Mail,
  'SPO': FolderOpen,
  'PCA': Monitor,
  'PWR': Key,
  'default': FileText
}

export default function ServiceSelection({
  onServiceSelect,
  selectedServices,
  disabled = false
}: ServiceSelectionProps) {
  const [services, setServices] = useState<ServiceCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  
  const { staff } = useAuth()
  const { userDepartmentId, canAccessDepartment } = useDepartmentFilter()

  useEffect(() => {
    loadServices()
  }, [staff])

  const loadServices = async () => {
    try {
      setLoading(true)
      setError('')

      const { data, error: fetchError } = await supabase
        .from('service_categories')
        .select('*')
        .eq('is_active', true)
        .order('name_jp')

      if (fetchError) throw fetchError

      // Filter services based on user permissions
      const filteredServices = data?.filter(service => {
        // If service is department-specific, check if user can access it
        if (service.is_department_specific) {
          return userDepartmentId && canAccessDepartment(userDepartmentId)
        }
        // Non-department-specific services are available to all
        return true
      }) || []

      setServices(filteredServices)
    } catch (err: any) {
      setError('サービスの読み込みに失敗しました / Failed to load services: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleServiceToggle = (service: ServiceCategory, checked: boolean) => {
    if (disabled) return

    let updatedServices: ServiceCategory[]
    
    if (checked) {
      updatedServices = [...selectedServices, service]
    } else {
      updatedServices = selectedServices.filter(s => s.id !== service.id)
    }
    
    onServiceSelect(updatedServices)
  }

  const isServiceSelected = (serviceId: string): boolean => {
    return selectedServices.some(s => s.id === serviceId)
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>サービスを読み込み中... / Loading services...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Plus className="mr-2 h-5 w-5" />
          サービス選択 / Service Selection
        </CardTitle>
        <CardDescription>
          リクエストするサービスカテゴリを選択してください
          <br />
          Select the service categories for your request
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {services.map((service) => {
            const IconComponent = serviceIcons[service.code] || serviceIcons.default
            const isSelected = isServiceSelected(service.id)
            
            return (
              <div
                key={service.id}
                className={`
                  relative p-4 border rounded-lg cursor-pointer transition-all
                  ${isSelected 
                    ? 'border-blue-500 bg-blue-50 shadow-md' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                  ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                `}
                onClick={() => handleServiceToggle(service, !isSelected)}
              >
                <div className="flex items-start space-x-3">
                  <Checkbox
                    checked={isSelected}
                    onChange={(e) => handleServiceToggle(service, e.target.checked)}
                    disabled={disabled}
                    className="mt-1"
                  />
                  
                  <IconComponent className="h-5 w-5 mt-1 text-gray-600" />
                  
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm">
                      {service.name_jp}
                    </div>
                    <div className="text-xs text-gray-500 mb-2">
                      {service.name_en}
                    </div>
                    
                    {service.is_department_specific && (
                      <Badge variant="secondary" className="text-xs">
                        部署限定 / Dept Only
                      </Badge>
                    )}
                    
                    {service.description && (
                      <div className="text-xs text-gray-600 mt-2">
                        {service.description}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {selectedServices.length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium mb-2">
              選択済みサービス / Selected Services ({selectedServices.length})
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedServices.map((service) => (
                <Badge key={service.id} variant="default" className="text-xs">
                  {service.name_jp}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
