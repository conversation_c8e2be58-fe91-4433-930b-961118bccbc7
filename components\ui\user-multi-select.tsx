'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/components/providers/supabase-provider'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { X, UserPlus } from 'lucide-react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

interface UserMultiSelectProps {
  selectedUsers: string[]
  onSelectionChange: (users: string[]) => void
  departmentId?: string
  placeholder?: string
}

interface StaffMember {
  id: string
  name_jp: string
  name_en: string
  email: string
  staff_id: string
  department?: {
    name_jp: string
    name_en: string
  }
}

export function UserMultiSelect({
  selectedUsers,
  onSelectionChange,
  departmentId,
  placeholder = 'Select users...'
}: UserMultiSelectProps) {
  const [open, setOpen] = useState(false)
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [selectedStaff, setSelectedStaff] = useState<StaffMember[]>([])
  const [loading, setLoading] = useState(false)
  const { supabase } = useSupabase()

  useEffect(() => {
    fetchStaff()
  }, [departmentId])

  useEffect(() => {
    fetchSelectedStaff()
  }, [selectedUsers])

  const fetchStaff = async () => {
    setLoading(true)
    try {
      let query = supabase
        .from('staff')
        .select(\`
          id,
          name_jp,
          name_en,
          email,
          staff_id,
          department:divisions(name_jp, name_en)
        \`)
        .eq('is_active', true)
        .order('name_en')

      if (departmentId) {
        query = query.eq('division_id', departmentId)
      }

      const { data, error } = await query

      if (error) throw error
      setStaff(data || [])
    } catch (error) {
      console.error('Error fetching staff:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSelectedStaff = async () => {
    if (selectedUsers.length === 0) {
      setSelectedStaff([])
      return
    }

    try {
      const { data, error } = await supabase
        .from('staff')
        .select(\`
          id,
          name_jp,
          name_en,
          email,
          staff_id,
          department:divisions(name_jp, name_en)
        \`)
        .in('id', selectedUsers)

      if (error) throw error
      setSelectedStaff(data || [])
    } catch (error) {
      console.error('Error fetching selected staff:', error)
    }
  }

  const handleSelect = (staffId: string) => {
    const newSelection = selectedUsers.includes(staffId)
      ? selectedUsers.filter(id => id !== staffId)
      : [...selectedUsers, staffId]
    
    onSelectionChange(newSelection)
  }

  const handleRemove = (staffId: string) => {
    onSelectionChange(selectedUsers.filter(id => id !== staffId))
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-start"
          >
            {selectedStaff.length > 0 ? (
              <span className="truncate">
                {selectedStaff.length} user{selectedStaff.length > 1 ? 's' : ''} selected
              </span>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <UserPlus className="ml-auto h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search staff..." />
            <CommandEmpty>No staff found.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {staff.map((member) => (
                <CommandItem
                  key={member.id}
                  onSelect={() => handleSelect(member.id)}
                >
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(member.id)}
                      className="h-4 w-4 rounded border-gray-300"
                      readOnly
                    />
                    <div className="flex-1">
                      <div className="font-medium">
                        {member.name_en} ({member.staff_id})
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {member.email}
                      </div>
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedStaff.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedStaff.map((member) => (
            <Badge key={member.id} variant="secondary">
              {member.name_en} ({member.staff_id})
              <button
                onClick={() => handleRemove(member.id)}
                className="ml-1 hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
