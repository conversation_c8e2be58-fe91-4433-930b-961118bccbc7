import { NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'
    
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case '24h':
        startDate.setDate(now.getDate() - 1)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      default:
        startDate.setDate(now.getDate() - 7)
    }

    // Get category distribution
    const { data, error } = await supabase
      .from('request_forms')
      .select(`
        service_categories!request_forms_service_category_id_fkey (
          id,
          name_en,
          name_jp
        )
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', now.toISOString())

    if (error) throw error

    // Count by category
    const categoryCount: Record<string, number> = {}
    data?.forEach(form => {
      const category = form.service_categories?.[0]
      if (category) {
        const categoryName = category.name_en || 'Unknown'
        categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1
      }
    })

    // Calculate total
    const total = Object.values(categoryCount).reduce((sum, count) => sum + count, 0)

    // Convert to array format with percentages
    const categoryData = Object.entries(categoryCount)
      .map(([category, count]) => ({
        category,
        count,
        percentage: Math.round((count / total) * 100)
      }))
      .sort((a, b) => b.count - a.count)

    return NextResponse.json(categoryData)
  } catch (error) {
    console.error('Error fetching category distribution:', error)
    return NextResponse.json(
      { error: 'Failed to fetch category distribution' },
      { status: 500 }
    )
  }
}
