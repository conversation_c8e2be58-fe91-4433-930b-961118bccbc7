import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { contentAutoUpdateService } from '@/lib/services/content-auto-update-service'

interface ContentGap {
  id: string
  topic: string
  query_count: number
  suggested_category: string
  priority: 'high' | 'medium' | 'low'
  status: string
  created_at: string
}

export function useContentGaps() {
  const [gaps, setGaps] = useState<ContentGap[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchContentGaps()
  }, [])

  const fetchContentGaps = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('kb_content_gaps')
        .select('*')
        .order('priority', { ascending: true })
        .order('query_count', { ascending: false })
      
      if (error) throw error
      
      setGaps(data || [])
    } catch (err: any) {
      console.error('Error fetching content gaps:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const analyzeGaps = async () => {
    try {
      setLoading(true)
      await contentAutoUpdateService.analyzeContentGaps()
      await fetchContentGaps()
    } catch (err: any) {
      console.error('Error analyzing gaps:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const dismissGap = async (gapId: string) => {
    try {
      const { error } = await supabase
        .from('kb_content_gaps')
        .update({ status: 'dismissed' })
        .eq('id', gapId)
      
      if (error) throw error
      
      await fetchContentGaps()
    } catch (err: any) {
      console.error('Error dismissing gap:', err)
      setError(err.message)
    }
  }

  const resolveGap = async (gapId: string) => {
    try {
      const { error } = await supabase
        .from('kb_content_gaps')
        .update({ 
          status: 'resolved',
          resolved_at: new Date().toISOString()
        })
        .eq('id', gapId)
      
      if (error) throw error
      
      await fetchContentGaps()
    } catch (err: any) {
      console.error('Error resolving gap:', err)
      setError(err.message)
    }
  }

  return {
    gaps,
    loading,
    error,
    analyzeGaps,
    dismissGap,
    resolveGap,
    refresh: fetchContentGaps
  }
}
