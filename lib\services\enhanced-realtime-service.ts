import { supabase } from '@/lib/supabase';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

type Tables = Database['public']['Tables'];
type TableName = keyof Tables;

export interface RealtimeConfig {
  table: TableName;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  schema?: string;
  includeOldRecord?: boolean;
}

export interface RealtimeSubscription {
  channel: RealtimeChannel;
  config: RealtimeConfig;
  callbacks: Map<string, (payload: any) => void>;
}

export class EnhancedRealtimeService {
  private subscriptions: Map<string, RealtimeSubscription> = new Map();
  private userRole: string | null = null;
  private userDepartmentId: string | null = null;
  private userId: string | null = null;

  constructor() {
    this.initializeUserContext();
  }

  /**
   * Initialize user context for role-based filtering
   */
  private async initializeUserContext() {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      this.userId = user.id;
      
      // Get user's role and department from staff table
      const { data: staffData } = await supabase
        .from('staff')
        .select('role_id, division_id, roles!inner(name)')
        .eq('auth_id', user.id)
        .single();
      
      if (staffData) {
        this.userRole = staffData.roles?.name || null;
        this.userDepartmentId = staffData.division_id;
      }
    }
  }

  /**
   * Get filter based on user role and department
   */
  private getRoleBasedFilter(table: TableName): string | undefined {
    if (!this.userRole || !this.userDepartmentId) return undefined;

    // Global Administrator and Web App System Administrator can see all data
    if (['Global Administrator', 'Web App System Administrator'].includes(this.userRole)) {
      return undefined;
    }

    // Department-specific filtering
    switch (table) {
      case 'request_forms':
        return `requester_id.in.(SELECT id FROM staff WHERE division_id='${this.userDepartmentId}')`;
      
      case 'staff':
        return `division_id=eq.${this.userDepartmentId}`;
      
      case 'groups':
        return `division_id=eq.${this.userDepartmentId}`;
      
      default:
        return undefined;
    }
  }

  /**
   * Subscribe to real-time changes with role-based filtering
   */
  subscribe(
    config: RealtimeConfig,
    callbackId: string,
    callback: (payload: RealtimePostgresChangesPayload<any>) => void
  ): string {
    // Apply role-based filter if not explicitly provided
    const filter = config.filter || this.getRoleBasedFilter(config.table);
    const enhancedConfig = { ...config, filter };
    
    // Create unique subscription key
    const subscriptionKey = this.getSubscriptionKey(enhancedConfig);
    
    // Check if subscription already exists
    if (this.subscriptions.has(subscriptionKey)) {
      const subscription = this.subscriptions.get(subscriptionKey)!;
      subscription.callbacks.set(callbackId, callback);
      return subscriptionKey;
    }

    // Create new subscription
    const channel = supabase
      .channel(subscriptionKey)
      .on(
        'postgres_changes',
        {
          event: enhancedConfig.event || '*',
          schema: enhancedConfig.schema || 'public',
          table: enhancedConfig.table,
          filter: enhancedConfig.filter
        },
        (payload) => {
          const subscription = this.subscriptions.get(subscriptionKey);
          if (subscription) {
            subscription.callbacks.forEach(cb => cb(payload));
          }
        }
      )
      .subscribe();

    // Store subscription
    const callbacks = new Map<string, (payload: any) => void>();
    callbacks.set(callbackId, callback);
    
    this.subscriptions.set(subscriptionKey, {
      channel,
      config: enhancedConfig,
      callbacks
    });

    return subscriptionKey;
  }

  /**
   * Subscribe to request status changes
   */
  subscribeToRequestStatus(
    requestId: string,
    callback: (payload: RealtimePostgresChangesPayload<Tables['request_forms']['Row']>) => void
  ): string {
    return this.subscribe(
      {
        table: 'request_forms',
        filter: `id=eq.${requestId}`,
        event: 'UPDATE'
      },
      `request-status-${requestId}`,
      callback
    );
  }

  /**
   * Subscribe to all requests for a department
   */
  subscribeToDepartmentRequests(
    callback: (payload: RealtimePostgresChangesPayload<Tables['request_forms']['Row']>) => void
  ): string {
    return this.subscribe(
      {
        table: 'request_forms',
        event: '*'
      },
      'department-requests',
      callback
    );
  }

  /**
   * Subscribe to notifications for the current user
   */
  subscribeToUserNotifications(
    callback: (payload: RealtimePostgresChangesPayload<any>) => void
  ): string {
    if (!this.userId) {
      throw new Error('User not authenticated');
    }

    return this.subscribe(
      {
        table: 'notifications' as TableName, // Assuming notifications table exists
        filter: `user_id=eq.${this.userId}`,
        event: 'INSERT'
      },
      'user-notifications',
      callback
    );
  }

  /**
   * Unsubscribe a specific callback
   */
  unsubscribe(subscriptionKey: string, callbackId: string): void {
    const subscription = this.subscriptions.get(subscriptionKey);
    if (subscription) {
      subscription.callbacks.delete(callbackId);
      
      // If no more callbacks, remove the entire subscription
      if (subscription.callbacks.size === 0) {
        supabase.removeChannel(subscription.channel);
        this.subscriptions.delete(subscriptionKey);
      }
    }
  }

  /**
   * Unsubscribe all subscriptions
   */
  unsubscribeAll(): void {
    this.subscriptions.forEach((subscription) => {
      supabase.removeChannel(subscription.channel);
    });
    this.subscriptions.clear();
  }

  /**
   * Get subscription key from config
   */
  private getSubscriptionKey(config: RealtimeConfig): string {
    return `${config.table}-${config.event || '*'}-${config.filter || 'all'}`;
  }

  /**
   * Get active subscription count
   */
  getActiveSubscriptionCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Check if a subscription exists
   */
  hasSubscription(subscriptionKey: string): boolean {
    return this.subscriptions.has(subscriptionKey);
  }
}

// Export singleton instance
export const enhancedRealtimeService = new EnhancedRealtimeService();
