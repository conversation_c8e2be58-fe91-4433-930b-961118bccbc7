import { supabase } from '@/lib/auth'
import { UserRole } from '@/lib/auth'

// Get all roles
export const getAllRoles = async () => {
  const { data, error } = await supabase
    .from('roles')
    .select('*')
    .order('name')
  
  return { data, error }
}

// Get all staff with roles
export const getAllStaffWithRoles = async () => {
  const { data, error } = await supabase
    .from('staff')
    .select(`
      id,
      name_jp,
      name_en,
      email,
      staff_id,
      division:divisions(id, name_jp, name_en),
      group:groups(id, name_jp, name_en),
      role:roles(id, name, description)
    `)
    .order('name_jp')
  
  return { data, error }
}

// Assign role to staff member
export const assignRoleToStaff = async (staffId: string, roleId: string) => {
  const { data, error } = await supabase
    .from('staff')
    .update({ role_id: roleId, updated_at: new Date().toISOString() })
    .eq('id', staffId)
    .select()
  
  return { data, error }
}

// Get staff filtered by department
export const getStaffByDepartment = async (departmentId: string) => {
  const { data, error } = await supabase
    .from('staff')
    .select(`
      id,
      name_jp,
      name_en,
      email,
      staff_id,
      division:divisions(id, name_jp, name_en),
      group:groups(id, name_jp, name_en),
      role:roles(id, name, description)
    `)
    .eq('division_id', departmentId)
    .order('name_jp')
  
  return { data, error }
}

// Check if user can manage specific staff member
export const canManageStaff = async (
  managerUserId: string, 
  targetStaffId: string
): Promise<boolean> => {
  try {
    // Get manager's information
    const { data: manager, error: managerError } = await supabase
      .from('staff')
      .select(`
        id,
        division_id,
        role:roles(name)
      `)
      .eq('auth_id', managerUserId)
      .single()
    
    if (managerError || !manager) return false
    
    // Global and System admins can manage anyone
    if (manager.role?.name === UserRole.GLOBAL_ADMIN || 
        manager.role?.name === UserRole.SYSTEM_ADMIN) {
      return true
    }
    
    // Get target staff information
    const { data: targetStaff, error: targetError } = await supabase
      .from('staff')
      .select('id, division_id')
      .eq('id', targetStaffId)
      .single()
    
    if (targetError || !targetStaff) return false
    
    // Department admins can only manage staff in their department
    if (manager.role?.name === UserRole.DEPT_ADMIN) {
      return manager.division_id === targetStaff.division_id
    }
    
    // HR staff can manage anyone
    if (manager.role?.name === UserRole.HR_STAFF) {
      return true
    }
    
    return false
  } catch (error) {
    console.error('Error checking staff management permission:', error)
    return false
  }
}
