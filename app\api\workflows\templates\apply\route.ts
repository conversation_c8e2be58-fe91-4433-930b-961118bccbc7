import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { WorkflowTemplateManager } from '@/lib/services/workflow/templates/workflow-template-manager';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';
import { AuditService } from '@/lib/services/audit/audit-service';

export async function POST(request: Request) {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: staff } = await supabase
      .from('staff')
      .select('*')
      .eq('auth_id', user.id)
      .single();

    if (!staff) {
      return NextResponse.json({ error: 'Staff not found' }, { status: 404 });
    }

    const body = await request.json();
    const { requestId, templateId, customizations } = body;

    if (!requestId || !templateId) {
      return NextResponse.json(
        { error: 'Request ID and Template ID are required' },
        { status: 400 }
      );
    }

    const auditService = new AuditService(supabase);
    const workflowEngine = new WorkflowEngine(supabase);
    const templateManager = new WorkflowTemplateManager(supabase, auditService, workflowEngine);

    if (customizations) {
      // Create workflow from template with customizations
      const instanceId = await templateManager.createWorkflowFromTemplate(
        templateId,
        requestId,
        staff.id,
        customizations
      );

      return NextResponse.json({ 
        message: 'Workflow created from template',
        workflowInstanceId: instanceId 
      });
    } else {
      // Apply template directly to request
      await templateManager.applyTemplateToRequest(
        requestId,
        templateId,
        staff.id
      );

      return NextResponse.json({ 
        message: 'Template applied to request successfully'
      });
    }
  } catch (error) {
    console.error('Error applying template:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to apply template' },
      { status: 500 }
    );
  }
}
