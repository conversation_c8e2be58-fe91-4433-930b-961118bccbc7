# ITSync Integration API Documentation

## Overview

ITSync provides REST API endpoints for integration with external systems like ServiceNow and Backlog. All data remains within the Supabase database - these endpoints allow external systems to create and sync requests with ITSync.

## Authentication

All API requests must include an API key in the header:

```
x-api-key: YOUR_INTEGRATION_API_KEY
```

Set the `INTEGRATION_API_KEY` environment variable in your `.env.local` file.

## Endpoints

### ServiceNow Integration

**Endpoint:** `POST /api/integrations/servicenow`

**Purpose:** Create IT requests from ServiceNow tickets

**Request Body:**
```json
{
  "action": "create_request",
  "ticket": {
    "number": "INC0001234",
    "sys_id": "abc123...",
    "short_description": "PC Admin Access Request",
    "description": "User needs admin access for software installation",
    "priority": 2,
    "assignment_group": "IT Support",
    "assigned_to": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "success": true,
  "request_id": "uuid-here",
  "message": "Request created successfully"
}
```

### Backlog Integration

**Endpoint:** `POST /api/integrations/backlog`

**Purpose:** Sync Backlog issues to IT requests

**Request Body:**
```json
{
  "action": "sync_issue",
  "issue": {
    "issueKey": "IT-1234",
    "projectId": "10001",
    "summary": "Setup new employee access",
    "description": "Configure access for new hire",
    "priority": {
      "id": 2,
      "name": "高"
    },
    "issueType": {
      "id": 1,
      "name": "Task"
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "request_id": "uuid-here",
  "message": "Issue synced successfully"
}
```

### Check Integration Status

**Endpoint:** `GET /api/integrations/{integration}`

**Purpose:** Check if integration endpoint is available

**Response:**
```json
{
  "integration": "servicenow",
  "status": "ready",
  "version": "1.0.0",
  "endpoints": {
    "servicenow": ["/api/integrations/servicenow"],
    "backlog": ["/api/integrations/backlog"]
  }
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "error": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "error": "Unknown integration"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error"
}
```

## Priority Mapping

### ServiceNow Priority
- 1 → Critical
- 2 → High  
- 3 → Medium
- 4-5 → Low

### Backlog Priority
- High/高 → High
- Normal/中 → Medium
- Low/低 → Low

## Usage Examples

### cURL Example - ServiceNow
```bash
curl -X POST https://your-domain.com/api/integrations/servicenow \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "action": "create_request",
    "ticket": {
      "number": "INC0001234",
      "short_description": "Test Request",
      "priority": 2
    }
  }'
```

### JavaScript Example - Backlog
```javascript
const response = await fetch('/api/integrations/backlog', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': process.env.INTEGRATION_API_KEY
  },
  body: JSON.stringify({
    action: 'sync_issue',
    issue: {
      issueKey: 'IT-1234',
      summary: 'New Request',
      priority: { id: 2, name: '高' }
    }
  })
});
```

## Future Enhancements

- Webhook support for real-time updates
- Bidirectional sync capabilities
- Additional integration points (Jira, Teams, Slack)
- OAuth 2.0 authentication option
- Rate limiting and usage analytics

## Notes

- All request data is stored in the Supabase database
- No external API calls are made to Microsoft services
- Group mail, mailbox, and SharePoint data are managed internally
- These endpoints prepare ITSync for future external system integrations