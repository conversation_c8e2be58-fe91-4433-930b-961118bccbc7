'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

export default function HomePage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      if (user) {
        router.push('/dashboard')
      } else {
        router.push('/login')
      }
    }
  }, [user, loading, router])

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <Card>
        <CardContent className="p-8 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-lg">読み込み中... / Loading...</span>
          </div>
          <h1 className="text-4xl font-bold mb-4">ITSync</h1>
          <p className="text-xl text-gray-600">Enterprise IT Helpdesk & Support Platform</p>
          <p className="mt-4 text-gray-500">AI-powered, Database-driven IT Support</p>
        </CardContent>
      </Card>
    </main>
  )
}
