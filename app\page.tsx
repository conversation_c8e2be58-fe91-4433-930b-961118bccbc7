import Link from 'next/link';

export default function HomePage() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">ITSync</h1>
        <p className="text-xl text-gray-600">Enterprise IT Helpdesk & Support Platform</p>
        <p className="mt-4 text-gray-500">AI-powered, Database-driven IT Support</p>
        
        <div className="mt-8 p-6 bg-green-50 rounded-lg">
          <h2 className="text-lg font-semibold text-green-800">✅ Build Successful</h2>
          <p className="text-green-600">All components loaded successfully</p>
        </div>
        
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-3">API Documentation</h3>
            <p className="mb-4 text-gray-600">
              Comprehensive documentation for the Enterprise IT Helpdesk API
            </p>
            <Link href="/docs" className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              View API Docs
            </Link>
          </div>
          
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-3">Authentication Guide</h3>
            <p className="mb-4 text-gray-600">
              Detailed documentation for authentication flows
            </p>
            <Link href="/auth-docs" className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              View Auth Docs
            </Link>
          </div>
          
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-3">Sample API</h3>
            <p className="mb-4 text-gray-600">
              Try out the sample Users API
            </p>
            <Link href="/api/users" className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              Try Users API
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}
