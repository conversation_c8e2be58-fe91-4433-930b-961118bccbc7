-- Create table for NLP parse logs
CREATE TABLE IF NOT EXISTS nlp_parse_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  input_text TEXT NOT NULL,
  parsed_intent VARCHAR(100),
  confidence DECIMAL(3, 2),
  extracted_data JSONB,
  language VARCHAR(10) DEFAULT 'ja',
  department_id UUID REFERENCES divisions(id),
  user_id UUID REFERENCES staff(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX idx_nlp_logs_intent ON nlp_parse_logs(parsed_intent);
CREATE INDEX idx_nlp_logs_created ON nlp_parse_logs(created_at DESC);
CREATE INDEX idx_nlp_logs_department ON nlp_parse_logs(department_id);

-- Enable RLS
ALTER TABLE nlp_parse_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can see their own logs
CREATE POLICY "nlp_logs_user_policy" ON nlp_parse_logs
  FOR SELECT TO authenticated
  USING (auth.uid()::UUID = user_id);

-- Admin can see all logs
CREATE POLICY "nlp_logs_admin_policy" ON nlp_parse_logs
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );
