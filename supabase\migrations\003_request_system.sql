-- Service categories table
CREATE TABLE service_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name_jp TEXT NOT NULL,
  name_en TEXT NOT NULL,
  code VARCHAR(20),
  description TEXT,
  is_department_specific BOOLEAN DEFAULT FALSE,
  form_schema JSONB, -- Dynamic form schema
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Request forms table
CREATE TABLE request_forms (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  requester_id UUID REFERENCES staff(id),
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  title TEXT NOT NULL,
  description TEXT,
  priority VARCHAR(10) DEFAULT 'medium',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Request items table (for multi-user, multi-category requests)
CREATE TABLE request_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  request_form_id UUID REFERENCES request_forms(id) ON DELETE CASCADE,
  service_category_id UUID REFERENCES service_categories(id),
  affected_user_id UUID REFERENCES staff(id),
  action_type VARCHAR(20) NOT NULL, -- 'add', 'remove', 'update'
  target_resource TEXT NOT NULL, -- e.g. group mail address, SPO library
  request_details JSONB NOT NULL DEFAULT '{}',
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indices
CREATE INDEX idx_request_forms_requester_id ON request_forms(requester_id);
CREATE INDEX idx_request_forms_status ON request_forms(status);
CREATE INDEX idx_request_items_request_form_id ON request_items(request_form_id);
CREATE INDEX idx_request_items_service_category_id ON request_items(service_category_id);
CREATE INDEX idx_request_items_affected_user_id ON request_items(affected_user_id);
