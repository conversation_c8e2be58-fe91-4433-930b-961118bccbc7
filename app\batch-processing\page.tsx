"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { BatchRequestBuilder } from '@/components/batch-processing/batch-request-builder';
import { BatchStatusMonitor } from '@/components/batch-processing/batch-status-monitor';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { batchProcessingService } from '@/lib/services/batch-processing-service';
import type { BatchRequest, BatchOperation } from '@/lib/batch-processing-types';
import { useAuth } from '@/lib/auth-context';
import { toast } from 'sonner';
import { FileText, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export default function BatchProcessingPage() {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeBatchId, setActiveBatchId] = useState<string | null>(null);
  const [batchHistory, setBatchHistory] = useState<BatchOperation[]>([]);
  const [activeTab, setActiveTab] = useState('create');

  const handleBatchSubmit = async (request: BatchRequest) => {
    if (!user) {
      toast.error('ログインが必要です');
      return;
    }

    setIsProcessing(true);
    try {
      const result = await batchProcessingService.createBatchOperation(
        user.id,
        request
      );

      if (result.success) {
        toast.success('バッチ処理を開始しました');
        setActiveBatchId(result.batch_id);
        setActiveTab('monitor');
        loadBatchHistory();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('バッチ処理の開始に失敗しました');
    } finally {
      setIsProcessing(false);
    }
  };
  const loadBatchHistory = async () => {
    // This would load batch history from the service
    // For now, we'll just show the current batch
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">バッチ処理システム</h1>
        <p className="text-muted-foreground mt-2">
          複数のユーザーやサービスに対する一括処理を実行します
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="create">新規作成</TabsTrigger>
          <TabsTrigger value="monitor" disabled={!activeBatchId}>
            処理状況
          </TabsTrigger>
          <TabsTrigger value="history">履歴</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="mt-6">
          <BatchRequestBuilder
            onSubmit={handleBatchSubmit}
            isProcessing={isProcessing}
          />
        </TabsContent>

        <TabsContent value="monitor" className="mt-6">
          {activeBatchId ? (
            <BatchStatusMonitor
              batchId={activeBatchId}
              onComplete={() => loadBatchHistory()}
            />          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>バッチが選択されていません</AlertTitle>
              <AlertDescription>
                新規作成タブからバッチ処理を開始してください。
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>バッチ処理履歴</CardTitle>
              <CardDescription>
                過去のバッチ処理の実行履歴を表示します
              </CardDescription>
            </CardHeader>
            <CardContent>
              {batchHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="w-12 h-12 mx-auto mb-4 opacity-30" />
                  <p>バッチ処理履歴がありません</p>
                </div>
              ) : (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {batchHistory.map((batch) => (
                      <Card key={batch.id} className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(batch.status)}
                            <div>
                              <p className="font-medium">
                                バッチ {batch.batch_id}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {new Date(batch.created_at).toLocaleString('ja-JP')}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm">
                              {batch.completed_items}/{batch.total_items} 件完了
                            </p>
                            {batch.failed_items > 0 && (
                              <p className="text-sm text-red-600">
                                {batch.failed_items} 件失敗
                              </p>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}