// PC Admin Management Types

export interface PcAsset {
  id: string;
  pc_id: string;
  model?: string;
  serial_number?: string;
  os_version?: string;
  purchase_date?: string;
  assigned_user_id?: string;
  assigned_date?: string;
  department_id?: string;
  status: 'active' | 'inactive' | 'maintenance' | 'retired';
  admin_enabled: boolean;
  last_admin_change?: string;
  created_at: string;
  updated_at: string;
}

export interface PcAdminRequest {
  id: string;
  request_id: string;
  requester_id: string;
  pc_asset_id?: string;
  pc_id_search?: string;
  action_type: 'grant_admin' | 'revoke_admin';
  reason: string;
  software_to_install?: string[];
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  approved_by?: string;
  approved_at?: string;
  completed_by?: string;
  completed_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface SoftwareCatalog {
  id: string;
  name: string;
  version?: string;
  vendor?: string;
  license_type?: 'perpetual' | 'subscription' | 'freeware';
  requires_admin: boolean;
  category?: 'productivity' | 'development' | 'security' | 'utility';
  description?: string;
  installation_notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PcAdminHistory {
  id: string;
  pc_asset_id: string;
  user_id?: string;
  action: 'admin_granted' | 'admin_revoked' | 'software_installed';
  performed_by: string;
  request_id?: string;
  details?: any;
  created_at: string;
}

export interface PcSearchResult {
  id: string;
  pc_id: string;
  model?: string;
  assigned_user_name?: string;
  department_name?: string;
  admin_enabled: boolean;
}

export interface PcAdminRequestForm {
  pc_id_search: string;
  action_type: 'grant_admin' | 'revoke_admin';
  reason: string;
  software_to_install?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export interface ProcessRequestResult {
  success: boolean;
  message: string;
}
