'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import type { User, Session } from '@supabase/supabase-js'
import { supabase, getUserWithRole, UserRole } from '@/lib/auth'
import { isDevelopmentWithoutConfig } from '@/lib/env'

interface AuthContextType {
  user: User | null
  session: Session | null
  staff: any | null
  userRole: UserRole | null
  loading: boolean
  isConfigured: boolean
  signIn: (email: string, password: string) => Promise<any>
  signOut: () => Promise<any>
  signUp: (email: string, password: string, userData: any) => Promise<any>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [staff, setStaff] = useState<any | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [isConfigured, setIsConfigured] = useState(false)

  const loadStaffData = async (userId: string) => {
    console.log(`[AUTH_CONTEXT] loadStaffData called for userId: ${userId}`);
    try {
      const { data, error } = await getUserWithRole(userId)
      if (error) {
        console.error('[AUTH_CONTEXT] Error loading staff data in loadStaffData:', error);
        if (process.env.NODE_ENV === 'development') {
          console.warn('[AUTH_CONTEXT] Setting default role (REGULAR_USER) in development due to staff data load error.');
          setUserRole(UserRole.REGULAR_USER);
        }
      } else if (data) {
        console.log('[AUTH_CONTEXT] Staff data loaded:', data);
        setStaff(data);
        
        let determinedRole: UserRole | null = null;
        if (data.role && data.role.name) {
          determinedRole = data.role.name as UserRole;
        } else if (data.role_id) {
          console.warn(`[AUTH_CONTEXT] Staff data has role_id (${data.role_id}) but no direct role object. Defaulting to REGULAR_USER.`);
          determinedRole = UserRole.REGULAR_USER;
        } else {
          console.warn('[AUTH_CONTEXT] No role information found in staff data. Defaulting to REGULAR_USER.');
          determinedRole = UserRole.REGULAR_USER;
        }
        setUserRole(determinedRole);
        console.log(`[AUTH_CONTEXT] User role set to: ${determinedRole}`);
      } else {
        console.warn('[AUTH_CONTEXT] No data returned from getUserWithRole in loadStaffData.');
      }
    } catch (error) {
      console.error('[AUTH_CONTEXT] Exception in loadStaffData:', error);
      if (process.env.NODE_ENV === 'development') {
        console.warn('[AUTH_CONTEXT] Setting default role (REGULAR_USER) in development due to exception in loadStaffData.');
        setUserRole(UserRole.REGULAR_USER);
      }
    }
  }

  useEffect(() => {
    console.log('[AUTH_CONTEXT] AuthProvider useEffect running.');
    if (!supabase || isDevelopmentWithoutConfig()) {
      console.warn('[AUTH_CONTEXT] Supabase not configured or in dev mode without config. Auth features disabled.');
      setIsConfigured(false);
      setLoading(false);
      return;
    }
    console.log('[AUTH_CONTEXT] Supabase is configured.');
    setIsConfigured(true);

    const getSession = async () => {
      console.log('[AUTH_CONTEXT] getSession called.');
      try {
        if (!supabase) {
          console.warn('[AUTH_CONTEXT] Supabase client not available in getSession (should not happen if configured).');
          setLoading(false);
          return;
        }
        
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('[AUTH_CONTEXT] Error getting initial session:', error);
        } else {
          console.log('[AUTH_CONTEXT] Initial session fetched:', currentSession ? `User ID: ${currentSession.user.id}` : 'No active session');
          setSession(currentSession);
          setUser(currentSession?.user ?? null);
          
          if (currentSession?.user) {
            await loadStaffData(currentSession.user.id);
          } else {
            setStaff(null);
            setUserRole(null);
            console.log('[AUTH_CONTEXT] No initial user session, staff and userRole cleared.');
          }
        }
      } catch (error) {
        console.error('[AUTH_CONTEXT] Exception in getSession:', error);
      } finally {
        setLoading(false);
        console.log('[AUTH_CONTEXT] Initial session loading complete.');
      }
    }

    getSession();

    let subscription: { unsubscribe: () => void } | undefined;
    
    if (supabase) {
      const { data: authListenerData } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log(`[AUTH_CONTEXT] onAuthStateChange event: ${event}`, newSession ? `User ID: ${newSession.user.id}` : 'No session');
        setSession(newSession);
        setUser(newSession?.user ?? null);
        
        if (newSession?.user) {
          await loadStaffData(newSession.user.id);
        } else {
          setStaff(null);
          setUserRole(null);
          console.log('[AUTH_CONTEXT] User signed out or session expired, staff and userRole cleared.');
        }
        setLoading(false); // Potentially set to true at start of event handling if async ops are long
      }
    );
      subscription = authListenerData.subscription;
      console.log('[AUTH_CONTEXT] Subscribed to onAuthStateChange.');
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
        console.log('[AUTH_CONTEXT] Unsubscribed from onAuthStateChange.');
      }
    }
  }, [])

  const handleSignIn = async (email: string, password: string) => {
    console.log(`[AUTH_CONTEXT] handleSignIn called for email: ${email}`);
    if (!supabase) {
      console.error('[AUTH_CONTEXT] Supabase not configured in handleSignIn.');
      return { data: null, error: new Error('Supabase is not configured') };
    }
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) {
      console.error(`[AUTH_CONTEXT] Error during signIn for ${email}:`, error);
    } else {
      console.log(`[AUTH_CONTEXT] signIn successful for ${email}. User:`, data.user?.id);
    }
    return { data, error };
  }

  const handleSignOut = async () => {
    console.log('[AUTH_CONTEXT] handleSignOut called.');
    if (!supabase) {
      console.error('[AUTH_CONTEXT] Supabase not configured in handleSignOut.');
      return { error: new Error('Supabase is not configured') };
    }
    
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('[AUTH_CONTEXT] Error during signOut:', error);
    } else {
      console.log('[AUTH_CONTEXT] signOut successful.');
    }
    return { error };
  }

  const handleSignUp = async (email: string, password: string, userData: any) => {
    console.log(`[AUTH_CONTEXT] handleSignUp called for email: ${email}`);
    if (!supabase) {
      console.error('[AUTH_CONTEXT] Supabase not configured in handleSignUp.');
      return { data: null, error: new Error('Supabase is not configured') };
    }
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    return { data, error }
  }

  const value: AuthContextType = {
    user,
    session,
    staff,
    userRole,
    loading,
    isConfigured,
    signIn: handleSignIn,
    signOut: handleSignOut,
    signUp: handleSignUp
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
