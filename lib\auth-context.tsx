'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import type { User, Session } from '@supabase/supabase-js'
import { supabase, getUserWithRole, UserRole } from '@/lib/auth'
import { isDevelopmentWithoutConfig } from '@/lib/env'

interface AuthContextType {
  user: User | null
  session: Session | null
  staff: any | null
  userRole: UserRole | null
  loading: boolean
  isConfigured: boolean
  signIn: (email: string, password: string) => Promise<any>
  signOut: () => Promise<any>
  signUp: (email: string, password: string, userData: any) => Promise<any>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [staff, setStaff] = useState<any | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [isConfigured, setIsConfigured] = useState(false)

  const loadStaffData = async (userId: string) => {
    try {
      const { data, error } = await getUserWithRole(userId)
      if (error) {
        console.error('Error loading staff data:', error)
        // Set default role for development/testing
        if (process.env.NODE_ENV === 'development') {
          setUserRole(UserRole.REGULAR_USER)
        }
      } else if (data) {
        setStaff(data)
        
        // Handle the case where role data might not be available
        if (data.role && data.role.name) {
          setUserRole(data.role.name as UserRole)
        } else if (data.role_id) {
          // If we have role_id but no role object, set a default based on the ID
          // This is a fallback for when relationships aren't working
          setUserRole(UserRole.REGULAR_USER)
        } else {
          // Default role if no role information is available
          setUserRole(UserRole.REGULAR_USER)
        }
      }
    } catch (error) {
      console.error('Error loading staff data:', error)
      // Set default role for development/testing
      if (process.env.NODE_ENV === 'development') {
        setUserRole(UserRole.REGULAR_USER)
      }
    }
  }

  useEffect(() => {
    // Check if Supabase is configured
    if (!supabase || isDevelopmentWithoutConfig()) {
      setIsConfigured(false)
      setLoading(false)
      return
    }

    setIsConfigured(true)

    const getSession = async () => {
      try {
        // Check if supabase is null before accessing it
        if (!supabase) {
          setLoading(false)
          return
        }
        
        const { data: { session }, error } = await supabase.auth.getSession()
        if (error) {
          console.error('Error getting session:', error)
        } else {
          setSession(session)
          setUser(session?.user ?? null)
          
          if (session?.user) {
            await loadStaffData(session.user.id)
          }
        }
      } catch (error) {
        console.error('Error in getSession:', error)
      } finally {
        setLoading(false)
      }
    }

    getSession()

    // Only set up auth state change listener if supabase is configured
    let subscription: { unsubscribe: () => void } | undefined;
    
    if (supabase) {
      const { data } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        
        if (session?.user) {
          await loadStaffData(session.user.id)
        } else {
          setStaff(null)
          setUserRole(null)
        }
        setLoading(false)
      }
    )
      
      // Store the subscription for cleanup
      subscription = data.subscription;
    }

    return () => {
      // Only unsubscribe if subscription exists
      subscription?.unsubscribe()
    }
  }, [])

  const handleSignIn = async (email: string, password: string) => {
    if (!supabase) {
      return { data: null, error: new Error('Supabase is not configured') }
    }
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  }

  const handleSignOut = async () => {
    if (!supabase) {
      return { error: new Error('Supabase is not configured') }
    }
    
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const handleSignUp = async (email: string, password: string, userData: any) => {
    if (!supabase) {
      return { data: null, error: new Error('Supabase is not configured') }
    }
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    return { data, error }
  }

  const value: AuthContextType = {
    user,
    session,
    staff,
    userRole,
    loading,
    isConfigured,
    signIn: handleSignIn,
    signOut: handleSignOut,
    signUp: handleSignUp
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
