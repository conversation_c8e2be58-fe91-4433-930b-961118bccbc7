// lib/hooks/use-error-detection.ts
// React hook for error detection and correction

import { useState, useCallback, useEffect } from 'react'
import { errorDetectionService, DetectedError, ErrorDetectionResult } from '@/lib/services/error-detection-service'
import { useDebounce } from '@/lib/hooks/use-debounce'

interface UseErrorDetectionOptions {
  fieldName: string
  fieldType: string
  departmentId?: string
  context?: Record<string, any>
  language?: 'ja' | 'en'
  enableRealTime?: boolean
  debounceMs?: number
}

export function useErrorDetection({
  fieldName,
  fieldType,
  departmentId,
  context,
  language = 'ja',
  enableRealTime = true,
  debounceMs = 500
}: UseErrorDetectionOptions) {
  const [value, setValue] = useState('')
  const [errors, setErrors] = useState<DetectedError[]>([])
  const [isChecking, setIsChecking] = useState(false)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [fieldStats, setFieldStats] = useState<any>(null)
  
  const debouncedValue = useDebounce(value, debounceMs)

  // Detect errors when value changes
  useEffect(() => {
    if (!enableRealTime || !debouncedValue) {
      setErrors([])
      return
    }

    const checkErrors = async () => {
      setIsChecking(true)
      
      try {
        const result = await errorDetectionService.detectErrors(
          fieldName,
          fieldType,
          debouncedValue,
          departmentId,
          context,
          language
        )
        
        setErrors(result.errors)
      } catch (error) {
        console.error('Error detection failed:', error)
        setErrors([])
      } finally {
        setIsChecking(false)
      }
    }

    checkErrors()
  }, [debouncedValue, fieldName, fieldType, departmentId, context, language, enableRealTime])

  // Get real-time suggestions
  useEffect(() => {
    if (!value || value.length < 2) {
      setSuggestions([])
      return
    }

    const getSuggestions = async () => {
      const results = await errorDetectionService.getRealTimeSuggestions(
        fieldType,
        value,
        language
      )
      setSuggestions(results)
    }

    getSuggestions()
  }, [value, fieldType, language])

  // Load field statistics
  useEffect(() => {
    const loadStats = async () => {
      const stats = await errorDetectionService.getFieldErrorStats(fieldName, departmentId)
      setFieldStats(stats)
    }

    loadStats()
  }, [fieldName, departmentId])

  const checkErrorsManually = useCallback(async (inputValue: string) => {
    setIsChecking(true)
    
    try {
      const result = await errorDetectionService.detectErrors(
        fieldName,
        fieldType,
        inputValue,
        departmentId,
        context,
        language
      )
      
      setErrors(result.errors)
      return result
    } catch (error) {
      console.error('Error detection failed:', error)
      return { errors: [], hasErrors: false }
    } finally {
      setIsChecking(false)
    }
  }, [fieldName, fieldType, departmentId, context, language])

  const acceptCorrection = useCallback(async (correctionId: string, acceptedValue: string) => {
    await errorDetectionService.acceptCorrection(correctionId, acceptedValue)
    setValue(acceptedValue)
    setErrors([])
  }, [])

  const provideFeedback = useCallback(async (
    correctionId: string,
    feedbackType: 'helpful' | 'not_helpful' | 'incorrect',
    userFeedback?: string,
    improvedSuggestion?: string
  ) => {
    await errorDetectionService.provideFeedback({
      correctionId,
      feedbackType,
      userFeedback,
      improvedSuggestion
    })
  }, [])

  const learnFromCorrection = useCallback(async (
    originalValue: string,
    correctedValue: string,
    errorType: string
  ) => {
    await errorDetectionService.learnFromCorrection(
      fieldType,
      originalValue,
      correctedValue,
      errorType
    )
  }, [fieldType])

  return {
    value,
    setValue,
    errors,
    isChecking,
    suggestions,
    fieldStats,
    hasErrors: errors.length > 0,
    checkErrorsManually,
    acceptCorrection,
    provideFeedback,
    learnFromCorrection,
    bestSuggestion: errors.length > 0 ? errors[0].suggestions[0] : null,
    errorRate: fieldStats?.error_rate || 0
  }
}
