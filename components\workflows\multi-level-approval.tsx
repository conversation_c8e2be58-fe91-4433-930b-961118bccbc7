'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle2, XCircle, Clock, AlertCircle, ChevronRight, User, Users, Calendar, MessageSquare, Paperclip } from 'lucide-react';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { useToast } from '@/components/ui/use-toast';

interface ApprovalLevel {
  level: number;
  name: string;
  status: string;
  approvers: {
    taskId: string;
    assignedTo?: string;
    assignedRole?: string;
    status: string;
    decision?: string;
    completedAt?: string;
    completedBy?: string;
  }[];
}

interface ApprovalChainStatus {
  id: string;
  status: string;
  outcome?: string;
  currentLevel: number;
  totalLevels: number;
  levels: ApprovalLevel[];
  startedAt: string;
  completedAt?: string;
}

interface MultiLevelApprovalProps {
  taskId?: string;
  chainId?: string;
  onDecision?: (decision: string) => void;
  viewOnly?: boolean;
}
export function MultiLevelApproval({ 
  taskId, 
  chainId, 
  onDecision,
  viewOnly = false 
}: MultiLevelApprovalProps) {
  const [chainStatus, setChainStatus] = useState<ApprovalChainStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [decision, setDecision] = useState<'approve' | 'reject' | 'escalate'>('approve');
  const [comments, setComments] = useState('');
  const [delegateDialog, setDelegateDialog] = useState(false);
  const [delegateToUser, setDelegateToUser] = useState('');
  const [delegateReason, setDelegateReason] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    if (chainId) {
      fetchApprovalChainStatus();
    }
  }, [chainId]);

  const fetchApprovalChainStatus = async () => {
    try {
      const response = await fetch(`/api/workflows/approval-chains/${chainId}`);
      if (!response.ok) throw new Error('Failed to fetch approval chain status');
      const data = await response.json();
      setChainStatus(data);
    } catch (error) {
      console.error('Error fetching approval chain:', error);
      toast({
        title: 'エラー / Error',
        description: '承認チェーンの取得に失敗しました / Failed to fetch approval chain',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDecision = async () => {
    if (!taskId) return;
    
    setSubmitting(true);
    try {
      const response = await fetch('/api/workflows/approvals/decision', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskId,
          decision,          comments,
        }),
      });

      if (!response.ok) throw new Error('Failed to submit decision');

      toast({
        title: '成功 / Success',
        description: '承認決定が送信されました / Approval decision submitted',
      });

      if (onDecision) {
        onDecision(decision);
      }

      // Refresh chain status
      if (chainId) {
        await fetchApprovalChainStatus();
      }
    } catch (error) {
      console.error('Error submitting decision:', error);
      toast({
        title: 'エラー / Error',
        description: '承認決定の送信に失敗しました / Failed to submit approval decision',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelegate = async () => {
    if (!taskId || !delegateToUser || !delegateReason) return;

    setSubmitting(true);
    try {
      const response = await fetch('/api/workflows/approvals/delegate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskId,
          toUserId: delegateToUser,
          reason: delegateReason,
        }),
      });

      if (!response.ok) throw new Error('Failed to delegate');

      toast({
        title: '成功 / Success',        description: '承認が委任されました / Approval delegated successfully',
      });

      setDelegateDialog(false);
      setDelegateToUser('');
      setDelegateReason('');
    } catch (error) {
      console.error('Error delegating approval:', error);
      toast({
        title: 'エラー / Error',
        description: '承認の委任に失敗しました / Failed to delegate approval',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-orange-500" />;
      case 'in_progress':
        return <AlertCircle className="h-5 w-5 text-blue-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      approved: 'default',
      rejected: 'destructive',
      pending: 'outline',
      in_progress: 'secondary',
    };

    const labels: Record<string, string> = {
      approved: '承認済み / Approved',
      rejected: '却下 / Rejected',
      pending: '保留中 / Pending',
      in_progress: '進行中 / In Progress',
    };

    return (      <Badge variant={variants[status] || 'outline'}>
        {labels[status] || status}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!chainStatus) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          承認チェーンが見つかりません / Approval chain not found
        </AlertDescription>
      </Alert>
    );
  }

  const progress = (chainStatus.currentLevel / chainStatus.totalLevels) * 100;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>多段階承認 / Multi-Level Approval</CardTitle>
            <CardDescription className="mt-2">
              レベル {chainStatus.currentLevel} / {chainStatus.totalLevels} • 
              開始: {format(new Date(chainStatus.startedAt), 'PPp', { locale: ja })}
            </CardDescription>
          </div>
          <div className="text-right">
            {getStatusBadge(chainStatus.status)}
            {chainStatus.outcome && (
              <Badge variant={chainStatus.outcome === 'approved' ? 'default' : 'destructive'} className="ml-2">
                {chainStatus.outcome === 'approved' ? '承認完了' : '却下'}
              </Badge>
            )}
          </div>        </div>
        <Progress value={progress} className="mt-4" />
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="levels" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="levels">承認レベル / Approval Levels</TabsTrigger>
            <TabsTrigger value="action">アクション / Action</TabsTrigger>
          </TabsList>

          <TabsContent value="levels" className="space-y-4">
            {chainStatus.levels.map((level, index) => (
              <div key={level.level} className="relative">
                {index < chainStatus.levels.length - 1 && (
                  <div className="absolute left-6 top-12 bottom-0 w-px bg-gray-200" />
                )}
                
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(level.status)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">
                        レベル {level.level}: {level.name}
                      </h4>
                      {getStatusBadge(level.status)}
                    </div>
                    
                    <div className="space-y-2">
                      {level.approvers.map((approver, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            {approver.assignedTo ? (
                              <User className="h-4 w-4 text-gray-500" />
                            ) : (
                              <Users className="h-4 w-4 text-gray-500" />
                            )}
                            <span className="text-sm">
                              {approver.assignedTo ? 
                                `ユーザー: ${approver.assignedTo}` : 
                                `ロール: ${approver.assignedRole}`
                              }
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            {approver.decision && (                              <Badge variant={approver.decision === 'approve' ? 'default' : 'destructive'}>
                                {approver.decision === 'approve' ? '承認' : '却下'}
                              </Badge>
                            )}
                            {approver.completedAt && (
                              <span className="text-xs text-gray-500">
                                {format(new Date(approver.completedAt), 'PP', { locale: ja })}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </TabsContent>

          <TabsContent value="action" className="space-y-4">
            {!viewOnly && taskId && chainStatus.status === 'active' && (
              <>
                <div className="space-y-2">
                  <Label>決定 / Decision</Label>
                  <Select value={decision} onValueChange={(value: any) => setDecision(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="approve">承認 / Approve</SelectItem>
                      <SelectItem value="reject">却下 / Reject</SelectItem>
                      <SelectItem value="escalate">エスカレーション / Escalate</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="comments">コメント / Comments</Label>
                  <Textarea
                    id="comments"
                    placeholder="承認に関するコメントを入力してください / Enter comments about your decision"
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    rows={4}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleDecision}
                    disabled={submitting}                    className="flex-1"
                    variant={decision === 'reject' ? 'destructive' : 'default'}
                  >
                    {submitting ? '送信中...' : '決定を送信 / Submit Decision'}
                  </Button>

                  <Dialog open={delegateDialog} onOpenChange={setDelegateDialog}>
                    <DialogTrigger asChild>
                      <Button variant="outline">
                        委任 / Delegate
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>承認を委任 / Delegate Approval</DialogTitle>
                        <DialogDescription>
                          別のユーザーに承認を委任します / Delegate this approval to another user
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="delegateUser">委任先ユーザー / Delegate To</Label>
                          <Select value={delegateToUser} onValueChange={setDelegateToUser}>
                            <SelectTrigger>
                              <SelectValue placeholder="ユーザーを選択 / Select user" />
                            </SelectTrigger>
                            <SelectContent>
                              {/* This would be populated with actual users */}
                              <SelectItem value="user1">User 1</SelectItem>
                              <SelectItem value="user2">User 2</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="delegateReason">理由 / Reason</Label>
                          <Textarea
                            id="delegateReason"
                            placeholder="委任の理由を入力してください / Enter reason for delegation"
                            value={delegateReason}
                            onChange={(e) => setDelegateReason(e.target.value)}
                            rows={3}
                          />
                        </div>
                        
                        <Button
                          onClick={handleDelegate}
                          disabled={submitting || !delegateToUser || !delegateReason}
                          className="w-full"
                        >                          {submitting ? '処理中...' : '委任する / Delegate'}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </>
            )}

            {viewOnly && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  閲覧専用モードです / View-only mode
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}