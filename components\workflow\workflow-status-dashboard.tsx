'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { createClient } from '@/lib/supabase/client';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Loader2,
  XCircle,
  TrendingUp,
  Users,
  FileText,
  AlertCircle,
} from 'lucide-react';

interface WorkflowMetrics {
  active: number;
  completed: number;
  cancelled: number;
  suspended: number;
  sla_compliance: number;
  average_completion_time: number;
  tasks_pending: number;
  tasks_escalated: number;
}

interface WorkflowInstance {
  id: string;
  workflow_definitions: {
    name: string;
  };
  request_forms: {
    title: string;
    priority: string;
  };
  current_state: string;
  status: string;
  started_at: string;
  sla_tracking?: {
    status: string;
    target_resolution_date: string;
  }[];
}

export function WorkflowStatusDashboard() {
  const [metrics, setMetrics] = useState<WorkflowMetrics | null>(null);
  const [activeWorkflows, setActiveWorkflows] = useState<WorkflowInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const supabase = createClient();

  useEffect(() => {
    loadDashboardData();
    
    // Set up real-time subscription
    const subscription = supabase
      .channel('workflow-updates')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'workflow_instances',
      }, () => {
        loadDashboardData();
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    
    try {
      // Load metrics
      const metricsData = await calculateMetrics();
      setMetrics(metricsData);

      // Load active workflows
      const { data: workflows } = await supabase
        .from('workflow_instances')
        .select(`
          *,
          workflow_definitions!inner(name),
          request_forms!inner(title, priority),
          sla_tracking(status, target_resolution_date)
        `)
        .eq('status', 'active')
        .order('started_at', { ascending: false })
        .limit(10);

      setActiveWorkflows(workflows || []);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };
