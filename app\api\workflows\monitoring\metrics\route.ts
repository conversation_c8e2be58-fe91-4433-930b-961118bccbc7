import { NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'
    
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case '24h':
        startDate.setDate(now.getDate() - 1)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      default:
        startDate.setDate(now.getDate() - 7)
    }

    // Get total workflows
    const { count: total } = await supabase
      .from('workflow_instances')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString())

    // Get active workflows
    const { count: active } = await supabase
      .from('workflow_instances')
      .select('*', { count: 'exact', head: true })
      .in('status', ['pending', 'in_progress', 'pending_approval'])
      .gte('created_at', startDate.toISOString())

    // Get completed workflows
    const { count: completed } = await supabase
      .from('workflow_instances')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed')
      .gte('created_at', startDate.toISOString())

    // Get failed workflows
    const { count: failed } = await supabase
      .from('workflow_instances')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'failed')
      .gte('created_at', startDate.toISOString())

    // Get on hold workflows
    const { count: onHold } = await supabase
      .from('workflow_instances')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'on_hold')
      .gte('created_at', startDate.toISOString())

    // Get pending approval workflows
    const { count: pendingApproval } = await supabase
      .from('workflow_instances')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending_approval')

    // Get overdue workflows
    const { data: overdueData } = await supabase
      .from('sla_tracking')
      .select('*')
      .eq('sla_status', 'breached')
      .gte('created_at', startDate.toISOString())

    const overdue = overdueData?.length || 0

    // Calculate average completion time
    const { data: completionTimes } = await supabase
      .from('workflow_instances')
      .select('created_at, completed_at')
      .eq('status', 'completed')
      .gte('created_at', startDate.toISOString())
      .not('completed_at', 'is', null)

    let avgCompletionTime = 0
    if (completionTimes && completionTimes.length > 0) {
      const totalTime = completionTimes.reduce((acc, wf) => {
        const start = new Date(wf.created_at).getTime()
        const end = new Date(wf.completed_at).getTime()
        return acc + (end - start)
      }, 0)
      avgCompletionTime = Math.round((totalTime / completionTimes.length) / (1000 * 60 * 60)) // in hours
    }

    // Calculate SLA compliance
    const { data: slaData } = await supabase
      .from('sla_tracking')
      .select('sla_status')
      .gte('created_at', startDate.toISOString())

    let slaCompliance = 100
    if (slaData && slaData.length > 0) {
      const compliant = slaData.filter(s => s.sla_status !== 'breached').length
      slaCompliance = Math.round((compliant / slaData.length) * 100)
    }

    // Calculate throughput
    const dailyThroughput = Math.round((completed || 0) / getDaysDiff(startDate, now))
    const weeklyThroughput = Math.round(dailyThroughput * 7)
    const monthlyThroughput = Math.round(dailyThroughput * 30)

    const metrics = {
      total: total || 0,
      active: active || 0,
      completed: completed || 0,
      failed: failed || 0,
      onHold: onHold || 0,
      pendingApproval: pendingApproval || 0,
      overdue,
      avgCompletionTime,
      slaCompliance,
      throughput: {
        daily: dailyThroughput,
        weekly: weeklyThroughput,
        monthly: monthlyThroughput
      }
    }

    return NextResponse.json(metrics)
  } catch (error) {
    console.error('Error fetching workflow metrics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch workflow metrics' },
      { status: 500 }
    )
  }
}

function getDaysDiff(startDate: Date, endDate: Date): number {
  const diff = endDate.getTime() - startDate.getTime()
  return Math.max(1, Math.ceil(diff / (1000 * 60 * 60 * 24)))
}
