"use strict";(()=>{var e={};e.id=5324,e.ids=[5324],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{e.exports=require("assert")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},14985:e=>{e.exports=require("dns")},94735:e=>{e.exports=require("events")},91645:e=>{e.exports=require("net")},21820:e=>{e.exports=require("os")},33873:e=>{e.exports=require("path")},27910:e=>{e.exports=require("stream")},41204:e=>{e.exports=require("string_decoder")},34631:e=>{e.exports=require("tls")},83997:e=>{e.exports=require("tty")},79551:e=>{e.exports=require("url")},28354:e=>{e.exports=require("util")},13382:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>K,routeModule:()=>E,serverHooks:()=>U,workAsyncStorage:()=>J,workUnitAsyncStorage:()=>N});var r={};s.r(r),s.d(r,{GET:()=>$});var i=s(42706),a=s(28203),o=s(45994),n=s(39187),c=s(61487),l=s(68335),u=s(56262);class d{async executeQuery(e,t,s={}){let{cache:r=!1,cacheTTL:i=300,cacheKey:a,cacheTags:o=[e],timeout:n=3e4,retries:c=2}=s,u=Date.now(),d=a||this.generateCacheKey(e,t.toString());if(r){let e=await this.cache.get(d);if(null!==e)return{data:e,error:null,executionTime:Date.now()-u,fromCache:!0}}let h=null;for(let s=0;s<=c;s++)try{let s=await this.executeWithTimeout(()=>t(this.supabase.from(e)),n),a=Date.now()-u,c=!s.error;return(0,l.I_)(e,a,c),a>this.slowQueryThreshold&&this.logSlowQuery(e,a,t.toString()),r&&c&&s.data&&await this.cache.set(d,s.data,{ttl:i,tags:o}),{data:s.data,error:s.error,count:s.count,executionTime:a,fromCache:!1}}catch(e){h=e,s<c&&await this.delay(100*Math.pow(2,s))}let p=Date.now()-u;return(0,l.I_)(e,p,!1),{data:null,error:h,executionTime:p,fromCache:!1}}async getUserProfile(e){return this.executeQuery("user_profiles",t=>t.select(`
          *,
          organizations (
            id,
            name,
            domain,
            settings
          ),
          divisions (
            id,
            name
          ),
          groups (
            id,
            name
          )
        `).eq("user_id",e).single(),{cache:!0,cacheTTL:900,cacheKey:`user_profile:${e}`,cacheTags:["user_profiles","users",e]})}async getOrganizationSettings(e){return this.executeQuery("organizations",t=>t.select("id, name, domain, settings, created_at, updated_at").eq("id",e).single(),{cache:!0,cacheTTL:1800,cacheKey:`org_settings:${e}`,cacheTags:["organizations",e]})}async getFormSchema(e){return this.executeQuery("forms",t=>t.select(`
          *,
          form_fields (
            id,
            name,
            type,
            required,
            options,
            validation_rules,
            order_index
          )
        `).eq("id",e).eq("form_fields.active",!0).order("order_index",{foreignTable:"form_fields"}).single(),{cache:!0,cacheTTL:3600,cacheKey:`form_schema:${e}`,cacheTags:["forms","form_fields",e]})}async getTickets(e={},t={page:1,limit:20}){let{page:s,limit:r}=t,i=(s-1)*r;return this.executeQuery("tickets",t=>{let s=t.select(`
            id,
            title,
            description,
            status,
            priority,
            category,
            created_at,
            updated_at,
            requester:user_profiles!requester_id (
              full_name,
              email
            ),
            assignee:user_profiles!assignee_id (
              full_name,
              email
            ),
            organization:organizations (
              name
            )
          `).order("created_at",{ascending:!1}).range(i,i+r-1);return e.status&&(s=s.eq("status",e.status)),e.priority&&(s=s.eq("priority",e.priority)),e.category&&(s=s.eq("category",e.category)),e.organization_id&&(s=s.eq("organization_id",e.organization_id)),e.assignee_id&&(s=s.eq("assignee_id",e.assignee_id)),s},{cache:!0,cacheTTL:60,cacheKey:`tickets:${JSON.stringify(e)}:${s}:${r}`,cacheTags:["tickets"]})}async bulkInsert(e,t,s=100){let r=Date.now(),i=[],a=null;for(let r=0;r<t.length;r+=s){let o=t.slice(r,r+s);try{let{data:t,error:s}=await this.supabase.from(e).insert(o).select();if(s){a=s;break}t&&i.push(...t)}catch(e){a=e;break}}let o=Date.now()-r,n=!a;return(0,l.I_)(`${e}_bulk_insert`,o,n),await this.cache.invalidateByTags([e]),{data:n?i:null,error:a,executionTime:o,fromCache:!1}}getSlowQueries(){return[...this.slowQueries].sort((e,t)=>t.duration-e.duration)}async getPerformanceMetrics(){let e=await this.cache.getStats();return{slowQueries:{count:this.slowQueries.length,threshold:this.slowQueryThreshold,queries:this.getSlowQueries().slice(0,10)},cache:e,connectionPool:{active:5,idle:3,total:8,maxConnections:20}}}async analyzeQuery(e){try{return(await this.supabase.rpc("analyze_query",{query:e})).data}catch(e){return console.error("Query analysis error:",e),null}}async invalidateCache(e){await this.cache.invalidateByTags(e),l.qd.increment("database.cache_invalidations",{tags:e.join(",")})}async warmUpCache(){let e=Date.now();try{let{data:t}=await this.supabase.from("organizations").select("id").limit(10);if(t){let e=t.map(e=>this.getOrganizationSettings(e.id));await Promise.all(e)}let{data:s}=await this.supabase.from("forms").select("id").eq("active",!0).limit(5);if(s){let e=s.map(e=>this.getFormSchema(e.id));await Promise.all(e)}let r=Date.now()-e;l.qd.timing("database.warmup_time",r)}catch(e){console.error("Cache warmup error:",e)}}generateCacheKey(e,t){let s=this.simpleHash(t);return`query:${e}:${s}`}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return Math.abs(t).toString(36)}async executeWithTimeout(e,t){return Promise.race([e(),new Promise((e,s)=>setTimeout(()=>s(Error("Query timeout")),t))])}logSlowQuery(e,t,s){let r={query:`${e}: ${s.substring(0,200)}...`,duration:t,timestamp:new Date,stackTrace:Error().stack};this.slowQueries.push(r),this.slowQueries.length>this.maxSlowQueries&&this.slowQueries.shift(),l.qd.increment("database.slow_queries"),l.qd.timing("database.slow_query_duration",t),console.warn(`Slow query detected: ${e} took ${t}ms`)}delay(e){return new Promise(t=>setTimeout(t,e))}constructor(){this.supabase=(0,c.U)(),this.cache=(0,u.Q)(),this.slowQueries=[],this.slowQueryThreshold=1e3,this.maxSlowQueries=100}}let h=null,p=()=>(h||(h=new d),h);var m=s(866),g=s(10669);class y{constructor(e={}){this.redis=(0,g.n)(),this.handlers=new Map,this.processing=new Map,this.isRunning=!1,this.concurrency=5,this.pollInterval=1e3,this.defaultTimeout=3e4,this.concurrency=e.concurrency||5,this.pollInterval=e.pollInterval||1e3}registerHandler(e,t){this.handlers.set(e,t)}async addJob(e,t,s={}){let r={id:this.generateJobId(),type:e,data:t,priority:s.priority||0,attempts:0,maxAttempts:s.maxAttempts||3,delay:s.delay||0,createdAt:Date.now(),status:"pending"},i=this.getQueueKey(r.priority),a=JSON.stringify(r);return r.delay>0?(Date.now(),r.delay,await this.redis.set(`job:delayed:${r.id}`,a),await this.redis.expire(`job:delayed:${r.id}`,Math.ceil(r.delay/1e3))):(await this.redis.set(`job:${r.id}`,a),await this.redis.set(i,r.id)),l.qd.increment("jobs.added",{type:e,priority:r.priority.toString()}),r.id}async start(){if(this.isRunning)return;this.isRunning=!0,console.log(`Job processor started with concurrency: ${this.concurrency}`);let e=Array.from({length:this.concurrency},(e,t)=>this.workerLoop(t)),t=this.delayedJobScheduler();await Promise.all([...e,t])}async stop(){for(this.isRunning=!1;this.processing.size>0;)await this.delay(100);console.log("Job processor stopped")}async getJob(e){try{let t=await this.redis.get(`job:${e}`);return t?JSON.parse(t):null}catch(e){return console.error("Error getting job:",e),null}}async getStats(){return{pending:0,processing:this.processing.size,completed:0,failed:0,retrying:0}}async workerLoop(e){for(console.log(`Worker ${e} started`);this.isRunning;)try{let t=await this.getNextJob();t?await this.processJob(t,e):await this.delay(this.pollInterval)}catch(t){console.error(`Worker ${e} error:`,t),await this.delay(this.pollInterval)}console.log(`Worker ${e} stopped`)}async getNextJob(){for(let e of["queue:high","queue:normal","queue:low"])try{let t=await this.redis.get(e);if(t){await this.redis.del(e);let s=await this.redis.get(`job:${t}`);if(s)return JSON.parse(s)}}catch(e){console.error("Error getting next job:",e)}return null}async processJob(e,t){let s=Date.now();try{e.status="processing",e.processedAt=s,e.attempts++,this.processing.set(e.id,e),await this.updateJob(e),console.log(`Worker ${t} processing job ${e.id} (${e.type})`);let r=this.handlers.get(e.type);if(!r)throw Error(`No handler registered for job type: ${e.type}`);let i=await this.executeWithTimeout(()=>r(e),this.defaultTimeout);e.status="completed",e.completedAt=Date.now(),e.result=i;let a=Date.now()-s;l.qd.timing("jobs.processing_time",a,{type:e.type,status:"completed"}),l.qd.increment("jobs.completed",{type:e.type}),console.log(`Job ${e.id} completed in ${a}ms`)}catch(r){let t=Date.now()-s;if(e.error=r instanceof Error?r.message:String(r),e.attempts<e.maxAttempts){e.status="retrying";let t=1e3*Math.pow(2,e.attempts);setTimeout(async()=>{await this.addJob(e.type,e.data,{priority:e.priority,maxAttempts:e.maxAttempts,delay:t})},t),l.qd.increment("jobs.retried",{type:e.type}),console.log(`Job ${e.id} will retry in ${t}ms (attempt ${e.attempts}/${e.maxAttempts})`)}else e.status="failed",e.failedAt=Date.now(),l.qd.increment("jobs.failed",{type:e.type}),console.error(`Job ${e.id} failed permanently:`,r);l.qd.timing("jobs.processing_time",t,{type:e.type,status:e.status})}finally{await this.updateJob(e),this.processing.delete(e.id)}}async delayedJobScheduler(){for(;this.isRunning;)try{await this.delay(5e3)}catch(e){console.error("Delayed job scheduler error:",e),await this.delay(5e3)}}async updateJob(e){try{await this.redis.set(`job:${e.id}`,JSON.stringify(e)),("completed"===e.status||"failed"===e.status)&&await this.redis.expire(`job:${e.id}`,86400)}catch(e){console.error("Error updating job:",e)}}async executeWithTimeout(e,t){return Promise.race([e(),new Promise((e,s)=>setTimeout(()=>s(Error("Job timeout")),t))])}generateJobId(){return`job_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getQueueKey(e){return e>5?"queue:high":e<-5?"queue:low":"queue:normal"}delay(e){return new Promise(t=>setTimeout(t,e))}}let f={BACKUP_CREATE:"backup:create",BACKUP_RESTORE:"backup:restore",AI_BATCH_PROCESS:"ai:batch_process"},w=null,b=()=>(w||(w=new y),w),v=b();v.registerHandler(f.BACKUP_CREATE,async e=>{let{BackupManager:t}=await s.e(2076).then(s.bind(s,37344));return new t().createBackup(e.data.description)}),v.registerHandler(f.BACKUP_RESTORE,async e=>{let{BackupManager:t}=await s.e(2076).then(s.bind(s,37344));return new t().restoreFromBackup(e.data.backupId,e.data.options)}),v.registerHandler(f.AI_BATCH_PROCESS,async e=>{let{getAIOptimizer:t}=await Promise.resolve().then(s.bind(s,866)),r=t(),i=[];for(let t of e.data.requests){let e=await r.request(t.provider,t.model,t.prompt,t.options,"low");i.push(e)}return i});let T=require("fs");var S=s(33873),z=s.n(S);class k{constructor(e=".next"){this.analysisCache=new Map,this.buildDir=e}async analyzeBundles(){let e=await this.getBundleStats(),t=this.generateRecommendations(e),s=await this.calculatePerformanceMetrics(e);return{stats:e,recommendations:t,metrics:s}}async getBundleStats(){let e=await this.loadBuildManifest(),t=[];for(let[s,r]of Object.entries(e.pages||{})){let e=await this.analyzeBundleFiles(s,r);t.push(e)}for(let[s,r]of Object.entries(e.chunks||{})){let e=await this.analyzeBundleFiles(`chunk-${s}`,r);t.push(e)}return t}generateRecommendations(e){let t=[];for(let s of e){s.size>512e3&&t.push({type:"code-splitting",severity:"high",description:`Bundle ${s.name} is ${this.formatSize(s.size)} which is quite large`,impact:"Slow initial page load, poor user experience",solution:"Implement code splitting and lazy loading for non-critical components",estimatedSavings:.3*s.size});let e=s.modules.map(e=>e.name),r=e.filter((t,s)=>e.indexOf(t)!==s);r.length>0&&t.push({type:"dependency",severity:"medium",description:`Found ${r.length} duplicate modules in ${s.name}`,impact:"Increased bundle size and redundant code",solution:"Configure webpack to deduplicate modules or use dynamic imports",estimatedSavings:10240*r.length});let i=s.gzipSize/s.size;i>.7&&t.push({type:"compression",severity:"medium",description:`Poor compression ratio (${(100*i).toFixed(1)}%) for ${s.name}`,impact:"Larger download sizes than necessary",solution:"Enable better compression or optimize code structure",estimatedSavings:s.size*(i-.3)})}for(let s of e.flatMap(e=>e.modules).filter(e=>e.size>102400))t.push({type:"dependency",severity:"high",description:`Large dependency: ${s.name} (${this.formatSize(s.size)})`,impact:"Significant contribution to bundle size",solution:"Consider lighter alternatives or lazy loading this dependency",estimatedSavings:.5*s.size});return t.sort((e,t)=>{let s={high:3,medium:2,low:1};return s[t.severity]-s[e.severity]})}async calculatePerformanceMetrics(e){let t=e.reduce((e,t)=>e+t.size,0),s=e.reduce((e,t)=>e+t.gzipSize,0),r=this.estimateLoadTime(s),i=this.estimateParseTime(t);return{bundleSize:t,gzipSize:s,loadTime:r,parseTime:i,firstContentfulPaint:r+i+200,largestContentfulPaint:r+i+500,cumulativeLayoutShift:.1,firstInputDelay:.1*i}}async generateReport(){let e=await this.analyzeBundles(),t="# Bundle Size Analysis Report\n\n";for(let s of(t+=`## Summary

- Total Bundle Size: ${this.formatSize(e.metrics.bundleSize)}
- Gzipped Size: ${this.formatSize(e.metrics.gzipSize)}
- Estimated Load Time: ${e.metrics.loadTime.toFixed(0)}ms
- Estimated Parse Time: ${e.metrics.parseTime.toFixed(0)}ms

## Bundle Breakdown

| Bundle | Size | Gzipped | Modules |
|--------|------|---------|----------|
`,e.stats))t+=`| ${s.name} | ${this.formatSize(s.size)} | ${this.formatSize(s.gzipSize)} | ${s.modules.length} |
`;if(t+="\n## Optimization Recommendations\n\n",0===e.recommendations.length)t+="No major optimization opportunities found. Great job! \uD83C\uDF89\n\n";else for(let s of e.recommendations){let e="high"===s.severity?"\uD83D\uDD34":"medium"===s.severity?"\uD83D\uDFE1":"\uD83D\uDFE2";t+=`### ${e} ${s.type.toUpperCase()} - ${s.severity.toUpperCase()}

**Issue:** ${s.description}

**Impact:** ${s.impact}

**Solution:** ${s.solution}

**Estimated Savings:** ${this.formatSize(s.estimatedSavings)}

---

`}t+=`## Performance Metrics

- **First Contentful Paint:** ${e.metrics.firstContentfulPaint.toFixed(0)}ms
- **Largest Contentful Paint:** ${e.metrics.largestContentfulPaint.toFixed(0)}ms
- **First Input Delay:** ${e.metrics.firstInputDelay.toFixed(0)}ms
- **Cumulative Layout Shift:** ${e.metrics.cumulativeLayoutShift.toFixed(3)}

`;let s=e.recommendations.reduce((e,t)=>e+t.estimatedSavings,0);return s>0&&(t+=`## Potential Savings

By implementing all recommendations, you could potentially save **${this.formatSize(s)}** (${(s/e.metrics.bundleSize*100).toFixed(1)}% reduction).

`),t}async trackBundleSize(){let e=await this.analyzeBundles(),t={timestamp:new Date().toISOString(),totalSize:e.metrics.bundleSize,gzipSize:e.metrics.gzipSize,bundles:e.stats.map(e=>({name:e.name,size:e.size,gzipSize:e.gzipSize}))},s=z().join(this.buildDir,"bundle-tracking.json"),r=[];try{let e=await T.promises.readFile(s,"utf-8");r=JSON.parse(e)}catch{}r.push(t),r.length>30&&(r=r.slice(-30)),await T.promises.writeFile(s,JSON.stringify(r,null,2))}async loadBuildManifest(){try{let e=z().join(this.buildDir,"build-manifest.json"),t=await T.promises.readFile(e,"utf-8");return JSON.parse(t)}catch(e){return console.warn("Could not load build manifest:",e),{pages:{},chunks:{}}}}async analyzeBundleFiles(e,t){let s=[],r=[],i=[],a=0,o=0;for(let r of t)try{let t=z().join(this.buildDir,"static",r),n=(await T.promises.stat(t)).size;a+=n,o+=.3*n,i.push({name:r,size:n,type:z().extname(r),compressed:!1}),s.push({name:r,size:n,chunks:[e],reasons:["entry"]})}catch(e){console.warn(`Could not analyze file ${r}:`,e)}return r.push({name:e,size:a,modules:t,parents:[],children:[]}),{name:e,size:a,gzipSize:o,modules:s,chunks:r,assets:i}}formatSize(e){let t=["B","KB","MB","GB"],s=e,r=0;for(;s>=1024&&r<t.length-1;)s/=1024,r++;return`${s.toFixed(1)} ${t[r]}`}estimateLoadTime(e){return e/209715.2*1e3}estimateParseTime(e){return e/1024}}class _{constructor(){this.metrics=[],this.observers=[],this.thresholds={CLS:{good:.1,poor:.25},FID:{good:100,poor:300},FCP:{good:1800,poor:3e3},LCP:{good:2500,poor:4e3},TTFB:{good:800,poor:1800},INP:{good:200,poor:500}}}initializeObservers(){this.observeMetric("largest-contentful-paint",e=>{let t=e[e.length-1];this.recordMetric("LCP",t.startTime,t.id)}),this.observeMetric("first-input",e=>{let t=e[0],s=t.processingStart-t.startTime;this.recordMetric("FID",s,t.id)}),this.observeMetric("layout-shift",e=>{let t=0;for(let s of e)s.hadRecentInput||(t+=s.value);this.recordMetric("CLS",t,"cls-session")}),this.observeMetric("paint",e=>{let t=e.find(e=>"first-contentful-paint"===e.name);t&&this.recordMetric("FCP",t.startTime,t.id)}),this.observeMetric("navigation",e=>{let t=e[0];if(t){let e=t.responseStart-t.requestStart;this.recordMetric("TTFB",e,t.id)}}),"PerformanceEventTiming"in window&&this.observeMetric("event",e=>{for(let t of e)t.duration>40&&this.recordMetric("INP",t.duration,t.id)})}observeMetric(e,t){try{let s=new PerformanceObserver(e=>{t(e.getEntries())});s.observe({type:e,buffered:!0}),this.observers.push(s)}catch(t){console.warn(`Could not observe ${e}:`,t)}}recordMetric(e,t,s){let r=this.getRating(e,t),i={name:e,value:t,rating:r,delta:t,id:s,navigationType:this.getNavigationType(),timestamp:Date.now()},a=this.metrics.findIndex(t=>t.name===e&&t.id===s);if(a>=0){let e=this.metrics[a];i.delta=t-e.value,this.metrics[a]=i}else this.metrics.push(i);this.sendMetricToAnalytics(i)}getRating(e,t){let s=this.thresholds[e];return!s||t<=s.good?"good":t<=s.poor?"needs-improvement":"poor"}getNavigationType(){return"unknown"}sendMetricToAnalytics(e){l.qd.timing(`web_vitals.${e.name.toLowerCase()}`,e.value,{rating:e.rating,navigation_type:e.navigationType})}setupVisibilityChangeHandler(){document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&this.sendFinalMetrics()}),window.addEventListener("beforeunload",()=>{this.sendFinalMetrics()})}sendFinalMetrics(){if(navigator.sendBeacon&&this.metrics.length>0){let e=JSON.stringify({url:window.location.href,metrics:this.metrics,timestamp:Date.now(),userAgent:navigator.userAgent});navigator.sendBeacon("/api/analytics/web-vitals",e)}}getMetrics(){return[...this.metrics]}getReport(){let e=this.getLatestMetrics(),t=this.calculateScore(e),s=this.getOverallRating(t),r=this.generateRecommendations(e);return{metrics:e,summary:{score:t,rating:s,recommendations:r},trends:this.calculateTrends()}}getLatestMetrics(){let e={};for(let t of this.metrics)(!e[t.name]||t.timestamp>e[t.name].timestamp)&&(e[t.name]=t);return Object.values(e)}calculateScore(e){if(0===e.length)return 0;let t={LCP:.25,FID:.25,CLS:.25,FCP:.15,TTFB:.05,INP:.05},s=0,r=0;for(let i of e){let e=t[i.name]||0;s+=("good"===i.rating?100:"needs-improvement"===i.rating?50:0)*e,r+=e}return r>0?Math.round(s/r):0}getOverallRating(e){return e>=90?"good":e>=50?"needs-improvement":"poor"}generateRecommendations(e){let t=[];for(let s of e)if("poor"===s.rating)switch(s.name){case"LCP":t.push("Optimize images and reduce server response times to improve LCP");break;case"FID":t.push("Reduce JavaScript execution time and optimize event handlers for better FID");break;case"CLS":t.push("Set explicit dimensions for images and ads to prevent layout shifts");break;case"FCP":t.push("Optimize critical rendering path and reduce render-blocking resources");break;case"TTFB":t.push("Optimize server response times and consider using a CDN");break;case"INP":t.push("Optimize JavaScript execution and reduce main thread blocking")}return t}calculateTrends(){return{}}cleanup(){for(let e of this.observers)e.disconnect();this.observers=[]}}let x=null,q=()=>(x||(x=new _),x);async function $(e){try{let t=(0,c.U)(),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:i}=await t.from("user_profiles").select("role").eq("user_id",s.id).single();if(!i||!["admin","system_admin"].includes(i.role))return n.NextResponse.json({error:"Insufficient permissions"},{status:403});let a=new URL(e.url).searchParams.get("section")||"overview",o={};switch(a){case"overview":o=await M();break;case"database":o=await C();break;case"cache":o=await P();break;case"ai":o=await R();break;case"jobs":o=await j();break;case"frontend":o=await B();break;case"bundle":o=await I();break;default:return n.NextResponse.json({error:"Invalid section"},{status:400})}return n.NextResponse.json({success:!0,section:a,data:o,timestamp:new Date().toISOString()})}catch(e){return console.error("Performance API error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function M(){let e=(0,u.Q)(),t=p(),s=(0,m.getAIOptimizer)(),r=b(),[i,a,o,n,c]=await Promise.all([function(){let e=process.memoryUsage(),t=process.uptime();return{memory:{used:Math.round(e.heapUsed/1024/1024),total:Math.round(e.heapTotal/1024/1024),external:Math.round(e.external/1024/1024),rss:Math.round(e.rss/1024/1024)},uptime:Math.round(t),nodeVersion:process.version,platform:process.platform,arch:process.arch}}(),e.getStats(),t.getPerformanceMetrics(),s.getPerformanceMetrics(),r.getStats()]);return{system:i,cache:{hitRate:a.redis?.hitRate||0,connected:a.redis?.connected||!1,memoryUsage:a.memory?.size||0},database:{slowQueries:o.slowQueries?.count||0,avgQueryTime:D(),connectionPool:o.connectionPool},ai:{requestsToday:n.costs?.today||0,costToday:n.costs?.today||0,cacheHitRate:n.cache?.hitRate||0},jobs:{pending:c.pending,processing:c.processing,failed:c.failed},alerts:await L()}}async function C(){let e=p(),t=await e.getPerformanceMetrics();return{slowQueries:t.slowQueries?.queries||[],queryStats:{total:A("database.queries"),slow:A("database.slow_queries"),errors:A("database.errors"),avgTime:D()},connectionPool:t.connectionPool,tableStats:await O(),indexUsage:await Q(),recommendations:function(e){let t=[];return e.slowQueries?.count>10&&t.push("Consider optimizing slow queries or adding indexes"),e.connectionPool?.active>e.connectionPool?.total*.8&&t.push("Consider increasing database connection pool size"),t}(t)}}async function P(){let e=(0,u.Q)(),t=await e.getStats();return{redis:t.redis,memory:t.memory,tags:t.tags,performance:{hitRate:t.redis?.hitRate||0,avgResponseTime:A("cache.get_time"),operations:{gets:A("cache.gets"),sets:A("cache.sets"),deletes:A("cache.deletes"),hits:A("cache.hits"),misses:A("cache.misses")}},recommendations:function(e){let t=[];return e.redis?.hitRate<80&&t.push("Cache hit rate is low, consider increasing TTL or cache more data"),e.redis?.connected||t.push("Redis is not connected, check Redis server status"),t}(t)}}async function R(){let e=(0,m.getAIOptimizer)(),t=await e.getPerformanceMetrics(),s=e.getCostAnalytics();return{costs:s,performance:{avgResponseTime:A("ai.response_time"),cacheHitRate:t.cache?.hitRate||0,requestsToday:A("ai.requests"),errorsToday:A("ai.errors")},queue:t.queue,usage:{byProvider:s.byProvider,byModel:s.byModel,trends:s.trends},recommendations:function(e,t){let s=[];return t.today>10&&s.push("Daily AI costs are high, consider implementing more aggressive caching"),e.cache?.hitRate<50&&s.push("AI cache hit rate is low, consider longer cache TTL for similar requests"),s}(t,s)}}async function j(){let e=b(),t=await e.getStats();return{queue:t,performance:{avgProcessingTime:A("jobs.processing_time"),completionRate:function(){let e=A("jobs.completed"),t=e+A("jobs.failed");return t>0?Math.round(e/t*100):100}(),throughput:A("jobs.completed")},jobTypes:{"backup:create":{completed:5,failed:0,avgTime:3e4},"ai:batch_process":{completed:25,failed:1,avgTime:5e3},"email:send":{completed:100,failed:2,avgTime:1e3}},recommendations:function(e){let t=[];return e.pending>100&&t.push("High number of pending jobs, consider increasing worker concurrency"),e.failed>.1*e.completed&&t.push("High job failure rate, check job handlers and error handling"),t}(t)}}async function B(){let e=q().getReport();return{webVitals:e,performance:{pageLoadTime:A("frontend.page_load_time"),resourceLoadTime:A("frontend.resource_load_time"),jsErrors:A("frontend.js_errors")},recommendations:e.summary.recommendations}}async function I(){let e=new k,t=await e.analyzeBundles();return{bundles:t.stats,metrics:t.metrics,recommendations:t.recommendations,trends:await F()}}function A(e){let t=l.qd.getMetrics(e);return t&&0!==t.length?t.filter(e=>e.timestamp>Date.now()-864e5).reduce((e,t)=>e+t.value,0):0}function D(){let e=l.qd.getMetrics("database.query_time");if(!e||0===e.length)return 0;let t=e.filter(e=>e.timestamp>Date.now()-36e5);return 0===t.length?0:Math.round(t.reduce((e,t)=>e+t.value,0)/t.length)}async function O(){return{tickets:{rows:1500,size:"2.5MB",lastVacuum:"2024-01-01"},users:{rows:250,size:"500KB",lastVacuum:"2024-01-01"},organizations:{rows:10,size:"50KB",lastVacuum:"2024-01-01"}}}async function Q(){return{tickets_status_idx:{usage:95,scans:1e3},tickets_created_at_idx:{usage:80,scans:500},users_email_idx:{usage:100,scans:200}}}async function F(){return{"2024-01-01":{size:5e5,gzipSize:15e4},"2024-01-02":{size:505e3,gzipSize:152e3},"2024-01-03":{size:498e3,gzipSize:149e3}}}async function L(){let e=[],t=D();t>1e3&&e.push({type:"warning",category:"database",message:`Average query time is ${t}ms (threshold: 1000ms)`,severity:"medium"});let s=process.memoryUsage().heapUsed/1024/1024;return s>500&&e.push({type:"warning",category:"system",message:`High memory usage: ${Math.round(s)}MB`,severity:"high"}),e}let E=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/performance/route",pathname:"/api/admin/performance",filename:"route",bundlePath:"app/api/admin/performance/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\performance\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:J,workUnitAsyncStorage:N,serverHooks:U}=E;function K(){return(0,o.patchFetch)({workAsyncStorage:J,workUnitAsyncStorage:N})}},866:(e,t,s)=>{s.d(t,{getAIOptimizer:()=>d});var r=s(56262),i=s(68335);class a{constructor(){this.chat={completions:{create:async e=>{if(await new Promise(e=>setTimeout(e,2e3*Math.random()+500)),.05>Math.random())throw Error("OpenAI API Error: Rate limit exceeded");let t=Math.floor(e.messages[0].content.length/4),s=Math.floor(500*Math.random())+50;return{id:`chatcmpl-${Math.random().toString(36).substr(2,9)}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:e.model,choices:[{index:0,message:{role:"assistant",content:`Mock response from ${e.model} for: "${e.messages[0].content.substring(0,50)}..."`},finish_reason:"stop"}],usage:{prompt_tokens:t,completion_tokens:s,total_tokens:t+s}}}}}}}let o=new a;class n{constructor(){this.messages={create:async e=>{if(await new Promise(e=>setTimeout(e,2e3*Math.random()+500)),.05>Math.random())throw Error("Anthropic API Error: Rate limit exceeded");let t=Math.floor(e.messages[0].content.length/4),s=Math.floor(500*Math.random())+50;return{id:`msg_${Math.random().toString(36).substr(2,9)}`,type:"message",role:"assistant",content:[{type:"text",text:`Mock response from ${e.model} for: "${e.messages[0].content.substring(0,50)}..."`}],model:e.model,stop_reason:"end_turn",stop_sequence:null,usage:{input_tokens:t,output_tokens:s}}}}}}let c=new n;class l{constructor(){this.cache=(0,r.Q)(),this.requestQueue=[],this.batchQueue=[],this.costTracker={daily:{},monthly:{},byProvider:{},byModel:{},total:0},this.batchSize=10,this.batchTimeout=5e3,this.maxRetries=3,this.cacheTTL=3600,this.processingBatch=!1,setInterval(()=>this.processBatchQueue(),this.batchTimeout),this.loadCostTracker()}async request(e,t,s,r={},a="normal",o,n,c="general"){let l=this.generateRequestId(),u=Date.now(),d=this.generateCacheKey(e,t,s,r),h=await this.cache.get(d);return h?((0,i.hf)(e,t,Date.now()-u,h.usage.totalTokens,h.cost),{...h,fromCache:!0,duration:Date.now()-u}):"high"===a?this.processRequest({id:l,provider:e,model:t,prompt:s,options:r,priority:a,timestamp:u,retries:0}):new Promise((i,o)=>{let n={id:l,provider:e,model:t,prompt:s,options:{...r,resolve:i,reject:o},priority:a,timestamp:u,retries:0};this.requestQueue.push(n),(this.requestQueue.length>=this.batchSize||"normal"===a)&&this.processBatchQueue()})}async processRequest(e){let t=Date.now();try{let s,r,a;"openai"===e.provider?(r=(s=await this.callOpenAI(e)).usage,a=this.calculateOpenAICost(e.model,r)):(r=(s=await this.callAnthropic(e)).usage,a=this.calculateAnthropicCost(e.model,r));let o=Date.now()-t,n=this.extractContent(s,e.provider),c={id:e.id,content:n,usage:{inputTokens:r.prompt_tokens||r.input_tokens||0,outputTokens:r.completion_tokens||r.output_tokens||0,totalTokens:r.total_tokens||r.input_tokens+r.output_tokens||0},cost:a,duration:o,fromCache:!1,provider:e.provider,model:e.model},l=this.generateCacheKey(e.provider,e.model,e.prompt,e.options);return await this.cache.set(l,c,{ttl:this.cacheTTL,tags:["ai_responses",e.provider,e.model]}),(0,i.hf)(e.provider,e.model,o,c.usage.totalTokens,a),this.updateCostTracker(e.provider,e.model,a),c}catch(r){if(e.retries<this.maxRetries)return e.retries++,await this.delay(1e3*Math.pow(2,e.retries)),this.processRequest(e);let s=Date.now()-t;throw(0,i.hf)(e.provider,e.model,s,0,0),r}}async processBatchQueue(){if(!this.processingBatch&&0!==this.requestQueue.length){this.processingBatch=!0;try{for(let e of this.groupRequestsForBatching())await this.processBatch(e)}finally{this.processingBatch=!1}}}groupRequestsForBatching(){let e={};for(let t of this.requestQueue.splice(0,3*this.batchSize)){let s=`${t.provider}:${t.model}`;e[s]||(e[s]=[]),e[s].push(t)}let t=[];for(let s of Object.values(e))for(let e=0;e<s.length;e+=this.batchSize)t.push(s.slice(e,e+this.batchSize));return t}async processBatch(e){let t=e.map(e=>this.processRequest(e).then(t=>{e.options.resolve&&e.options.resolve(t)}).catch(t=>{e.options.reject&&e.options.reject(t)}));await Promise.allSettled(t)}async callOpenAI(e){return o.chat.completions.create({model:e.model,messages:[{role:"user",content:e.prompt}],...e.options})}async callAnthropic(e){return c.messages.create({model:e.model,max_tokens:e.options.max_tokens||1e3,messages:[{role:"user",content:e.prompt}],...e.options})}extractContent(e,t){return"openai"===t?e.choices[0]?.message?.content||"":e.content[0]?.text||""}calculateOpenAICost(e,t){let s={"gpt-4":{input:.03,output:.06},"gpt-4-turbo":{input:.01,output:.03},"gpt-3.5-turbo":{input:.0015,output:.002}},r=s[e]||s["gpt-3.5-turbo"];return t.prompt_tokens/1e3*r.input+t.completion_tokens/1e3*r.output}calculateAnthropicCost(e,t){let s={"claude-3-opus":{input:.015,output:.075},"claude-3-sonnet":{input:.003,output:.015},"claude-3-haiku":{input:25e-5,output:.00125}},r=s[e]||s["claude-3-haiku"];return t.input_tokens/1e3*r.input+t.output_tokens/1e3*r.output}updateCostTracker(e,t,s){let r=new Date().toISOString().split("T")[0],i=r.substring(0,7);this.costTracker.daily[r]=(this.costTracker.daily[r]||0)+s,this.costTracker.monthly[i]=(this.costTracker.monthly[i]||0)+s,this.costTracker.byProvider[e]=(this.costTracker.byProvider[e]||0)+s,this.costTracker.byModel[t]=(this.costTracker.byModel[t]||0)+s,this.costTracker.total+=s,this.saveCostTracker()}getCostAnalytics(){let e=new Date().toISOString().split("T")[0],t=e.substring(0,7);return{today:this.costTracker.daily[e]||0,thisMonth:this.costTracker.monthly[t]||0,total:this.costTracker.total,byProvider:this.costTracker.byProvider,byModel:this.costTracker.byModel,trends:this.calculateCostTrends()}}async getPerformanceMetrics(){let e=await this.cache.getStats();return{queue:{pending:this.requestQueue.length,processing:this.processingBatch},cache:{hitRate:e.redis?.hitRate||0,size:e.memory?.size||0},costs:this.getCostAnalytics()}}async clearCache(){await this.cache.invalidateByTags(["ai_responses"])}generateRequestId(){return`ai_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateCacheKey(e,t,s,r){let i=this.hashObject(r),a=this.hashString(s);return`ai:${e}:${t}:${a}:${i}`}hashString(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return Math.abs(t).toString(36)}hashObject(e){return this.hashString(JSON.stringify(e))}delay(e){return new Promise(t=>setTimeout(t,e))}calculateCostTrends(){let e={},t=new Date;for(let s=6;s>=0;s--){let r=new Date(t);r.setDate(r.getDate()-s);let i=r.toISOString().split("T")[0];e[i]=this.costTracker.daily[i]||0}return e}loadCostTracker(){}saveCostTracker(){}}let u=null,d=()=>(u||(u=new l),u)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8096,2076],()=>s(13382));module.exports=r})();