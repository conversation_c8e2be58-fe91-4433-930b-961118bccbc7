(()=>{var e={};e.id=1401,e.ids=[1401],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},56174:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>o});var a=s(70260),i=s(28203),n=s(25155),r=s.n(n),c=s(67292),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let o=["",{children:["test-ai-agents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,97773)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-agents\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-agents\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-ai-agents/page",pathname:"/test-ai-agents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},57191:(e,t,s)=>{Promise.resolve().then(s.bind(s,97773))},87455:(e,t,s)=>{Promise.resolve().then(s.bind(s,58419))},58419:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(45512),i=s(58009),n=s(97643),r=s(69193),c=s(87021),l=s(77252),o=s(3328),d=s(75339),u=s(78896),p=s(61594),g=s(4269),m=s(78397),h=s(64977),x=s(26658),f=s(37133),y=s(95379);class v{constructor(e,t){this.agentId=e,this.specialization=t,this.lastActivityTime=new Date,this.learningHistory=new Map}async runAgentLoop(){for(console.log(`[${this.agentId}] Agent starting...`);;)try{for(let e of(await this.identifyResearchTopics())){let t=await this.scrapeWebContent(e),s=await this.transformToEducational(t,e),a=await this.createInteractiveExperience(s);await this.storeInKnowledgeBase(a),await this.updateEmbeddings(a)}await this.learnFromFeedback(),await this.sleep(3e5)}catch(e){console.error(`[${this.agentId}] Agent error:`,e),await this.sleep(6e4)}}async identifyResearchTopics(){let e=[],{data:t}=await f.N.from("kb_search_history").select("query").eq("search_type","ai").or("clicked_results.is.null,clicked_results.eq.{}").ilike("query",`%${this.specialization.serviceCategory}%`).gte("created_at",new Date(Date.now()-864e5).toISOString()).limit(20);t&&e.push(...t.map(e=>e.query));let{data:s}=await f.N.from("kb_content_gaps").select("topic").eq("suggested_category",this.specialization.serviceCategory).eq("status","identified").limit(10);s&&e.push(...s.map(e=>e.topic));let{data:a}=await f.N.from("kb_article_feedback").select("suggestion").in("feedback_type",["not_helpful","outdated"]).not("suggestion","is",null).limit(10);return a&&e.push(...a.map(e=>e.suggestion)),[...new Set(e)].slice(0,5)}async scrapeWebContent(e){let t=[],s=`${e} ${this.specialization.expertise.join(" ")} tutorial guide`;try{for(let a of(await this.performWebSearch(s)).slice(0,5)){let s=await this.scrapeUrl(a.url);s&&t.push({url:a.url,title:a.title,content:s,relevance:await this.calculateRelevance(s,e),lastUpdated:new Date})}}catch(e){console.error(`[${this.agentId}] Web scraping error:`,e)}return t.sort((e,t)=>t.relevance-e.relevance)}async transformToEducational(e,t){let s=e.slice(0,3).map(e=>e.content).join("\n\n"),a=this.buildEducationalPrompt(t,s),i=await y.aiApiService.generateText(a,{temperature:.3,maxTokens:3e3});return this.parseEducationalContent(i,t)}async createInteractiveExperience(e){if(!e.interactive||!e.steps)return e;let t=await Promise.all(e.steps.map(async e=>(!e.visualAid&&this.shouldHaveVisual(e)&&(e.visualAid=await this.generateVisualAid(e)),!e.validation&&e.userAction&&(e.validation=await this.generateValidation(e)),e.troubleshooting&&0!==e.troubleshooting.length||(e.troubleshooting=await this.generateTroubleshooting(e)),e)));return e.steps=t,e}async storeInKnowledgeBase(e){try{let t=await this.findSimilarContent(e.title);if(t)await f.N.from("kb_articles").update({content_en:e.content,content_jp:e.contentJp,metadata:{type:e.type,difficulty:e.difficulty,estimatedTime:e.estimatedTime,interactive:e.interactive,steps:e.steps,lastUpdatedByAgent:this.agentId,agentVersion:"1.0"},updated_at:new Date().toISOString(),last_auto_update:new Date().toISOString()}).eq("id",t.id);else{let{data:t,error:s}=await f.N.from("kb_articles").insert({title_en:e.title,title_jp:e.titleJp,content_en:e.content,content_jp:e.contentJp,category:this.specialization.serviceCategory,tags:e.relatedTopics||[],status:"published",author_id:null,metadata:{type:e.type,difficulty:e.difficulty,estimatedTime:e.estimatedTime,interactive:e.interactive,steps:e.steps,createdByAgent:this.agentId,agentVersion:"1.0"}}).select().single();if(s)throw s;await this.logActivity("article_created",{articleId:t.id,title:e.title,type:e.type})}}catch(e){console.error(`[${this.agentId}] Error storing content:`,e)}}async updateEmbeddings(e){}async learnFromFeedback(){let{data:e}=await f.N.from("kb_article_feedback").select(`
        *,
        article:kb_articles!inner(
          id,
          metadata
        )
      `).eq("article.metadata->>createdByAgent",this.agentId).gte("created_at",new Date(Date.now()-864e5).toISOString());if(e&&e.length>0){let t=this.analyzeFeedbackPatterns(e);t.forEach((e,t)=>{this.learningHistory.set(t,e)}),await this.adjustBehavior(t)}}async performWebSearch(e){return[]}async scrapeUrl(e){return null}async calculateRelevance(e,t){let s=t.toLowerCase().split(" "),a=e.toLowerCase(),i=0;return s.forEach(e=>{a.includes(e)&&i++}),i/s.length}async findSimilarContent(e){let{data:t}=await f.N.from("kb_articles").select("id, title_en").eq("category",this.specialization.serviceCategory).ilike("title_en",`%${e}%`).limit(1).single();return t}async logActivity(e,t){await f.N.from("agent_activity_logs").insert({agent_id:this.agentId,action:e,details:t,created_at:new Date().toISOString()})}async sleep(e){return new Promise(t=>setTimeout(t,e))}}class j extends v{constructor(){super("group-mail-agent",{serviceCategory:"group_mail",expertise:["email distribution lists","group mail management","Microsoft Exchange","email permissions","distribution group best practices"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!1,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are an expert in email and group mail management, specializing in making complex concepts simple for non-technical users.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Explains the concept in simple, non-technical terms
2. Provides step-by-step instructions with clear actions
3. Includes common mistakes to avoid
4. Offers troubleshooting tips for typical issues
5. Uses analogies that office workers can relate to

Format the response as JSON:
{
  "type": "guide" | "tutorial" | "wizard",
  "title": "English title",
  "titleJp": "Japanese title",
  "content": "Main content in simple English",
  "contentJp": "Main content in simple Japanese",
  "difficulty": "beginner",
  "estimatedTime": 10,
  "interactive": true,
  "steps": [
    {
      "order": 1,
      "title": "Step title",
      "titleJp": "Japanese step title",
      "description": "What to do",
      "descriptionJp": "Japanese description",
      "userAction": "Click on...",
      "troubleshooting": ["If you see X, try Y"]
    }
  ],
  "relatedTopics": ["topic1", "topic2"]
}

Focus on making it accessible for someone who has never managed email groups before.
`}parseEducationalContent(e,t){try{let s=JSON.parse(e);return{type:s.type||"guide",title:s.title||t,titleJp:s.titleJp||t,content:s.content||"",contentJp:s.contentJp||"",difficulty:"beginner",estimatedTime:s.estimatedTime||15,interactive:!0,steps:s.steps||[],relatedTopics:s.relatedTopics||[]}}catch(e){return{type:"guide",title:`Guide: ${t}`,titleJp:`ガイド: ${t}`,content:`Learn about ${t}`,contentJp:`${t}について学ぶ`,difficulty:"beginner",estimatedTime:10,interactive:!1}}}shouldHaveVisual(e){return["click","select","button","menu","screen"].some(t=>e.description.toLowerCase().includes(t))}async generateVisualAid(e){return`/images/group-mail/step-${e.order}.png`}async generateValidation(e){return e.userAction?.includes("email address")?"Check if email address format is valid":e.userAction?.includes("select")?"Verify at least one option is selected":"Confirm action was completed"}async generateTroubleshooting(e){let t=[];return e.description.includes("permission")&&t.push('If you see "Access Denied", contact your department administrator'),e.description.includes("email")&&(t.push("If the email address is not found, check the spelling"),t.push("For external emails, you may need special approval")),e.description.includes("group")&&t.push("If the group doesn't appear, it may not be assigned to your department"),t}analyzeFeedbackPatterns(e){let t=new Map,s=e.map(e=>e.feedback_type),a=new Map;s.forEach(e=>{a.set(e,(a.get(e)||0)+1)}),t.set("commonIssues",Array.from(a.entries())),t.set("totalFeedback",e.length);let i=e.filter(e=>e.suggestion).map(e=>e.suggestion);return t.set("userSuggestions",i),t}async adjustBehavior(e){for(let[t,s]of e.get("commonIssues")||[])"outdated"===t&&s>3&&console.log(`[${this.agentId}] Increasing update frequency due to outdated content feedback`),"not_helpful"===t&&s>5&&console.log(`[${this.agentId}] Simplifying content due to not helpful feedback`)}}class w extends v{constructor(){super("sharepoint-agent",{serviceCategory:"sharepoint",expertise:["SharePoint Online","document libraries","permissions management","SharePoint sites","Microsoft 365 integration"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a SharePoint expert who specializes in teaching non-technical users.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Demystifies SharePoint concepts using office analogies (folders, filing cabinets, etc.)
2. Provides visual step-by-step instructions
3. Includes Japanese translations for all UI elements
4. Covers common permission scenarios
5. Explains best practices for document organization

Include specific examples relevant to Japanese corporate culture.

Format as JSON with the same structure as before, ensuring all content is beginner-friendly.
`}parseEducationalContent(e,t){try{let s=JSON.parse(e);return{type:s.type||"tutorial",title:s.title||t,titleJp:s.titleJp||t,content:s.content||"",contentJp:s.contentJp||"",difficulty:"beginner",estimatedTime:s.estimatedTime||20,interactive:!0,steps:s.steps||[],relatedTopics:s.relatedTopics||[]}}catch(e){return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"tutorial",title:`SharePoint Tutorial: ${e}`,titleJp:`SharePointチュートリアル: ${e}`,content:`Learn how to use SharePoint for ${e}`,contentJp:`SharePointを使用して${e}を行う方法を学ぶ`,difficulty:"beginner",estimatedTime:15,interactive:!1}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return`/images/sharepoint/step-${e.order}-${e.title.toLowerCase().replace(/\s+/g,"-")}.png`}async generateValidation(e){return e.userAction?.includes("permission")?"Verify permission level is set correctly":e.userAction?.includes("upload")?"Check if file was uploaded successfully":e.userAction?.includes("create")?"Confirm new item was created":"Verify action completed successfully"}async generateTroubleshooting(e){let t=[];return t.push("If you cannot see the SharePoint site, check with your administrator"),t.push("For upload errors, check file size limits (usually 250GB)"),t.push("If permissions don't work, wait 5 minutes for changes to apply"),t}analyzeFeedbackPatterns(e){let t=new Map,s=e.filter(e=>e.suggestion?.toLowerCase().includes("permission")).length,a=e.filter(e=>e.suggestion?.toLowerCase().includes("find")||e.suggestion?.toLowerCase().includes("navigate")).length;return t.set("permissionIssues",s),t.set("navigationIssues",a),t.set("totalFeedback",e.length),t}async adjustBehavior(e){let t=e.get("permissionIssues")||0,s=e.get("navigationIssues")||0;t>3&&console.log(`[${this.agentId}] Creating more permission-focused content`),s>3&&console.log(`[${this.agentId}] Creating more navigation guides`)}}class b extends v{constructor(){super("password-reset-agent",{serviceCategory:"password_reset",expertise:["password management","account recovery","multi-factor authentication","security best practices","Microsoft 365 accounts"],languages:["en","jp"],targetAudience:"all staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a security expert who helps non-technical users with password and account issues.

Topic: ${e}

Create a guide that:
1. Emphasizes security without causing anxiety
2. Uses simple language and clear instructions
3. Includes memory tips for passwords
4. Covers MFA setup in detail
5. Provides emergency contact information

Make it reassuring and helpful, not intimidating.
`}parseEducationalContent(e,t){try{return JSON.parse(e)}catch{return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"wizard",title:`Password Help: ${e}`,titleJp:`パスワードヘルプ: ${e}`,content:"Step-by-step password assistance",contentJp:"ステップバイステップのパスワード支援",difficulty:"beginner",estimatedTime:5,interactive:!0}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return`/images/password/step-${e.order}-secure.png`}async generateValidation(e){return"Verify security requirements are met"}async generateTroubleshooting(e){return["If you're locked out, wait 30 minutes before trying again","For immediate help, contact IT support at extension 1234","Keep your recovery email up to date"]}analyzeFeedbackPatterns(e){return new Map}async adjustBehavior(e){}}class A extends v{constructor(){super("pc-admin-agent",{serviceCategory:"pc_admin",expertise:["Windows administration","software installation","admin privileges","security policies","software deployment","troubleshooting Windows issues"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a Windows PC expert helping non-technical office workers understand administrative tasks.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Explains why admin rights are needed in simple terms
2. Shows how to properly request admin access
3. Covers security best practices
4. Provides safe software installation steps
5. Includes what to do if something goes wrong

Use office analogies (like keys to locked cabinets) to explain concepts.
Emphasize security without scaring users.

Format as JSON with interactive steps that guide users safely through PC admin tasks.
`}parseEducationalContent(e,t){try{let s=JSON.parse(e);return{type:s.type||"wizard",title:s.title||`PC Admin Guide: ${t}`,titleJp:s.titleJp||`PC管理ガイド: ${t}`,content:s.content||"",contentJp:s.contentJp||"",difficulty:"intermediate",estimatedTime:s.estimatedTime||20,interactive:!0,steps:s.steps||[],relatedTopics:s.relatedTopics||["security","software","permissions"]}}catch(e){return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"wizard",title:`PC Administration: ${e}`,titleJp:`PC管理: ${e}`,content:`Learn about PC administrative tasks for ${e}`,contentJp:`${e}のPC管理タスクについて学ぶ`,difficulty:"intermediate",estimatedTime:15,interactive:!0,steps:[]}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return e.description.includes("Control Panel")?"/images/pc-admin/control-panel-guide.png":e.description.includes("UAC")?"/images/pc-admin/uac-prompt.png":`/images/pc-admin/step-${e.order}.png`}async generateValidation(e){return e.userAction?.includes("install")?"Verify software installed correctly and appears in Programs list":e.userAction?.includes("permission")?"Check if admin rights are active (shield icon appears)":e.userAction?.includes("security")?"Confirm security settings are properly configured":"Verify the action completed without errors"}async generateTroubleshooting(e){let t=['If you see "Access Denied", you need to request admin rights first',"For installation errors, check if you have enough disk space","If the software doesn't work, try restarting your PC","Contact IT support if you see any security warnings"];return e.description.includes("install")&&(t.push("Some software requires specific versions of .NET Framework"),t.push("Antivirus may block installations - contact IT if this happens")),t}analyzeFeedbackPatterns(e){let t=new Map,s=e.filter(e=>e.suggestion?.toLowerCase().includes("security")||e.suggestion?.toLowerCase().includes("virus")).length,a=e.filter(e=>e.suggestion?.toLowerCase().includes("install")||e.suggestion?.toLowerCase().includes("software")).length;return t.set("securityConcerns",s),t.set("installationIssues",a),t}async adjustBehavior(e){(e.get("securityConcerns")||0)>3&&console.log(`[${this.agentId}] Enhancing security explanations in content`)}}!function(){var e=Error("Cannot find module './mailbox-agent'");throw e.code="MODULE_NOT_FOUND",e}();class N{constructor(){this.isRunning=!1,this.agents=new Map,this.agentStatuses=new Map,this.initializeAgents()}static getInstance(){return N.instance||(N.instance=new N),N.instance}initializeAgents(){[new j,new w,new b,new A,Object(function(){var e=Error("Cannot find module './mailbox-agent'");throw e.code="MODULE_NOT_FOUND",e}())()].forEach(e=>{this.agents.set(e.agentId,e),this.agentStatuses.set(e.agentId,{agentId:e.agentId,category:e.specialization.serviceCategory,status:"idle",lastActivity:new Date,articlesCreated:0,articlesUpdated:0})})}async startAllAgents(){if(this.isRunning){console.log("AI Agent Manager is already running");return}this.isRunning=!0,console.log("Starting AI Agent Manager with",this.agents.size,"agents"),this.startMonitoring();let e=Array.from(this.agents.entries()).map(([e,t])=>this.startAgent(e,t));await Promise.all(e)}async startAgent(e,t){try{let s=this.agentStatuses.get(e);s&&(s.status="active",s.lastActivity=new Date),await t.runAgentLoop()}catch(s){console.error(`Error in agent ${e}:`,s);let t=this.agentStatuses.get(e);t&&(t.status="error")}}async stopAllAgents(){this.isRunning=!1,console.log("Stopping all AI agents"),this.agents.forEach((e,t)=>{let s=this.agentStatuses.get(t);s&&(s.status="idle")})}getAgentStatuses(){return Array.from(this.agentStatuses.values())}getAgentStatus(e){return this.agentStatuses.get(e)}async startMonitoring(){setInterval(async()=>{if(this.isRunning){for(let[e,t]of this.agentStatuses)await this.updateAgentStats(e,t);await this.logSystemStatus()}},6e4)}async updateAgentStats(e,t){try{let{data:s}=await f.N.from("kb_articles").select("id").eq("metadata->>createdByAgent",e).gte("created_at",new Date(Date.now()-864e5).toISOString()),{data:a}=await f.N.from("kb_articles").select("id").eq("metadata->>lastUpdatedByAgent",e).gte("updated_at",new Date(Date.now()-864e5).toISOString());t.articlesCreated=s?.length||0,t.articlesUpdated=a?.length||0}catch(t){console.error(`Error updating stats for agent ${e}:`,t)}}async logSystemStatus(){let e=Array.from(this.agentStatuses.values()).filter(e=>"active"===e.status).length,t=Array.from(this.agentStatuses.values()).reduce((e,t)=>e+t.articlesCreated,0),s=Array.from(this.agentStatuses.values()).reduce((e,t)=>e+t.articlesUpdated,0);console.log(`[AI Agent Manager] Status Update:
      - Active Agents: ${e}/${this.agents.size}
      - Articles Created (24h): ${t}
      - Articles Updated (24h): ${s}
    `),await f.N.from("agent_system_logs").insert({active_agents:e,total_agents:this.agents.size,articles_created_24h:t,articles_updated_24h:s,created_at:new Date().toISOString()})}async triggerAgentResearch(e,t){Array.from(this.agents.values()).find(t=>t.specialization.serviceCategory===e)?console.log(`Triggering ${e} agent to research: ${t}`):console.error(`No agent found for category: ${e}`)}async getAgentMetrics(e="day"){let t=new Date;switch(e){case"day":t.setDate(t.getDate()-1);break;case"week":t.setDate(t.getDate()-7);break;case"month":t.setMonth(t.getMonth()-1)}let s=new Map;for(let[e,a]of this.agents){let{data:i}=await f.N.from("agent_activity_logs").select("*").eq("agent_id",e).gte("created_at",t.toISOString()),{data:n}=await f.N.from("kb_articles").select("id, metadata").or(`metadata->>createdByAgent.eq.${e},metadata->>lastUpdatedByAgent.eq.${e}`).gte("created_at",t.toISOString());s.set(e,{activities:i?.length||0,articlesCreated:n?.filter(t=>t.metadata?.createdByAgent===e).length||0,articlesUpdated:n?.filter(t=>t.metadata?.lastUpdatedByAgent===e).length||0,category:a.specialization.serviceCategory})}return Object.fromEntries(s)}}let C=N.getInstance();function _(){let[e,t]=(0,i.useState)([]),[s,y]=(0,i.useState)({}),[v,j]=(0,i.useState)(!1),[w,b]=(0,i.useState)(!0),[A,N]=(0,i.useState)("day"),_=async()=>{try{let{data:e}=await f.N.from("ai_agents").select("*").order("category");e&&(t(e.map(e=>({agentId:e.agent_id,category:e.category,name:e.name,status:e.status,lastActivity:new Date(e.last_active_at||e.created_at),articlesCreated:0,articlesUpdated:0}))),j(e.some(e=>"active"===e.status)));let{data:s}=await f.N.rpc("get_agent_performance_metrics",{p_time_range:"day"===A?"1 day":"week"===A?"7 days":"30 days"});if(s){let e={};s.forEach(t=>{e[t.agent_id]={articles_created:t.articles_created||0,articles_updated:t.articles_updated||0,educational_content_created:t.educational_content_created||0,web_pages_scraped:t.web_pages_scraped||0,active_time_percentage:t.active_time_percentage||0}}),y(e)}}catch(e){console.error("Error fetching agent data:",e)}finally{b(!1)}},S=async()=>{try{v?(await C.stopAllAgents(),j(!1)):(await C.startAllAgents(),j(!0)),_()}catch(e){console.error("Error toggling agent system:",e)}},I=e=>{switch(e){case"group_mail":return"\uD83D\uDCE7";case"sharepoint":return"\uD83D\uDCC1";case"password_reset":return"\uD83D\uDD10";case"pc_admin":return"\uD83D\uDCBB";case"mailbox":return"\uD83D\uDCEE";case"general":return"\uD83E\uDDE0";default:return"\uD83E\uDD16"}},k=e=>{switch(e){case"active":return"success";case"idle":return"secondary";case"error":return"destructive";default:return"default"}};return w?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:"Loading AI Agent System..."}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-8 w-8"}),"AI Agent Knowledge System"]}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"Specialized AI agents continuously learn and create educational content for each IT service"})]}),(0,a.jsx)(c.$,{size:"lg",variant:v?"destructive":"default",onClick:S,children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Stop All Agents"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Start All Agents"]})})]}),(0,a.jsxs)(d.Fc,{variant:v?"default":"warning",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"System Status"}),(0,a.jsx)(d.TN,{children:v?(0,a.jsxs)(a.Fragment,{children:["AI Agent System is ",(0,a.jsx)("strong",{children:"ACTIVE"}),". Agents are continuously scraping web content and creating educational materials."]}):(0,a.jsxs)(a.Fragment,{children:["AI Agent System is ",(0,a.jsx)("strong",{children:"INACTIVE"}),". Start the system to enable automatic content generation."]})})]}),(0,a.jsxs)(r.tU,{defaultValue:"overview",className:"space-y-4",children:[(0,a.jsxs)(r.j7,{children:[(0,a.jsx)(r.Xi,{value:"overview",children:"Overview"}),(0,a.jsx)(r.Xi,{value:"agents",children:"Agent Status"}),(0,a.jsx)(r.Xi,{value:"content",children:"Generated Content"}),(0,a.jsx)(r.Xi,{value:"learning",children:"Learning Paths"})]}),(0,a.jsxs)(r.av,{value:"overview",className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(c.$,{variant:"day"===A?"default":"outline",size:"sm",onClick:()=>N("day"),children:"24 Hours"}),(0,a.jsx)(c.$,{variant:"week"===A?"default":"outline",size:"sm",onClick:()=>N("week"),children:"7 Days"}),(0,a.jsx)(c.$,{variant:"month"===A?"default":"outline",size:"sm",onClick:()=>N("month"),children:"30 Days"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-2",children:(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Active Agents"})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[e.filter(e=>"active"===e.status).length,"/",e.length]}),(0,a.jsx)(o.k,{value:e.filter(e=>"active"===e.status).length/e.length*100,className:"mt-2"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-2",children:(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Articles Created"})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:Object.values(s).reduce((e,t)=>e+t.articles_created,0)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["In the last ",A]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-2",children:(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Educational Content"})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:Object.values(s).reduce((e,t)=>e+t.educational_content_created,0)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Interactive guides created"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{className:"pb-2",children:(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Web Pages Analyzed"})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:Object.values(s).reduce((e,t)=>e+t.web_pages_scraped,0)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"For content generation"})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>{let t=s[e.agentId]||{};return(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:I(e.category)}),(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-lg",children:e.name}),(0,a.jsx)(n.BT,{children:e.category})]})]}),(0,a.jsx)(l.E,{variant:k(e.status),children:e.status})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Articles Created:"}),(0,a.jsx)("span",{className:"font-medium",children:t.articles_created||0})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Content Generated:"}),(0,a.jsx)("span",{className:"font-medium",children:t.educational_content_created||0})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Web Pages Scraped:"}),(0,a.jsx)("span",{className:"font-medium",children:t.web_pages_scraped||0})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,a.jsx)("span",{children:"Active Time:"}),(0,a.jsxs)("span",{className:"font-medium",children:[t.active_time_percentage||0,"%"]})]}),(0,a.jsx)(o.k,{value:t.active_time_percentage||0})]})]})})]},e.agentId)})})]}),(0,a.jsx)(r.av,{value:"agents",className:"space-y-4",children:(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-2xl",children:I(e.category)}),(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{children:e.name}),(0,a.jsxs)(n.BT,{children:["Agent ID: ",e.agentId," | Category: ",e.category]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.E,{variant:k(e.status),children:e.status}),"active"===e.status&&(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-500 animate-pulse"})]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Last Active:"})," ",e.lastActivity.toLocaleString()]}),e.currentTask&&(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Current Task:"})," ",e.currentTask]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(c.$,{size:"sm",variant:"outline",children:"View Logs"}),(0,a.jsx)(c.$,{size:"sm",variant:"outline",children:"Trigger Research"}),(0,a.jsx)(c.$,{size:"sm",variant:"outline",children:"Configure"})]})]})})]},e.agentId))})}),(0,a.jsxs)(r.av,{value:"content",className:"space-y-4",children:[(0,a.jsxs)(d.Fc,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"Educational Content Library"}),(0,a.jsx)(d.TN,{children:"AI agents have generated interactive guides, tutorials, and troubleshooting wizards tailored for non-technical staff. Content is available in both English and Japanese."})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Recent Educational Content"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"View and manage AI-generated educational content across all service categories."})})]})]}),(0,a.jsxs)(r.av,{value:"learning",className:"space-y-4",children:[(0,a.jsxs)(d.Fc,{children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"Interactive Learning Paths"}),(0,a.jsx)(d.TN,{children:"AI agents create personalized learning journeys for staff members based on their role and knowledge level. Track progress and completion rates."})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Learning Path Analytics"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"Monitor user engagement with AI-generated learning materials."})})]})]})]})]})}var S=s(74464);function I(){return(0,a.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsxs)("h1",{className:"text-4xl font-bold flex items-center justify-center gap-3",children:[(0,a.jsx)(m.A,{className:"h-10 w-10 text-primary"}),"AI Agent Knowledge System"]}),(0,a.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Specialized AI agents that continuously learn from the web and create interactive educational content for non-technical staff"})]}),(0,a.jsxs)(d.Fc,{className:"bg-primary/5 border-primary",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"Revolutionary Learning System"}),(0,a.jsxs)(d.TN,{children:["Each IT service category has its own dedicated AI agent that:",(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"Continuously scrapes the web for the latest information"}),(0,a.jsx)("li",{children:"Transforms complex technical content into simple, interactive guides"}),(0,a.jsx)("li",{children:"Creates step-by-step wizards in both English and Japanese"}),(0,a.jsx)("li",{children:"Learns from user feedback to improve content quality"}),(0,a.jsx)("li",{children:"Automatically updates content when new information is available"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCE7"}),"Group Mail Agent"]}),(0,a.jsx)(n.BT,{children:"Specializes in email distribution lists"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm",children:"Creates easy-to-follow guides for managing group email addresses, adding/removing members, and understanding email permissions."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC1"}),"SharePoint Agent"]}),(0,a.jsx)(n.BT,{children:"Expert in document management"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm",children:"Generates visual tutorials for SharePoint navigation, file uploads, permissions, and collaboration features using office analogies."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD10"}),"Password Agent"]}),(0,a.jsx)(n.BT,{children:"Security and account specialist"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm",children:"Provides reassuring, step-by-step password reset guides and multi-factor authentication setup with security best practices."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),"PC Admin Agent"]}),(0,a.jsx)(n.BT,{children:"Windows administration expert"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm",children:"Creates safe, visual guides for software installation, admin rights requests, and troubleshooting common PC issues."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCEE"}),"Mailbox Agent"]}),(0,a.jsx)(n.BT,{children:"Email account configuration"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm",children:"Develops guides for email signatures, out-of-office settings, and mailbox organization following Japanese business etiquette."})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83E\uDDE0"}),"Master Agent"]}),(0,a.jsx)(n.BT,{children:"Coordinates all knowledge"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("p",{className:"text-sm",children:"Provides comprehensive overviews and helps users find the right service for their needs with intelligent routing."})})]})]}),(0,a.jsxs)(n.Zp,{className:"border-2 border-primary/20",children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(S.A,{className:"h-5 w-5"}),"How the AI Agent System Works"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"1. Continuous Web Learning"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Agents continuously monitor the web for updates about their specialized topics, ensuring content stays current with the latest best practices and solutions."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"2. Content Transformation"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Complex technical documentation is transformed into simple, relatable content using everyday analogies that non-technical staff can easily understand."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"3. Interactive Experience Creation"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Static content becomes interactive wizards with visual aids, step-by-step validation, and built-in troubleshooting tips for common issues."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"4. Bilingual Support"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"All content is automatically available in both English and Japanese, with cultural considerations for Japanese business environments."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"5. Adaptive Learning"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Agents learn from user feedback and search patterns to continuously improve content relevance and identify new topics that need coverage."})]})]})]}),(0,a.jsx)(_,{})]})}},97773:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-ai-agents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-agents\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>s(56174));module.exports=a})();