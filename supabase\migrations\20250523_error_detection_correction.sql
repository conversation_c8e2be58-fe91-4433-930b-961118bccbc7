-- Create tables for AI-powered error detection and correction system

-- Error patterns table
CREATE TABLE IF NOT EXISTS error_patterns (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  field_type VARCHAR(50) NOT NULL,
  error_type VARCHAR(50) NOT NULL,
  pattern_regex TEXT,
  common_mistake TEXT NOT NULL,
  suggested_correction TEXT,
  department_id UUID REFERENCES divisions(id),
  frequency INTEGER DEFAULT 1,
  confidence_score DECIMAL(3, 2) DEFAULT 0.00,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Error correction history
CREATE TABLE IF NOT EXISTS error_corrections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES staff(id),
  field_name VARCHAR(100) NOT NULL,
  original_value TEXT NOT NULL,
  detected_errors JSONB NOT NULL DEFAULT '[]',
  suggested_corrections JSONB NOT NULL DEFAULT '[]',
  accepted_correction TEXT,
  correction_accepted BOOLEAN DEFAULT FALSE,
  ai_confidence DECIMAL(3, 2),
  context_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Field error statistics
CREATE TABLE IF NOT EXISTS field_error_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  field_name VARCHAR(100) NOT NULL,
  department_id UUID REFERENCES divisions(id),
  error_count INTEGER DEFAULT 0,
  total_submissions INTEGER DEFAULT 0,
  error_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
    CASE 
      WHEN total_submissions > 0 THEN (error_count::DECIMAL / total_submissions * 100)
      ELSE 0
    END
  ) STORED,
  common_errors JSONB DEFAULT '[]',
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(field_name, department_id)
);

-- AI learning feedback for error corrections
CREATE TABLE IF NOT EXISTS error_correction_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  correction_id UUID REFERENCES error_corrections(id),
  feedback_type VARCHAR(20) CHECK (feedback_type IN ('helpful', 'not_helpful', 'incorrect')),
  user_feedback TEXT,
  improved_suggestion TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Common typos and autocorrect mappings
CREATE TABLE IF NOT EXISTS autocorrect_mappings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  field_type VARCHAR(50),
  incorrect_value TEXT NOT NULL,
  correct_value TEXT NOT NULL,
  language VARCHAR(10) DEFAULT 'ja',
  usage_count INTEGER DEFAULT 1,
  last_used TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(incorrect_value, correct_value, field_type)
);

-- Indexes for performance
CREATE INDEX idx_error_patterns_field_type ON error_patterns(field_type);
CREATE INDEX idx_error_patterns_department ON error_patterns(department_id);
CREATE INDEX idx_error_corrections_user ON error_corrections(user_id);
CREATE INDEX idx_error_corrections_created ON error_corrections(created_at DESC);
CREATE INDEX idx_field_error_stats_field ON field_error_stats(field_name);
CREATE INDEX idx_autocorrect_field_type ON autocorrect_mappings(field_type);

-- Row Level Security
ALTER TABLE error_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_corrections ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_error_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_correction_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE autocorrect_mappings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Error patterns: readable by all authenticated users
CREATE POLICY "error_patterns_read_policy" ON error_patterns
  FOR SELECT TO authenticated
  USING (true);

-- Error corrections: users can see their own corrections
CREATE POLICY "error_corrections_user_policy" ON error_corrections
  FOR ALL TO authenticated
  USING (auth.uid()::UUID = user_id);

-- Admin can see all error corrections
CREATE POLICY "error_corrections_admin_policy" ON error_corrections
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Field error stats: department-based access
CREATE POLICY "field_error_stats_dept_policy" ON field_error_stats
  FOR SELECT TO authenticated
  USING (
    department_id IS NULL OR
    department_id IN (
      SELECT division_id FROM staff WHERE auth_id = auth.uid()
    )
  );

-- Initial data: Common error patterns
INSERT INTO error_patterns (field_type, error_type, pattern_regex, common_mistake, suggested_correction, confidence_score) VALUES
  -- Email patterns
  ('email', 'missing_domain', '^[^@]+$', 'Email without domain', 'Add @company.com', 0.95),
  ('email', 'wrong_domain', '@gmail\\.com$|@yahoo\\.com$', 'Personal email used', 'Use company email @company.com', 0.90),
  ('email', 'typo_domain', '@comapny\\.com$|@compnay\\.com$', 'Typo in domain', 'Correct to @company.com', 0.98),
  
  -- Staff ID patterns
  ('staff_id', 'wrong_format', '^[^R].*|^R[0-9]{1,5}$', 'Invalid staff ID format', 'Format should be R + 6 digits (e.g., R000123)', 0.85),
  ('staff_id', 'missing_zeros', '^R[1-9][0-9]{2,5}$', 'Missing leading zeros', 'Add leading zeros (e.g., R000123)', 0.90),
  
  -- PC ID patterns
  ('pc_id', 'wrong_prefix', '^[^M].*', 'PC ID should start with M', 'Format: M + 6 digits (e.g., M241234)', 0.85),
  ('pc_id', 'invalid_length', '^M[0-9]{1,5}$|^M[0-9]{7,}$', 'Invalid PC ID length', 'PC ID should be M + 6 digits', 0.90),
  
  -- Name patterns (Japanese specific)
  ('name_jp', 'missing_space', '^[一-龯ぁ-ゔァ-ヴー]+$', 'Missing space between surname and given name', 'Add space between names', 0.75),
  ('name_jp', 'romaji_used', '^[a-zA-Z\\s]+$', 'Romaji used instead of Japanese', 'Use Japanese characters', 0.80),
  
  -- Date patterns
  ('date', 'wrong_format', '^[0-9]{2}/[0-9]{2}/[0-9]{4}$', 'American date format used', 'Use YYYY-MM-DD format', 0.85),
  ('date', 'future_date', NULL, 'Future date entered for past event', 'Date should be in the past', 0.70);

-- Common autocorrect mappings
INSERT INTO autocorrect_mappings (field_type, incorrect_value, correct_value, language) VALUES
  ('department', '経理', '経理部', 'ja'),
  ('department', '人事', '人事部', 'ja'),
  ('department', 'HR', 'Human Resources Division', 'en'),
  ('department', 'IT', 'IT Systems Division', 'en'),
  ('group_mail', '<EMAIL>', '<EMAIL>', 'en'),
  ('group_mail', '<EMAIL>', '<EMAIL>', 'en');

-- Function to update error statistics
CREATE OR REPLACE FUNCTION update_field_error_stats()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO field_error_stats (field_name, department_id, error_count, total_submissions)
  VALUES (
    NEW.field_name,
    (SELECT division_id FROM staff WHERE id = NEW.user_id),
    CASE WHEN array_length(NEW.detected_errors::TEXT[], 1) > 0 THEN 1 ELSE 0 END,
    1
  )
  ON CONFLICT (field_name, department_id) DO UPDATE
  SET 
    error_count = field_error_stats.error_count + 
      CASE WHEN array_length(NEW.detected_errors::TEXT[], 1) > 0 THEN 1 ELSE 0 END,
    total_submissions = field_error_stats.total_submissions + 1,
    last_updated = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updating statistics
CREATE TRIGGER update_error_stats_trigger
AFTER INSERT ON error_corrections
FOR EACH ROW
EXECUTE FUNCTION update_field_error_stats();
