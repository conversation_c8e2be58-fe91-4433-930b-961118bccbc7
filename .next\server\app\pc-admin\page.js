(()=>{var e={};e.id=4328,e.ids=[4328],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},94560:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>u,routeModule:()=>m,tree:()=>p});var n=t(70260),s=t(28203),a=t(25155),i=t.n(a),o=t(67292),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let p=["",{children:["pc-admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,96524,23)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\pc-admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],u=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\pc-admin\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/pc-admin/page",pathname:"/pc-admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},96524:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Unexpected token `AuthGuard`. Expected jsx identifier\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\pc-admin\\page.tsx\x1b[0m:23:1]\n \x1b[2m20\x1b[0m │   };\n \x1b[2m21\x1b[0m │ \n \x1b[2m22\x1b[0m │   return (\n \x1b[2m23\x1b[0m │     <AuthGuard>\n    \xb7 \x1b[35;1m     ─────────\x1b[0m\n \x1b[2m24\x1b[0m │       <div className="container mx-auto py-8">\n \x1b[2m25\x1b[0m │         <div className="mb-8 flex items-center justify-between">\n \x1b[2m26\x1b[0m │           <div>\n    ╰────\n\n\nCaused by:\n    Syntax Error')}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8096,2076],()=>t(94560));module.exports=n})();