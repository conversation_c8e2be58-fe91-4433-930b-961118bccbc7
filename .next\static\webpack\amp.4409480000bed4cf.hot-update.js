"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("amp",{

/***/ "./node_modules/next/dist/build/deployment-id.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/build/deployment-id.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDeploymentIdQueryOrEmptyString\", ({\n    enumerable: true,\n    get: function() {\n        return getDeploymentIdQueryOrEmptyString;\n    }\n}));\nfunction getDeploymentIdQueryOrEmptyString() {\n    if (false) {}\n    return \"\";\n}\n\n//# sourceMappingURL=deployment-id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL2RlcGxveW1lbnQtaWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixxRUFBb0U7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBLFFBQVEsS0FBOEIsRUFBRSxFQUVuQztBQUNMO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC9kZXBsb3ltZW50LWlkLmpzP2FhZDgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJnZXREZXBsb3ltZW50SWRRdWVyeU9yRW1wdHlTdHJpbmdcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZztcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZygpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTkVYVF9ERVBMT1lNRU5UX0lEKSB7XG4gICAgICAgIHJldHVybiBgP2RwbD0ke3Byb2Nlc3MuZW52Lk5FWFRfREVQTE9ZTUVOVF9JRH1gO1xuICAgIH1cbiAgICByZXR1cm4gXCJcIjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVwbG95bWVudC1pZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/deployment-id.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/dev/error-overlay/websocket.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/error-overlay/websocket.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addMessageListener: function() {\n        return addMessageListener;\n    },\n    sendMessage: function() {\n        return sendMessage;\n    },\n    connectHMR: function() {\n        return connectHMR;\n    }\n});\nlet source;\nconst eventCallbacks = [];\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === \"http:\" ? \"ws\" : \"wss\";\n}\nfunction addMessageListener(callback) {\n    eventCallbacks.push(callback);\n}\nfunction sendMessage(data) {\n    if (!source || source.readyState !== source.OPEN) return;\n    return source.send(data);\n}\nlet reconnections = 0;\nfunction connectHMR(options) {\n    function init() {\n        if (source) source.close();\n        function handleOnline() {\n            reconnections = 0;\n            window.console.log(\"[HMR] connected\");\n        }\n        function handleMessage(event) {\n            // Coerce into HMR_ACTION_TYPES as that is the format.\n            const msg = JSON.parse(event.data);\n            for (const eventCallback of eventCallbacks){\n                eventCallback(msg);\n            }\n        }\n        let timer;\n        function handleDisconnect() {\n            source.onerror = null;\n            source.onclose = null;\n            source.close();\n            reconnections++;\n            // After 25 reconnects we'll want to reload the page as it indicates the dev server is no longer running.\n            if (reconnections > 25) {\n                window.location.reload();\n                return;\n            }\n            clearTimeout(timer);\n            // Try again after 5 seconds\n            timer = setTimeout(init, reconnections > 5 ? 5000 : 1000);\n        }\n        const { hostname, port } = location;\n        const protocol = getSocketProtocol(options.assetPrefix || \"\");\n        const assetPrefix = options.assetPrefix.replace(/^\\/+/, \"\");\n        let url = protocol + \"://\" + hostname + \":\" + port + (assetPrefix ? \"/\" + assetPrefix : \"\");\n        if (assetPrefix.startsWith(\"http\")) {\n            url = protocol + \"://\" + assetPrefix.split(\"://\")[1];\n        }\n        source = new window.WebSocket(\"\" + url + options.path);\n        source.onopen = handleOnline;\n        source.onerror = handleDisconnect;\n        source.onclose = handleDisconnect;\n        source.onmessage = handleMessage;\n    }\n    init();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/dev/error-overlay/websocket.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/route-loader.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/route-loader.js ***!
  \*******************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    markAssetError: function() {\n        return markAssetError;\n    },\n    isAssetError: function() {\n        return isAssetError;\n    },\n    getClientBuildManifest: function() {\n        return getClientBuildManifest;\n    },\n    createRouteLoader: function() {\n        return createRouteLoader;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _getassetpathfromroute = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/router/utils/get-asset-path-from-route */ \"./node_modules/next/dist/shared/lib/router/utils/get-asset-path-from-route.js\"));\nconst _trustedtypes = __webpack_require__(/*! ./trusted-types */ \"./node_modules/next/dist/client/trusted-types.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst _deploymentid = __webpack_require__(/*! ../build/deployment-id */ \"./node_modules/next/dist/build/deployment-id.js\");\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800;\nfunction withFuture(key, map, generator) {\n    let entry = map.get(key);\n    if (entry) {\n        if (\"future\" in entry) {\n            return entry.future;\n        }\n        return Promise.resolve(entry);\n    }\n    let resolver;\n    const prom = new Promise((resolve)=>{\n        resolver = resolve;\n    });\n    map.set(key, entry = {\n        resolve: resolver,\n        future: prom\n    });\n    return generator ? generator() // eslint-disable-next-line no-sequences\n    .then((value)=>(resolver(value), value)).catch((err)=>{\n        map.delete(key);\n        throw err;\n    }) : prom;\n}\nconst ASSET_LOAD_ERROR = Symbol(\"ASSET_LOAD_ERROR\");\nfunction markAssetError(err) {\n    return Object.defineProperty(err, ASSET_LOAD_ERROR, {});\n}\nfunction isAssetError(err) {\n    return err && ASSET_LOAD_ERROR in err;\n}\nfunction hasPrefetch(link) {\n    try {\n        link = document.createElement(\"link\");\n        return(// with relList.support\n        !!window.MSInputMethodContext && !!document.documentMode || link.relList.supports(\"prefetch\"));\n    } catch (e) {\n        return false;\n    }\n}\nconst canPrefetch = hasPrefetch();\nconst getAssetQueryString = ()=>{\n    return (0, _deploymentid.getDeploymentIdQueryOrEmptyString)();\n};\nfunction prefetchViaDom(href, as, link) {\n    return new Promise((resolve, reject)=>{\n        const selector = '\\n      link[rel=\"prefetch\"][href^=\"' + href + '\"],\\n      link[rel=\"preload\"][href^=\"' + href + '\"],\\n      script[src^=\"' + href + '\"]';\n        if (document.querySelector(selector)) {\n            return resolve();\n        }\n        link = document.createElement(\"link\");\n        // The order of property assignment here is intentional:\n        if (as) link.as = as;\n        link.rel = \"prefetch\";\n        link.crossOrigin = undefined;\n        link.onload = resolve;\n        link.onerror = ()=>reject(markAssetError(new Error(\"Failed to prefetch: \" + href)));\n        // `href` should always be last:\n        link.href = href;\n        document.head.appendChild(link);\n    });\n}\nfunction appendScript(src, script) {\n    return new Promise((resolve, reject)=>{\n        script = document.createElement(\"script\");\n        // The order of property assignment here is intentional.\n        // 1. Setup success/failure hooks in case the browser synchronously\n        //    executes when `src` is set.\n        script.onload = resolve;\n        script.onerror = ()=>reject(markAssetError(new Error(\"Failed to load script: \" + src)));\n        // 2. Configure the cross-origin attribute before setting `src` in case the\n        //    browser begins to fetch.\n        script.crossOrigin = undefined;\n        // 3. Finally, set the source and inject into the DOM in case the child\n        //    must be appended for fetching to start.\n        script.src = src;\n        document.body.appendChild(script);\n    });\n}\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise;\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout(p, ms, err) {\n    return new Promise((resolve, reject)=>{\n        let cancelled = false;\n        p.then((r)=>{\n            // Resolved, cancel the timeout\n            cancelled = true;\n            resolve(r);\n        }).catch(reject);\n        // We wrap these checks separately for better dead-code elimination in\n        // production bundles.\n        if (true) {\n            (devBuildPromise || Promise.resolve()).then(()=>{\n                (0, _requestidlecallback.requestIdleCallback)(()=>setTimeout(()=>{\n                        if (!cancelled) {\n                            reject(err);\n                        }\n                    }, ms));\n            });\n        }\n        if (false) {}\n    });\n}\nfunction getClientBuildManifest() {\n    if (self.__BUILD_MANIFEST) {\n        return Promise.resolve(self.__BUILD_MANIFEST);\n    }\n    const onBuildManifest = new Promise((resolve)=>{\n        // Mandatory because this is not concurrent safe:\n        const cb = self.__BUILD_MANIFEST_CB;\n        self.__BUILD_MANIFEST_CB = ()=>{\n            resolve(self.__BUILD_MANIFEST);\n            cb && cb();\n        };\n    });\n    return resolvePromiseWithTimeout(onBuildManifest, MS_MAX_IDLE_DELAY, markAssetError(new Error(\"Failed to load client build manifest\")));\n}\nfunction getFilesForRoute(assetPrefix, route) {\n    if (true) {\n        const scriptUrl = assetPrefix + \"/_next/static/chunks/pages\" + encodeURI((0, _getassetpathfromroute.default)(route, \".js\")) + getAssetQueryString();\n        return Promise.resolve({\n            scripts: [\n                (0, _trustedtypes.__unsafeCreateTrustedScriptURL)(scriptUrl)\n            ],\n            // Styles are handled by `style-loader` in development:\n            css: []\n        });\n    }\n    return getClientBuildManifest().then((manifest)=>{\n        if (!(route in manifest)) {\n            throw markAssetError(new Error(\"Failed to lookup route: \" + route));\n        }\n        const allFiles = manifest[route].map((entry)=>assetPrefix + \"/_next/\" + encodeURI(entry));\n        return {\n            scripts: allFiles.filter((v)=>v.endsWith(\".js\")).map((v)=>(0, _trustedtypes.__unsafeCreateTrustedScriptURL)(v) + getAssetQueryString()),\n            css: allFiles.filter((v)=>v.endsWith(\".css\")).map((v)=>v + getAssetQueryString())\n        };\n    });\n}\nfunction createRouteLoader(assetPrefix) {\n    const entrypoints = new Map();\n    const loadedScripts = new Map();\n    const styleSheets = new Map();\n    const routes = new Map();\n    function maybeExecuteScript(src) {\n        // With HMR we might need to \"reload\" scripts when they are\n        // disposed and readded. Executing scripts twice has no functional\n        // differences\n        if (false) {} else {\n            return appendScript(src);\n        }\n    }\n    function fetchStyleSheet(href) {\n        let prom = styleSheets.get(href);\n        if (prom) {\n            return prom;\n        }\n        styleSheets.set(href, prom = fetch(href).then((res)=>{\n            if (!res.ok) {\n                throw new Error(\"Failed to load stylesheet: \" + href);\n            }\n            return res.text().then((text)=>({\n                    href: href,\n                    content: text\n                }));\n        }).catch((err)=>{\n            throw markAssetError(err);\n        }));\n        return prom;\n    }\n    return {\n        whenEntrypoint (route) {\n            return withFuture(route, entrypoints);\n        },\n        onEntrypoint (route, execute) {\n            (execute ? Promise.resolve().then(()=>execute()).then((exports1)=>({\n                    component: exports1 && exports1.default || exports1,\n                    exports: exports1\n                }), (err)=>({\n                    error: err\n                })) : Promise.resolve(undefined)).then((input)=>{\n                const old = entrypoints.get(route);\n                if (old && \"resolve\" in old) {\n                    if (input) {\n                        entrypoints.set(route, input);\n                        old.resolve(input);\n                    }\n                } else {\n                    if (input) {\n                        entrypoints.set(route, input);\n                    } else {\n                        entrypoints.delete(route);\n                    }\n                    // when this entrypoint has been resolved before\n                    // the route is outdated and we want to invalidate\n                    // this cache entry\n                    routes.delete(route);\n                }\n            });\n        },\n        loadRoute (route, prefetch) {\n            return withFuture(route, routes, ()=>{\n                let devBuildPromiseResolve;\n                if (true) {\n                    devBuildPromise = new Promise((resolve)=>{\n                        devBuildPromiseResolve = resolve;\n                    });\n                }\n                return resolvePromiseWithTimeout(getFilesForRoute(assetPrefix, route).then((param)=>{\n                    let { scripts, css } = param;\n                    return Promise.all([\n                        entrypoints.has(route) ? [] : Promise.all(scripts.map(maybeExecuteScript)),\n                        Promise.all(css.map(fetchStyleSheet))\n                    ]);\n                }).then((res)=>{\n                    return this.whenEntrypoint(route).then((entrypoint)=>({\n                            entrypoint,\n                            styles: res[1]\n                        }));\n                }), MS_MAX_IDLE_DELAY, markAssetError(new Error(\"Route did not complete loading: \" + route))).then((param)=>{\n                    let { entrypoint, styles } = param;\n                    const res = Object.assign({\n                        styles: styles\n                    }, entrypoint);\n                    return \"error\" in entrypoint ? entrypoint : res;\n                }).catch((err)=>{\n                    if (prefetch) {\n                        // we don't want to cache errors during prefetch\n                        throw err;\n                    }\n                    return {\n                        error: err\n                    };\n                }).finally(()=>devBuildPromiseResolve == null ? void 0 : devBuildPromiseResolve());\n            });\n        },\n        prefetch (route) {\n            // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n            // License: Apache 2.0\n            let cn;\n            if (cn = navigator.connection) {\n                // Don't prefetch if using 2G or if Save-Data is enabled.\n                if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve();\n            }\n            return getFilesForRoute(assetPrefix, route).then((output)=>Promise.all(canPrefetch ? output.scripts.map((script)=>prefetchViaDom(script.toString(), \"script\")) : [])).then(()=>{\n                (0, _requestidlecallback.requestIdleCallback)(()=>this.loadRoute(route, true).catch(()=>{}));\n            }).catch(()=>{});\n        }\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=route-loader.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/route-loader.js\n"));

/***/ })

});