import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/language-context';
import { ChevronDown, ChevronUp, Tag, CheckCircle, XCircle } from 'lucide-react';

interface FAQ {
  id?: string;
  question_ja: string;
  question_en: string;
  answer_ja: string;
  answer_en: string;
  category: string;
  keywords: string[];
  confidence_score?: number;
  source_article_ids?: string[];
  usage_count?: number;
  is_active?: boolean;
}

interface FAQListProps {
  faqs: FAQ[];
  onSave?: (faqs: FAQ[]) => void;
  onToggleActive?: (faqId: string, isActive: boolean) => void;
  showActions?: boolean;
  showStats?: boolean;
}

export function FAQList({ 
  faqs, 
  onSave, 
  onToggleActive, 
  showActions = true,
  showStats = false 
}: FAQListProps) {
  const { language } = useLanguage();
  const [expandedIds, setExpandedIds] = React.useState<Set<string>>(new Set());
  const [selectedFAQs, setSelectedFAQs] = React.useState<Set<number>>(new Set());

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedIds);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedIds(newExpanded);
  };

  const toggleSelected = (index: number) => {
    const newSelected = new Set(selectedFAQs);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedFAQs(newSelected);
  };

  const handleSaveSelected = () => {
    if (onSave) {
      const selected = Array.from(selectedFAQs).map(index => faqs[index]);
      onSave(selected);
      setSelectedFAQs(new Set());
    }
  };

  return (
    <div className="space-y-4">
      {showActions && (
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-muted-foreground">
            {selectedFAQs.size} of {faqs.length} FAQs selected
          </div>
          {selectedFAQs.size > 0 && onSave && (
            <Button onClick={handleSaveSelected}>
              Save Selected FAQs
            </Button>
          )}
        </div>
      )}

      <div className="space-y-2">
        {faqs.map((faq, index) => {
          const id = faq.id || `temp-${index}`;
          const isExpanded = expandedIds.has(id);
          const isSelected = selectedFAQs.has(index);

          return (
            <Card key={id} className={`transition-all ${isSelected ? 'ring-2 ring-primary' : ''}`}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {showActions && !faq.id && (
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleSelected(index)}
                          className="h-4 w-4"
                        />
                      )}
                      <h3 className="font-medium">
                        {language === 'ja' ? faq.question_ja : faq.question_en}
                      </h3>
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline">{faq.category}</Badge>
                      {faq.confidence_score && (
                        <Badge variant={faq.confidence_score > 0.8 ? "default" : "secondary"}>
                          {Math.round(faq.confidence_score * 100)}% confidence
                        </Badge>
                      )}
                      {showStats && faq.usage_count !== undefined && (
                        <Badge variant="outline">
                          Used {faq.usage_count} times
                        </Badge>
                      )}
                      {faq.is_active !== undefined && (
                        <Badge variant={faq.is_active ? "default" : "secondary"}>
                          {faq.is_active ? <CheckCircle className="h-3 w-3 mr-1" /> : <XCircle className="h-3 w-3 mr-1" />}
                          {faq.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleExpanded(id)}
                  >
                    {isExpanded ? <ChevronUp /> : <ChevronDown />}
                  </Button>
                </div>
              </CardHeader>

              {isExpanded && (
                <CardContent>
                  <Tabs defaultValue={language} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="ja">日本語</TabsTrigger>
                      <TabsTrigger value="en">English</TabsTrigger>
                    </TabsList>
                    <TabsContent value="ja" className="space-y-2">
                      <div>
                        <h4 className="font-medium mb-1">質問:</h4>
                        <p className="text-sm text-muted-foreground">{faq.question_ja}</p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-1">回答:</h4>
                        <p className="text-sm">{faq.answer_ja}</p>
                      </div>
                    </TabsContent>
                    <TabsContent value="en" className="space-y-2">
                      <div>
                        <h4 className="font-medium mb-1">Question:</h4>
                        <p className="text-sm text-muted-foreground">{faq.question_en}</p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-1">Answer:</h4>
                        <p className="text-sm">{faq.answer_en}</p>
                      </div>
                    </TabsContent>
                  </Tabs>

                  <div className="mt-4 space-y-2">
                    {faq.keywords && faq.keywords.length > 0 && (
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-muted-foreground" />
                        <div className="flex flex-wrap gap-1">
                          {faq.keywords.map((keyword, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {showActions && faq.id && onToggleActive && (
                      <div className="flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onToggleActive(faq.id!, !faq.is_active)}
                        >
                          {faq.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>
    </div>
  );
}