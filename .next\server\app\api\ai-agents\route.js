(()=>{var e={};e.id=7551,e.ids=[7551],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},4399:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>I,routeModule:()=>k,serverHooks:()=>C,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>S});var a={};s.r(a),s.d(a,{GET:()=>b,POST:()=>A});var i=s(42706),r=s(28203),n=s(45994),o=s(39187),c=s(33406),l=s(44512),u=s(73865),p=s(45895);class d{constructor(e,t){this.agentId=e,this.specialization=t,this.lastActivityTime=new Date,this.learningHistory=new Map}async runAgentLoop(){for(console.log(`[${this.agentId}] Agent starting...`);;)try{for(let e of(await this.identifyResearchTopics())){let t=await this.scrapeWebContent(e),s=await this.transformToEducational(t,e),a=await this.createInteractiveExperience(s);await this.storeInKnowledgeBase(a),await this.updateEmbeddings(a)}await this.learnFromFeedback(),await this.sleep(3e5)}catch(e){console.error(`[${this.agentId}] Agent error:`,e),await this.sleep(6e4)}}async identifyResearchTopics(){let e=[],{data:t}=await u.N.from("kb_search_history").select("query").eq("search_type","ai").or("clicked_results.is.null,clicked_results.eq.{}").ilike("query",`%${this.specialization.serviceCategory}%`).gte("created_at",new Date(Date.now()-864e5).toISOString()).limit(20);t&&e.push(...t.map(e=>e.query));let{data:s}=await u.N.from("kb_content_gaps").select("topic").eq("suggested_category",this.specialization.serviceCategory).eq("status","identified").limit(10);s&&e.push(...s.map(e=>e.topic));let{data:a}=await u.N.from("kb_article_feedback").select("suggestion").in("feedback_type",["not_helpful","outdated"]).not("suggestion","is",null).limit(10);return a&&e.push(...a.map(e=>e.suggestion)),[...new Set(e)].slice(0,5)}async scrapeWebContent(e){let t=[],s=`${e} ${this.specialization.expertise.join(" ")} tutorial guide`;try{for(let a of(await this.performWebSearch(s)).slice(0,5)){let s=await this.scrapeUrl(a.url);s&&t.push({url:a.url,title:a.title,content:s,relevance:await this.calculateRelevance(s,e),lastUpdated:new Date})}}catch(e){console.error(`[${this.agentId}] Web scraping error:`,e)}return t.sort((e,t)=>t.relevance-e.relevance)}async transformToEducational(e,t){let s=e.slice(0,3).map(e=>e.content).join("\n\n"),a=this.buildEducationalPrompt(t,s),i=await p.aiApiService.generateText(a,{temperature:.3,maxTokens:3e3});return this.parseEducationalContent(i,t)}async createInteractiveExperience(e){if(!e.interactive||!e.steps)return e;let t=await Promise.all(e.steps.map(async e=>(!e.visualAid&&this.shouldHaveVisual(e)&&(e.visualAid=await this.generateVisualAid(e)),!e.validation&&e.userAction&&(e.validation=await this.generateValidation(e)),e.troubleshooting&&0!==e.troubleshooting.length||(e.troubleshooting=await this.generateTroubleshooting(e)),e)));return e.steps=t,e}async storeInKnowledgeBase(e){try{let t=await this.findSimilarContent(e.title);if(t)await u.N.from("kb_articles").update({content_en:e.content,content_jp:e.contentJp,metadata:{type:e.type,difficulty:e.difficulty,estimatedTime:e.estimatedTime,interactive:e.interactive,steps:e.steps,lastUpdatedByAgent:this.agentId,agentVersion:"1.0"},updated_at:new Date().toISOString(),last_auto_update:new Date().toISOString()}).eq("id",t.id);else{let{data:t,error:s}=await u.N.from("kb_articles").insert({title_en:e.title,title_jp:e.titleJp,content_en:e.content,content_jp:e.contentJp,category:this.specialization.serviceCategory,tags:e.relatedTopics||[],status:"published",author_id:null,metadata:{type:e.type,difficulty:e.difficulty,estimatedTime:e.estimatedTime,interactive:e.interactive,steps:e.steps,createdByAgent:this.agentId,agentVersion:"1.0"}}).select().single();if(s)throw s;await this.logActivity("article_created",{articleId:t.id,title:e.title,type:e.type})}}catch(e){console.error(`[${this.agentId}] Error storing content:`,e)}}async updateEmbeddings(e){}async learnFromFeedback(){let{data:e}=await u.N.from("kb_article_feedback").select(`
        *,
        article:kb_articles!inner(
          id,
          metadata
        )
      `).eq("article.metadata->>createdByAgent",this.agentId).gte("created_at",new Date(Date.now()-864e5).toISOString());if(e&&e.length>0){let t=this.analyzeFeedbackPatterns(e);t.forEach((e,t)=>{this.learningHistory.set(t,e)}),await this.adjustBehavior(t)}}async performWebSearch(e){return[]}async scrapeUrl(e){return null}async calculateRelevance(e,t){let s=t.toLowerCase().split(" "),a=e.toLowerCase(),i=0;return s.forEach(e=>{a.includes(e)&&i++}),i/s.length}async findSimilarContent(e){let{data:t}=await u.N.from("kb_articles").select("id, title_en").eq("category",this.specialization.serviceCategory).ilike("title_en",`%${e}%`).limit(1).single();return t}async logActivity(e,t){await u.N.from("agent_activity_logs").insert({agent_id:this.agentId,action:e,details:t,created_at:new Date().toISOString()})}async sleep(e){return new Promise(t=>setTimeout(t,e))}}class g extends d{constructor(){super("group-mail-agent",{serviceCategory:"group_mail",expertise:["email distribution lists","group mail management","Microsoft Exchange","email permissions","distribution group best practices"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!1,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are an expert in email and group mail management, specializing in making complex concepts simple for non-technical users.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Explains the concept in simple, non-technical terms
2. Provides step-by-step instructions with clear actions
3. Includes common mistakes to avoid
4. Offers troubleshooting tips for typical issues
5. Uses analogies that office workers can relate to

Format the response as JSON:
{
  "type": "guide" | "tutorial" | "wizard",
  "title": "English title",
  "titleJp": "Japanese title",
  "content": "Main content in simple English",
  "contentJp": "Main content in simple Japanese",
  "difficulty": "beginner",
  "estimatedTime": 10,
  "interactive": true,
  "steps": [
    {
      "order": 1,
      "title": "Step title",
      "titleJp": "Japanese step title",
      "description": "What to do",
      "descriptionJp": "Japanese description",
      "userAction": "Click on...",
      "troubleshooting": ["If you see X, try Y"]
    }
  ],
  "relatedTopics": ["topic1", "topic2"]
}

Focus on making it accessible for someone who has never managed email groups before.
`}parseEducationalContent(e,t){try{let s=JSON.parse(e);return{type:s.type||"guide",title:s.title||t,titleJp:s.titleJp||t,content:s.content||"",contentJp:s.contentJp||"",difficulty:"beginner",estimatedTime:s.estimatedTime||15,interactive:!0,steps:s.steps||[],relatedTopics:s.relatedTopics||[]}}catch(e){return{type:"guide",title:`Guide: ${t}`,titleJp:`ガイド: ${t}`,content:`Learn about ${t}`,contentJp:`${t}について学ぶ`,difficulty:"beginner",estimatedTime:10,interactive:!1}}}shouldHaveVisual(e){return["click","select","button","menu","screen"].some(t=>e.description.toLowerCase().includes(t))}async generateVisualAid(e){return`/images/group-mail/step-${e.order}.png`}async generateValidation(e){return e.userAction?.includes("email address")?"Check if email address format is valid":e.userAction?.includes("select")?"Verify at least one option is selected":"Confirm action was completed"}async generateTroubleshooting(e){let t=[];return e.description.includes("permission")&&t.push('If you see "Access Denied", contact your department administrator'),e.description.includes("email")&&(t.push("If the email address is not found, check the spelling"),t.push("For external emails, you may need special approval")),e.description.includes("group")&&t.push("If the group doesn't appear, it may not be assigned to your department"),t}analyzeFeedbackPatterns(e){let t=new Map,s=e.map(e=>e.feedback_type),a=new Map;s.forEach(e=>{a.set(e,(a.get(e)||0)+1)}),t.set("commonIssues",Array.from(a.entries())),t.set("totalFeedback",e.length);let i=e.filter(e=>e.suggestion).map(e=>e.suggestion);return t.set("userSuggestions",i),t}async adjustBehavior(e){for(let[t,s]of e.get("commonIssues")||[])"outdated"===t&&s>3&&console.log(`[${this.agentId}] Increasing update frequency due to outdated content feedback`),"not_helpful"===t&&s>5&&console.log(`[${this.agentId}] Simplifying content due to not helpful feedback`)}}class m extends d{constructor(){super("sharepoint-agent",{serviceCategory:"sharepoint",expertise:["SharePoint Online","document libraries","permissions management","SharePoint sites","Microsoft 365 integration"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a SharePoint expert who specializes in teaching non-technical users.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Demystifies SharePoint concepts using office analogies (folders, filing cabinets, etc.)
2. Provides visual step-by-step instructions
3. Includes Japanese translations for all UI elements
4. Covers common permission scenarios
5. Explains best practices for document organization

Include specific examples relevant to Japanese corporate culture.

Format as JSON with the same structure as before, ensuring all content is beginner-friendly.
`}parseEducationalContent(e,t){try{let s=JSON.parse(e);return{type:s.type||"tutorial",title:s.title||t,titleJp:s.titleJp||t,content:s.content||"",contentJp:s.contentJp||"",difficulty:"beginner",estimatedTime:s.estimatedTime||20,interactive:!0,steps:s.steps||[],relatedTopics:s.relatedTopics||[]}}catch(e){return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"tutorial",title:`SharePoint Tutorial: ${e}`,titleJp:`SharePointチュートリアル: ${e}`,content:`Learn how to use SharePoint for ${e}`,contentJp:`SharePointを使用して${e}を行う方法を学ぶ`,difficulty:"beginner",estimatedTime:15,interactive:!1}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return`/images/sharepoint/step-${e.order}-${e.title.toLowerCase().replace(/\s+/g,"-")}.png`}async generateValidation(e){return e.userAction?.includes("permission")?"Verify permission level is set correctly":e.userAction?.includes("upload")?"Check if file was uploaded successfully":e.userAction?.includes("create")?"Confirm new item was created":"Verify action completed successfully"}async generateTroubleshooting(e){let t=[];return t.push("If you cannot see the SharePoint site, check with your administrator"),t.push("For upload errors, check file size limits (usually 250GB)"),t.push("If permissions don't work, wait 5 minutes for changes to apply"),t}analyzeFeedbackPatterns(e){let t=new Map,s=e.filter(e=>e.suggestion?.toLowerCase().includes("permission")).length,a=e.filter(e=>e.suggestion?.toLowerCase().includes("find")||e.suggestion?.toLowerCase().includes("navigate")).length;return t.set("permissionIssues",s),t.set("navigationIssues",a),t.set("totalFeedback",e.length),t}async adjustBehavior(e){let t=e.get("permissionIssues")||0,s=e.get("navigationIssues")||0;t>3&&console.log(`[${this.agentId}] Creating more permission-focused content`),s>3&&console.log(`[${this.agentId}] Creating more navigation guides`)}}class h extends d{constructor(){super("password-reset-agent",{serviceCategory:"password_reset",expertise:["password management","account recovery","multi-factor authentication","security best practices","Microsoft 365 accounts"],languages:["en","jp"],targetAudience:"all staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a security expert who helps non-technical users with password and account issues.

Topic: ${e}

Create a guide that:
1. Emphasizes security without causing anxiety
2. Uses simple language and clear instructions
3. Includes memory tips for passwords
4. Covers MFA setup in detail
5. Provides emergency contact information

Make it reassuring and helpful, not intimidating.
`}parseEducationalContent(e,t){try{return JSON.parse(e)}catch{return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"wizard",title:`Password Help: ${e}`,titleJp:`パスワードヘルプ: ${e}`,content:"Step-by-step password assistance",contentJp:"ステップバイステップのパスワード支援",difficulty:"beginner",estimatedTime:5,interactive:!0}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return`/images/password/step-${e.order}-secure.png`}async generateValidation(e){return"Verify security requirements are met"}async generateTroubleshooting(e){return["If you're locked out, wait 30 minutes before trying again","For immediate help, contact IT support at extension 1234","Keep your recovery email up to date"]}analyzeFeedbackPatterns(e){return new Map}async adjustBehavior(e){}}class y extends d{constructor(){super("pc-admin-agent",{serviceCategory:"pc_admin",expertise:["Windows administration","software installation","admin privileges","security policies","software deployment","troubleshooting Windows issues"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a Windows PC expert helping non-technical office workers understand administrative tasks.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Explains why admin rights are needed in simple terms
2. Shows how to properly request admin access
3. Covers security best practices
4. Provides safe software installation steps
5. Includes what to do if something goes wrong

Use office analogies (like keys to locked cabinets) to explain concepts.
Emphasize security without scaring users.

Format as JSON with interactive steps that guide users safely through PC admin tasks.
`}parseEducationalContent(e,t){try{let s=JSON.parse(e);return{type:s.type||"wizard",title:s.title||`PC Admin Guide: ${t}`,titleJp:s.titleJp||`PC管理ガイド: ${t}`,content:s.content||"",contentJp:s.contentJp||"",difficulty:"intermediate",estimatedTime:s.estimatedTime||20,interactive:!0,steps:s.steps||[],relatedTopics:s.relatedTopics||["security","software","permissions"]}}catch(e){return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"wizard",title:`PC Administration: ${e}`,titleJp:`PC管理: ${e}`,content:`Learn about PC administrative tasks for ${e}`,contentJp:`${e}のPC管理タスクについて学ぶ`,difficulty:"intermediate",estimatedTime:15,interactive:!0,steps:[]}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return e.description.includes("Control Panel")?"/images/pc-admin/control-panel-guide.png":e.description.includes("UAC")?"/images/pc-admin/uac-prompt.png":`/images/pc-admin/step-${e.order}.png`}async generateValidation(e){return e.userAction?.includes("install")?"Verify software installed correctly and appears in Programs list":e.userAction?.includes("permission")?"Check if admin rights are active (shield icon appears)":e.userAction?.includes("security")?"Confirm security settings are properly configured":"Verify the action completed without errors"}async generateTroubleshooting(e){let t=['If you see "Access Denied", you need to request admin rights first',"For installation errors, check if you have enough disk space","If the software doesn't work, try restarting your PC","Contact IT support if you see any security warnings"];return e.description.includes("install")&&(t.push("Some software requires specific versions of .NET Framework"),t.push("Antivirus may block installations - contact IT if this happens")),t}analyzeFeedbackPatterns(e){let t=new Map,s=e.filter(e=>e.suggestion?.toLowerCase().includes("security")||e.suggestion?.toLowerCase().includes("virus")).length,a=e.filter(e=>e.suggestion?.toLowerCase().includes("install")||e.suggestion?.toLowerCase().includes("software")).length;return t.set("securityConcerns",s),t.set("installationIssues",a),t}async adjustBehavior(e){(e.get("securityConcerns")||0)>3&&console.log(`[${this.agentId}] Enhancing security explanations in content`)}}class f{async processEmails(){if(!this.isProcessing){this.isProcessing=!0;try{console.log("Processing emails..."),await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error("Email processing failed:",e)}finally{this.isProcessing=!1}}}async createTicketFromEmail(e){try{return{ticketId:`TICKET-${Date.now()}`,success:!0}}catch(e){return{ticketId:"",success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getEmailStats(){return{totalEmails:0,processedEmails:0,pendingEmails:0,errorEmails:0}}constructor(){this.isProcessing=!1}}new f;class w{constructor(){this.isRunning=!1,this.agents=new Map,this.agentStatuses=new Map,this.initializeAgents()}static getInstance(){return w.instance||(w.instance=new w),w.instance}initializeAgents(){[new g,new m,new h,new y,new f].forEach(e=>{this.agents.set(e.agentId,e),this.agentStatuses.set(e.agentId,{agentId:e.agentId,category:e.specialization.serviceCategory,status:"idle",lastActivity:new Date,articlesCreated:0,articlesUpdated:0})})}async startAllAgents(){if(this.isRunning){console.log("AI Agent Manager is already running");return}this.isRunning=!0,console.log("Starting AI Agent Manager with",this.agents.size,"agents"),this.startMonitoring();let e=Array.from(this.agents.entries()).map(([e,t])=>this.startAgent(e,t));await Promise.all(e)}async startAgent(e,t){try{let s=this.agentStatuses.get(e);s&&(s.status="active",s.lastActivity=new Date),await t.runAgentLoop()}catch(s){console.error(`Error in agent ${e}:`,s);let t=this.agentStatuses.get(e);t&&(t.status="error")}}async stopAllAgents(){this.isRunning=!1,console.log("Stopping all AI agents"),this.agents.forEach((e,t)=>{let s=this.agentStatuses.get(t);s&&(s.status="idle")})}getAgentStatuses(){return Array.from(this.agentStatuses.values())}getAgentStatus(e){return this.agentStatuses.get(e)}async startMonitoring(){setInterval(async()=>{if(this.isRunning){for(let[e,t]of this.agentStatuses)await this.updateAgentStats(e,t);await this.logSystemStatus()}},6e4)}async updateAgentStats(e,t){try{let{data:s}=await u.N.from("kb_articles").select("id").eq("metadata->>createdByAgent",e).gte("created_at",new Date(Date.now()-864e5).toISOString()),{data:a}=await u.N.from("kb_articles").select("id").eq("metadata->>lastUpdatedByAgent",e).gte("updated_at",new Date(Date.now()-864e5).toISOString());t.articlesCreated=s?.length||0,t.articlesUpdated=a?.length||0}catch(t){console.error(`Error updating stats for agent ${e}:`,t)}}async logSystemStatus(){let e=Array.from(this.agentStatuses.values()).filter(e=>"active"===e.status).length,t=Array.from(this.agentStatuses.values()).reduce((e,t)=>e+t.articlesCreated,0),s=Array.from(this.agentStatuses.values()).reduce((e,t)=>e+t.articlesUpdated,0);console.log(`[AI Agent Manager] Status Update:
      - Active Agents: ${e}/${this.agents.size}
      - Articles Created (24h): ${t}
      - Articles Updated (24h): ${s}
    `),await u.N.from("agent_system_logs").insert({active_agents:e,total_agents:this.agents.size,articles_created_24h:t,articles_updated_24h:s,created_at:new Date().toISOString()})}async triggerAgentResearch(e,t){Array.from(this.agents.values()).find(t=>t.specialization.serviceCategory===e)?console.log(`Triggering ${e} agent to research: ${t}`):console.error(`No agent found for category: ${e}`)}async getAgentMetrics(e="day"){let t=new Date;switch(e){case"day":t.setDate(t.getDate()-1);break;case"week":t.setDate(t.getDate()-7);break;case"month":t.setMonth(t.getMonth()-1)}let s=new Map;for(let[e,a]of this.agents){let{data:i}=await u.N.from("agent_activity_logs").select("*").eq("agent_id",e).gte("created_at",t.toISOString()),{data:r}=await u.N.from("kb_articles").select("id, metadata").or(`metadata->>createdByAgent.eq.${e},metadata->>lastUpdatedByAgent.eq.${e}`).gte("created_at",t.toISOString());s.set(e,{activities:i?.length||0,articlesCreated:r?.filter(t=>t.metadata?.createdByAgent===e).length||0,articlesUpdated:r?.filter(t=>t.metadata?.lastUpdatedByAgent===e).length||0,category:a.specialization.serviceCategory})}return Object.fromEntries(s)}}let v=w.getInstance();async function b(e){try{let e=(0,c.createRouteHandlerClient)({cookies:l.UL}),{data:{user:t}}=await e.auth.getUser();if(!t)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=v.getAgentStatuses(),{data:a}=await e.from("agent_educational_content").select("*").order("created_at",{ascending:!1}).limit(10),{data:i}=await e.from("user_learning_progress").select("*").eq("user_id",t.id);return o.NextResponse.json({agents:s,recentContent:a||[],userProgress:i||[],systemStatus:s.some(e=>"active"===e.status)?"running":"stopped"})}catch(e){return console.error("AI Agents API error:",e),o.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}async function A(e){try{let t=(0,c.createRouteHandlerClient)({cookies:l.UL}),{data:{user:s}}=await t.auth.getUser();if(!s)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await t.from("staff").select("role:roles(name)").eq("auth_id",s.id).single();if(!a||!["Global Administrator","Web App System Administrator"].includes(a.role?.name))return o.NextResponse.json({error:"Insufficient permissions"},{status:403});let{action:i,agentId:r,data:n}=await e.json();switch(i){case"start_all":return await v.startAllAgents(),o.NextResponse.json({success:!0,message:"All agents started"});case"stop_all":return await v.stopAllAgents(),o.NextResponse.json({success:!0,message:"All agents stopped"});case"trigger_research":if(r&&n?.topic)return await v.triggerAgentResearch(r,n.topic),o.NextResponse.json({success:!0,message:`Research triggered for ${r}`});return o.NextResponse.json({error:"Missing agentId or topic"},{status:400});default:return o.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("AI Agents API error:",e),o.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}let k=new i.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/ai-agents/route",pathname:"/api/ai-agents",filename:"route",bundlePath:"app/api/ai-agents/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\ai-agents\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:_,workUnitAsyncStorage:S,serverHooks:C}=k;function I(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:S})}},96487:()=>{},78335:()=>{},45895:(e,t,s)=>{"use strict";class a{constructor(){this.providers=new Map,this.defaultProvider="openai",this.initializeProviders()}initializeProviders(){let e=process.env.NEXT_PUBLIC_OPENAI_API_KEY||process.env.OPENAI_API_KEY;e&&this.providers.set("openai",{name:"openai",apiKey:e,baseUrl:"https://api.openai.com/v1"});let t=process.env.NEXT_PUBLIC_ANTHROPIC_API_KEY||process.env.ANTHROPIC_API_KEY;t&&this.providers.set("anthropic",{name:"anthropic",apiKey:t,baseUrl:"https://api.anthropic.com/v1"})}setDefaultProvider(e){if(this.providers.has(e))this.defaultProvider=e;else throw Error(`Provider ${e} is not configured`)}async completion(e,t){let s=t||this.defaultProvider,a=this.providers.get(s);if(!a)throw Error(`Provider ${s} is not configured`);switch(s){case"openai":return this.openAICompletion(e,a);case"anthropic":return this.anthropicCompletion(e,a);default:throw Error(`Unsupported provider: ${s}`)}}async openAICompletion(e,t){let s=e.model||"gpt-4-turbo-preview",a={model:s,messages:[...e.systemPrompt?[{role:"system",content:e.systemPrompt}]:[],{role:"user",content:e.prompt}],temperature:e.temperature??.7,max_tokens:e.maxTokens??1e3};"json"===e.responseFormat&&(a.response_format={type:"json_object"});let i=await fetch(`${t.baseUrl}/chat/completions`,{method:"POST",headers:{Authorization:`Bearer ${t.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok)throw Error(`OpenAI API error: ${i.statusText}`);let r=await i.json();return{text:r.choices[0].message.content,usage:r.usage?{promptTokens:r.usage.prompt_tokens,completionTokens:r.usage.completion_tokens,totalTokens:r.usage.total_tokens}:void 0,provider:"openai",model:s}}async anthropicCompletion(e,t){let s=e.model||"claude-3-opus-20240229",a={model:s,messages:[{role:"user",content:e.prompt}],system:e.systemPrompt,temperature:e.temperature??.7,max_tokens:e.maxTokens??1e3},i=await fetch(`${t.baseUrl}/messages`,{method:"POST",headers:{"x-api-key":t.apiKey,"anthropic-version":"2023-06-01","Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok)throw Error(`Anthropic API error: ${i.statusText}`);let r=await i.json();return{text:r.content[0].text,usage:r.usage?{promptTokens:r.usage.input_tokens,completionTokens:r.usage.output_tokens,totalTokens:r.usage.input_tokens+r.usage.output_tokens}:void 0,provider:"anthropic",model:s}}async embedding(e,t="openai"){let s=this.providers.get(t);if(!s)throw Error(`Provider ${t} is not configured`);if("openai"!==t)throw Error("Embeddings are currently only supported with OpenAI");let a=e.model||"text-embedding-3-small",i=Array.isArray(e.text)?e.text:[e.text],r=await fetch(`${s.baseUrl}/embeddings`,{method:"POST",headers:{Authorization:`Bearer ${s.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({model:a,input:i})});if(!r.ok)throw Error(`OpenAI Embeddings API error: ${r.statusText}`);let n=await r.json();return{embeddings:n.data.map(e=>e.embedding),usage:n.usage?{totalTokens:n.usage.total_tokens}:void 0}}async validateFormField(e,t,s){let a=`You are validating form fields for a Japanese IT helpdesk system.
Field type: ${e}
Context: ${JSON.stringify(s||{})}

Validate the input and provide:
1. Whether it's valid (true/false)
2. List of issues if invalid
3. Suggestions for correction

Return as JSON with keys: isValid, issues, suggestions`;return JSON.parse((await this.completion({systemPrompt:a,prompt:`Validate this value: "${t}"`,temperature:.3,responseFormat:"json"})).text)}async generateFormSuggestions(e,t,s){let a=`Generate autocomplete suggestions for a Japanese IT helpdesk form field.
Field type: ${e}
Context: ${JSON.stringify(s||{})}

Provide 3-5 relevant suggestions based on the partial input.
Return as JSON array of strings.`;return JSON.parse((await this.completion({systemPrompt:a,prompt:`Partial value: "${t}"`,temperature:.5,responseFormat:"json"})).text)}async classifyRequest(e,t){let s=`Classify IT helpdesk requests into categories.
Available categories: ${t.join(", ")}

Return JSON with: category (exact match from list), confidence (0-1)`;return JSON.parse((await this.completion({systemPrompt:s,prompt:e,temperature:.3,responseFormat:"json"})).text)}async translateText(e,t,s){return t===s?e:(await this.completion({systemPrompt:`Translate the following text from ${t} to ${s}. 
Maintain technical terminology appropriate for IT helpdesk context.
Return only the translated text.`,prompt:e,temperature:.3})).text}estimateCost(e,t){let s={"gpt-4-turbo-preview":{prompt:.01,completion:.03},"gpt-4":{prompt:.03,completion:.06},"gpt-3.5-turbo":{prompt:5e-4,completion:.0015},"claude-3-opus-20240229":{prompt:.015,completion:.075},"claude-3-sonnet-20240229":{prompt:.003,completion:.015}}[t]||{prompt:.01,completion:.03};return e.promptTokens/1e3*s.prompt+e.completionTokens/1e3*s.completion}isConfigured(e){return e?this.providers.has(e):this.providers.size>0}getAvailableProviders(){return Array.from(this.providers.keys())}}new a},73865:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});let a=(0,s(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[5994,5452,4512,3406],()=>s(4399));module.exports=a})();