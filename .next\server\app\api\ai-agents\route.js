"use strict";(()=>{var e={};e.id=7551,e.ids=[7551],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},94735:e=>{e.exports=require("events")},81630:e=>{e.exports=require("http")},55591:e=>{e.exports=require("https")},28354:e=>{e.exports=require("util")},74075:e=>{e.exports=require("zlib")},91121:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>_,routeModule:()=>A,serverHooks:()=>k,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>S});var s={};a.r(s),a.d(s,{GET:()=>v,POST:()=>b});var i=a(42706),n=a(28203),r=a(45994),o=a(39187),c=a(33406),l=a(44512),u=a(73865),d=a(45895);class p{constructor(e,t){this.agentId=e,this.specialization=t,this.lastActivityTime=new Date,this.learningHistory=new Map}async runAgentLoop(){for(console.log(`[${this.agentId}] Agent starting...`);;)try{for(let e of(await this.identifyResearchTopics())){let t=await this.scrapeWebContent(e),a=await this.transformToEducational(t,e),s=await this.createInteractiveExperience(a);await this.storeInKnowledgeBase(s),await this.updateEmbeddings(s)}await this.learnFromFeedback(),await this.sleep(3e5)}catch(e){console.error(`[${this.agentId}] Agent error:`,e),await this.sleep(6e4)}}async identifyResearchTopics(){let e=[],{data:t}=await u.N.from("kb_search_history").select("query").eq("search_type","ai").or("clicked_results.is.null,clicked_results.eq.{}").ilike("query",`%${this.specialization.serviceCategory}%`).gte("created_at",new Date(Date.now()-864e5).toISOString()).limit(20);t&&e.push(...t.map(e=>e.query));let{data:a}=await u.N.from("kb_content_gaps").select("topic").eq("suggested_category",this.specialization.serviceCategory).eq("status","identified").limit(10);a&&e.push(...a.map(e=>e.topic));let{data:s}=await u.N.from("kb_article_feedback").select("suggestion").in("feedback_type",["not_helpful","outdated"]).not("suggestion","is",null).limit(10);return s&&e.push(...s.map(e=>e.suggestion)),[...new Set(e)].slice(0,5)}async scrapeWebContent(e){let t=[],a=`${e} ${this.specialization.expertise.join(" ")} tutorial guide`;try{for(let s of(await this.performWebSearch(a)).slice(0,5)){let a=await this.scrapeUrl(s.url);a&&t.push({url:s.url,title:s.title,content:a,relevance:await this.calculateRelevance(a,e),lastUpdated:new Date})}}catch(e){console.error(`[${this.agentId}] Web scraping error:`,e)}return t.sort((e,t)=>t.relevance-e.relevance)}async transformToEducational(e,t){let a=e.slice(0,3).map(e=>e.content).join("\n\n"),s=this.buildEducationalPrompt(t,a),i=await d.aiApiService.generateText(s,{temperature:.3,maxTokens:3e3});return this.parseEducationalContent(i,t)}async createInteractiveExperience(e){if(!e.interactive||!e.steps)return e;let t=await Promise.all(e.steps.map(async e=>(!e.visualAid&&this.shouldHaveVisual(e)&&(e.visualAid=await this.generateVisualAid(e)),!e.validation&&e.userAction&&(e.validation=await this.generateValidation(e)),e.troubleshooting&&0!==e.troubleshooting.length||(e.troubleshooting=await this.generateTroubleshooting(e)),e)));return e.steps=t,e}async storeInKnowledgeBase(e){try{let t=await this.findSimilarContent(e.title);if(t)await u.N.from("kb_articles").update({content_en:e.content,content_jp:e.contentJp,metadata:{type:e.type,difficulty:e.difficulty,estimatedTime:e.estimatedTime,interactive:e.interactive,steps:e.steps,lastUpdatedByAgent:this.agentId,agentVersion:"1.0"},updated_at:new Date().toISOString(),last_auto_update:new Date().toISOString()}).eq("id",t.id);else{let{data:t,error:a}=await u.N.from("kb_articles").insert({title_en:e.title,title_jp:e.titleJp,content_en:e.content,content_jp:e.contentJp,category:this.specialization.serviceCategory,tags:e.relatedTopics||[],status:"published",author_id:null,metadata:{type:e.type,difficulty:e.difficulty,estimatedTime:e.estimatedTime,interactive:e.interactive,steps:e.steps,createdByAgent:this.agentId,agentVersion:"1.0"}}).select().single();if(a)throw a;await this.logActivity("article_created",{articleId:t.id,title:e.title,type:e.type})}}catch(e){console.error(`[${this.agentId}] Error storing content:`,e)}}async updateEmbeddings(e){}async learnFromFeedback(){let{data:e}=await u.N.from("kb_article_feedback").select(`
        *,
        article:kb_articles!inner(
          id,
          metadata
        )
      `).eq("article.metadata->>createdByAgent",this.agentId).gte("created_at",new Date(Date.now()-864e5).toISOString());if(e&&e.length>0){let t=this.analyzeFeedbackPatterns(e);t.forEach((e,t)=>{this.learningHistory.set(t,e)}),await this.adjustBehavior(t)}}async performWebSearch(e){return[]}async scrapeUrl(e){return null}async calculateRelevance(e,t){let a=t.toLowerCase().split(" "),s=e.toLowerCase(),i=0;return a.forEach(e=>{s.includes(e)&&i++}),i/a.length}async findSimilarContent(e){let{data:t}=await u.N.from("kb_articles").select("id, title_en").eq("category",this.specialization.serviceCategory).ilike("title_en",`%${e}%`).limit(1).single();return t}async logActivity(e,t){await u.N.from("agent_activity_logs").insert({agent_id:this.agentId,action:e,details:t,created_at:new Date().toISOString()})}async sleep(e){return new Promise(t=>setTimeout(t,e))}}class g extends p{constructor(){super("group-mail-agent",{serviceCategory:"group_mail",expertise:["email distribution lists","group mail management","Microsoft Exchange","email permissions","distribution group best practices"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!1,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are an expert in email and group mail management, specializing in making complex concepts simple for non-technical users.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Explains the concept in simple, non-technical terms
2. Provides step-by-step instructions with clear actions
3. Includes common mistakes to avoid
4. Offers troubleshooting tips for typical issues
5. Uses analogies that office workers can relate to

Format the response as JSON:
{
  "type": "guide" | "tutorial" | "wizard",
  "title": "English title",
  "titleJp": "Japanese title",
  "content": "Main content in simple English",
  "contentJp": "Main content in simple Japanese",
  "difficulty": "beginner",
  "estimatedTime": 10,
  "interactive": true,
  "steps": [
    {
      "order": 1,
      "title": "Step title",
      "titleJp": "Japanese step title",
      "description": "What to do",
      "descriptionJp": "Japanese description",
      "userAction": "Click on...",
      "troubleshooting": ["If you see X, try Y"]
    }
  ],
  "relatedTopics": ["topic1", "topic2"]
}

Focus on making it accessible for someone who has never managed email groups before.
`}parseEducationalContent(e,t){try{let a=JSON.parse(e);return{type:a.type||"guide",title:a.title||t,titleJp:a.titleJp||t,content:a.content||"",contentJp:a.contentJp||"",difficulty:"beginner",estimatedTime:a.estimatedTime||15,interactive:!0,steps:a.steps||[],relatedTopics:a.relatedTopics||[]}}catch(e){return{type:"guide",title:`Guide: ${t}`,titleJp:`ガイド: ${t}`,content:`Learn about ${t}`,contentJp:`${t}について学ぶ`,difficulty:"beginner",estimatedTime:10,interactive:!1}}}shouldHaveVisual(e){return["click","select","button","menu","screen"].some(t=>e.description.toLowerCase().includes(t))}async generateVisualAid(e){return`/images/group-mail/step-${e.order}.png`}async generateValidation(e){return e.userAction?.includes("email address")?"Check if email address format is valid":e.userAction?.includes("select")?"Verify at least one option is selected":"Confirm action was completed"}async generateTroubleshooting(e){let t=[];return e.description.includes("permission")&&t.push('If you see "Access Denied", contact your department administrator'),e.description.includes("email")&&(t.push("If the email address is not found, check the spelling"),t.push("For external emails, you may need special approval")),e.description.includes("group")&&t.push("If the group doesn't appear, it may not be assigned to your department"),t}analyzeFeedbackPatterns(e){let t=new Map,a=e.map(e=>e.feedback_type),s=new Map;a.forEach(e=>{s.set(e,(s.get(e)||0)+1)}),t.set("commonIssues",Array.from(s.entries())),t.set("totalFeedback",e.length);let i=e.filter(e=>e.suggestion).map(e=>e.suggestion);return t.set("userSuggestions",i),t}async adjustBehavior(e){for(let[t,a]of e.get("commonIssues")||[])"outdated"===t&&a>3&&console.log(`[${this.agentId}] Increasing update frequency due to outdated content feedback`),"not_helpful"===t&&a>5&&console.log(`[${this.agentId}] Simplifying content due to not helpful feedback`)}}class h extends p{constructor(){super("sharepoint-agent",{serviceCategory:"sharepoint",expertise:["SharePoint Online","document libraries","permissions management","SharePoint sites","Microsoft 365 integration"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a SharePoint expert who specializes in teaching non-technical users.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Demystifies SharePoint concepts using office analogies (folders, filing cabinets, etc.)
2. Provides visual step-by-step instructions
3. Includes Japanese translations for all UI elements
4. Covers common permission scenarios
5. Explains best practices for document organization

Include specific examples relevant to Japanese corporate culture.

Format as JSON with the same structure as before, ensuring all content is beginner-friendly.
`}parseEducationalContent(e,t){try{let a=JSON.parse(e);return{type:a.type||"tutorial",title:a.title||t,titleJp:a.titleJp||t,content:a.content||"",contentJp:a.contentJp||"",difficulty:"beginner",estimatedTime:a.estimatedTime||20,interactive:!0,steps:a.steps||[],relatedTopics:a.relatedTopics||[]}}catch(e){return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"tutorial",title:`SharePoint Tutorial: ${e}`,titleJp:`SharePointチュートリアル: ${e}`,content:`Learn how to use SharePoint for ${e}`,contentJp:`SharePointを使用して${e}を行う方法を学ぶ`,difficulty:"beginner",estimatedTime:15,interactive:!1}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return`/images/sharepoint/step-${e.order}-${e.title.toLowerCase().replace(/\s+/g,"-")}.png`}async generateValidation(e){return e.userAction?.includes("permission")?"Verify permission level is set correctly":e.userAction?.includes("upload")?"Check if file was uploaded successfully":e.userAction?.includes("create")?"Confirm new item was created":"Verify action completed successfully"}async generateTroubleshooting(e){let t=[];return t.push("If you cannot see the SharePoint site, check with your administrator"),t.push("For upload errors, check file size limits (usually 250GB)"),t.push("If permissions don't work, wait 5 minutes for changes to apply"),t}analyzeFeedbackPatterns(e){let t=new Map,a=e.filter(e=>e.suggestion?.toLowerCase().includes("permission")).length,s=e.filter(e=>e.suggestion?.toLowerCase().includes("find")||e.suggestion?.toLowerCase().includes("navigate")).length;return t.set("permissionIssues",a),t.set("navigationIssues",s),t.set("totalFeedback",e.length),t}async adjustBehavior(e){let t=e.get("permissionIssues")||0,a=e.get("navigationIssues")||0;t>3&&console.log(`[${this.agentId}] Creating more permission-focused content`),a>3&&console.log(`[${this.agentId}] Creating more navigation guides`)}}class m extends p{constructor(){super("password-reset-agent",{serviceCategory:"password_reset",expertise:["password management","account recovery","multi-factor authentication","security best practices","Microsoft 365 accounts"],languages:["en","jp"],targetAudience:"all staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a security expert who helps non-technical users with password and account issues.

Topic: ${e}

Create a guide that:
1. Emphasizes security without causing anxiety
2. Uses simple language and clear instructions
3. Includes memory tips for passwords
4. Covers MFA setup in detail
5. Provides emergency contact information

Make it reassuring and helpful, not intimidating.
`}parseEducationalContent(e,t){try{return JSON.parse(e)}catch{return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"wizard",title:`Password Help: ${e}`,titleJp:`パスワードヘルプ: ${e}`,content:"Step-by-step password assistance",contentJp:"ステップバイステップのパスワード支援",difficulty:"beginner",estimatedTime:5,interactive:!0}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return`/images/password/step-${e.order}-secure.png`}async generateValidation(e){return"Verify security requirements are met"}async generateTroubleshooting(e){return["If you're locked out, wait 30 minutes before trying again","For immediate help, contact IT support at extension 1234","Keep your recovery email up to date"]}analyzeFeedbackPatterns(e){return new Map}async adjustBehavior(e){}}class y extends p{constructor(){super("pc-admin-agent",{serviceCategory:"pc_admin",expertise:["Windows administration","software installation","admin privileges","security policies","software deployment","troubleshooting Windows issues"],languages:["en","jp"],targetAudience:"non-technical staff",capabilities:{webScraping:!0,contentTranslation:!0,interactiveGuides:!0,videoTutorials:!0,stepByStepWizards:!0,troubleshooting:!0,realTimeAssistance:!0}})}buildEducationalPrompt(e,t){return`
You are a Windows PC expert helping non-technical office workers understand administrative tasks.

Topic: ${e}

Reference Content:
${t}

Create an educational guide that:
1. Explains why admin rights are needed in simple terms
2. Shows how to properly request admin access
3. Covers security best practices
4. Provides safe software installation steps
5. Includes what to do if something goes wrong

Use office analogies (like keys to locked cabinets) to explain concepts.
Emphasize security without scaring users.

Format as JSON with interactive steps that guide users safely through PC admin tasks.
`}parseEducationalContent(e,t){try{let a=JSON.parse(e);return{type:a.type||"wizard",title:a.title||`PC Admin Guide: ${t}`,titleJp:a.titleJp||`PC管理ガイド: ${t}`,content:a.content||"",contentJp:a.contentJp||"",difficulty:"intermediate",estimatedTime:a.estimatedTime||20,interactive:!0,steps:a.steps||[],relatedTopics:a.relatedTopics||["security","software","permissions"]}}catch(e){return this.createFallbackContent(t)}}createFallbackContent(e){return{type:"wizard",title:`PC Administration: ${e}`,titleJp:`PC管理: ${e}`,content:`Learn about PC administrative tasks for ${e}`,contentJp:`${e}のPC管理タスクについて学ぶ`,difficulty:"intermediate",estimatedTime:15,interactive:!0,steps:[]}}shouldHaveVisual(e){return!0}async generateVisualAid(e){return e.description.includes("Control Panel")?"/images/pc-admin/control-panel-guide.png":e.description.includes("UAC")?"/images/pc-admin/uac-prompt.png":`/images/pc-admin/step-${e.order}.png`}async generateValidation(e){return e.userAction?.includes("install")?"Verify software installed correctly and appears in Programs list":e.userAction?.includes("permission")?"Check if admin rights are active (shield icon appears)":e.userAction?.includes("security")?"Confirm security settings are properly configured":"Verify the action completed without errors"}async generateTroubleshooting(e){let t=['If you see "Access Denied", you need to request admin rights first',"For installation errors, check if you have enough disk space","If the software doesn't work, try restarting your PC","Contact IT support if you see any security warnings"];return e.description.includes("install")&&(t.push("Some software requires specific versions of .NET Framework"),t.push("Antivirus may block installations - contact IT if this happens")),t}analyzeFeedbackPatterns(e){let t=new Map,a=e.filter(e=>e.suggestion?.toLowerCase().includes("security")||e.suggestion?.toLowerCase().includes("virus")).length,s=e.filter(e=>e.suggestion?.toLowerCase().includes("install")||e.suggestion?.toLowerCase().includes("software")).length;return t.set("securityConcerns",a),t.set("installationIssues",s),t}async adjustBehavior(e){(e.get("securityConcerns")||0)>3&&console.log(`[${this.agentId}] Enhancing security explanations in content`)}}!function(){var e=Error("Cannot find module './mailbox-agent'");throw e.code="MODULE_NOT_FOUND",e}();class f{constructor(){this.isRunning=!1,this.agents=new Map,this.agentStatuses=new Map,this.initializeAgents()}static getInstance(){return f.instance||(f.instance=new f),f.instance}initializeAgents(){[new g,new h,new m,new y,Object(function(){var e=Error("Cannot find module './mailbox-agent'");throw e.code="MODULE_NOT_FOUND",e}())()].forEach(e=>{this.agents.set(e.agentId,e),this.agentStatuses.set(e.agentId,{agentId:e.agentId,category:e.specialization.serviceCategory,status:"idle",lastActivity:new Date,articlesCreated:0,articlesUpdated:0})})}async startAllAgents(){if(this.isRunning){console.log("AI Agent Manager is already running");return}this.isRunning=!0,console.log("Starting AI Agent Manager with",this.agents.size,"agents"),this.startMonitoring();let e=Array.from(this.agents.entries()).map(([e,t])=>this.startAgent(e,t));await Promise.all(e)}async startAgent(e,t){try{let a=this.agentStatuses.get(e);a&&(a.status="active",a.lastActivity=new Date),await t.runAgentLoop()}catch(a){console.error(`Error in agent ${e}:`,a);let t=this.agentStatuses.get(e);t&&(t.status="error")}}async stopAllAgents(){this.isRunning=!1,console.log("Stopping all AI agents"),this.agents.forEach((e,t)=>{let a=this.agentStatuses.get(t);a&&(a.status="idle")})}getAgentStatuses(){return Array.from(this.agentStatuses.values())}getAgentStatus(e){return this.agentStatuses.get(e)}async startMonitoring(){setInterval(async()=>{if(this.isRunning){for(let[e,t]of this.agentStatuses)await this.updateAgentStats(e,t);await this.logSystemStatus()}},6e4)}async updateAgentStats(e,t){try{let{data:a}=await u.N.from("kb_articles").select("id").eq("metadata->>createdByAgent",e).gte("created_at",new Date(Date.now()-864e5).toISOString()),{data:s}=await u.N.from("kb_articles").select("id").eq("metadata->>lastUpdatedByAgent",e).gte("updated_at",new Date(Date.now()-864e5).toISOString());t.articlesCreated=a?.length||0,t.articlesUpdated=s?.length||0}catch(t){console.error(`Error updating stats for agent ${e}:`,t)}}async logSystemStatus(){let e=Array.from(this.agentStatuses.values()).filter(e=>"active"===e.status).length,t=Array.from(this.agentStatuses.values()).reduce((e,t)=>e+t.articlesCreated,0),a=Array.from(this.agentStatuses.values()).reduce((e,t)=>e+t.articlesUpdated,0);console.log(`[AI Agent Manager] Status Update:
      - Active Agents: ${e}/${this.agents.size}
      - Articles Created (24h): ${t}
      - Articles Updated (24h): ${a}
    `),await u.N.from("agent_system_logs").insert({active_agents:e,total_agents:this.agents.size,articles_created_24h:t,articles_updated_24h:a,created_at:new Date().toISOString()})}async triggerAgentResearch(e,t){Array.from(this.agents.values()).find(t=>t.specialization.serviceCategory===e)?console.log(`Triggering ${e} agent to research: ${t}`):console.error(`No agent found for category: ${e}`)}async getAgentMetrics(e="day"){let t=new Date;switch(e){case"day":t.setDate(t.getDate()-1);break;case"week":t.setDate(t.getDate()-7);break;case"month":t.setMonth(t.getMonth()-1)}let a=new Map;for(let[e,s]of this.agents){let{data:i}=await u.N.from("agent_activity_logs").select("*").eq("agent_id",e).gte("created_at",t.toISOString()),{data:n}=await u.N.from("kb_articles").select("id, metadata").or(`metadata->>createdByAgent.eq.${e},metadata->>lastUpdatedByAgent.eq.${e}`).gte("created_at",t.toISOString());a.set(e,{activities:i?.length||0,articlesCreated:n?.filter(t=>t.metadata?.createdByAgent===e).length||0,articlesUpdated:n?.filter(t=>t.metadata?.lastUpdatedByAgent===e).length||0,category:s.specialization.serviceCategory})}return Object.fromEntries(a)}}let w=f.getInstance();async function v(e){try{let e=(0,c.createRouteHandlerClient)({cookies:l.UL}),{data:{user:t}}=await e.auth.getUser();if(!t)return o.NextResponse.json({error:"Unauthorized"},{status:401});let a=w.getAgentStatuses(),{data:s}=await e.from("agent_educational_content").select("*").order("created_at",{ascending:!1}).limit(10),{data:i}=await e.from("user_learning_progress").select("*").eq("user_id",t.id);return o.NextResponse.json({agents:a,recentContent:s||[],userProgress:i||[],systemStatus:a.some(e=>"active"===e.status)?"running":"stopped"})}catch(e){return console.error("AI Agents API error:",e),o.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}async function b(e){try{let t=(0,c.createRouteHandlerClient)({cookies:l.UL}),{data:{user:a}}=await t.auth.getUser();if(!a)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await t.from("staff").select("role:roles(name)").eq("auth_id",a.id).single();if(!s||!["Global Administrator","Web App System Administrator"].includes(s.role?.name))return o.NextResponse.json({error:"Insufficient permissions"},{status:403});let{action:i,agentId:n,data:r}=await e.json();switch(i){case"start_all":return await w.startAllAgents(),o.NextResponse.json({success:!0,message:"All agents started"});case"stop_all":return await w.stopAllAgents(),o.NextResponse.json({success:!0,message:"All agents stopped"});case"trigger_research":if(n&&r?.topic)return await w.triggerAgentResearch(n,r.topic),o.NextResponse.json({success:!0,message:`Research triggered for ${n}`});return o.NextResponse.json({error:"Missing agentId or topic"},{status:400});default:return o.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("AI Agents API error:",e),o.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}let A=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai-agents/route",pathname:"/api/ai-agents",filename:"route",bundlePath:"app/api/ai-agents/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\ai-agents\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:C,workUnitAsyncStorage:S,serverHooks:k}=A;function _(){return(0,r.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:S})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>a(91121));module.exports=s})();