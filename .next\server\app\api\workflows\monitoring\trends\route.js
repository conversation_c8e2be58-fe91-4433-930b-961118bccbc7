"use strict";(()=>{var e={};e.id=1736,e.ids=[1736],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},93729:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>k,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{GET:()=>g});var s=r(42706),o=r(28203),n=r(45994),i=r(39187),d=r(73865),p=r(44512),u=r(38345),l=r(44576),c=r(89623),w=r(99065);async function g(e){try{let{searchParams:t}=new URL(e.url),r=t.get("timeRange")||"7d",a=await (0,p.UL)(),s=(0,d.createServerSupabase)(a),o=new Date,n=new Date;switch(r){case"24h":n.setDate(o.getDate()-1);break;case"7d":default:n.setDate(o.getDate()-7);break;case"30d":n.setDate(o.getDate()-30);break;case"90d":n.setDate(o.getDate()-90)}let g=(0,u.k)({start:n,end:o}),{data:m}=await s.from("workflow_instances").select("created_at, completed_at, status").gte("created_at",n.toISOString()).lte("created_at",o.toISOString()),f=g.map(e=>{let t=(0,w.o)(e),r=(0,l.D)(e),a=m?.filter(e=>{let a=new Date(e.created_at);return a>=t&&a<=r})||[],s=a.filter(e=>"completed"===e.status&&e.completed_at&&new Date(e.completed_at)>=t&&new Date(e.completed_at)<=r).length,o=a.filter(e=>"failed"===e.status).length,n=a.length;return{date:(0,c.GP)(e,"MMM dd"),created:n,completed:s,failed:o}});return i.NextResponse.json(f)}catch(e){return console.error("Error fetching workflow trends:",e),i.NextResponse.json({error:"Failed to fetch workflow trends"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/trends/route",pathname:"/api/workflows/monitoring/trends",filename:"route",bundlePath:"app/api/workflows/monitoring/trends/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\trends\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:k}=m;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>r(93729));module.exports=a})();