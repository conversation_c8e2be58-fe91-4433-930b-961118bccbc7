(()=>{var e={};e.id=1736,e.ids=[1736],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},23097:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>k,routeModule:()=>g,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>w});var a=r(42706),o=r(28203),n=r(45994),i=r(39187),u=r(73865),d=r(44512),p=r(55657),c=r(99065),l=r(3744);async function w(e){try{let{searchParams:t}=new URL(e.url),r=t.get("timeRange")||"7d",s=await (0,d.UL)(),a=(0,u.createServerSupabase)(s),o=new Date,n=new Date;switch(r){case"24h":n.setDate(o.getDate()-1);break;case"7d":default:n.setDate(o.getDate()-7);break;case"30d":n.setDate(o.getDate()-30);break;case"90d":n.setDate(o.getDate()-90)}let w=function(e,t){let r=(0,p.a)(e.start),s=(0,p.a)(e.end),a=+r>+s,o=a?+r:+s,n=a?s:r;n.setHours(0,0,0,0);let i=(void 0)??1;if(!i)return[];i<0&&(i=-i,a=!a);let u=[];for(;+n<=o;)u.push((0,p.a)(n)),n.setDate(n.getDate()+i),n.setHours(0,0,0,0);return a?u.reverse():u}({start:n,end:o}),{data:g}=await a.from("workflow_instances").select("created_at, completed_at, status").gte("created_at",n.toISOString()).lte("created_at",o.toISOString()),f=w.map(e=>{let t=(0,c.o)(e),r=function(e){let t=(0,p.a)(e);return t.setHours(23,59,59,999),t}(e),s=g?.filter(e=>{let s=new Date(e.created_at);return s>=t&&s<=r})||[],a=s.filter(e=>"completed"===e.status&&e.completed_at&&new Date(e.completed_at)>=t&&new Date(e.completed_at)<=r).length,o=s.filter(e=>"failed"===e.status).length,n=s.length;return{date:(0,l.GP)(e,"MMM dd"),created:n,completed:a,failed:o}});return i.NextResponse.json(f)}catch(e){return console.error("Error fetching workflow trends:",e),i.NextResponse.json({error:"Failed to fetch workflow trends"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/trends/route",pathname:"/api/workflows/monitoring/trends",filename:"route",bundlePath:"app/api/workflows/monitoring/trends/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\trends\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:x}=g;function k(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},73865:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,5452,4512,3744],()=>r(23097));module.exports=s})();