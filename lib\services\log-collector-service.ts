import { auditService } from '@/lib/services/enhanced-audit-service';
import { supabase } from '@/lib/supabase';

export interface DatabaseChangeEvent {
  schema: string;
  table: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  oldRecord?: any;
  newRecord?: any;
  timestamp: string;
  userId?: string;
}

export interface SystemEvent {
  type: 'startup' | 'shutdown' | 'error' | 'config_change' | 'integration';
  source: string;
  message: string;
  metadata?: any;
  timestamp: string;
}

export interface APIRequestEvent {
  method: string;
  path: string;
  statusCode: number;
  duration: number;
  userId?: string;
  requestBody?: any;
  responseBody?: any;
  timestamp: string;
}

/**
 * Log Collector Service
 * Collects logs from various sources within the application
 */
class LogCollectorService {
  private static instance: LogCollectorService;
  private initialized = false;

  private constructor() {}

  public static getInstance(): LogCollectorService {
    if (!LogCollectorService.instance) {
      LogCollectorService.instance = new LogCollectorService();
    }
    return LogCollectorService.instance;
  }

  /**
   * Initialize log collectors
   */
  public async initialize() {
    if (this.initialized) return;
    
    // Set up database change listeners
    this.setupDatabaseChangeListeners();
    
    // Set up global error handler
    this.setupErrorHandler();
    
    // Set up navigation tracking
    this.setupNavigationTracking();
    
    // Set up API interceptors
    this.setupAPIInterceptors();
    
    this.initialized = true;
  }

  /**
   * Setup database change listeners
   */
  private setupDatabaseChangeListeners() {
    // Critical tables to monitor
    const criticalTables = [
      'users', 'staff', 'roles', 'request_forms', 'request_items',
      'group_mail_members', 'mailbox_members', 'sharepoint_access'
    ];

    criticalTables.forEach(table => {
      supabase
        .channel(`db-changes-${table}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table
          },
          async (payload) => {
            await this.handleDatabaseChange({
              schema: payload.schema,
              table: payload.table,
              operation: payload.eventType as any,
              oldRecord: payload.old,
              newRecord: payload.new,
              timestamp: new Date().toISOString()
            });
          }
        )
        .subscribe();
    });
  }

  /**
   * Handle database change events
   */
  private async handleDatabaseChange(event: DatabaseChangeEvent) {
    const eventTypeMap = {
      'INSERT': '_CREATED',
      'UPDATE': '_UPDATED',
      'DELETE': '_DELETED'
    };

    const tableName = event.table.toUpperCase();
    const eventType = `${tableName}${eventTypeMap[event.operation]}` as any;

    await auditService.log({
      event_type: eventType,
      severity: 'INFO',
      action: `${event.operation} on ${event.table}`,
      entity_type: event.table,
      entity_id: event.newRecord?.id || event.oldRecord?.id,
      old_value: event.oldRecord,
      new_value: event.newRecord,
      metadata: {
        schema: event.schema,
        operation: event.operation
      }
    });
  }

  /**
   * Setup global error handler
   */
  private setupErrorHandler() {
    if (typeof window !== 'undefined') {
      const originalError = window.onerror;
      window.onerror = (message, source, lineno, colno, error) => {
        this.logSystemEvent({
          type: 'error',
          source: 'window.onerror',
          message: message?.toString() || 'Unknown error',
          metadata: {
            source,
            lineno,
            colno,
            stack: error?.stack
          },
          timestamp: new Date().toISOString()
        });
        
        if (originalError) {
          return originalError(message, source, lineno, colno, error);
        }
        return false;
      };

      const originalUnhandledRejection = window.onunhandledrejection;
      window.onunhandledrejection = (event) => {
        this.logSystemEvent({
          type: 'error',
          source: 'unhandledrejection',
          message: event.reason?.message || 'Unhandled promise rejection',
          metadata: {
            reason: event.reason,
            promise: event.promise
          },
          timestamp: new Date().toISOString()
        });
        
        if (originalUnhandledRejection) {
          return originalUnhandledRejection(event);
        }
      };
    }
  }

  /**
   * Setup navigation tracking
   */
  private setupNavigationTracking() {
    if (typeof window !== 'undefined') {
      // Track page navigation
      const originalPushState = history.pushState;
      history.pushState = function(...args) {
        auditService.log({
          event_type: 'DATA_ACCESSED',
          severity: 'INFO',
          action: 'Page navigation',
          description: `Navigated to ${args[2]}`,
          metadata: {
            from: window.location.pathname,
            to: args[2],
            method: 'pushState'
          }
        });
        return originalPushState.apply(history, args);
      };

      window.addEventListener('popstate', () => {
        auditService.log({
          event_type: 'DATA_ACCESSED',
          severity: 'INFO',
          action: 'Page navigation',
          description: `Navigated to ${window.location.pathname}`,
          metadata: {
            method: 'popstate'
          }
        });
      });
    }
  }

  /**
   * Setup API interceptors
   */
  private setupAPIInterceptors() {
    if (typeof window !== 'undefined') {
      // Intercept fetch API
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const startTime = Date.now();
        const [resource, config] = args;
        const url = typeof resource === 'string' ? resource : resource.url;
        
        try {
          const response = await originalFetch(...args);
          const duration = Date.now() - startTime;
          
          // Log API requests (excluding certain paths)
          if (!this.shouldSkipAPILogging(url)) {
            await this.logAPIRequest({
              method: config?.method || 'GET',
              path: url,
              statusCode: response.status,
              duration,
              timestamp: new Date().toISOString()
            });
          }
          
          return response;
        } catch (error) {
          const duration = Date.now() - startTime;
          
          await this.logAPIRequest({
            method: config?.method || 'GET',
            path: url,
            statusCode: 0,
            duration,
            metadata: { error: error.message },
            timestamp: new Date().toISOString()
          });
          
          throw error;
        }
      };
    }
  }

  /**
   * Check if API path should be skipped from logging
   */
  private shouldSkipAPILogging(path: string): boolean {
    const skipPaths = [
      '/api/audit-logs',
      '/supabase/functions/process-audit-logs',
      '/_next/',
      '/favicon'
    ];
    
    return skipPaths.some(skip => path.includes(skip));
  }

  /**
   * Log system events
   */
  public async logSystemEvent(event: SystemEvent) {
    const severityMap = {
      'startup': 'INFO',
      'shutdown': 'INFO',
      'error': 'ERROR',
      'config_change': 'WARNING',
      'integration': 'INFO'
    };

    await auditService.log({
      event_type: event.type === 'error' ? 'SYSTEM_ERROR' : 'SYSTEM_INFO',
      severity: severityMap[event.type] as any,
      action: `System ${event.type}`,
      description: event.message,
      metadata: event.metadata
    });
  }

  /**
   * Log API requests
   */
  private async logAPIRequest(event: APIRequestEvent) {
    // Only log non-GET requests or failed requests
    if (event.method !== 'GET' || event.statusCode >= 400) {
      await auditService.log({
        event_type: event.statusCode >= 400 ? 'SYSTEM_ERROR' : 'DATA_ACCESSED',
        severity: event.statusCode >= 500 ? 'ERROR' : 'INFO',
        action: `API ${event.method} ${event.path}`,
        description: `${event.method} request to ${event.path} returned ${event.statusCode}`,
        metadata: {
          method: event.method,
          path: event.path,
          statusCode: event.statusCode,
          duration: event.duration,
          requestBody: event.requestBody,
          responseBody: event.responseBody
        }
      });
    }
  }

  /**
   * Manual log collection for specific actions
   */
  public async collectUserAction(action: string, details?: any) {
    await auditService.log({
      event_type: 'DATA_ACCESSED',
      severity: 'INFO',
      action,
      metadata: details
    });
  }

  /**
   * Collect form submission
   */
  public async collectFormSubmission(formType: string, formData: any, success: boolean) {
    await auditService.log({
      event_type: 'REQUEST_CREATED',
      severity: success ? 'INFO' : 'ERROR',
      action: `Form submission: ${formType}`,
      description: success ? 'Form submitted successfully' : 'Form submission failed',
      entity_type: 'form_submission',
      metadata: {
        formType,
        fields: Object.keys(formData),
        success
      }
    });
  }

  /**
   * Collect authentication events
   */
  public async collectAuthEvent(event: 'login' | 'logout' | 'signup', success: boolean, details?: any) {
    const eventTypeMap = {
      'login': 'USER_LOGIN',
      'logout': 'USER_LOGOUT',
      'signup': 'USER_CREATED'
    };

    await auditService.log({
      event_type: eventTypeMap[event] as any,
      severity: success ? 'INFO' : 'WARNING',
      action: `User ${event}`,
      description: success ? `${event} successful` : `${event} failed`,
      metadata: details
    });
  }
}

// Export singleton instance
export const logCollector = LogCollectorService.getInstance();