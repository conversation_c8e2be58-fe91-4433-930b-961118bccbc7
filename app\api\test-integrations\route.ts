import { NextRequest, NextResponse } from 'next/server'
import { serverClient, serviceClient } from '@/lib/supabase/index'
import { config } from '@/lib/env'

export async function GET(request: NextRequest) {
  const results = {
    timestamp: new Date().toISOString(),
    supabase: {
      server: { status: 'unknown' as string, error: null as string | null },
      service: { status: 'unknown' as string, error: null as string | null }
    },
    ai: {
      openai: { status: 'unknown', configured: false },
      anthropic: { status: 'unknown', configured: false }
    },
    environment: {
      supabase_url: config.supabase.url ? 'configured' : 'missing',
      supabase_anon_key: config.supabase.anonKey ? 'configured' : 'missing',
      supabase_service_key: config.supabase.serviceRoleKey ? 'configured' : 'missing',
      openai_key: config.ai.openaiKey ? 'configured' : 'missing',
      anthropic_key: config.ai.anthropicKey ? 'configured' : 'missing'
    }
  }

  // Test Supabase Server Client
  try {
    const supabaseServer = serverClient()
    const { data, error } = await supabaseServer.from('profiles').select('count').limit(1)
    if (error) {
      results.supabase.server = { status: 'error', error: error.message }
    } else {
      results.supabase.server = { status: 'connected', error: null }
    }
  } catch (error) {
    results.supabase.server = { 
      status: 'error', 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }

  // Test Supabase Service Client
  try {
    const supabaseService = serviceClient()
    const { data, error } = await supabaseService.from('profiles').select('count').limit(1)
    if (error) {
      results.supabase.service = { status: 'error', error: error.message }
    } else {
      results.supabase.service = { status: 'connected', error: null }
    }
  } catch (error) {
    results.supabase.service = { 
      status: 'error', 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }

  // Test AI API configurations
  results.ai.openai.configured = !!config.ai.openaiKey
  results.ai.anthropic.configured = !!config.ai.anthropicKey

  if (config.ai.openaiKey) {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${config.ai.openaiKey}`
        }
      })
      results.ai.openai.status = response.ok ? 'connected' : 'error'
    } catch (error) {
      results.ai.openai.status = 'error'
    }
  }

  if (config.ai.anthropicKey) {
    // Note: Anthropic doesn't have a simple health check endpoint
    // We'll just verify the key format
    results.ai.anthropic.status = config.ai.anthropicKey.startsWith('sk-ant-') ? 'configured' : 'invalid_format'
  }

  return NextResponse.json(results)
}
