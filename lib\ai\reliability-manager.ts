/**
 * AI Service Reliability Manager
 * Handles fallbacks, circuit breakers, and graceful degradation
 */

import { getCacheService } from '@/lib/cache/cache-service'
import { metrics } from '@/lib/monitoring/metrics'
import { getUsageTracker } from './usage-tracker'

interface ServiceHealth {
  provider: string
  model: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  lastCheck: Date
  responseTime: number
  errorRate: number
  consecutiveFailures: number
  lastError?: string
}

interface CircuitBreakerState {
  provider: string
  model: string
  state: 'closed' | 'open' | 'half-open'
  failureCount: number
  lastFailureTime: Date
  nextRetryTime: Date
  successCount: number
}

interface FallbackStrategy {
  primary: { provider: string; model: string }
  fallbacks: Array<{ provider: string; model: string; priority: number }>
  degradedMode?: {
    enabled: boolean
    cacheOnly: boolean
    simplifiedResponse: boolean
  }
}

interface AIRequest {
  prompt: string
  options: any
  feature: string
  userId: string
  organizationId: string
  priority: 'low' | 'normal' | 'high' | 'critical'
}

interface AIResponse {
  content: string
  provider: string
  model: string
  fromCache: boolean
  fromFallback: boolean
  degraded: boolean
  metadata: any
}

class AIReliabilityManager {
  private cache = getCacheService()
  private usageTracker = getUsageTracker()
  private serviceHealth = new Map<string, ServiceHealth>()
  private circuitBreakers = new Map<string, CircuitBreakerState>()
  
  // Configuration
  private readonly failureThreshold = 5
  private readonly recoveryTimeout = 60000 // 1 minute
  private readonly healthCheckInterval = 30000 // 30 seconds
  private readonly maxResponseTime = 30000 // 30 seconds
  private readonly errorRateThreshold = 0.5 // 50%

  // Fallback strategies for different features
  private fallbackStrategies: { [feature: string]: FallbackStrategy } = {
    ticket_analysis: {
      primary: { provider: 'openai', model: 'gpt-4-turbo' },
      fallbacks: [
        { provider: 'anthropic', model: 'claude-3-sonnet', priority: 1 },
        { provider: 'openai', model: 'gpt-3.5-turbo', priority: 2 },
        { provider: 'anthropic', model: 'claude-3-haiku', priority: 3 }
      ],
      degradedMode: {
        enabled: true,
        cacheOnly: true,
        simplifiedResponse: false
      }
    },
    email_generation: {
      primary: { provider: 'anthropic', model: 'claude-3-sonnet' },
      fallbacks: [
        { provider: 'openai', model: 'gpt-3.5-turbo', priority: 1 },
        { provider: 'anthropic', model: 'claude-3-haiku', priority: 2 }
      ],
      degradedMode: {
        enabled: true,
        cacheOnly: false,
        simplifiedResponse: true
      }
    },
    chat_support: {
      primary: { provider: 'openai', model: 'gpt-3.5-turbo' },
      fallbacks: [
        { provider: 'anthropic', model: 'claude-3-haiku', priority: 1 }
      ],
      degradedMode: {
        enabled: true,
        cacheOnly: true,
        simplifiedResponse: true
      }
    },
    default: {
      primary: { provider: 'openai', model: 'gpt-3.5-turbo' },
      fallbacks: [
        { provider: 'anthropic', model: 'claude-3-haiku', priority: 1 }
      ],
      degradedMode: {
        enabled: true,
        cacheOnly: true,
        simplifiedResponse: true
      }
    }
  }

  constructor() {
    // Start health monitoring
    this.startHealthMonitoring()
  }

  /**
   * Make AI request with reliability features
   */
  async makeReliableRequest(request: AIRequest): Promise<AIResponse> {
    const strategy = this.fallbackStrategies[request.feature] || this.fallbackStrategies.default
    const startTime = Date.now()

    // Check if user can make request (quotas, budgets)
    const canMakeRequest = await this.usageTracker.canMakeRequest(
      request.userId,
      request.organizationId,
      0.01, // Estimated cost
      100   // Estimated tokens
    )

    if (!canMakeRequest.allowed) {
      return this.handleQuotaExceeded(request, canMakeRequest.reason || 'Quota exceeded')
    }

    // Try primary service first
    try {
      const primaryResponse = await this.tryService(
        strategy.primary.provider,
        strategy.primary.model,
        request
      )

      if (primaryResponse) {
        await this.recordSuccess(strategy.primary.provider, strategy.primary.model, Date.now() - startTime)
        return primaryResponse
      }
    } catch (error) {
      await this.recordFailure(strategy.primary.provider, strategy.primary.model, error)
      console.warn(`Primary service failed: ${strategy.primary.provider}:${strategy.primary.model}`, error)
    }

    // Try fallback services
    for (const fallback of strategy.fallbacks.sort((a, b) => a.priority - b.priority)) {
      if (this.isServiceAvailable(fallback.provider, fallback.model)) {
        try {
          const fallbackResponse = await this.tryService(
            fallback.provider,
            fallback.model,
            request
          )

          if (fallbackResponse) {
            await this.recordSuccess(fallback.provider, fallback.model, Date.now() - startTime)
            fallbackResponse.fromFallback = true
            return fallbackResponse
          }
        } catch (error) {
          await this.recordFailure(fallback.provider, fallback.model, error)
          console.warn(`Fallback service failed: ${fallback.provider}:${fallback.model}`, error)
        }
      }
    }

    // All services failed, try degraded mode
    if (strategy.degradedMode?.enabled) {
      return this.handleDegradedMode(request, strategy.degradedMode)
    }

    // Complete failure
    throw new Error('All AI services are unavailable')
  }

  /**
   * Check if a service is available (not circuit broken)
   */
  isServiceAvailable(provider: string, model: string): boolean {
    const key = `${provider}:${model}`
    const circuitBreaker = this.circuitBreakers.get(key)

    if (!circuitBreaker) {
      return true
    }

    switch (circuitBreaker.state) {
      case 'closed':
        return true
      case 'open':
        // Check if we should try half-open
        if (Date.now() >= circuitBreaker.nextRetryTime.getTime()) {
          circuitBreaker.state = 'half-open'
          circuitBreaker.successCount = 0
          return true
        }
        return false
      case 'half-open':
        return true
      default:
        return true
    }
  }

  /**
   * Get service health status
   */
  getServiceHealth(): ServiceHealth[] {
    return Array.from(this.serviceHealth.values())
  }

  /**
   * Get circuit breaker states
   */
  getCircuitBreakerStates(): CircuitBreakerState[] {
    return Array.from(this.circuitBreakers.values())
  }

  /**
   * Manually reset circuit breaker
   */
  resetCircuitBreaker(provider: string, model: string): void {
    const key = `${provider}:${model}`
    const circuitBreaker = this.circuitBreakers.get(key)

    if (circuitBreaker) {
      circuitBreaker.state = 'closed'
      circuitBreaker.failureCount = 0
      circuitBreaker.successCount = 0
      console.log(`Circuit breaker reset for ${key}`)
    }
  }

  /**
   * Update fallback strategy for a feature
   */
  updateFallbackStrategy(feature: string, strategy: FallbackStrategy): void {
    this.fallbackStrategies[feature] = strategy
    console.log(`Updated fallback strategy for feature: ${feature}`)
  }

  // Private methods
  private async tryService(
    provider: string,
    model: string,
    request: AIRequest
  ): Promise<AIResponse | null> {
    const key = `${provider}:${model}`

    // Check circuit breaker
    if (!this.isServiceAvailable(provider, model)) {
      throw new Error(`Service ${key} is circuit broken`)
    }

    // Try cache first for non-critical requests
    if (request.priority !== 'critical') {
      const cached = await this.getCachedResponse(request)
      if (cached) {
        return {
          ...cached,
          provider,
          model,
          fromCache: true,
          fromFallback: false,
          degraded: false
        }
      }
    }

    // Make actual AI request
    const response = await this.callAIService(provider, model, request)
    
    // Cache successful responses
    if (response && request.priority !== 'critical') {
      await this.cacheResponse(request, response)
    }

    return response
  }

  private async callAIService(
    provider: string,
    model: string,
    request: AIRequest
  ): Promise<AIResponse | null> {
    // This would integrate with actual AI service clients
    // For now, simulate the call
    
    const startTime = Date.now()
    
    try {
      // Simulate AI service call
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000))
      
      // Simulate occasional failures
      if (Math.random() < 0.05) { // 5% failure rate
        throw new Error('Simulated AI service error')
      }

      const duration = Date.now() - startTime
      
      return {
        content: `AI response from ${provider}:${model} for: ${request.prompt.substring(0, 50)}...`,
        provider,
        model,
        fromCache: false,
        fromFallback: false,
        degraded: false,
        metadata: {
          duration,
          tokens: Math.floor(Math.random() * 1000) + 100
        }
      }
    } catch (error) {
      const duration = Date.now() - startTime
      await this.recordFailure(provider, model, error)
      throw error
    }
  }

  private async getCachedResponse(request: AIRequest): Promise<Partial<AIResponse> | null> {
    const cacheKey = this.generateCacheKey(request)
    return await this.cache.get(cacheKey)
  }

  private async cacheResponse(request: AIRequest, response: AIResponse): Promise<void> {
    const cacheKey = this.generateCacheKey(request)
    await this.cache.set(cacheKey, {
      content: response.content,
      metadata: response.metadata
    }, {
      ttl: 3600, // 1 hour
      tags: ['ai_responses', request.feature]
    })
  }

  private generateCacheKey(request: AIRequest): string {
    const hash = this.hashString(request.prompt + JSON.stringify(request.options))
    return `ai_response:${request.feature}:${hash}`
  }

  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(36)
  }

  private async recordSuccess(provider: string, model: string, duration: number): Promise<void> {
    const key = `${provider}:${model}`
    
    // Update service health
    const health = this.serviceHealth.get(key) || this.createServiceHealth(provider, model)
    health.status = 'healthy'
    health.lastCheck = new Date()
    health.responseTime = duration
    health.consecutiveFailures = 0
    this.serviceHealth.set(key, health)

    // Update circuit breaker
    const circuitBreaker = this.circuitBreakers.get(key)
    if (circuitBreaker) {
      if (circuitBreaker.state === 'half-open') {
        circuitBreaker.successCount++
        if (circuitBreaker.successCount >= 3) {
          circuitBreaker.state = 'closed'
          circuitBreaker.failureCount = 0
          console.log(`Circuit breaker closed for ${key}`)
        }
      }
    }

    // Track metrics
    metrics.increment('ai.service_success', { provider, model })
    metrics.timing('ai.service_response_time', duration, { provider, model })
  }

  private async recordFailure(provider: string, model: string, error: any): Promise<void> {
    const key = `${provider}:${model}`
    
    // Update service health
    const health = this.serviceHealth.get(key) || this.createServiceHealth(provider, model)
    health.consecutiveFailures++
    health.lastError = error.message
    health.lastCheck = new Date()
    
    if (health.consecutiveFailures >= this.failureThreshold) {
      health.status = 'unhealthy'
    } else if (health.consecutiveFailures >= 2) {
      health.status = 'degraded'
    }
    
    this.serviceHealth.set(key, health)

    // Update circuit breaker
    let circuitBreaker = this.circuitBreakers.get(key)
    if (!circuitBreaker) {
      circuitBreaker = this.createCircuitBreaker(provider, model)
      this.circuitBreakers.set(key, circuitBreaker)
    }

    circuitBreaker.failureCount++
    circuitBreaker.lastFailureTime = new Date()

    if (circuitBreaker.failureCount >= this.failureThreshold) {
      circuitBreaker.state = 'open'
      circuitBreaker.nextRetryTime = new Date(Date.now() + this.recoveryTimeout)
      console.warn(`Circuit breaker opened for ${key}`)
    }

    // Track metrics
    metrics.increment('ai.service_failure', { provider, model })
    metrics.increment('ai.service_errors', { provider, model, error_type: error.name })
  }

  private createServiceHealth(provider: string, model: string): ServiceHealth {
    return {
      provider,
      model,
      status: 'healthy',
      lastCheck: new Date(),
      responseTime: 0,
      errorRate: 0,
      consecutiveFailures: 0
    }
  }

  private createCircuitBreaker(provider: string, model: string): CircuitBreakerState {
    return {
      provider,
      model,
      state: 'closed',
      failureCount: 0,
      lastFailureTime: new Date(),
      nextRetryTime: new Date(),
      successCount: 0
    }
  }

  private async handleQuotaExceeded(request: AIRequest, reason: string): Promise<AIResponse> {
    // Try to serve from cache
    const cached = await this.getCachedResponse(request)
    if (cached) {
      return {
        content: cached.content || 'Cached response due to quota limits',
        provider: 'cache',
        model: 'cached',
        fromCache: true,
        fromFallback: false,
        degraded: true,
        metadata: { reason: 'quota_exceeded', ...cached.metadata }
      }
    }

    // Return degraded response
    return {
      content: 'Service temporarily unavailable due to usage limits. Please try again later.',
      provider: 'system',
      model: 'degraded',
      fromCache: false,
      fromFallback: false,
      degraded: true,
      metadata: { reason: 'quota_exceeded' }
    }
  }

  private async handleDegradedMode(
    request: AIRequest,
    degradedConfig: { cacheOnly: boolean; simplifiedResponse: boolean }
  ): Promise<AIResponse> {
    // Try cache first
    if (degradedConfig.cacheOnly) {
      const cached = await this.getCachedResponse(request)
      if (cached) {
        return {
          content: cached.content || 'Cached response (degraded mode)',
          provider: 'cache',
          model: 'cached',
          fromCache: true,
          fromFallback: false,
          degraded: true,
          metadata: cached.metadata
        }
      }
    }

    // Return simplified response
    if (degradedConfig.simplifiedResponse) {
      return {
        content: this.generateSimplifiedResponse(request),
        provider: 'system',
        model: 'simplified',
        fromCache: false,
        fromFallback: false,
        degraded: true,
        metadata: { mode: 'simplified' }
      }
    }

    throw new Error('No degraded mode available')
  }

  private generateSimplifiedResponse(request: AIRequest): string {
    // Generate basic responses based on feature type
    switch (request.feature) {
      case 'ticket_analysis':
        return 'This ticket requires manual review. AI analysis is temporarily unavailable.'
      case 'email_generation':
        return 'Thank you for your inquiry. We will respond to your request shortly.'
      case 'chat_support':
        return 'I apologize, but I\'m experiencing technical difficulties. Please contact support directly.'
      default:
        return 'AI service is temporarily unavailable. Please try again later.'
    }
  }

  private startHealthMonitoring(): void {
    setInterval(async () => {
      await this.performHealthChecks()
    }, this.healthCheckInterval)
  }

  private async performHealthChecks(): Promise<void> {
    // Perform lightweight health checks on all services
    for (const [key, health] of this.serviceHealth) {
      try {
        // Simple ping to check service availability
        const startTime = Date.now()
        await this.pingService(health.provider, health.model)
        const duration = Date.now() - startTime

        health.responseTime = duration
        health.lastCheck = new Date()
        
        if (health.status === 'unhealthy' && health.consecutiveFailures > 0) {
          health.consecutiveFailures = Math.max(0, health.consecutiveFailures - 1)
          if (health.consecutiveFailures === 0) {
            health.status = 'healthy'
          }
        }
      } catch (error) {
        health.consecutiveFailures++
        health.lastError = error.message
        health.status = health.consecutiveFailures >= this.failureThreshold ? 'unhealthy' : 'degraded'
      }
    }
  }

  private async pingService(provider: string, model: string): Promise<void> {
    // Lightweight health check - would ping actual service endpoints
    // For now, simulate with random success/failure
    if (Math.random() < 0.95) { // 95% success rate for health checks
      return Promise.resolve()
    } else {
      throw new Error('Health check failed')
    }
  }
}

// Singleton instance
let reliabilityManager: AIReliabilityManager | null = null

export const getReliabilityManager = (): AIReliabilityManager => {
  if (!reliabilityManager) {
    reliabilityManager = new AIReliabilityManager()
  }
  return reliabilityManager
}

export { AIReliabilityManager }
export type { ServiceHealth, CircuitBreakerState, FallbackStrategy, AIRequest, AIResponse }
