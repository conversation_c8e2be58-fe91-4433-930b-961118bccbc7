// Historical Data Analysis Service
// Integrates with Supabase Edge Functions for pattern analysis and learning

import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export interface PatternAnalysis {
  commonPatterns: Array<{
    value: string
    frequency: number
    confidence: number
  }>
  aiSuggestion?: string
  reasoning?: string
}

export interface FeedbackData {
  userId: string
  fieldId: string
  originalValue: string
  suggestedValue: string
  wasAccepted: boolean
  finalValue?: string
  departmentId?: string
  confidence?: number
}

export class HistoricalDataService {
  private static instance: HistoricalDataService
  private cache: Map<string, { data: PatternAnalysis; timestamp: number }> = new Map()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  private constructor() {}

  public static getInstance(): HistoricalDataService {
    if (!HistoricalDataService.instance) {
      HistoricalDataService.instance = new HistoricalDataService()
    }
    return HistoricalDataService.instance
  }

  /**
   * Analyze form patterns for intelligent suggestions
   */
  async analyzeFormPatterns(
    departmentId: string,
    fieldId: string,
    currentValue?: string,
    formContext?: Record<string, any>
  ): Promise<PatternAnalysis | null> {
    try {
      // Check cache first
      const cacheKey = `${departmentId}:${fieldId}:${currentValue || ''}`
      const cached = this.cache.get(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data
      }

      // Call edge function
      const { data, error } = await supabase.functions.invoke('analyze-form-patterns', {
        body: {
          departmentId,
          fieldId,
          currentValue,
          formContext,
          limit: 20
        }
      })

      if (error) throw error

      // Cache the result
      if (data) {
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now()
        })
      }

      return data as PatternAnalysis
    } catch (error) {
      console.error('Pattern analysis error:', error)
      return null
    }
  }

  /**
   * Submit feedback on AI suggestions
   */
  async submitFeedback(feedback: FeedbackData): Promise<boolean> {
    try {
      const { data, error } = await supabase.functions.invoke('learn-from-feedback', {
        body: feedback
      })

      if (error) throw error

      return data?.success || false
    } catch (error) {
      console.error('Feedback submission error:', error)
      return false
    }
  }

  /**
   * Get field suggestions based on historical data
   */
  async getFieldSuggestions(
    departmentId: string,
    fieldId: string,
    limit: number = 5
  ): Promise<string[]> {
    try {
      const analysis = await this.analyzeFormPatterns(departmentId, fieldId)
      
      if (!analysis?.commonPatterns) return []

      return analysis.commonPatterns
        .slice(0, limit)
        .map(pattern => pattern.value)
    } catch (error) {
      console.error('Get suggestions error:', error)
      return []
    }
  }

  /**
   * Record form submission for pattern learning
   */
  async recordFormSubmission(
    userId: string,
    departmentId: string,
    serviceCategoryId: string,
    formData: Record<string, any>
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .rpc('record_form_submission', {
          p_user_id: userId,
          p_department_id: departmentId,
          p_service_category_id: serviceCategoryId,
          p_form_data: formData
        })

      if (error) throw error

      return data
    } catch (error) {
      console.error('Record submission error:', error)
      return null
    }
  }

  /**
   * Get department-specific field statistics
   */
  async getFieldStatistics(
    departmentId: string,
    fieldId: string
  ): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('field_usage_statistics')
        .select('*')
        .eq('department_id', departmentId)
        .eq('field_id', fieldId)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      return data
    } catch (error) {
      console.error('Get field statistics error:', error)
      return null
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const historicalDataService = HistoricalDataService.getInstance()
