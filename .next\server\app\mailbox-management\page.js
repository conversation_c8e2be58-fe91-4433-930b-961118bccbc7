(()=>{var e={};e.id=7063,e.ids=[7063],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},11810:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>u,tree:()=>p});var s=t(70260),n=t(28203),a=t(25155),o=t.n(a),i=t(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p=["",{children:["mailbox-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,56515,23)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mailbox-management\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],l=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mailbox-management\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/mailbox-management/page",pathname:"/mailbox-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},56515:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected '}', got '<eof>'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mailbox-management\\page.tsx\x1b[0m:61:1]\n \x1b[2m58\x1b[0m │   const [searchTerm, setSearchTerm] = useState('');\n \x1b[2m59\x1b[0m │   const [isLoading, setIsLoading] = useState(false);\n \x1b[2m60\x1b[0m │   const [selectedUsers, setSelectedUsers] = useState<any[]>([]);\n \x1b[2m61\x1b[0m │   const supabase = createClient();\n    \xb7 \x1b[35;1m                                 ─\x1b[0m\n    ╰────\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(11810));module.exports=s})();