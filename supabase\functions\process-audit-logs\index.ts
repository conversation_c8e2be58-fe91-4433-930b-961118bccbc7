import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface AuditLogEntry {
  event_type: string;
  severity: string;
  action: string;
  description?: string;
  occurred_at?: string;
  user_id?: string;
  staff_id?: string;
  role_name?: string;
  department_id?: string;
  department_name?: string;
  entity_type?: string;
  entity_id?: string;
  entity_name?: string;
  old_value?: any;
  new_value?: any;
  changes_summary?: any;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  request_id?: string;
  trace_id?: string;
  metadata?: Record<string, any>;
  tags?: string[];
  retention_until?: string;
  compliance_flags?: string[];
  is_sensitive?: boolean;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { logs } = await req.json()
    
    if (!logs || !Array.isArray(logs)) {
      throw new Error('Invalid logs data')
    }

    // Get client IP address
    const clientIp = req.headers.get('x-forwarded-for') || 
                    req.headers.get('x-real-ip') || 
                    'unknown'

    // Process each log entry
    const processedLogs = await Promise.all(logs.map(async (log: AuditLogEntry) => {
      // Add server-side metadata
      const processedLog = {
        ...log,
        ip_address: clientIp,
        created_at: new Date().toISOString(),
      }

      // Calculate checksum
      const checksum = await calculateChecksum(processedLog)
      processedLog.checksum = checksum

      // Apply retention policy
      if (!processedLog.retention_until) {
        const retentionDays = getRetentionDays(processedLog.event_type)
        const retentionDate = new Date()
        retentionDate.setDate(retentionDate.getDate() + retentionDays)
        processedLog.retention_until = retentionDate.toISOString()
      }

      return processedLog
    }))

    // Batch insert logs
    const { error: insertError } = await supabaseClient
      .from('audit_logs')
      .insert(processedLogs)

    if (insertError) {
      throw insertError
    }

    // Check for critical events that need immediate attention
    const criticalEvents = processedLogs.filter(
      log => log.severity === 'CRITICAL' || log.severity === 'ERROR'
    )

    if (criticalEvents.length > 0) {
      // Trigger alerts for critical events
      await triggerAlerts(supabaseClient, criticalEvents)
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        processed: processedLogs.length 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error processing audit logs:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function calculateChecksum(data: any): Promise<string> {
  const encoder = new TextEncoder()
  const dataString = JSON.stringify(data)
  const dataBuffer = encoder.encode(dataString)
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

function getRetentionDays(eventType: string): number {
  // Default retention periods by event type
  const retentionMap: Record<string, number> = {
    // Security events - 2 years
    'USER_CREATED': 730,
    'USER_DELETED': 730,
    'ROLE_ASSIGNED': 730,
    'ROLE_REMOVED': 730,
    'PERMISSION_GRANTED': 730,
    'PERMISSION_REVOKED': 730,
    
    // Critical events - 2 years
    'SYSTEM_ERROR': 730,
    'SYSTEM_CRITICAL': 730,
    'DATA_EXPORTED': 730,
    'CONFIGURATION_CHANGED': 730,
    
    // Request events - 6 months
    'REQUEST_CREATED': 180,
    'REQUEST_UPDATED': 180,
    'REQUEST_APPROVED': 180,
    'REQUEST_COMPLETED': 180,
    
    // General events - 3 months
    'USER_LOGIN': 90,
    'USER_LOGOUT': 90,
    'SYSTEM_INFO': 90,
  }
  
  return retentionMap[eventType] || 90 // Default 90 days
}

async function triggerAlerts(supabaseClient: any, criticalEvents: AuditLogEntry[]) {
  // Create notifications for critical events
  const notifications = criticalEvents.map(event => ({
    type: 'CRITICAL_AUDIT_EVENT',
    title: `Critical Event: ${event.event_type}`,
    message: event.description || event.action,
    severity: event.severity,
    metadata: {
      event_id: event.request_id,
      event_type: event.event_type,
      user_id: event.user_id,
      department_id: event.department_id
    }
  }))

  // Insert notifications
  await supabaseClient
    .from('notifications')
    .insert(notifications)
    .select()

  // TODO: Send email alerts to administrators
  // TODO: Send Slack/Teams notifications if configured
}
