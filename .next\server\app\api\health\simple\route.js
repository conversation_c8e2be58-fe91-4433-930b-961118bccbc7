"use strict";(()=>{var e={};e.id=6285,e.ids=[6285],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},9598:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>i,HEAD:()=>u});var a=r(42706),n=r(28203),o=r(45994),p=r(39187);async function i(){return p.NextResponse.json({status:"ok",timestamp:new Date().toISOString()},{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}async function u(){return new p.NextResponse(null,{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/simple/route",pathname:"/api/health/simple",filename:"route",bundlePath:"app/api/health/simple/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\simple\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:h}=d;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>r(9598));module.exports=s})();