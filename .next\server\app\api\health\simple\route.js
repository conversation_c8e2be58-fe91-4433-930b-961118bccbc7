(()=>{var e={};e.id=6285,e.ids=[6285],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},54225:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p,HEAD:()=>u});var a=r(42706),n=r(28203),o=r(45994),i=r(39187);async function p(){return i.NextResponse.json({status:"ok",timestamp:new Date().toISOString()},{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}async function u(){return new i.NextResponse(null,{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/simple/route",pathname:"/api/health/simple",filename:"route",bundlePath:"app/api/health/simple/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\simple\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},96487:()=>{},78335:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,5452],()=>r(54225));module.exports=s})();