import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin/helpdesk role
    const { data: staff } = await supabase
      .from('staff')
      .select('roles!inner(name)')
      .eq('auth_id', user.id)
      .single();

    const allowedRoles = ['Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support'];
    if (!staff || !allowedRoles.includes(staff.roles.name)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { articleIds, categories, fromSearchPatterns, timeRange, options } = body;

    // Call the edge function
    const { data, error } = await supabase.functions.invoke('generate-faq', {
      body: {
        articleIds,
        categories,
        fromSearchPatterns,
        timeRange,
        options
      }
    });

    if (error) {
      console.error('Edge function error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Log the generation
    await supabase.from('faq_generation_logs').insert({
      generation_type: fromSearchPatterns ? 'search_patterns' : 'kb_articles',
      parameters: body,
      total_generated: data.count || 0,
      total_saved: data.saved || 0,
      user_id: user.id
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error('FAQ generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate FAQs' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const isActive = searchParams.get('active');

    let query = supabase
      .from('chatbot_faq')
      .select('*')
      .order('created_at', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true');
    }

    const { data: faqs, error } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ faqs });
  } catch (error) {
    console.error('FAQ fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch FAQs' },
      { status: 500 }
    );
  }
}