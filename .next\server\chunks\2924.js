"use strict";exports.id=2924,exports.ids=[2924],exports.modules={14724:(e,t,a)=>{a.d(t,{a:()=>i,g:()=>o});var s=a(61487);!function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();class i{async createApprovalChain(e,t,a){let i=(0,s.U)();try{let{data:s,error:o}=await i.from("approval_chains").insert({workflow_instance_id:e,chain_config:t,current_level:0,status:"pending",context_data:a}).select().single();if(o||!s)throw Error("Failed to create approval chain");await this.processApprovalLevel(s.id,0,a),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"APPROVAL_CHAIN_CREATED",details:{chain_id:s.id,workflow_instance_id:e,levels:t.levels.length}})}catch(e){throw console.error("Error creating approval chain:",e),e}}async processApprovalLevel(e,t,a){let i=(0,s.U)(),{data:o,error:r}=await i.from("approval_chains").select("*").eq("id",e).single();if(r||!o)throw Error("Approval chain not found");let n=o.chain_config.levels[t];if(!n){await this.completeApprovalChain(e,"approved");return}if(n.skipCondition&&this.evaluateCondition(n.skipCondition,a)){await this.processApprovalLevel(e,t+1,a);return}let l=await this.resolveApprovers(n.approvers,a),c=[];for(let s of l){let i=await this.createApprovalTask({chainId:e,level:t,approver:s,dueMinutes:n.escalationMinutes,requireAll:n.requireAll,context:a});c.push(i)}await i.from("approval_chains").update({current_level:t,current_level_tasks:c.map(e=>e.id)}).eq("id",e)}async createApprovalTask(e){let t=(0,s.U)(),{chainId:a,level:i,approver:o,dueMinutes:r,requireAll:n,context:l}=e,c=r?new Date(Date.now()+6e4*r).toISOString():void 0,{data:d,error:w}=await t.from("workflow_tasks").insert({workflow_instance_id:l.workflow_instance_id,task_type:"multi_level_approval",task_name:`Approval Required - Level ${i+1}`,assigned_to:o.userId,assigned_role:o.role,status:"pending",due_date:c,task_data:{approval_chain_id:a,approval_level:i,require_all:n,context:l}}).select().single();if(w||!d)throw Error("Failed to create approval task");return await this.sendApprovalNotification(d,i),d}async processApprovalDecision(e,t){let a=(0,s.U)();try{let{data:s,error:i}=await a.from("workflow_tasks").select("*").eq("id",e.taskId).single();if(i||!s)throw Error("Approval task not found");await a.from("workflow_tasks").update({status:"completed",completed_at:new Date().toISOString(),completed_by:t,task_data:{...s.task_data,decision:e.decision,comments:e.comments,attachments:e.attachments}}).eq("id",e.taskId);let o=s.task_data.approval_chain_id,{data:r}=await a.from("approval_chains").select("*").eq("id",o).single();if(!r)throw Error("Approval chain not found");switch(e.decision){case"approve":await this.handleApproval(r,s,t);break;case"reject":await this.handleRejection(r,s,t);break;case"escalate":await this.handleEscalation(r,s,t)}await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"APPROVAL_DECISION",details:{task_id:e.taskId,decision:e.decision,level:s.task_data.approval_level,comments:e.comments},user_id:t})}catch(e){throw console.error("Error processing approval decision:",e),e}}async handleApproval(e,t,a){let i=(0,s.U)(),o=e.chain_config,r=e.current_level;if(o.levels[r].requireAll){let{data:a}=await i.from("workflow_tasks").select("*").eq("task_data->approval_chain_id",e.id).eq("task_data->approval_level",r);if(!a?.every(e=>e.id===t.id||"approve"===e.task_data.decision))return}r<o.levels.length-1?await this.processApprovalLevel(e.id,r+1,e.context_data):await this.completeApprovalChain(e.id,"approved")}async handleRejection(e,t,a){await this.completeApprovalChain(e.id,"rejected")}async handleEscalation(e,t,a){let i=(0,s.U)(),o=e.chain_config,r=e.current_level;r<o.levels.length-1?(await i.from("workflow_tasks").update({status:"cancelled"}).eq("task_data->approval_chain_id",e.id).eq("task_data->approval_level",r).eq("status","pending"),await this.processApprovalLevel(e.id,r+1,e.context_data)):await this.completeApprovalChain(e.id,"escalated")}async completeApprovalChain(e,t){let a=(0,s.U)(),{data:i,error:o}=await a.from("approval_chains").update({status:"completed",outcome:t,completed_at:new Date().toISOString()}).eq("id",e).select().single();if(o||!i)throw Error("Failed to complete approval chain");await a.from("workflow_instances").update({context_data:{...i.context_data,approval_outcome:t,approval_chain_id:e}}).eq("id",i.workflow_instance_id),await this.triggerWorkflowContinuation(i.workflow_instance_id,t)}async resolveApprovers(e,t){let a=[],i=(0,s.U)();for(let s of e)if(s.userId)a.push({userId:s.userId});else if(s.role){let e=s.department||t.department,{data:o}=await i.from("staff").select("id").eq("role_id",await this.getRoleId(s.role)).eq("division_id",e);o&&o.length>0?a.push({userId:o[0].id}):a.push({role:`${s.role}:${e}`})}else if(s.expression){let e=this.evaluateExpression(s.expression,t);e&&a.push({userId:e})}return a}async getRoleId(e){let t=(0,s.U)(),{data:a}=await t.from("roles").select("id").eq("name",e).single();return a?.id||null}evaluateCondition(e,t){try{return Function("context",`return ${e}`)(t)}catch(e){return console.error("Error evaluating condition:",e),!1}}evaluateExpression(e,t){try{return Function("context",`return ${e}`)(t)}catch(e){return console.error("Error evaluating expression:",e),null}}async sendApprovalNotification(e,t){console.log(`Sending approval notification for task ${e.id} at level ${t}`)}async triggerWorkflowContinuation(e,t){console.log(`Triggering workflow continuation for ${e} with outcome ${t}`)}async handleApprovalTimeout(e){let t=(0,s.U)(),{data:a}=await t.from("approval_chains").select("*").eq("id",e).single();if(!a)return;let i=a.chain_config;switch(i.timeoutAction){case"escalate":let o=a.current_level;o<i.levels.length-1?await this.processApprovalLevel(e,o+1,a.context_data):await this.completeApprovalChain(e,"timeout");break;case"auto_approve":await this.completeApprovalChain(e,"approved");break;case"auto_reject":await this.completeApprovalChain(e,"rejected")}}async getApprovalChainStatus(e){let t=(0,s.U)(),{data:a,error:i}=await t.from("approval_chains").select(`
        *,
        workflow_tasks (
          id,
          task_name,
          assigned_to,
          assigned_role,
          status,
          task_data,
          completed_at,
          completed_by
        )
      `).eq("id",e).single();if(i||!a)throw Error("Approval chain not found");let o=a.chain_config,r=o.levels.map((e,t)=>{let s=a.workflow_tasks.filter(e=>e.task_data.approval_level===t);return{level:t+1,name:e.name,status:this.getLevelStatus(s),approvers:s.map(e=>({taskId:e.id,assignedTo:e.assigned_to,assignedRole:e.assigned_role,status:e.status,decision:e.task_data.decision,completedAt:e.completed_at,completedBy:e.completed_by}))}});return{id:a.id,status:a.status,outcome:a.outcome,currentLevel:a.current_level+1,totalLevels:o.levels.length,levels:r,startedAt:a.created_at,completedAt:a.completed_at}}getLevelStatus(e){return 0===e.length?"pending":e.some(e=>"reject"===e.task_data.decision)?"rejected":e.every(e=>"completed"===e.status)?"approved":e.some(e=>"in_progress"===e.status)?"in_progress":"pending"}}let o=new i},2924:(e,t,a)=>{a.d(t,{I:()=>w});var s=a(61487);class i{async evaluateTransitions(e,t){for(let a of e)if(!a.condition||await this.evaluateCondition(a.condition,t))return a.to;return null}async evaluateCondition(e,t){try{let a=this.createSafeContext(t);return!!Function(...Object.keys(a),`return ${e}`)(...Object.values(a))}catch(t){return console.error("Error evaluating condition:",e,t),!1}}createSafeContext(e){let t={},a=(e,s="")=>{for(let i in e){let o=s?`${s}_${i}`:i;"object"!=typeof e[i]||null===e[i]||Array.isArray(e[i])?t[o]=e[i]:a(e[i],o)}};return a(e),t}async evaluateBusinessRules(e,t){let a=(0,s.U)(),{data:i,error:o}=await a.from("business_rules").select("*").eq("rule_type",e).eq("is_active",!0).order("priority",{ascending:!1});if(o||!i)return[];let r=[];for(let e of i)await this.evaluateRuleConditions(e.conditions,t)&&r.push({rule:e,actions:e.actions});return r}async evaluateRuleConditions(e,t){let a=!0,s="AND";for(let i of e){let e=this.evaluateSingleCondition(i,t);a="AND"===s?a&&e:a||e,i.logic&&(s=i.logic)}return a}evaluateSingleCondition(e,t){let a=this.getFieldValue(e.field,t);switch(e.operator){case"equals":return a===e.value;case"not_equals":return a!==e.value;case"contains":return String(a).includes(String(e.value));case"greater_than":return Number(a)>Number(e.value);case"less_than":return Number(a)<Number(e.value);case"in":return Array.isArray(e.value)&&e.value.includes(a);case"not_in":return Array.isArray(e.value)&&!e.value.includes(a);default:return!1}}getFieldValue(e,t){let a=e.split("."),s=t;for(let e of a){if(!s||"object"!=typeof s||!(e in s))return;s=s[e]}return s}async executeRuleActions(e,t){for(let a of e)switch(a.type){case"assign":await this.executeAssignAction(a.parameters,t);break;case"notify":await this.executeNotifyAction(a.parameters,t);break;case"update_priority":await this.executeUpdatePriorityAction(a.parameters,t);break;case"add_tag":await this.executeAddTagAction(a.parameters,t);break;default:console.warn(`Unknown action type: ${a.type}`)}}async executeAssignAction(e,t){console.log("Executing assign action:",e)}async executeNotifyAction(e,t){console.log("Executing notify action:",e)}async executeUpdatePriorityAction(e,t){console.log("Executing update priority action:",e)}async executeAddTagAction(e,t){console.log("Executing add tag action:",e)}}class o{isValidTransition(e,t,a){let s=a.states[e];return!!s&&!!s.transitions&&s.transitions.some(e=>e.to===t)}getAvailableTransitions(e,t){let a=t.states[e];return a&&a.transitions?a.transitions.map(e=>e.to):[]}isTerminalState(e,t){let a=t.states[e];return a&&"end"===a.type}getStateMetadata(e,t){return t.states[e]||null}validateWorkflowDefinition(e){let t=[];for(let[a,s]of(e.id||t.push("Workflow definition must have an id"),e.states||t.push("Workflow definition must have states"),e.states?.initial||t.push("Workflow must have an initial state"),Object.entries(e.states||{}))){if(s.type||t.push(`State '${a}' must have a type`),s.transitions)for(let i of s.transitions)i.to||t.push(`Transition in state '${a}' must have a 'to' field`),i.to&&!e.states[i.to]&&t.push(`State '${a}' has transition to non-existent state '${i.to}'`);switch(s.type){case"approval":s.assignee||s.assigned_role||t.push(`Approval state '${a}' must have an assignee configuration`);break;case"task":s.handler||s.assignee||s.assigned_role||t.push(`Task state '${a}' must have a handler or assignee`)}}return Object.values(e.states||{}).some(e=>"end"===e.type)||t.push("Workflow must have at least one end state"),{valid:0===t.length,errors:t}}}var r=a(92537),n=a(75971);class l{async scheduleEscalation(e,t){let a=(0,s.U)(),{data:i}=await a.from("workflow_tasks").select("*").eq("id",e).single();if(i&&"pending"===i.status)for(let a of t.levels){let s=(0,n.z)(new Date(i.created_at),a.minutes).getTime()-Date.now();if(s>0){let i=setTimeout(async()=>{await this.executeEscalation(e,a,t.levels.indexOf(a)+1)},s);this.escalationTimers.set(`${e}-${a.minutes}`,i)}}}async executeEscalation(e,t,a){let i=(0,s.U)(),{data:o}=await i.from("workflow_tasks").select("*").eq("id",e).single();if(!o||"pending"!==o.status){this.clearEscalationTimers(e);return}let r=await this.resolveEscalationAssignee(t.assignee,o);await i.from("workflow_tasks").update({assigned_to:r.userId,assigned_role:r.role,status:"escalated",updated_at:new Date().toISOString()}).eq("id",e),await i.from("escalation_log").insert({workflow_task_id:e,escalated_from:o.assigned_to,escalated_to:r.userId,escalation_reason:`Task not completed within ${t.minutes} minutes`,escalation_level:a}),await this.sendEscalationNotification(o,r,a)}async cancelEscalation(e){this.clearEscalationTimers(e)}clearEscalationTimers(e){for(let[t,a]of this.escalationTimers.entries())t.startsWith(e)&&(clearTimeout(a),this.escalationTimers.delete(t))}async resolveEscalationAssignee(e,t){let a=(0,s.U)();if(e.userId)return{userId:e.userId};if("manager"===e.role&&t.assigned_to){let{data:e}=await a.from("staff").select("*, departments!inner(*)").eq("id",t.assigned_to).single();if(e){let{data:t}=await a.from("staff").select("*").eq("department_id",e.department_id).eq("role","Department Administrator").single();if(t)return{userId:t.id}}}if(e.role)return{role:e.role};let{data:i}=await a.from("staff").select("*").eq("role","Web App System Administrator").limit(1).single();return{userId:i?.id}}async sendEscalationNotification(e,t,a){console.log(`Escalation notification: Task ${e.id} escalated to level ${a}`)}async getActiveEscalations(){let e=(0,s.U)(),{data:t}=await e.from("workflow_tasks").select(`
        *,
        escalation_log!inner(*),
        workflow_instances!inner(*)
      `).eq("status","escalated").order("escalation_log.created_at",{ascending:!1});return t||[]}async getEscalationMetrics(e,t){let a=(0,s.U)(),{data:i}=await a.from("escalation_log").select("*, workflow_tasks!inner(*)").gte("created_at",e.toISOString()).lte("created_at",t.toISOString());if(!i)return null;let o={total_escalations:i.length,by_level:{},by_reason:{},average_time_to_escalation:0,resolution_after_escalation:0};return i.forEach(e=>{o.by_level[e.escalation_level]||(o.by_level[e.escalation_level]=0),o.by_level[e.escalation_level]++;let t=e.escalation_reason||"Unknown";o.by_reason[t]||(o.by_reason[t]=0),o.by_reason[t]++}),o}constructor(){this.escalationTimers=new Map}}var c=a(14724);!function(){var e=Error("Cannot find module '@/lib/services/notification-service'");throw e.code="MODULE_NOT_FOUND",e}();class d{constructor(e){this.supabase=e,this.notificationService=Object(function(){var e=Error("Cannot find module '@/lib/services/notification-service'");throw e.code="MODULE_NOT_FOUND",e}())(e)}async sendWorkflowNotification(e,t,a,s){try{let{data:i}=await this.supabase.from("workflow_instances").select(`
          *,
          workflow_definitions (
            id,
            name,
            name_jp
          ),
          request_forms (
            id,
            title,
            service_categories (
              name_en,
              name_jp
            )
          )
        `).eq("id",e).single();if(!i)return;let o=i.workflow_definitions?.name||i.request_forms?.service_categories?.name_en||"Workflow",r=i.workflow_definitions?.name_jp||i.request_forms?.service_categories?.name_jp||"ワークフロー",{data:n}=await this.supabase.from("notification_preferences").select("*").in("user_id",a);for(let i of a){let a=n?.find(e=>e.user_id===i);if(!a?.workflow_notifications||this.isInQuietHours(a))continue;let l=this.getNotificationTemplate(t,o,r,s);a.email_enabled&&await this.notificationService.sendEmail({to:await this.getUserEmail(i),subject:l.subject,template:"workflow-notification",data:{...l,workflowId:e,workflowName:o,event:t,...s}}),a.sms_enabled&&await this.notificationService.sendSMS({to:await this.getUserPhone(i),message:l.smsMessage}),a.in_app_enabled&&await this.notificationService.createInAppNotification({userId:i,type:"workflow",title:l.title,message:l.message,data:{workflowId:e,event:t,...s}})}}catch(e){console.error("Failed to send workflow notification:",e)}}async handleStatusChange(e,t,a){let{data:s}=await this.supabase.from("workflow_instances").select(`
        *,
        workflow_tasks (
          assigned_to
        ),
        request_forms (
          requester_id
        )
      `).eq("id",e).single();if(!s)return;let i=new Set;s.request_forms?.requester_id&&i.add(s.request_forms.requester_id),s.workflow_tasks?.forEach(e=>{e.assigned_to&&i.add(e.assigned_to)});let o="workflow_updated";"completed"===a?o="workflow_completed":"failed"===a?o="workflow_failed":"pending_approval"===a&&(o="approval_required"),await this.sendWorkflowNotification(e,o,Array.from(i),{oldStatus:t,newStatus:a})}async handleApprovalRequest(e,t,a){await this.sendWorkflowNotification(e,"approval_required",a,{approvalChainId:t})}async handleSLAEvent(e,t,a){await this.sendWorkflowNotification(e,"breached"===t?"sla_breached":"sla_warning",a,{slaStatus:t})}async handleEscalation(e,t,a){await this.sendWorkflowNotification(e,"workflow_escalated",a,{escalationRule:t.name,reason:t.condition_type})}getNotificationTemplate(e,t,a,s){let i={workflow_created:{subject:`New Workflow Created: ${t}`,title:"New Workflow Created",message:`A new ${t} workflow has been created and assigned to you.`,smsMessage:`New workflow: ${t}. Check your dashboard for details.`},workflow_updated:{subject:`Workflow Updated: ${t}`,title:"Workflow Updated",message:`The ${t} workflow has been updated. Status: ${s.newStatus}`,smsMessage:`Workflow ${t} updated to ${s.newStatus}.`},workflow_completed:{subject:`Workflow Completed: ${t}`,title:"Workflow Completed",message:`The ${t} workflow has been completed successfully.`,smsMessage:`Workflow ${t} completed successfully.`},workflow_failed:{subject:`Workflow Failed: ${t}`,title:"Workflow Failed",message:`The ${t} workflow has failed. Please check for errors.`,smsMessage:`Workflow ${t} failed. Check dashboard.`},approval_required:{subject:`Approval Required: ${t}`,title:"Approval Required",message:`Your approval is required for the ${t} workflow.`,smsMessage:`Approval needed for ${t}.`},approval_completed:{subject:`Approval Completed: ${t}`,title:"Approval Completed",message:`The ${t} workflow has been approved.`,smsMessage:`${t} approved.`},sla_warning:{subject:`SLA Warning: ${t}`,title:"SLA At Risk",message:`The ${t} workflow is at risk of breaching SLA.`,smsMessage:`SLA warning for ${t}.`},sla_breached:{subject:`SLA Breached: ${t}`,title:"SLA Breached",message:`The ${t} workflow has breached its SLA.`,smsMessage:`SLA breached for ${t}.`},workflow_escalated:{subject:`Workflow Escalated: ${t}`,title:"Workflow Escalated",message:`The ${t} workflow has been escalated due to ${s.reason}.`,smsMessage:`${t} escalated: ${s.reason}.`}};return i[e]||i.workflow_updated}isInQuietHours(e){if(!e?.quiet_hours_enabled)return!1;let t=new Date,a=60*t.getHours()+t.getMinutes(),[s,i]=(e.quiet_hours_start||"22:00").split(":").map(Number),[o,r]=(e.quiet_hours_end||"08:00").split(":").map(Number),n=60*s+i,l=60*o+r;return n>l?a>=n||a<l:a>=n&&a<l}async getUserEmail(e){let{data:t}=await this.supabase.from("staff").select("email").eq("id",e).single();return t?.email||""}async getUserPhone(e){let{data:t}=await this.supabase.from("staff").select("phone").eq("id",e).single();return t?.phone||""}subscribeToWorkflowEvents(){this.supabase.channel("workflow-status-changes").on("postgres_changes",{event:"UPDATE",schema:"public",table:"workflow_instances",filter:"status=neq.prev.status"},async e=>{await this.handleStatusChange(e.new.id,e.old.status,e.new.status)}).subscribe(),this.supabase.channel("workflow-approvals").on("postgres_changes",{event:"INSERT",schema:"public",table:"approval_chains"},async e=>{let t=e.new.approvers||[];await this.handleApprovalRequest(e.new.workflow_instance_id,e.new.id,t)}).subscribe(),this.supabase.channel("sla-status-changes").on("postgres_changes",{event:"UPDATE",schema:"public",table:"sla_tracking",filter:"sla_status=in.(at_risk,breached)"},async e=>{if(e.old.sla_status!==e.new.sla_status){let{data:t}=await this.supabase.from("workflow_instances").select("workflow_tasks(assigned_to)").eq("id",e.new.workflow_instance_id).single(),a=t?.workflow_tasks?.map(e=>e.assigned_to).filter(Boolean)||[];await this.handleSLAEvent(e.new.workflow_instance_id,e.new.sla_status,a)}}).subscribe(),this.supabase.channel("workflow-escalations").on("postgres_changes",{event:"INSERT",schema:"public",table:"escalation_instances"},async e=>{let t=e.new.actions?.filter(e=>"reassign"===e.type).map(e=>e.target)||[];await this.handleEscalation(e.new.workflow_instance_id,e.new.rule,t)}).subscribe()}}!function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();class w{constructor(){let e=(0,s.U)();this.ruleEngine=new i,this.stateMachine=new o,this.slaManager=new r.y,this.escalationEngine=new l,this.approvalEngine=new c.a,this.notificationIntegration=new d(e),this.notificationIntegration.subscribeToWorkflowEvents()}async startWorkflow(e){let{workflowDefinitionId:t,requestId:a,contextData:i,userId:o}=e,r=(0,s.U)();try{let{data:e,error:s}=await r.from("workflow_definitions").select("*").eq("id",t).eq("is_active",!0).single();if(s||!e)throw Error("Workflow definition not found");let{data:n,error:l}=await r.from("workflow_instances").insert({workflow_definition_id:t,request_id:a,current_state:"initial",status:"active",context_data:{...i,request_id:a},created_by:o}).select().single();if(l||!n)throw Error("Failed to create workflow instance");return await this.processWorkflowState(n,e.workflow_json),await this.slaManager.initializeSLA(n.id,e.workflow_json),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"WORKFLOW_STARTED",details:{workflow_id:n.id,definition_id:t,request_id:a},user_id:o}),n}catch(e){throw console.error("Error starting workflow:",e),e}}async processWorkflowState(e,t){let a=t.states[e.current_state];if(!a)throw Error(`Invalid state: ${e.current_state}`);switch(a.type){case"start":await this.handleStartState(e,a,t);break;case"approval":await this.handleApprovalState(e,a);break;case"task":await this.handleTaskState(e,a);break;case"automatic":await this.handleAutomaticState(e,a,t);break;case"end":await this.handleEndState(e)}}async handleStartState(e,t,a){let s=await this.ruleEngine.evaluateTransitions(t.transitions,e.context_data);s&&await this.transitionTo(e,s,a)}async handleApprovalState(e,t){let a=(0,s.U)();if(t.approvalChain)await this.approvalEngine.createApprovalChain(e.id,t.approvalChain,{...e.context_data,workflow_instance_id:e.id,state_name:e.current_state});else{let s=await this.resolveAssignee(t.assignee,e),{data:i,error:o}=await a.from("workflow_tasks").insert({workflow_instance_id:e.id,task_type:"approval",task_name:t.name||"Approval Required",assigned_to:s.userId,assigned_role:s.role,status:"pending",due_date:this.slaManager.calculateDueDate(t.sla_minutes),task_data:{state_name:e.current_state,approval_options:t.approval_options||["approve","reject"]}}).select().single();if(o||!i)throw Error("Failed to create approval task");t.escalation&&await this.escalationEngine.scheduleEscalation(i.id,t.escalation),await this.notificationIntegration.sendWorkflowNotification(e.id,"approval_required",[s.userId].filter(Boolean),{taskId:i.id,taskName:i.task_name,stateName:e.current_state})}}async handleTaskState(e,t){let a=(0,s.U)(),i=await this.resolveAssignee(t.assignee,e),{data:o,error:r}=await a.from("workflow_tasks").insert({workflow_instance_id:e.id,task_type:"task",task_name:t.name||"Task",assigned_to:i.userId,assigned_role:i.role,status:"pending",due_date:this.slaManager.calculateDueDate(t.sla_minutes),task_data:{handler:t.handler,parameters:t.parameters}}).select().single();if(r||!o)throw Error("Failed to create task");t.automatic&&t.handler&&await this.executeTaskHandler(o,t.handler)}async handleAutomaticState(e,t,a){t.actions&&await this.executeActions(t.actions,e);let s=await this.ruleEngine.evaluateTransitions(t.transitions,e.context_data);s&&await this.transitionTo(e,s,a)}async handleEndState(e){let t=(0,s.U)();await t.from("workflow_instances").update({status:"completed",completed_at:new Date().toISOString()}).eq("id",e.id),await this.slaManager.completeSLA(e.id),await this.sendCompletionNotification(e)}async transitionTo(e,t,a){let i=(0,s.U)();await i.from("workflow_transitions").insert({workflow_instance_id:e.id,from_state:e.current_state,to_state:t});let{data:o,error:r}=await i.from("workflow_instances").update({current_state:t}).eq("id",e.id).select().single();if(r||!o)throw Error("Failed to update workflow state");await this.processWorkflowState(o,a)}async completeTask(e,t,a){let i=(0,s.U)(),{data:o,error:r}=await i.from("workflow_tasks").select("*, workflow_instances!inner(*)").eq("id",e).single();if(r||!o)throw Error("Task not found");await i.from("workflow_tasks").update({status:"completed",completed_at:new Date().toISOString(),completed_by:t,task_data:{...o.task_data,result:a}}).eq("id",e);let n={...o.workflow_instances.context_data,[`task_${e}_result`]:a};await i.from("workflow_instances").update({context_data:n}).eq("id",o.workflow_instance_id);let{data:l}=await i.from("workflow_definitions").select("workflow_json").eq("id",o.workflow_instances.workflow_definition_id).single();if(l){let e=l.workflow_json.states[o.workflow_instances.current_state];if(e.transitions){let t=await this.ruleEngine.evaluateTransitions(e.transitions,n);t&&await this.transitionTo(o.workflow_instances,t,l.workflow_json)}}await this.slaManager.updateTaskCompletion(e)}async resolveAssignee(e,t){if(e.userId)return{userId:e.userId};if(e.role){let a=e.department||t.context_data.department;return{role:`${e.role}:${a}`}}return{}}async executeTaskHandler(e,t){console.log(`Executing handler: ${t} for task: ${e.id}`)}async executeActions(e,t){for(let a of e)console.log(`Executing action: ${a.type} for workflow: ${t.id}`)}async validateWorkflowDefinition(e){let t=e.workflow_json;if(!t.id||!t.name||!t.states)throw Error("Workflow definition missing required fields");if(!t.states.initial)throw Error("Workflow definition must have an initial state");if(0===Object.values(t.states).filter(e=>"end"===e.type).length)throw Error("Workflow definition must have at least one end state");for(let[e,a]of Object.entries(t.states))if(a.transitions){for(let s of a.transitions)if(!t.states[s.to])throw Error(`Invalid transition from ${e} to ${s.to}`)}}async cancelWorkflow(e,t){let a=await (0,s.U)(),{data:i,error:o}=await a.from("workflow_instances").select("*").eq("id",e).single();if(o||!i)throw Error("Workflow instance not found");if("active"!==i.status)throw Error("Can only cancel active workflows");let{error:r}=await a.from("workflow_instances").update({status:"cancelled",completed_at:new Date().toISOString()}).eq("id",e);if(r)throw Error("Failed to cancel workflow");await a.from("workflow_tasks").update({status:"cancelled"}).eq("workflow_instance_id",e).in("status",["pending","in_progress"]),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"WORKFLOW_CANCELLED",details:{workflow_id:e},user_id:t})}async sendTaskNotification(e,t){e.assigned_to&&await this.notificationIntegration.sendWorkflowNotification(e.workflow_instance_id,"workflow_created",[e.assigned_to],{taskId:e.id,taskName:e.task_name||"Task",taskType:t})}async sendCompletionNotification(e){let t=(0,s.U)(),{data:a}=await t.from("request_forms").select("requester_id").eq("id",e.context_data?.request_id).single(),i=[];a?.requester_id&&i.push(a.requester_id),await this.notificationIntegration.sendWorkflowNotification(e.id,"workflow_completed",i,{completedAt:new Date().toISOString()})}}new w}};