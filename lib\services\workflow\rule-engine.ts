import { createClient } from '@/lib/supabase/server';
import { BusinessRule, RuleCondition, StateTransition } from './types';

export class RuleEngine {
  /**
   * Evaluate transitions based on conditions
   */
  async evaluateTransitions(
    transitions: StateTransition[],
    context: Record<string, any>
  ): Promise<string | null> {
    for (const transition of transitions) {
      if (!transition.condition || await this.evaluateCondition(transition.condition, context)) {
        return transition.to;
      }
    }
    return null;
  }

  /**
   * Evaluate a single condition expression
   */
  private async evaluateCondition(
    condition: string,
    context: Record<string, any>
  ): Promise<boolean> {
    try {
      // Create a safe evaluation context
      const safeContext = this.createSafeContext(context);
      
      // Use Function constructor for safe evaluation
      const evaluator = new Function(...Object.keys(safeContext), `return ${condition}`);
      const result = evaluator(...Object.values(safeContext));
      
      return Boolean(result);
    } catch (error) {
      console.error('Error evaluating condition:', condition, error);
      return false;
    }
  }

  /**
   * Create a safe context for evaluation
   */
  private createSafeContext(context: Record<string, any>): Record<string, any> {
    // Flatten nested objects for easier access
    const flattened: Record<string, any> = {};
    
    const flatten = (obj: any, prefix = '') => {
      for (const key in obj) {
        const fullKey = prefix ? `${prefix}_${key}` : key;
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          flatten(obj[key], fullKey);
        } else {
          flattened[fullKey] = obj[key];
        }
      }
    };
    
    flatten(context);
    return flattened;
  }

  /**
   * Evaluate business rules
   */
  async evaluateBusinessRules(
    ruleType: string,
    context: Record<string, any>
  ): Promise<any[]> {
    const supabase = createClient();
    
    // Get active rules of the specified type
    const { data: rules, error } = await supabase
      .from('business_rules')
      .select('*')
      .eq('rule_type', ruleType)
      .eq('is_active', true)
      .order('priority', { ascending: false });

    if (error || !rules) {
      return [];
    }

    const results = [];
    
    for (const rule of rules) {
      if (await this.evaluateRuleConditions(rule.conditions, context)) {
        results.push({
          rule,
          actions: rule.actions,
        });
      }
    }

    return results;
  }

  /**
   * Evaluate rule conditions
   */
  private async evaluateRuleConditions(
    conditions: RuleCondition[],
    context: Record<string, any>
  ): Promise<boolean> {
    let result = true;
    let currentLogic: 'AND' | 'OR' = 'AND';

    for (const condition of conditions) {
      const conditionResult = this.evaluateSingleCondition(condition, context);

      if (currentLogic === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      if (condition.logic) {
        currentLogic = condition.logic;
      }
    }

    return result;
  }

  /**
   * Evaluate a single rule condition
   */
  private evaluateSingleCondition(
    condition: RuleCondition,
    context: Record<string, any>
  ): boolean {
    const fieldValue = this.getFieldValue(condition.field, context);

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      
      case 'not_equals':
        return fieldValue !== condition.value;
      
      case 'contains':
        return String(fieldValue).includes(String(condition.value));
      
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
      
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
      
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue);
      
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue);
      
      default:
        return false;
    }
  }

  /**
   * Get field value from context using dot notation
   */
  private getFieldValue(field: string, context: Record<string, any>): any {
    const parts = field.split('.');
    let value = context;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Execute rule actions
   */
  async executeRuleActions(
    actions: any[],
    context: Record<string, any>
  ): Promise<void> {
    for (const action of actions) {
      switch (action.type) {
        case 'assign':
          await this.executeAssignAction(action.parameters, context);
          break;
        
        case 'notify':
          await this.executeNotifyAction(action.parameters, context);
          break;
        
        case 'update_priority':
          await this.executeUpdatePriorityAction(action.parameters, context);
          break;
        
        case 'add_tag':
          await this.executeAddTagAction(action.parameters, context);
          break;
        
        default:
          console.warn(`Unknown action type: ${action.type}`);
      }
    }
  }

  private async executeAssignAction(params: any, context: any): Promise<void> {
    console.log('Executing assign action:', params);
  }

  private async executeNotifyAction(params: any, context: any): Promise<void> {
    console.log('Executing notify action:', params);
  }

  private async executeUpdatePriorityAction(params: any, context: any): Promise<void> {
    console.log('Executing update priority action:', params);
  }

  private async executeAddTagAction(params: any, context: any): Promise<void> {
    console.log('Executing add tag action:', params);
  }
}
