-- Create approval chains table for multi-level approval workflows
CREATE TABLE IF NOT EXISTS approval_chains (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_instance_id UUID REFERENCES workflow_instances(id) ON DELETE CASCADE,
  chain_config J<PERSON>NB NOT NULL, -- Stores the complete approval chain configuration
  current_level INTEGER DEFAULT 0,
  current_level_tasks UUID[] DEFAULT '{}',
  status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, active, completed, cancelled
  outcome VARCHAR(50), -- approved, rejected, escalated, timeout
  context_data JSONB DEFAULT '{}',
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for approval chains
CREATE INDEX idx_approval_chains_workflow_instance ON approval_chains(workflow_instance_id);
CREATE INDEX idx_approval_chains_status ON approval_chains(status);
CREATE INDEX idx_approval_chains_outcome ON approval_chains(outcome);

-- Create approval chain history table
CREATE TABLE IF NOT EXISTS approval_chain_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  approval_chain_id UUID REFERENCES approval_chains(id) ON DELETE CASCADE,
  level INTEGER NOT NULL,
  action VARCHAR(50) NOT NULL, -- started, approved, rejected, escalated, timeout
  actor_id UUID REFERENCES staff(id),
  actor_role VARCHAR(100),
  comments TEXT,
  attachments TEXT[],
  action_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index for approval chain history
CREATE INDEX idx_approval_chain_history_chain_id ON approval_chain_history(approval_chain_id);
CREATE INDEX idx_approval_chain_history_actor ON approval_chain_history(actor_id);

-- Create approval delegations table
CREATE TABLE IF NOT EXISTS approval_delegations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  from_user_id UUID REFERENCES staff(id) NOT NULL,
  to_user_id UUID REFERENCES staff(id) NOT NULL,
  approval_chain_id UUID REFERENCES approval_chains(id),
  workflow_task_id UUID REFERENCES workflow_tasks(id),
  reason TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  delegated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES staff(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for delegations
CREATE INDEX idx_approval_delegations_from_user ON approval_delegations(from_user_id);
CREATE INDEX idx_approval_delegations_to_user ON approval_delegations(to_user_id);
CREATE INDEX idx_approval_delegations_active ON approval_delegations(is_active);

-- Create approval templates table
CREATE TABLE IF NOT EXISTS approval_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  service_category_id UUID REFERENCES service_categories(id),
  request_type VARCHAR(100),
  approval_chain JSONB NOT NULL, -- Template approval chain configuration
  conditions JSONB DEFAULT '{}', -- Conditions for using this template
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES staff(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for approval templates
CREATE INDEX idx_approval_templates_name ON approval_templates(name);
CREATE INDEX idx_approval_templates_service_category ON approval_templates(service_category_id);
CREATE INDEX idx_approval_templates_active ON approval_templates(is_active);

-- Add approval-related columns to workflow_tasks if not exists
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'workflow_tasks' AND column_name = 'approval_deadline'
  ) THEN
    ALTER TABLE workflow_tasks ADD COLUMN approval_deadline TIMESTAMP WITH TIME ZONE;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'workflow_tasks' AND column_name = 'approval_reminders_sent'
  ) THEN
    ALTER TABLE workflow_tasks ADD COLUMN approval_reminders_sent INTEGER DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'workflow_tasks' AND column_name = 'is_delegated'
  ) THEN
    ALTER TABLE workflow_tasks ADD COLUMN is_delegated BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'workflow_tasks' AND column_name = 'original_assignee'
  ) THEN
    ALTER TABLE workflow_tasks ADD COLUMN original_assignee UUID REFERENCES staff(id);
  END IF;
END $$;

-- Create function to update approval chain timestamps
CREATE OR REPLACE FUNCTION update_approval_chain_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for approval chain timestamp updates
DROP TRIGGER IF EXISTS update_approval_chains_timestamp ON approval_chains;
CREATE TRIGGER update_approval_chains_timestamp
  BEFORE UPDATE ON approval_chains
  FOR EACH ROW
  EXECUTE FUNCTION update_approval_chain_timestamp();

-- Create function to handle approval delegation
CREATE OR REPLACE FUNCTION handle_approval_delegation(
  p_task_id UUID,
  p_from_user_id UUID,
  p_to_user_id UUID,
  p_reason TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  v_task_record RECORD;
  v_delegation_id UUID;
BEGIN
  -- Get task details
  SELECT * INTO v_task_record FROM workflow_tasks WHERE id = p_task_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found';
  END IF;
  
  -- Create delegation record
  INSERT INTO approval_delegations (
    from_user_id,
    to_user_id,
    workflow_task_id,
    reason,
    created_by
  ) VALUES (
    p_from_user_id,
    p_to_user_id,
    p_task_id,
    p_reason,
    p_from_user_id
  ) RETURNING id INTO v_delegation_id;
  
  -- Update task assignment
  UPDATE workflow_tasks
  SET 
    assigned_to = p_to_user_id,
    is_delegated = TRUE,
    original_assignee = COALESCE(original_assignee, assigned_to),
    task_data = jsonb_set(
      task_data,
      '{delegation_id}',
      to_jsonb(v_delegation_id)
    )
  WHERE id = p_task_id;
  
  -- Log delegation in approval chain history
  INSERT INTO approval_chain_history (
    approval_chain_id,
    level,
    action,
    actor_id,
    comments,
    action_data
  )
  SELECT 
    (task_data->>'approval_chain_id')::UUID,
    (task_data->>'approval_level')::INTEGER,
    'delegated',
    p_from_user_id,
    p_reason,
    jsonb_build_object(
      'delegated_to', p_to_user_id,
      'delegation_id', v_delegation_id
    )
  FROM workflow_tasks
  WHERE id = p_task_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Sample approval templates
INSERT INTO approval_templates (name, description, service_category_id, approval_chain, conditions) 
VALUES 
  ('Standard IT Request Approval', 
   'Standard approval workflow for regular IT requests',
   NULL,
   '{
     "id": "standard-it-approval",
     "name": "Standard IT Request Approval",
     "levels": [
       {
         "level": 0,
         "name": "Supervisor Approval",
         "approvers": [
           {"role": "Department Administrator"}
         ],
         "requireAll": false,
         "escalationMinutes": 1440
       },
       {
         "level": 1,
         "name": "IT Manager Approval",
         "approvers": [
           {"role": "IT Helpdesk Support"}
         ],
         "requireAll": false,
         "escalationMinutes": 2880,
         "skipCondition": "context.request.priority !== \"high\""
       }
     ],
     "timeoutAction": "escalate"
   }'::jsonb,
   '{"request_types": ["standard"]}'::jsonb),
   
  ('High Priority IT Request Approval',
   'Expedited approval workflow for high priority requests',
   NULL,
   '{
     "id": "high-priority-it-approval",
     "name": "High Priority IT Request Approval",
     "levels": [
       {
         "level": 0,
         "name": "Department Head Approval",
         "approvers": [
           {"role": "Department Administrator"},
           {"role": "IT Helpdesk Support"}
         ],
         "requireAll": true,
         "escalationMinutes": 120
       },
       {
         "level": 1,
         "name": "Executive Approval",
         "approvers": [
           {"role": "Web App System Administrator"}
         ],
         "requireAll": false,
         "escalationMinutes": 240
       }
     ],
     "timeoutAction": "auto_approve",
     "notificationTemplate": "high_priority_approval"
   }'::jsonb,
   '{"priority": "high", "request_amount_above": 100000}'::jsonb),
   
  ('Access Request Approval',
   'Approval workflow for system access requests',
   NULL,
   '{
     "id": "access-request-approval",
     "name": "Access Request Approval",
     "levels": [
       {
         "level": 0,
         "name": "Manager Approval",
         "approvers": [
           {"expression": "context.request.manager_id"}
         ],
         "requireAll": false,
         "escalationMinutes": 1440
       },
       {
         "level": 1,
         "name": "Security Review",
         "approvers": [
           {"role": "Web App System Administrator"}
         ],
         "requireAll": false,
         "escalationMinutes": 2880
       },
       {
         "level": 2,
         "name": "IT Implementation",
         "approvers": [
           {"role": "IT Helpdesk Support"}
         ],
         "requireAll": false,
         "escalationMinutes": 1440
       }
     ],
     "timeoutAction": "escalate"
   }'::jsonb,
   '{"service_categories": ["group_mail", "sharepoint", "pc_admin"]}'::jsonb);

-- Create view for pending approvals
CREATE OR REPLACE VIEW pending_approvals AS
SELECT 
  wt.id AS task_id,
  wt.task_name,
  wt.assigned_to,
  wt.assigned_role,
  wt.due_date,
  wt.created_at,
  ac.id AS approval_chain_id,
  ac.current_level,
  ac.chain_config->>'name' AS approval_type,
  wi.id AS workflow_instance_id,
  rf.title AS request_title,
  rf.priority,
  s.name_en AS requester_name,
  s.division_id AS requester_department
FROM workflow_tasks wt
INNER JOIN approval_chains ac ON (wt.task_data->>'approval_chain_id')::UUID = ac.id
INNER JOIN workflow_instances wi ON wt.workflow_instance_id = wi.id
LEFT JOIN request_forms rf ON wi.request_id = rf.id
LEFT JOIN staff s ON rf.requester_id = s.id
WHERE wt.status = 'pending'
  AND wt.task_type = 'multi_level_approval'
  AND ac.status IN ('pending', 'active');

-- Grant permissions
GRANT SELECT ON approval_chains TO authenticated;
GRANT SELECT ON approval_chain_history TO authenticated;
GRANT SELECT ON approval_delegations TO authenticated;
GRANT SELECT ON approval_templates TO authenticated;
GRANT SELECT ON pending_approvals TO authenticated;

-- Add RLS policies
ALTER TABLE approval_chains ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_chain_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_delegations ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_templates ENABLE ROW LEVEL SECURITY;

-- RLS policy for approval chains (users can see chains they're involved in)
CREATE POLICY approval_chains_view_policy ON approval_chains
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM workflow_tasks wt
      WHERE (wt.task_data->>'approval_chain_id')::UUID = approval_chains.id
      AND (
        wt.assigned_to = auth.uid() OR
        wt.completed_by = auth.uid() OR
        EXISTS (
          SELECT 1 FROM roles r
          JOIN staff s ON s.role_id = r.id
          WHERE s.auth_id = auth.uid()
          AND r.name IN ('Global Administrator', 'Web App System Administrator')
        )
      )
    )
  );

-- RLS policy for approval history (same as chains)
CREATE POLICY approval_history_view_policy ON approval_chain_history
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM approval_chains ac
      WHERE ac.id = approval_chain_history.approval_chain_id
      AND EXISTS (
        SELECT 1 FROM workflow_tasks wt
        WHERE (wt.task_data->>'approval_chain_id')::UUID = ac.id
        AND (
          wt.assigned_to = auth.uid() OR
          wt.completed_by = auth.uid() OR
          EXISTS (
            SELECT 1 FROM roles r
            JOIN staff s ON s.role_id = r.id
            WHERE s.auth_id = auth.uid()
            AND r.name IN ('Global Administrator', 'Web App System Administrator')
          )
        )
      )
    )
  );
