"use strict";(()=>{var e={};e.id=2498,e.ids=[2498],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71358:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>w,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var o={};t.r(o),t.d(o,{POST:()=>u});var s=t(42706),n=t(28203),a=t(45994),i=t(39187),c=t(52054);async function u(e,{params:r}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await e.from("staff").select("*").eq("auth_id",t.id).single();if(!s)return i.NextResponse.json({error:"User not found"},{status:404});let n=new c.I;try{return await n.cancelWorkflow(r.id,s.id),i.NextResponse.json({message:"Workflow cancelled successfully"})}catch(e){return console.error("Error cancelling workflow:",e),i.NextResponse.json({error:e.message||"Failed to cancel workflow"},{status:400})}}catch(e){return console.error("Error in workflow cancellation:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let l=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/instances/[id]/cancel/route",pathname:"/api/workflows/instances/[id]/cancel",filename:"route",bundlePath:"app/api/workflows/instances/[id]/cancel/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\cancel\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:w}=l;function f(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(71358));module.exports=o})();