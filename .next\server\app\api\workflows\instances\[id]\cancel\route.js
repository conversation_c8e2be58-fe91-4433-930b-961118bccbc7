"use strict";(()=>{var e={};e.id=2498,e.ids=[2498],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71358:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{POST:()=>l});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),c=t(61487),u=t(2924);async function l(e,{params:r}){try{let e=await (0,c.createServerClient)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await e.from("staff").select("*").eq("auth_id",t.id).single();if(!o)return i.NextResponse.json({error:"User not found"},{status:404});let a=new u.I;try{return await a.cancelWorkflow(r.id,o.id),i.NextResponse.json({message:"Workflow cancelled successfully"})}catch(e){return console.error("Error cancelling workflow:",e),i.NextResponse.json({error:e.message||"Failed to cancel workflow"},{status:400})}}catch(e){return console.error("Error in workflow cancellation:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/instances/[id]/cancel/route",pathname:"/api/workflows/instances/[id]/cancel",filename:"route",bundlePath:"app/api/workflows/instances/[id]/cancel/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\cancel\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:w,serverHooks:x}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:w})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(71358));module.exports=s})();