{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/vendors.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "lwN3sjIID4KeJtZ7M1CDs", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Qt9yVc6nArGwhhcc7MPzIRmejsfC9hKSkqitVR8cPok=", "__NEXT_PREVIEW_MODE_ID": "b4fa346a1e3475c94d8f35996c6be8ef", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "724437ecdc8cb3c4c36bda86dfb61aa024a69263fa7d5216d96e55a6304fc359", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a4c89e57311b1180beaadba02b87e186d32358212312b724004441d9e16bcfb8"}}}, "functions": {}, "sortedMiddleware": ["/"]}