-- Data Encryption Implementation
-- This migration adds encryption support for sensitive data fields

-- Enable pgcrypto extension for encryption functions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create vault schema for encryption keys
CREATE SCHEMA IF NOT EXISTS vault;

-- Key management table
CREATE TABLE IF NOT EXISTS vault.encryption_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key_name VARCHAR(255) UNIQUE NOT NULL,
  encrypted_key TEXT NOT NULL, -- Encrypted with master key
  algorithm VA<PERSON>HA<PERSON>(50) DEFAULT 'aes-256-gcm',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  rotated_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true
);

-- Create function to encrypt sensitive data
CREATE OR REPLACE FUNCTION vault.encrypt_sensitive(
  data TEXT,
  key_name <PERSON><PERSON><PERSON><PERSON> DEFAULT 'default'
) R<PERSON><PERSON>NS TEXT AS $$
DECLARE
  encryption_key TEXT;
BEGIN
  -- Get active encryption key
  SELECT encrypted_key INTO encryption_key
  FROM vault.encryption_keys
  WHERE key_name = key_name AND is_active = true
  LIMIT 1;
  
  IF encryption_key IS NULL THEN
    RAISE EXCEPTION 'No active encryption key found';
  END IF;
  
  -- Encrypt data using pgcrypto
  RETURN encode(
    pgp_sym_encrypt(
      data::text,
      encryption_key,
      'cipher-algo=aes256, compress-algo=1'
    ),
    'base64'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to decrypt sensitive data
CREATE OR REPLACE FUNCTION vault.decrypt_sensitive(
  encrypted_data TEXT,
  key_name VARCHAR DEFAULT 'default'
) RETURNS TEXT AS $$
DECLARE
  encryption_key TEXT;
BEGIN
  -- Get encryption key
  SELECT encrypted_key INTO encryption_key
  FROM vault.encryption_keys
  WHERE key_name = key_name
  LIMIT 1;
  
  IF encryption_key IS NULL THEN
    RAISE EXCEPTION 'Encryption key not found';
  END IF;
  
  -- Decrypt data
  RETURN pgp_sym_decrypt(
    decode(encrypted_data, 'base64'),
    encryption_key
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add encrypted columns for sensitive data
-- Staff table sensitive fields
ALTER TABLE staff ADD COLUMN IF NOT EXISTS phone_encrypted TEXT;
ALTER TABLE staff ADD COLUMN IF NOT EXISTS personal_email_encrypted TEXT;
ALTER TABLE staff ADD COLUMN IF NOT EXISTS address_encrypted TEXT;
ALTER TABLE staff ADD COLUMN IF NOT EXISTS emergency_contact_encrypted TEXT;

-- Create triggers to automatically encrypt data on insert/update
CREATE OR REPLACE FUNCTION encrypt_staff_sensitive_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Encrypt phone number if provided
  IF NEW.phone IS NOT NULL THEN
    NEW.phone_encrypted := vault.encrypt_sensitive(NEW.phone);
    NEW.phone := NULL; -- Clear plaintext
  END IF;
  
  -- Encrypt personal email if provided
  IF NEW.personal_email IS NOT NULL THEN
    NEW.personal_email_encrypted := vault.encrypt_sensitive(NEW.personal_email);
    NEW.personal_email := NULL;
  END IF;
  
  -- Encrypt address if provided
  IF NEW.address IS NOT NULL THEN
    NEW.address_encrypted := vault.encrypt_sensitive(NEW.address);
    NEW.address := NULL;
  END IF;
  
  -- Encrypt emergency contact if provided
  IF NEW.emergency_contact IS NOT NULL THEN
    NEW.emergency_contact_encrypted := vault.encrypt_sensitive(NEW.emergency_contact);
    NEW.emergency_contact := NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for staff table
DROP TRIGGER IF EXISTS encrypt_staff_data_trigger ON staff;
CREATE TRIGGER encrypt_staff_data_trigger
  BEFORE INSERT OR UPDATE ON staff
  FOR EACH ROW
  EXECUTE FUNCTION encrypt_staff_sensitive_data();

-- Create view for decrypted staff data (for authorized access)
CREATE OR REPLACE VIEW staff_decrypted AS
SELECT 
  id,
  auth_id,
  staff_id,
  name_jp,
  name_en,
  name_kana,
  email,
  pc_login_id,
  division_id,
  group_id,
  role_id,
  position,
  gender,
  birth_date,
  employment_type,
  union_id,
  is_active,
  pc_id,
  CASE 
    WHEN phone_encrypted IS NOT NULL 
    THEN vault.decrypt_sensitive(phone_encrypted)
    ELSE NULL
  END AS phone,
  CASE 
    WHEN personal_email_encrypted IS NOT NULL 
    THEN vault.decrypt_sensitive(personal_email_encrypted)
    ELSE NULL
  END AS personal_email,
  CASE 
    WHEN address_encrypted IS NOT NULL 
    THEN vault.decrypt_sensitive(address_encrypted)
    ELSE NULL
  END AS address,
  CASE 
    WHEN emergency_contact_encrypted IS NOT NULL 
    THEN vault.decrypt_sensitive(emergency_contact_encrypted)
    ELSE NULL
  END AS emergency_contact,
  created_at,
  updated_at
FROM staff;

-- RLS for the decrypted view
ALTER VIEW staff_decrypted SET (security_barrier = on);

-- Grant access to the view based on roles
CREATE OR REPLACE FUNCTION can_access_decrypted_staff_data(user_id UUID, staff_row_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
  user_dept UUID;
  staff_dept UUID;
BEGIN
  -- Get user's role and department
  SELECT r.name, p.division_id 
  INTO user_role, user_dept
  FROM profiles p
  JOIN roles r ON p.role_id = r.id
  WHERE p.auth_id = user_id;
  
  -- Global and System admins can access all
  IF user_role IN ('Global Administrator', 'Web App System Administrator') THEN
    RETURN TRUE;
  END IF;
  
  -- HR staff can access all
  IF user_role = 'HR Staff' THEN
    RETURN TRUE;
  END IF;
  
  -- Get staff member's department
  SELECT division_id INTO staff_dept
  FROM staff
  WHERE id = staff_row_id;
  
  -- Department admins can access their department
  IF user_role = 'Department Administrator' AND user_dept = staff_dept THEN
    RETURN TRUE;
  END IF;
  
  -- Users can access their own data
  IF EXISTS (SELECT 1 FROM staff WHERE id = staff_row_id AND auth_id = user_id) THEN
    RETURN TRUE;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Encrypt password reset tokens
ALTER TABLE password_reset_tokens ADD COLUMN IF NOT EXISTS token_encrypted TEXT;

CREATE OR REPLACE FUNCTION encrypt_password_token()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.token IS NOT NULL THEN
    NEW.token_encrypted := vault.encrypt_sensitive(NEW.token);
    NEW.token := substr(NEW.token, 1, 6) || '****'; -- Keep first 6 chars for reference
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS encrypt_password_token_trigger ON password_reset_tokens;
CREATE TRIGGER encrypt_password_token_trigger
  BEFORE INSERT OR UPDATE ON password_reset_tokens
  FOR EACH ROW
  EXECUTE FUNCTION encrypt_password_token();

-- Encrypt API keys
CREATE TABLE IF NOT EXISTS encrypted_api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  name VARCHAR(255) NOT NULL,
  key_hash VARCHAR(255) NOT NULL, -- For lookup
  encrypted_key TEXT NOT NULL,
  last_used_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Function to create encrypted API key
CREATE OR REPLACE FUNCTION create_encrypted_api_key(
  p_user_id UUID,
  p_name VARCHAR,
  p_expires_in_days INTEGER DEFAULT 365
) RETURNS TABLE(id UUID, key_prefix VARCHAR, expires_at TIMESTAMP WITH TIME ZONE) AS $$
DECLARE
  v_api_key VARCHAR;
  v_key_hash VARCHAR;
  v_key_id UUID;
  v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Generate API key
  v_api_key := encode(gen_random_bytes(32), 'base64');
  v_key_hash := encode(digest(v_api_key, 'sha256'), 'hex');
  v_expires_at := NOW() + (p_expires_in_days || ' days')::INTERVAL;
  
  -- Insert encrypted key
  INSERT INTO encrypted_api_keys (
    user_id, name, key_hash, encrypted_key, expires_at
  ) VALUES (
    p_user_id, p_name, v_key_hash, vault.encrypt_sensitive(v_api_key), v_expires_at
  ) RETURNING encrypted_api_keys.id INTO v_key_id;
  
  -- Return info
  RETURN QUERY SELECT v_key_id, substr(v_api_key, 1, 8) AS key_prefix, v_expires_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add encryption for file attachments metadata
CREATE TABLE IF NOT EXISTS encrypted_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  request_id UUID REFERENCES request_forms(id),
  file_name_encrypted TEXT NOT NULL,
  file_path_encrypted TEXT NOT NULL,
  file_size BIGINT,
  mime_type VARCHAR(255),
  uploaded_by UUID REFERENCES auth.users(id),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS for encrypted tables
ALTER TABLE encrypted_api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE encrypted_attachments ENABLE ROW LEVEL SECURITY;

-- Policies for encrypted API keys
CREATE POLICY "Users can manage own API keys" ON encrypted_api_keys
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admins can view all API keys" ON encrypted_api_keys
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN roles r ON p.role_id = r.id
      WHERE p.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Initialize default encryption key (in production, this should be done securely)
-- This is just for development/testing
INSERT INTO vault.encryption_keys (key_name, encrypted_key)
VALUES ('default', 'CHANGE_THIS_IN_PRODUCTION_USE_PROPER_KEY_MANAGEMENT')
ON CONFLICT (key_name) DO NOTHING;

-- Audit log for encryption events
CREATE TABLE IF NOT EXISTS encryption_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type VARCHAR(50) NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  table_name VARCHAR(255),
  record_id UUID,
  key_name VARCHAR(255),
  success BOOLEAN,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to log encryption events
CREATE OR REPLACE FUNCTION log_encryption_event(
  p_event_type VARCHAR,
  p_user_id UUID,
  p_table_name VARCHAR,
  p_record_id UUID,
  p_key_name VARCHAR,
  p_success BOOLEAN,
  p_error_message TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  INSERT INTO encryption_audit_log (
    event_type, user_id, table_name, record_id, key_name, success, error_message
  ) VALUES (
    p_event_type, p_user_id, p_table_name, p_record_id, p_key_name, p_success, p_error_message
  );
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON SCHEMA vault IS 'Schema for encryption key management and cryptographic functions';
COMMENT ON TABLE vault.encryption_keys IS 'Stores encryption keys for data protection';
COMMENT ON FUNCTION vault.encrypt_sensitive IS 'Encrypts sensitive data using the specified key';
COMMENT ON FUNCTION vault.decrypt_sensitive IS 'Decrypts sensitive data using the specified key';
COMMENT ON VIEW staff_decrypted IS 'View providing decrypted staff data with proper access control';
