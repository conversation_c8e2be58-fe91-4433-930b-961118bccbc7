'use client'

import { useState } from 'react'
import { useSupabase } from '@/components/providers/supabase-provider'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { UserMultiSelect } from '@/components/ui/user-multi-select'
import { useDepartmentAccess } from '@/lib/auth/hooks'

interface PcAdminRequestDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function PcAdminRequestDialog({ open, onOpenChange, onSuccess }: PcAdminRequestDialogProps) {
  const [loading, setLoading] = useState(false)
  const [actionType, setActionType] = useState<string>('grant')
  const [pcId, setPcId] = useState('')
  const [reason, setReason] = useState('')
  const [softwareName, setSoftwareName] = useState('')
  const [urgency, setUrgency] = useState<string>('medium')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const { supabase, user } = useSupabase()
  const { departmentId } = useDepartmentAccess()

  const handleSubmit = async () => {
    if (!pcId || !reason || selectedUsers.length === 0) {
      toast.error('Please fill in all required fields')
      return
    }

    setLoading(true)
    try {
      // Create a new request form
      const { data: requestForm, error: formError } = await supabase
        .from('request_forms')
        .insert({
          requester_id: user?.id,
          title: `PC Admin Request - ${actionType}`,
          description: reason,
          priority: urgency,
          status: 'submitted',
          submitted_at: new Date().toISOString()
        })
        .select()
        .single()

      if (formError) throw formError

      // Create request items for each selected user
      const requestItems = selectedUsers.map(userId => ({
        request_form_id: requestForm.id,
        service_category_id: null, // Will be set by trigger or separately
        affected_user_id: userId,
        action_type: actionType,
        target_resource: pcId,
        service_type: 'PC Administrative Status',
        request_details: {
          pc_id: pcId,
          reason: reason,
          software_name: actionType === 'software_install' ? softwareName : null,
          urgency: urgency
        },
        parameters: {
          pc_id: pcId,
          reason: reason,
          software_name: actionType === 'software_install' ? softwareName : null,
          urgency: urgency
        },
        affected_users: [userId],
        status: 'pending'
      }))

      const { error: itemsError } = await supabase
        .from('request_items')
        .insert(requestItems)

      if (itemsError) throw itemsError

      toast.success('PC admin request submitted successfully')
      onSuccess()
      resetForm()
    } catch (error) {
      console.error('Error submitting PC admin request:', error)
      toast.error('Failed to submit request')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setActionType('grant')
    setPcId('')
    setReason('')
    setSoftwareName('')
    setUrgency('medium')
    setSelectedUsers([])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>New PC Administrative Request</DialogTitle>
          <DialogDescription>
            Submit a request for PC administrative privileges or software installation
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="action-type">Request Type</Label>
            <Select value={actionType} onValueChange={setActionType}>
              <SelectTrigger id="action-type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="grant">Grant Administrative Access</SelectItem>
                <SelectItem value="revoke">Revoke Administrative Access</SelectItem>
                <SelectItem value="software_install">Software Installation</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="pc-id">PC ID Number *</Label>
            <Input
              id="pc-id"
              placeholder="e.g., M241234"
              value={pcId}
              onChange={(e) => setPcId(e.target.value)}
            />
          </div>

          {actionType === 'software_install' && (
            <div className="grid gap-2">
              <Label htmlFor="software-name">Software Name *</Label>
              <Input
                id="software-name"
                placeholder="Enter software name"
                value={softwareName}
                onChange={(e) => setSoftwareName(e.target.value)}
              />
            </div>
          )}

          <div className="grid gap-2">
            <Label htmlFor="urgency">Priority</Label>
            <Select value={urgency} onValueChange={setUrgency}>
              <SelectTrigger id="urgency">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="reason">Reason for Request *</Label>
            <Textarea
              id="reason"
              placeholder="Provide detailed justification for this request"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="users">Affected Users *</Label>
            <UserMultiSelect
              selectedUsers={selectedUsers}
              onSelectionChange={setSelectedUsers}
              departmentId={departmentId}
              placeholder="Select users who need PC admin access"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Submitting...' : 'Submit Request'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
