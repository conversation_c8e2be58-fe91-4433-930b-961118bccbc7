'use client'

import { useAuth } from '@/lib/auth-context'
import { usePermissions } from '@/lib/use-permissions'
import AuthGuard from '@/components/auth/auth-guard'
import WorkflowMonitoringDashboard from '@/components/workflows/monitoring/WorkflowMonitoringDashboard'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function WorkflowMonitoringPage() {
  const { user } = useAuth()
  const { isGlobalAdmin, isSystemAdmin, isDepartmentAdmin, isITSupport } = usePermissions()
  const router = useRouter()

  // Check if user has permission to view workflow monitoring
  const hasPermission = isGlobalAdmin() || isSystemAdmin() || isDepartmentAdmin() || isITSupport()

  if (!hasPermission) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-8">You do not have permission to view workflow monitoring.</p>
            <Button onClick={() => router.push('/dashboard')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </div>
        </div>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="mr-4"
                  onClick={() => router.push('/dashboard')}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Workflow Monitoring</h1>
                  <p className="text-sm text-gray-600">ワークフロー監視ダッシュボード</p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <WorkflowMonitoringDashboard />
        </main>
      </div>
    </AuthGuard>
  )
}
