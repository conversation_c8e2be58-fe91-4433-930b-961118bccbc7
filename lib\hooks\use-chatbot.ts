import { useState, useCallback } from 'react'
import { chatbotService, type ChatMessage, type ChatbotResponse } from '@/lib/services/chatbot-service'
import { toast } from '@/components/ui/use-toast'

export interface UseChatbotOptions {
  onMessageSent?: (message: string) => void
  onResponseReceived?: (response: ChatbotResponse) => void
  onError?: (error: Error) => void
}

export function useChatbot(options: UseChatbotOptions = {}) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [faqs, setFaqs] = useState<any[]>([])
  const [tutorials, setTutorials] = useState<any[]>([])

  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim() || isLoading) return

    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)
    options.onMessageSent?.(message)

    try {
      const response = await chatbotService.sendMessage(message)
      
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.response,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
      setSuggestions(response.suggestions || [])
      setFaqs(response.faqs || [])
      setTutorials(response.tutorials || [])
      
      options.onResponseReceived?.(response)
    } catch (error) {
      console.error('Failed to send message:', error)
      const errorObj = error instanceof Error ? error : new Error('Unknown error')
      
      toast({
        title: 'エラー',
        description: 'メッセージの送信に失敗しました。',
        variant: 'destructive'
      })
      
      options.onError?.(errorObj)
    } finally {
      setIsLoading(false)
    }
  }, [isLoading, options])

  const clearChat = useCallback(async () => {
    setMessages([])
    setSuggestions([])
    setFaqs([])
    setTutorials([])
    await chatbotService.clearConversation()
  }, [])

  const setContext = useCallback((context: Parameters<typeof chatbotService.setContext>[0]) => {
    chatbotService.setContext(context)
  }, [])

  return {
    messages,
    isLoading,
    suggestions,
    faqs,
    tutorials,
    sendMessage,
    clearChat,
    setContext
  }
}
