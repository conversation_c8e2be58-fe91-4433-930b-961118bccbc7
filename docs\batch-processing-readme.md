# ITSync Batch Processing System

## Overview

The Batch Processing System is a key component of the ITSync enterprise-grade IT Helpdesk platform. It enables efficient handling of multiple IT service requests across multiple users in a single atomic operation, significantly reducing manual effort and ensuring data consistency.

## Features

### Core Capabilities
- **Multi-User Processing**: Handle requests for multiple users simultaneously
- **Multi-Service Support**: Process multiple service types in a single batch
- **Atomic Transactions**: All-or-nothing processing ensures data integrity
- **Real-time Progress Tracking**: Monitor batch status with live updates
- **Error Recovery**: Retry failed items without reprocessing successful ones
- **Department-Based Access Control**: Automatic filtering based on user permissions

### Supported Scenarios (PRD Requirements)
1. **Single User, Multiple Requests (Addition)**: Add multiple services for one user
2. **Multiple Users, Multiple Requests (Addition)**: Add multiple services for multiple users
3. **Multiple Users, Single Request (Addition)**: Add the same service for multiple users
4. **Single User, Multiple Requests (Deletion)**: Remove multiple services from one user
5. **Single User, Multiple Requests (Mixed)**: Add and remove services for one user
6. **Single User, Multiple Categories (Mixed)**: Complex operations across service categories

### Service Categories
- **Group Mail (GM)**: 51 group email addresses
- **Mailbox (MB)**: 28 mailbox addresses
- **SharePoint Libraries (SPO)**: 42 document libraries
- **PC Administrative Status (PCA)**: Administrative privileges with searchable PC ID
- **Password Reset (PWR)**: M365 and MFA password resets

## Architecture

### Database Schema
```sql
batch_operations          -- Main batch tracking table
├── batch_items          -- Individual items within a batch
├── batch_validation_rules -- Pre-processing validation rules
└── batch_progress_logs   -- Detailed progress tracking
```

### Components
1. **BatchRequestBuilder**: Interactive UI for creating batch requests
2. **BatchStatusMonitor**: Real-time monitoring of batch progress
3. **BatchProcessingService**: Core service handling batch operations
4. **Database Functions**: PostgreSQL functions for atomic processing

## Usage

### Creating a Batch Request
1. Navigate to `/batch-processing` from the dashboard
2. Select the processing scenario