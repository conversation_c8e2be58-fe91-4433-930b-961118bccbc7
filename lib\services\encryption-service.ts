import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32;
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

// Get encryption key from environment or generate
const getEncryptionKey = async (): Promise<Buffer> => {
  const masterKey = process.env.ENCRYPTION_KEY || process.env.SUPABASE_ANON_KEY;
  if (!masterKey) {
    throw new Error('Encryption key not configured');
  }
  
  // Derive key using scrypt
  const salt = Buffer.from('itsync-mfa-salt-v1', 'utf8');
  const key = await scryptAsync(masterKey, salt, KEY_LENGTH) as Buffer;
  return key;
};

/**
 * Encrypt sensitive data
 */
export async function encrypt(text: string): Promise<string> {
  try {
    const key = await getEncryptionKey();
    const iv = randomBytes(IV_LENGTH);
    const cipher = createCipheriv(ALGORITHM, key, iv);
    
    const encrypted = Buffer.concat([
      cipher.update(text, 'utf8'),
      cipher.final()
    ]);
    
    const tag = cipher.getAuthTag();
    
    // Combine IV, tag, and encrypted data
    const combined = Buffer.concat([iv, tag, encrypted]);
    
    return combined.toString('base64');
  } catch (error) {
    throw new Error(`Encryption failed: ${error.message}`);
  }
}

/**
 * Decrypt sensitive data
 */
export async function decrypt(encryptedText: string): Promise<string> {
  try {
    const key = await getEncryptionKey();
    const combined = Buffer.from(encryptedText, 'base64');
    
    // Extract IV, tag, and encrypted data
    const iv = combined.slice(0, IV_LENGTH);
    const tag = combined.slice(IV_LENGTH, IV_LENGTH + TAG_LENGTH);
    const encrypted = combined.slice(IV_LENGTH + TAG_LENGTH);
    
    const decipher = createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    
    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]);
    
    return decrypted.toString('utf8');
  } catch (error) {
    throw new Error(`Decryption failed: ${error.message}`);
  }
}

/**
 * Hash data for comparison (one-way)
 */
export async function hash(data: string): Promise<string> {
  const { createHash } = await import('crypto');
  const salt = randomBytes(SALT_LENGTH);
  const hash = createHash('sha256')
    .update(salt)
    .update(data)
    .digest();
  
  return Buffer.concat([salt, hash]).toString('base64');
}

/**
 * Verify hashed data
 */
export async function verifyHash(data: string, hashedData: string): Promise<boolean> {
  const { createHash } = await import('crypto');
  const combined = Buffer.from(hashedData, 'base64');
  const salt = combined.slice(0, SALT_LENGTH);
  const originalHash = combined.slice(SALT_LENGTH);
  
  const hash = createHash('sha256')
    .update(salt)
    .update(data)
    .digest();
  
  return hash.equals(originalHash);
}
