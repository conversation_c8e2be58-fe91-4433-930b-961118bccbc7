import { createCipher<PERSON>, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32;
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

// Get encryption key from environment or generate
const getEncryptionKey = async (salt: Buffer): Promise<Buffer> => {
  const masterKey = process.env.ENCRYPTION_KEY;
  if (!masterKey) {
    throw new Error('ENCRYPTION_KEY environment variable is required for secure encryption');
  }
  
  if (masterKey.length < 32) {
    throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
  }
  
  // Derive key using scrypt with provided salt
  const key = await scryptAsync(masterKey, salt, KEY_LENGTH) as Buffer;
  return key;
};

/**
 * Encrypt sensitive data
 */
export async function encrypt(text: string): Promise<string> {
  try {
    const salt = randomBytes(SALT_LENGTH);
    const key = await getEncryptionKey(salt);
    const iv = randomBytes(IV_LENGTH);
    const cipher = createCipheriv(ALGORITHM, key, iv);
    
    const encrypted = Buffer.concat([
      cipher.update(text, 'utf8'),
      cipher.final()
    ]);
    
    const tag = cipher.getAuthTag();
    
    // Combine salt, IV, tag, and encrypted data
    const combined = Buffer.concat([salt, iv, tag, encrypted]);
    
    return combined.toString('base64');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown encryption error';
    throw new Error(`Encryption failed: ${errorMessage}`);
  }
}

/**
 * Decrypt sensitive data
 */
export async function decrypt(encryptedText: string): Promise<string> {
  try {
    const combined = Buffer.from(encryptedText, 'base64');
    
    // Extract salt, IV, tag, and encrypted data
    const salt = combined.slice(0, SALT_LENGTH);
    const iv = combined.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
    const tag = combined.slice(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
    const encrypted = combined.slice(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
    
    const key = await getEncryptionKey(salt);
    const decipher = createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    
    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]);
    
    return decrypted.toString('utf8');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown decryption error';
    throw new Error(`Decryption failed: ${errorMessage}`);
  }
}

/**
 * Hash data for comparison (one-way)
 */
export async function hash(data: string): Promise<string> {
  const { createHash } = await import('crypto');
  const salt = randomBytes(SALT_LENGTH);
  const hash = createHash('sha256')
    .update(salt)
    .update(data)
    .digest();
  
  return Buffer.concat([salt, hash]).toString('base64');
}

/**
 * Verify hashed data
 */
export async function verifyHash(data: string, hashedData: string): Promise<boolean> {
  const { createHash } = await import('crypto');
  const combined = Buffer.from(hashedData, 'base64');
  const salt = combined.slice(0, SALT_LENGTH);
  const originalHash = combined.slice(SALT_LENGTH);
  
  const hash = createHash('sha256')
    .update(salt)
    .update(data)
    .digest();
  
  return hash.equals(originalHash);
}
