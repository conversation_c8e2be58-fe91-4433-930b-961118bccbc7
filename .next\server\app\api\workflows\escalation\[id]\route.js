"use strict";(()=>{var e={};e.id=9402,e.ids=[9402],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},53680:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>O,routeModule:()=>p,serverHooks:()=>v,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var o={};t.r(o),t.d(o,{DELETE:()=>c,GET:()=>d,PUT:()=>l});var s=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(50867);async function d(e,{params:r}){try{let e=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let s=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(e),a=new u.W(e,s),n=await a.getRule(r.id);if(!n)return i.NextResponse.json({error:"Rule not found"},{status:404});return i.NextResponse.json({rule:n})}catch(e){return console.error("Error fetching escalation rule:",e),i.NextResponse.json({error:"Failed to fetch escalation rule"},{status:500})}}async function l(e,{params:r}){try{let t=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:o},error:s}=await t.auth.getUser();if(s||!o)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await t.from("staff").select("*, roles(*)").eq("auth_id",o.id).single();if(!a||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(a.roles.name))return i.NextResponse.json({error:"Forbidden"},{status:403});let n=await e.json(),d=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(t),l=new u.W(t,d),c=await l.updateRule(r.id,n,a.id);return i.NextResponse.json({rule:c})}catch(e){return console.error("Error updating escalation rule:",e),i.NextResponse.json({error:"Failed to update escalation rule"},{status:500})}}async function c(e,{params:r}){try{let e=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await e.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await e.from("staff").select("*, roles(*)").eq("auth_id",t.id).single();if(!s||!["Global Administrator","Web App System Administrator"].includes(s.roles.name))return i.NextResponse.json({error:"Forbidden"},{status:403});let a=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(e),n=new u.W(e,a);return await n.deleteRule(r.id,s.id),i.NextResponse.json({message:"Rule deleted successfully"})}catch(e){return console.error("Error deleting escalation rule:",e),i.NextResponse.json({error:"Failed to delete escalation rule"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let p=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/[id]/route",pathname:"/api/workflows/escalation/[id]",filename:"route",bundlePath:"app/api/workflows/escalation/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\[id]\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:v}=p;function O(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(53680));module.exports=o})();