(()=>{var e={};e.id=9402,e.ids=[9402],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},23589:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>y,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var a={};r.r(a),r.d(a,{DELETE:()=>g,GET:()=>d,PUT:()=>p});var s=r(42706),i=r(28203),o=r(45994),n=r(39187),c=r(61487),l=r(50867),u=r(68967);async function d(e,{params:t}){try{let e=(0,c.U)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let s=new l.W(e,u.H),i=await s.getRule(t.id);if(!i)return n.NextResponse.json({error:"Rule not found"},{status:404});return n.NextResponse.json({rule:i})}catch(e){return console.error("Error fetching escalation rule:",e),n.NextResponse.json({error:"Failed to fetch escalation rule"},{status:500})}}async function p(e,{params:t}){try{let r=(0,c.U)(),{data:{user:a},error:s}=await r.auth.getUser();if(s||!a)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:i}=await r.from("staff").select("*, roles(*)").eq("auth_id",a.id).single();if(!i||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(i.roles.name))return n.NextResponse.json({error:"Forbidden"},{status:403});let o=await e.json(),d=new l.W(r,u.H),p=await d.updateRule(t.id,o,i.id);return n.NextResponse.json({rule:p})}catch(e){return console.error("Error updating escalation rule:",e),n.NextResponse.json({error:"Failed to update escalation rule"},{status:500})}}async function g(e,{params:t}){try{let e=(0,c.U)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await e.from("staff").select("*, roles(*)").eq("auth_id",r.id).single();if(!s||!["Global Administrator","Web App System Administrator"].includes(s.roles.name))return n.NextResponse.json({error:"Forbidden"},{status:403});let i=new l.W(e,u.H);return await i.deleteRule(t.id,s.id),n.NextResponse.json({message:"Rule deleted successfully"})}catch(e){return console.error("Error deleting escalation rule:",e),n.NextResponse.json({error:"Failed to delete escalation rule"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/[id]/route",pathname:"/api/workflows/escalation/[id]",filename:"route",bundlePath:"app/api/workflows/escalation/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:m}=y;function _(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},96487:()=>{},78335:()=>{},68967:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});class a{async logAction(e){try{console.log("Audit log entry:",{...e,timestamp:e.timestamp||new Date}),await new Promise(e=>setTimeout(e,100))}catch(e){console.error("Failed to log audit entry:",e)}}async getAuditLogs(e={}){try{return await new Promise(e=>setTimeout(e,200)),[{id:"1",user_id:"user1",action:"login",resource_type:"auth",timestamp:new Date,ip_address:"***********"}]}catch(e){return console.error("Failed to fetch audit logs:",e),[]}}async getUserActivity(e,t=50){return this.getAuditLogs({userId:e,limit:t})}async getResourceActivity(e,t){return this.getAuditLogs({resourceType:e})}async logLogin(e,t,r){await this.logAction({user_id:e,action:"login",resource_type:"auth",ip_address:t,user_agent:r})}async logLogout(e,t){await this.logAction({user_id:e,action:"logout",resource_type:"auth",ip_address:t})}async logPasswordReset(e,t){await this.logAction({user_id:e,action:"password_reset",resource_type:"auth",ip_address:t})}async logTicketCreated(e,t){await this.logAction({user_id:e,action:"create",resource_type:"ticket",resource_id:t})}async logTicketUpdated(e,t,r){await this.logAction({user_id:e,action:"update",resource_type:"ticket",resource_id:t,details:r})}async logWorkflowAction(e,t,r,a){await this.logAction({user_id:e,action:r,resource_type:"workflow",resource_id:t,details:a})}}let s=new a},50867:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});class a{constructor(e,t){this.supabase=e,this.auditService=t}async createRule(e){try{let{data:t,error:r}=await this.supabase.from("escalation_rules").insert({...e,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}).select().single();if(r)throw r;return await this.auditService.logActivity({userId:e.createdBy,action:"create_escalation_rule",resourceType:"escalation_rule",resourceId:t.id,details:{name:e.name}}),t}catch(e){throw console.error("Error creating escalation rule:",e),e}}async updateRule(e,t,r){try{let{data:a,error:s}=await this.supabase.from("escalation_rules").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return await this.auditService.logActivity({userId:r,action:"update_escalation_rule",resourceType:"escalation_rule",resourceId:e,details:{updates:t}}),a}catch(e){throw console.error("Error updating escalation rule:",e),e}}async deleteRule(e,t){try{let{error:r}=await this.supabase.from("escalation_rules").delete().eq("id",e);if(r)throw r;await this.auditService.logActivity({userId:t,action:"delete_escalation_rule",resourceType:"escalation_rule",resourceId:e})}catch(e){throw console.error("Error deleting escalation rule:",e),e}}async getRules(e){try{let t=this.supabase.from("escalation_rules").select("*");e?.isActive!==void 0&&(t=t.eq("is_active",e.isActive)),e?.triggerType&&(t=t.eq("trigger_type",e.triggerType)),e?.priority&&(t=t.eq("priority",e.priority)),e?.categoryId&&(t=t.contains("applicable_to_categories",[e.categoryId])),e?.departmentId&&(t=t.contains("applicable_to_departments",[e.departmentId]));let{data:r,error:a}=await t.order("priority",{ascending:!1});if(a)throw a;return r||[]}catch(e){throw console.error("Error fetching escalation rules:",e),e}}async getRule(e){try{let{data:t,error:r}=await this.supabase.from("escalation_rules").select("*").eq("id",e).single();if(r)throw r;return t}catch(e){throw console.error("Error fetching escalation rule:",e),e}}async toggleRuleStatus(e,t){try{let{data:r}=await this.supabase.from("escalation_rules").select("is_active").eq("id",e).single();if(!r)throw Error("Rule not found");let{data:a,error:s}=await this.supabase.from("escalation_rules").update({is_active:!r.is_active,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return await this.auditService.logActivity({userId:t,action:"toggle_escalation_rule",resourceType:"escalation_rule",resourceId:e,details:{isActive:a.is_active}}),a}catch(e){throw console.error("Error toggling rule status:",e),e}}async createDefaultRules(e){for(let t of[{name:"SLA Breach Critical Priority",description:"Escalate when critical priority requests breach SLA",triggerType:"sla_breach",conditions:[{field:"priority",operator:"equals",value:"critical"},{field:"status",operator:"equals",value:"breached"}],actions:[{type:"notify",target:"role:IT Helpdesk Support",parameters:{message:"Critical SLA breach requires immediate attention"}},{type:"change_priority",parameters:{priority:"critical"}},{type:"notify",target:"manager",parameters:{message:"Critical request has breached SLA"}}],priority:"critical",isActive:!0,retryInterval:15,maxRetries:3,createdBy:e},{name:"Overdue High Priority Requests",description:"Escalate high priority requests overdue by 24 hours",triggerType:"overdue",conditions:[{field:"priority",operator:"equals",value:"high"},{field:"age_hours",operator:"greater_than",value:24}],actions:[{type:"reassign",target:"role:IT Helpdesk Support"},{type:"notify",target:"manager",parameters:{message:"High priority request overdue by 24+ hours"}}],priority:"high",isActive:!0,retryInterval:30,maxRetries:2,createdBy:e},{name:"Approval Timeout Auto-Escalate",description:"Escalate to next level when approval times out",triggerType:"approval_timeout",conditions:[],actions:[{type:"add_approver",target:"manager"},{type:"notify",target:"manager",parameters:{message:"Approval request has timed out and requires your attention"}}],priority:"medium",isActive:!0,createdBy:e},{name:"Password Reset Urgent",description:"Escalate password reset requests older than 2 hours",triggerType:"overdue",conditions:[{field:"service_category",operator:"contains",value:"password"},{field:"age_hours",operator:"greater_than",value:2}],actions:[{type:"change_priority",parameters:{priority:"high"}},{type:"notify",target:"role:IT Helpdesk Support",parameters:{message:"Password reset request requires urgent attention"}}],priority:"high",isActive:!0,applicableToCategories:["PWR","O365"],createdBy:e}])await this.createRule(t)}validateRule(e){let t=[];for(let r of(e.name&&0!==e.name.trim().length||t.push("Rule name is required"),e.triggerType||t.push("Trigger type is required"),e.actions&&0!==e.actions.length||t.push("At least one action is required"),e.conditions||[]))r.field&&r.operator||t.push("Invalid condition: field and operator are required");for(let r of e.actions||[])r.type||t.push("Invalid action: type is required"),["notify","reassign","add_approver"].includes(r.type)&&!r.target&&t.push(`Invalid action: target is required for ${r.type}`);return{isValid:0===t.length,errors:t}}}},61487:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var a=r(49064),s=r(44512);let i=()=>{let e=(0,s.UL)();return(0,a.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:a})=>e.set(t,r,a))}catch{}}}})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5994,5452,4512,9064],()=>r(23589));module.exports=a})();