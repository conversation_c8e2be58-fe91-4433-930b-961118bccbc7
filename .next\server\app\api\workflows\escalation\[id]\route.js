"use strict";(()=>{var e={};e.id=9402,e.ids=[9402],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},53680:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>p,GET:()=>l,PUT:()=>c});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(61487),d=t(50867);async function l(e,{params:r}){try{let e=(0,u.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(e),a=new d.W(e,o),n=await a.getRule(r.id);if(!n)return i.NextResponse.json({error:"Rule not found"},{status:404});return i.NextResponse.json({rule:n})}catch(e){return console.error("Error fetching escalation rule:",e),i.NextResponse.json({error:"Failed to fetch escalation rule"},{status:500})}}async function c(e,{params:r}){try{let t=(0,u.U)(),{data:{user:s},error:o}=await t.auth.getUser();if(o||!s)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await t.from("staff").select("*, roles(*)").eq("auth_id",s.id).single();if(!a||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(a.roles.name))return i.NextResponse.json({error:"Forbidden"},{status:403});let n=await e.json(),l=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(t),c=new d.W(t,l),p=await c.updateRule(r.id,n,a.id);return i.NextResponse.json({rule:p})}catch(e){return console.error("Error updating escalation rule:",e),i.NextResponse.json({error:"Failed to update escalation rule"},{status:500})}}async function p(e,{params:r}){try{let e=(0,u.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await e.from("staff").select("*, roles(*)").eq("auth_id",t.id).single();if(!o||!["Global Administrator","Web App System Administrator"].includes(o.roles.name))return i.NextResponse.json({error:"Forbidden"},{status:403});let a=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(e),n=new d.W(e,a);return await n.deleteRule(r.id,o.id),i.NextResponse.json({message:"Rule deleted successfully"})}catch(e){return console.error("Error deleting escalation rule:",e),i.NextResponse.json({error:"Failed to delete escalation rule"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let f=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/[id]/route",pathname:"/api/workflows/escalation/[id]",filename:"route",bundlePath:"app/api/workflows/escalation/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:x,serverHooks:m}=f;function h(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(53680));module.exports=s})();