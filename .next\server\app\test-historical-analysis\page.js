(()=>{var e={};e.id=6251,e.ids=[6251],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},70694:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=t(70260),a=t(28203),n=t(25155),i=t.n(n),l=t(67292),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o=["",{children:["test-historical-analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,92079)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-historical-analysis\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-historical-analysis\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-historical-analysis/page",pathname:"/test-historical-analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9801:(e,s,t)=>{Promise.resolve().then(t.bind(t,92079))},51657:(e,s,t)=>{Promise.resolve().then(t.bind(t,80272))},80272:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(45512),a=t(58009),n=t(97643),i=t(87021),l=t(77252),c=t(25409),o=t(47699),d=t(4643),u=t(12214),m=t(80832);let p=(0,t(93939).createClient)("your_supabase_project_url","your_supabase_anon_key");class x{constructor(){this.cache=new Map,this.cacheTimeout=3e5}static getInstance(){return x.instance||(x.instance=new x),x.instance}async analyzeFormPatterns(e,s,t,r){try{let a=`${e}:${s}:${t||""}`,n=this.cache.get(a);if(n&&Date.now()-n.timestamp<this.cacheTimeout)return n.data;let{data:i,error:l}=await p.functions.invoke("analyze-form-patterns",{body:{departmentId:e,fieldId:s,currentValue:t,formContext:r,limit:20}});if(l)throw l;return i&&this.cache.set(a,{data:i,timestamp:Date.now()}),i}catch(e){return console.error("Pattern analysis error:",e),null}}async submitFeedback(e){try{let{data:s,error:t}=await p.functions.invoke("learn-from-feedback",{body:e});if(t)throw t;return s?.success||!1}catch(e){return console.error("Feedback submission error:",e),!1}}async getFieldSuggestions(e,s,t=5){try{let r=await this.analyzeFormPatterns(e,s);if(!r?.commonPatterns)return[];return r.commonPatterns.slice(0,t).map(e=>e.value)}catch(e){return console.error("Get suggestions error:",e),[]}}async recordFormSubmission(e,s,t,r){try{let{data:a,error:n}=await p.rpc("record_form_submission",{p_user_id:e,p_department_id:s,p_service_category_id:t,p_form_data:r});if(n)throw n;return a}catch(e){return console.error("Record submission error:",e),null}}async getFieldStatistics(e,s){try{let{data:t,error:r}=await p.from("field_usage_statistics").select("*").eq("department_id",e).eq("field_id",s).single();if(r&&"PGRST116"!==r.code)throw r;return t}catch(e){return console.error("Get field statistics error:",e),null}}clearCache(){this.cache.clear()}}let h=x.getInstance();var g=t(59462);function j({fieldId:e,label:s,labelJp:t,value:n,onChange:p,departmentId:x,placeholder:j,required:f,className:y}){let[b,v]=(0,a.useState)(null),[N,_]=(0,a.useState)(!1),[w,C]=(0,a.useState)(!1),P=s=>{p(s),C(!1),x&&h.submitFeedback({userId:"current-user",fieldId:e,originalValue:n,suggestedValue:s,wasAccepted:!0,finalValue:s,departmentId:x})},k=b?.commonPatterns&&b.commonPatterns.length>0;return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(o.J,{htmlFor:e,children:[t?`${t} / ${s}`:s,f&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.p,{id:e,value:n,onChange:e=>p(e.target.value),placeholder:j,required:f,className:(0,g.cn)(y,k&&"pr-10")}),k&&(0,r.jsx)("div",{className:"absolute right-2 top-1/2 -translate-y-1/2",children:(0,r.jsxs)(l.E,{variant:"secondary",className:"text-xs",children:[(0,r.jsx)(d.A,{className:"h-3 w-3 mr-1"}),b.commonPatterns[0].frequency]})})]}),b?.aiSuggestion&&b.aiSuggestion!==n&&(0,r.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("p",{className:"text-sm font-medium flex items-center gap-1 text-blue-700 dark:text-blue-300",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"AI提案 / AI Suggestion"]}),(0,r.jsx)("p",{className:"text-sm mt-1",children:b.aiSuggestion}),b.reasoning&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:b.reasoning})]}),(0,r.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>P(b.aiSuggestion),className:"text-blue-700 hover:text-blue-800",children:"使用 / Use"})]})}),w&&k&&(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-md p-3 space-y-2",children:[(0,r.jsxs)("p",{className:"text-sm font-medium flex items-center gap-1 text-gray-700 dark:text-gray-300",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),"よく使用される値 / Common Values"]}),(0,r.jsx)("div",{className:"space-y-1",children:b.commonPatterns.slice(0,3).map((e,s)=>(0,r.jsxs)("button",{onClick:()=>P(e.value),className:(0,g.cn)("w-full text-left p-2 rounded text-sm","hover:bg-gray-100 dark:hover:bg-gray-800","transition-colors cursor-pointer","flex items-center justify-between"),children:[(0,r.jsx)("span",{className:"truncate",children:e.value}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{children:[e.frequency,"回使用"]}),(0,r.jsxs)("span",{children:[Math.round(100*e.confidence),"%"]})]})]},s))})]}),N&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"履歴データを読み込み中... / Loading historical data..."})]})}var f=t(91542),y=t(78397),b=t(88976),v=t(22385);function N(){let[e,s]=(0,a.useState)({email:"",pc_id:"",reason:"",department:""}),[t,c]=(0,a.useState)(null),o=(e,t)=>{s(s=>({...s,[e]:t}))},d=async()=>{await h.recordFormSubmission("test-user-id","it-systems-dept-id","pc-admin-request",e)?(f.oR.success("データが記録されました / Data recorded successfully"),c(await h.getFieldStatistics("it-systems-dept-id","email"))):f.oR.error("記録に失敗しました / Failed to record data")};return(0,r.jsx)("div",{className:"container mx-auto p-6 max-w-6xl",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold flex items-center justify-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-8 w-8 text-purple-600"}),"履歴データ分析デモ"]}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Historical Data Analysis & Intelligent Suggestions"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 justify-center",children:[(0,r.jsxs)(l.E,{variant:"secondary",children:[(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"パターン分析 / Pattern Analysis"]}),(0,r.jsxs)(l.E,{variant:"secondary",children:[(0,r.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"AI学習 / AI Learning"]}),(0,r.jsxs)(l.E,{variant:"secondary",children:[(0,r.jsx)(b.A,{className:"h-3 w-3 mr-1"}),"履歴データ / Historical Data"]}),(0,r.jsx)(l.E,{variant:"secondary",children:"部門別提案 / Department-specific"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"フォーム入力 / Form Input"}),(0,r.jsxs)(n.BT,{children:["入力すると履歴データに基づいた提案が表示されます",(0,r.jsx)("br",{}),"Suggestions based on historical data will appear as you type"]})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsx)(j,{fieldId:"email",label:"Email Address",labelJp:"メールアドレス",value:e.email,onChange:e=>o("email",e),departmentId:"it-systems-dept-id",placeholder:"例: <EMAIL>",required:!0}),(0,r.jsx)(j,{fieldId:"pc_id",label:"PC ID",labelJp:"PC ID",value:e.pc_id,onChange:e=>o("pc_id",e),departmentId:"it-systems-dept-id",placeholder:"例: LT24123101",required:!0}),(0,r.jsx)(j,{fieldId:"reason",label:"Reason for Request",labelJp:"申請理由",value:e.reason,onChange:e=>o("reason",e),departmentId:"it-systems-dept-id",placeholder:"申請理由を入力してください",required:!0}),(0,r.jsx)(j,{fieldId:"department",label:"Department",labelJp:"部署",value:e.department,onChange:e=>o("department",e),departmentId:"it-systems-dept-id",placeholder:"例: ITシステム部",required:!0}),(0,r.jsx)(i.$,{onClick:d,className:"w-full",children:"送信してデータを記録 / Submit and Record Data"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"機能説明 / Feature Explanation"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"履歴パターン分析 / Historical Pattern Analysis"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"部門内で頻繁に使用される値を分析し、入力候補として表示します。使用頻度と信頼度スコアも表示されます。"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Analyzes frequently used values within the department and displays them as input suggestions with frequency and confidence scores."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"AI提案 / AI Suggestions"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"履歴データとコンテキストを考慮して、AIが最適な値を提案します。"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"AI suggests optimal values based on historical data and context."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"学習機能 / Learning Feature"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"提案の採用/却下がフィードバックとして記録され、今後の提案精度が向上します。"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Acceptance/rejection of suggestions is recorded as feedback to improve future suggestion accuracy."})]})]})]}),t&&(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"フィールド統計 / Field Statistics"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("pre",{className:"text-xs overflow-auto bg-gray-100 dark:bg-gray-900 p-3 rounded",children:JSON.stringify(t,null,2)})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"現在のフォームデータ / Current Form Data"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("pre",{className:"text-xs overflow-auto bg-gray-100 dark:bg-gray-900 p-3 rounded",children:JSON.stringify(e,null,2)})})]})]})]})]})})}},92079:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-historical-analysis\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-historical-analysis\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8096,2076],()=>t(70694));module.exports=r})();