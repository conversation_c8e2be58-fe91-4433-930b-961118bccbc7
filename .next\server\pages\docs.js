"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/docs";
exports.ids = ["pages/docs"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Cdocs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Cdocs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./node_modules/next/dist/pages/_app.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\docs.tsx */ \"./pages/docs.tsx\");\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\n// Import the app and document modules.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst PagesRouteModule = next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule;\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/docs\",\n        pathname: \"/docs\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: (private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default()),\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_docs_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Cdocs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./lib/api/api-documentation-page.tsx":
/*!********************************************!*\
  !*** ./lib/api/api-documentation-page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiDocumentationPage: () => (/* binding */ ApiDocumentationPage),\n/* harmony export */   ApiDocumentationViewType: () => (/* binding */ ApiDocumentationViewType)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _swagger_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./swagger-ui */ \"./lib/api/swagger-ui.tsx\");\n/* harmony import */ var _redoc_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./redoc-ui */ \"./lib/api/redoc-ui.tsx\");\n/**\r\n * API Documentation Page\r\n * \r\n * This component provides a unified interface for displaying API documentation\r\n * with support for switching between Swagger UI and ReDoc UI.\r\n * \r\n * NOTE: You need to install the following packages:\r\n * npm install swagger-ui-react redoc styled-components\r\n */ \n\n\n\nvar ApiDocumentationViewType;\n(function(ApiDocumentationViewType) {\n    ApiDocumentationViewType[\"SWAGGER\"] = \"swagger\";\n    ApiDocumentationViewType[\"REDOC\"] = \"redoc\";\n})(ApiDocumentationViewType || (ApiDocumentationViewType = {}));\n/**\r\n * Default styles\r\n */ const defaultStyles = {\n    container: {\n        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n        maxWidth: \"100%\",\n        margin: \"0 auto\",\n        padding: \"0\"\n    },\n    header: {\n        padding: \"1rem\",\n        borderBottom: \"1px solid #eee\",\n        marginBottom: \"1rem\"\n    },\n    title: {\n        fontSize: \"2rem\",\n        fontWeight: \"bold\",\n        margin: \"0 0 0.5rem 0\"\n    },\n    description: {\n        fontSize: \"1rem\",\n        margin: \"0 0 1rem 0\",\n        color: \"#666\"\n    },\n    viewSwitcher: {\n        display: \"flex\",\n        gap: \"0.5rem\",\n        marginBottom: \"1rem\"\n    },\n    viewSwitcherButton: {\n        padding: \"0.5rem 1rem\",\n        border: \"1px solid #ddd\",\n        borderRadius: \"4px\",\n        backgroundColor: \"#f5f5f5\",\n        cursor: \"pointer\",\n        fontSize: \"0.875rem\",\n        fontWeight: \"normal\"\n    },\n    viewSwitcherButtonActive: {\n        backgroundColor: \"#e0e0e0\",\n        fontWeight: \"bold\"\n    },\n    content: {\n        padding: \"0 1rem\"\n    },\n    loading: {\n        padding: \"2rem\",\n        textAlign: \"center\",\n        color: \"#666\"\n    },\n    error: {\n        padding: \"2rem\",\n        textAlign: \"center\",\n        color: \"#d32f2f\"\n    }\n};\n/**\r\n * API documentation page component\r\n */ const ApiDocumentationPage = ({ title = \"API Documentation\", description, apiUrl = \"/api/docs\", defaultViewType = \"swagger\", showViewSwitcher = true, styles = {}, classNames = {}, swaggerUIProps = {}, reDocUIProps = {} })=>{\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultViewType);\n    const [spec, setSpec] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Merge styles with defaults\n    const mergedStyles = {\n        container: {\n            ...defaultStyles.container,\n            ...styles.container\n        },\n        header: {\n            ...defaultStyles.header,\n            ...styles.header\n        },\n        title: {\n            ...defaultStyles.title,\n            ...styles.title\n        },\n        description: {\n            ...defaultStyles.description,\n            ...styles.description\n        },\n        viewSwitcher: {\n            ...defaultStyles.viewSwitcher,\n            ...styles.viewSwitcher\n        },\n        viewSwitcherButton: {\n            ...defaultStyles.viewSwitcherButton,\n            ...styles.viewSwitcherButton\n        },\n        viewSwitcherButtonActive: {\n            ...defaultStyles.viewSwitcherButtonActive,\n            ...styles.viewSwitcherButtonActive\n        },\n        content: {\n            ...defaultStyles.content,\n            ...styles.content\n        },\n        loading: {\n            ...defaultStyles.loading,\n            ...styles.loading\n        },\n        error: {\n            ...defaultStyles.error,\n            ...styles.error\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSpec = async ()=>{\n            try {\n                const response = await fetch(apiUrl);\n                if (!response.ok) {\n                    throw new Error(`Failed to fetch API documentation: ${response.statusText}`);\n                }\n                const data = await response.json();\n                setSpec(data);\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(String(err)));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSpec();\n    }, [\n        apiUrl\n    ]);\n    const handleViewTypeChange = (type)=>{\n        setViewType(type);\n    };\n    const renderViewSwitcher = ()=>{\n        if (!showViewSwitcher) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: mergedStyles.viewSwitcher,\n            className: classNames.viewSwitcher,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    style: {\n                        ...mergedStyles.viewSwitcherButton,\n                        ...viewType === \"swagger\" ? mergedStyles.viewSwitcherButtonActive : {}\n                    },\n                    className: `${classNames.viewSwitcherButton || \"\"} ${viewType === \"swagger\" ? classNames.viewSwitcherButtonActive || \"\" : \"\"}`,\n                    onClick: ()=>handleViewTypeChange(\"swagger\"),\n                    children: \"Swagger UI\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    style: {\n                        ...mergedStyles.viewSwitcherButton,\n                        ...viewType === \"redoc\" ? mergedStyles.viewSwitcherButtonActive : {}\n                    },\n                    className: `${classNames.viewSwitcherButton || \"\"} ${viewType === \"redoc\" ? classNames.viewSwitcherButtonActive || \"\" : \"\"}`,\n                    onClick: ()=>handleViewTypeChange(\"redoc\"),\n                    children: \"ReDoc\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n            lineNumber: 294,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderContent = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.loading,\n                className: classNames.loading,\n                children: \"Loading API documentation...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (error) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.error,\n                className: classNames.error,\n                children: [\n                    \"Error loading API documentation: \",\n                    error.message\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 340,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!spec) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.error,\n                className: classNames.error,\n                children: \"No API documentation available.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (viewType === \"swagger\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_swagger_ui__WEBPACK_IMPORTED_MODULE_2__.SwaggerUI, {\n                spec: spec,\n                ...swaggerUIProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 361,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_redoc_ui__WEBPACK_IMPORTED_MODULE_3__.ReDocUI, {\n            spec: spec,\n            ...reDocUIProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n            lineNumber: 364,\n            columnNumber: 12\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: mergedStyles.container,\n        className: classNames.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.header,\n                className: classNames.header,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: mergedStyles.title,\n                        className: classNames.title,\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: mergedStyles.description,\n                        className: classNames.description,\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, undefined),\n                    renderViewSwitcher()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.content,\n                className: classNames.content,\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * // pages/api/docs.ts\r\n * import { NextApiRequest, NextApiResponse } from 'next';\r\n * import { openApiMiddleware } from '../../lib/api/openapi-documentation';\r\n * \r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * \r\n * // pages/docs.tsx\r\n * import { ApiDocumentationPage, ApiDocumentationViewType } from '../../lib/api/api-documentation-page';\r\n * \r\n * export default function DocsPage() {\r\n *   return (\r\n *     <ApiDocumentationPage\r\n *       title=\"API Documentation\"\r\n *       description=\"Documentation for the My API\"\r\n *       apiUrl=\"/api/docs\"\r\n *       defaultViewType={ApiDocumentationViewType.SWAGGER}\r\n *       showViewSwitcher={true}\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api/api-documentation-page.tsx\n");

/***/ }),

/***/ "./lib/api/redoc-ui.tsx":
/*!******************************!*\
  !*** ./lib/api/redoc-ui.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiDocumentationPageReDoc: () => (/* binding */ ApiDocumentationPageReDoc),\n/* harmony export */   ReDocUI: () => (/* binding */ ReDocUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/**\r\n * ReDoc UI Component\r\n * \r\n * This component provides a ReDoc interface for displaying OpenAPI documentation.\r\n * It uses the redoc package to render the UI.\r\n * \r\n * NOTE: You need to install the redoc package:\r\n * npm install redoc styled-components\r\n */ \n\n\n// Dynamically import ReDoc to avoid SSR issues\n// NOTE: This requires the redoc package to be installed\nconst RedocStandalone = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! redoc */ \"redoc\", 23)).then((mod)=>mod.RedocStandalone), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\lib\\\\api\\\\redoc-ui.tsx -> \" + \"redoc\"\n        ]\n    },\n    ssr: false\n});\n/**\r\n * ReDoc UI component\r\n */ const ReDocUI = ({ spec, options = {}, style, className })=>{\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RedocStandalone, {\n            spec: spec,\n            options: {\n                hideHostname: false,\n                hideDownloadButton: false,\n                noAutoAuth: false,\n                disableSearch: false,\n                requiredPropsFirst: true,\n                sortPropsAlphabetically: false,\n                pathInMiddlePanel: false,\n                hideSchemaTitles: false,\n                disableStickySidebar: false,\n                ...options\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, undefined);\n};\n/**\r\n * API documentation page component using ReDoc\r\n */ const ApiDocumentationPageReDoc = ({ title = \"API Documentation\", description, apiUrl = \"/api/docs\" })=>{\n    const [spec, setSpec] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSpec = async ()=>{\n            try {\n                const response = await fetch(apiUrl);\n                if (!response.ok) {\n                    throw new Error(`Failed to fetch API documentation: ${response.statusText}`);\n                }\n                const data = await response.json();\n                setSpec(data);\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(String(err)));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSpec();\n    }, [\n        apiUrl\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-loading\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading API documentation...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Error loading API documentation: \",\n                        error.message\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!spec) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No API documentation available.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"api-docs\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReDocUI, {\n            spec: spec\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 358,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * // pages/api/docs.ts\r\n * import { NextApiRequest, NextApiResponse } from 'next';\r\n * import { openApiMiddleware } from '../../lib/api/openapi-documentation';\r\n * \r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * \r\n * // pages/docs.tsx\r\n * import { ApiDocumentationPageReDoc } from '../../lib/api/redoc-ui';\r\n * \r\n * export default function DocsPage() {\r\n *   return (\r\n *     <ApiDocumentationPageReDoc\r\n *       title=\"API Documentation\"\r\n *       description=\"Documentation for the My API\"\r\n *       apiUrl=\"/api/docs\"\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvYXBpL3JlZG9jLXVpLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBOzs7Ozs7OztDQVFDO0FBRWtEO0FBQ2hCO0FBR25DLCtDQUErQztBQUMvQyx3REFBd0Q7QUFDeEQsTUFBTUksa0JBQWtCRCxtREFBT0EsQ0FDN0IsSUFBTSxnSEFBTyxDQUFTRSxJQUFJLENBQUMsQ0FBQ0MsTUFBUUEsSUFBSUYsZUFBZTs7Ozs7O0lBQ3JERyxLQUFLOztBQXdPVDs7Q0FFQyxHQUNNLE1BQU1DLFVBQWtDLENBQUMsRUFDOUNDLElBQUksRUFDSkMsVUFBVSxDQUFDLENBQUMsRUFDWkMsS0FBSyxFQUNMQyxTQUFTLEVBQ1Y7SUFDQyxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1osK0NBQVFBLENBQUM7SUFFekNELGdEQUFTQSxDQUFDO1FBQ1JhLFlBQVk7SUFDZCxHQUFHLEVBQUU7SUFFTCxJQUFJLENBQUNELFVBQVU7UUFDYixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0U7UUFBSUosT0FBT0E7UUFBT0MsV0FBV0E7a0JBSzVCLDRFQUFDUjtZQUNDSyxNQUFNQTtZQUNOQyxTQUFTO2dCQUNQTSxjQUFjO2dCQUNkQyxvQkFBb0I7Z0JBQ3BCQyxZQUFZO2dCQUNaQyxlQUFlO2dCQUNmQyxvQkFBb0I7Z0JBQ3BCQyx5QkFBeUI7Z0JBQ3pCQyxtQkFBbUI7Z0JBQ25CQyxrQkFBa0I7Z0JBQ2xCQyxzQkFBc0I7Z0JBQ3RCLEdBQUdkLE9BQU87WUFDWjs7Ozs7Ozs7Ozs7QUFJUixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNZSw0QkFJUixDQUFDLEVBQUVDLFFBQVEsbUJBQW1CLEVBQUVDLFdBQVcsRUFBRUMsU0FBUyxXQUFXLEVBQUU7SUFDdEUsTUFBTSxDQUFDbkIsTUFBTW9CLFFBQVEsR0FBRzNCLCtDQUFRQSxDQUF5QjtJQUN6RCxNQUFNLENBQUM0QixTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM4QixPQUFPQyxTQUFTLEdBQUcvQiwrQ0FBUUEsQ0FBZTtJQUVqREQsZ0RBQVNBLENBQUM7UUFDUixNQUFNaUMsWUFBWTtZQUNoQixJQUFJO2dCQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTVI7Z0JBQzdCLElBQUksQ0FBQ08sU0FBU0UsRUFBRSxFQUFFO29CQUNoQixNQUFNLElBQUlDLE1BQU0sQ0FBQyxtQ0FBbUMsRUFBRUgsU0FBU0ksVUFBVSxDQUFDLENBQUM7Z0JBQzdFO2dCQUNBLE1BQU1DLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtnQkFDaENaLFFBQVFXO1lBQ1YsRUFBRSxPQUFPRSxLQUFLO2dCQUNaVCxTQUFTUyxlQUFlSixRQUFRSSxNQUFNLElBQUlKLE1BQU1LLE9BQU9EO1lBQ3pELFNBQVU7Z0JBQ1JYLFdBQVc7WUFDYjtRQUNGO1FBRUFHO0lBQ0YsR0FBRztRQUFDTjtLQUFPO0lBRVgsSUFBSUUsU0FBUztRQUNYLHFCQUNFLDhEQUFDZjtZQUFJSCxXQUFVOzs4QkFDYiw4REFBQ2dDOzhCQUFJbEI7Ozs7OztnQkFDSkMsNkJBQWUsOERBQUNrQjs4QkFBR2xCOzs7Ozs7OEJBQ3BCLDhEQUFDa0I7OEJBQUU7Ozs7Ozs7Ozs7OztJQUdUO0lBRUEsSUFBSWIsT0FBTztRQUNULHFCQUNFLDhEQUFDakI7WUFBSUgsV0FBVTs7OEJBQ2IsOERBQUNnQzs4QkFBSWxCOzs7Ozs7Z0JBQ0pDLDZCQUFlLDhEQUFDa0I7OEJBQUdsQjs7Ozs7OzhCQUNwQiw4REFBQ2tCOzt3QkFBRTt3QkFBa0NiLE1BQU1jLE9BQU87Ozs7Ozs7Ozs7Ozs7SUFHeEQ7SUFFQSxJQUFJLENBQUNyQyxNQUFNO1FBQ1QscUJBQ0UsOERBQUNNO1lBQUlILFdBQVU7OzhCQUNiLDhEQUFDZ0M7OEJBQUlsQjs7Ozs7O2dCQUNKQyw2QkFBZSw4REFBQ2tCOzhCQUFHbEI7Ozs7Ozs4QkFDcEIsOERBQUNrQjs4QkFBRTs7Ozs7Ozs7Ozs7O0lBR1Q7SUFFQSxxQkFDRSw4REFBQzlCO1FBQUlILFdBQVU7a0JBQ2IsNEVBQUNKO1lBQVFDLE1BQU1BOzs7Ozs7Ozs7OztBQUdyQixFQUFFLENBRUY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXVDQyIsInNvdXJjZXMiOlsid2VicGFjazovL2VudGVycHJpc2UtaXQtaGVscGRlc2svLi9saWIvYXBpL3JlZG9jLXVpLnRzeD8wNmI4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBSZURvYyBVSSBDb21wb25lbnRcclxuICogXHJcbiAqIFRoaXMgY29tcG9uZW50IHByb3ZpZGVzIGEgUmVEb2MgaW50ZXJmYWNlIGZvciBkaXNwbGF5aW5nIE9wZW5BUEkgZG9jdW1lbnRhdGlvbi5cclxuICogSXQgdXNlcyB0aGUgcmVkb2MgcGFja2FnZSB0byByZW5kZXIgdGhlIFVJLlxyXG4gKiBcclxuICogTk9URTogWW91IG5lZWQgdG8gaW5zdGFsbCB0aGUgcmVkb2MgcGFja2FnZTpcclxuICogbnBtIGluc3RhbGwgcmVkb2Mgc3R5bGVkLWNvbXBvbmVudHNcclxuICovXHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJztcclxuaW1wb3J0IHsgT3BlbkFQSURvY3VtZW50IH0gZnJvbSAnLi9vcGVuYXBpLWRvY3VtZW50YXRpb24nO1xyXG5cclxuLy8gRHluYW1pY2FsbHkgaW1wb3J0IFJlRG9jIHRvIGF2b2lkIFNTUiBpc3N1ZXNcclxuLy8gTk9URTogVGhpcyByZXF1aXJlcyB0aGUgcmVkb2MgcGFja2FnZSB0byBiZSBpbnN0YWxsZWRcclxuY29uc3QgUmVkb2NTdGFuZGFsb25lID0gZHluYW1pYyhcclxuICAoKSA9PiBpbXBvcnQoJ3JlZG9jJykudGhlbigobW9kKSA9PiBtb2QuUmVkb2NTdGFuZGFsb25lKSxcclxuICB7IHNzcjogZmFsc2UgfVxyXG4pO1xyXG5cclxuLyoqXHJcbiAqIFJlRG9jIFVJIHByb3BzXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIFJlRG9jVUlQcm9wcyB7XHJcbiAgLyoqXHJcbiAgICogT3BlbkFQSSBkb2N1bWVudCBvciBVUkwgdG8gT3BlbkFQSSBkb2N1bWVudFxyXG4gICAqL1xyXG4gIHNwZWM6IE9wZW5BUElEb2N1bWVudCB8IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogT3B0aW9ucyBmb3IgUmVEb2NcclxuICAgKi9cclxuICBvcHRpb25zPzoge1xyXG4gICAgLyoqXHJcbiAgICAgKiBIaWRlIGhvc3RuYW1lIGluIG1ldGhvZCBkZWZpbml0aW9uXHJcbiAgICAgKi9cclxuICAgIGhpZGVIb3N0bmFtZT86IGJvb2xlYW47XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBIaWRlIGRvd25sb2FkIGJ1dHRvblxyXG4gICAgICovXHJcbiAgICBoaWRlRG93bmxvYWRCdXR0b24/OiBib29sZWFuO1xyXG5cclxuICAgIC8qKlxyXG4gICAgICogRG9uJ3QgZGlzcGxheSBzY2hlbWEgdGl0bGUgaW4gdGhlIGRlZmluaXRpb25cclxuICAgICAqL1xyXG4gICAgbm9BdXRvQXV0aD86IGJvb2xlYW47XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBEaXNhYmxlIHNlYXJjaCBpbmRleGluZyBhbmQgc2VhcmNoIGJveFxyXG4gICAgICovXHJcbiAgICBkaXNhYmxlU2VhcmNoPzogYm9vbGVhbjtcclxuXHJcbiAgICAvKipcclxuICAgICAqIFNob3cgb25seSByZXF1aXJlZCBmaWVsZHMgaW4gcmVxdWVzdCBzY2hlbWFzXHJcbiAgICAgKi9cclxuICAgIHJlcXVpcmVkUHJvcHNGaXJzdD86IGJvb2xlYW47XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBTb3J0IHByb3BlcnRpZXMgYWxwaGFiZXRpY2FsbHlcclxuICAgICAqL1xyXG4gICAgc29ydFByb3BzQWxwaGFiZXRpY2FsbHk/OiBib29sZWFuO1xyXG5cclxuICAgIC8qKlxyXG4gICAgICogU2hvdyBwYXRoIGluIG9wZXJhdGlvbiBpZFxyXG4gICAgICovXHJcbiAgICBwYXRoSW5NaWRkbGVQYW5lbD86IGJvb2xlYW47XHJcblxyXG4gICAgLyoqXHJcbiAgICAgKiBIaWRlIHJlcXVlc3Qgc2FtcGxlIHRhYlxyXG4gICAgICovXHJcbiAgICBoaWRlU2NoZW1hVGl0bGVzPzogYm9vbGVhbjtcclxuXHJcbiAgICAvKipcclxuICAgICAqIERpc2FibGUgc3RpY2t5IHNpZGViYXJcclxuICAgICAqL1xyXG4gICAgZGlzYWJsZVN0aWNreVNpZGViYXI/OiBib29sZWFuO1xyXG5cclxuICAgIC8qKlxyXG4gICAgICogR2VuZXJhdGUgdW5pcXVlIG9wZXJhdGlvbiBJRHNcclxuICAgICAqL1xyXG4gICAgZ2VuZXJhdGVDb2RlU2FtcGxlcz86IHtcclxuICAgICAgbGFuZ3VhZ2VzOiBBcnJheTx7XHJcbiAgICAgICAgbGFuZzogc3RyaW5nO1xyXG4gICAgICAgIGxhYmVsOiBzdHJpbmc7XHJcbiAgICAgICAgc291cmNlOiAob3BlcmF0aW9uOiBhbnkpID0+IHN0cmluZztcclxuICAgICAgfT47XHJcbiAgICB9O1xyXG5cclxuICAgIC8qKlxyXG4gICAgICogVGhlbWVcclxuICAgICAqL1xyXG4gICAgdGhlbWU/OiB7XHJcbiAgICAgIHNwYWNpbmc/OiB7XHJcbiAgICAgICAgdW5pdD86IG51bWJlcjtcclxuICAgICAgICBzZWN0aW9uSG9yaXpvbnRhbD86IG51bWJlcjtcclxuICAgICAgICBzZWN0aW9uVmVydGljYWw/OiBudW1iZXI7XHJcbiAgICAgIH07XHJcbiAgICAgIGJyZWFrcG9pbnRzPzoge1xyXG4gICAgICAgIHNtYWxsPzogc3RyaW5nO1xyXG4gICAgICAgIG1lZGl1bT86IHN0cmluZztcclxuICAgICAgICBsYXJnZT86IHN0cmluZztcclxuICAgICAgfTtcclxuICAgICAgY29sb3JzPzoge1xyXG4gICAgICAgIHByaW1hcnk/OiB7XHJcbiAgICAgICAgICBtYWluPzogc3RyaW5nO1xyXG4gICAgICAgICAgbGlnaHQ/OiBzdHJpbmc7XHJcbiAgICAgICAgICBkYXJrPzogc3RyaW5nO1xyXG4gICAgICAgICAgY29udHJhc3RUZXh0Pzogc3RyaW5nO1xyXG4gICAgICAgIH07XHJcbiAgICAgICAgc3VjY2Vzcz86IHtcclxuICAgICAgICAgIG1haW4/OiBzdHJpbmc7XHJcbiAgICAgICAgICBsaWdodD86IHN0cmluZztcclxuICAgICAgICAgIGRhcms/OiBzdHJpbmc7XHJcbiAgICAgICAgICBjb250cmFzdFRleHQ/OiBzdHJpbmc7XHJcbiAgICAgICAgfTtcclxuICAgICAgICB3YXJuaW5nPzoge1xyXG4gICAgICAgICAgbWFpbj86IHN0cmluZztcclxuICAgICAgICAgIGxpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgICAgZGFyaz86IHN0cmluZztcclxuICAgICAgICAgIGNvbnRyYXN0VGV4dD86IHN0cmluZztcclxuICAgICAgICB9O1xyXG4gICAgICAgIGVycm9yPzoge1xyXG4gICAgICAgICAgbWFpbj86IHN0cmluZztcclxuICAgICAgICAgIGxpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgICAgZGFyaz86IHN0cmluZztcclxuICAgICAgICAgIGNvbnRyYXN0VGV4dD86IHN0cmluZztcclxuICAgICAgICB9O1xyXG4gICAgICAgIGdyYXk/OiB7XHJcbiAgICAgICAgICA1MD86IHN0cmluZztcclxuICAgICAgICAgIDEwMD86IHN0cmluZztcclxuICAgICAgICAgIDIwMD86IHN0cmluZztcclxuICAgICAgICAgIDMwMD86IHN0cmluZztcclxuICAgICAgICAgIDQwMD86IHN0cmluZztcclxuICAgICAgICAgIDUwMD86IHN0cmluZztcclxuICAgICAgICAgIDYwMD86IHN0cmluZztcclxuICAgICAgICAgIDcwMD86IHN0cmluZztcclxuICAgICAgICAgIDgwMD86IHN0cmluZztcclxuICAgICAgICAgIDkwMD86IHN0cmluZztcclxuICAgICAgICB9O1xyXG4gICAgICAgIHRleHQ/OiB7XHJcbiAgICAgICAgICBwcmltYXJ5Pzogc3RyaW5nO1xyXG4gICAgICAgICAgc2Vjb25kYXJ5Pzogc3RyaW5nO1xyXG4gICAgICAgIH07XHJcbiAgICAgICAgYm9yZGVyPzoge1xyXG4gICAgICAgICAgZGFyaz86IHN0cmluZztcclxuICAgICAgICAgIGxpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgIH07XHJcbiAgICAgICAgcmVzcG9uc2VzPzoge1xyXG4gICAgICAgICAgc3VjY2Vzcz86IHtcclxuICAgICAgICAgICAgY29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj86IHN0cmluZztcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgICBlcnJvcj86IHtcclxuICAgICAgICAgICAgY29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj86IHN0cmluZztcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgICByZWRpcmVjdD86IHtcclxuICAgICAgICAgICAgY29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj86IHN0cmluZztcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgICBpbmZvPzoge1xyXG4gICAgICAgICAgICBjb2xvcj86IHN0cmluZztcclxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9O1xyXG4gICAgICAgIGh0dHA/OiB7XHJcbiAgICAgICAgICBnZXQ/OiBzdHJpbmc7XHJcbiAgICAgICAgICBwb3N0Pzogc3RyaW5nO1xyXG4gICAgICAgICAgcHV0Pzogc3RyaW5nO1xyXG4gICAgICAgICAgb3B0aW9ucz86IHN0cmluZztcclxuICAgICAgICAgIHBhdGNoPzogc3RyaW5nO1xyXG4gICAgICAgICAgZGVsZXRlPzogc3RyaW5nO1xyXG4gICAgICAgICAgYmFzaWM/OiBzdHJpbmc7XHJcbiAgICAgICAgICBsaW5rPzogc3RyaW5nO1xyXG4gICAgICAgICAgaGVhZD86IHN0cmluZztcclxuICAgICAgICB9O1xyXG4gICAgICB9O1xyXG4gICAgICB0eXBvZ3JhcGh5Pzoge1xyXG4gICAgICAgIGZvbnRTaXplPzogc3RyaW5nO1xyXG4gICAgICAgIGxpbmVIZWlnaHQ/OiBzdHJpbmc7XHJcbiAgICAgICAgZm9udFdlaWdodFJlZ3VsYXI/OiBzdHJpbmc7XHJcbiAgICAgICAgZm9udFdlaWdodEJvbGQ/OiBzdHJpbmc7XHJcbiAgICAgICAgZm9udFdlaWdodExpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgIGZvbnRGYW1pbHk/OiBzdHJpbmc7XHJcbiAgICAgICAgc21vb3RoaW5nPzogc3RyaW5nO1xyXG4gICAgICAgIG9wdGltaXplU3BlZWQ/OiBib29sZWFuO1xyXG4gICAgICAgIGhlYWRpbmdzPzoge1xyXG4gICAgICAgICAgZm9udEZhbWlseT86IHN0cmluZztcclxuICAgICAgICAgIGZvbnRXZWlnaHQ/OiBzdHJpbmc7XHJcbiAgICAgICAgICBsaW5lSGVpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29kZT86IHtcclxuICAgICAgICAgIGZvbnRTaXplPzogc3RyaW5nO1xyXG4gICAgICAgICAgZm9udEZhbWlseT86IHN0cmluZztcclxuICAgICAgICAgIGxpbmVIZWlnaHQ/OiBzdHJpbmc7XHJcbiAgICAgICAgICBmb250V2VpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgICAgY29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgICB3cmFwPzogYm9vbGVhbjtcclxuICAgICAgICB9O1xyXG4gICAgICAgIGxpbmtzPzoge1xyXG4gICAgICAgICAgY29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgICB2aXNpdGVkPzogc3RyaW5nO1xyXG4gICAgICAgICAgaG92ZXI/OiBzdHJpbmc7XHJcbiAgICAgICAgICB0ZXh0RGVjb3JhdGlvbj86IHN0cmluZztcclxuICAgICAgICAgIGhvdmVyVGV4dERlY29yYXRpb24/OiBzdHJpbmc7XHJcbiAgICAgICAgfTtcclxuICAgICAgfTtcclxuICAgICAgc2lkZWJhcj86IHtcclxuICAgICAgICB3aWR0aD86IHN0cmluZztcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgdGV4dENvbG9yPzogc3RyaW5nO1xyXG4gICAgICAgIGFjdGl2ZVRleHRDb2xvcj86IHN0cmluZztcclxuICAgICAgICBncm91cEl0ZW1zPzoge1xyXG4gICAgICAgICAgdGV4dFRyYW5zZm9ybT86IHN0cmluZztcclxuICAgICAgICB9O1xyXG4gICAgICAgIGxldmVsMUl0ZW1zPzoge1xyXG4gICAgICAgICAgdGV4dFRyYW5zZm9ybT86IHN0cmluZztcclxuICAgICAgICB9O1xyXG4gICAgICAgIGFycm93Pzoge1xyXG4gICAgICAgICAgc2l6ZT86IHN0cmluZztcclxuICAgICAgICAgIGNvbG9yPzogc3RyaW5nO1xyXG4gICAgICAgIH07XHJcbiAgICAgIH07XHJcbiAgICAgIGxvZ28/OiB7XHJcbiAgICAgICAgbWF4SGVpZ2h0Pzogc3RyaW5nO1xyXG4gICAgICAgIG1heFdpZHRoPzogc3RyaW5nO1xyXG4gICAgICAgIGd1dHRlcj86IHN0cmluZztcclxuICAgICAgfTtcclxuICAgICAgcmlnaHRQYW5lbD86IHtcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I/OiBzdHJpbmc7XHJcbiAgICAgICAgd2lkdGg/OiBzdHJpbmc7XHJcbiAgICAgICAgdGV4dENvbG9yPzogc3RyaW5nO1xyXG4gICAgICB9O1xyXG4gICAgfTtcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBDdXN0b20gc3R5bGVzIHRvIGFwcGx5IHRvIFJlRG9jXHJcbiAgICovXHJcbiAgc3R5bGU/OiBSZWFjdC5DU1NQcm9wZXJ0aWVzO1xyXG5cclxuICAvKipcclxuICAgKiBDdXN0b20gY2xhc3MgbmFtZSB0byBhcHBseSB0byBSZURvY1xyXG4gICAqL1xyXG4gIGNsYXNzTmFtZT86IHN0cmluZztcclxufVxyXG5cclxuLyoqXHJcbiAqIFJlRG9jIFVJIGNvbXBvbmVudFxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IFJlRG9jVUk6IFJlYWN0LkZDPFJlRG9jVUlQcm9wcz4gPSAoe1xyXG4gIHNwZWMsXHJcbiAgb3B0aW9ucyA9IHt9LFxyXG4gIHN0eWxlLFxyXG4gIGNsYXNzTmFtZVxyXG59KSA9PiB7XHJcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRJc0NsaWVudCh0cnVlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGlmICghaXNDbGllbnQpIHtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgc3R5bGU9e3N0eWxlfSBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XHJcbiAgICAgIHsvKiBcclxuICAgICAgICBOT1RFOiBUaGUgUmVkb2NTdGFuZGFsb25lIGNvbXBvbmVudCBleHBlY3RzIGEgJ3NwZWMnIHByb3AgYW5kIG90aGVyIGNvbmZpZ3VyYXRpb24gb3B0aW9ucy5cclxuICAgICAgICBUeXBlU2NyaXB0IGVycm9ycyB3aWxsIGJlIHJlc29sdmVkIG9uY2UgdGhlIHJlZG9jIHBhY2thZ2UgaXMgaW5zdGFsbGVkLlxyXG4gICAgICAqL31cclxuICAgICAgPFJlZG9jU3RhbmRhbG9uZVxyXG4gICAgICAgIHNwZWM9e3NwZWN9XHJcbiAgICAgICAgb3B0aW9ucz17e1xyXG4gICAgICAgICAgaGlkZUhvc3RuYW1lOiBmYWxzZSxcclxuICAgICAgICAgIGhpZGVEb3dubG9hZEJ1dHRvbjogZmFsc2UsXHJcbiAgICAgICAgICBub0F1dG9BdXRoOiBmYWxzZSxcclxuICAgICAgICAgIGRpc2FibGVTZWFyY2g6IGZhbHNlLFxyXG4gICAgICAgICAgcmVxdWlyZWRQcm9wc0ZpcnN0OiB0cnVlLFxyXG4gICAgICAgICAgc29ydFByb3BzQWxwaGFiZXRpY2FsbHk6IGZhbHNlLFxyXG4gICAgICAgICAgcGF0aEluTWlkZGxlUGFuZWw6IGZhbHNlLFxyXG4gICAgICAgICAgaGlkZVNjaGVtYVRpdGxlczogZmFsc2UsXHJcbiAgICAgICAgICBkaXNhYmxlU3RpY2t5U2lkZWJhcjogZmFsc2UsXHJcbiAgICAgICAgICAuLi5vcHRpb25zXHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG4vKipcclxuICogQVBJIGRvY3VtZW50YXRpb24gcGFnZSBjb21wb25lbnQgdXNpbmcgUmVEb2NcclxuICovXHJcbmV4cG9ydCBjb25zdCBBcGlEb2N1bWVudGF0aW9uUGFnZVJlRG9jOiBSZWFjdC5GQzx7XHJcbiAgdGl0bGU/OiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgYXBpVXJsPzogc3RyaW5nO1xyXG59PiA9ICh7IHRpdGxlID0gJ0FQSSBEb2N1bWVudGF0aW9uJywgZGVzY3JpcHRpb24sIGFwaVVybCA9ICcvYXBpL2RvY3MnIH0pID0+IHtcclxuICBjb25zdCBbc3BlYywgc2V0U3BlY10gPSB1c2VTdGF0ZTxPcGVuQVBJRG9jdW1lbnQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPEVycm9yIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaFNwZWMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChhcGlVcmwpO1xyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIEFQSSBkb2N1bWVudGF0aW9uOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0U3BlYyhkYXRhKTtcclxuICAgICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIgOiBuZXcgRXJyb3IoU3RyaW5nKGVycikpKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBmZXRjaFNwZWMoKTtcclxuICB9LCBbYXBpVXJsXSk7XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFwaS1kb2NzLWxvYWRpbmdcIj5cclxuICAgICAgICA8aDE+e3RpdGxlfTwvaDE+XHJcbiAgICAgICAge2Rlc2NyaXB0aW9uICYmIDxwPntkZXNjcmlwdGlvbn08L3A+fVxyXG4gICAgICAgIDxwPkxvYWRpbmcgQVBJIGRvY3VtZW50YXRpb24uLi48L3A+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIGlmIChlcnJvcikge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhcGktZG9jcy1lcnJvclwiPlxyXG4gICAgICAgIDxoMT57dGl0bGV9PC9oMT5cclxuICAgICAgICB7ZGVzY3JpcHRpb24gJiYgPHA+e2Rlc2NyaXB0aW9ufTwvcD59XHJcbiAgICAgICAgPHA+RXJyb3IgbG9hZGluZyBBUEkgZG9jdW1lbnRhdGlvbjoge2Vycm9yLm1lc3NhZ2V9PC9wPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoIXNwZWMpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXBpLWRvY3MtZXJyb3JcIj5cclxuICAgICAgICA8aDE+e3RpdGxlfTwvaDE+XHJcbiAgICAgICAge2Rlc2NyaXB0aW9uICYmIDxwPntkZXNjcmlwdGlvbn08L3A+fVxyXG4gICAgICAgIDxwPk5vIEFQSSBkb2N1bWVudGF0aW9uIGF2YWlsYWJsZS48L3A+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImFwaS1kb2NzXCI+XHJcbiAgICAgIDxSZURvY1VJIHNwZWM9e3NwZWN9IC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuLyoqXHJcbiAqIEV4YW1wbGUgdXNhZ2U6XHJcbiAqIFxyXG4gKiBgYGB0c3hcclxuICogLy8gcGFnZXMvYXBpL2RvY3MudHNcclxuICogaW1wb3J0IHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xyXG4gKiBpbXBvcnQgeyBvcGVuQXBpTWlkZGxld2FyZSB9IGZyb20gJy4uLy4uL2xpYi9hcGkvb3BlbmFwaS1kb2N1bWVudGF0aW9uJztcclxuICogXHJcbiAqIGV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGhhbmRsZXIocmVxOiBOZXh0QXBpUmVxdWVzdCwgcmVzOiBOZXh0QXBpUmVzcG9uc2UpIHtcclxuICogICByZXR1cm4gb3BlbkFwaU1pZGRsZXdhcmUoe1xyXG4gKiAgICAgdGl0bGU6ICdNeSBBUEknLFxyXG4gKiAgICAgZGVzY3JpcHRpb246ICdBUEkgZG9jdW1lbnRhdGlvbicsXHJcbiAqICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxyXG4gKiAgICAgc2VydmVyczogW1xyXG4gKiAgICAgICB7XHJcbiAqICAgICAgICAgdXJsOiAnaHR0cHM6Ly9hcGkuZXhhbXBsZS5jb20nLFxyXG4gKiAgICAgICAgIGRlc2NyaXB0aW9uOiAnUHJvZHVjdGlvbiBzZXJ2ZXInXHJcbiAqICAgICAgIH0sXHJcbiAqICAgICAgIHtcclxuICogICAgICAgICB1cmw6ICdodHRwczovL3N0YWdpbmcuZXhhbXBsZS5jb20nLFxyXG4gKiAgICAgICAgIGRlc2NyaXB0aW9uOiAnU3RhZ2luZyBzZXJ2ZXInXHJcbiAqICAgICAgIH1cclxuICogICAgIF1cclxuICogICB9KShyZXEsIHJlcyk7XHJcbiAqIH1cclxuICogXHJcbiAqIC8vIHBhZ2VzL2RvY3MudHN4XHJcbiAqIGltcG9ydCB7IEFwaURvY3VtZW50YXRpb25QYWdlUmVEb2MgfSBmcm9tICcuLi8uLi9saWIvYXBpL3JlZG9jLXVpJztcclxuICogXHJcbiAqIGV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3NQYWdlKCkge1xyXG4gKiAgIHJldHVybiAoXHJcbiAqICAgICA8QXBpRG9jdW1lbnRhdGlvblBhZ2VSZURvY1xyXG4gKiAgICAgICB0aXRsZT1cIkFQSSBEb2N1bWVudGF0aW9uXCJcclxuICogICAgICAgZGVzY3JpcHRpb249XCJEb2N1bWVudGF0aW9uIGZvciB0aGUgTXkgQVBJXCJcclxuICogICAgICAgYXBpVXJsPVwiL2FwaS9kb2NzXCJcclxuICogICAgIC8+XHJcbiAqICAgKTtcclxuICogfVxyXG4gKiBgYGBcclxuICovIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJkeW5hbWljIiwiUmVkb2NTdGFuZGFsb25lIiwidGhlbiIsIm1vZCIsInNzciIsIlJlRG9jVUkiLCJzcGVjIiwib3B0aW9ucyIsInN0eWxlIiwiY2xhc3NOYW1lIiwiaXNDbGllbnQiLCJzZXRJc0NsaWVudCIsImRpdiIsImhpZGVIb3N0bmFtZSIsImhpZGVEb3dubG9hZEJ1dHRvbiIsIm5vQXV0b0F1dGgiLCJkaXNhYmxlU2VhcmNoIiwicmVxdWlyZWRQcm9wc0ZpcnN0Iiwic29ydFByb3BzQWxwaGFiZXRpY2FsbHkiLCJwYXRoSW5NaWRkbGVQYW5lbCIsImhpZGVTY2hlbWFUaXRsZXMiLCJkaXNhYmxlU3RpY2t5U2lkZWJhciIsIkFwaURvY3VtZW50YXRpb25QYWdlUmVEb2MiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiYXBpVXJsIiwic2V0U3BlYyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImZldGNoU3BlYyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwic3RhdHVzVGV4dCIsImRhdGEiLCJqc29uIiwiZXJyIiwiU3RyaW5nIiwiaDEiLCJwIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/api/redoc-ui.tsx\n");

/***/ }),

/***/ "./lib/api/swagger-ui.tsx":
/*!********************************!*\
  !*** ./lib/api/swagger-ui.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiDocumentationPage: () => (/* binding */ ApiDocumentationPage),\n/* harmony export */   SwaggerUI: () => (/* binding */ SwaggerUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/**\r\n * Swagger UI Component\r\n *\r\n * This component provides a Swagger UI interface for displaying OpenAPI documentation.\r\n * It uses the swagger-ui-react package to render the UI.\r\n *\r\n * NOTE: You need to install the swagger-ui-react package:\r\n * npm install swagger-ui-react\r\n */ \n\n\n// Dynamically import swagger-ui-react to avoid SSR issues\n// NOTE: This requires the swagger-ui-react package to be installed\nconst SwaggerUIReact = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! swagger-ui-react */ \"swagger-ui-react\", 23)).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\lib\\\\api\\\\swagger-ui.tsx -> \" + \"swagger-ui-react\"\n        ]\n    },\n    ssr: false\n});\n// Dynamically import swagger-ui styles\n// NOTE: This requires the swagger-ui-react package to be installed\nconst SwaggerUIStyles = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        __webpack_require__.e(/*! import() */ \"vendor-chunks/swagger-ui-react\").then(__webpack_require__.t.bind(__webpack_require__, /*! swagger-ui-react/swagger-ui.css */ \"./node_modules/swagger-ui-react/swagger-ui.css\", 23));\n    }, []);\n    return null;\n};\n/**\r\n * Swagger UI component\r\n */ const SwaggerUI = ({ spec, showTopBar = true, showModels = true, showExamples = true, showTryItOut = true, ...props })=>{\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwaggerUIStyles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwaggerUIReact, {\n                spec: spec,\n                docExpansion: \"list\",\n                defaultModelsExpandDepth: showModels ? 1 : -1,\n                displayRequestDuration: true,\n                filter: true,\n                showExtensions: true,\n                showCommonExtensions: true,\n                deepLinking: true,\n                persistAuthorization: true,\n                withCredentials: true,\n                supportedSubmitMethods: showTryItOut ? [\n                    \"get\",\n                    \"post\",\n                    \"put\",\n                    \"delete\",\n                    \"patch\",\n                    \"options\",\n                    \"head\"\n                ] : [],\n                tagsSorter: \"alpha\",\n                operationsSorter: \"alpha\",\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/**\r\n * API documentation page component\r\n */ const ApiDocumentationPage = ({ title = \"API Documentation\", description, apiUrl = \"/api/docs\" })=>{\n    const [spec, setSpec] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSpec = async ()=>{\n            try {\n                const response = await fetch(apiUrl);\n                if (!response.ok) {\n                    throw new Error(`Failed to fetch API documentation: ${response.statusText}`);\n                }\n                const data = await response.json();\n                setSpec(data);\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(String(err)));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSpec();\n    }, [\n        apiUrl\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-loading\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading API documentation...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n            lineNumber: 456,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Error loading API documentation: \",\n                        error.message\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n            lineNumber: 466,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!spec) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No API documentation available.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"api-docs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, undefined),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 487,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwaggerUI, {\n                spec: spec\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * // pages/api/docs.ts\r\n * import { NextApiRequest, NextApiResponse } from 'next';\r\n * import { openApiMiddleware } from '../../lib/api/openapi-documentation';\r\n * \r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * \r\n * // pages/docs.tsx\r\n * import { ApiDocumentationPage } from '../../lib/api/swagger-ui';\r\n * \r\n * export default function DocsPage() {\r\n *   return (\r\n *     <ApiDocumentationPage\r\n *       title=\"API Documentation\"\r\n *       description=\"Documentation for the My API\"\r\n *       apiUrl=\"/api/docs\"\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api/swagger-ui.tsx\n");

/***/ }),

/***/ "./pages/docs.tsx":
/*!************************!*\
  !*** ./pages/docs.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_api_documentation_page__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/api/api-documentation-page */ \"./lib/api/api-documentation-page.tsx\");\n\n\n\n/**\r\n * API Documentation Page\r\n * \r\n * This page displays the API documentation using both Swagger UI and ReDoc.\r\n */ function DocsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_api_api_documentation_page__WEBPACK_IMPORTED_MODULE_2__.ApiDocumentationPage, {\n        title: \"Enterprise IT Helpdesk API Documentation\",\n        description: \"Comprehensive documentation for the Enterprise IT Helpdesk API\",\n        apiUrl: \"/api/docs\",\n        defaultViewType: _lib_api_api_documentation_page__WEBPACK_IMPORTED_MODULE_2__.ApiDocumentationViewType.SWAGGER,\n        showViewSwitcher: true,\n        styles: {\n            container: {\n                maxWidth: \"1200px\",\n                margin: \"0 auto\",\n                padding: \"2rem 1rem\"\n            },\n            header: {\n                marginBottom: \"2rem\",\n                borderBottom: \"1px solid #eee\",\n                paddingBottom: \"1rem\"\n            },\n            title: {\n                fontSize: \"2.5rem\",\n                color: \"#333\"\n            },\n            description: {\n                fontSize: \"1.2rem\",\n                color: \"#666\"\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\docs.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/docs.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "redoc":
/*!************************!*\
  !*** external "redoc" ***!
  \************************/
/***/ ((module) => {

module.exports = require("redoc");

/***/ }),

/***/ "swagger-ui-react":
/*!***********************************!*\
  !*** external "swagger-ui-react" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("swagger-ui-react");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdocs&preferredRegion=&absolutePagePath=.%2Fpages%5Cdocs.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();