 justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              コンプライアンス率 / Compliance Rate
            </CardTitle>
            {complianceTrend === 'excellent' ? 
              <TrendingUp className="h-4 w-4 text-green-600" /> :
              complianceTrend === 'critical' ?
              <TrendingDown className="h-4 w-4 text-red-600" /> :
              <BarChart3 className="h-4 w-4 text-orange-500" />
            }
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.compliance_rate.toFixed(1)}%</div>
            <Progress 
              value={metrics.compliance_rate} 
              className="mt-2"
              indicatorClassName={
                complianceTrend === 'excellent' ? 'bg-green-600' :
                complianceTrend === 'critical' ? 'bg-red-600' :
                'bg-orange-500'
              }
            />
            <p className="text-xs text-muted-foreground mt-2">
              {metrics.met}/{metrics.total} 達成 / Met
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              平均応答時間 / Avg Response Time
            </CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatTime(metrics.average_response_time)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              平均解決時間 / Avg Resolution Time
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatTime(metrics.average_resolution_time)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              リスクあり / At Risk
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.at_risk}</div>
            <p className="text-xs text-muted-foreground mt-2">
              違反: {metrics.breached} / Breached: {metrics.breached}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed View */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概要 / Overview</TabsTrigger>
          <TabsTrigger value="priority">優先度別 / By Priority</TabsTrigger>
          <TabsTrigger value="at-risk">リスクあり / At Risk</TabsTrigger>
          <TabsTrigger value="breaches">違反 / Breaches</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SLA パフォーマンス概要 / SLA Performance Overview</CardTitle>
              <CardDescription>
                期間: {dateRange === '24h' ? '過去24時間' : dateRange === '7d' ? '過去7日間' : '過去30日間'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span>達成 / Met</span>
                  </div>
                  <div className="text-right">
                    <span className="font-bold">{metrics.met}</span>
                    <span className="text-muted-foreground ml-2">
                      ({((metrics.met / metrics.total) * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                    <span>リスクあり / At Risk</span>
                  </div>
                  <div className="text-right">
                    <span className="font-bold">{metrics.at_risk}</span>
                    <span className="text-muted-foreground ml-2">
                      ({((metrics.at_risk / metrics.total) * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <XCircle className="h-5 w-5 text-red-600" />
                    <span>違反 / Breached</span>
                  </div>
                  <div className="text-right">
                    <span className="font-bold">{metrics.breached}</span>
                    <span className="text-muted-foreground ml-2">
                      ({((metrics.breached / metrics.total) * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="priority" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>優先度別SLAパフォーマンス / SLA Performance by Priority</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(metrics.by_priority).map(([priority, data]) => (
                  <div key={priority} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant={
                          priority === 'critical' ? 'destructive' :
                          priority === 'high' ? 'default' :
                          priority === 'medium' ? 'secondary' : 'outline'
                        }>
                          {priority.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          合計: {data.total}
                        </span>
                      </div>
                      <span className="font-medium">
                        {((data.met / data.total) * 100).toFixed(1)}% 達成
                      </span>
                    </div>
                    <Progress 
                      value={(data.met / data.total) * 100} 
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>達成: {data.met}</span>
                      <span>リスク: {data.at_risk}</span>
                      <span>違反: {data.breached}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="at-risk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>リスクありの項目 / At-Risk Items</CardTitle>
              <CardDescription>
                SLA違反のリスクがある項目 / Items at risk of SLA breach
              </CardDescription>
            </CardHeader>
            <CardContent>
              {atRiskItems.length === 0 ? (
                <p className="text-center text-muted-foreground py-4">
                  リスクありの項目はありません / No at-risk items
                </p>
              ) : (
                <div className="space-y-3">
                  {atRiskItems.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold">
                          {item.workflow_instances.request_forms.title}
                        </h4>
                        <Badge variant="outline">
                          {item.workflow_instances.request_forms.priority}
                        </Badge>
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        申請者 / Requester: {item.workflow_instances.request_forms.staff.name_jp} / 
                        {item.workflow_instances.request_forms.staff.name_en}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">応答期限 / Response Due:</span>
                          <br />
                          {format(new Date(item.target_response_date), 'MM/dd HH:mm', { locale: ja })}
                        </div>
                        <div>
                          <span className="text-muted-foreground">解決期限 / Resolution Due:</span>
                          <br />
                          {format(new Date(item.target_resolution_date), 'MM/dd HH:mm', { locale: ja })}
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <Button size="sm" variant="outline">
                          詳細を表示 / View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breaches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SLA違反の理由 / SLA Breach Reasons</CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(metrics.breach_reasons).length === 0 ? (
                <p className="text-center text-muted-foreground py-4">
                  違反はありません / No breaches
                </p>
              ) : (
                <div className="space-y-3">
                  {Object.entries(metrics.breach_reasons).map(([reason, count]) => (
                    <div key={reason} className="flex items-center justify-between p-3 border rounded-lg">
                      <span>{reason}</span>
                      <Badge variant="destructive">{count}</Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
