"use strict";(()=>{var e={};e.id=6898,e.ids=[6898],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},94735:e=>{e.exports=require("events")},81630:e=>{e.exports=require("http")},55591:e=>{e.exports=require("https")},28354:e=>{e.exports=require("util")},74075:e=>{e.exports=require("zlib")},44150:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>_,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(42706),o=t(28203),i=t(45994),n=t(39187),u=t(33406),p=t(44512);async function d(e){try{let r=(0,u.createRouteHandlerClient)({cookies:p.UL}),{data:{user:t}}=await r.auth.getUser();if(!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{action:s}=await e.json();switch(s){case"create-indexes":for(let e of["CREATE INDEX IF NOT EXISTS idx_staff_department_id ON staff(department_id)","CREATE INDEX IF NOT EXISTS idx_request_forms_requester_id ON request_forms(requester_id)","CREATE INDEX IF NOT EXISTS idx_request_forms_status ON request_forms(status)","CREATE INDEX IF NOT EXISTS idx_request_items_form_id ON request_items(request_form_id)","CREATE INDEX IF NOT EXISTS idx_workflow_instances_status ON workflow_instances(status)","CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)","CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)"])await r.rpc("execute_sql",{query:e});return n.NextResponse.json({success:!0,message:"Performance indexes created successfully"});case"get-metrics":return n.NextResponse.json({success:!0,data:{responseTime:200*Math.random()+50,throughput:1e3*Math.random()+500,errorRate:5*Math.random(),activeConnections:Math.floor(100*Math.random())+50,cacheHitRate:30*Math.random()+70,dbQueryTime:100*Math.random()+20}});default:return n.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("Performance optimization error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/performance/optimize/route",pathname:"/api/admin/performance/optimize",filename:"route",bundlePath:"app/api/admin/performance/optimize/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\performance\\optimize\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:_}=c;function l(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(44150));module.exports=s})();