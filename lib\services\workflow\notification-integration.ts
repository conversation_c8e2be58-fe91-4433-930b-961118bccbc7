import { createServerSupabase } from '@/lib/supabase'
import { NotificationService } from '@/lib/services/notification-service'
import { WorkflowEvent, WorkflowStatus } from '@/lib/types/workflow'

export interface WorkflowNotificationConfig {
  enabled: boolean
  channels: ('email' | 'sms' | 'in_app')[]
  events: WorkflowEvent[]
  quietHours?: {
    enabled: boolean
    start: string // HH:mm format
    end: string // HH:mm format
  }
}

export class WorkflowNotificationIntegration {
  private notificationService: NotificationService

  constructor(private supabase: ReturnType<typeof createServerSupabase>) {
    this.notificationService = new NotificationService(supabase)
  }

  /**
   * Send notifications for workflow events
   */
  async sendWorkflowNotification(
    workflowId: string,
    event: WorkflowEvent,
    recipients: string[],
    data: any
  ): Promise<void> {
    try {
      // Get workflow details
      const { data: workflow } = await this.supabase
        .from('workflow_instances')
        .select(`
          *,
          workflow_definitions (
            id,
            name,
            name_jp
          ),
          request_forms (
            id,
            title,
            service_categories (
              name_en,
              name_jp
            )
          )
        `)
        .eq('id', workflowId)
        .single()

      if (!workflow) return

      const workflowName = workflow.workflow_definitions?.name || 
                          workflow.request_forms?.service_categories?.name_en || 
                          'Workflow'
      const workflowNameJp = workflow.workflow_definitions?.name_jp || 
                            workflow.request_forms?.service_categories?.name_jp || 
                            'ワークフロー'

      // Get notification preferences for recipients
      const { data: preferences } = await this.supabase
        .from('notification_preferences')
        .select('*')
        .in('user_id', recipients)

      // Create notification for each recipient
      for (const recipientId of recipients) {
        const pref = preferences?.find(p => p.user_id === recipientId)
        
        // Check if user wants workflow notifications
        if (!pref?.workflow_notifications) continue

        // Check quiet hours
        if (this.isInQuietHours(pref)) continue

        const template = this.getNotificationTemplate(event, workflowName, workflowNameJp, data)

        // Send to enabled channels
        if (pref.email_enabled) {
          await this.notificationService.sendEmail({
            to: await this.getUserEmail(recipientId),
            subject: template.subject,
            template: 'workflow-notification',
            data: {
              ...template,
              workflowId,
              workflowName,
              event,
              ...data
            }
          })
        }

        if (pref.sms_enabled) {
          await this.notificationService.sendSMS({
            to: await this.getUserPhone(recipientId),
            message: template.smsMessage
          })
        }

        if (pref.in_app_enabled) {
          await this.notificationService.createInAppNotification({
            userId: recipientId,
            type: 'workflow',
            title: template.title,
            message: template.message,
            data: {
              workflowId,
              event,
              ...data
            }
          })
        }
      }
    } catch (error) {
      console.error('Failed to send workflow notification:', error)
    }
  }

  /**
   * Handle workflow status changes
   */
  async handleStatusChange(
    workflowId: string,
    oldStatus: WorkflowStatus,
    newStatus: WorkflowStatus
  ): Promise<void> {
    // Get workflow details with assignees and watchers
    const { data: workflow } = await this.supabase
      .from('workflow_instances')
      .select(`
        *,
        workflow_tasks (
          assigned_to
        ),
        request_forms (
          requester_id
        )
      `)
      .eq('id', workflowId)
      .single()

    if (!workflow) return

    const recipients = new Set<string>()
    
    // Add requester
    if (workflow.request_forms?.requester_id) {
      recipients.add(workflow.request_forms.requester_id)
    }

    // Add assignees
    workflow.workflow_tasks?.forEach(task => {
      if (task.assigned_to) recipients.add(task.assigned_to)
    })

    // Determine event type based on status change
    let event: WorkflowEvent = 'workflow_updated'
    if (newStatus === 'completed') event = 'workflow_completed'
    else if (newStatus === 'failed') event = 'workflow_failed'
    else if (newStatus === 'pending_approval') event = 'approval_required'

    await this.sendWorkflowNotification(
      workflowId,
      event,
      Array.from(recipients),
      { oldStatus, newStatus }
    )
  }

  /**
   * Handle approval requests
   */
  async handleApprovalRequest(
    workflowId: string,
    approvalChainId: string,
    approvers: string[]
  ): Promise<void> {
    await this.sendWorkflowNotification(
      workflowId,
      'approval_required',
      approvers,
      { approvalChainId }
    )
  }

  /**
   * Handle SLA events
   */
  async handleSLAEvent(
    workflowId: string,
    slaStatus: 'at_risk' | 'breached',
    assignees: string[]
  ): Promise<void> {
    const event = slaStatus === 'breached' ? 'sla_breached' : 'sla_warning'
    
    await this.sendWorkflowNotification(
      workflowId,
      event,
      assignees,
      { slaStatus }
    )
  }

  /**
   * Handle escalation events
   */
  async handleEscalation(
    workflowId: string,
    escalationRule: any,
    newAssignees: string[]
  ): Promise<void> {
    await this.sendWorkflowNotification(
      workflowId,
      'workflow_escalated',
      newAssignees,
      { 
        escalationRule: escalationRule.name,
        reason: escalationRule.condition_type
      }
    )
  }

  /**
   * Get notification template based on event
   */
  private getNotificationTemplate(
    event: WorkflowEvent,
    workflowName: string,
    workflowNameJp: string,
    data: any
  ) {
    const templates = {
      workflow_created: {
        subject: `New Workflow Created: ${workflowName}`,
        title: 'New Workflow Created',
        message: `A new ${workflowName} workflow has been created and assigned to you.`,
        smsMessage: `New workflow: ${workflowName}. Check your dashboard for details.`
      },
      workflow_updated: {
        subject: `Workflow Updated: ${workflowName}`,
        title: 'Workflow Updated',
        message: `The ${workflowName} workflow has been updated. Status: ${data.newStatus}`,
        smsMessage: `Workflow ${workflowName} updated to ${data.newStatus}.`
      },
      workflow_completed: {
        subject: `Workflow Completed: ${workflowName}`,
        title: 'Workflow Completed',
        message: `The ${workflowName} workflow has been completed successfully.`,
        smsMessage: `Workflow ${workflowName} completed successfully.`
      },
      workflow_failed: {
        subject: `Workflow Failed: ${workflowName}`,
        title: 'Workflow Failed',
        message: `The ${workflowName} workflow has failed. Please check for errors.`,
        smsMessage: `Workflow ${workflowName} failed. Check dashboard.`
      },
      approval_required: {
        subject: `Approval Required: ${workflowName}`,
        title: 'Approval Required',
        message: `Your approval is required for the ${workflowName} workflow.`,
        smsMessage: `Approval needed for ${workflowName}.`
      },
      approval_completed: {
        subject: `Approval Completed: ${workflowName}`,
        title: 'Approval Completed',
        message: `The ${workflowName} workflow has been approved.`,
        smsMessage: `${workflowName} approved.`
      },
      sla_warning: {
        subject: `SLA Warning: ${workflowName}`,
        title: 'SLA At Risk',
        message: `The ${workflowName} workflow is at risk of breaching SLA.`,
        smsMessage: `SLA warning for ${workflowName}.`
      },
      sla_breached: {
        subject: `SLA Breached: ${workflowName}`,
        title: 'SLA Breached',
        message: `The ${workflowName} workflow has breached its SLA.`,
        smsMessage: `SLA breached for ${workflowName}.`
      },
      workflow_escalated: {
        subject: `Workflow Escalated: ${workflowName}`,
        title: 'Workflow Escalated',
        message: `The ${workflowName} workflow has been escalated due to ${data.reason}.`,
        smsMessage: `${workflowName} escalated: ${data.reason}.`
      }
    }

    return templates[event] || templates.workflow_updated
  }

  /**
   * Check if current time is in quiet hours
   */
  private isInQuietHours(preferences: any): boolean {
    if (!preferences?.quiet_hours_enabled) return false

    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const currentTime = currentHour * 60 + currentMinute

    const [startHour, startMinute] = (preferences.quiet_hours_start || '22:00').split(':').map(Number)
    const [endHour, endMinute] = (preferences.quiet_hours_end || '08:00').split(':').map(Number)
    
    const startTime = startHour * 60 + startMinute
    const endTime = endHour * 60 + endMinute

    // Handle overnight quiet hours
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime < endTime
    }

    return currentTime >= startTime && currentTime < endTime
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const { data } = await this.supabase
      .from('staff')
      .select('email')
      .eq('id', userId)
      .single()

    return data?.email || ''
  }

  /**
   * Get user phone
   */
  private async getUserPhone(userId: string): Promise<string> {
    const { data } = await this.supabase
      .from('staff')
      .select('phone')
      .eq('id', userId)
      .single()

    return data?.phone || ''
  }

  /**
   * Subscribe to workflow events
   */
  subscribeToWorkflowEvents() {
    // Subscribe to workflow status changes
    this.supabase
      .channel('workflow-status-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'workflow_instances',
          filter: 'status=neq.prev.status'
        },
        async (payload) => {
          await this.handleStatusChange(
            payload.new.id,
            payload.old.status,
            payload.new.status
          )
        }
      )
      .subscribe()

    // Subscribe to new approvals
    this.supabase
      .channel('workflow-approvals')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'approval_chains'
        },
        async (payload) => {
          const approvers = payload.new.approvers || []
          await this.handleApprovalRequest(
            payload.new.workflow_instance_id,
            payload.new.id,
            approvers
          )
        }
      )
      .subscribe()

    // Subscribe to SLA status changes
    this.supabase
      .channel('sla-status-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'sla_tracking',
          filter: 'sla_status=in.(at_risk,breached)'
        },
        async (payload) => {
          if (payload.old.sla_status !== payload.new.sla_status) {
            // Get assignees
            const { data: workflow } = await this.supabase
              .from('workflow_instances')
              .select('workflow_tasks(assigned_to)')
              .eq('id', payload.new.workflow_instance_id)
              .single()

            const assignees = workflow?.workflow_tasks
              ?.map(t => t.assigned_to)
              .filter(Boolean) || []

            await this.handleSLAEvent(
              payload.new.workflow_instance_id,
              payload.new.sla_status,
              assignees
            )
          }
        }
      )
      .subscribe()

    // Subscribe to escalations
    this.supabase
      .channel('workflow-escalations')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'escalation_instances'
        },
        async (payload) => {
          const newAssignees = payload.new.actions
            ?.filter((a: any) => a.type === 'reassign')
            .map((a: any) => a.target) || []

          await this.handleEscalation(
            payload.new.workflow_instance_id,
            payload.new.rule,
            newAssignees
          )
        }
      )
      .subscribe()
  }
}
