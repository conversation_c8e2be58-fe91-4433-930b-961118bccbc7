# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://pvfymxuzhzlibbnaedgt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB2ZnlteHV6aHpsaWJibmFlZGd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNDcwMjcsImV4cCI6MjA2MTkyMzAyN30.o4FlWTRTJSGUX_pjejobTeaJF3Ay5UVN5hVhVtJ0Y_M
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB2Znltenh1aHpsaWJibmFlZGd0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMjI3NTI4OCwiZXhwIjoyMDQ3ODUxMjg4fQ.XZWLcRf9qdNpm0KrQlZCBJzGVLwR3h_fRNZg4ogDMTM

# API Keys for AI Integration
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************

# SharePoint Integration
SHAREPOINT_CLIENT_ID=your_sharepoint_client_id
SHAREPOINT_CLIENT_SECRET=your_sharepoint_client_secret
SHAREPOINT_TENANT_ID=your_sharepoint_tenant_id

# Exchange Online Integration
EXCHANGE_CLIENT_ID=your_exchange_client_id
EXCHANGE_CLIENT_SECRET=your_exchange_client_secret
EXCHANGE_TENANT_ID=your_exchange_tenant_id

# Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=ITSync
NODE_ENV=development

# AI API Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************

# Optional: Custom API endpoints
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_BASE_URL=https://api.anthropic.com/v1

# Default AI Provider (openai or anthropic)
DEFAULT_AI_PROVIDER=openai

# AI Model Configuration
OPENAI_MODEL=gpt-4.1
ANTHROPIC_MODEL=claude-opus-4-20250514

# AI Parameters
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=1000
