# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# API Keys for AI Integration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# SharePoint Integration
SHAREPOINT_CLIENT_ID=your_sharepoint_client_id
SHAREPOINT_CLIENT_SECRET=your_sharepoint_client_secret
SHAREPOINT_TENANT_ID=your_sharepoint_tenant_id

# Exchange Online Integration
EXCHANGE_CLIENT_ID=your_exchange_client_id
EXCHANGE_CLIENT_SECRET=your_exchange_client_secret
EXCHANGE_TENANT_ID=your_exchange_tenant_id

# Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=ITSync
NODE_ENV=development

# AI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Custom API endpoints
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_BASE_URL=https://api.anthropic.com/v1

# Default AI Provider (openai or anthropic)
DEFAULT_AI_PROVIDER=openai

# AI Model Configuration
OPENAI_MODEL=gpt-4.1
ANTHROPIC_MODEL=claude-opus-4-20250514

# AI Parameters
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=1000
