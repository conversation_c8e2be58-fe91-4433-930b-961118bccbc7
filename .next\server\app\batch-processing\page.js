(()=>{var e={};e.id=5079,e.ids=[5079],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54970:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(70260),r=t(28203),i=t(25155),l=t.n(i),c=t(67292),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["batch-processing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73187)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\batch-processing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\batch-processing\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/batch-processing/page",pathname:"/batch-processing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},32781:(e,s,t)=>{Promise.resolve().then(t.bind(t,73187))},56749:(e,s,t)=>{Promise.resolve().then(t.bind(t,68434))},68434:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(45512),r=t(58009),i=t(69193),l=t(97643),c=t(87021),n=t(77252),d=t(75339),o=t(666),u=t(67418),m=t(46583),p=t(86235),h=t(18741),x=t(64977),_=t(44269),j=t(85035),g=t(4890),f=t(37133);function b({value:e,onChange:s,placeholder:t="サービスカテゴリを選択",disabled:i}){let[l,c]=(0,r.useState)([]),[n,d]=(0,r.useState)(!0);return(0,f.createClient)(),(0,a.jsxs)(g.l6,{value:e,onValueChange:s,disabled:i||n,children:[(0,a.jsx)(g.bq,{children:(0,a.jsx)(g.yv,{placeholder:t})}),(0,a.jsx)(g.gC,{children:l.map(e=>(0,a.jsxs)(g.eb,{value:e.id,children:[e.name_jp," (",e.name_en,")"]},e.id))})]})}var v=t(82847),N=t(47699),y=t(25409),w=t(16873);function q({serviceCategory:e,selectedResources:s,onChange:t}){let[i,l]=(0,r.useState)([]),[c,n]=(0,r.useState)(""),[d,u]=(0,r.useState)(!1),m=((0,f.createClient)(),i.filter(e=>e.name.toLowerCase().includes(c.toLowerCase())||e.description&&e.description.toLowerCase().includes(c.toLowerCase()))),p=e=>{s.includes(e)?t(s.filter(s=>s!==e)):t([...s,e])};return e?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(y.p,{placeholder:"リソースを検索...",value:c,onChange:e=>n(e.target.value),className:"pl-8"})]}),(0,a.jsx)(o.F,{className:"h-[200px] border rounded-md p-2",children:d?(0,a.jsx)("div",{className:"text-center p-4 text-sm text-muted-foreground",children:"読み込み中..."}):0===m.length?(0,a.jsx)("div",{className:"text-center p-4 text-sm text-muted-foreground",children:"リソースが見つかりません"}):(0,a.jsx)("div",{className:"space-y-2",children:m.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.S,{id:e.id,checked:s.includes(e.id),onCheckedChange:()=>p(e.id)}),(0,a.jsxs)(N.J,{htmlFor:e.id,className:"flex-1 cursor-pointer text-sm",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),e.description&&(0,a.jsxs)("span",{className:"text-muted-foreground ml-2",children:["(",e.description,")"]})]})]},e.id))})}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[s.length," 個のリソースが選択されています"]})]}):(0,a.jsx)("div",{className:"text-sm text-muted-foreground p-4 text-center",children:"サービスカテゴリを選択してください"})}function C({onSubmit:e,isProcessing:s}){let[t,g]=(0,r.useState)("single_user_multiple_requests_add"),[f,v]=(0,r.useState)([]),[N,y]=(0,r.useState)([]),[w,C]=(0,r.useState)([]),S=e=>{v(f.filter((s,t)=>t!==e))},A=(e,s)=>{let t=[...f];t[e]={...t[e],...s},v(t)},B=()=>{let e=[];return 0===N.length&&e.push("少なくとも1人のユーザーを選択してください。"),0===f.length&&e.push("少なくとも1つのサービスリクエストを追加してください。"),f.forEach((s,t)=>{s.service_category_id||e.push(`リクエスト ${t+1}: サービスカテゴリを選択してください。`),0===s.target_resources.length&&e.push(`リクエスト ${t+1}: 対象リソースを選択してください。`)}),C(e),0===e.length},R=e=>{switch(e){case"single_user_multiple_requests_add":case"single_user_multiple_requests_delete":case"single_user_multiple_requests_mixed":case"single_user_multiple_category_mixed":return"single_user_multi_service";case"multiple_users_multiple_requests_add":return"multi_user_multi_service";case"multiple_users_single_request_add":return"multi_user_single_service";default:return"mixed_operations"}};return(0,a.jsxs)(l.Zp,{className:"w-full",children:[(0,a.jsxs)(l.aR,{children:["        ",(0,a.jsx)(l.ZB,{children:"バッチリクエストビルダー"}),(0,a.jsx)(l.BT,{children:"複数のユーザーやサービスに対する一括処理リクエストを作成します"})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"処理シナリオ"}),(0,a.jsx)(i.tU,{value:t,onValueChange:e=>g(e),children:(0,a.jsx)(i.j7,{className:"grid grid-cols-2 gap-2 h-auto",children:[{value:"single_user_multiple_requests_add",label:"単一ユーザー・複数リクエスト（追加）",description:"一人のユーザーに複数のサービスを追加"},{value:"multiple_users_multiple_requests_add",label:"複数ユーザー・複数リクエスト（追加）",description:"複数のユーザーに複数のサービスを追加"},{value:"multiple_users_single_request_add",label:"複数ユーザー・単一リクエスト（追加）",description:"複数のユーザーに同じサービスを追加"},{value:"single_user_multiple_requests_delete",label:"単一ユーザー・複数リクエスト（削除）",description:"一人のユーザーから複数のサービスを削除"},{value:"single_user_multiple_requests_mixed",label:"単一ユーザー・複数リクエスト（混合）",description:"一人のユーザーのサービスを追加・削除"},{value:"single_user_multiple_category_mixed",label:"単一ユーザー・複数カテゴリ（混合）",description:"一人のユーザーの複数カテゴリを操作"}].map(e=>(0,a.jsxs)(i.Xi,{value:e.value,className:"flex flex-col items-start p-3 h-auto",children:[(0,a.jsx)("span",{className:"font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.description})]},e.value))})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"対象ユーザー"}),(0,a.jsx)(j.M,{multiple:t.includes("multiple_users"),onSelect:y,placeholder:"ユーザーを検索..."}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:N.map(e=>(0,a.jsxs)(n.E,{variant:"secondary",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),e.name_jp||e.name_en]},e.id))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"サービスリクエスト"}),(0,a.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{v([...f,{service_category_id:"",affected_users:[],action_type:"add",target_resources:[],request_details:{}}])},children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-1"}),"追加"]})]}),(0,a.jsx)(o.F,{className:"h-[400px] pr-4",children:(0,a.jsx)("div",{className:"space-y-4",children:f.map((e,s)=>(0,a.jsxs)(l.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("h4",{className:"font-medium",children:["リクエスト ",s+1]}),(0,a.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>S(s),children:(0,a.jsx)(_.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(b,{value:e.service_category_id,onChange:e=>A(s,{service_category_id:e})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(c.$,{type:"button",variant:"add"===e.action_type?"default":"outline",size:"sm",onClick:()=>A(s,{action_type:"add"}),children:"追加"}),(0,a.jsx)(c.$,{type:"button",variant:"remove"===e.action_type?"default":"outline",size:"sm",onClick:()=>A(s,{action_type:"remove"}),children:"削除"}),(0,a.jsx)(c.$,{type:"button",variant:"update"===e.action_type?"default":"outline",size:"sm",onClick:()=>A(s,{action_type:"update"}),children:"更新"})]}),(0,a.jsx)(q,{serviceCategory:e.service_category_id,selectedResources:e.target_resources,onChange:e=>A(s,{target_resources:e})})]})]},s))})})]}),w.length>0&&(0,a.jsxs)(d.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(d.TN,{children:(0,a.jsx)("ul",{className:"list-disc list-inside",children:w.map((e,s)=>(0,a.jsx)("li",{children:e},s))})})]}),(0,a.jsx)(c.$,{onClick:()=>{B()&&e({operation_type:R(t),items:f.map(e=>({...e,affected_users:N.map(e=>e.id)}))})},disabled:s,className:"w-full",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2 animate-spin"}),"処理中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"バッチリクエストを送信"]})})]})]})}var S=t(3328),A=t(12784),B=t(92056),R=t(4643),E=t(92557),P=t(97832);class k{async createBatchOperation(e,s){try{let t=await this.validateBatchRequest(e,s);if(!t.valid)return{success:!1,message:"Batch validation failed",batch_id:"",processed_items:0,failed_items:0,errors:t.errors.map(e=>({item_id:"",error_code:"VALIDATION_ERROR",error_message:e.error,timestamp:new Date().toISOString()}))};let{data:a,error:r}=await this.supabase.from("batch_operations").insert({requester_id:e,operation_type:s.operation_type,total_items:s.items.reduce((e,s)=>e+s.affected_users.length,0),status:"pending"}).select().single();if(r||!a)throw Error("Failed to create batch operation");let i=[],l=0;for(let e of s.items)for(let s of e.affected_users)for(let t of e.target_resources){let{data:r,error:c}=await this.supabase.from("request_items").insert({request_form_id:null,service_category_id:e.service_category_id,affected_user_id:s,action_type:e.action_type,target_resource:t,request_details:e.request_details,status:"pending"}).select().single();if(c||!r)throw Error("Failed to create request item");i.push(r),await this.supabase.from("batch_items").insert({batch_operation_id:a.id,request_item_id:r.id,item_order:l++,status:"pending"})}return this.processBatch(a.id,i.map(e=>e.id)),{success:!0,message:"Batch operation created successfully",batch_id:a.batch_id,processed_items:0,failed_items:0}}catch(e){return console.error("Error creating batch operation:",e),{success:!1,message:"Failed to create batch operation",batch_id:"",processed_items:0,failed_items:0,errors:[{item_id:"",error_code:"SYSTEM_ERROR",error_message:e.message,timestamp:new Date().toISOString()}]}}}async validateBatchRequest(e,s){let{data:t,error:a}=await this.supabase.rpc("validate_batch_request",{p_requester_id:e,p_request_items:JSON.stringify(s.items)});return a?{valid:!1,errors:[{item:s.items[0],error:"Validation failed: "+a.message}]}:t}async processBatch(e,s){try{let{data:t,error:a}=await this.supabase.rpc("process_batch_transaction",{p_batch_operation_id:e,p_request_items:s});a&&(console.error("Batch processing error:",a),await this.supabase.from("batch_operations").update({status:"failed",completed_at:new Date().toISOString(),error_details:[{item_id:"",error_code:"PROCESSING_ERROR",error_message:a.message,timestamp:new Date().toISOString()}]}).eq("id",e))}catch(e){console.error("Batch processing exception:",e)}}async getBatchStatus(e){let{data:s,error:t}=await this.supabase.from("batch_operations").select("*").eq("batch_id",e).single();return t?(console.error("Error fetching batch status:",t),null):s}async getBatchItems(e){let{data:s,error:t}=await this.supabase.from("batch_items").select("*").eq("batch_operation_id",e).order("item_order");return t?(console.error("Error fetching batch items:",t),[]):s||[]}subscribeToBatchUpdates(e,s){let t=this.supabase.channel(`batch-${e}`).on("postgres_changes",{event:"UPDATE",schema:"public",table:"batch_operations",filter:`batch_id=eq.${e}`},e=>{let t=e.new;s({batch_id:t.batch_id,status:t.status,progress_percentage:t.progress_percentage,completed_items:t.completed_items,total_items:t.total_items})}).subscribe();return()=>{this.supabase.removeChannel(t)}}async cancelBatch(e){let{error:s}=await this.supabase.from("batch_operations").update({status:"cancelled",completed_at:new Date().toISOString()}).eq("batch_id",e).eq("status","pending");return s?(console.error("Error cancelling batch:",s),!1):(await this.supabase.from("batch_items").update({status:"cancelled"}).eq("batch_operation_id",e).eq("status","pending"),!0)}async retryFailedItems(e){let{data:s,error:t}=await this.supabase.from("batch_items").select("request_item_id").eq("batch_operation_id",e).eq("status","failed");return t||!s||0===s.length?{success:!1,message:"No failed items to retry",batch_id:e,processed_items:0,failed_items:0}:(await this.supabase.from("batch_items").update({status:"pending",error_message:null}).eq("batch_operation_id",e).eq("status","failed"),await this.processBatch(e,s.map(e=>e.request_item_id)),{success:!0,message:"Retrying failed items",batch_id:e,processed_items:0,failed_items:s.length})}constructor(){this.supabase=(0,f.createClient)()}}let D=new k;var F=t(59462);function I({batchId:e,onComplete:s}){var t;let[i,d]=(0,r.useState)(null),[m,h]=(0,r.useState)([]),[x,_]=(0,r.useState)(!0),j=async()=>{_(!0);try{let s=await D.getBatchStatus(e);if(s){d(s);let e=await D.getBatchItems(s.id);h(e)}}finally{_(!1)}},g=async()=>{i&&(await D.retryFailedItems(i.id),j())},f=async()=>{i&&"pending"===i.status&&await D.cancelBatch(i.batch_id)},b=e=>{switch(e){case"completed":return(0,a.jsx)(B.A,{className:"w-5 h-5 text-green-600"});case"failed":return(0,a.jsx)(P.A,{className:"w-5 h-5 text-red-600"});case"processing":return(0,a.jsx)(p.A,{className:"w-5 h-5 text-blue-600 animate-spin"});case"pending":return(0,a.jsx)(R.A,{className:"w-5 h-5 text-gray-600"});default:return(0,a.jsx)(u.A,{className:"w-5 h-5 text-yellow-600"})}};return x?(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"flex items-center justify-center p-8",children:(0,a.jsx)(p.A,{className:"w-8 h-8 animate-spin"})})}):i?(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.ZB,{children:"バッチ処理状況"}),(0,a.jsxs)(l.BT,{children:["バッチID: ",i.batch_id]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(t=i.status,(0,a.jsx)(n.E,{variant:{completed:"default",failed:"destructive",processing:"secondary",pending:"outline"}[t]||"outline",children:{completed:"完了",failed:"失敗",processing:"処理中",pending:"待機中",partially_completed:"部分完了"}[t]||t})),"pending"===i.status&&(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:f,children:[(0,a.jsx)(P.A,{className:"w-4 h-4 mr-1"}),"キャンセル"]}),i.failed_items>0&&(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:g,children:[(0,a.jsx)(E.A,{className:"w-4 h-4 mr-1"}),"失敗項目を再試行"]})]})]})}),"      ",(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"進捗状況"}),(0,a.jsxs)("span",{children:[i.progress_percentage.toFixed(1),"%"]})]}),(0,a.jsx)(S.k,{value:i.progress_percentage}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{children:["完了: ",i.completed_items]}),(0,a.jsxs)("span",{children:["失敗: ",i.failed_items]}),(0,a.jsxs)("span",{children:["合計: ",i.total_items]})]})]}),(0,a.jsx)(A.w,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:"処理項目"}),(0,a.jsx)(o.F,{className:"h-[300px]",children:(0,a.jsx)("div",{className:"space-y-2",children:m.map((e,s)=>(0,a.jsxs)("div",{className:(0,F.cn)("flex items-center justify-between p-3 rounded-lg border","completed"===e.status&&"bg-green-50 border-green-200","failed"===e.status&&"bg-red-50 border-red-200","processing"===e.status&&"bg-blue-50 border-blue-200","pending"===e.status&&"bg-gray-50 border-gray-200"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[b(e.status),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["項目 ",s+1]}),e.error_message&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:e.error_message})]})]}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:e.processed_at?new Date(e.processed_at).toLocaleString("ja-JP"):"未処理"})]},e.id))})})]})]})]}):(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"text-center p-8",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"バッチ操作が見つかりません"})})})}var O=t(43433),$=t(91542),T=t(61075);function Z(){let{user:e}=(0,O.A)(),[s,t]=(0,r.useState)(!1),[c,n]=(0,r.useState)(null),[p,h]=(0,r.useState)([]),[x,_]=(0,r.useState)("create"),j=async s=>{if(!e){$.oR.error("ログインが必要です");return}t(!0);try{let t=await D.createBatchOperation(e.id,s);t.success?($.oR.success("バッチ処理を開始しました"),n(t.batch_id),_("monitor"),g()):$.oR.error(t.message)}catch(e){$.oR.error("バッチ処理の開始に失敗しました")}finally{t(!1)}},g=async()=>{},f=e=>{switch(e){case"completed":return(0,a.jsx)(m.A,{className:"w-4 h-4 text-green-600"});case"failed":return(0,a.jsx)(P.A,{className:"w-4 h-4 text-red-600"});case"processing":return(0,a.jsx)(R.A,{className:"w-4 h-4 text-blue-600"});case"pending":return(0,a.jsx)(R.A,{className:"w-4 h-4 text-gray-600"});default:return(0,a.jsx)(u.A,{className:"w-4 h-4 text-yellow-600"})}};return(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"バッチ処理システム"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"複数のユーザーやサービスに対する一括処理を実行します"})]}),(0,a.jsxs)(i.tU,{value:x,onValueChange:_,children:[(0,a.jsxs)(i.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(i.Xi,{value:"create",children:"新規作成"}),(0,a.jsx)(i.Xi,{value:"monitor",disabled:!c,children:"処理状況"}),(0,a.jsx)(i.Xi,{value:"history",children:"履歴"})]}),(0,a.jsx)(i.av,{value:"create",className:"mt-6",children:(0,a.jsx)(C,{onSubmit:j,isProcessing:s})}),(0,a.jsx)(i.av,{value:"monitor",className:"mt-6",children:c?(0,a.jsx)(I,{batchId:c,onComplete:()=>g()}):(0,a.jsxs)(d.Fc,{children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"バッチが選択されていません"}),(0,a.jsx)(d.TN,{children:"新規作成タブからバッチ処理を開始してください。"})]})}),(0,a.jsx)(i.av,{value:"history",className:"mt-6",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"バッチ処理履歴"}),(0,a.jsx)(l.BT,{children:"過去のバッチ処理の実行履歴を表示します"})]}),(0,a.jsx)(l.Wu,{children:0===p.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(T.A,{className:"w-12 h-12 mx-auto mb-4 opacity-30"}),(0,a.jsx)("p",{children:"バッチ処理履歴がありません"})]}):(0,a.jsx)(o.F,{className:"h-[400px]",children:(0,a.jsx)("div",{className:"space-y-4",children:p.map(e=>(0,a.jsx)(l.Zp,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[f(e.status),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["バッチ ",e.batch_id]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:new Date(e.created_at).toLocaleString("ja-JP")})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm",children:[e.completed_items,"/",e.total_items," 件完了"]}),e.failed_items>0&&(0,a.jsxs)("p",{className:"text-sm text-red-600",children:[e.failed_items," 件失敗"]})]})]})},e.id))})})})]})})]})]})}},73187:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\batch-processing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\batch-processing\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8096,2076],()=>t(54970));module.exports=a})();