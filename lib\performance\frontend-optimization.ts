// Frontend Performance Optimization
// Lazy loading and code splitting implementation

import dynamic from 'next/dynamic';
import { lazy, Suspense } from 'react';

// Lazy load heavy components
export const LazyFormBuilder = dynamic(
  () => import('@/components/forms/dynamic-form-builder'),
  { 
    loading: () => <div className="animate-pulse h-96 bg-muted rounded-lg" />,
    ssr: false 
  }
);

export const LazyWorkflowDashboard = dynamic(
  () => import('@/components/workflows/workflow-monitoring-dashboard'),
  { 
    loading: () => <div className="animate-pulse h-96 bg-muted rounded-lg" />,
    ssr: false 
  }
);

export const LazyKnowledgeBase = dynamic(
  () => import('@/components/knowledge-base/knowledge-base-widget'),
  { 
    loading: () => <div className="animate-pulse h-64 bg-muted rounded-lg" />,
    ssr: false 
  }
);

// Image optimization utility
export const optimizeImage = (src: string, width: number) => {
  // Use Next.js Image optimization API
  return `/_next/image?url=${encodeURIComponent(src)}&w=${width}&q=75`;
};

// Bundle size optimization strategies
export const optimizationConfig = {
  // Tree shake unused code
  sideEffects: false,
  
  // Optimize chunk sizes
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      default: false,
      vendors: false,
      vendor: {
        name: 'vendor',
        chunks: 'all',
        test: /node_modules/,
        priority: 20
      },
      common: {
        minChunks: 2,
        priority: 10,
        reuseExistingChunk: true
      }
    }
  }
};
