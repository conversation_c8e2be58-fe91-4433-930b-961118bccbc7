'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { FormField, FormData } from '@/lib/form-types'
import { aiFormAssistant } from '@/lib/ai-form-assistant'

export interface ValidationResult {
  isValid: boolean
  message?: string
  type: 'success' | 'warning' | 'error' | 'info'
  aiInsight?: string
  suggestion?: any
}

export interface ValidationState {
  [fieldId: string]: ValidationResult
}

interface UseRealTimeValidationProps {
  fields: FormField[]
  formData: FormData
  context?: {
    userId?: string
    departmentId?: string
    userRole?: string
  }
  debounceMs?: number
}

export function useRealTimeValidation({
  fields,
  formData,
  context,
  debounceMs = 500
}: UseRealTimeValidationProps) {
  const [validationState, setValidationState] = useState<ValidationState>({})
  const [isValidating, setIsValidating] = useState<Record<string, boolean>>({})
  const timeoutRefs = useRef<Record<string, NodeJS.Timeout>>({})

  // Clear all timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(timeoutRefs.current).forEach(timeout => clearTimeout(timeout))
    }
  }, [])

  const validateField = useCallback(async (
    field: FormField, 
    value: any
  ): Promise<ValidationResult> => {
    // Basic validation first
    const basicValidation = await performBasicValidation(field, value)
    if (!basicValidation.isValid) {
      return basicValidation
    }

    // AI-powered validation if basic validation passes
    if (value && value.toString().trim() !== '') {
      try {
        const aiValidation = await aiFormAssistant.validateFieldWithAI(field, value, context)
        return aiValidation || basicValidation
      } catch (error) {
        console.error('AI validation failed:', error)
        return basicValidation
      }
    }

    return basicValidation
  }, [context])

  const performBasicValidation = async (
    field: FormField, 
    value: any
  ): Promise<ValidationResult> => {
    // Required field validation
    if (field.required && (!value || value.toString().trim() === '')) {
      return {
        isValid: false,
        type: 'error',
        message: `${field.labelJp || field.label}は必須です / ${field.label} is required`
      }
    }

    // Empty field - no validation needed
    if (!value || value.toString().trim() === '') {
      return {
        isValid: true,
        type: 'info'
      }
    }

    // Type-specific validation
    switch (field.type) {
      case 'text':
        if (field.validation === 'email') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(value)) {
            return {
              isValid: false,
              type: 'error',
              message: '有効なメールアドレスを入力してください / Please enter a valid email address'
            }
          }
        }
        
        if (field.validation === 'pc_id') {
          const pcIdRegex = /^[A-Z]{2}\d{8}$/
          if (!pcIdRegex.test(value)) {
            return {
              isValid: false,
              type: 'warning',
              message: 'PC IDは通常「XX12345678」の形式です / PC ID should be in format "XX12345678"',
              suggestion: generatePCIdSuggestion(value)
            }
          }
        }
        break

      case 'search':
        if (field.required && (!value || !value.id)) {
          return {
            isValid: false,
            type: 'error',
            message: `${field.labelJp || field.label}から選択してください / Please select from ${field.label}`
          }
        }
        break

      case 'multiselect':
        if (field.required && (!value || !Array.isArray(value) || value.length === 0)) {
          return {
            isValid: false,
            type: 'error',
            message: `${field.labelJp || field.label}を少なくとも1つ選択してください / Please select at least one ${field.label}`
          }
        }
        break
    }

    return {
      isValid: true,
      type: 'success',
      message: '入力内容は正常です / Input is valid'
    }
  }

  const debouncedValidate = useCallback((fieldId: string, value: any) => {
    // Clear existing timeout
    if (timeoutRefs.current[fieldId]) {
      clearTimeout(timeoutRefs.current[fieldId])
    }

    // Set validation loading state
    setIsValidating(prev => ({ ...prev, [fieldId]: true }))

    // Create new timeout
    timeoutRefs.current[fieldId] = setTimeout(async () => {
      const field = fields.find(f => f.id === fieldId)
      if (!field) return

      try {
        const result = await validateField(field, value)
        setValidationState(prev => ({
          ...prev,
          [fieldId]: result
        }))
      } catch (error) {
        console.error('Validation error:', error)
        setValidationState(prev => ({
          ...prev,
          [fieldId]: {
            isValid: false,
            type: 'error',
            message: 'バリデーションエラーが発生しました / Validation error occurred'
          }
        }))
      } finally {
        setIsValidating(prev => ({ ...prev, [fieldId]: false }))
      }
    }, debounceMs)
  }, [fields, validateField, debounceMs])

  // Validate field when form data changes
  useEffect(() => {
    Object.keys(formData).forEach(fieldId => {
      const value = formData[fieldId]
      debouncedValidate(fieldId, value)
    })
  }, [formData, debouncedValidate])

  const getFieldValidation = (fieldId: string): ValidationResult | undefined => {
    return validationState[fieldId]
  }

  const isFieldValidating = (fieldId: string): boolean => {
    return isValidating[fieldId] || false
  }

  const validateAllFields = async (): Promise<boolean> => {
    const results = await Promise.all(
      fields.map(async field => {
        const value = formData[field.id]
        return validateField(field, value)
      })
    )

    const newValidationState: ValidationState = {}
    fields.forEach((field, index) => {
      newValidationState[field.id] = results[index]
    })

    setValidationState(newValidationState)
    return results.every(result => result.isValid)
  }

  return {
    validationState,
    isValidating,
    getFieldValidation,
    isFieldValidating,
    validateAllFields,
    debouncedValidate
  }
}

// Helper function to generate PC ID suggestions
function generatePCIdSuggestion(input: string): string {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const rand = Math.floor(Math.random() * 100).toString().padStart(2, '0')
  
  const lowerInput = input.toLowerCase()
  
  if (lowerInput.includes('laptop') || lowerInput.includes('ノート')) {
    return `LT${year}${month}${day}${rand}`
  }
  
  if (lowerInput.includes('desktop') || lowerInput.includes('デスク')) {
    return `DT${year}${month}${day}${rand}`
  }
  
  if (lowerInput.includes('server') || lowerInput.includes('サーバ')) {
    return `SV${year}${month}${day}${rand}`
  }
  
  return `PC${year}${month}${day}${rand}`
}
