"use strict";(()=>{var e={};e.id=5033,e.ids=[5033],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68605:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>d,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{GET:()=>u});var a=s(42706),i=s(28203),o=s(45994),n=s(39187),_=s(68335);async function u(e){try{let t=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await t.from("user_profiles").select("role").eq("user_id",s.id).single();if(!a||!["admin","system_admin"].includes(a.role))return n.NextResponse.json({error:"Insufficient permissions"},{status:403});(0,_.ip)();let i=new URL(e.url),o=i.searchParams.get("format")||"json",u=parseInt(i.searchParams.get("window")||"3600000"),c=_.qd.getSystemMetrics(),l="true"===i.searchParams.get("detailed"),m={};l&&(m=_.qd.getMetricNames().reduce((e,t)=>(e[t]=_.qd.getAggregatedMetrics(t,u),e),{}));let y={timestamp:new Date().toISOString(),window_ms:u,system:c,...l&&{detailed:m}};if("prometheus"===o)return new n.NextResponse(function(e){let t=Date.now();return`
# HELP itsync_http_requests_total Total number of HTTP requests
# TYPE itsync_http_requests_total counter
itsync_http_requests_total ${e.requests.total} ${t}

# HELP itsync_http_requests_success_total Total number of successful HTTP requests
# TYPE itsync_http_requests_success_total counter
itsync_http_requests_success_total ${e.requests.success} ${t}

# HELP itsync_http_requests_error_total Total number of failed HTTP requests
# TYPE itsync_http_requests_error_total counter
itsync_http_requests_error_total ${e.requests.errors} ${t}

# HELP itsync_http_response_time_avg Average HTTP response time in milliseconds
# TYPE itsync_http_response_time_avg gauge
itsync_http_response_time_avg ${e.requests.response_time_avg||0} ${t}

# HELP itsync_database_query_time_avg Average database query time in milliseconds
# TYPE itsync_database_query_time_avg gauge
itsync_database_query_time_avg ${e.database.query_time_avg||0} ${t}

# HELP itsync_database_slow_queries_total Total number of slow database queries
# TYPE itsync_database_slow_queries_total counter
itsync_database_slow_queries_total ${e.database.slow_queries} ${t}

# HELP itsync_ai_requests_total Total number of AI API requests
# TYPE itsync_ai_requests_total counter
itsync_ai_requests_total ${e.ai.requests_total} ${t}

# HELP itsync_ai_cost_total Total estimated AI API cost in USD
# TYPE itsync_ai_cost_total counter
itsync_ai_cost_total ${e.ai.cost_estimate||0} ${t}

# HELP itsync_ai_errors_total Total number of AI API errors
# TYPE itsync_ai_errors_total counter
itsync_ai_errors_total ${e.ai.errors} ${t}

# HELP itsync_security_failed_logins_total Total number of failed login attempts
# TYPE itsync_security_failed_logins_total counter
itsync_security_failed_logins_total ${e.security.failed_logins} ${t}

# HELP itsync_security_rate_limit_hits_total Total number of rate limit hits
# TYPE itsync_security_rate_limit_hits_total counter
itsync_security_rate_limit_hits_total ${e.security.rate_limit_hits} ${t}

# HELP itsync_security_mfa_failures_total Total number of MFA failures
# TYPE itsync_security_mfa_failures_total counter
itsync_security_mfa_failures_total ${e.security.mfa_failures} ${t}

# HELP itsync_system_memory_usage_mb Current memory usage in MB
# TYPE itsync_system_memory_usage_mb gauge
itsync_system_memory_usage_mb ${e.system.memory_usage||0} ${t}

# HELP itsync_system_uptime_ms System uptime in milliseconds
# TYPE itsync_system_uptime_ms gauge
itsync_system_uptime_ms ${e.system.uptime} ${t}
`.trim()}(c),{headers:{"Content-Type":"text/plain; version=0.0.4","Cache-Control":"no-cache, no-store, must-revalidate"}});return n.NextResponse.json(y,{headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return console.error("Metrics API error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/metrics/route",pathname:"/api/metrics",filename:"route",bundlePath:"app/api/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\metrics\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:y}=c;function d(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8096,2076],()=>s(68605));module.exports=r})();