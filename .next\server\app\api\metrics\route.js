(()=>{var e={};e.id=5033,e.ids=[5033],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},25984:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>p,routeModule:()=>m,serverHooks:()=>d,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>_});var i=s(42706),a=s(28203),o=s(45994),n=s(39187),c=s(68335),u=s(61487);async function _(e){try{let t=(0,u.U)(),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:i}=await t.from("user_profiles").select("role").eq("user_id",s.id).single();if(!i||!["admin","system_admin"].includes(i.role))return n.NextResponse.json({error:"Insufficient permissions"},{status:403});(0,c.ip)();let a=new URL(e.url),o=a.searchParams.get("format")||"json",_=parseInt(a.searchParams.get("window")||"3600000"),m=c.qd.getSystemMetrics(),l="true"===a.searchParams.get("detailed"),g={};l&&(g=c.qd.getMetricNames().reduce((e,t)=>(e[t]=c.qd.getAggregatedMetrics(t,_),e),{}));let d={timestamp:new Date().toISOString(),window_ms:_,system:m,...l&&{detailed:g}};if("prometheus"===o)return new n.NextResponse(function(e){let t=Date.now();return`
# HELP itsync_http_requests_total Total number of HTTP requests
# TYPE itsync_http_requests_total counter
itsync_http_requests_total ${e.requests.total} ${t}

# HELP itsync_http_requests_success_total Total number of successful HTTP requests
# TYPE itsync_http_requests_success_total counter
itsync_http_requests_success_total ${e.requests.success} ${t}

# HELP itsync_http_requests_error_total Total number of failed HTTP requests
# TYPE itsync_http_requests_error_total counter
itsync_http_requests_error_total ${e.requests.errors} ${t}

# HELP itsync_http_response_time_avg Average HTTP response time in milliseconds
# TYPE itsync_http_response_time_avg gauge
itsync_http_response_time_avg ${e.requests.response_time_avg||0} ${t}

# HELP itsync_database_query_time_avg Average database query time in milliseconds
# TYPE itsync_database_query_time_avg gauge
itsync_database_query_time_avg ${e.database.query_time_avg||0} ${t}

# HELP itsync_database_slow_queries_total Total number of slow database queries
# TYPE itsync_database_slow_queries_total counter
itsync_database_slow_queries_total ${e.database.slow_queries} ${t}

# HELP itsync_ai_requests_total Total number of AI API requests
# TYPE itsync_ai_requests_total counter
itsync_ai_requests_total ${e.ai.requests_total} ${t}

# HELP itsync_ai_cost_total Total estimated AI API cost in USD
# TYPE itsync_ai_cost_total counter
itsync_ai_cost_total ${e.ai.cost_estimate||0} ${t}

# HELP itsync_ai_errors_total Total number of AI API errors
# TYPE itsync_ai_errors_total counter
itsync_ai_errors_total ${e.ai.errors} ${t}

# HELP itsync_security_failed_logins_total Total number of failed login attempts
# TYPE itsync_security_failed_logins_total counter
itsync_security_failed_logins_total ${e.security.failed_logins} ${t}

# HELP itsync_security_rate_limit_hits_total Total number of rate limit hits
# TYPE itsync_security_rate_limit_hits_total counter
itsync_security_rate_limit_hits_total ${e.security.rate_limit_hits} ${t}

# HELP itsync_security_mfa_failures_total Total number of MFA failures
# TYPE itsync_security_mfa_failures_total counter
itsync_security_mfa_failures_total ${e.security.mfa_failures} ${t}

# HELP itsync_system_memory_usage_mb Current memory usage in MB
# TYPE itsync_system_memory_usage_mb gauge
itsync_system_memory_usage_mb ${e.system.memory_usage||0} ${t}

# HELP itsync_system_uptime_ms System uptime in milliseconds
# TYPE itsync_system_uptime_ms gauge
itsync_system_uptime_ms ${e.system.uptime} ${t}
`.trim()}(m),{headers:{"Content-Type":"text/plain; version=0.0.4","Cache-Control":"no-cache, no-store, must-revalidate"}});return n.NextResponse.json(d,{headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return console.error("Metrics API error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/metrics/route",pathname:"/api/metrics",filename:"route",bundlePath:"app/api/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\metrics\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:d}=m;function p(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},68335:(e,t,s)=>{"use strict";s.d(t,{I_:()=>a,hf:()=>o,ip:()=>n,qd:()=>i});class r{record(e,t,s,r){let i={name:e,value:t,timestamp:Date.now(),tags:s,unit:r};this.metrics.has(e)||this.metrics.set(e,[]);let a=this.metrics.get(e);a.push(i);let o=Date.now()-this.retentionPeriod,n=a.filter(e=>e.timestamp>o);n.length>this.maxMetricsPerType&&n.splice(0,n.length-this.maxMetricsPerType),this.metrics.set(e,n)}increment(e,t){this.record(e,1,t,"count")}timing(e,t,s){this.record(e,t,s,"ms")}gauge(e,t,s){this.record(e,t,s,"gauge")}getMetrics(e){return this.metrics.get(e)||[]}getMetricNames(){return Array.from(this.metrics.keys())}getAggregatedMetrics(e,t=6e4){let s=Date.now()-t,r=this.getMetrics(e).filter(e=>e.timestamp>s);if(0===r.length)return{count:0,sum:0,avg:0,min:0,max:0};let i=r.map(e=>e.value),a=i.reduce((e,t)=>e+t,0);return{count:r.length,sum:a,avg:a/r.length,min:Math.min(...i),max:Math.max(...i)}}getSystemMetrics(){let e=Date.now();return{requests:{total:this.getAggregatedMetrics("http.requests",36e5).count,success:this.getAggregatedMetrics("http.requests.success",36e5).count,errors:this.getAggregatedMetrics("http.requests.error",36e5).count,response_time_avg:this.getAggregatedMetrics("http.response_time",36e5).avg},database:{connections:this.getAggregatedMetrics("db.connections",36e5).avg,query_time_avg:this.getAggregatedMetrics("db.query_time",36e5).avg,slow_queries:this.getAggregatedMetrics("db.slow_queries",36e5).count},ai:{requests_total:this.getAggregatedMetrics("ai.requests",36e5).count,cost_estimate:this.getAggregatedMetrics("ai.cost",36e5).sum,errors:this.getAggregatedMetrics("ai.errors",36e5).count,response_time_avg:this.getAggregatedMetrics("ai.response_time",36e5).avg},security:{failed_logins:this.getAggregatedMetrics("auth.failed_logins",36e5).count,rate_limit_hits:this.getAggregatedMetrics("rate_limit.hits",36e5).count,mfa_failures:this.getAggregatedMetrics("mfa.failures",36e5).count},system:{memory_usage:this.getAggregatedMetrics("system.memory",3e5).avg,cpu_usage:this.getAggregatedMetrics("system.cpu",3e5).avg,uptime:e-(this.getMetrics("system.start_time")[0]?.timestamp||e)}}}cleanup(){let e=Date.now()-this.retentionPeriod;for(let[t,s]of this.metrics.entries()){let r=s.filter(t=>t.timestamp>e);this.metrics.set(t,r)}}constructor(){this.metrics=new Map,this.maxMetricsPerType=1e3,this.retentionPeriod=864e5}}let i=new r;function a(e,t,s){let r={operation:e,success:s.toString()};i.timing("db.query_time",t,r),i.increment("db.queries",r),t>1e3&&i.increment("db.slow_queries",r)}function o(e,t,s,r,a){let o={provider:e,model:t};i.increment("ai.requests",o),i.timing("ai.response_time",s,o),i.record("ai.tokens",r,o,"count"),i.record("ai.cost",a,o,"usd")}function n(){let e=Math.round(process.memoryUsage().heapUsed/1024/1024);i.gauge("system.memory",e)}i.record("system.start_time",Date.now()),setInterval(()=>{i.cleanup()},36e5)},61487:(e,t,s)=>{"use strict";s.d(t,{U:()=>a});var r=s(49064),i=s(44512);let a=()=>{let e=(0,i.UL)();return(0,r.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:s,options:r})=>e.set(t,s,r))}catch{}}}})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5994,5452,4512,9064],()=>s(25984));module.exports=r})();