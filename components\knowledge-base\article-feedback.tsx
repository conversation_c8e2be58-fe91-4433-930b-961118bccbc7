'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ThumbsUp, ThumbsDown, AlertCircle, Clock } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/lib/auth-context'

interface ArticleFeedbackProps {
  articleId: string
  articleTitle: string
}

export function ArticleFeedback({ articleId, articleTitle }: ArticleFeedbackProps) {
  const [feedbackType, setFeedbackType] = useState<string | null>(null)
  const [suggestion, setSuggestion] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const { user } = useAuth()
  const { toast } = useToast()

  const submitFeedback = async (type: string) => {
    if (!user) {
      toast({
        title: 'ログインが必要です',
        description: 'フィードバックを送信するにはログインしてください',
        variant: 'destructive'
      })
      return
    }

    try {
      setSubmitting(true)
      setFeedbackType(type)

      // Get staff info
      const { data: staff } = await supabase
        .from('staff')
        .select('id')
        .eq('auth_id', user.id)
        .single()

      if (!staff) {
        throw new Error('Staff record not found')
      }

      // Submit feedback
      const { error } = await supabase
        .from('kb_article_feedback')
        .insert({
          article_id: articleId,
          user_id: staff.id,
          feedback_type: type,
          suggestion: suggestion || null,
          created_at: new Date().toISOString()
        })

      if (error) throw error

      // Trigger content update check via API
      if (type !== 'helpful') {
        await fetch('/api/knowledge-base/update-content', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            articleId,
            updateType: 'feedback'
          })
        })
      }

      setSubmitted(true)
      toast({
        title: 'フィードバックを送信しました',
        description: 'ご協力ありがとうございます'
      })

      // Reset after delay
      setTimeout(() => {
        setSubmitted(false)
        setFeedbackType(null)
        setSuggestion('')
      }, 5000)
    } catch (error) {
      console.error('Error submitting feedback:', error)
      toast({
        title: 'エラー',
        description: 'フィードバックの送信に失敗しました',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (submitted) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <div className="text-green-500 mb-2">
            <ThumbsUp className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-lg font-medium">
            フィードバックありがとうございました！
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            あなたのフィードバックは記事の改善に活用されます
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>この記事は役に立ちましたか？</CardTitle>
        <CardDescription>
          フィードバックをお寄せください
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!feedbackType ? (
          <div className="flex gap-4 justify-center">
            <Button
              variant="outline"
              onClick={() => submitFeedback('helpful')}
              disabled={submitting}
            >
              <ThumbsUp className="h-4 w-4 mr-2" />
              はい
            </Button>
            <Button
              variant="outline"
              onClick={() => setFeedbackType('not_helpful')}
              disabled={submitting}
            >
              <ThumbsDown className="h-4 w-4 mr-2" />
              いいえ
            </Button>
            <Button
              variant="outline"
              onClick={() => setFeedbackType('outdated')}
              disabled={submitting}
            >
              <Clock className="h-4 w-4 mr-2" />
              古い情報
            </Button>
            <Button
              variant="outline"
              onClick={() => setFeedbackType('incorrect')}
              disabled={submitting}
            >
              <AlertCircle className="h-4 w-4 mr-2" />
              誤り
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-sm">
              改善のためのご提案があればお聞かせください（任意）
            </p>
            <Textarea
              placeholder="どのように改善できるか教えてください..."
              value={suggestion}
              onChange={(e) => setSuggestion(e.target.value)}
              rows={3}
            />
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setFeedbackType(null)
                  setSuggestion('')
                }}
                disabled={submitting}
              >
                キャンセル
              </Button>
              <Button
                onClick={() => submitFeedback(feedbackType)}
                disabled={submitting}
              >
                送信
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
