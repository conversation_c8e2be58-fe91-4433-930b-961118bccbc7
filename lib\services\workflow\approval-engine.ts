import { createClient } from '@/lib/supabase/server';
import { WorkflowInstance, WorkflowTask, WorkflowState } from './types';
import { auditLogger } from '@/lib/services/audit';

export interface ApprovalLevel {
  level: number;
  name: string;
  approvers: ApproverConfig[];
  requireAll?: boolean; // If true, all approvers must approve
  escalationMinutes?: number;
  skipCondition?: string; // Condition to skip this level
}

export interface ApproverConfig {
  userId?: string;
  role?: string;
  department?: string;
  expression?: string; // Dynamic approver resolution
}

export interface ApprovalDecision {
  taskId: string;
  decision: 'approve' | 'reject' | 'escalate';
  comments?: string;
  attachments?: string[];
  delegateTo?: string; // For delegation
}

export interface ApprovalChain {
  id: string;
  name: string;
  description?: string;
  levels: ApprovalLevel[];
  timeoutAction: 'escalate' | 'auto_approve' | 'auto_reject';
  notificationTemplate?: string;
}

export class ApprovalEngine {
  /**
   * Create a multi-level approval workflow
   */
  async createApprovalChain(
    workflowInstanceId: string,
    approvalChain: ApprovalChain,
    context: Record<string, any>
  ): Promise<void> {
    const supabase = createClient();

    try {
      // Store approval chain configuration
      const { data: chainRecord, error } = await supabase
        .from('approval_chains')
        .insert({
          workflow_instance_id: workflowInstanceId,
          chain_config: approvalChain,
          current_level: 0,
          status: 'pending',
          context_data: context,
        })
        .select()
        .single();

      if (error || !chainRecord) {
        throw new Error('Failed to create approval chain');
      }

      // Start with the first level
      await this.processApprovalLevel(chainRecord.id, 0, context);

      await auditLogger.log({
        action: 'APPROVAL_CHAIN_CREATED',
        details: {
          chain_id: chainRecord.id,
          workflow_instance_id: workflowInstanceId,
          levels: approvalChain.levels.length,
        },
      });
    } catch (error) {
      console.error('Error creating approval chain:', error);
      throw error;
    }
  }

  /**
   * Process a specific approval level
   */
  private async processApprovalLevel(
    chainId: string,
    levelIndex: number,
    context: Record<string, any>
  ): Promise<void> {
    const supabase = createClient();

    // Get approval chain
    const { data: chain, error } = await supabase
      .from('approval_chains')
      .select('*')
      .eq('id', chainId)
      .single();

    if (error || !chain) {
      throw new Error('Approval chain not found');
    }

    const approvalConfig = chain.chain_config as ApprovalChain;
    const level = approvalConfig.levels[levelIndex];

    if (!level) {
      // No more levels, mark chain as complete
      await this.completeApprovalChain(chainId, 'approved');
      return;
    }

    // Check skip condition
    if (level.skipCondition && this.evaluateCondition(level.skipCondition, context)) {
      // Skip this level and move to next
      await this.processApprovalLevel(chainId, levelIndex + 1, context);
      return;
    }

    // Resolve approvers for this level
    const approvers = await this.resolveApprovers(level.approvers, context);

    // Create approval tasks for each approver
    const tasks = [];
    for (const approver of approvers) {
      const task = await this.createApprovalTask({
        chainId,
        level: levelIndex,
        approver,
        dueMinutes: level.escalationMinutes,
        requireAll: level.requireAll,
        context,
      });
      tasks.push(task);
    }

    // Update chain status
    await supabase
      .from('approval_chains')
      .update({
        current_level: levelIndex,
        current_level_tasks: tasks.map(t => t.id),
      })
      .eq('id', chainId);
  }

  /**
   * Create an individual approval task
   */
  private async createApprovalTask(params: {
    chainId: string;
    level: number;
    approver: { userId?: string; role?: string };
    dueMinutes?: number;
    requireAll?: boolean;
    context: Record<string, any>;
  }): Promise<WorkflowTask> {
    const supabase = createClient();
    const { chainId, level, approver, dueMinutes, requireAll, context } = params;

    const dueDate = dueMinutes
      ? new Date(Date.now() + dueMinutes * 60 * 1000).toISOString()
      : undefined;

    const { data: task, error } = await supabase
      .from('workflow_tasks')
      .insert({
        workflow_instance_id: context.workflow_instance_id,
        task_type: 'multi_level_approval',
        task_name: `Approval Required - Level ${level + 1}`,
        assigned_to: approver.userId,
        assigned_role: approver.role,
        status: 'pending',
        due_date: dueDate,
        task_data: {
          approval_chain_id: chainId,
          approval_level: level,
          require_all: requireAll,
          context: context,
        },
      })
      .select()
      .single();

    if (error || !task) {
      throw new Error('Failed to create approval task');
    }

    // Send notification
    await this.sendApprovalNotification(task, level);

    return task;
  }

  /**
   * Process an approval decision
   */
  async processApprovalDecision(
    decision: ApprovalDecision,
    userId: string
  ): Promise<void> {
    const supabase = createClient();

    try {
      // Get task details
      const { data: task, error: taskError } = await supabase
        .from('workflow_tasks')
        .select('*')
        .eq('id', decision.taskId)
        .single();

      if (taskError || !task) {
        throw new Error('Approval task not found');
      }

      // Update task with decision
      await supabase
        .from('workflow_tasks')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          completed_by: userId,
          task_data: {
            ...task.task_data,
            decision: decision.decision,
            comments: decision.comments,
            attachments: decision.attachments,
          },
        })
        .eq('id', decision.taskId);

      // Get approval chain
      const chainId = task.task_data.approval_chain_id;
      const { data: chain } = await supabase
        .from('approval_chains')
        .select('*')
        .eq('id', chainId)
        .single();

      if (!chain) {
        throw new Error('Approval chain not found');
      }

      // Process decision based on type
      switch (decision.decision) {
        case 'approve':
          await this.handleApproval(chain, task, userId);
          break;
        case 'reject':
          await this.handleRejection(chain, task, userId);
          break;
        case 'escalate':
          await this.handleEscalation(chain, task, userId);
          break;
      }

      // Log decision
      await auditLogger.log({
        action: 'APPROVAL_DECISION',
        details: {
          task_id: decision.taskId,
          decision: decision.decision,
          level: task.task_data.approval_level,
          comments: decision.comments,
        },
        user_id: userId,
      });
    } catch (error) {
      console.error('Error processing approval decision:', error);
      throw error;
    }
  }

  /**
   * Handle approval decision
   */
  private async handleApproval(
    chain: any,
    task: WorkflowTask,
    userId: string
  ): Promise<void> {
    const supabase = createClient();
    const approvalConfig = chain.chain_config as ApprovalChain;
    const currentLevel = chain.current_level;
    const level = approvalConfig.levels[currentLevel];

    // Check if all approvals are required at this level
    if (level.requireAll) {
      // Get all tasks for this level
      const { data: levelTasks } = await supabase
        .from('workflow_tasks')
        .select('*')
        .eq('task_data->approval_chain_id', chain.id)
        .eq('task_data->approval_level', currentLevel);

      const allApproved = levelTasks?.every(
        t => t.id === task.id || t.task_data.decision === 'approve'
      );

      if (!allApproved) {
        // Wait for other approvals
        return;
      }
    }

    // Move to next level or complete
    if (currentLevel < approvalConfig.levels.length - 1) {
      await this.processApprovalLevel(chain.id, currentLevel + 1, chain.context_data);
    } else {
      await this.completeApprovalChain(chain.id, 'approved');
    }
  }

  /**
   * Handle rejection decision
   */
  private async handleRejection(
    chain: any,
    task: WorkflowTask,
    userId: string
  ): Promise<void> {
    // Any rejection completes the chain as rejected
    await this.completeApprovalChain(chain.id, 'rejected');
  }

  /**
   * Handle escalation decision
   */
  private async handleEscalation(
    chain: any,
    task: WorkflowTask,
    userId: string
  ): Promise<void> {
    const supabase = createClient();
    const approvalConfig = chain.chain_config as ApprovalChain;
    const currentLevel = chain.current_level;

    // Check if there's a next level to escalate to
    if (currentLevel < approvalConfig.levels.length - 1) {
      // Cancel current level tasks
      await supabase
        .from('workflow_tasks')
        .update({ status: 'cancelled' })
        .eq('task_data->approval_chain_id', chain.id)
        .eq('task_data->approval_level', currentLevel)
        .eq('status', 'pending');

      // Move to next level
      await this.processApprovalLevel(chain.id, currentLevel + 1, chain.context_data);
    } else {
      // No more levels to escalate to
      await this.completeApprovalChain(chain.id, 'escalated');
    }
  }

  /**
   * Complete the approval chain
   */
  private async completeApprovalChain(
    chainId: string,
    outcome: 'approved' | 'rejected' | 'escalated' | 'timeout'
  ): Promise<void> {
    const supabase = createClient();

    // Update chain status
    const { data: chain, error } = await supabase
      .from('approval_chains')
      .update({
        status: 'completed',
        outcome,
        completed_at: new Date().toISOString(),
      })
      .eq('id', chainId)
      .select()
      .single();

    if (error || !chain) {
      throw new Error('Failed to complete approval chain');
    }

    // Update workflow instance context
    await supabase
      .from('workflow_instances')
      .update({
        context_data: {
          ...chain.context_data,
          approval_outcome: outcome,
          approval_chain_id: chainId,
        },
      })
      .eq('id', chain.workflow_instance_id);

    // Trigger workflow continuation
    await this.triggerWorkflowContinuation(chain.workflow_instance_id, outcome);
  }

  /**
   * Resolve approvers based on configuration
   */
  private async resolveApprovers(
    approverConfigs: ApproverConfig[],
    context: Record<string, any>
  ): Promise<{ userId?: string; role?: string }[]> {
    const approvers = [];
    const supabase = createClient();

    for (const config of approverConfigs) {
      if (config.userId) {
        approvers.push({ userId: config.userId });
      } else if (config.role) {
        // Resolve role-based approver
        const department = config.department || context.department;
        
        // Find users with this role in the department
        const { data: users } = await supabase
          .from('staff')
          .select('id')
          .eq('role_id', await this.getRoleId(config.role))
          .eq('division_id', department);

        if (users && users.length > 0) {
          // For now, assign to first user found
          // In production, might want round-robin or load balancing
          approvers.push({ userId: users[0].id });
        } else {
          // Fallback to role assignment
          approvers.push({ role: `${config.role}:${department}` });
        }
      } else if (config.expression) {
        // Evaluate dynamic expression
        const userId = this.evaluateExpression(config.expression, context);
        if (userId) {
          approvers.push({ userId });
        }
      }
    }

    return approvers;
  }

  /**
   * Helper to get role ID from role name
   */
  private async getRoleId(roleName: string): Promise<string | null> {
    const supabase = createClient();
    const { data } = await supabase
      .from('roles')
      .select('id')
      .eq('name', roleName)
      .single();
    
    return data?.id || null;
  }

  /**
   * Evaluate a condition expression
   */
  private evaluateCondition(condition: string, context: Record<string, any>): boolean {
    try {
      const func = new Function('context', `return ${condition}`);
      return func(context);
    } catch (error) {
      console.error('Error evaluating condition:', error);
      return false;
    }
  }

  /**
   * Evaluate an expression to get a value
   */
  private evaluateExpression(expression: string, context: Record<string, any>): any {
    try {
      const func = new Function('context', `return ${expression}`);
      return func(context);
    } catch (error) {
      console.error('Error evaluating expression:', error);
      return null;
    }
  }

  /**
   * Send approval notification
   */
  private async sendApprovalNotification(task: WorkflowTask, level: number): Promise<void> {
    // Integration with notification service
    console.log(`Sending approval notification for task ${task.id} at level ${level}`);
  }

  /**
   * Trigger workflow continuation after approval chain completion
   */
  private async triggerWorkflowContinuation(
    workflowInstanceId: string,
    outcome: string
  ): Promise<void> {
    // This would trigger the workflow engine to continue processing
    console.log(`Triggering workflow continuation for ${workflowInstanceId} with outcome ${outcome}`);
  }

  /**
   * Handle approval timeout
   */
  async handleApprovalTimeout(chainId: string): Promise<void> {
    const supabase = createClient();

    const { data: chain } = await supabase
      .from('approval_chains')
      .select('*')
      .eq('id', chainId)
      .single();

    if (!chain) return;

    const config = chain.chain_config as ApprovalChain;

    switch (config.timeoutAction) {
      case 'escalate':
        // Escalate to next level
        const currentLevel = chain.current_level;
        if (currentLevel < config.levels.length - 1) {
          await this.processApprovalLevel(chainId, currentLevel + 1, chain.context_data);
        } else {
          await this.completeApprovalChain(chainId, 'timeout');
        }
        break;
      
      case 'auto_approve':
        await this.completeApprovalChain(chainId, 'approved');
        break;
      
      case 'auto_reject':
        await this.completeApprovalChain(chainId, 'rejected');
        break;
    }
  }

  /**
   * Get approval chain status
   */
  async getApprovalChainStatus(chainId: string): Promise<any> {
    const supabase = createClient();

    const { data: chain, error } = await supabase
      .from('approval_chains')
      .select(`
        *,
        workflow_tasks (
          id,
          task_name,
          assigned_to,
          assigned_role,
          status,
          task_data,
          completed_at,
          completed_by
        )
      `)
      .eq('id', chainId)
      .single();

    if (error || !chain) {
      throw new Error('Approval chain not found');
    }

    const config = chain.chain_config as ApprovalChain;
    const levels = config.levels.map((level, index) => {
      const levelTasks = chain.workflow_tasks.filter(
        (task: any) => task.task_data.approval_level === index
      );

      return {
        level: index + 1,
        name: level.name,
        status: this.getLevelStatus(levelTasks),
        approvers: levelTasks.map((task: any) => ({
          taskId: task.id,
          assignedTo: task.assigned_to,
          assignedRole: task.assigned_role,
          status: task.status,
          decision: task.task_data.decision,
          completedAt: task.completed_at,
          completedBy: task.completed_by,
        })),
      };
    });

    return {
      id: chain.id,
      status: chain.status,
      outcome: chain.outcome,
      currentLevel: chain.current_level + 1,
      totalLevels: config.levels.length,
      levels,
      startedAt: chain.created_at,
      completedAt: chain.completed_at,
    };
  }

  /**
   * Get level status based on tasks
   */
  private getLevelStatus(tasks: any[]): string {
    if (tasks.length === 0) return 'pending';
    
    const hasRejection = tasks.some(t => t.task_data.decision === 'reject');
    if (hasRejection) return 'rejected';
    
    const allCompleted = tasks.every(t => t.status === 'completed');
    if (allCompleted) return 'approved';
    
    const hasInProgress = tasks.some(t => t.status === 'in_progress');
    if (hasInProgress) return 'in_progress';
    
    return 'pending';
  }
}

// Export singleton instance
export const approvalEngine = new ApprovalEngine();
