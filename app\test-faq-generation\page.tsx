'use client';

import React from 'react';
import { FAQGenerationManager } from '@/components/knowledge-base/faq-generation-manager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Bot, MessageSquare, BookOpen, Search, CheckCircle } from 'lucide-react';

export default function TestFAQGenerationPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Bot className="h-8 w-8" />
          AI-Powered FAQ Generation System
        </h1>
        <p className="text-muted-foreground">
          Generate intelligent FAQs from knowledge base articles and user search patterns
        </p>
      </div>

      {/* Feature Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              KB-Based Generation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Analyze knowledge base articles to generate relevant FAQs automatically
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search Pattern Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Create FAQs from failed searches and identified content gaps
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Bilingual Support
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Generate FAQs in both Japanese and English for all users
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="generate" className="space-y-4">
        <TabsList>
          <TabsTrigger value="generate">Generate FAQs</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        <TabsContent value="generate">
          <FAQGenerationManager />
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Key Features</CardTitle>
              <CardDescription>
                Advanced capabilities of the FAQ generation system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">AI-Powered Analysis</h4>
                    <p className="text-sm text-muted-foreground">
                      Uses OpenAI to analyze content and generate natural, helpful FAQs
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Confidence Scoring</h4>
                    <p className="text-sm text-muted-foreground">
                      Each FAQ is scored for quality and relevance, allowing filtering
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Duplicate Detection</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically merges similar FAQs to avoid redundancy
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Usage Analytics</h4>
                    <p className="text-sm text-muted-foreground">
                      Tracks FAQ usage and automatically suggests improvements
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Category Management</h4>
                    <p className="text-sm text-muted-foreground">
                      Organize FAQs by service categories for better discoverability
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Generation Sources</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium mb-2">Knowledge Base Articles</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge>Group Mail Management</Badge>
                  <Badge>Mailbox Configuration</Badge>
                  <Badge>SharePoint Access</Badge>
                  <Badge>PC Administration</Badge>
                  <Badge>Password Reset</Badge>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Search Patterns</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">Failed Searches</Badge>
                  <Badge variant="secondary">Content Gaps</Badge>
                  <Badge variant="secondary">Common Queries</Badge>
                  <Badge variant="secondary">User Feedback</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integration" className="space-y-4">
          <Alert>
            <AlertTitle>Integration with IT Helpdesk</AlertTitle>
            <AlertDescription>
              The FAQ generation system is fully integrated with the IT Helpdesk platform,
              enhancing user support through intelligent content creation.
            </AlertDescription>
          </Alert>

          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">1. Content Analysis</h4>
                <p className="text-sm text-muted-foreground">
                  The system analyzes existing knowledge base articles to identify common questions
                </p>
              </div>

              <div>
                <h4 className="font-medium">2. Pattern Recognition</h4>
                <p className="text-sm text-muted-foreground">
                  Failed searches and content gaps are analyzed to identify missing FAQs
                </p>
              </div>

              <div>
                <h4 className="font-medium">3. AI Generation</h4>
                <p className="text-sm text-muted-foreground">
                  OpenAI generates natural language questions and answers in both languages
                </p>
              </div>

              <div>
                <h4 className="font-medium">4. Quality Filtering</h4>
                <p className="text-sm text-muted-foreground">
                  FAQs are scored and filtered based on confidence and relevance
                </p>
              </div>

              <div>
                <h4 className="font-medium">5. Chatbot Integration</h4>
                <p className="text-sm text-muted-foreground">
                  Approved FAQs are automatically available in the AI chatbot for instant help
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Database Schema</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto">
{`chatbot_faq
├── id (UUID)
├── question_ja (TEXT)
├── question_en (TEXT)
├── answer_ja (TEXT)
├── answer_en (TEXT)
├── category (VARCHAR)
├── keywords (TEXT[])
├── usage_count (INTEGER)
├── is_active (BOOLEAN)
├── source_type (VARCHAR)
├── source_article_ids (UUID[])
├── confidence_score (DECIMAL)
└── timestamps`}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}