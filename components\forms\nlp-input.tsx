'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { nlpFormService } from '@/lib/services/nlp-form-service'
import { Loader2, MessageSquare, ArrowRight, Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'

interface NLPInputProps {
  onFormSuggested: (formType: string, extractedData: Record<string, any>) => void
  availableForms: string[]
  language?: 'ja' | 'en'
  className?: string
}

export function NLPInput({
  onFormSuggested,
  availableForms,
  language = 'ja',
  className
}: NLPInputProps) {
  const [input, setInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [parsedResult, setParsedResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const exampleRequests = language === 'ja' ? [
    '山田太郎さんをITシステムグループのグループメールに追加してください',
    '田中花子のM365パスワードをリセットしたい',
    '営業部の佐藤さんに経理フォルダのアクセス権を付与',
    'PC M241234の管理者権限が必要です'
  ] : [
    'Add John Smith to the IT System Group email list',
    'Reset password for Mary Johnson M365 account',
    'Grant access to accounting folder for sales department staff',
    'Need admin privileges for PC M241234'
  ]

  const handleParse = async () => {
    if (!input.trim()) return

    setIsProcessing(true)
    setError(null)
    setParsedResult(null)

    try {
      const result = await nlpFormService.parseNaturalLanguageInput(
        input,
        {
          language,
          availableForms
        }
      )

      setParsedResult(result)
      
      if (result.suggestedForm && result.confidence > 0.5) {
        onFormSuggested(result.suggestedForm, result.extractedData)
      }
    } catch (err) {
      setError('Failed to parse input. Please try again.')
      console.error('NLP parsing error:', err)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleExampleClick = (example: string) => {
    setInput(example)
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          {language === 'ja' ? '自然言語でリクエスト' : 'Natural Language Request'}
        </CardTitle>
        <CardDescription>
          {language === 'ja' 
            ? 'お手伝いが必要なことを自然な言葉で入力してください'
            : 'Type what you need help with in natural language'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={
              language === 'ja'
                ? '例: 山田さんをグループメールに追加したい...'
                : 'e.g., I need to add Yamada-san to the group email...'
            }
            className="min-h-[100px]"
          />
          <div className="flex justify-between items-center">
            <Button
              onClick={handleParse}
              disabled={!input.trim() || isProcessing}
              className="gap-2"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {language === 'ja' ? '解析中...' : 'Parsing...'}
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  {language === 'ja' ? '解析する' : 'Parse Request'}
                </>
              )}
            </Button>
            <span className="text-sm text-muted-foreground">
              {input.length} / 500
            </span>
          </div>
        </div>

        {/* Example requests */}
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">
            {language === 'ja' ? '例:' : 'Examples:'}
          </p>
          <div className="flex flex-wrap gap-2">
            {exampleRequests.map((example, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="text-xs h-auto py-1 px-2"
                onClick={() => handleExampleClick(example)}
              >
                {example}
              </Button>
            ))}
          </div>
        </div>

        {/* Parsed result */}
        {parsedResult && (
          <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">
                {language === 'ja' ? '解析結果' : 'Parsed Result'}
              </h4>
              <Badge variant={parsedResult.confidence > 0.7 ? 'default' : 'secondary'}>
                {Math.round(parsedResult.confidence * 100)}% confidence
              </Badge>
            </div>
            
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">
                  {language === 'ja' ? '意図: ' : 'Intent: '}
                </span>
                <span className="text-muted-foreground">{parsedResult.intent}</span>
              </div>
              
              {parsedResult.suggestedForm && (
                <div>
                  <span className="font-medium">
                    {language === 'ja' ? '推奨フォーム: ' : 'Suggested Form: '}
                  </span>
                  <Badge variant="outline">{parsedResult.suggestedForm}</Badge>
                </div>
              )}
              
              {Object.keys(parsedResult.extractedData).length > 0 && (
                <div>
                  <span className="font-medium">
                    {language === 'ja' ? '抽出データ:' : 'Extracted Data:'}
                  </span>
                  <pre className="mt-1 p-2 bg-background rounded text-xs overflow-auto">
                    {JSON.stringify(parsedResult.extractedData, null, 2)}
                  </pre>
                </div>
              )}
            </div>

            {parsedResult.suggestedForm && parsedResult.confidence > 0.5 && (
              <Button 
                className="w-full gap-2" 
                onClick={() => onFormSuggested(parsedResult.suggestedForm, parsedResult.extractedData)}
              >
                <ArrowRight className="h-4 w-4" />
                {language === 'ja' 
                  ? `${parsedResult.suggestedForm}フォームを開く`
                  : `Open ${parsedResult.suggestedForm} form`
                }
              </Button>
            )}
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md text-sm">
            {error}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
