'use client'

import React from 'react'
import { AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { DetectedError } from '@/lib/services/error-detection-service'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface ErrorIndicatorProps {
  errors: DetectedError[]
  isChecking: boolean
  onAcceptSuggestion?: (suggestion: string) => void
  className?: string
}

export function ErrorIndicator({
  errors,
  isChecking,
  onAcceptSuggestion,
  className
}: ErrorIndicatorProps) {
  if (isChecking) {
    return (
      <div className={cn("flex items-center gap-2 text-sm text-muted-foreground", className)}>
        <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
        <span>検証中...</span>
      </div>
    )
  }

  if (errors.length === 0) {
    return null
  }

  const highestConfidenceError = errors.reduce((prev, current) => 
    current.confidence > prev.confidence ? current : prev
  )

  const getErrorIcon = (confidence: number) => {
    if (confidence >= 0.8) return <AlertCircle className="h-4 w-4 text-red-500" />
    if (confidence >= 0.5) return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    return <Info className="h-4 w-4 text-blue-500" />
  }

  return (
    <TooltipProvider>
      <div className={cn("space-y-2", className)}>
        {errors.map((error, index) => (
          <div
            key={index}
            className={cn(
              "flex items-start gap-2 p-2 rounded-md",
              error.confidence >= 0.8 ? "bg-red-50 dark:bg-red-900/20" :
              error.confidence >= 0.5 ? "bg-yellow-50 dark:bg-yellow-900/20" :
              "bg-blue-50 dark:bg-blue-900/20"
            )}
          >
            {getErrorIcon(error.confidence)}
            <div className="flex-1 space-y-1">
              <p className="text-sm font-medium">{error.message}</p>
              {error.suggestions.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {error.suggestions.map((suggestion, sugIndex) => (
                    <Tooltip key={sugIndex}>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-6 text-xs"
                          onClick={() => onAcceptSuggestion?.(suggestion)}
                        >
                          {suggestion}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>クリックして適用</p>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                </div>
              )}
            </div>
            <span className="text-xs text-muted-foreground">
              {Math.round(error.confidence * 100)}%
            </span>
          </div>
        ))}
      </div>
    </TooltipProvider>
  )
}

interface FieldWithErrorDetectionProps {
  children: React.ReactElement
  errors: DetectedError[]
  isChecking: boolean
  hasErrors: boolean
  errorRate?: number
  showStats?: boolean
}

export function FieldWithErrorDetection({
  children,
  errors,
  isChecking,
  hasErrors,
  errorRate = 0,
  showStats = false
}: FieldWithErrorDetectionProps) {
  const borderColor = hasErrors 
    ? errors[0].confidence >= 0.8 ? 'border-red-500' : 'border-yellow-500'
    : 'border-input'

  return (
    <div className="space-y-2">
      <div className="relative">
        {React.cloneElement(children, {
          className: cn(
            children.props.className,
            borderColor,
            hasErrors && 'animate-pulse-once'
          )
        })}
        {(hasErrors || isChecking) && (
          <div className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
            {isChecking ? (
              <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
            ) : hasErrors ? (
              errors[0].confidence >= 0.8 ? (
                <AlertCircle className="h-4 w-4 text-red-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              )
            ) : null}
          </div>
        )}
      </div>
      {showStats && errorRate > 0 && (
        <p className="text-xs text-muted-foreground">
          このフィールドのエラー率: {errorRate.toFixed(1)}%
        </p>
      )}
    </div>
  )
}
