-- AI Agent System Tables

-- Agent registry
CREATE TABLE IF NOT EXISTS ai_agents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id TEXT UNIQUE NOT NULL,
  category TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  capabilities JSONB NOT NULL,
  status TEXT DEFAULT 'idle' CHECK (status IN ('active', 'idle', 'error', 'disabled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE
);

-- Agent activity logs
CREATE TABLE IF NOT EXISTS agent_activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id TEXT NOT NULL,
  action TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agent system logs
CREATE TABLE IF NOT EXISTS agent_system_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  active_agents INTEGER NOT NULL,
  total_agents INTEGER NOT NULL,
  articles_created_24h INTEGER DEFAULT 0,
  articles_updated_24h INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agent learning history
CREATE TABLE IF NOT EXISTS agent_learning_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id TEXT NOT NULL,
  learning_type TEXT NOT NULL,
  learning_data JSONB NOT NULL,
  applied BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Web scraping cache
CREATE TABLE IF NOT EXISTS agent_web_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  url TEXT NOT NULL,
  content TEXT NOT NULL,
  title TEXT,
  relevance_score NUMERIC(3,2),
  agent_id TEXT NOT NULL,
  topic TEXT,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(url, agent_id)
);

-- Agent-generated educational content
CREATE TABLE IF NOT EXISTS agent_educational_content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id TEXT NOT NULL,
  content_type TEXT CHECK (content_type IN ('guide', 'tutorial', 'wizard', 'faq', 'troubleshooting')),
  title TEXT NOT NULL,
  title_jp TEXT NOT NULL,
  content TEXT NOT NULL,
  content_jp TEXT NOT NULL,
  difficulty TEXT CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
  estimated_time INTEGER, -- in minutes
  interactive BOOLEAN DEFAULT false,
  steps JSONB,
  related_topics TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Interactive learning paths
CREATE TABLE IF NOT EXISTS agent_learning_paths (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id TEXT NOT NULL,
  category TEXT NOT NULL,
  path_name TEXT NOT NULL,
  description TEXT,
  modules JSONB NOT NULL, -- Array of educational content IDs in order
  prerequisites TEXT[],
  estimated_total_time INTEGER, -- in minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User progress tracking
CREATE TABLE IF NOT EXISTS user_learning_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES staff(id),
  content_id UUID REFERENCES agent_educational_content(id),
  status TEXT CHECK (status IN ('not_started', 'in_progress', 'completed')),
  progress_percentage INTEGER DEFAULT 0,
  completed_steps JSONB,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  feedback TEXT,
  UNIQUE(user_id, content_id)
);

-- Initialize AI agents
INSERT INTO ai_agents (agent_id, category, name, description, capabilities) VALUES
('group-mail-agent', 'group_mail', 'Group Mail Specialist', 'Expert in email distribution lists and group mail management', 
 '{"webScraping": true, "contentTranslation": true, "interactiveGuides": true, "troubleshooting": true}'),
('sharepoint-agent', 'sharepoint', 'SharePoint Specialist', 'Expert in SharePoint document management and permissions',
 '{"webScraping": true, "contentTranslation": true, "interactiveGuides": true, "videoTutorials": true}'),
('password-reset-agent', 'password_reset', 'Security & Password Specialist', 'Expert in password management and account security',
 '{"webScraping": true, "contentTranslation": true, "interactiveGuides": true, "videoTutorials": true}'),
('pc-admin-agent', 'pc_admin', 'PC Administration Specialist', 'Expert in Windows administration and software management',
 '{"webScraping": true, "contentTranslation": true, "interactiveGuides": true, "troubleshooting": true}'),
('mailbox-agent', 'mailbox', 'Mailbox Management Specialist', 'Expert in email account configuration and management',
 '{"webScraping": true, "contentTranslation": true, "interactiveGuides": true, "troubleshooting": true}'),
('master-knowledge-agent', 'general', 'Master Knowledge Coordinator', 'Oversees all IT helpdesk knowledge and guides users',
 '{"webScraping": true, "contentTranslation": true, "interactiveGuides": true, "videoTutorials": true}');
-- Create indexes
CREATE INDEX idx_agent_activity_logs_agent_id ON agent_activity_logs(agent_id);
CREATE INDEX idx_agent_activity_logs_created_at ON agent_activity_logs(created_at);
CREATE INDEX idx_agent_web_cache_agent_id ON agent_web_cache(agent_id);
CREATE INDEX idx_agent_web_cache_topic ON agent_web_cache(topic);
CREATE INDEX idx_agent_educational_content_agent_id ON agent_educational_content(agent_id);
CREATE INDEX idx_agent_educational_content_category ON agent_educational_content(content_type);
CREATE INDEX idx_user_learning_progress_user_id ON user_learning_progress(user_id);
CREATE INDEX idx_user_learning_progress_status ON user_learning_progress(status);

-- RLS Policies
ALTER TABLE ai_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_educational_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_progress ENABLE ROW LEVEL SECURITY;

-- Agents visible to all authenticated users
CREATE POLICY "view_agents" ON ai_agents
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Educational content visible to all authenticated users
CREATE POLICY "view_educational_content" ON agent_educational_content
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Users can view and update their own progress
CREATE POLICY "manage_own_progress" ON user_learning_progress
  FOR ALL
  USING (auth.uid() = (SELECT auth_id FROM staff WHERE id = user_id));

-- Admins can view all agent logs
CREATE POLICY "admin_view_logs" ON agent_activity_logs
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Function to get agent performance metrics
CREATE OR REPLACE FUNCTION get_agent_performance_metrics(
  p_time_range INTERVAL DEFAULT INTERVAL '1 day'
)
RETURNS TABLE (
  agent_id TEXT,
  articles_created BIGINT,
  articles_updated BIGINT,
  educational_content_created BIGINT,
  web_pages_scraped BIGINT,
  active_time_percentage NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.agent_id,
    COUNT(DISTINCT ac.id) FILTER (WHERE ac.metadata->>'createdByAgent' = a.agent_id) as articles_created,
    COUNT(DISTINCT ac2.id) FILTER (WHERE ac2.metadata->>'lastUpdatedByAgent' = a.agent_id) as articles_updated,
    COUNT(DISTINCT aec.id) as educational_content_created,
    COUNT(DISTINCT awc.id) as web_pages_scraped,
    ROUND(
      EXTRACT(EPOCH FROM (COALESCE(a.last_active_at, NOW()) - (NOW() - p_time_range))) / 
      EXTRACT(EPOCH FROM p_time_range) * 100, 2
    ) as active_time_percentage
  FROM ai_agents a
  LEFT JOIN kb_articles ac ON ac.created_at >= NOW() - p_time_range
  LEFT JOIN kb_articles ac2 ON ac2.updated_at >= NOW() - p_time_range
  LEFT JOIN agent_educational_content aec ON aec.agent_id = a.agent_id AND aec.created_at >= NOW() - p_time_range
  LEFT JOIN agent_web_cache awc ON awc.agent_id = a.agent_id AND awc.last_updated >= NOW() - p_time_range
  GROUP BY a.agent_id, a.last_active_at;
END;
$$;

-- Function to find best educational content for a topic
CREATE OR REPLACE FUNCTION find_best_educational_content(
  p_topic TEXT,
  p_category TEXT DEFAULT NULL,
  p_difficulty TEXT DEFAULT 'beginner'
)
RETURNS TABLE (
  content_id UUID,
  agent_id TEXT,
  title TEXT,
  title_jp TEXT,
  content_type TEXT,
  difficulty TEXT,
  estimated_time INTEGER,
  relevance_score NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    aec.id as content_id,
    aec.agent_id,
    aec.title,
    aec.title_jp,
    aec.content_type,
    aec.difficulty,
    aec.estimated_time,
    -- Calculate relevance score based on title/content match
    (
      CASE 
        WHEN LOWER(aec.title) LIKE '%' || LOWER(p_topic) || '%' THEN 1.0
        WHEN LOWER(aec.content) LIKE '%' || LOWER(p_topic) || '%' THEN 0.8
        ELSE 0.5
      END
    ) as relevance_score
  FROM agent_educational_content aec
  WHERE 
    (p_category IS NULL OR aec.agent_id = p_category || '-agent')
    AND aec.difficulty = p_difficulty
    AND (
      LOWER(aec.title) LIKE '%' || LOWER(p_topic) || '%'
      OR LOWER(aec.content) LIKE '%' || LOWER(p_topic) || '%'
      OR p_topic = ANY(aec.related_topics)
    )
  ORDER BY relevance_score DESC, aec.created_at DESC
  LIMIT 10;
END;
$$;

-- Trigger to update article metadata when AI agents modify content
CREATE OR REPLACE FUNCTION update_article_agent_metadata()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the metadata to track agent modifications
  IF NEW.metadata IS NULL THEN
    NEW.metadata = '{}'::jsonb;
  END IF;
  
  -- Check if this is an agent-initiated update
  IF current_setting('app.current_agent_id', true) IS NOT NULL THEN
    NEW.metadata = NEW.metadata || 
      jsonb_build_object(
        'lastUpdatedByAgent', current_setting('app.current_agent_id', true),
        'agentUpdateTime', NOW()
      );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER article_agent_metadata_trigger
BEFORE UPDATE ON kb_articles
FOR EACH ROW
EXECUTE FUNCTION update_article_agent_metadata();
