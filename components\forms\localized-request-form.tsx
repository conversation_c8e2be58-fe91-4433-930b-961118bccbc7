// Localized Request Form Component
'use client';

import { useState } from 'react';
import { useLanguage } from '@/lib/i18n/language-provider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { JapaneseDatePicker } from '@/components/ui/japanese-date-picker';
import { Textarea } from '@/components/ui/textarea';

export function LocalizedRequestForm() {
  const { t, locale } = useLanguage();
  const [formData, setFormData] = useState({
    requestType: '',
    department: '',
    priority: '',
    requestDate: new Date(),
    description: ''
  });

  const priorities = [
    { value: 'high', label: t('request.high') },
    { value: 'medium', label: t('request.medium') },
    { value: 'low', label: t('request.low') }
  ];

  const departments = [
    { value: 'corporate_planning', label: t('departments.corporatePlanning') },
    { value: 'human_resources', label: t('departments.humanResources') },
    { value: 'it_systems', label: t('departments.itSystems') }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {t('request.newRequest')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="department">
              {t('request.department')}
            </Label>
            <Select
              value={formData.department}
              onValueChange={(value) => setFormData({ ...formData, department: value })}
            >
              <SelectTrigger id="department">
                <SelectValue placeholder={t('common.search')} />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept.value} value={dept.value}>
                    {dept.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="priority">
              {t('request.priority')}
            </Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData({ ...formData, priority: value })}
            >
              <SelectTrigger id="priority">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {priorities.map((priority) => (
                  <SelectItem key={priority.value} value={priority.value}>
                    {priority.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t('request.createdAt')}</Label>
            <JapaneseDatePicker
              value={formData.requestDate}
              onChange={(date) => setFormData({ ...formData, requestDate: date || new Date() })}
              showEra={locale === 'ja'}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">
              {t('request.description')}
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={4}
              className="resize-none"
            />
          </div>

          <div className="flex gap-4 justify-end">
            <Button type="button" variant="outline">
              {t('common.cancel')}
            </Button>
            <Button type="submit">
              {t('common.submit')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}