import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { chatbotService } from '@/lib/services/chatbot-service';
import { EmbeddingsService } from '@/lib/services/embeddings-service';

interface KBChatbotEnhancement {
  suggestArticles: (query: string) => Promise<any[]>;
  findRelevantFAQs: (query: string, category?: string) => Promise<any[]>;
  getContextualHelp: (context: any) => Promise<string>;
}

export class KBChatbotIntegration implements KBChatbotEnhancement {
  private embeddingsService: EmbeddingsService;
  private static instance: KBChatbotIntegration;

  private constructor() {
    this.embeddingsService = EmbeddingsService.getInstance();
  }

  static getInstance(): KBChatbotIntegration {
    if (!this.instance) {
      this.instance = new KBChatbotIntegration();
    }
    return this.instance;
  }

  /**
   * Suggest relevant KB articles based on user query
   */
  async suggestArticles(query: string): Promise<any[]> {
    try {
      // Use semantic search to find relevant articles
      const results = await this.embeddingsService.semanticSearch(query, {
        matchThreshold: 0.7,
        matchCount: 3
      });

      return results.map(article => ({
        id: article.id,
        title: article.title_en || article.title_jp,
        preview: (article.content_en || article.content_jp || '').substring(0, 150) + '...',
        category: article.category,
        relevance: article.relevance,
        url: `/knowledge-base/article/${article.id}`
      }));
    } catch (error) {
      console.error('Error suggesting articles:', error);
      return [];
    }
  }

  /**
   * Find relevant FAQs based on query and optional category
   */
  async findRelevantFAQs(query: string, category?: string): Promise<any[]> {
    try {
      // First, try to find FAQs with matching keywords
      let faqQuery = supabase
        .from('chatbot_faq')
        .select('*')
        .eq('is_active', true);

      if (category) {
        faqQuery = faqQuery.eq('category', category);
      }

      // Search in keywords array
      const { data: keywordMatches } = await faqQuery
        .contains('keywords', [query.toLowerCase()])
        .limit(5);

      // If no keyword matches, do a text search
      if (!keywordMatches || keywordMatches.length === 0) {
        const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 2);
        
        const { data: textMatches } = await supabase
          .from('chatbot_faq')
          .select('*')
          .eq('is_active', true)
          .or(searchTerms.map(term => 
            `question_en.ilike.%${term}%,question_ja.ilike.%${term}%`
          ).join(','))
          .limit(5);

        return textMatches || [];
      }

      return keywordMatches;
    } catch (error) {
      console.error('Error finding FAQs:', error);
      return [];
    }
  }

  /**
   * Get contextual help based on current form/page context
   */
  async getContextualHelp(context: {
    currentForm?: string;
    currentPage?: string;
    serviceCategory?: string;
    userRole?: string;
  }): Promise<string> {
    try {
      // Build a context-aware query
      let contextQuery = '';
      
      if (context.currentForm) {
        contextQuery += `form: ${context.currentForm} `;
      }
      if (context.serviceCategory) {
        contextQuery += `category: ${context.serviceCategory} `;
      }
      if (context.currentPage) {
        contextQuery += `page: ${context.currentPage} `;
      }

      // Find relevant articles
      const articles = await this.suggestArticles(contextQuery);
      const faqs = await this.findRelevantFAQs(contextQuery, context.serviceCategory);

      // Build help message
      let helpMessage = '';

      if (articles.length > 0) {
        helpMessage += 'I found these helpful articles:\n';
        articles.forEach((article, index) => {
          helpMessage += `${index + 1}. ${article.title}\n`;
        });
      }

      if (faqs.length > 0) {
        helpMessage += '\nFrequently asked questions:\n';
        faqs.slice(0, 3).forEach((faq, index) => {
          helpMessage += `${index + 1}. ${faq.question_en || faq.question_ja}\n`;
        });
      }

      if (!helpMessage) {
        helpMessage = 'I can help you with your request. What would you like to know?';
      }

      return helpMessage;
    } catch (error) {
      console.error('Error getting contextual help:', error);
      return 'How can I assist you today?';
    }
  }

  /**
   * Enhance chatbot response with KB content
   */
  async enhanceChatbotResponse(
    userMessage: string,
    context: any
  ): Promise<{
    message: string;
    articles?: any[];
    faqs?: any[];
    suggestions?: string[];
  }> {
    try {
      // Get base response from chatbot
      const baseResponse = await chatbotService.sendMessage(userMessage);

      // Find relevant KB content
      const articles = await this.suggestArticles(userMessage);
      const faqs = await this.findRelevantFAQs(userMessage, context.serviceCategory);

      // Generate smart suggestions based on context
      const suggestions = await this.generateSmartSuggestions(userMessage, context);

      return {
        message: baseResponse.content,
        articles: articles.slice(0, 2),
        faqs: faqs.slice(0, 3),
        suggestions
      };
    } catch (error) {
      console.error('Error enhancing chatbot response:', error);
      return {
        message: 'I apologize, but I encountered an error. Please try again.',
        suggestions: ['View help articles', 'Contact support', 'Try a different question']
      };
    }
  }

  /**
   * Generate smart suggestions based on user input and context
   */
  private async generateSmartSuggestions(
    userMessage: string,
    context: any
  ): Promise<string[]> {
    const suggestions: string[] = [];

    // Analyze user intent
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('how') || lowerMessage.includes('どう')) {
      suggestions.push('Show me a tutorial');
      suggestions.push('View step-by-step guide');
    }

    if (lowerMessage.includes('error') || lowerMessage.includes('problem')) {
      suggestions.push('Troubleshooting guide');
      suggestions.push('Common issues');
    }

    if (context.serviceCategory) {
      suggestions.push(`More about ${context.serviceCategory}`);
      suggestions.push(`${context.serviceCategory} FAQs`);
    }

    // Add default suggestions if none were generated
    if (suggestions.length === 0) {
      suggestions.push('View all help topics');
      suggestions.push('Contact IT support');
      suggestions.push('Search knowledge base');
    }

    return suggestions.slice(0, 3);
  }

  /**
   * Track article views from chatbot
   */
  async trackArticleView(articleId: string, source: string = 'chatbot'): Promise<void> {
    try {
      await supabase.from('kb_article_views').insert({
        article_id: articleId,
        source,
        viewed_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error tracking article view:', error);
    }
  }

  /**
   * Update FAQ usage from chatbot interaction
   */
  async trackFAQUsage(faqId: string): Promise<void> {
    try {
      await supabase.rpc('increment_faq_usage', { faq_id: faqId });
    } catch (error) {
      console.error('Error tracking FAQ usage:', error);
    }
  }
}