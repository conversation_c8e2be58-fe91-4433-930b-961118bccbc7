// Supabase Connection Pool Manager
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

export class ConnectionPoolManager {
  private static instance: ConnectionPoolManager;
  private pools: Map<string, any> = new Map();
  
  private constructor() {}

  static getInstance(): ConnectionPoolManager {
    if (!ConnectionPoolManager.instance) {
      ConnectionPoolManager.instance = new ConnectionPoolManager();
    }
    return ConnectionPoolManager.instance;
  }

  // Get optimized Supabase client with connection pooling
  getPooledClient(options?: { poolSize?: number }) {
    const key = `pool_${options?.poolSize || 25}`;
    
    if (!this.pools.has(key)) {
      const client = createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          db: {
            schema: 'public'
          },
          auth: {
            persistSession: true,
            autoRefreshToken: true
          },
          global: {
            headers: {
              'x-connection-pool': 'true',
              'x-pool-size': String(options?.poolSize || 25)
            }
          }
        }
      );
      
      this.pools.set(key, client);
    }
    
    return this.pools.get(key);
  }

  // Monitor pool health
  async checkPoolHealth() {
    const results = [];
    
    for (const [key, client] of this.pools) {
      try {
        const start = Date.now();
        await client.from('_health_check').select('1').single();
        const duration = Date.now() - start;
        
        results.push({
          pool: key,
          status: 'healthy',
          responseTime: duration
        });
      } catch (error) {
        results.push({
          pool: key,
          status: 'unhealthy',
          error: error.message
        });
      }
    }
    
    return results;
  }
}

export const poolManager = ConnectionPoolManager.getInstance();