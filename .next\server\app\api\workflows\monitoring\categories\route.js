"use strict";(()=>{var e={};e.id=5496,e.ids=[5496],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3044:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>l});var a={};r.r(a),r.d(a,{GET:()=>p});var s=r(42706),o=r(28203),n=r(45994),i=r(39187),c=r(73865),u=r(44512);async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("timeRange")||"7d",a=await (0,u.UL)(),s=(0,c.createServerSupabase)(a),o=new Date,n=new Date;switch(r){case"24h":n.setDate(o.getDate()-1);break;case"7d":default:n.setDate(o.getDate()-7);break;case"30d":n.setDate(o.getDate()-30);break;case"90d":n.setDate(o.getDate()-90)}let{data:p,error:d}=await s.from("request_forms").select(`
        service_categories!request_forms_service_category_id_fkey (
          id,
          name_en,
          name_jp
        )
      `).gte("created_at",n.toISOString()).lte("created_at",o.toISOString());if(d)throw d;let g={};p?.forEach(e=>{let t=e.service_categories?.[0];if(t){let e=t.name_en||"Unknown";g[e]=(g[e]||0)+1}});let l=Object.values(g).reduce((e,t)=>e+t,0),m=Object.entries(g).map(([e,t])=>({category:e,count:t,percentage:Math.round(t/l*100)})).sort((e,t)=>t.count-e.count);return i.NextResponse.json(m)}catch(e){return console.error("Error fetching category distribution:",e),i.NextResponse.json({error:"Failed to fetch category distribution"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/categories/route",pathname:"/api/workflows/monitoring/categories",filename:"route",bundlePath:"app/api/workflows/monitoring/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\categories\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:l,serverHooks:m}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:l})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>r(3044));module.exports=a})();