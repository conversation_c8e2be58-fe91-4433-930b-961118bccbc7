(()=>{var e={};e.id=8672,e.ids=[8672],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},93272:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>u,routeModule:()=>l,tree:()=>p});var s=t(70260),n=t(28203),o=t(25155),i=t.n(o),a=t(67292),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p=["",{children:["test-predictive-form",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,78596,23)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-predictive-form\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],u=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-predictive-form\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test-predictive-form/page",pathname:"/test-predictive-form",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},78596:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-predictive-form\\page.tsx\x1b[0m:54:1]\n \x1b[2m51\x1b[0m │         { value: 'remove', label: '削除' }\n \x1b[2m52\x1b[0m │       ],\n \x1b[2m53\x1b[0m │       validation: { required: true }\n \x1b[2m54\x1b[0m │     }    {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m55\x1b[0m │       name: 'reason',\n \x1b[2m56\x1b[0m │       type: 'textarea',\n \x1b[2m57\x1b[0m │       label: '理由',\n    ╰────\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(93272));module.exports=s})();