'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/lib/LanguageContext'
import { ChevronLeft, Menu } from 'lucide-react'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'

export function KnowledgeBaseLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { language } = useLanguage()
  const [categories, setCategories] = useState<any[]>([])
  const supabase = createClient()

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    const { data } = await supabase
      .from('kb_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order')

    if (data) {
      setCategories(data)
    }
  }

  const SidebarContent = () => (
    <div className="space-y-4">
      <Link href="/knowledge-base">
        <Button variant="ghost" className="w-full justify-start">
          <ChevronLeft className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Knowledge Base Home' : 'ナレッジベースホーム'}
        </Button>
      </Link>
      <div className="space-y-2">
        <h3 className="font-semibold px-2">
          {language === 'en' ? 'Quick Links' : 'クイックリンク'}
        </h3>
        {categories.map(category => (
          <Link key={category.id} href={`/knowledge-base/category/${category.slug}`}>
            <Button variant="ghost" className="w-full justify-start">
              {language === 'en' ? category.name_en : category.name_jp}
            </Button>
          </Link>
        ))}
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:block w-64 border-r bg-card p-6">
          <SidebarContent />
        </aside>

        {/* Mobile Header */}
        <div className="flex-1">
          <div className="lg:hidden border-b p-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">
                {language === 'en' ? 'Knowledge Base' : 'ナレッジベース'}
              </h2>
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-64">
                  <SidebarContent />
                </SheetContent>
              </Sheet>
            </div>
          </div>

          {/* Main Content */}
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}