'use client'

import { useState, useEffect } from 'react'
import { SelectedUser } from '@/lib/request-types'
import { useDepartmentFilter } from '@/lib/use-department-filter'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Users, Search, X } from 'lucide-react'

interface UserSelectionProps {
  onUserSelect: (users: SelectedUser[]) => void
  selectedUsers: SelectedUser[]
  disabled?: boolean
  departmentFilter?: string
}

export default function UserSelection({
  onUserSelect,
  selectedUsers,
  disabled = false,
  departmentFilter
}: UserSelectionProps) {
  const [users, setUsers] = useState<SelectedUser[]>([])
  const [filteredUsers, setFilteredUsers] = useState<SelectedUser[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [error, setError] = useState<string>('')

  const { getStaffByDepartment, userDepartmentId } = useDepartmentFilter()

  useEffect(() => {
    loadUsers()
  }, [departmentFilter])

  useEffect(() => {
    filterUsers()
  }, [users, searchTerm])

  const loadUsers = async () => {
    try {
      setLoading(true)
      setError('')

      const departmentId = departmentFilter || userDepartmentId
      const { data, error: fetchError } = await getStaffByDepartment(departmentId)

      if (fetchError) throw new Error(fetchError)

      const formattedUsers: SelectedUser[] = data?.map(staff => ({
        id: staff.id,
        name_jp: staff.name_jp,
        name_en: staff.name_en,
        email: staff.email,
        staff_id: staff.staff_id,
        pc_id: staff.pc_id || undefined,
        division: {
          id: staff.division?.id || '',
          name_jp: staff.division?.name_jp || '',
          name_en: staff.division?.name_en || ''
        },
        group: staff.group ? {
          id: staff.group.id,
          name_jp: staff.group.name_jp,
          name_en: staff.group.name_en
        } : undefined
      })) || []

      setUsers(formattedUsers)
    } catch (err: any) {
      setError('ユーザーの読み込みに失敗しました / Failed to load users: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const filterUsers = () => {
    if (!searchTerm) {
      setFilteredUsers(users)
      return
    }

    const filtered = users.filter(user => 
      user.name_jp.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.name_en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.staff_id.toLowerCase().includes(searchTerm.toLowerCase())
    )

    setFilteredUsers(filtered)
  }

  const handleUserToggle = (user: SelectedUser, checked: boolean) => {
    if (disabled) return

    let updatedUsers: SelectedUser[]
    
    if (checked) {
      updatedUsers = [...selectedUsers, user]
    } else {
      updatedUsers = selectedUsers.filter(u => u.id !== user.id)
    }
    
    onUserSelect(updatedUsers)
  }

  const isUserSelected = (userId: string): boolean => {
    return selectedUsers.some(u => u.id === userId)
  }

  const removeSelectedUser = (userId: string) => {
    const updatedUsers = selectedUsers.filter(u => u.id !== userId)
    onUserSelect(updatedUsers)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          ユーザー選択 / User Selection
        </CardTitle>
        <CardDescription>
          リクエスト対象のユーザーを選択してください
          <br />
          Select the users for this request
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="名前、メール、スタッフIDで検索 / Search by name, email, or staff ID"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
            disabled={disabled}
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Selected Users */}
        {selectedUsers.length > 0 && (
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="text-sm font-medium mb-2">
              選択済みユーザー / Selected Users ({selectedUsers.length})
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map((user) => (
                <Badge
                  key={user.id}
                  variant="default"
                  className="text-xs flex items-center gap-1"
                >
                  {user.name_jp}
                  <button
                    type="button"
                    onClick={() => removeSelectedUser(user.id)}
                    className="ml-1 hover:bg-red-500 rounded-full p-0.5"
                    disabled={disabled}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* User List */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>ユーザーを読み込み中... / Loading users...</span>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto space-y-2">
            {filteredUsers.map((user) => {
              const isSelected = isUserSelected(user.id)
              
              return (
                <div
                  key={user.id}
                  className={`
                    p-3 border rounded-lg cursor-pointer transition-all
                    ${isSelected 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                    }
                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                  onClick={() => handleUserToggle(user, !isSelected)}
                >
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      checked={isSelected}
                      onChange={(e) => handleUserToggle(user, e.target.checked)}
                      disabled={disabled}
                      className="mt-1"
                    />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="font-medium text-sm">
                          {user.name_jp}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {user.staff_id}
                        </Badge>
                      </div>
                      
                      <div className="text-xs text-gray-500 mb-1">
                        {user.name_en}
                      </div>
                      
                      <div className="text-xs text-gray-600">
                        {user.email}
                      </div>
                      
                      <div className="text-xs text-gray-500 mt-1">
                        {user.division.name_jp}
                        {user.group && ` - ${user.group.name_jp}`}
                      </div>
                      
                      {user.pc_id && (
                        <div className="text-xs text-gray-500">
                          PC ID: {user.pc_id}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
            
            {filteredUsers.length === 0 && !loading && (
              <div className="text-center py-8 text-gray-500">
                {searchTerm 
                  ? 'ユーザーが見つかりません / No users found'
                  : 'ユーザーがありません / No users available'
                }
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
