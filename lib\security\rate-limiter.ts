import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';

interface RateLimiterOptions {
  windowMs: number;
  max: number;
  message?: string;
  keyGenerator?: (req: NextRequest) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitResult {
  allowed: boolean;
  current: number;
  max: number;
  message: string;
  resetAt: Date;
  key: string;
}

export class RateLimiter {
  private options: Required<RateLimiterOptions>;

  constructor(options: RateLimiterOptions) {
    this.options = {
      windowMs: options.windowMs,
      max: options.max,
      message: options.message || 'Too many requests, please try again later.',
      keyGenerator: options.keyGenerator || ((req) => req.ip || 'unknown'),
      skipSuccessfulRequests: options.skipSuccessfulRequests || false,
      skipFailedRequests: options.skipFailedRequests || false,
    };
  }

  async check(req: NextRequest): Promise<RateLimitResult> {
    const key = this.options.keyGenerator(req);
    const supabase = createClient();
    const now = new Date();
    const windowStart = new Date(now.getTime() - this.options.windowMs);

    // Get current request count within window
    const { data: requests, error } = await supabase
      .from('rate_limit_logs')
      .select('id')
      .eq('key', key)
      .gte('created_at', windowStart.toISOString())
      .order('created_at', { ascending: false });

    const current = requests?.length || 0;

    // Check if limit exceeded
    if (current >= this.options.max) {
      const oldestRequest = requests?.[requests.length - 1];
      const resetAt = oldestRequest 
        ? new Date(new Date(oldestRequest.created_at).getTime() + this.options.windowMs)
        : new Date(now.getTime() + this.options.windowMs);

      return {
        allowed: false,
        current,
        max: this.options.max,
        message: this.options.message,
        resetAt,
        key,
      };
    }

    // Log this request
    await supabase.from('rate_limit_logs').insert({
      key,
      endpoint: req.url,
      method: req.method,
      ip: req.ip,
    });

    // Clean up old entries (older than 24 hours)
    const cleanupTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    await supabase
      .from('rate_limit_logs')
      .delete()
      .lt('created_at', cleanupTime.toISOString());

    return {
      allowed: true,
      current: current + 1,
      max: this.options.max,
      message: 'Request allowed',
      resetAt: new Date(now.getTime() + this.options.windowMs),
      key,
    };
  }

  async reset(key: string): Promise<void> {
    const supabase = createClient();
    await supabase
      .from('rate_limit_logs')
      .delete()
      .eq('key', key);
  }
}

// Export convenience rate limiters for common use cases
export const authRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per 15 minutes
  message: 'Too many authentication attempts. Please try again later.',
});

export const apiRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute
  message: 'API rate limit exceeded. Please slow down.',
});

export const uploadRateLimiter = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 100, // 100 uploads per hour
  message: 'Upload limit exceeded. Please try again later.',
});
