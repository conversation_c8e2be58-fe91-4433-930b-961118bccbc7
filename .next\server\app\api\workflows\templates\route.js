(()=>{var e={};e.id=6656,e.ids=[6656],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},38874:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>c,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>l});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(61487),p=t(6804),d=t(2924);async function l(e){try{let r=(0,u.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:o}=new URL(e.url),a={category:o.get("category")||void 0,serviceCategoryId:o.get("serviceCategoryId")||void 0,priority:o.get("priority")||void 0},n=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),l=new d.I(r),c=new p.WorkflowTemplateManager(r,n,l),x=await c.getTemplates(a);return i.NextResponse.json({templates:x})}catch(e){return console.error("Error fetching workflow templates:",e),i.NextResponse.json({error:"Failed to fetch workflow templates"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/templates/route",pathname:"/api/workflows/templates",filename:"route",bundlePath:"app/api/workflows/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:w}=c;function b(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},6804:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Expression expected\n   ,-[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-template-manager.ts\x1b[0m:2:1]\n \x1b[2m1\x1b[0m | owEngine\n \x1b[2m2\x1b[0m |   ) {\n   : \x1b[35;1m  ^\x1b[0m\n \x1b[2m3\x1b[0m |     this.supabase = supabase;\n \x1b[2m4\x1b[0m |     this.auditService = auditService;\n \x1b[2m5\x1b[0m |     this.workflowEngine = workflowEngine;\n   `----\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064,3744,9389,2924],()=>t(38874));module.exports=s})();