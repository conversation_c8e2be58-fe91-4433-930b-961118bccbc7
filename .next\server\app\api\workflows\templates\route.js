"use strict";(()=>{var e={};e.id=6656,e.ids=[6656],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},52898:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>v});var o={};t.r(o),t.d(o,{GET:()=>l});var s=t(42706),a=t(28203),n=t(45994),i=t(39187),p=t(61487),u=t(6804),d=t(2924);async function l(e){try{let r=(0,p.U)(),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),a={category:s.get("category")||void 0,serviceCategoryId:s.get("serviceCategoryId")||void 0,priority:s.get("priority")||void 0},n=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),l=new d.I(r),c=new u.WorkflowTemplateManager(r,n,l),w=await c.getTemplates(a);return i.NextResponse.json({templates:w})}catch(e){return console.error("Error fetching workflow templates:",e),i.NextResponse.json({error:"Failed to fetch workflow templates"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let c=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/templates/route",pathname:"/api/workflows/templates",filename:"route",bundlePath:"app/api/workflows/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:w,workUnitAsyncStorage:v,serverHooks:x}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:v})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(52898));module.exports=o})();