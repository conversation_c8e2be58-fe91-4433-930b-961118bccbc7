"use strict";(()=>{var e={};e.id=6656,e.ids=[6656],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},52898:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>w});var o={};t.r(o),t.d(o,{GET:()=>d});var a=t(42706),s=t(28203),n=t(45994),i=t(39187),p=t(6804),u=t(52054);async function d(e){try{let r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),s={category:a.get("category")||void 0,serviceCategoryId:a.get("serviceCategoryId")||void 0,priority:a.get("priority")||void 0},n=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),d=new u.I(r),l=new p.WorkflowTemplateManager(r,n,d),c=await l.getTemplates(s);return i.NextResponse.json({templates:c})}catch(e){return console.error("Error fetching workflow templates:",e),i.NextResponse.json({error:"Failed to fetch workflow templates"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let l=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/templates/route",pathname:"/api/workflows/templates",filename:"route",bundlePath:"app/api/workflows/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:w,serverHooks:v}=l;function f(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:w})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(52898));module.exports=o})();