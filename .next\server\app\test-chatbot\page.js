(()=>{var e={};e.id=9365,e.ids=[9365],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33438:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=r(70260),i=r(28203),n=r(25155),l=r.n(n),a=r(67292),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(s,c);let d=["",{children:["test-chatbot",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7685)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-chatbot\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-chatbot\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-chatbot/page",pathname:"/test-chatbot",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},11535:(e,s,r)=>{Promise.resolve().then(r.bind(r,7685))},19783:(e,s,r)=>{Promise.resolve().then(r.bind(r,37994))},37994:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h});var t=r(45512),i=r(58009),n=r.n(i),l=r(69544),a=r(97643),c=r(87021),d=r(77252),o=r(4269),x=r(6841),p=r(31575),m=r(46904);function h(){let[e,s]=n().useState("");return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"AI チャットボットテスト"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"ITヘルプデスクアシスタントのチャットボット機能をテストします。"})]}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5"}),"リアルタイムサポート"]})}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"ユーザーの質問に対してリアルタイムで回答を提供します。"}),"            ",(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mt-0.5 text-green-500"}),(0,t.jsx)("span",{children:"24時間対応"})]}),(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mt-0.5 text-green-500"}),(0,t.jsx)("span",{children:"日本語・英語対応"})]}),(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mt-0.5 text-green-500"}),(0,t.jsx)("span",{children:"コンテキスト認識"})]})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),"FAQ統合"]})}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"よくある質問を即座に検索して回答します。"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsx)("li",{children:"• パスワードリセット方法"}),(0,t.jsx)("li",{children:"• グループメール管理"}),(0,t.jsx)("li",{children:"• SharePointアクセス申請"}),(0,t.jsx)("li",{children:"• PC管理者権限"})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),"チュートリアル"]})}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"ステップバイステップのガイドを提供します。"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsx)("li",{children:"• 新入社員IT設定"}),"              ",(0,t.jsx)("li",{children:"• フォーム入力ガイド"}),(0,t.jsx)("li",{children:"• トラブルシューティング"}),(0,t.jsx)("li",{children:"• ベストプラクティス"})]})]})]})]}),(0,t.jsxs)(a.Zp,{className:"mb-8",children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"テストシナリオ"}),(0,t.jsx)(a.BT,{children:"異なるコンテキストでチャットボットをテストできます"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:[{title:"パスワードリセット申請",form:"password-reset",description:"パスワードリセットフォームでのアシスタント"},{title:"グループメール管理",form:"group-mail",description:"グループメール追加・削除のサポート"},{title:"一般的な質問",form:"",description:"一般的なITサポートの質問"}].map((r,i)=>(0,t.jsx)(c.$,{variant:e===r.form?"default":"outline",onClick:()=>s(r.form),children:r.title},i))}),e&&(0,t.jsxs)(d.E,{className:"mt-4",variant:"secondary",children:["現在のコンテキスト: ",e||"なし"]})]})]}),(0,t.jsxs)(a.Zp,{className:"mb-8",children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"サンプル質問"}),(0,t.jsx)(a.BT,{children:"以下の質問を試してみてください"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"一般的な質問"}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,t.jsx)("li",{children:"• パスワードをリセットしたいです"}),(0,t.jsx)("li",{children:"• グループメールに追加してください"}),"                ",(0,t.jsx)("li",{children:"• SharePointにアクセスできません"}),(0,t.jsx)("li",{children:"• 新しいPCの設定を教えてください"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"フォーム関連の質問"}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,t.jsx)("li",{children:"• このフォームの入力方法を教えて"}),(0,t.jsx)("li",{children:"• 必須項目は何ですか？"}),(0,t.jsx)("li",{children:"• 申請の承認にはどれくらいかかりますか？"}),(0,t.jsx)("li",{children:"• 複数のユーザーを一度に申請できますか？"})]})]})]})})]}),(0,t.jsx)(l.G,{defaultOpen:!0,currentForm:e,currentPage:"test-chatbot"})]})}},7685:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-chatbot\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-chatbot\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8096,2076],()=>r(33438));module.exports=t})();