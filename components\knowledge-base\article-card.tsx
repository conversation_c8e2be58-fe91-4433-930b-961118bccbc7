'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { BookOpen, Calendar, Eye } from 'lucide-react'
import { useLanguage } from '@/lib/LanguageContext'
import Link from 'next/link'

interface ArticleCardProps {
  article: any
}

export function ArticleCard({ article }: ArticleCardProps) {
  const { language } = useLanguage()

  const title = language === 'en' ? article.title_en : article.title_jp
  const summary = language === 'en' ? article.summary_en : article.summary_jp
  const categoryName = language === 'en' 
    ? article.category?.name_en 
    : article.category?.name_jp

  return (
    <Link href={`/knowledge-base/article/${article.slug}`}>
      <Card className="h-full p-6 hover:shadow-lg transition-shadow cursor-pointer">
        <div className="flex items-start justify-between mb-4">
          <div>
            {categoryName && (
              <Badge variant="secondary" className="mb-2">
                {categoryName}
              </Badge>
            )}
            <h3 className="text-lg font-semibold line-clamp-2">{title}</h3>
          </div>
          <BookOpen className="h-5 w-5 text-muted-foreground flex-shrink-0" />
        </div>
        
        {summary && (
          <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
            {summary}
          </p>
        )}
        
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {new Date(article.published_at || article.created_at).toLocaleDateString(
              language === 'en' ? 'en-US' : 'ja-JP'
            )}
          </div>
          <div className="flex items-center gap-1">
            <Eye className="h-3 w-3" />
            {article.view_count} {language === 'en' ? 'views' : '閲覧'}
          </div>
        </div>
      </Card>
    </Link>
  )
}