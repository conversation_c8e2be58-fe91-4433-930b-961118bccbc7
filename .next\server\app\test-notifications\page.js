(()=>{var e={};e.id=5426,e.ids=[5426],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34008:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>x,routeModule:()=>c,tree:()=>d});var n=r(70260),s=r(28203),o=r(25155),i=r.n(o),a=r(67292),p={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>a[e]);r.d(t,p);let d=["",{children:["test-notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,91914,23)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],x=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-notifications\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/test-notifications/page",pathname:"/test-notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91914:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expression expected\n   ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-notifications\\page.tsx\x1b[0m:1:1]\n \x1b[2m1\x1b[0m │               </div>\n   \xb7 \x1b[35;1m               ─\x1b[0m\n \x1b[2m2\x1b[0m │ \n \x1b[2m3\x1b[0m │               <div>\n \x1b[2m4\x1b[0m │                 <Label htmlFor="actionUrl">Action URL (Optional)</Label>\n   ╰────\n  \x1b[31m\xd7\x1b[0m Expression expected\n   ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-notifications\\page.tsx\x1b[0m:1:1]\n \x1b[2m1\x1b[0m │               </div>\n   \xb7 \x1b[35;1m                ───\x1b[0m\n \x1b[2m2\x1b[0m │ \n \x1b[2m3\x1b[0m │               <div>\n \x1b[2m4\x1b[0m │                 <Label htmlFor="actionUrl">Action URL (Optional)</Label>\n   ╰────\n\n\nCaused by:\n    Syntax Error')}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8096,2076],()=>r(34008));module.exports=n})();