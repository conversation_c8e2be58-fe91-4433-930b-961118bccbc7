import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth-context'
import { supabase } from '@/lib/auth'

interface Department {
  id: string
  name: string
  name_jp: string
  code: string
}

export function useDepartmentFilter() {
  const [userDepartmentId, setUserDepartmentId] = useState<string | null>(null)
  const [userDepartment, setUserDepartment] = useState<Department | null>(null)
  const [loading, setLoading] = useState(true)
  const { user, staff } = useAuth()

  useEffect(() => {
    if (staff?.department_id) {
      loadDepartmentData(staff.department_id)
    } else {
      setLoading(false)
    }
  }, [staff])

  const loadDepartmentData = async (departmentId: string) => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name, name_jp, code')
        .eq('id', departmentId)
        .single()

      if (error) throw error

      setUserDepartmentId(departmentId)
      setUserDepartment(data)
    } catch (error) {
      console.error('Failed to load department data:', error)
      setUserDepartmentId(null)
      setUserDepartment(null)
    } finally {
      setLoading(false)
    }
  }

  const canAccessDepartment = (departmentId: string): boolean => {
    // Global and System admins can access all departments
    if (staff?.role === 'global_admin' || staff?.role === 'system_admin') {
      return true
    }
    
    // Department admins and regular users can only access their own department
    return userDepartmentId === departmentId
  }

  const canAccessAllDepartments = (): boolean => {
    return staff?.role === 'global_admin' || staff?.role === 'system_admin'
  }

  return {
    userDepartmentId,
    userDepartment,
    loading,
    canAccessDepartment,
    canAccessAllDepartments
  }
}
