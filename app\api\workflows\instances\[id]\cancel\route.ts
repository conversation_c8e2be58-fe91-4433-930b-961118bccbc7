import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user data
    const { data: userData } = await supabase
      .from('staff')
      .select('*')
      .eq('auth_id', user.id)
      .single();

    if (!userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Initialize workflow engine
    const engine = new WorkflowEngine();

    try {
      // Cancel the workflow
      await engine.cancelWorkflow(params.id, userData.id);

      return NextResponse.json({ 
        message: 'Workflow cancelled successfully' 
      });
    } catch (error: any) {
      console.error('Error cancelling workflow:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to cancel workflow' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error in workflow cancellation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
