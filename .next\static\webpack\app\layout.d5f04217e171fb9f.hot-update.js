"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80e7d0e20714\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJpbmNcXGl0c3luYy1wcm9qZWN0XFxDbGF1ZGVcXERlbW8xXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODBlN2QwZTIwNzE0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   canAccessDepartment: () => (/* binding */ canAccessDepartment),\n/* harmony export */   getCurrentSession: () => (/* binding */ getCurrentSession),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   rolePermissions: () => (/* binding */ rolePermissions),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env */ \"(app-pages-browser)/./lib/env.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n// Validate Supabase configuration\nconst supabaseConfig = (0,_env__WEBPACK_IMPORTED_MODULE_0__.validateSupabaseConfig)();\n// Create Supabase client for authentication\nconst supabase = supabaseConfig.isValid ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(_env__WEBPACK_IMPORTED_MODULE_0__.config.supabase.url, _env__WEBPACK_IMPORTED_MODULE_0__.config.supabase.anonKey) : null;\n// User roles enum\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"GLOBAL_ADMIN\"] = \"Global Administrator\";\n    UserRole[\"SYSTEM_ADMIN\"] = \"Web App System Administrator\";\n    UserRole[\"DEPT_ADMIN\"] = \"Department Administrator\";\n    UserRole[\"HR_STAFF\"] = \"HR Staff\";\n    UserRole[\"IT_SUPPORT\"] = \"IT Helpdesk Support\";\n    UserRole[\"REGULAR_USER\"] = \"Regular User\";\n    return UserRole;\n}({});\n// Role definitions with permissions\nconst rolePermissions = {\n    [\"Global Administrator\"]: [\n        {\n            resource: '*',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'delete',\n            scope: 'all'\n        }\n    ],\n    [\"Web App System Administrator\"]: [\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'system',\n            action: 'read',\n            scope: 'all'\n        }\n    ],\n    [\"Department Administrator\"]: [\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'department'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'department'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'department'\n        },\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'department'\n        },\n        {\n            resource: 'requests',\n            action: 'update',\n            scope: 'department'\n        }\n    ],\n    [\"HR Staff\"]: [\n        {\n            resource: 'hr_requests',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'hr_requests',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'hr_requests',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'all'\n        }\n    ],\n    [\"IT Helpdesk Support\"]: [\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'requests',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'update',\n            scope: 'all'\n        }\n    ],\n    [\"Regular User\"]: [\n        {\n            resource: 'requests',\n            action: 'create',\n            scope: 'own'\n        },\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'own'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'read',\n            scope: 'all'\n        }\n    ]\n};\n// Authentication functions\nconst signIn = async (email, password)=>{\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    if (!supabase) {\n        return {\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\nconst signUp = async (email, password, userData)=>{\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: userData\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst getCurrentUser = async ()=>{\n    if (!supabase) {\n        return {\n            user: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data: { user }, error } = await supabase.auth.getUser();\n    return {\n        user,\n        error\n    };\n};\nconst getCurrentSession = async ()=>{\n    if (!supabase) {\n        return {\n            session: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data: { session }, error } = await supabase.auth.getSession();\n    return {\n        session,\n        error\n    };\n};\n// Get user with role information\nconst getUserWithRole = async (userId)=>{\n    // In development mode, return a mock user to avoid RLS errors\n    if (typeof process !== 'undefined' && \"development\" !== 'production') {\n        console.log('Development mode: Using mock user data');\n        return {\n            data: {\n                id: userId,\n                auth_id: userId,\n                email: '<EMAIL>',\n                first_name: 'Development',\n                last_name: 'User',\n                role: {\n                    name: 'admin'\n                },\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            error: null\n        };\n    }\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    try {\n        // First check if the user exists in the staff table\n        const { data, error } = await supabase.from('staff').select('*').eq('auth_id', userId).maybeSingle() // Use maybeSingle instead of single to avoid error if no record found\n        ;\n        if (error) {\n            console.error('Database error:', error.message);\n            return {\n                data: null,\n                error\n            };\n        }\n        // If staff record found, fetch related data separately\n        if (data) {\n            // Create an enriched staff object\n            const enrichedStaff = {\n                ...data\n            };\n            try {\n                // Fetch division data if division_id exists\n                if (data.division_id) {\n                    const { data: divisionData } = await supabase.from('divisions').select('*').eq('id', data.division_id).single();\n                    if (divisionData) {\n                        enrichedStaff.division = divisionData;\n                    }\n                }\n                // Fetch group data if group_id exists\n                if (data.group_id) {\n                    const { data: groupData } = await supabase.from('groups').select('*').eq('id', data.group_id).single();\n                    if (groupData) {\n                        enrichedStaff.group = groupData;\n                    }\n                }\n                // Fetch role data if role_id exists\n                if (data.role_id) {\n                    const { data: roleData } = await supabase.from('roles').select('*').eq('id', data.role_id).single();\n                    if (roleData) {\n                        enrichedStaff.role = roleData;\n                    }\n                }\n                return {\n                    data: enrichedStaff,\n                    error: null\n                };\n            } catch (fetchError) {\n                console.error('Error fetching related data:', fetchError);\n                // Return the basic staff data even if related data fetch fails\n                return {\n                    data,\n                    error: null\n                };\n            }\n        }\n        // If no staff record found, check if user exists in profiles table\n        if (!data) {\n            try {\n                // Try to get basic user profile information\n                const { data: profileData, error: profileError } = await supabase.from('profiles').select('*').eq('id', userId).maybeSingle();\n                if (profileError) {\n                    // Check if this is an RLS policy error\n                    if (profileError.message && (profileError.message.includes('infinite recursion') || profileError.message.includes('permission denied'))) {\n                        console.warn('RLS policy error for profiles table. Using basic user data instead.');\n                        // Instead of making another API call, create a minimal user object\n                        // This reduces the number of API calls and potential for more errors\n                        return {\n                            data: {\n                                id: userId,\n                                // Add minimal required fields\n                                role: {\n                                    name: 'regular_user'\n                                },\n                                // Add default values for required fields\n                                email: '',\n                                first_name: '',\n                                last_name: '',\n                                created_at: new Date().toISOString(),\n                                updated_at: new Date().toISOString()\n                            },\n                            error: null\n                        };\n                    } else {\n                        console.error('Profile lookup error:', profileError.message);\n                        return {\n                            data: null,\n                            error: profileError\n                        };\n                    }\n                }\n                // Return profile data if found, or null if not\n                return {\n                    data: profileData,\n                    error: null\n                };\n            } catch (profileErr) {\n                console.error('Error accessing profiles:', profileErr);\n                // Fallback to basic user data in development\n                if (typeof process !== 'undefined' && \"development\" !== 'production') {\n                    return {\n                        data: {\n                            id: userId,\n                            role: {\n                                name: 'regular_user'\n                            }\n                        },\n                        error: null\n                    };\n                }\n                return {\n                    data: null,\n                    error: profileErr instanceof Error ? profileErr : new Error('Unknown error')\n                };\n            }\n        }\n        // Return staff data if found\n        return {\n            data,\n            error: null\n        };\n    } catch (err) {\n        console.error('Unexpected error in getUserWithRole:', err);\n        return {\n            data: null,\n            error: err instanceof Error ? err : new Error('Unknown error')\n        };\n    }\n};\n// Check if user has specific permission\nconst hasPermission = (userRole, resource, action, scope)=>{\n    const permissions = rolePermissions[userRole];\n    if (!permissions) return false;\n    return permissions.some((permission)=>{\n        if (permission.resource === '*') return true;\n        if (permission.resource === resource && permission.action === action) {\n            if (scope && permission.scope !== scope && permission.scope !== 'all') {\n                return false;\n            }\n            return true;\n        }\n        return false;\n    });\n};\n// Check if user can access department data\nconst canAccessDepartment = (userRole, userDepartmentId, targetDepartmentId)=>{\n    if (userRole === \"Global Administrator\" || userRole === \"Web App System Administrator\") {\n        return true;\n    }\n    if (userRole === \"Department Administrator\") {\n        return userDepartmentId === targetDepartmentId;\n    }\n    if (userRole === \"HR Staff\" || userRole === \"IT Helpdesk Support\") {\n        return true;\n    }\n    if (userRole === \"Regular User\") {\n        return userDepartmentId === targetDepartmentId;\n    }\n    return false;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth.ts\n"));

/***/ })

});