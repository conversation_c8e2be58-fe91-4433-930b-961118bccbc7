"use strict";(()=>{var e={};e.id=1764,e.ids=[1764],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},89768:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>d});var n=t(42706),o=t(28203),a=t(45994),i=t(39187),u=t(61487),p=t(98837);let l=p.z.object({name:p.z.string().min(1),description:p.z.string().optional(),rule_type:p.z.enum(["routing","approval","escalation","assignment"]),conditions:p.z.record(p.z.any()),actions:p.z.record(p.z.any()),priority:p.z.number().int().optional(),is_active:p.z.boolean().optional()});async function c(){try{let e=await (0,u.createServerClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:n}=await e.from("business_rules").select("*").eq("is_active",!0).order("priority",{ascending:!1});if(n)return i.NextResponse.json({error:n.message},{status:500});return i.NextResponse.json({data:s})}catch(e){return console.error("Error fetching business rules:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let r=await (0,u.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let n=await e.json(),o=l.safeParse(n);if(!o.success)return i.NextResponse.json({error:"Invalid request data",details:o.error.errors},{status:400});let{data:a,error:p}=await r.from("business_rules").insert(o.data).select().single();if(p)return i.NextResponse.json({error:p.message},{status:500});return i.NextResponse.json({data:a},{status:201})}catch(e){return console.error("Error creating business rule:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/rules/route",pathname:"/api/workflows/rules",filename:"route",bundlePath:"app/api/workflows/rules/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\rules\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:f}=x;function v(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(89768));module.exports=s})();