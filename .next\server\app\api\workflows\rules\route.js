"use strict";(()=>{var e={};e.id=1764,e.ids=[1764],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},89768:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>c});var o=t(42706),n=t(28203),a=t(45994),i=t(39187),u=t(59913);!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let p=u.z.object({name:u.z.string().min(1),description:u.z.string().optional(),rule_type:u.z.enum(["routing","approval","escalation","assignment"]),conditions:u.z.record(u.z.any()),actions:u.z.record(u.z.any()),priority:u.z.number().int().optional(),is_active:u.z.boolean().optional()});async function l(){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:o}=await e.from("business_rules").select("*").eq("is_active",!0).order("priority",{ascending:!1});if(o)return i.NextResponse.json({error:o.message},{status:500});return i.NextResponse.json({data:s})}catch(e){return console.error("Error fetching business rules:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e){try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=await e.json(),n=p.safeParse(o);if(!n.success)return i.NextResponse.json({error:"Invalid request data",details:n.error.errors},{status:400});let{data:a,error:u}=await r.from("business_rules").insert(n.data).select().single();if(u)return i.NextResponse.json({error:u.message},{status:500});return i.NextResponse.json({data:a},{status:201})}catch(e){return console.error("Error creating business rule:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/rules/route",pathname:"/api/workflows/rules",filename:"route",bundlePath:"app/api/workflows/rules/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\rules\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:v}=d;function w(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(89768));module.exports=s})();