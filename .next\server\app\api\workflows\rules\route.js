(()=>{var e={};e.id=1764,e.ids=[1764],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85882:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>d});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(61487),p=t(98837);let l=p.z.object({name:p.z.string().min(1),description:p.z.string().optional(),rule_type:p.z.enum(["routing","approval","escalation","assignment"]),conditions:p.z.record(p.z.any()),actions:p.z.record(p.z.any()),priority:p.z.number().int().optional(),is_active:p.z.boolean().optional()});async function c(){try{let e=await (0,u.createServerClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:o}=await e.from("business_rules").select("*").eq("is_active",!0).order("priority",{ascending:!1});if(o)return i.NextResponse.json({error:o.message},{status:500});return i.NextResponse.json({data:s})}catch(e){return console.error("Error fetching business rules:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let r=await (0,u.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=await e.json(),a=l.safeParse(o);if(!a.success)return i.NextResponse.json({error:"Invalid request data",details:a.error.errors},{status:400});let{data:n,error:p}=await r.from("business_rules").insert(a.data).select().single();if(p)return i.NextResponse.json({error:p.message},{status:500});return i.NextResponse.json({data:n},{status:201})}catch(e){return console.error("Error creating business rule:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/rules/route",pathname:"/api/workflows/rules",filename:"route",bundlePath:"app/api/workflows/rules/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\rules\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:v,serverHooks:f}=x;function w(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:v})}},96487:()=>{},78335:()=>{},61487:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(49064),o=t(44512);let a=()=>{let e=(0,o.UL)();return(0,s.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064,8837],()=>t(85882));module.exports=s})();