// Performance Monitoring Dashboard
'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Activity, Database, Gauge, Globe, HardDrive, Zap } from 'lucide-react';

interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  activeConnections: number;
  cacheHitRate: number;
  dbQueryTime: number;
}

export default function PerformanceMonitoringDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    responseTime: 0,
    throughput: 0,
    errorRate: 0,
    activeConnections: 0,
    cacheHitRate: 0,
    dbQueryTime: 0
  });

  useEffect(() => {
    // Simulate real-time metrics updates
    const interval = setInterval(() => {
      setMetrics({
        responseTime: Math.random() * 200 + 50,
        throughput: Math.random() * 1000 + 500,
        errorRate: Math.random() * 5,
        activeConnections: Math.floor(Math.random() * 100) + 50,
        cacheHitRate: Math.random() * 30 + 70,
        dbQueryTime: Math.random() * 100 + 20
      });
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-500';
    if (value <= thresholds.warning) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Performance Monitoring</h1>
        <Badge variant="outline" className="text-sm">
          <Activity className="w-3 h-3 mr-1" />
          Live Monitoring Active
        </Badge>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Gauge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(metrics.responseTime, { good: 100, warning: 200 })}`}>
              {metrics.responseTime.toFixed(0)}ms
            </div>
            <Progress value={Math.min(metrics.responseTime / 300 * 100, 100)} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">Target: &lt;200ms</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Throughput</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {metrics.throughput.toFixed(0)} req/s
            </div>
            <Progress value={metrics.throughput / 1500 * 100} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">Peak: 1500 req/s</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(metrics.errorRate, { good: 1, warning: 3 })}`}>
              {metrics.errorRate.toFixed(2)}%
            </div>
            <Progress value={metrics.errorRate * 20} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">Target: &lt;1%</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeConnections}</div>
            <Progress value={metrics.activeConnections / 150 * 100} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">Max: 150 connections</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {metrics.cacheHitRate.toFixed(1)}%
            </div>
            <Progress value={metrics.cacheHitRate} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">Target: &gt;80%</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DB Query Time</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(metrics.dbQueryTime, { good: 50, warning: 100 })}`}>
              {metrics.dbQueryTime.toFixed(0)}ms
            </div>
            <Progress value={Math.min(metrics.dbQueryTime / 150 * 100, 100)} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">Target: &lt;100ms</p>
          </CardContent>
        </Card>
      </div>

      {/* Alert Section */}
      <Card className="border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20">
        <CardHeader>
          <CardTitle className="text-lg">Performance Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm">
            <li>• Consider implementing Redis caching for frequently accessed data</li>
            <li>• Enable database query optimization for complex joins</li>
            <li>• Implement CDN for static assets to reduce server load</li>
            <li>• Configure connection pooling for better resource utilization</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}