"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Inbox, 
  Users, 
  Plus, 
  Minus, 
  Search,
  UserPlus,
  UserMinus,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { createClient } from '@/lib/supabase';
import { useAuth } from '@/lib/auth-context';
import { UserSearchInput } from '@/components/forms/user-search-input';
import { toast } from 'sonner';
import { useDepartmentFilter } from '@/lib/use-department-filter';

interface MailboxAddress {
  id: string;
  email_address: string;
  description: string;
  division_id?: string;
  created_at: string;
  updated_at: string;
}

interface MailboxMember {
  id: string;
  mailbox_id: string;
  staff_id: string;
  created_at: string;
  staff?: {
    id: string;
    name_jp: string;
    name_en: string;
    email: string;
    staff_id: string;
  };
}

export default function MailboxManagementPage() {
  const { user, staff } = useAuth();
  const { filterByDepartment } = useDepartmentFilter();
  const [mailboxes, setMailboxes] = useState<MailboxAddress[]>([]);
  const [selectedMailbox, setSelectedMailbox] = useState<MailboxAddress | null>(null);
  const [mailboxMembers, setMailboxMembers] = useState<MailboxMember[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]);
  const supabase = createClient();