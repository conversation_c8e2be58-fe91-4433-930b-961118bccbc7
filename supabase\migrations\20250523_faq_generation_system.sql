-- FAQ Generation Tracking and Improvements
-- ========================================

-- Add source tracking to FAQ table
ALTER TABLE chatbot_faq ADD COLUMN IF NOT EXISTS source_type VARCHAR(20) DEFAULT 'manual';
ALTER TABLE chatbot_faq ADD COLUMN IF NOT EXISTS source_article_ids UUID[] DEFAULT '{}';
ALTER TABLE chatbot_faq ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2) DEFAULT 1.0;
ALTER TABLE chatbot_faq ADD COLUMN IF NOT EXISTS last_reviewed TIMESTAMP WITH TIME ZONE;
ALTER TABLE chatbot_faq ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0;

-- Create FAQ generation logs
CREATE TABLE IF NOT EXISTS faq_generation_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  generation_type VARCHAR(20) NOT NULL, -- 'kb_articles', 'search_patterns', 'manual'
  parameters JSONB DEFAULT '{}',
  total_generated INTEGER DEFAULT 0,
  total_saved INTEGER DEFAULT 0,
  generation_time_ms INTEGER,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create FAQ review queue
CREATE TABLE IF NOT EXISTS faq_review_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  faq_id UUID REFERENCES chatbot_faq(id) ON DELETE CASCADE,
  review_type VARCHAR(20) NOT NULL, -- 'low_confidence', 'low_usage', 'user_feedback'
  review_reason TEXT,
  suggested_action VARCHAR(20), -- 'update', 'deactivate', 'delete'
  suggested_changes JSONB,
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  review_decision VARCHAR(20), -- 'approved', 'rejected', 'modified'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create FAQ analytics view
CREATE OR REPLACE VIEW faq_analytics AS
SELECT 
  f.id,
  f.category,
  f.usage_count,
  f.is_active,
  f.confidence_score,
  f.source_type,
  f.created_at,
  f.last_reviewed,
  COUNT(DISTINCT fb.id) as feedback_count,
  AVG(fb.rating) as avg_rating,
  COUNT(DISTINCT rq.id) as review_requests
FROM chatbot_faq f
LEFT JOIN chatbot_feedback fb ON fb.message_id IN (
  SELECT id FROM chatbot_messages 
  WHERE metadata->>'faq_id' = f.id::text
)
LEFT JOIN faq_review_queue rq ON rq.faq_id = f.id
GROUP BY f.id;

-- Create function to suggest FAQ updates
CREATE OR REPLACE FUNCTION suggest_faq_updates()
RETURNS TABLE(
  faq_id UUID,
  suggestion_type TEXT,
  suggestion_reason TEXT,
  confidence DECIMAL
) AS $$
BEGIN
  -- Low usage FAQs
  RETURN QUERY
  SELECT 
    f.id,
    'deactivate'::TEXT,
    'Low usage (less than 5 uses in 30 days)'::TEXT,
    0.8::DECIMAL
  FROM chatbot_faq f
  WHERE f.is_active = true
    AND f.usage_count < 5
    AND f.created_at < NOW() - INTERVAL '30 days';

  -- Low confidence FAQs
  RETURN QUERY
  SELECT 
    f.id,
    'review'::TEXT,
    'Low confidence score'::TEXT,
    f.confidence_score
  FROM chatbot_faq f
  WHERE f.is_active = true
    AND f.confidence_score < 0.7;

  -- FAQs with negative feedback
  RETURN QUERY
  SELECT 
    f.id,
    'update'::TEXT,
    'Negative user feedback'::TEXT,
    0.9::DECIMAL
  FROM faq_analytics f
  WHERE f.avg_rating < 3.0
    AND f.feedback_count >= 3;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate FAQ from article
CREATE OR REPLACE FUNCTION generate_faq_metadata(
  p_article_id UUID,
  p_question_en TEXT,
  p_answer_en TEXT,
  p_question_ja TEXT,
  p_answer_ja TEXT
) RETURNS JSONB AS $$
DECLARE
  v_article RECORD;
  v_keywords TEXT[];
BEGIN
  -- Get article details
  SELECT * INTO v_article FROM kb_articles WHERE id = p_article_id;
  
  -- Extract keywords from content
  v_keywords := ARRAY(
    SELECT DISTINCT word
    FROM (
      SELECT regexp_split_to_table(
        lower(v_article.content_en || ' ' || v_article.content_jp), 
        '\s+'
      ) AS word
    ) words
    WHERE length(word) > 3
    ORDER BY word
    LIMIT 10
  );

  RETURN jsonb_build_object(
    'source_type', 'kb_article',
    'source_article_ids', ARRAY[p_article_id],
    'category', v_article.category,
    'keywords', v_keywords,
    'confidence_score', 0.85
  );
END;
$$ LANGUAGE plpgsql;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_chatbot_faq_source_type ON chatbot_faq(source_type);
CREATE INDEX IF NOT EXISTS idx_chatbot_faq_confidence ON chatbot_faq(confidence_score);
CREATE INDEX IF NOT EXISTS idx_chatbot_faq_usage ON chatbot_faq(usage_count);
CREATE INDEX IF NOT EXISTS idx_faq_generation_logs_type ON faq_generation_logs(generation_type);
CREATE INDEX IF NOT EXISTS idx_faq_review_queue_faq ON faq_review_queue(faq_id);

-- Enable RLS on new tables
ALTER TABLE faq_generation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE faq_review_queue ENABLE ROW LEVEL SECURITY;

-- RLS Policies for generation logs
CREATE POLICY "Admins can view all generation logs"
  ON faq_generation_logs FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

CREATE POLICY "Users can view own generation logs"
  ON faq_generation_logs FOR SELECT
  USING (user_id = auth.uid());

-- RLS Policies for review queue
CREATE POLICY "Admins can manage review queue"
  ON faq_review_queue FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Sample FAQ generation from existing KB articles
INSERT INTO chatbot_faq (
  question_ja, 
  question_en, 
  answer_ja, 
  answer_en, 
  category, 
  keywords,
  source_type,
  source_article_ids,
  confidence_score,
  is_active
)
SELECT 
  'グループメールに新しいメンバーを追加するにはどうすればよいですか？' as question_ja,
  'How do I add a new member to a group mail?' as question_en,
  'ITヘルプデスクシステムにログインし、「グループメール管理」を選択してください。追加したいグループメールアドレスを選択し、「メンバー追加」ボタンをクリックして、部門内のスタッフを検索・選択してください。' as answer_ja,
  'Log in to the IT Helpdesk system and select "Group Mail Management". Choose the group mail address you want to modify, click "Add Members", and search for staff within your department to add.' as answer_en,
  'group_mail' as category,
  ARRAY['group mail', 'add member', 'email', 'グループメール', '追加'] as keywords,
  'kb_article' as source_type,
  ARRAY[(SELECT id FROM kb_articles WHERE category = 'group_mail' LIMIT 1)]::UUID[] as source_article_ids,
  0.95 as confidence_score,
  true as is_active
WHERE NOT EXISTS (
  SELECT 1 FROM chatbot_faq 
  WHERE question_en = 'How do I add a new member to a group mail?'
);