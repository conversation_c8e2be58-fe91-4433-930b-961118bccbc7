import { createClient } from '@/lib/supabase/server';
import { WorkflowDefinition, WorkflowInstance, WorkflowTask } from './types';
import { RuleEngine } from './rule-engine';
import { StateMachine } from './state-machine';
import { SLAManager } from './sla-manager';
import { EscalationEngine } from './escalation-engine';
import { ApprovalEngine } from './approval-engine';
import { WorkflowNotificationIntegration } from './notification-integration';
import { auditLogger } from '@/lib/services/audit';

export class WorkflowEngine {
  private ruleEngine: RuleEngine;
  private stateMachine: StateMachine;
  private slaManager: SLAManager;
  private escalationEngine: EscalationEngine;
  private approvalEngine: ApprovalEngine;
  private notificationIntegration: WorkflowNotificationIntegration;

  constructor() {
    const supabase = createClient();
    this.ruleEngine = new RuleEngine();
    this.stateMachine = new StateMachine();
    this.slaManager = new SLAManager();
    this.escalationEngine = new EscalationEngine();
    this.approvalEngine = new ApprovalEngine();
    this.notificationIntegration = new WorkflowNotificationIntegration(supabase);
    
    // Subscribe to workflow events for notifications
    this.notificationIntegration.subscribeToWorkflowEvents();
  }

  /**
   * Start a new workflow instance
   */
  async startWorkflow(params: {
    workflowDefinitionId: string;
    requestId?: string;
    contextData?: Record<string, any>;
    userId: string;
  }): Promise<WorkflowInstance> {
    const { workflowDefinitionId, requestId, contextData, userId } = params;
    const supabase = createClient();

    try {
      // Get workflow definition
      const { data: definition, error: defError } = await supabase
        .from('workflow_definitions')
        .select('*')
        .eq('id', workflowDefinitionId)
        .eq('is_active', true)
        .single();

      if (defError || !definition) {
        throw new Error('Workflow definition not found');
      }

      // Create workflow instance
      const { data: instance, error: instanceError } = await supabase
        .from('workflow_instances')
        .insert({
          workflow_definition_id: workflowDefinitionId,
          request_id: requestId,
          current_state: 'initial',
          status: 'active',
          context_data: { ...contextData, request_id: requestId },
          created_by: userId,
        })
        .select()
        .single();

      if (instanceError || !instance) {
        throw new Error('Failed to create workflow instance');
      }

      // Process initial state
      await this.processWorkflowState(instance, definition.workflow_json);

      // Set up SLA tracking
      await this.slaManager.initializeSLA(instance.id, definition.workflow_json);

      // Log workflow start
      await auditLogger.log({
        action: 'WORKFLOW_STARTED',
        details: {
          workflow_id: instance.id,
          definition_id: workflowDefinitionId,
          request_id: requestId,
        },
        user_id: userId,
      });

      return instance;
    } catch (error) {
      console.error('Error starting workflow:', error);
      throw error;
    }
  }

  /**
   * Process workflow state transitions
   */
  async processWorkflowState(
    instance: WorkflowInstance,
    workflowDef: any
  ): Promise<void> {
    const currentState = workflowDef.states[instance.current_state];
    
    if (!currentState) {
      throw new Error(`Invalid state: ${instance.current_state}`);
    }

    switch (currentState.type) {
      case 'start':
        await this.handleStartState(instance, currentState, workflowDef);
        break;
      case 'approval':
        await this.handleApprovalState(instance, currentState);
        break;
      case 'task':
        await this.handleTaskState(instance, currentState);
        break;
      case 'automatic':
        await this.handleAutomaticState(instance, currentState, workflowDef);
        break;
      case 'end':
        await this.handleEndState(instance);
        break;
    }
  }

  /**
   * Handle start state
   */
  private async handleStartState(
    instance: WorkflowInstance,
    state: any,
    workflowDef: any
  ): Promise<void> {
    // Evaluate routing rules
    const nextState = await this.ruleEngine.evaluateTransitions(
      state.transitions,
      instance.context_data
    );

    if (nextState) {
      await this.transitionTo(instance, nextState, workflowDef);
    }
  }

  /**
   * Handle approval state
   */
  private async handleApprovalState(
    instance: WorkflowInstance,
    state: any
  ): Promise<void> {
    const supabase = createClient();

    // Check if this is a multi-level approval
    if (state.approvalChain) {
      // Use the approval engine for multi-level approvals
      await this.approvalEngine.createApprovalChain(
        instance.id,
        state.approvalChain,
        {
          ...instance.context_data,
          workflow_instance_id: instance.id,
          state_name: instance.current_state,
        }
      );
    } else {
      // Handle single-level approval (legacy)
      const assignee = await this.resolveAssignee(state.assignee, instance);

      const { data: task, error } = await supabase
        .from('workflow_tasks')
        .insert({
          workflow_instance_id: instance.id,
          task_type: 'approval',
          task_name: state.name || 'Approval Required',
          assigned_to: assignee.userId,
          assigned_role: assignee.role,
          status: 'pending',
          due_date: this.slaManager.calculateDueDate(state.sla_minutes),
          task_data: {
            state_name: instance.current_state,
            approval_options: state.approval_options || ['approve', 'reject'],
          },
        })
        .select()
        .single();

      if (error || !task) {
        throw new Error('Failed to create approval task');
      }

      // Set up escalation if needed
      if (state.escalation) {
        await this.escalationEngine.scheduleEscalation(task.id, state.escalation);
      }

      // Send notification
      await this.notificationIntegration.sendWorkflowNotification(
        instance.id,
        'approval_required',
        [assignee.userId].filter(Boolean),
        {
          taskId: task.id,
          taskName: task.task_name,
          stateName: instance.current_state
        }
      );
    }
  }

  /**
   * Handle task state
   */
  private async handleTaskState(
    instance: WorkflowInstance,
    state: any
  ): Promise<void> {
    const supabase = createClient();

    const assignee = await this.resolveAssignee(state.assignee, instance);

    const { data: task, error } = await supabase
      .from('workflow_tasks')
      .insert({
        workflow_instance_id: instance.id,
        task_type: 'task',
        task_name: state.name || 'Task',
        assigned_to: assignee.userId,
        assigned_role: assignee.role,
        status: 'pending',
        due_date: this.slaManager.calculateDueDate(state.sla_minutes),
        task_data: {
          handler: state.handler,
          parameters: state.parameters,
        },
      })
      .select()
      .single();

    if (error || !task) {
      throw new Error('Failed to create task');
    }

    // Execute handler if automatic
    if (state.automatic && state.handler) {
      await this.executeTaskHandler(task, state.handler);
    }
  }

  /**
   * Handle automatic state
   */
  private async handleAutomaticState(
    instance: WorkflowInstance,
    state: any,
    workflowDef: any
  ): Promise<void> {
    // Execute any automatic actions
    if (state.actions) {
      await this.executeActions(state.actions, instance);
    }

    // Automatically transition
    const nextState = await this.ruleEngine.evaluateTransitions(
      state.transitions,
      instance.context_data
    );

    if (nextState) {
      await this.transitionTo(instance, nextState, workflowDef);
    }
  }

  /**
   * Handle end state
   */
  private async handleEndState(instance: WorkflowInstance): Promise<void> {
    const supabase = createClient();

    // Update instance status
    await supabase
      .from('workflow_instances')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
      })
      .eq('id', instance.id);

    // Complete SLA tracking
    await this.slaManager.completeSLA(instance.id);

    // Send completion notification
    await this.sendCompletionNotification(instance);
  }

  /**
   * Transition to next state
   */
  private async transitionTo(
    instance: WorkflowInstance,
    nextState: string,
    workflowDef: any
  ): Promise<void> {
    const supabase = createClient();

    // Log transition
    await supabase.from('workflow_transitions').insert({
      workflow_instance_id: instance.id,
      from_state: instance.current_state,
      to_state: nextState,
    });

    // Update instance
    const { data: updatedInstance, error } = await supabase
      .from('workflow_instances')
      .update({ current_state: nextState })
      .eq('id', instance.id)
      .select()
      .single();

    if (error || !updatedInstance) {
      throw new Error('Failed to update workflow state');
    }

    // Process new state
    await this.processWorkflowState(updatedInstance, workflowDef);
  }

  /**
   * Complete a workflow task
   */
  async completeTask(
    taskId: string,
    userId: string,
    result: any
  ): Promise<void> {
    const supabase = createClient();

    // Get task details
    const { data: task, error: taskError } = await supabase
      .from('workflow_tasks')
      .select('*, workflow_instances!inner(*)')
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      throw new Error('Task not found');
    }

    // Update task status
    await supabase
      .from('workflow_tasks')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        completed_by: userId,
        task_data: { ...task.task_data, result },
      })
      .eq('id', taskId);

    // Update workflow context
    const updatedContext = {
      ...task.workflow_instances.context_data,
      [`task_${taskId}_result`]: result,
    };

    await supabase
      .from('workflow_instances')
      .update({ context_data: updatedContext })
      .eq('id', task.workflow_instance_id);

    // Get workflow definition
    const { data: definition } = await supabase
      .from('workflow_definitions')
      .select('workflow_json')
      .eq('id', task.workflow_instances.workflow_definition_id)
      .single();

    if (definition) {
      const currentState = definition.workflow_json.states[task.workflow_instances.current_state];
      
      // Check for transitions
      if (currentState.transitions) {
        const nextState = await this.ruleEngine.evaluateTransitions(
          currentState.transitions,
          updatedContext
        );

        if (nextState) {
          await this.transitionTo(task.workflow_instances, nextState, definition.workflow_json);
        }
      }
    }

    // Update SLA tracking
    await this.slaManager.updateTaskCompletion(taskId);
  }

  /**
   * Helper methods
   */
  private async resolveAssignee(
    assigneeConfig: any,
    instance: WorkflowInstance
  ): Promise<{ userId?: string; role?: string }> {
    if (assigneeConfig.userId) {
      return { userId: assigneeConfig.userId };
    }

    if (assigneeConfig.role) {
      // Resolve role with department context
      const department = assigneeConfig.department || instance.context_data.department;
      return { role: `${assigneeConfig.role}:${department}` };
    }

    return {};
  }

  private async executeTaskHandler(task: WorkflowTask, handlerName: string): Promise<void> {
    // Task handler execution logic
    // This would call specific handlers based on the handler name
    console.log(`Executing handler: ${handlerName} for task: ${task.id}`);
  }

  private async executeActions(actions: any[], instance: WorkflowInstance): Promise<void> {
    // Execute workflow actions
    for (const action of actions) {
      console.log(`Executing action: ${action.type} for workflow: ${instance.id}`);
    }
  }

  /**
   * Validate a workflow definition
   */
  async validateWorkflowDefinition(definition: WorkflowDefinition): Promise<void> {
    const workflowJson = definition.workflow_json as any;
    
    // Check required fields
    if (!workflowJson.id || !workflowJson.name || !workflowJson.states) {
      throw new Error('Workflow definition missing required fields');
    }
    
    // Check for initial state
    if (!workflowJson.states.initial) {
      throw new Error('Workflow definition must have an initial state');
    }
    
    // Check for at least one end state
    const endStates = Object.values(workflowJson.states)
      .filter((state: any) => state.type === 'end');
    if (endStates.length === 0) {
      throw new Error('Workflow definition must have at least one end state');
    }
    
    // Validate all transitions
    for (const [stateName, state] of Object.entries(workflowJson.states)) {
      const stateObj = state as any;
      if (stateObj.transitions) {
        for (const transition of stateObj.transitions) {
          if (!workflowJson.states[transition.to]) {
            throw new Error(`Invalid transition from ${stateName} to ${transition.to}`);
          }
        }
      }
    }
  }

  /**
   * Cancel a workflow instance
   */
  async cancelWorkflow(instanceId: string, userId: string): Promise<void> {
    const supabase = await createClient();
    
    // Get workflow instance
    const { data: instance, error } = await supabase
      .from('workflow_instances')
      .select('*')
      .eq('id', instanceId)
      .single();
      
    if (error || !instance) {
      throw new Error('Workflow instance not found');
    }
    
    if (instance.status !== 'active') {
      throw new Error('Can only cancel active workflows');
    }
    
    // Update workflow status
    const { error: updateError } = await supabase
      .from('workflow_instances')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString(),
      })
      .eq('id', instanceId);
      
    if (updateError) {
      throw new Error('Failed to cancel workflow');
    }
    
    // Cancel all pending tasks
    await supabase
      .from('workflow_tasks')
      .update({ status: 'cancelled' })
      .eq('workflow_instance_id', instanceId)
      .in('status', ['pending', 'in_progress']);
      
    // Log cancellation
    await auditLogger.log({
      action: 'WORKFLOW_CANCELLED',
      details: { workflow_id: instanceId },
      user_id: userId,
    });
  }

  private async sendTaskNotification(task: WorkflowTask, type: string): Promise<void> {
    // Send notification through notification integration
    if (task.assigned_to) {
      await this.notificationIntegration.sendWorkflowNotification(
        task.workflow_instance_id,
        'workflow_created',
        [task.assigned_to],
        {
          taskId: task.id,
          taskName: task.task_name || 'Task',
          taskType: type
        }
      );
    }
  }

  private async sendCompletionNotification(instance: WorkflowInstance): Promise<void> {
    // Get all stakeholders
    const supabase = createClient();
    const { data: request } = await supabase
      .from('request_forms')
      .select('requester_id')
      .eq('id', instance.context_data?.request_id)
      .single();

    const recipients = [];
    if (request?.requester_id) {
      recipients.push(request.requester_id);
    }

    // Send completion notification
    await this.notificationIntegration.sendWorkflowNotification(
      instance.id,
      'workflow_completed',
      recipients,
      {
        completedAt: new Date().toISOString()
      }
    );
  }
}

// Export singleton instance
export const workflowEngine = new WorkflowEngine();
