// parse-natural-language/index.ts
// Edge function for natural language parsing

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface NLPRequest {
  input: string
  context: {
    userId?: string
    departmentId?: string
    language: 'ja' | 'en'
    availableForms: string[]
  }
}

interface ParsedFormData {
  intent: string
  entities: Record<string, any>
  confidence: number
  suggestedForm: string
  extractedData: Record<string, any>
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { input, context } = await req.json() as NLPRequest
    
    // Use OpenAI to parse the natural language input
    const parsedData = await parseWithAI(input, context)
    
    // Store the parsing result for analytics
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Log the NLP request for learning
    await supabaseClient
      .from('nlp_parse_logs')
      .insert({
        input_text: input,
        parsed_intent: parsedData.intent,
        confidence: parsedData.confidence,
        extracted_data: parsedData.extractedData,
        language: context.language,
        department_id: context.departmentId
      })

    return new Response(
      JSON.stringify(parsedData),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

async function parseWithAI(
  input: string,
  context: any
): Promise<ParsedFormData> {
  const apiKey = Deno.env.get('OPENAI_API_KEY')
  if (!apiKey) {
    // Fallback to basic parsing
    return basicParsing(input, context)
  }

  const systemPrompt = `You are an expert at parsing natural language requests for a Japanese IT Helpdesk system.
Available forms: ${context.availableForms.join(', ')}
Language: ${context.language}

Parse the user's request and extract:
1. Intent (what they want to do)
2. Entities (people, items, dates, etc.)
3. Which form type best matches their request
4. Specific data to populate form fields

Common request types:
- Group mail management (add/remove users)
- Mailbox access
- SharePoint library access
- PC admin privileges
- Password resets
- Software installation
- Web browsing access

Return a JSON object with: intent, entities, confidence, suggestedForm, extractedData`

  const userPrompt = `Parse this request: "${input}"`

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        response_format: { type: 'json_object' }
      }),
    })

    const data = await response.json()
    return JSON.parse(data.choices[0].message.content)
  } catch (error) {
    console.error('AI parsing failed:', error)
    return basicParsing(input, context)
  }
}

function basicParsing(input: string, context: any): ParsedFormData {
  const lowerInput = input.toLowerCase()
  let intent = 'unknown'
  let suggestedForm = ''
  const entities: Record<string, any> = {}
  const extractedData: Record<string, any> = {}
  let confidence = 0

  // Check for group mail keywords
  if (lowerInput.includes('グループメール') || lowerInput.includes('group mail')) {
    intent = 'group_mail_request'
    suggestedForm = 'group_mail'
    confidence = 0.7
    
    if (lowerInput.includes('追加') || lowerInput.includes('add')) {
      extractedData.action = 'add'
    } else if (lowerInput.includes('削除') || lowerInput.includes('remove')) {
      extractedData.action = 'remove'
    }
  }
  // Check for password reset
  else if (lowerInput.includes('パスワード') || lowerInput.includes('password')) {
    intent = 'password_reset'
    suggestedForm = 'password_reset'
    confidence = 0.8
    
    if (lowerInput.includes('m365') || lowerInput.includes('office')) {
      extractedData.passwordType = 'M365 Office'
    } else if (lowerInput.includes('mfa')) {
      extractedData.passwordType = 'Multi Factor Authenticator'
    }
  }
  // Check for SharePoint
  else if (lowerInput.includes('sharepoint') || lowerInput.includes('シェアポイント')) {
    intent = 'sharepoint_access'
    suggestedForm = 'sharepoint'
    confidence = 0.7
    
    if (lowerInput.includes('アクセス') || lowerInput.includes('access')) {
      extractedData.action = 'grant'
    }
  }
  // Check for PC admin
  else if (lowerInput.includes('pc') && (lowerInput.includes('管理') || lowerInput.includes('admin'))) {
    intent = 'pc_admin_request'
    suggestedForm = 'pc_admin'
    confidence = 0.6
  }

  // Extract potential user names (simple pattern)
  const namePattern = /([一-龯ぁ-ゔァ-ヴー]+\s*[一-龯ぁ-ゔァ-ヴー]+)さん?/g
  const names = input.match(namePattern)
  if (names) {
    entities.users = names.map(n => n.replace('さん', ''))
    if (entities.users.length > 0) {
      extractedData.userName = entities.users[0]
    }
  }

  return {
    intent,
    entities,
    confidence,
    suggestedForm,
    extractedData
  }
}
