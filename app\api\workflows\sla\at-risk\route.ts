import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { slaManager } from '@/lib/services/workflow/sla-manager';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get at-risk SLA items
    const atRiskItems = await slaManager.getAtRiskItems();

    return NextResponse.json(atRiskItems);
  } catch (error) {
    console.error('Error fetching at-risk items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch at-risk items' },
      { status: 500 }
    );
  }
}
