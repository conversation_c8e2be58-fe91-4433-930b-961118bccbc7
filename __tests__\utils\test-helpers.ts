/**
 * Test Helpers
 * 
 * This module provides helper functions and utilities for testing.
 */

import { vi } from 'vitest';
import { createLegacySupabaseAdapter } from '../../lib/test-utils/legacy-adapters';

/**
 * Default test timeout in milliseconds
 */
export const DEFAULT_TEST_TIMEOUT = 10000;

/**
 * Extended test timeout for complex operations
 */
export const EXTENDED_TEST_TIMEOUT = 30000;

/**
 * Mock data for testing
 */
export const mockData = {
  users: [
    {
      id: '123e4567-e89b-12d3-a456-426614174000',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      role: { name: 'admin' }
    },
    {
      id: '223e4567-e89b-12d3-a456-426614174001',
      email: '<EMAIL>',
      first_name: 'Regular',
      last_name: 'User',
      role: { name: 'user' }
    }
  ],
  staff: [
    {
      id: '323e4567-e89b-12d3-a456-426614174002',
      auth_id: '123e4567-e89b-12d3-a456-426614174000',
      staff_id: 'STAFF001',
      name_jp: 'テスト ユーザー',
      name_en: 'Test User',
      email: '<EMAIL>',
      division_id: 'div1',
      group_id: 'group1',
      role_id: 'role1',
      is_active: true
    }
  ],
  divisions: [
    {
      id: 'div1',
      name_jp: '部門1',
      name_en: 'Division 1',
      code: 'DIV1'
    }
  ],
  groups: [
    {
      id: 'group1',
      division_id: 'div1',
      name_jp: 'グループ1',
      name_en: 'Group 1',
      code: 'GRP1'
    }
  ],
  roles: [
    {
      id: 'role1',
      name: 'admin',
      description: 'Administrator',
      permissions: {
        resources: ['*'],
        actions: ['*']
      }
    },
    {
      id: 'role2',
      name: 'user',
      description: 'Regular User',
      permissions: {
        resources: ['profile', 'requests'],
        actions: ['read', 'create']
      }
    }
  ]
};

/**
 * Create a mock Supabase client for testing
 */
export function createMockSupabaseClient() {
  return {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    single: vi.fn(),
    maybeSingle: vi.fn(),
    then: vi.fn(),
    auth: {
      getUser: vi.fn(),
      getSession: vi.fn(),
      signInWithPassword: vi.fn(),
      signOut: vi.fn()
    },
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn(),
        download: vi.fn()
      })
    }
  };
}

/**
 * Create mock responses for Supabase queries
 */
export function createMockResponses() {
  return {
    staff: {
      data: mockData.staff[0],
      error: null
    },
    staff_with_relations: {
      data: {
        ...mockData.staff[0],
        division: mockData.divisions[0],
        group: mockData.groups[0],
        role: mockData.roles[0]
      },
      error: null
    },
    divisions: {
      data: mockData.divisions,
      error: null
    },
    groups: {
      data: mockData.groups,
      error: null
    },
    roles: {
      data: mockData.roles,
      error: null
    },
    profiles: {
      data: mockData.users[0],
      error: null
    },
    user: {
      data: { user: mockData.users[0] },
      error: null
    },
    session: {
      data: { session: { user: mockData.users[0] } },
      error: null
    }
  };
}

/**
 * Create a legacy-compatible Supabase client with mock responses
 */
export function createMockLegacyClient() {
  const mockResponses = createMockResponses();
  return createLegacySupabaseAdapter(mockResponses);
}

/**
 * Wait for a specified time
 */
export function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry a function until it succeeds or times out
 */
export async function retry<T>(
  fn: () => Promise<T>,
  options: {
    maxRetries?: number;
    delay?: number;
    timeout?: number;
    onRetry?: (attempt: number, error: Error) => void;
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    delay = 100,
    timeout = 5000,
    onRetry = () => {}
  } = options;
  
  const startTime = Date.now();
  let attempt = 0;
  
  while (attempt < maxRetries) {
    try {
      return await fn();
    } catch (error) {
      attempt++;
      
      if (attempt >= maxRetries || Date.now() - startTime > timeout) {
        throw error;
      }
      
      onRetry(attempt, error as Error);
      await wait(delay);
    }
  }
  
  throw new Error('Retry failed: max retries exceeded');
}

/**
 * Mock the Next.js router
 */
export function mockRouter(overrides = {}) {
  return {
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    reload: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn()
    },
    isFallback: false,
    isReady: true,
    isPreview: false,
    query: {},
    asPath: '/',
    pathname: '/',
    basePath: '',
    ...overrides
  };
}

/**
 * Mock the Next.js request and response objects
 */
export function mockNextApiContext() {
  const req = {
    headers: {
      'content-type': 'application/json',
      'x-forwarded-for': '192.168.1.1'
    },
    cookies: {},
    query: {},
    body: {},
    method: 'GET',
    url: '/api/test'
  };
  
  const res = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    setHeader: vi.fn().mockReturnThis(),
    end: vi.fn()
  };
  
  return { req, res };
}