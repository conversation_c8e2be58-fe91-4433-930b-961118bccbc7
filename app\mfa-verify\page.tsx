'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { MFAVerify } from '@/components/auth/mfa-verify';
import { useMFA } from '@/lib/hooks/use-mfa';
import { useAuth } from '@/lib/auth-context';
import { Loader2 } from 'lucide-react';

export default function MFAVerifyPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { createChallenge, loading } = useMFA();
  
  const [sessionToken, setSessionToken] = useState<string | null>(null);
  const [method, setMethod] = useState<'totp' | 'sms' | 'email'>('totp');
  const [error, setError] = useState<string | null>(null);

  const redirectUrl = searchParams.get('redirect') || '/dashboard';

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    // Create MFA challenge
    const initChallenge = async () => {
      try {
        const result = await createChallenge(user.id);
        setSessionToken(result.sessionToken);
        
        // Set primary method
        const primaryMethod = result.methods.find(m => m.isPrimary);
        if (primaryMethod) {
          setMethod(primaryMethod.method as any);
        }
      } catch (err) {
        setError(err.message);
      }
    };

    initChallenge();
  }, [user, createChallenge, router]);

  const handleSuccess = () => {
    // Set MFA verified cookie
    document.cookie = `mfa_verified=true; path=/; max-age=${12 * 60 * 60}`; // 12 hours
    
    // Redirect to original destination
    router.push(redirectUrl);
  };

  const handleCancel = () => {
    // Log out user
    router.push('/api/auth/logout');
  };

  if (loading || !sessionToken) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-2">Error</h1>
          <p className="text-muted-foreground">{error}</p>
          <button
            onClick={() => router.push('/login')}
            className="mt-4 text-primary hover:underline"
          >
            Back to login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <MFAVerify
        sessionToken={sessionToken}
        method={method}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
