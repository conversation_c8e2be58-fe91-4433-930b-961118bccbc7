'use client';

import { createContext, useContext, useEffect, ReactNode } from 'react';
import { logCollector } from '@/lib/services/log-collector-service';
import { auditService } from '@/lib/services/enhanced-audit-service';

interface AuditProviderProps {
  children: ReactNode;
}

const AuditContext = createContext<{
  initialized: boolean;
}>({
  initialized: false
});

export function AuditProvider({ children }: AuditProviderProps) {
  useEffect(() => {
    // Initialize log collectors
    logCollector.initialize();
    
    // Log application startup
    logCollector.logSystemEvent({
      type: 'startup',
      source: 'AuditProvider',
      message: 'Application started',
      metadata: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

    // Clean up on unmount
    return () => {
      // Log application shutdown
      logCollector.logSystemEvent({
        type: 'shutdown',
        source: 'AuditProvider',
        message: 'Application stopped',
        timestamp: new Date().toISOString()
      });
      
      // Destroy audit service to flush pending logs
      auditService.destroy();
    };
  }, []);

  return (
    <AuditContext.Provider value={{ initialized: true }}>
      {children}
    </AuditContext.Provider>
  );
}

export const useAudit = () => {
  const context = useContext(AuditContext);
  if (!context) {
    throw new Error('useAudit must be used within AuditProvider');
  }
  return context;
};