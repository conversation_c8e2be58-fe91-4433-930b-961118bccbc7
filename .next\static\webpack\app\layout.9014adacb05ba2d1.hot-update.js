"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   canAccessDepartment: () => (/* binding */ canAccessDepartment),\n/* harmony export */   getCurrentSession: () => (/* binding */ getCurrentSession),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   rolePermissions: () => (/* binding */ rolePermissions),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env */ \"(app-pages-browser)/./lib/env.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n// Validate Supabase configuration\nconst supabaseConfig = (0,_env__WEBPACK_IMPORTED_MODULE_0__.validateSupabaseConfig)();\n// Create Supabase client for authentication\nconst supabase = supabaseConfig.isValid ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(_env__WEBPACK_IMPORTED_MODULE_0__.config.supabase.url, _env__WEBPACK_IMPORTED_MODULE_0__.config.supabase.anonKey) : null;\n// User roles enum\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"GLOBAL_ADMIN\"] = \"Global Administrator\";\n    UserRole[\"SYSTEM_ADMIN\"] = \"Web App System Administrator\";\n    UserRole[\"DEPT_ADMIN\"] = \"Department Administrator\";\n    UserRole[\"HR_STAFF\"] = \"HR Staff\";\n    UserRole[\"IT_SUPPORT\"] = \"IT Helpdesk Support\";\n    UserRole[\"REGULAR_USER\"] = \"Regular User\";\n    return UserRole;\n}({});\n// Role definitions with permissions\nconst rolePermissions = {\n    [\"Global Administrator\"]: [\n        {\n            resource: '*',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'delete',\n            scope: 'all'\n        }\n    ],\n    [\"Web App System Administrator\"]: [\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'system',\n            action: 'read',\n            scope: 'all'\n        }\n    ],\n    [\"Department Administrator\"]: [\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'department'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'department'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'department'\n        },\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'department'\n        },\n        {\n            resource: 'requests',\n            action: 'update',\n            scope: 'department'\n        }\n    ],\n    [\"HR Staff\"]: [\n        {\n            resource: 'hr_requests',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'hr_requests',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'hr_requests',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'all'\n        }\n    ],\n    [\"IT Helpdesk Support\"]: [\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'requests',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'update',\n            scope: 'all'\n        }\n    ],\n    [\"Regular User\"]: [\n        {\n            resource: 'requests',\n            action: 'create',\n            scope: 'own'\n        },\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'own'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'read',\n            scope: 'all'\n        }\n    ]\n};\n// Authentication functions\nconst signIn = async (email, password)=>{\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    if (!supabase) {\n        return {\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\nconst signUp = async (email, password, userData)=>{\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: userData\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst getCurrentUser = async ()=>{\n    if (!supabase) {\n        return {\n            user: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data: { user }, error } = await supabase.auth.getUser();\n    return {\n        user,\n        error\n    };\n};\nconst getCurrentSession = async ()=>{\n    if (!supabase) {\n        return {\n            session: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data: { session }, error } = await supabase.auth.getSession();\n    return {\n        session,\n        error\n    };\n};\n// Get user with role information\nconst getUserWithRole = async (userId)=>{\n    // In development mode, return a mock user to avoid RLS errors\n    if (typeof process !== 'undefined' && \"development\" !== 'production') {\n        console.log('Development mode: Using mock user data');\n        return {\n            data: {\n                id: userId,\n                auth_id: userId,\n                email: '<EMAIL>',\n                first_name: 'Development',\n                last_name: 'User',\n                role: {\n                    name: 'admin'\n                },\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            error: null\n        };\n    }\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    try {\n        // First check if the user exists in the staff table\n        const { data, error } = await supabase.from('staff').select('*').eq('auth_id', userId).maybeSingle() // Use maybeSingle instead of single to avoid error if no record found\n        ;\n        if (error) {\n            console.error('Database error:', error.message);\n            return {\n                data: null,\n                error\n            };\n        }\n        // If staff record found, fetch related data separately\n        if (data) {\n            // Create an enriched staff object\n            const enrichedStaff = {\n                ...data\n            };\n            try {\n                // Fetch division data if division_id exists\n                if (data.division_id) {\n                    const { data: divisionData } = await supabase.from('divisions').select('*').eq('id', data.division_id).single();\n                    if (divisionData) {\n                        enrichedStaff.division = divisionData;\n                    }\n                }\n                // Fetch group data if group_id exists\n                if (data.group_id) {\n                    const { data: groupData } = await supabase.from('groups').select('*').eq('id', data.group_id).single();\n                    if (groupData) {\n                        enrichedStaff.group = groupData;\n                    }\n                }\n                // Fetch role data if role_id exists\n                if (data.role_id) {\n                    const { data: roleData } = await supabase.from('roles').select('*').eq('id', data.role_id).single();\n                    if (roleData) {\n                        enrichedStaff.role = roleData;\n                    }\n                }\n                return {\n                    data: enrichedStaff,\n                    error: null\n                };\n            } catch (fetchError) {\n                console.error('Error fetching related data:', fetchError);\n                // Return the basic staff data even if related data fetch fails\n                return {\n                    data,\n                    error: null\n                };\n            }\n        }\n        // If no staff record found, check if user exists in profiles table\n        if (!data) {\n            try {\n                // Try to get basic user profile information\n                const { data: profileData, error: profileError } = await supabase.from('profiles').select('*').eq('id', userId).maybeSingle();\n                if (profileError) {\n                    // Check if this is an RLS policy error\n                    if (profileError.message && (profileError.message.includes('infinite recursion') || profileError.message.includes('permission denied'))) {\n                        console.warn('RLS policy error for profiles table. Using basic user data instead.');\n                        // Instead of making another API call, create a minimal user object\n                        // This reduces the number of API calls and potential for more errors\n                        return {\n                            data: {\n                                id: userId,\n                                // Add minimal required fields\n                                role: {\n                                    name: 'regular_user'\n                                },\n                                // Add default values for required fields\n                                email: '',\n                                first_name: '',\n                                last_name: '',\n                                created_at: new Date().toISOString(),\n                                updated_at: new Date().toISOString()\n                            },\n                            error: null\n                        };\n                    } else {\n                        console.error('Profile lookup error:', profileError.message);\n                        return {\n                            data: null,\n                            error: profileError\n                        };\n                    }\n                }\n                // Return profile data if found, or null if not\n                return {\n                    data: profileData,\n                    error: null\n                };\n            } catch (profileErr) {\n                console.error('Error accessing profiles:', profileErr);\n                // Fallback to basic user data in development\n                if (true) {\n                    return {\n                        data: {\n                            id: userId,\n                            role: {\n                                name: 'regular_user'\n                            }\n                        },\n                        error: null\n                    };\n                }\n                return {\n                    data: null,\n                    error: profileErr instanceof Error ? profileErr : new Error('Unknown error')\n                };\n            }\n        }\n        // Return staff data if found\n        return {\n            data,\n            error: null\n        };\n    } catch (err) {\n        console.error('Unexpected error in getUserWithRole:', err);\n        return {\n            data: null,\n            error: err instanceof Error ? err : new Error('Unknown error')\n        };\n    }\n};\n// Check if user has specific permission\nconst hasPermission = (userRole, resource, action, scope)=>{\n    const permissions = rolePermissions[userRole];\n    if (!permissions) return false;\n    return permissions.some((permission)=>{\n        if (permission.resource === '*') return true;\n        if (permission.resource === resource && permission.action === action) {\n            if (scope && permission.scope !== scope && permission.scope !== 'all') {\n                return false;\n            }\n            return true;\n        }\n        return false;\n    });\n};\n// Check if user can access department data\nconst canAccessDepartment = (userRole, userDepartmentId, targetDepartmentId)=>{\n    if (userRole === \"Global Administrator\" || userRole === \"Web App System Administrator\") {\n        return true;\n    }\n    if (userRole === \"Department Administrator\") {\n        return userDepartmentId === targetDepartmentId;\n    }\n    if (userRole === \"HR Staff\" || userRole === \"IT Helpdesk Support\") {\n        return true;\n    }\n    if (userRole === \"Regular User\") {\n        return userDepartmentId === targetDepartmentId;\n    }\n    return false;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth.ts\n"));

/***/ })

});