/**
 * Workflow Template Manager
 * Manages workflow templates and their lifecycle
 */

import { WorkflowTemplate, WorkflowStep, WorkflowEngine } from './workflow-engine'

export interface TemplateFilters {
  isActive?: boolean
  category?: string
  search?: string
}

export interface TemplateValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export class WorkflowTemplateManager {
  constructor(
    private supabase: any,
    private auditService: any,
    private workflowEngine: WorkflowEngine
  ) {}

  async getTemplates(filters: TemplateFilters = {}): Promise<WorkflowTemplate[]> {
    try {
      // Simulate database query
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Return mock templates
      return [
        {
          id: 'template_1',
          name: 'IT Request Approval',
          description: 'Standard IT request approval workflow',
          steps: [
            {
              id: 'step_1',
              name: 'Manager Approval',
              type: 'approval',
              config: { approverRole: 'manager' },
              order: 1
            },
            {
              id: 'step_2',
              name: 'IT Review',
              type: 'approval',
              config: { approverRole: 'it_admin' },
              order: 2
            }
          ],
          isActive: true,
          version: 1
        }
      ]
    } catch (error) {
      console.error('Failed to get workflow templates:', error)
      return []
    }
  }

  async getTemplate(id: string): Promise<WorkflowTemplate | null> {
    try {
      // Simulate database query
      await new Promise(resolve => setTimeout(resolve, 100))
      
      return {
        id,
        name: 'Sample Template',
        description: 'A sample workflow template',
        steps: [],
        isActive: true,
        version: 1
      }
    } catch (error) {
      console.error('Failed to get workflow template:', error)
      return null
    }
  }

  async createTemplate(template: Omit<WorkflowTemplate, 'id' | 'version'>): Promise<WorkflowTemplate> {
    try {
      // Validate template
      const validation = this.validateTemplate(template)
      if (!validation.isValid) {
        throw new Error(`Template validation failed: ${validation.errors.join(', ')}`)
      }

      // Simulate database insert
      await new Promise(resolve => setTimeout(resolve, 200))
      
      const newTemplate: WorkflowTemplate = {
        ...template,
        id: `template_${Date.now()}`,
        version: 1
      }

      // Log the action
      await this.auditService.logAction({
        user_id: 'system',
        action: 'create',
        resource_type: 'workflow_template',
        resource_id: newTemplate.id,
        details: { templateName: template.name }
      })

      return newTemplate
    } catch (error) {
      console.error('Failed to create workflow template:', error)
      throw new Error('Failed to create workflow template')
    }
  }

  async updateTemplate(id: string, updates: Partial<WorkflowTemplate>): Promise<WorkflowTemplate> {
    try {
      // Validate updates
      if (updates.steps) {
        const validation = this.validateTemplate(updates as any)
        if (!validation.isValid) {
          throw new Error(`Template validation failed: ${validation.errors.join(', ')}`)
        }
      }

      // Simulate database update
      await new Promise(resolve => setTimeout(resolve, 150))
      
      const updatedTemplate: WorkflowTemplate = {
        id,
        name: 'Updated Template',
        steps: [],
        isActive: true,
        version: 1,
        ...updates
      }

      // Log the action
      await this.auditService.logAction({
        user_id: 'system',
        action: 'update',
        resource_type: 'workflow_template',
        resource_id: id,
        details: updates
      })

      return updatedTemplate
    } catch (error) {
      console.error('Failed to update workflow template:', error)
      throw new Error('Failed to update workflow template')
    }
  }

  async deleteTemplate(id: string): Promise<void> {
    try {
      // Check if template is in use
      const activeInstances = await this.workflowEngine.getActiveInstances()
      const inUse = activeInstances.some(instance => instance.templateId === id)
      
      if (inUse) {
        throw new Error('Cannot delete template that has active instances')
      }

      // Simulate database delete
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Log the action
      await this.auditService.logAction({
        user_id: 'system',
        action: 'delete',
        resource_type: 'workflow_template',
        resource_id: id
      })
    } catch (error) {
      console.error('Failed to delete workflow template:', error)
      throw new Error('Failed to delete workflow template')
    }
  }

  async applyTemplate(templateId: string, context: Record<string, any>): Promise<string> {
    try {
      const template = await this.getTemplate(templateId)
      if (!template) {
        throw new Error('Template not found')
      }

      if (!template.isActive) {
        throw new Error('Template is not active')
      }

      // Create workflow instance
      const instance = await this.workflowEngine.createInstance(templateId, context)
      
      // Log the action
      await this.auditService.logAction({
        user_id: context.userId || 'system',
        action: 'apply',
        resource_type: 'workflow_template',
        resource_id: templateId,
        details: { instanceId: instance.id }
      })

      return instance.id
    } catch (error) {
      console.error('Failed to apply workflow template:', error)
      throw new Error('Failed to apply workflow template')
    }
  }

  validateTemplate(template: Partial<WorkflowTemplate>): TemplateValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Basic validation
    if (!template.name || template.name.trim().length === 0) {
      errors.push('Template name is required')
    }

    if (!template.steps || template.steps.length === 0) {
      errors.push('Template must have at least one step')
    }

    // Step validation
    if (template.steps) {
      const stepOrders = template.steps.map(step => step.order)
      const uniqueOrders = new Set(stepOrders)
      
      if (stepOrders.length !== uniqueOrders.size) {
        errors.push('Step orders must be unique')
      }

      template.steps.forEach((step, index) => {
        if (!step.name || step.name.trim().length === 0) {
          errors.push(`Step ${index + 1}: Name is required`)
        }

        if (!step.type) {
          errors.push(`Step ${index + 1}: Type is required`)
        }

        if (step.order < 1) {
          errors.push(`Step ${index + 1}: Order must be greater than 0`)
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  async duplicateTemplate(id: string, newName: string): Promise<WorkflowTemplate> {
    try {
      const template = await this.getTemplate(id)
      if (!template) {
        throw new Error('Template not found')
      }

      const duplicatedTemplate = await this.createTemplate({
        name: newName,
        description: `Copy of ${template.description || template.name}`,
        steps: template.steps.map(step => ({ ...step, id: `${step.id}_copy` })),
        isActive: false // Start as inactive
      })

      return duplicatedTemplate
    } catch (error) {
      console.error('Failed to duplicate workflow template:', error)
      throw new Error('Failed to duplicate workflow template')
    }
  }
}
