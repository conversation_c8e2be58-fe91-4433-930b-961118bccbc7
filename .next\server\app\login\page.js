/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cproviders%5C%5Cai-provider.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Ci18n%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cproviders%5C%5Cai-provider.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Ci18n%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/ai-provider.tsx */ \"(rsc)/./components/providers/ai-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(rsc)/./lib/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/i18n/context.tsx */ \"(rsc)/./lib/i18n/context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByaW5jJTVDJTVDaXRzeW5jLXByb2plY3QlNUMlNUNDbGF1ZGUlNUMlNUNEZW1vMSU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByaW5jJTVDJTVDaXRzeW5jLXByb2plY3QlNUMlNUNDbGF1ZGUlNUMlNUNEZW1vMSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwcm92aWRlcnMlNUMlNUNhaS1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBSVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByaW5jJTVDJTVDaXRzeW5jLXByb2plY3QlNUMlNUNDbGF1ZGUlNUMlNUNEZW1vMSU1QyU1Q2xpYiU1QyU1Q2F1dGgtY29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcHJpbmMlNUMlNUNpdHN5bmMtcHJvamVjdCU1QyU1Q0NsYXVkZSU1QyU1Q0RlbW8xJTVDJTVDbGliJTVDJTVDaTE4biU1QyU1Q2NvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySTE4blByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByaW5jJTVDJTVDaXRzeW5jLXByb2plY3QlNUMlNUNDbGF1ZGUlNUMlNUNEZW1vMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmluYyU1QyU1Q2l0c3luYy1wcm9qZWN0JTVDJTVDQ2xhdWRlJTVDJTVDRGVtbzElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMk5vdG9fU2Fuc19KUCU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCUyQyU1QyUyMndlaWdodCU1QyUyMiUzQSU1QiU1QyUyMjQwMCU1QyUyMiUyQyU1QyUyMjUwMCU1QyUyMiUyQyU1QyUyMjcwMCU1QyUyMiU1RCUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LW5vdG8tc2Fucy1qcCU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMm5vdG9TYW5zSlAlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUE0SjtBQUM1SjtBQUNBLHdKQUE2STtBQUM3STtBQUNBLHdKQUE4SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQUlQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHByaW5jXFxcXGl0c3luYy1wcm9qZWN0XFxcXENsYXVkZVxcXFxEZW1vMVxcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxhaS1wcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHByaW5jXFxcXGl0c3luYy1wcm9qZWN0XFxcXENsYXVkZVxcXFxEZW1vMVxcXFxsaWJcXFxcYXV0aC1jb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiSTE4blByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJpbmNcXFxcaXRzeW5jLXByb2plY3RcXFxcQ2xhdWRlXFxcXERlbW8xXFxcXGxpYlxcXFxpMThuXFxcXGNvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cproviders%5C%5Cai-provider.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Ci18n%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cproviders%5C%5Cai-provider.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Ci18n%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cproviders%5C%5Cai-provider.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Ci18n%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/ai-provider.tsx */ \"(ssr)/./components/providers/ai-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(ssr)/./lib/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/i18n/context.tsx */ \"(ssr)/./lib/i18n/context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cproviders%5C%5Cai-provider.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Clib%5C%5Ci18n%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_JP%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-jp%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansJP%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cauth%5C%5Clogin-form.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cauth%5C%5Clogin-form.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/login-form.tsx */ \"(rsc)/./components/auth/login-form.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByaW5jJTVDJTVDaXRzeW5jLXByb2plY3QlNUMlNUNDbGF1ZGUlNUMlNUNEZW1vMSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoJTVDJTVDbG9naW4tZm9ybS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBbUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwcmluY1xcXFxpdHN5bmMtcHJvamVjdFxcXFxDbGF1ZGVcXFxcRGVtbzFcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXGxvZ2luLWZvcm0udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cauth%5C%5Clogin-form.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cauth%5C%5Clogin-form.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cauth%5C%5Clogin-form.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/login-form.tsx */ \"(ssr)/./components/auth/login-form.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByaW5jJTVDJTVDaXRzeW5jLXByb2plY3QlNUMlNUNDbGF1ZGUlNUMlNUNEZW1vMSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoJTVDJTVDbG9naW4tZm9ybS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBbUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwcmluY1xcXFxpdHN5bmMtcHJvamVjdFxcXFxDbGF1ZGVcXFxcRGVtbzFcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXGxvZ2luLWZvcm0udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Ccomponents%5C%5Cauth%5C%5Clogin-form.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cprinc%5C%5Citsync-project%5C%5CClaude%5C%5CDemo1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/auth/login-form.tsx":
/*!****************************************!*\
  !*** ./components/auth/login-form.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/validation */ \"(ssr)/./lib/validation.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { signIn } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        // Validate input\n        if (!(0,_lib_validation__WEBPACK_IMPORTED_MODULE_4__.validateEmail)(email)) {\n            setError('有効なメールアドレスを入力してください / Please enter a valid email address');\n            setLoading(false);\n            return;\n        }\n        if (password.length < 8) {\n            setError('パスワードは8文字以上で入力してください / Password must be at least 8 characters');\n            setLoading(false);\n            return;\n        }\n        try {\n            const { data, error: signInError } = await signIn(email, password);\n            if (signInError) {\n                setError(signInError.message);\n            } else if (data?.user) {\n                router.push('/dashboard');\n            }\n        } catch (err) {\n            setError('ログインに失敗しました / Login failed');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                            className: \"text-2xl font-bold text-center\",\n                            children: \"ITSync ログイン\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                            className: \"text-center\",\n                            children: \"アカウントにサインインしてください\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                variant: \"destructive\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        htmlFor: \"email\",\n                                        children: \"メールアドレス / Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        required: true,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        htmlFor: \"password\",\n                                        children: \"パスワード / Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"password\",\n                                                type: showPassword ? 'text' : 'password',\n                                                placeholder: \"••••••••\",\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                required: true,\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                onClick: ()=>setShowPassword(!showPassword),\n                                                disabled: loading,\n                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"submit\",\n                                className: \"w-full\",\n                                disabled: loading,\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 27\n                                    }, this),\n                                    \"サインイン / Sign In\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/login-form.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/ai-provider.tsx":
/*!**********************************************!*\
  !*** ./components/providers/ai-provider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIProvider: () => (/* binding */ AIProvider),\n/* harmony export */   useAI: () => (/* binding */ useAI),\n/* harmony export */   useAICompletion: () => (/* binding */ useAICompletion),\n/* harmony export */   useAIFormValidation: () => (/* binding */ useAIFormValidation),\n/* harmony export */   useAITranslation: () => (/* binding */ useAITranslation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_config_ai_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config/ai-config */ \"(ssr)/./lib/config/ai-config.ts\");\n/* harmony import */ var _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/ai-api-service */ \"(ssr)/./lib/services/ai-api-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AIProvider,useAI,useAICompletion,useAIFormValidation,useAITranslation auto */ \n\n\n\nconst AIContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AIProvider({ children }) {\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConfigured, setIsConfigured] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIProvider.useEffect\": ()=>{\n            const aiConfig = (0,_lib_config_ai_config__WEBPACK_IMPORTED_MODULE_2__.getAIConfig)();\n            const validation = (0,_lib_config_ai_config__WEBPACK_IMPORTED_MODULE_2__.validateAIConfig)(aiConfig);\n            setConfig(aiConfig);\n            setIsConfigured(validation.isValid);\n            setErrors(validation.errors);\n            if (validation.isValid && aiConfig.defaultProvider) {\n                _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__.aiAPIService.setDefaultProvider(aiConfig.defaultProvider);\n            }\n        }\n    }[\"AIProvider.useEffect\"], []);\n    const updateConfig = (newConfig)=>{\n        if (!config) return;\n        const updatedConfig = {\n            ...config,\n            ...newConfig\n        };\n        const validation = (0,_lib_config_ai_config__WEBPACK_IMPORTED_MODULE_2__.validateAIConfig)(updatedConfig);\n        setConfig(updatedConfig);\n        setIsConfigured(validation.isValid);\n        setErrors(validation.errors);\n        if (validation.isValid && updatedConfig.defaultProvider) {\n            _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__.aiAPIService.setDefaultProvider(updatedConfig.defaultProvider);\n        }\n    };\n    const testConnection = async (provider)=>{\n        try {\n            const response = await _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__.aiAPIService.completion({\n                prompt: 'Hello, this is a test.',\n                systemPrompt: 'Respond with \"Connection successful\"',\n                maxTokens: 50,\n                temperature: 0\n            }, provider);\n            return response.text.includes('successful');\n        } catch (error) {\n            console.error(`Failed to test ${provider} connection:`, error);\n            return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIContext.Provider, {\n        value: {\n            config,\n            isConfigured,\n            errors,\n            updateConfig,\n            testConnection\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\providers\\\\ai-provider.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction useAI() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIContext);\n    if (context === undefined) {\n        throw new Error('useAI must be used within an AIProvider');\n    }\n    return context;\n}\n// Hook for using AI completion\nfunction useAICompletion() {\n    const { isConfigured } = useAI();\n    const complete = async (prompt, options)=>{\n        if (!isConfigured) {\n            throw new Error('AI is not configured. Please check your API keys.');\n        }\n        return _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__.aiAPIService.completion({\n            prompt,\n            systemPrompt: options?.systemPrompt,\n            temperature: options?.temperature,\n            maxTokens: options?.maxTokens\n        }, options?.provider);\n    };\n    return {\n        complete,\n        isConfigured\n    };\n}\n// Hook for form field validation\nfunction useAIFormValidation() {\n    const { isConfigured } = useAI();\n    const validateField = async (fieldType, value, context)=>{\n        if (!isConfigured) {\n            return {\n                isValid: true,\n                issues: [],\n                suggestions: []\n            };\n        }\n        return _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__.aiAPIService.validateFormField(fieldType, value, context);\n    };\n    return {\n        validateField,\n        isConfigured\n    };\n}\n// Hook for text translation\nfunction useAITranslation() {\n    const { isConfigured } = useAI();\n    const translate = async (text, from, to)=>{\n        if (!isConfigured || from === to) {\n            return text;\n        }\n        return _lib_services_ai_api_service__WEBPACK_IMPORTED_MODULE_3__.aiAPIService.translateText(text, from, to);\n    };\n    return {\n        translate,\n        isConfigured\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/ai-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByaW5jXFxpdHN5bmMtcHJvamVjdFxcQ2xhdWRlXFxEZW1vMVxcY29tcG9uZW50c1xcdWlcXGlucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJpbmNcXGl0c3luYy1wcm9qZWN0XFxDbGF1ZGVcXERlbW8xXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./lib/auth.ts\");\n/* harmony import */ var _lib_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/env */ \"(ssr)/./lib/env.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [staff, setStaff] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isConfigured, setIsConfigured] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadStaffData = async (userId)=>{\n        try {\n            const { data, error } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getUserWithRole)(userId);\n            if (error) {\n                console.error('Error loading staff data:', error);\n                // Set default role for development/testing\n                if (true) {\n                    setUserRole(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.UserRole.REGULAR_USER);\n                }\n            } else if (data) {\n                setStaff(data);\n                // Handle the case where role data might not be available\n                if (data.role && data.role.name) {\n                    setUserRole(data.role.name);\n                } else if (data.role_id) {\n                    // If we have role_id but no role object, set a default based on the ID\n                    // This is a fallback for when relationships aren't working\n                    setUserRole(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.UserRole.REGULAR_USER);\n                } else {\n                    // Default role if no role information is available\n                    setUserRole(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.UserRole.REGULAR_USER);\n                }\n            }\n        } catch (error) {\n            console.error('Error loading staff data:', error);\n            // Set default role for development/testing\n            if (true) {\n                setUserRole(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.UserRole.REGULAR_USER);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if Supabase is configured\n            if (!_lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase || (0,_lib_env__WEBPACK_IMPORTED_MODULE_3__.isDevelopmentWithoutConfig)()) {\n                setIsConfigured(false);\n                setLoading(false);\n                return;\n            }\n            setIsConfigured(true);\n            const getSession = {\n                \"AuthProvider.useEffect.getSession\": async ()=>{\n                    try {\n                        // Check if supabase is null before accessing it\n                        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase) {\n                            setLoading(false);\n                            return;\n                        }\n                        const { data: { session }, error } = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                        if (error) {\n                            console.error('Error getting session:', error);\n                        } else {\n                            setSession(session);\n                            setUser(session?.user ?? null);\n                            if (session?.user) {\n                                await loadStaffData(session.user.id);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error in getSession:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.getSession\"];\n            getSession();\n            // Only set up auth state change listener if supabase is configured\n            let subscription;\n            if (_lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase) {\n                const { data } = _lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                    \"AuthProvider.useEffect\": async (event, session)=>{\n                        setSession(session);\n                        setUser(session?.user ?? null);\n                        if (session?.user) {\n                            await loadStaffData(session.user.id);\n                        } else {\n                            setStaff(null);\n                            setUserRole(null);\n                        }\n                        setLoading(false);\n                    }\n                }[\"AuthProvider.useEffect\"]);\n                // Store the subscription for cleanup\n                subscription = data.subscription;\n            }\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    // Only unsubscribe if subscription exists\n                    subscription?.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const handleSignIn = async (email, password)=>{\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase) {\n            return {\n                data: null,\n                error: new Error('Supabase is not configured')\n            };\n        }\n        const { data, error } = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const handleSignOut = async ()=>{\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase) {\n            return {\n                error: new Error('Supabase is not configured')\n            };\n        }\n        const { error } = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n        return {\n            error\n        };\n    };\n    const handleSignUp = async (email, password, userData)=>{\n        if (!_lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase) {\n            return {\n                data: null,\n                error: new Error('Supabase is not configured')\n            };\n        }\n        const { data, error } = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: userData\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const value = {\n        user,\n        session,\n        staff,\n        userRole,\n        loading,\n        isConfigured,\n        signIn: handleSignIn,\n        signOut: handleSignOut,\n        signUp: handleSignUp\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   canAccessDepartment: () => (/* binding */ canAccessDepartment),\n/* harmony export */   getCurrentSession: () => (/* binding */ getCurrentSession),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   rolePermissions: () => (/* binding */ rolePermissions),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env */ \"(ssr)/./lib/env.ts\");\n\n\n// Validate Supabase configuration\nconst supabaseConfig = (0,_env__WEBPACK_IMPORTED_MODULE_0__.validateSupabaseConfig)();\n// Create Supabase client for authentication\nconst supabase = supabaseConfig.isValid ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(_env__WEBPACK_IMPORTED_MODULE_0__.config.supabase.url, _env__WEBPACK_IMPORTED_MODULE_0__.config.supabase.anonKey) : null;\n// User roles enum\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"GLOBAL_ADMIN\"] = \"Global Administrator\";\n    UserRole[\"SYSTEM_ADMIN\"] = \"Web App System Administrator\";\n    UserRole[\"DEPT_ADMIN\"] = \"Department Administrator\";\n    UserRole[\"HR_STAFF\"] = \"HR Staff\";\n    UserRole[\"IT_SUPPORT\"] = \"IT Helpdesk Support\";\n    UserRole[\"REGULAR_USER\"] = \"Regular User\";\n    return UserRole;\n}({});\n// Role definitions with permissions\nconst rolePermissions = {\n    [\"Global Administrator\"]: [\n        {\n            resource: '*',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: '*',\n            action: 'delete',\n            scope: 'all'\n        }\n    ],\n    [\"Web App System Administrator\"]: [\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'roles',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'system',\n            action: 'read',\n            scope: 'all'\n        }\n    ],\n    [\"Department Administrator\"]: [\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'department'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'department'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'department'\n        },\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'department'\n        },\n        {\n            resource: 'requests',\n            action: 'update',\n            scope: 'department'\n        }\n    ],\n    [\"HR Staff\"]: [\n        {\n            resource: 'hr_requests',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'hr_requests',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'hr_requests',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'users',\n            action: 'update',\n            scope: 'all'\n        }\n    ],\n    [\"IT Helpdesk Support\"]: [\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'requests',\n            action: 'update',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'create',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'read',\n            scope: 'all'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'update',\n            scope: 'all'\n        }\n    ],\n    [\"Regular User\"]: [\n        {\n            resource: 'requests',\n            action: 'create',\n            scope: 'own'\n        },\n        {\n            resource: 'requests',\n            action: 'read',\n            scope: 'own'\n        },\n        {\n            resource: 'knowledge_base',\n            action: 'read',\n            scope: 'all'\n        }\n    ]\n};\n// Authentication functions\nconst signIn = async (email, password)=>{\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    if (!supabase) {\n        return {\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\nconst signUp = async (email, password, userData)=>{\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: userData\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst getCurrentUser = async ()=>{\n    if (!supabase) {\n        return {\n            user: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data: { user }, error } = await supabase.auth.getUser();\n    return {\n        user,\n        error\n    };\n};\nconst getCurrentSession = async ()=>{\n    if (!supabase) {\n        return {\n            session: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    const { data: { session }, error } = await supabase.auth.getSession();\n    return {\n        session,\n        error\n    };\n};\n// Get user with role information\nconst getUserWithRole = async (userId)=>{\n    // In development mode, return a mock user to avoid RLS errors\n    if (typeof process !== 'undefined' && \"development\" !== 'production') {\n        console.log('Development mode: Using mock user data');\n        return {\n            data: {\n                id: userId,\n                auth_id: userId,\n                email: '<EMAIL>',\n                first_name: 'Development',\n                last_name: 'User',\n                role: {\n                    name: 'admin'\n                },\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            error: null\n        };\n    }\n    if (!supabase) {\n        return {\n            data: null,\n            error: new Error('Supabase is not configured')\n        };\n    }\n    try {\n        // First check if the user exists in the staff table\n        const { data, error } = await supabase.from('staff').select('*').eq('auth_id', userId).maybeSingle() // Use maybeSingle instead of single to avoid error if no record found\n        ;\n        if (error) {\n            console.error('Database error:', error.message);\n            return {\n                data: null,\n                error\n            };\n        }\n        // If staff record found, fetch related data separately\n        if (data) {\n            // Create an enriched staff object\n            const enrichedStaff = {\n                ...data\n            };\n            try {\n                // Fetch division data if division_id exists\n                if (data.division_id) {\n                    const { data: divisionData } = await supabase.from('divisions').select('*').eq('id', data.division_id).single();\n                    if (divisionData) {\n                        enrichedStaff.division = divisionData;\n                    }\n                }\n                // Fetch group data if group_id exists\n                if (data.group_id) {\n                    const { data: groupData } = await supabase.from('groups').select('*').eq('id', data.group_id).single();\n                    if (groupData) {\n                        enrichedStaff.group = groupData;\n                    }\n                }\n                // Fetch role data if role_id exists\n                if (data.role_id) {\n                    const { data: roleData } = await supabase.from('roles').select('*').eq('id', data.role_id).single();\n                    if (roleData) {\n                        enrichedStaff.role = roleData;\n                    }\n                }\n                return {\n                    data: enrichedStaff,\n                    error: null\n                };\n            } catch (fetchError) {\n                console.error('Error fetching related data:', fetchError);\n                // Return the basic staff data even if related data fetch fails\n                return {\n                    data,\n                    error: null\n                };\n            }\n        }\n        // If no staff record found, check if user exists in profiles table\n        if (!data) {\n            try {\n                // Try to get basic user profile information\n                const { data: profileData, error: profileError } = await supabase.from('profiles').select('*').eq('id', userId).maybeSingle();\n                if (profileError) {\n                    // Check if this is an RLS policy error\n                    if (profileError.message && (profileError.message.includes('infinite recursion') || profileError.message.includes('permission denied'))) {\n                        console.warn('RLS policy error for profiles table. Using basic user data instead.');\n                        // Instead of making another API call, create a minimal user object\n                        // This reduces the number of API calls and potential for more errors\n                        return {\n                            data: {\n                                id: userId,\n                                // Add minimal required fields\n                                role: {\n                                    name: 'regular_user'\n                                },\n                                // Add default values for required fields\n                                email: '',\n                                first_name: '',\n                                last_name: '',\n                                created_at: new Date().toISOString(),\n                                updated_at: new Date().toISOString()\n                            },\n                            error: null\n                        };\n                    } else {\n                        console.error('Profile lookup error:', profileError.message);\n                        return {\n                            data: null,\n                            error: profileError\n                        };\n                    }\n                }\n                // Return profile data if found, or null if not\n                return {\n                    data: profileData,\n                    error: null\n                };\n            } catch (profileErr) {\n                console.error('Error accessing profiles:', profileErr);\n                // Fallback to basic user data in development\n                if (typeof process !== 'undefined' && \"development\" !== 'production') {\n                    return {\n                        data: {\n                            id: userId,\n                            role: {\n                                name: 'regular_user'\n                            }\n                        },\n                        error: null\n                    };\n                }\n                return {\n                    data: null,\n                    error: profileErr instanceof Error ? profileErr : new Error('Unknown error')\n                };\n            }\n        }\n        // Return staff data if found\n        return {\n            data,\n            error: null\n        };\n    } catch (err) {\n        console.error('Unexpected error in getUserWithRole:', err);\n        return {\n            data: null,\n            error: err instanceof Error ? err : new Error('Unknown error')\n        };\n    }\n};\n// Check if user has specific permission\nconst hasPermission = (userRole, resource, action, scope)=>{\n    const permissions = rolePermissions[userRole];\n    if (!permissions) return false;\n    return permissions.some((permission)=>{\n        if (permission.resource === '*') return true;\n        if (permission.resource === resource && permission.action === action) {\n            if (scope && permission.scope !== scope && permission.scope !== 'all') {\n                return false;\n            }\n            return true;\n        }\n        return false;\n    });\n};\n// Check if user can access department data\nconst canAccessDepartment = (userRole, userDepartmentId, targetDepartmentId)=>{\n    if (userRole === \"Global Administrator\" || userRole === \"Web App System Administrator\") {\n        return true;\n    }\n    if (userRole === \"Department Administrator\") {\n        return userDepartmentId === targetDepartmentId;\n    }\n    if (userRole === \"HR Staff\" || userRole === \"IT Helpdesk Support\") {\n        return true;\n    }\n    if (userRole === \"Regular User\") {\n        return userDepartmentId === targetDepartmentId;\n    }\n    return false;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/config/ai-config.ts":
/*!*********************************!*\
  !*** ./lib/config/ai-config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AI_MODELS: () => (/* binding */ AI_MODELS),\n/* harmony export */   getAIConfig: () => (/* binding */ getAIConfig),\n/* harmony export */   validateAIConfig: () => (/* binding */ validateAIConfig)\n/* harmony export */ });\n// lib/config/ai-config.ts\n// Configuration management for AI services\nfunction getAIConfig() {\n    return {\n        providers: {\n            openai: process.env.OPENAI_API_KEY ? {\n                apiKey: process.env.OPENAI_API_KEY,\n                baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',\n                model: process.env.OPENAI_MODEL || 'gpt-4-turbo-preview'\n            } : undefined,\n            anthropic: process.env.ANTHROPIC_API_KEY ? {\n                apiKey: process.env.ANTHROPIC_API_KEY,\n                baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com/v1',\n                model: process.env.ANTHROPIC_MODEL || 'claude-3-opus-20240229'\n            } : undefined\n        },\n        defaultProvider: process.env.DEFAULT_AI_PROVIDER || 'openai',\n        parameters: {\n            temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),\n            maxTokens: parseInt(process.env.AI_MAX_TOKENS || '1000', 10)\n        }\n    };\n}\nfunction validateAIConfig(config) {\n    const errors = [];\n    // Check if at least one provider is configured\n    if (!config.providers.openai && !config.providers.anthropic) {\n        errors.push('No AI providers configured. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY.');\n    }\n    // Check if default provider is configured\n    if (config.defaultProvider === 'openai' && !config.providers.openai) {\n        errors.push('Default provider is set to OpenAI but OpenAI is not configured.');\n    }\n    if (config.defaultProvider === 'anthropic' && !config.providers.anthropic) {\n        errors.push('Default provider is set to Anthropic but Anthropic is not configured.');\n    }\n    // Validate temperature\n    if (config.parameters.temperature < 0 || config.parameters.temperature > 2) {\n        errors.push('Temperature must be between 0 and 2.');\n    }\n    // Validate max tokens\n    if (config.parameters.maxTokens < 1 || config.parameters.maxTokens > 32000) {\n        errors.push('Max tokens must be between 1 and 32000.');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n// Available models for each provider\nconst AI_MODELS = {\n    openai: [\n        {\n            id: 'gpt-4-turbo-preview',\n            name: 'GPT-4 Turbo',\n            maxTokens: 128000\n        },\n        {\n            id: 'gpt-4',\n            name: 'GPT-4',\n            maxTokens: 8192\n        },\n        {\n            id: 'gpt-3.5-turbo',\n            name: 'GPT-3.5 Turbo',\n            maxTokens: 16385\n        }\n    ],\n    anthropic: [\n        {\n            id: 'claude-3-opus-20240229',\n            name: 'Claude 3 Opus',\n            maxTokens: 200000\n        },\n        {\n            id: 'claude-3-sonnet-20240229',\n            name: 'Claude 3 Sonnet',\n            maxTokens: 200000\n        },\n        {\n            id: 'claude-3-haiku-20240307',\n            name: 'Claude 3 Haiku',\n            maxTokens: 200000\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvY29uZmlnL2FpLWNvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSwwQkFBMEI7QUFDMUIsMkNBQTJDO0FBc0JwQyxTQUFTQTtJQUNkLE9BQU87UUFDTEMsV0FBVztZQUNUQyxRQUFRQyxRQUFRQyxHQUFHLENBQUNDLGNBQWMsR0FBRztnQkFDbkNDLFFBQVFILFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYztnQkFDbENFLFNBQVNKLFFBQVFDLEdBQUcsQ0FBQ0ksZUFBZSxJQUFJO2dCQUN4Q0MsT0FBT04sUUFBUUMsR0FBRyxDQUFDTSxZQUFZLElBQUk7WUFDckMsSUFBSUM7WUFDSkMsV0FBV1QsUUFBUUMsR0FBRyxDQUFDUyxpQkFBaUIsR0FBRztnQkFDekNQLFFBQVFILFFBQVFDLEdBQUcsQ0FBQ1MsaUJBQWlCO2dCQUNyQ04sU0FBU0osUUFBUUMsR0FBRyxDQUFDVSxrQkFBa0IsSUFBSTtnQkFDM0NMLE9BQU9OLFFBQVFDLEdBQUcsQ0FBQ1csZUFBZSxJQUFJO1lBQ3hDLElBQUlKO1FBQ047UUFDQUssaUJBQWlCLFFBQVNaLEdBQUcsQ0FBQ2EsbUJBQW1CLElBQStCO1FBQ2hGQyxZQUFZO1lBQ1ZDLGFBQWFDLFdBQVdqQixRQUFRQyxHQUFHLENBQUNpQixjQUFjLElBQUk7WUFDdERDLFdBQVdDLFNBQVNwQixRQUFRQyxHQUFHLENBQUNvQixhQUFhLElBQUksUUFBUTtRQUMzRDtJQUNGO0FBQ0Y7QUFFTyxTQUFTQyxpQkFBaUJDLE1BQWdCO0lBQy9DLE1BQU1DLFNBQW1CLEVBQUU7SUFFM0IsK0NBQStDO0lBQy9DLElBQUksQ0FBQ0QsT0FBT3pCLFNBQVMsQ0FBQ0MsTUFBTSxJQUFJLENBQUN3QixPQUFPekIsU0FBUyxDQUFDVyxTQUFTLEVBQUU7UUFDM0RlLE9BQU9DLElBQUksQ0FBQztJQUNkO0lBQ0EsMENBQTBDO0lBQzFDLElBQUlGLE9BQU9WLGVBQWUsS0FBSyxZQUFZLENBQUNVLE9BQU96QixTQUFTLENBQUNDLE1BQU0sRUFBRTtRQUNuRXlCLE9BQU9DLElBQUksQ0FBQztJQUNkO0lBRUEsSUFBSUYsT0FBT1YsZUFBZSxLQUFLLGVBQWUsQ0FBQ1UsT0FBT3pCLFNBQVMsQ0FBQ1csU0FBUyxFQUFFO1FBQ3pFZSxPQUFPQyxJQUFJLENBQUM7SUFDZDtJQUVBLHVCQUF1QjtJQUN2QixJQUFJRixPQUFPUixVQUFVLENBQUNDLFdBQVcsR0FBRyxLQUFLTyxPQUFPUixVQUFVLENBQUNDLFdBQVcsR0FBRyxHQUFHO1FBQzFFUSxPQUFPQyxJQUFJLENBQUM7SUFDZDtJQUVBLHNCQUFzQjtJQUN0QixJQUFJRixPQUFPUixVQUFVLENBQUNJLFNBQVMsR0FBRyxLQUFLSSxPQUFPUixVQUFVLENBQUNJLFNBQVMsR0FBRyxPQUFPO1FBQzFFSyxPQUFPQyxJQUFJLENBQUM7SUFDZDtJQUVBLE9BQU87UUFDTEMsU0FBU0YsT0FBT0csTUFBTSxLQUFLO1FBQzNCSDtJQUNGO0FBQ0Y7QUFFQSxxQ0FBcUM7QUFDOUIsTUFBTUksWUFBWTtJQUN2QjdCLFFBQVE7UUFDTjtZQUFFOEIsSUFBSTtZQUF1QkMsTUFBTTtZQUFlWCxXQUFXO1FBQU87UUFDcEU7WUFBRVUsSUFBSTtZQUFTQyxNQUFNO1lBQVNYLFdBQVc7UUFBSztRQUM5QztZQUFFVSxJQUFJO1lBQWlCQyxNQUFNO1lBQWlCWCxXQUFXO1FBQU07S0FDaEU7SUFDRFYsV0FBVztRQUNUO1lBQUVvQixJQUFJO1lBQTBCQyxNQUFNO1lBQWlCWCxXQUFXO1FBQU87UUFDekU7WUFBRVUsSUFBSTtZQUE0QkMsTUFBTTtZQUFtQlgsV0FBVztRQUFPO1FBQzdFO1lBQUVVLElBQUk7WUFBMkJDLE1BQU07WUFBa0JYLFdBQVc7UUFBTztLQUM1RTtBQUNILEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJpbmNcXGl0c3luYy1wcm9qZWN0XFxDbGF1ZGVcXERlbW8xXFxsaWJcXGNvbmZpZ1xcYWktY29uZmlnLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9jb25maWcvYWktY29uZmlnLnRzXG4vLyBDb25maWd1cmF0aW9uIG1hbmFnZW1lbnQgZm9yIEFJIHNlcnZpY2VzXG5cbmV4cG9ydCBpbnRlcmZhY2UgQUlDb25maWcge1xuICBwcm92aWRlcnM6IHtcbiAgICBvcGVuYWk/OiB7XG4gICAgICBhcGlLZXk6IHN0cmluZ1xuICAgICAgYmFzZVVybD86IHN0cmluZ1xuICAgICAgbW9kZWw/OiBzdHJpbmdcbiAgICB9XG4gICAgYW50aHJvcGljPzoge1xuICAgICAgYXBpS2V5OiBzdHJpbmdcbiAgICAgIGJhc2VVcmw/OiBzdHJpbmdcbiAgICAgIG1vZGVsPzogc3RyaW5nXG4gICAgfVxuICB9XG4gIGRlZmF1bHRQcm92aWRlcjogJ29wZW5haScgfCAnYW50aHJvcGljJ1xuICBwYXJhbWV0ZXJzOiB7XG4gICAgdGVtcGVyYXR1cmU6IG51bWJlclxuICAgIG1heFRva2VuczogbnVtYmVyXG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldEFJQ29uZmlnKCk6IEFJQ29uZmlnIHtcbiAgcmV0dXJuIHtcbiAgICBwcm92aWRlcnM6IHtcbiAgICAgIG9wZW5haTogcHJvY2Vzcy5lbnYuT1BFTkFJX0FQSV9LRVkgPyB7XG4gICAgICAgIGFwaUtleTogcHJvY2Vzcy5lbnYuT1BFTkFJX0FQSV9LRVksXG4gICAgICAgIGJhc2VVcmw6IHByb2Nlc3MuZW52Lk9QRU5BSV9CQVNFX1VSTCB8fCAnaHR0cHM6Ly9hcGkub3BlbmFpLmNvbS92MScsXG4gICAgICAgIG1vZGVsOiBwcm9jZXNzLmVudi5PUEVOQUlfTU9ERUwgfHwgJ2dwdC00LXR1cmJvLXByZXZpZXcnXG4gICAgICB9IDogdW5kZWZpbmVkLFxuICAgICAgYW50aHJvcGljOiBwcm9jZXNzLmVudi5BTlRIUk9QSUNfQVBJX0tFWSA/IHtcbiAgICAgICAgYXBpS2V5OiBwcm9jZXNzLmVudi5BTlRIUk9QSUNfQVBJX0tFWSxcbiAgICAgICAgYmFzZVVybDogcHJvY2Vzcy5lbnYuQU5USFJPUElDX0JBU0VfVVJMIHx8ICdodHRwczovL2FwaS5hbnRocm9waWMuY29tL3YxJyxcbiAgICAgICAgbW9kZWw6IHByb2Nlc3MuZW52LkFOVEhST1BJQ19NT0RFTCB8fCAnY2xhdWRlLTMtb3B1cy0yMDI0MDIyOSdcbiAgICAgIH0gOiB1bmRlZmluZWRcbiAgICB9LFxuICAgIGRlZmF1bHRQcm92aWRlcjogKHByb2Nlc3MuZW52LkRFRkFVTFRfQUlfUFJPVklERVIgYXMgJ29wZW5haScgfCAnYW50aHJvcGljJykgfHwgJ29wZW5haScsXG4gICAgcGFyYW1ldGVyczoge1xuICAgICAgdGVtcGVyYXR1cmU6IHBhcnNlRmxvYXQocHJvY2Vzcy5lbnYuQUlfVEVNUEVSQVRVUkUgfHwgJzAuNycpLFxuICAgICAgbWF4VG9rZW5zOiBwYXJzZUludChwcm9jZXNzLmVudi5BSV9NQVhfVE9LRU5TIHx8ICcxMDAwJywgMTApXG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUFJQ29uZmlnKGNvbmZpZzogQUlDb25maWcpOiB7IGlzVmFsaWQ6IGJvb2xlYW47IGVycm9yczogc3RyaW5nW10gfSB7XG4gIGNvbnN0IGVycm9yczogc3RyaW5nW10gPSBbXVxuXG4gIC8vIENoZWNrIGlmIGF0IGxlYXN0IG9uZSBwcm92aWRlciBpcyBjb25maWd1cmVkXG4gIGlmICghY29uZmlnLnByb3ZpZGVycy5vcGVuYWkgJiYgIWNvbmZpZy5wcm92aWRlcnMuYW50aHJvcGljKSB7XG4gICAgZXJyb3JzLnB1c2goJ05vIEFJIHByb3ZpZGVycyBjb25maWd1cmVkLiBQbGVhc2Ugc2V0IE9QRU5BSV9BUElfS0VZIG9yIEFOVEhST1BJQ19BUElfS0VZLicpXG4gIH1cbiAgLy8gQ2hlY2sgaWYgZGVmYXVsdCBwcm92aWRlciBpcyBjb25maWd1cmVkXG4gIGlmIChjb25maWcuZGVmYXVsdFByb3ZpZGVyID09PSAnb3BlbmFpJyAmJiAhY29uZmlnLnByb3ZpZGVycy5vcGVuYWkpIHtcbiAgICBlcnJvcnMucHVzaCgnRGVmYXVsdCBwcm92aWRlciBpcyBzZXQgdG8gT3BlbkFJIGJ1dCBPcGVuQUkgaXMgbm90IGNvbmZpZ3VyZWQuJylcbiAgfVxuICBcbiAgaWYgKGNvbmZpZy5kZWZhdWx0UHJvdmlkZXIgPT09ICdhbnRocm9waWMnICYmICFjb25maWcucHJvdmlkZXJzLmFudGhyb3BpYykge1xuICAgIGVycm9ycy5wdXNoKCdEZWZhdWx0IHByb3ZpZGVyIGlzIHNldCB0byBBbnRocm9waWMgYnV0IEFudGhyb3BpYyBpcyBub3QgY29uZmlndXJlZC4nKVxuICB9XG5cbiAgLy8gVmFsaWRhdGUgdGVtcGVyYXR1cmVcbiAgaWYgKGNvbmZpZy5wYXJhbWV0ZXJzLnRlbXBlcmF0dXJlIDwgMCB8fCBjb25maWcucGFyYW1ldGVycy50ZW1wZXJhdHVyZSA+IDIpIHtcbiAgICBlcnJvcnMucHVzaCgnVGVtcGVyYXR1cmUgbXVzdCBiZSBiZXR3ZWVuIDAgYW5kIDIuJylcbiAgfVxuXG4gIC8vIFZhbGlkYXRlIG1heCB0b2tlbnNcbiAgaWYgKGNvbmZpZy5wYXJhbWV0ZXJzLm1heFRva2VucyA8IDEgfHwgY29uZmlnLnBhcmFtZXRlcnMubWF4VG9rZW5zID4gMzIwMDApIHtcbiAgICBlcnJvcnMucHVzaCgnTWF4IHRva2VucyBtdXN0IGJlIGJldHdlZW4gMSBhbmQgMzIwMDAuJylcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgaXNWYWxpZDogZXJyb3JzLmxlbmd0aCA9PT0gMCxcbiAgICBlcnJvcnNcbiAgfVxufVxuXG4vLyBBdmFpbGFibGUgbW9kZWxzIGZvciBlYWNoIHByb3ZpZGVyXG5leHBvcnQgY29uc3QgQUlfTU9ERUxTID0ge1xuICBvcGVuYWk6IFtcbiAgICB7IGlkOiAnZ3B0LTQtdHVyYm8tcHJldmlldycsIG5hbWU6ICdHUFQtNCBUdXJibycsIG1heFRva2VuczogMTI4MDAwIH0sXG4gICAgeyBpZDogJ2dwdC00JywgbmFtZTogJ0dQVC00JywgbWF4VG9rZW5zOiA4MTkyIH0sXG4gICAgeyBpZDogJ2dwdC0zLjUtdHVyYm8nLCBuYW1lOiAnR1BULTMuNSBUdXJibycsIG1heFRva2VuczogMTYzODUgfVxuICBdLFxuICBhbnRocm9waWM6IFtcbiAgICB7IGlkOiAnY2xhdWRlLTMtb3B1cy0yMDI0MDIyOScsIG5hbWU6ICdDbGF1ZGUgMyBPcHVzJywgbWF4VG9rZW5zOiAyMDAwMDAgfSxcbiAgICB7IGlkOiAnY2xhdWRlLTMtc29ubmV0LTIwMjQwMjI5JywgbmFtZTogJ0NsYXVkZSAzIFNvbm5ldCcsIG1heFRva2VuczogMjAwMDAwIH0sXG4gICAgeyBpZDogJ2NsYXVkZS0zLWhhaWt1LTIwMjQwMzA3JywgbmFtZTogJ0NsYXVkZSAzIEhhaWt1JywgbWF4VG9rZW5zOiAyMDAwMDAgfVxuICBdXG59Il0sIm5hbWVzIjpbImdldEFJQ29uZmlnIiwicHJvdmlkZXJzIiwib3BlbmFpIiwicHJvY2VzcyIsImVudiIsIk9QRU5BSV9BUElfS0VZIiwiYXBpS2V5IiwiYmFzZVVybCIsIk9QRU5BSV9CQVNFX1VSTCIsIm1vZGVsIiwiT1BFTkFJX01PREVMIiwidW5kZWZpbmVkIiwiYW50aHJvcGljIiwiQU5USFJPUElDX0FQSV9LRVkiLCJBTlRIUk9QSUNfQkFTRV9VUkwiLCJBTlRIUk9QSUNfTU9ERUwiLCJkZWZhdWx0UHJvdmlkZXIiLCJERUZBVUxUX0FJX1BST1ZJREVSIiwicGFyYW1ldGVycyIsInRlbXBlcmF0dXJlIiwicGFyc2VGbG9hdCIsIkFJX1RFTVBFUkFUVVJFIiwibWF4VG9rZW5zIiwicGFyc2VJbnQiLCJBSV9NQVhfVE9LRU5TIiwidmFsaWRhdGVBSUNvbmZpZyIsImNvbmZpZyIsImVycm9ycyIsInB1c2giLCJpc1ZhbGlkIiwibGVuZ3RoIiwiQUlfTU9ERUxTIiwiaWQiLCJuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/config/ai-config.ts\n");

/***/ }),

/***/ "(ssr)/./lib/env.ts":
/*!********************!*\
  !*** ./lib/env.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   isDevelopmentWithoutConfig: () => (/* binding */ isDevelopmentWithoutConfig),\n/* harmony export */   validateSupabaseConfig: () => (/* binding */ validateSupabaseConfig)\n/* harmony export */ });\n// Environment variable validation and type safety\nconst getEnvVar = (key, defaultValue)=>{\n    const value = process.env[key] || defaultValue;\n    if (!value) {\n        console.warn(`Missing environment variable: ${key}`);\n        return '';\n    }\n    return value;\n};\n// Validate Supabase configuration\nconst validateSupabaseConfig = ()=>{\n    try {\n        // Get the values directly from process.env to avoid throwing errors\n        const url = \"https://pvfymxuzhzlibbnaedgt.supabase.co\" || 0;\n        const anonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB2ZnlteHV6aHpsaWJibmFlZGd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNDcwMjcsImV4cCI6MjA2MTkyMzAyN30.o4FlWTRTJSGUX_pjejobTeaJF3Ay5UVN5hVhVtJ0Y_M\" || 0;\n        // Basic validation - just check that we have values\n        if (!url || !anonKey) {\n            return {\n                url,\n                anonKey,\n                isValid: false,\n                error: 'Missing Supabase configuration'\n            };\n        }\n        // Simple format checks\n        if (!url.includes('supabase.co')) {\n            return {\n                url,\n                anonKey,\n                isValid: false,\n                error: 'Invalid Supabase URL format'\n            };\n        }\n        // Anon key should be reasonably long\n        if (anonKey.length < 20) {\n            return {\n                url,\n                anonKey,\n                isValid: false,\n                error: 'Invalid Supabase anon key format'\n            };\n        }\n        return {\n            url,\n            anonKey,\n            isValid: true\n        };\n    } catch (error) {\n        // Ensure error is properly typed for TypeScript\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error validating Supabase config';\n        return {\n            url: '',\n            anonKey: '',\n            isValid: false,\n            error: errorMessage\n        };\n    }\n};\n// Environment configuration with defaults\nconst config = {\n    supabase: {\n        url: \"https://pvfymxuzhzlibbnaedgt.supabase.co\" || 0,\n        anonKey: \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB2ZnlteHV6aHpsaWJibmFlZGd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNDcwMjcsImV4cCI6MjA2MTkyMzAyN30.o4FlWTRTJSGUX_pjejobTeaJF3Ay5UVN5hVhVtJ0Y_M\" || 0,\n        serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || ''\n    },\n    ai: {\n        openaiKey: process.env.OPENAI_API_KEY || '',\n        anthropicKey: process.env.ANTHROPIC_API_KEY || ''\n    },\n    app: {\n        url: \"http://localhost:3000\" || 0,\n        name: \"ITSync\" || 0,\n        env: \"development\" || 0\n    }\n};\n// Check if running in development without proper config\nconst isDevelopmentWithoutConfig = ()=>{\n    return config.app.env === 'development' && (!config.supabase.url || !config.supabase.url.includes('supabase.co') || !config.supabase.anonKey || config.supabase.anonKey.length < 20);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/env.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/config.ts":
/*!****************************!*\
  !*** ./lib/i18n/config.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   formatJapaneseDate: () => (/* binding */ formatJapaneseDate),\n/* harmony export */   isJapaneseBusinessDay: () => (/* binding */ isJapaneseBusinessDay),\n/* harmony export */   japaneseEras: () => (/* binding */ japaneseEras),\n/* harmony export */   loadDictionary: () => (/* binding */ loadDictionary),\n/* harmony export */   localeNames: () => (/* binding */ localeNames),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   toJapaneseEra: () => (/* binding */ toJapaneseEra)\n/* harmony export */ });\n// Japanese Localization Configuration\nconst locales = [\n    'ja',\n    'en'\n];\nconst defaultLocale = 'ja';\nconst localeNames = {\n    ja: '日本語',\n    en: 'English'\n};\n// Japanese date formatting with 和暦 (Japanese Era) support\nconst japaneseEras = [\n    {\n        name: '令和',\n        romanji: 'Reiwa',\n        startDate: new Date('2019-05-01')\n    },\n    {\n        name: '平成',\n        romanji: 'Heisei',\n        startDate: new Date('1989-01-08')\n    },\n    {\n        name: '昭和',\n        romanji: 'Showa',\n        startDate: new Date('1926-12-25')\n    }\n];\nfunction toJapaneseEra(date) {\n    for (const era of japaneseEras){\n        if (date >= era.startDate) {\n            const year = date.getFullYear() - era.startDate.getFullYear() + 1;\n            return `${era.name}${year}年`;\n        }\n    }\n    return date.getFullYear() + '年';\n}\nfunction formatJapaneseDate(date, includeEra = true) {\n    const year = includeEra ? toJapaneseEra(date) : `${date.getFullYear()}年`;\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n    return `${year}${month}月${day}日`;\n}\n// Japanese business day calculation\nfunction isJapaneseBusinessDay(date) {\n    const day = date.getDay();\n    // Weekend check\n    if (day === 0 || day === 6) return false;\n    // Japanese national holidays (simplified - add more as needed)\n    const holidays = [\n        '01-01',\n        '01-02',\n        '01-03',\n        '02-11',\n        '02-23',\n        '03-20',\n        '04-29',\n        '05-03',\n        '05-04',\n        '05-05',\n        '07-15',\n        '08-11',\n        '09-15',\n        '09-23',\n        '10-10',\n        '11-03',\n        '11-23'\n    ];\n    const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    return !holidays.includes(dateStr);\n}\n// Load locale dictionary\nasync function loadDictionary(locale) {\n    const dictionaries = {\n        ja: ()=>__webpack_require__.e(/*! import() */ \"_ssr_lib_i18n_dictionaries_ja_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ./dictionaries/ja.json */ \"(ssr)/./lib/i18n/dictionaries/ja.json\", 19)).then((module)=>module.default),\n        en: ()=>__webpack_require__.e(/*! import() */ \"_ssr_lib_i18n_dictionaries_en_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ./dictionaries/en.json */ \"(ssr)/./lib/i18n/dictionaries/en.json\", 19)).then((module)=>module.default)\n    };\n    return dictionaries[locale]();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/config.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/context.tsx":
/*!******************************!*\
  !*** ./lib/i18n/context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider),\n/* harmony export */   useI18n: () => (/* binding */ useI18n)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config */ \"(ssr)/./lib/i18n/config.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./translations */ \"(ssr)/./lib/i18n/translations.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider,useI18n auto */ \n\n\n\nconst I18nContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction I18nProvider({ children }) {\n    const [locale, setLocaleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_config__WEBPACK_IMPORTED_MODULE_2__.defaultLocale);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"I18nProvider.useEffect\": ()=>{\n            // Load saved locale from localStorage\n            const savedLocale = localStorage.getItem('locale');\n            if (savedLocale && (savedLocale === 'ja' || savedLocale === 'en')) {\n                setLocaleState(savedLocale);\n            }\n        }\n    }[\"I18nProvider.useEffect\"], []);\n    const setLocale = (newLocale)=>{\n        setLocaleState(newLocale);\n        localStorage.setItem('locale', newLocale);\n    };\n    const t = (key, params)=>{\n        const keys = key.split('.');\n        let value = _translations__WEBPACK_IMPORTED_MODULE_3__.translations[locale];\n        for (const k of keys){\n            value = value?.[k];\n        }\n        if (typeof value !== 'string') {\n            console.warn(`Translation key not found: ${key}`);\n            return key;\n        }\n        if (params) {\n            return value.replace(/\\{(\\w+)\\}/g, (match, paramKey)=>{\n                return params[paramKey] || match;\n            });\n        }\n        return value;\n    };\n    const formatDate = (date, format)=>{\n        const options = {};\n        switch(format){\n            case 'time':\n                options.hour = '2-digit';\n                options.minute = '2-digit';\n                break;\n            case 'datetime':\n                options.year = 'numeric';\n                options.month = '2-digit';\n                options.day = '2-digit';\n                options.hour = '2-digit';\n                options.minute = '2-digit';\n                break;\n            case 'wareki':\n                if (locale === 'ja') {\n                    return new Intl.DateTimeFormat('ja-JP-u-ca-japanese', {\n                        era: 'long',\n                        year: 'numeric',\n                        month: 'long',\n                        day: 'numeric'\n                    }).format(date);\n                }\n            // Fall through to default for non-Japanese\n            default:\n                options.year = 'numeric';\n                options.month = '2-digit';\n                options.day = '2-digit';\n        }\n        return new Intl.DateTimeFormat(locale === 'ja' ? 'ja-JP' : 'en-US', options).format(date);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(I18nContext.Provider, {\n        value: {\n            locale,\n            setLocale,\n            t,\n            formatDate\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\i18n\\\\context.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\nfunction useI18n() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(I18nContext);\n    if (!context) {\n        throw new Error('useI18n must be used within an I18nProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/translations.ts":
/*!**********************************!*\
  !*** ./lib/i18n/translations.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst translations = {\n    ja: {\n        common: {\n            welcome: 'ようこそ',\n            logout: 'ログアウト',\n            login: 'ログイン',\n            save: '保存',\n            cancel: 'キャンセル',\n            delete: '削除',\n            edit: '編集',\n            create: '作成',\n            search: '検索',\n            loading: '読み込み中...',\n            error: 'エラー',\n            success: '成功',\n            confirm: '確認',\n            back: '戻る',\n            next: '次へ',\n            submit: '送信',\n            required: '必須',\n            optional: '任意',\n            selectAll: 'すべて選択',\n            clearAll: 'すべてクリア',\n            noData: 'データがありません',\n            actions: 'アクション'\n        },\n        auth: {\n            signIn: 'サインイン',\n            signOut: 'サインアウト',\n            email: 'メールアドレス',\n            password: 'パスワード',\n            forgotPassword: 'パスワードをお忘れですか？',\n            rememberMe: 'ログイン状態を保持',\n            signInError: 'ログインに失敗しました',\n            unauthorized: '権限がありません'\n        },\n        dashboard: {\n            title: 'ダッシュボード',\n            overview: '概要',\n            recentRequests: '最近のリクエスト',\n            pendingApprovals: '承認待ち',\n            statistics: '統計',\n            welcome: 'おかえりなさい、{name}さん'\n        },\n        navigation: {\n            home: 'ホーム',\n            dashboard: 'ダッシュボード',\n            requests: 'リクエスト',\n            itHelpdesk: 'ITヘルプデスク',\n            groupMail: 'グループメール',\n            mailbox: 'メールボックス',\n            sharepoint: 'SharePoint',\n            pcAdmin: 'PC管理者権限',\n            passwordReset: 'パスワードリセット',\n            hr: '人事',\n            onboarding: 'オンボーディング',\n            offboarding: 'オフボーディング',\n            knowledgeBase: 'ナレッジベース',\n            settings: '設定',\n            profile: 'プロフィール',\n            help: 'ヘルプ'\n        },\n        roles: {\n            globalAdmin: 'グローバル管理者',\n            systemAdmin: 'システム管理者',\n            departmentAdmin: '部門管理者',\n            itSupport: 'ITサポート',\n            hrStaff: '人事スタッフ',\n            regularUser: '一般ユーザー'\n        },\n        departments: {\n            corporatePlanning: '経営企画部',\n            humanResources: '人事部',\n            financeAccounting: '財務・経理部',\n            legalCompliance: '法務・コンプライアンス部',\n            fleetOperations: 'フリートオペレーション部',\n            technicalManagement: '工務部',\n            itSystems: 'ITシステム部',\n            commercialStrategies: '営業戦略部',\n            guestExperience: 'ゲストエクスピリエンス部'\n        },\n        forms: {\n            selectUser: 'ユーザーを選択',\n            selectDepartment: '部門を選択',\n            selectService: 'サービスを選択',\n            reason: '理由',\n            description: '説明',\n            priority: '優先度',\n            high: '高',\n            medium: '中',\n            low: '低',\n            attachments: '添付ファイル',\n            additionalNotes: '追加メモ',\n            confirmationTitle: '確認',\n            confirmationMessage: '以下の内容でリクエストを送信しますか？',\n            affectedUsers: '対象ユーザー',\n            requestDetails: 'リクエスト詳細',\n            submittedSuccessfully: 'リクエストが正常に送信されました'\n        },\n        services: {\n            groupMail: {\n                title: 'グループメール管理',\n                description: 'グループメールアドレスへのメンバー追加・削除',\n                add: 'メンバー追加',\n                remove: 'メンバー削除',\n                addresses: 'グループメールアドレス'\n            },\n            mailbox: {\n                title: 'メールボックス管理',\n                description: 'メールボックスアドレスの管理',\n                add: 'アドレス追加',\n                remove: 'アドレス削除',\n                addresses: 'メールボックスアドレス'\n            },\n            sharepoint: {\n                title: 'SharePointライブラリ',\n                description: 'SharePointライブラリへのアクセス管理',\n                grant: 'アクセス許可',\n                revoke: 'アクセス取消',\n                libraries: 'ライブラリ',\n                accessLevel: 'アクセスレベル',\n                read: '読み取り',\n                contribute: '投稿',\n                full: 'フルコントロール'\n            },\n            pcAdmin: {\n                title: 'PC管理者権限',\n                description: 'PC管理者権限の付与・取消',\n                grant: '権限付与',\n                revoke: '権限取消',\n                pcId: 'PC ID',\n                softwareInstall: 'ソフトウェアインストール',\n                softwareName: 'ソフトウェア名'\n            },\n            passwordReset: {\n                title: 'パスワードリセット',\n                description: 'パスワードのリセット要求',\n                types: {\n                    m365: 'M365 Office',\n                    mfa: '多要素認証',\n                    windows: 'Windows'\n                },\n                temporaryPassword: '仮パスワード',\n                resetInstructions: 'ユーザーは初回ログイン時にパスワードを変更する必要があります'\n            }\n        },\n        status: {\n            pending: '保留中',\n            processing: '処理中',\n            completed: '完了',\n            failed: '失敗',\n            approved: '承認済み',\n            rejected: '却下',\n            draft: '下書き',\n            submitted: '送信済み'\n        },\n        date: {\n            today: '今日',\n            yesterday: '昨日',\n            thisWeek: '今週',\n            lastWeek: '先週',\n            thisMonth: '今月',\n            lastMonth: '先月',\n            dateFormat: 'YYYY年MM月DD日',\n            timeFormat: 'HH:mm',\n            dateTimeFormat: 'YYYY年MM月DD日 HH:mm',\n            wareki: '和暦'\n        },\n        errors: {\n            generic: 'エラーが発生しました',\n            notFound: 'ページが見つかりません',\n            unauthorized: 'アクセス権限がありません',\n            validation: {\n                required: 'このフィールドは必須です',\n                email: '有効なメールアドレスを入力してください',\n                minLength: '最低{min}文字必要です',\n                maxLength: '最大{max}文字まで入力できます'\n            }\n        },\n        notifications: {\n            newRequest: '新しいリクエストがあります',\n            requestApproved: 'リクエストが承認されました',\n            requestRejected: 'リクエストが却下されました',\n            requestCompleted: 'リクエストが完了しました',\n            passwordReset: 'パスワードがリセットされました'\n        }\n    },\n    en: {\n        common: {\n            welcome: 'Welcome',\n            logout: 'Logout',\n            login: 'Login',\n            save: 'Save',\n            cancel: 'Cancel',\n            delete: 'Delete',\n            edit: 'Edit',\n            create: 'Create',\n            search: 'Search',\n            loading: 'Loading...',\n            error: 'Error',\n            success: 'Success',\n            confirm: 'Confirm',\n            back: 'Back',\n            next: 'Next',\n            submit: 'Submit',\n            required: 'Required',\n            optional: 'Optional',\n            selectAll: 'Select All',\n            clearAll: 'Clear All',\n            noData: 'No data available',\n            actions: 'Actions'\n        },\n        auth: {\n            signIn: 'Sign In',\n            signOut: 'Sign Out',\n            email: 'Email',\n            password: 'Password',\n            forgotPassword: 'Forgot Password?',\n            rememberMe: 'Remember Me',\n            signInError: 'Failed to sign in',\n            unauthorized: 'Unauthorized'\n        },\n        dashboard: {\n            title: 'Dashboard',\n            overview: 'Overview',\n            recentRequests: 'Recent Requests',\n            pendingApprovals: 'Pending Approvals',\n            statistics: 'Statistics',\n            welcome: 'Welcome back, {name}'\n        },\n        navigation: {\n            home: 'Home',\n            dashboard: 'Dashboard',\n            requests: 'Requests',\n            itHelpdesk: 'IT Helpdesk',\n            groupMail: 'Group Mail',\n            mailbox: 'Mailbox',\n            sharepoint: 'SharePoint',\n            pcAdmin: 'PC Admin Rights',\n            passwordReset: 'Password Reset',\n            hr: 'Human Resources',\n            onboarding: 'Onboarding',\n            offboarding: 'Offboarding',\n            knowledgeBase: 'Knowledge Base',\n            settings: 'Settings',\n            profile: 'Profile',\n            help: 'Help'\n        },\n        roles: {\n            globalAdmin: 'Global Administrator',\n            systemAdmin: 'System Administrator',\n            departmentAdmin: 'Department Administrator',\n            itSupport: 'IT Support',\n            hrStaff: 'HR Staff',\n            regularUser: 'Regular User'\n        },\n        departments: {\n            corporatePlanning: 'Corporate Planning Division',\n            humanResources: 'Human Resources Division',\n            financeAccounting: 'Finance and Accounting Division',\n            legalCompliance: 'Legal, Ethics and Compliance Division',\n            fleetOperations: 'Fleet Operations Division',\n            technicalManagement: 'Technical Management Division',\n            itSystems: 'IT Systems Division',\n            commercialStrategies: 'Commercial Strategies Division',\n            guestExperience: 'Guest Experience Division'\n        },\n        forms: {\n            selectUser: 'Select User',\n            selectDepartment: 'Select Department',\n            selectService: 'Select Service',\n            reason: 'Reason',\n            description: 'Description',\n            priority: 'Priority',\n            high: 'High',\n            medium: 'Medium',\n            low: 'Low',\n            attachments: 'Attachments',\n            additionalNotes: 'Additional Notes',\n            confirmationTitle: 'Confirmation',\n            confirmationMessage: 'Do you want to submit the request with the following details?',\n            affectedUsers: 'Affected Users',\n            requestDetails: 'Request Details',\n            submittedSuccessfully: 'Request submitted successfully'\n        },\n        services: {\n            groupMail: {\n                title: 'Group Mail Management',\n                description: 'Add or remove members from group mail addresses',\n                add: 'Add Member',\n                remove: 'Remove Member',\n                addresses: 'Group Mail Addresses'\n            },\n            mailbox: {\n                title: 'Mailbox Management',\n                description: 'Manage mailbox addresses',\n                add: 'Add Address',\n                remove: 'Remove Address',\n                addresses: 'Mailbox Addresses'\n            },\n            sharepoint: {\n                title: 'SharePoint Library',\n                description: 'Manage access to SharePoint libraries',\n                grant: 'Grant Access',\n                revoke: 'Revoke Access',\n                libraries: 'Libraries',\n                accessLevel: 'Access Level',\n                read: 'Read',\n                contribute: 'Contribute',\n                full: 'Full Control'\n            },\n            pcAdmin: {\n                title: 'PC Administrative Rights',\n                description: 'Grant or revoke PC administrative privileges',\n                grant: 'Grant Rights',\n                revoke: 'Revoke Rights',\n                pcId: 'PC ID',\n                softwareInstall: 'Software Installation',\n                softwareName: 'Software Name'\n            },\n            passwordReset: {\n                title: 'Password Reset',\n                description: 'Request password reset',\n                types: {\n                    m365: 'M365 Office',\n                    mfa: 'Multi Factor Authenticator',\n                    windows: 'Windows'\n                },\n                temporaryPassword: 'Temporary Password',\n                resetInstructions: 'User must change password on first login'\n            }\n        },\n        status: {\n            pending: 'Pending',\n            processing: 'Processing',\n            completed: 'Completed',\n            failed: 'Failed',\n            approved: 'Approved',\n            rejected: 'Rejected',\n            draft: 'Draft',\n            submitted: 'Submitted'\n        },\n        date: {\n            today: 'Today',\n            yesterday: 'Yesterday',\n            thisWeek: 'This Week',\n            lastWeek: 'Last Week',\n            thisMonth: 'This Month',\n            lastMonth: 'Last Month',\n            dateFormat: 'MM/DD/YYYY',\n            timeFormat: 'HH:mm',\n            dateTimeFormat: 'MM/DD/YYYY HH:mm',\n            wareki: 'Japanese Era'\n        },\n        errors: {\n            generic: 'An error occurred',\n            notFound: 'Page not found',\n            unauthorized: 'You do not have permission to access this',\n            validation: {\n                required: 'This field is required',\n                email: 'Please enter a valid email address',\n                minLength: 'Must be at least {min} characters',\n                maxLength: 'Must be at most {max} characters'\n            }\n        },\n        notifications: {\n            newRequest: 'New request received',\n            requestApproved: 'Request has been approved',\n            requestRejected: 'Request has been rejected',\n            requestCompleted: 'Request has been completed',\n            passwordReset: 'Password has been reset'\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/translations.ts\n");

/***/ }),

/***/ "(ssr)/./lib/services/ai-api-service.ts":
/*!****************************************!*\
  !*** ./lib/services/ai-api-service.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIAPIService: () => (/* binding */ AIAPIService),\n/* harmony export */   aiAPIService: () => (/* binding */ aiAPIService)\n/* harmony export */ });\n// lib/services/ai-api-service.ts\n// Unified service for integrating with external AI APIs (OpenAI and Anthropic)\nclass AIAPIService {\n    constructor(){\n        this.providers = new Map();\n        this.defaultProvider = 'openai';\n        // Initialize providers from environment variables\n        this.initializeProviders();\n    }\n    initializeProviders() {\n        // OpenAI configuration\n        const openaiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || process.env.OPENAI_API_KEY;\n        if (openaiKey) {\n            this.providers.set('openai', {\n                name: 'openai',\n                apiKey: openaiKey,\n                baseUrl: 'https://api.openai.com/v1'\n            });\n        }\n        // Anthropic configuration\n        const anthropicKey = process.env.NEXT_PUBLIC_ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY;\n        if (anthropicKey) {\n            this.providers.set('anthropic', {\n                name: 'anthropic',\n                apiKey: anthropicKey,\n                baseUrl: 'https://api.anthropic.com/v1'\n            });\n        }\n    }\n    setDefaultProvider(provider) {\n        if (this.providers.has(provider)) {\n            this.defaultProvider = provider;\n        } else {\n            throw new Error(`Provider ${provider} is not configured`);\n        }\n    }\n    async completion(request, provider) {\n        const targetProvider = provider || this.defaultProvider;\n        const providerConfig = this.providers.get(targetProvider);\n        if (!providerConfig) {\n            throw new Error(`Provider ${targetProvider} is not configured`);\n        }\n        switch(targetProvider){\n            case 'openai':\n                return this.openAICompletion(request, providerConfig);\n            case 'anthropic':\n                return this.anthropicCompletion(request, providerConfig);\n            default:\n                throw new Error(`Unsupported provider: ${targetProvider}`);\n        }\n    }\n    async openAICompletion(request, provider) {\n        const model = request.model || 'gpt-4-turbo-preview';\n        const body = {\n            model,\n            messages: [\n                ...request.systemPrompt ? [\n                    {\n                        role: 'system',\n                        content: request.systemPrompt\n                    }\n                ] : [],\n                {\n                    role: 'user',\n                    content: request.prompt\n                }\n            ],\n            temperature: request.temperature ?? 0.7,\n            max_tokens: request.maxTokens ?? 1000\n        };\n        if (request.responseFormat === 'json') {\n            body.response_format = {\n                type: 'json_object'\n            };\n        }\n        const response = await fetch(`${provider.baseUrl}/chat/completions`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${provider.apiKey}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            throw new Error(`OpenAI API error: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return {\n            text: data.choices[0].message.content,\n            usage: data.usage ? {\n                promptTokens: data.usage.prompt_tokens,\n                completionTokens: data.usage.completion_tokens,\n                totalTokens: data.usage.total_tokens\n            } : undefined,\n            provider: 'openai',\n            model\n        };\n    }\n    async anthropicCompletion(request, provider) {\n        const model = request.model || 'claude-3-opus-20240229';\n        const body = {\n            model,\n            messages: [\n                {\n                    role: 'user',\n                    content: request.prompt\n                }\n            ],\n            system: request.systemPrompt,\n            temperature: request.temperature ?? 0.7,\n            max_tokens: request.maxTokens ?? 1000\n        };\n        const response = await fetch(`${provider.baseUrl}/messages`, {\n            method: 'POST',\n            headers: {\n                'x-api-key': provider.apiKey,\n                'anthropic-version': '2023-06-01',\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            throw new Error(`Anthropic API error: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return {\n            text: data.content[0].text,\n            usage: data.usage ? {\n                promptTokens: data.usage.input_tokens,\n                completionTokens: data.usage.output_tokens,\n                totalTokens: data.usage.input_tokens + data.usage.output_tokens\n            } : undefined,\n            provider: 'anthropic',\n            model\n        };\n    }\n    async embedding(request, provider = 'openai') {\n        const providerConfig = this.providers.get(provider);\n        if (!providerConfig) {\n            throw new Error(`Provider ${provider} is not configured`);\n        }\n        if (provider !== 'openai') {\n            throw new Error('Embeddings are currently only supported with OpenAI');\n        }\n        const model = request.model || 'text-embedding-3-small';\n        const input = Array.isArray(request.text) ? request.text : [\n            request.text\n        ];\n        const response = await fetch(`${providerConfig.baseUrl}/embeddings`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${providerConfig.apiKey}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model,\n                input\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`OpenAI Embeddings API error: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return {\n            embeddings: data.data.map((item)=>item.embedding),\n            usage: data.usage ? {\n                totalTokens: data.usage.total_tokens\n            } : undefined\n        };\n    }\n    // Specialized methods for IT Helpdesk use cases\n    async validateFormField(fieldType, value, context) {\n        const systemPrompt = `You are validating form fields for a Japanese IT helpdesk system.\nField type: ${fieldType}\nContext: ${JSON.stringify(context || {})}\n\nValidate the input and provide:\n1. Whether it's valid (true/false)\n2. List of issues if invalid\n3. Suggestions for correction\n\nReturn as JSON with keys: isValid, issues, suggestions`;\n        const response = await this.completion({\n            systemPrompt,\n            prompt: `Validate this value: \"${value}\"`,\n            temperature: 0.3,\n            responseFormat: 'json'\n        });\n        return JSON.parse(response.text);\n    }\n    async generateFormSuggestions(fieldType, partialValue, context) {\n        const systemPrompt = `Generate autocomplete suggestions for a Japanese IT helpdesk form field.\nField type: ${fieldType}\nContext: ${JSON.stringify(context || {})}\n\nProvide 3-5 relevant suggestions based on the partial input.\nReturn as JSON array of strings.`;\n        const response = await this.completion({\n            systemPrompt,\n            prompt: `Partial value: \"${partialValue}\"`,\n            temperature: 0.5,\n            responseFormat: 'json'\n        });\n        return JSON.parse(response.text);\n    }\n    async classifyRequest(requestText, availableCategories) {\n        const systemPrompt = `Classify IT helpdesk requests into categories.\nAvailable categories: ${availableCategories.join(', ')}\n\nReturn JSON with: category (exact match from list), confidence (0-1)`;\n        const response = await this.completion({\n            systemPrompt,\n            prompt: requestText,\n            temperature: 0.3,\n            responseFormat: 'json'\n        });\n        return JSON.parse(response.text);\n    }\n    async translateText(text, from, to) {\n        if (from === to) return text;\n        const response = await this.completion({\n            systemPrompt: `Translate the following text from ${from} to ${to}. \nMaintain technical terminology appropriate for IT helpdesk context.\nReturn only the translated text.`,\n            prompt: text,\n            temperature: 0.3\n        });\n        return response.text;\n    }\n    // Cost estimation utilities\n    estimateCost(usage, model) {\n        const pricing = {\n            'gpt-4-turbo-preview': {\n                prompt: 0.01,\n                completion: 0.03\n            },\n            'gpt-4': {\n                prompt: 0.03,\n                completion: 0.06\n            },\n            'gpt-3.5-turbo': {\n                prompt: 0.0005,\n                completion: 0.0015\n            },\n            'claude-3-opus-20240229': {\n                prompt: 0.015,\n                completion: 0.075\n            },\n            'claude-3-sonnet-20240229': {\n                prompt: 0.003,\n                completion: 0.015\n            }\n        };\n        const modelPricing = pricing[model] || {\n            prompt: 0.01,\n            completion: 0.03\n        };\n        return usage.promptTokens / 1000 * modelPricing.prompt + usage.completionTokens / 1000 * modelPricing.completion;\n    }\n    isConfigured(provider) {\n        if (provider) {\n            return this.providers.has(provider);\n        }\n        return this.providers.size > 0;\n    }\n    getAvailableProviders() {\n        return Array.from(this.providers.keys());\n    }\n}\n// Export singleton instance\nconst aiAPIService = new AIAPIService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/services/ai-api-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByaW5jXFxpdHN5bmMtcHJvamVjdFxcQ2xhdWRlXFxEZW1vMVxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateJapaneseName: () => (/* binding */ validateJapaneseName),\n/* harmony export */   validateLoginId: () => (/* binding */ validateLoginId),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validatePcId: () => (/* binding */ validatePcId),\n/* harmony export */   validateStaffId: () => (/* binding */ validateStaffId)\n/* harmony export */ });\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Japanese name validation\nconst validateJapaneseName = (name)=>{\n    // Check if string contains Japanese characters (Hiragana, Katakana, Kanji)\n    const japaneseRegex = /[\\u3040-\\u309F\\u30A0-\\u30FF\\u4E00-\\u9FAF]/;\n    return japaneseRegex.test(name);\n};\n// Staff ID validation (format: R000123)\nconst validateStaffId = (staffId)=>{\n    const staffIdRegex = /^[A-Z]\\d{6}$/;\n    return staffIdRegex.test(staffId);\n};\n// PC ID validation \nconst validatePcId = (pcId)=>{\n    const pcIdRegex = /^[A-Z]\\d{7}$/;\n    return pcIdRegex.test(pcId);\n};\n// Login ID validation (format: <EMAIL>)\nconst validateLoginId = (loginId)=>{\n    const loginIdRegex = /^[A-Z]\\d{7}@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    return loginIdRegex.test(loginId);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./lib/validation.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1636f30637e3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJpbmNcXGl0c3luYy1wcm9qZWN0XFxDbGF1ZGVcXERlbW8xXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTYzNmYzMDYzN2UzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Noto_Sans_JP\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"variable\":\"--font-noto-sans-jp\"}],\"variableName\":\"notoSansJP\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_JP\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-noto-sans-jp\\\"}],\\\"variableName\\\":\\\"notoSansJP\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./lib/auth-context.tsx\");\n/* harmony import */ var _components_providers_ai_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ai-provider */ \"(rsc)/./components/providers/ai-provider.tsx\");\n/* harmony import */ var _lib_i18n_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n/context */ \"(rsc)/./lib/i18n/context.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'ITSync - Enterprise IT Helpdesk',\n    description: 'AI-powered IT Helpdesk & Support Platform'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ja\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} ${(next_font_google_target_css_path_app_layout_tsx_import_Noto_Sans_JP_arguments_subsets_latin_weight_400_500_700_variable_font_noto_sans_jp_variableName_notoSansJP___WEBPACK_IMPORTED_MODULE_6___default().variable)} font-sans`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ai_provider__WEBPACK_IMPORTED_MODULE_2__.AIProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_i18n_context__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_login_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/login-form */ \"(rsc)/./components/auth/login-form.tsx\");\n\n\nfunction LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_login_form__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9naW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0Q7QUFFckMsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELG1FQUFTQTs7Ozs7QUFDbkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJpbmNcXGl0c3luYy1wcm9qZWN0XFxDbGF1ZGVcXERlbW8xXFxhcHBcXGxvZ2luXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTG9naW5Gb3JtIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL2xvZ2luLWZvcm0nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvZ2luUGFnZSgpIHtcbiAgcmV0dXJuIDxMb2dpbkZvcm0gLz5cbn1cbiJdLCJuYW1lcyI6WyJMb2dpbkZvcm0iLCJMb2dpblBhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth/login-form.tsx":
/*!****************************************!*\
  !*** ./components/auth/login-form.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\login-form.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/providers/ai-provider.tsx":
/*!**********************************************!*\
  !*** ./components/providers/ai-provider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIProvider: () => (/* binding */ AIProvider),
/* harmony export */   useAI: () => (/* binding */ useAI),
/* harmony export */   useAICompletion: () => (/* binding */ useAICompletion),
/* harmony export */   useAIFormValidation: () => (/* binding */ useAIFormValidation),
/* harmony export */   useAITranslation: () => (/* binding */ useAITranslation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AIProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AIProvider() from the server but AIProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx",
"AIProvider",
);const useAI = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAI() from the server but useAI is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx",
"useAI",
);const useAICompletion = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAICompletion() from the server but useAICompletion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx",
"useAICompletion",
);const useAIFormValidation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAIFormValidation() from the server but useAIFormValidation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx",
"useAIFormValidation",
);const useAITranslation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAITranslation() from the server but useAITranslation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx",
"useAITranslation",
);

/***/ }),

/***/ "(rsc)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./lib/i18n/context.tsx":
/*!******************************!*\
  !*** ./lib/i18n/context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider),
/* harmony export */   useI18n: () => (/* binding */ useI18n)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const I18nProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call I18nProvider() from the server but I18nProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\context.tsx",
"I18nProvider",
);const useI18n = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\context.tsx",
"useI18n",
);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/lucide-react","vendor-chunks/whatwg-url","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();