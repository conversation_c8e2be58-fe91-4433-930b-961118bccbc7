(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46624:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(70260),a=r(28203),n=r(25155),i=r.n(n),o=r(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43642)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\login\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3996:(e,s,r)=>{Promise.resolve().then(r.bind(r,95467))},90428:(e,s,r)=>{Promise.resolve().then(r.bind(r,23459))},23459:(e,s,r)=>{"use strict";r.d(s,{default:()=>f});var t=r(45512),a=r(58009),n=r(79334),i=r(43433);let o=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e);var l=r(87021),d=r(25409),c=r(47699),p=r(97643),u=r(75339),m=r(69208),x=r(21956),h=r(86235);function f(){let[e,s]=(0,a.useState)(""),[r,f]=(0,a.useState)(""),[v,g]=(0,a.useState)(!1),[j,b]=(0,a.useState)(!1),[y,C]=(0,a.useState)(""),{signIn:P}=(0,i.A)(),w=(0,n.useRouter)(),N=async s=>{if(s.preventDefault(),C(""),b(!0),!o(e)){C("有効なメールアドレスを入力してください / Please enter a valid email address"),b(!1);return}if(r.length<8){C("パスワードは8文字以上で入力してください / Password must be at least 8 characters"),b(!1);return}try{let{data:s,error:t}=await P(e,r);t?C(t.message):s?.user&&w.push("/dashboard")}catch(e){C("ログインに失敗しました / Login failed")}finally{b(!1)}};return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)(p.Zp,{className:"w-full max-w-md",children:[(0,t.jsxs)(p.aR,{className:"space-y-1",children:[(0,t.jsx)(p.ZB,{className:"text-2xl font-bold text-center",children:"ITSync ログイン"}),(0,t.jsx)(p.BT,{className:"text-center",children:"アカウントにサインインしてください"})]}),(0,t.jsx)(p.Wu,{children:(0,t.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[y&&(0,t.jsx)(u.Fc,{variant:"destructive",children:(0,t.jsx)(u.TN,{children:y})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"email",children:"メールアドレス / Email"}),(0,t.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>s(e.target.value),required:!0,disabled:j})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"password",children:"パスワード / Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.p,{id:"password",type:v?"text":"password",placeholder:"••••••••",value:r,onChange:e=>f(e.target.value),required:!0,disabled:j}),(0,t.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>g(!v),disabled:j,children:v?(0,t.jsx)(m.A,{className:"h-4 w-4"}):(0,t.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)(l.$,{type:"submit",className:"w-full",disabled:j,children:[j&&(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"サインイン / Sign In"]})]})})]})})}},43642:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(62740),a=r(95467);function n(){return(0,t.jsx)(a.default,{})}},95467:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\components\\\\auth\\\\login-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\login-form.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8096,2076],()=>r(46624));module.exports=t})();