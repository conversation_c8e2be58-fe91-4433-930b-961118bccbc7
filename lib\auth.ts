import { createClient } from '@supabase/supabase-js'
import { Database } from './database.types'
import { config, validateSupabaseConfig } from './env'

// Validate Supabase configuration
const supabaseConfig = validateSupabaseConfig()

// Create Supabase client for authentication
export const supabase = supabaseConfig.isValid
  ? createClient<Database>(
      config.supabase.url,
      config.supabase.anonKey
    )
  : null

// User roles enum
export enum UserRole {
  GLOBAL_ADMIN = 'Global Administrator',
  SYSTEM_ADMIN = 'Web App System Administrator',
  DEPT_ADMIN = 'Department Administrator',
  HR_STAFF = 'HR Staff',
  IT_SUPPORT = 'IT Helpdesk Support',
  REGULAR_USER = 'Regular User'
}

// Permission types
export interface Permission {
  resource: string
  action: 'create' | 'read' | 'update' | 'delete'
  scope: 'own' | 'department' | 'all'
  conditions?: Record<string, any>
}

// Role definitions with permissions
export const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.GLOBAL_ADMIN]: [
    { resource: '*', action: 'create', scope: 'all' },
    { resource: '*', action: 'read', scope: 'all' },
    { resource: '*', action: 'update', scope: 'all' },
    { resource: '*', action: 'delete', scope: 'all' }
  ],
  [UserRole.SYSTEM_ADMIN]: [
    { resource: 'users', action: 'create', scope: 'all' },
    { resource: 'users', action: 'read', scope: 'all' },
    { resource: 'users', action: 'update', scope: 'all' },
    { resource: 'roles', action: 'create', scope: 'all' },
    { resource: 'roles', action: 'read', scope: 'all' },
    { resource: 'roles', action: 'update', scope: 'all' },
    { resource: 'system', action: 'read', scope: 'all' }
  ],
  [UserRole.DEPT_ADMIN]: [
    { resource: 'users', action: 'create', scope: 'department' },
    { resource: 'users', action: 'read', scope: 'department' },
    { resource: 'users', action: 'update', scope: 'department' },
    { resource: 'requests', action: 'read', scope: 'department' },
    { resource: 'requests', action: 'update', scope: 'department' }
  ],
  [UserRole.HR_STAFF]: [
    { resource: 'hr_requests', action: 'create', scope: 'all' },
    { resource: 'hr_requests', action: 'read', scope: 'all' },
    { resource: 'hr_requests', action: 'update', scope: 'all' },
    { resource: 'users', action: 'create', scope: 'all' },
    { resource: 'users', action: 'read', scope: 'all' },
    { resource: 'users', action: 'update', scope: 'all' }
  ],
  [UserRole.IT_SUPPORT]: [
    { resource: 'requests', action: 'read', scope: 'all' },
    { resource: 'requests', action: 'update', scope: 'all' },
    { resource: 'knowledge_base', action: 'create', scope: 'all' },
    { resource: 'knowledge_base', action: 'read', scope: 'all' },
    { resource: 'knowledge_base', action: 'update', scope: 'all' }
  ],
  [UserRole.REGULAR_USER]: [
    { resource: 'requests', action: 'create', scope: 'own' },
    { resource: 'requests', action: 'read', scope: 'own' },
    { resource: 'knowledge_base', action: 'read', scope: 'all' }
  ]
}

// Authentication functions
export const signIn = async (email: string, password: string) => {
  if (!supabase) {
    return { data: null, error: new Error('Supabase is not configured') }
  }
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  return { data, error }
}

export const signOut = async () => {
  if (!supabase) {
    return { error: new Error('Supabase is not configured') }
  }
  
  const { error } = await supabase.auth.signOut()
  return { error }
}

export const signUp = async (email: string, password: string, userData: any) => {
  if (!supabase) {
    return { data: null, error: new Error('Supabase is not configured') }
  }
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: userData
    }
  })
  return { data, error }
}

export const getCurrentUser = async () => {
  if (!supabase) {
    return { user: null, error: new Error('Supabase is not configured') }
  }
  
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

export const getCurrentSession = async () => {
  if (!supabase) {
    return { session: null, error: new Error('Supabase is not configured') }
  }
  
  const { data: { session }, error } = await supabase.auth.getSession()
  return { session, error }
}

// Get user with role information
export const getUserWithRole = async (userId: string) => {
  // In development mode, return a mock user to avoid RLS errors
  if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'production') {
    console.log('Development mode: Using mock user data')
    return {
      data: {
        id: userId,
        auth_id: userId,
        email: '<EMAIL>',
        first_name: 'Development',
        last_name: 'User',
        role: { name: 'admin' },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      error: null
    }
  }
  
  if (!supabase) {
    return { data: null, error: new Error('Supabase is not configured') }
  }
  
  try {
    // First check if the user exists in the staff table
    const { data, error } = await supabase
      .from('staff')
      .select('*')
      .eq('auth_id', userId)
      .maybeSingle() // Use maybeSingle instead of single to avoid error if no record found
    
    if (error) {
      console.error('Database error:', error.message)
      return { data: null, error }
    }
    
    // If staff record found, fetch related data separately
    if (data) {
      // Create an enriched staff object
      const enrichedStaff = { ...data };
      
      try {
        // Fetch division data if division_id exists
        if (data.division_id) {
          const { data: divisionData } = await supabase
            .from('divisions')
            .select('*')
            .eq('id', data.division_id)
            .single();
          
          if (divisionData) {
            enrichedStaff.division = divisionData;
          }
        }
        
        // Fetch group data if group_id exists
        if (data.group_id) {
          const { data: groupData } = await supabase
            .from('groups')
            .select('*')
            .eq('id', data.group_id)
            .single();
          
          if (groupData) {
            enrichedStaff.group = groupData;
          }
        }
        
        // Fetch role data if role_id exists
        if (data.role_id) {
          const { data: roleData } = await supabase
            .from('roles')
            .select('*')
            .eq('id', data.role_id)
            .single();
          
          if (roleData) {
            enrichedStaff.role = roleData;
          }
        }
        
        return { data: enrichedStaff, error: null };
      } catch (fetchError) {
        console.error('Error fetching related data:', fetchError);
        // Return the basic staff data even if related data fetch fails
        return { data, error: null };
      }
    }
    
    // If no staff record found, check if user exists in profiles table
    if (!data) {
      try {
        // Try to get basic user profile information
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .maybeSingle()
        
        if (profileError) {
          // Check if this is an RLS policy error
          if (profileError.message && (profileError.message.includes('infinite recursion') || profileError.message.includes('permission denied'))) {
            console.warn('RLS policy error for profiles table. Using basic user data instead.')
            
            // Instead of making another API call, create a minimal user object
            // This reduces the number of API calls and potential for more errors
            return { 
              data: {
                id: userId,
                // Add minimal required fields
                role: { name: 'regular_user' },
                // Add default values for required fields
                email: '',
                first_name: '',
                last_name: '',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              }, 
              error: null 
            }
          } else {
            console.error('Profile lookup error:', profileError.message)
            return { data: null, error: profileError }
          }
        }
        
        // Return profile data if found, or null if not
        return { data: profileData, error: null }
      } catch (profileErr) {
        console.error('Error accessing profiles:', profileErr)
        // Fallback to basic user data in development
        if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'production') {
          return { 
            data: { 
              id: userId,
              role: { name: 'regular_user' } 
            }, 
            error: null 
          }
        }
        return { data: null, error: profileErr instanceof Error ? profileErr : new Error('Unknown error') }
      }
    }
    
    // Return staff data if found
    return { data, error: null }
  } catch (err) {
    console.error('Unexpected error in getUserWithRole:', err)
    return { data: null, error: err instanceof Error ? err : new Error('Unknown error') }
  }
}

// Check if user has specific permission
export const hasPermission = (
  userRole: UserRole,
  resource: string,
  action: string,
  scope?: string
): boolean => {
  const permissions = rolePermissions[userRole]
  if (!permissions) return false

  return permissions.some(permission => {
    if (permission.resource === '*') return true
    if (permission.resource === resource && permission.action === action) {
      if (scope && permission.scope !== scope && permission.scope !== 'all') {
        return false
      }
      return true
    }
    return false
  })
}

// Check if user can access department data
export const canAccessDepartment = (
  userRole: UserRole,
  userDepartmentId: string,
  targetDepartmentId: string
): boolean => {
  if (userRole === UserRole.GLOBAL_ADMIN || userRole === UserRole.SYSTEM_ADMIN) {
    return true
  }
  if (userRole === UserRole.DEPT_ADMIN) {
    return userDepartmentId === targetDepartmentId
  }
  if (userRole === UserRole.HR_STAFF || userRole === UserRole.IT_SUPPORT) {
    return true
  }
  if (userRole === UserRole.REGULAR_USER) {
    return userDepartmentId === targetDepartmentId
  }
  return false
}
