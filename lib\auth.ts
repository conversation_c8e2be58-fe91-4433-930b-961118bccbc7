import { createClient, browserClient, serverClient, serviceClient } from './supabase/index'
import { Database } from './database.types'
import { ErrorService, ErrorCategory, ErrorSeverity, ErrorContext } from './services/error-service'

// Create Supabase client for authentication
// Use the service client for auth operations that need elevated privileges
export const supabase = typeof window === 'undefined'
  ? serverClient()
  : browserClient()

// Service client for admin operations
export const adminClient = serviceClient()

// User roles enum
export enum UserRole {
  GLOBAL_ADMIN = 'Global Administrator',
  SYSTEM_ADMIN = 'Web App System Administrator',
  DEPT_ADMIN = 'Department Administrator',
  HR_STAFF = 'HR Staff',
  IT_SUPPORT = 'IT Helpdesk Support',
  REGULAR_USER = 'Regular User'
}

// Permission types
export interface Permission {
  resource: string
  action: 'create' | 'read' | 'update' | 'delete'
  scope: 'own' | 'department' | 'all'
  conditions?: Record<string, any>
}

// Role definitions with permissions
export const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.GLOBAL_ADMIN]: [
    { resource: '*', action: 'create', scope: 'all' },
    { resource: '*', action: 'read', scope: 'all' },
    { resource: '*', action: 'update', scope: 'all' },
    { resource: '*', action: 'delete', scope: 'all' }
  ],
  [UserRole.SYSTEM_ADMIN]: [
    { resource: 'users', action: 'create', scope: 'all' },
    { resource: 'users', action: 'read', scope: 'all' },
    { resource: 'users', action: 'update', scope: 'all' },
    { resource: 'roles', action: 'create', scope: 'all' },
    { resource: 'roles', action: 'read', scope: 'all' },
    { resource: 'roles', action: 'update', scope: 'all' },
    { resource: 'system', action: 'read', scope: 'all' }
  ],
  [UserRole.DEPT_ADMIN]: [
    { resource: 'users', action: 'create', scope: 'department' },
    { resource: 'users', action: 'read', scope: 'department' },
    { resource: 'users', action: 'update', scope: 'department' },
    { resource: 'requests', action: 'read', scope: 'department' },
    { resource: 'requests', action: 'update', scope: 'department' }
  ],
  [UserRole.HR_STAFF]: [
    { resource: 'hr_requests', action: 'create', scope: 'all' },
    { resource: 'hr_requests', action: 'read', scope: 'all' },
    { resource: 'hr_requests', action: 'update', scope: 'all' },
    { resource: 'users', action: 'create', scope: 'all' },
    { resource: 'users', action: 'read', scope: 'all' },
    { resource: 'users', action: 'update', scope: 'all' }
  ],
  [UserRole.IT_SUPPORT]: [
    { resource: 'requests', action: 'read', scope: 'all' },
    { resource: 'requests', action: 'update', scope: 'all' },
    { resource: 'knowledge_base', action: 'create', scope: 'all' },
    { resource: 'knowledge_base', action: 'read', scope: 'all' },
    { resource: 'knowledge_base', action: 'update', scope: 'all' }
  ],
  [UserRole.REGULAR_USER]: [
    { resource: 'requests', action: 'create', scope: 'own' },
    { resource: 'requests', action: 'read', scope: 'own' },
    { resource: 'knowledge_base', action: 'read', scope: 'all' }
  ]
}

// Authentication functions
export const signIn = async (email: string, password: string) => {
  if (!supabase) {
    return { data: null, error: new Error('Supabase is not configured') }
  }
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  return { data, error }
}

export const signOut = async () => {
  if (!supabase) {
    return { error: new Error('Supabase is not configured') }
  }
  
  const { error } = await supabase.auth.signOut()
  return { error }
}

export const signUp = async (email: string, password: string, userData: any) => {
  if (!supabase) {
    return { data: null, error: new Error('Supabase is not configured') }
  }
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: userData
    }
  })
  return { data, error }
}

export const getCurrentUser = async () => {
  if (!supabase) {
    return { user: null, error: new Error('Supabase is not configured') }
  }
  
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

export const getCurrentSession = async () => {
  if (!supabase) {
    return { session: null, error: new Error('Supabase is not configured') }
  }
  
  const { data: { session }, error } = await supabase.auth.getSession()
  return { session, error }
}

/**
 * User response type with consistent structure
 */
interface UserResponse {
  data: any | null;
  error: Error | null;
}

/**
 * Creates a fallback user object with minimal required fields
 */
export const createFallbackUser = (userId: string, role: string = 'regular_user'): any => ({
  id: userId,
  auth_id: userId,
  email: '',
  first_name: '',
  last_name: '',
  role: { name: role },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

/**
 * Creates a development mode mock user
 */
export const createDevMockUser = (userId: string): any => ({
  id: userId,
  auth_id: userId,
  email: '<EMAIL>',
  first_name: 'Development',
  last_name: 'User',
  role: { name: 'admin' },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

/**
 * Standardized error handler for auth operations
 */
const handleAuthError = (error: any, context: string, userId: string): Error => {
  const errorContext: ErrorContext = {
    userId,
    component: 'auth',
    action: context
  };
  
  return ErrorService.createAuthError(
    error instanceof Error ? error.message : `${context}: ${String(error)}`,
    'AUTH_USER_ERROR',
    errorContext
  );
};

/**
 * Fetch staff record with related data in a single query using joins
 */
const fetchStaffWithRelations = async (userId: string): Promise<UserResponse> => {
  if (!supabase) {
    return {
      data: null,
      error: ErrorService.createAuthError('Supabase is not configured', 'AUTH_CONFIG_ERROR', { userId })
    };
  }

  try {
    // Use a single query with joins to fetch staff and related data
    const { data, error } = await supabase
      .from('staff')
      .select(`
        *,
        division:divisions(*),
        group:groups(*),
        role:roles(*)
      `)
      .eq('auth_id', userId)
      .maybeSingle();

    if (error) {
      return {
        data: null,
        error: ErrorService.createDatabaseError(
          'Database error fetching staff',
          error instanceof Error ? error : new Error(String(error)),
          { userId, action: 'fetchStaffWithRelations' }
        )
      };
    }

    return { data, error: null };
  } catch (error) {
    return {
      data: null,
      error: ErrorService.createAppError(error instanceof Error ? error : String(error), {
        severity: ErrorSeverity.ERROR,
        category: ErrorCategory.DATABASE,
        code: 'STAFF_FETCH_ERROR',
        context: { userId, action: 'fetchStaffWithRelations' },
        originalError: error instanceof Error ? error : undefined
      })
    };
  }
};

/**
 * Fetch user profile as fallback
 */
const fetchUserProfile = async (userId: string): Promise<UserResponse> => {
  if (!supabase) {
    return {
      data: null,
      error: ErrorService.createAuthError('Supabase is not configured', 'AUTH_CONFIG_ERROR', { userId })
    };
  }

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      // Check if this is an RLS policy error
      if (error.message && (error.message.includes('infinite recursion') || error.message.includes('permission denied'))) {
        ErrorService.logError(
          new Error(`RLS policy error for profiles table: ${error.message}`),
          ErrorSeverity.WARNING,
          ErrorCategory.SECURITY,
          { userId, action: 'fetchUserProfile', additionalData: { errorType: 'RLS_POLICY' } }
        );
        return { data: createFallbackUser(userId), error: null };
      }
      
      return {
        data: null,
        error: ErrorService.createDatabaseError(
          'Profile lookup error',
          error instanceof Error ? error : new Error(String(error)),
          { userId, action: 'fetchUserProfile' }
        )
      };
    }

    return { data, error: null };
  } catch (error) {
    return {
      data: null,
      error: ErrorService.createAppError(error instanceof Error ? error : String(error), {
        severity: ErrorSeverity.ERROR,
        category: ErrorCategory.DATABASE,
        code: 'PROFILE_FETCH_ERROR',
        context: { userId, action: 'fetchUserProfile' },
        originalError: error instanceof Error ? error : undefined
      })
    };
  }
};

/**
 * Get user with role information using a clean, structured approach
 */
export const getUserWithRole = async (userId: string): Promise<UserResponse> => {
  // Development mode shortcut
  if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'production') {
    ErrorService.logError(
      new Error(`Using mock user data for getUserWithRole. User ID: ${userId}`),
      ErrorSeverity.INFO,
      ErrorCategory.AUTH,
      { userId, action: 'getUserWithRole', additionalData: { mode: 'development' } }
    );
    return { data: createDevMockUser(userId), error: null };
  }
  
  if (!supabase) {
    return {
      data: null,
      error: ErrorService.createAuthError('Supabase is not configured', 'AUTH_CONFIG_ERROR', { userId })
    };
  }
  
  try {
    // First try to get staff record with relations
    const staffResult = await fetchStaffWithRelations(userId);
    
    // If staff record found, return it
    if (staffResult.data) {
      return staffResult;
    }
    
    // If no staff record, try profile as fallback
    const profileResult = await fetchUserProfile(userId);
    
    // If neither found and in development, provide minimal user
    if (!profileResult.data && typeof process !== 'undefined' && process.env.NODE_ENV !== 'production') {
      ErrorService.logError(
        new Error(`No user data found for User ID ${userId}. Using fallback in development.`),
        ErrorSeverity.WARNING,
        ErrorCategory.AUTH,
        { userId, action: 'getUserWithRole', additionalData: { fallback: true } }
      );
      return { data: createFallbackUser(userId), error: null };
    }
    
    return profileResult;
  } catch (error) {
    return {
      data: null,
      error: ErrorService.createAuthError(
        `Unexpected error in getUserWithRole: ${error instanceof Error ? error.message : String(error)}`,
        'AUTH_UNEXPECTED_ERROR',
        { userId, action: 'getUserWithRole' }
      )
    };
  }
}

// Check if user has specific permission
export const hasPermission = (
  userRole: UserRole,
  resource: string,
  action: string,
  scope?: string
): boolean => {
  const permissions = rolePermissions[userRole]
  if (!permissions) {
    console.warn(`[AUTH_PERMISSIONS_CONFIG] No permissions defined for role: ${userRole}. Denying permission check.`);
    return false;
  }

  return permissions.some(permission => {
    if (permission.resource === '*') return true
    if (permission.resource === resource && permission.action === action) {
      if (scope && permission.scope !== scope && permission.scope !== 'all') {
        return false
      }
      return true
    }
    return false
  })
}

// Check if user can access department data
export const canAccessDepartment = (
  userRole: UserRole,
  userDepartmentId: string,
  targetDepartmentId: string
): boolean => {
  if (userRole === UserRole.GLOBAL_ADMIN || userRole === UserRole.SYSTEM_ADMIN) {
    return true
  }
  if (userRole === UserRole.DEPT_ADMIN) {
    return userDepartmentId === targetDepartmentId
  }
  if (userRole === UserRole.HR_STAFF || userRole === UserRole.IT_SUPPORT) {
    return true
  }
  if (userRole === UserRole.REGULAR_USER) {
    return userDepartmentId === targetDepartmentId
  }
  return false
}
