"use strict";exports.id=1274,exports.ids=[1274],exports.modules={91274:(e,t,r)=>{var o,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(u,{createBrowserSupabaseClient:()=>O,createClientComponentClient:()=>c,createMiddlewareClient:()=>b,createMiddlewareSupabaseClient:()=>P,createPagesBrowserClient:()=>p,createPagesServerClient:()=>g,createRouteHandlerClient:()=>w,createServerActionClient:()=>A,createServerComponentClient:()=>m,createServerSupabaseClient:()=>E}),e.exports=((e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))s.call(e,r)||void 0===r||i(e,r,{get:()=>t[r],enumerable:!(o=a(t,r))||o.enumerable});return e})(i({},"__esModule",{value:!0}),u);var l=r(40745);function c({supabaseUrl:e="your_supabase_project_url",supabaseKey:t="your_supabase_anon_key",options:r,cookieOptions:i,isSingleton:a=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let n=()=>{var o;return(0,l.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(o=null==r?void 0:r.global)?void 0:o.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new l.BrowserCookieAuthStorageAdapter(i)}})};if(a){let e=o??n();return"undefined"==typeof window?e:(o||(o=e),o)}return n()}var p=c,d=r(40745),h=r(45491),f=class extends d.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,o;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,d.parseCookies)(t)[e]).find(e=>!!e)??(null==(o=this.context.req)?void 0:o.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var o;let i=(0,h.splitCookiesString)((null==(o=this.context.res.getHeader("set-cookie"))?void 0:o.toString())??"").filter(t=>!(e in(0,d.parseCookies)(t))),a=(0,d.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...i,a])}};function g(e,{supabaseUrl:t="your_supabase_project_url",supabaseKey:r="your_supabase_anon_key",options:o,cookieOptions:i}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,d.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(a=null==o?void 0:o.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new f(e,i)}})}var k=r(40745),v=r(45491),_=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return(0,v.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,k.parseCookies)(t)[e]).find(e=>!!e)||(0,k.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let o=(0,k.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",o)}};function b(e,{supabaseUrl:t="your_supabase_project_url",supabaseKey:r="your_supabase_anon_key",options:o,cookieOptions:i}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(a=null==o?void 0:o.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new _(e,i)}})}var C=r(40745),y=class extends C.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function m(e,{supabaseUrl:t="your_supabase_project_url",supabaseKey:r="your_supabase_anon_key",options:o,cookieOptions:i}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,C.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(a=null==o?void 0:o.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new y(e,i)}})}var S=r(40745),x=class extends S.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function w(e,{supabaseUrl:t="your_supabase_project_url",supabaseKey:r="your_supabase_anon_key",options:o,cookieOptions:i}={}){var a;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,S.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(a=null==o?void 0:o.global)?void 0:a.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new x(e,i)}})}var A=w;function O({supabaseUrl:e="your_supabase_project_url",supabaseKey:t="your_supabase_anon_key",options:r,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),p({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:o})}function E(e,{supabaseUrl:t="your_supabase_project_url",supabaseKey:r="your_supabase_anon_key",options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),g(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}function P(e,{supabaseUrl:t="your_supabase_project_url",supabaseKey:r="your_supabase_anon_key",options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),b(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}},45491:e=>{var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function o(e,o){var i,a,n,s,u=e.split(";").filter(r),l=(i=u.shift(),a="",n="",(s=i.split("=")).length>1?(a=s.shift(),n=s.join("=")):n=i,{name:a,value:n}),c=l.name,p=l.value;o=o?Object.assign({},t,o):t;try{p=o.decodeValues?decodeURIComponent(p):p}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+p+"'. Set options.decodeValues to false to disable this feature.",e)}var d={name:c,value:p};return u.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),o=t.join("=");"expires"===r?d.expires=new Date(o):"max-age"===r?d.maxAge=parseInt(o,10):"secure"===r?d.secure=!0:"httponly"===r?d.httpOnly=!0:"samesite"===r?d.sameSite=o:"partitioned"===r?d.partitioned=!0:d[r]=o}),d}function i(e,i){if(i=i?Object.assign({},t,i):t,!e)return i.map?{}:[];if(e.headers){if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var a=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];a||!e.headers.cookie||i.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=a}}return(Array.isArray(e)||(e=[e]),i.map)?e.filter(r).reduce(function(e,t){var r=o(t,i);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return o(e,i)})}e.exports=i,e.exports.parse=i,e.exports.parseString=o,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,o,i,a,n=[],s=0;function u(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;u();)if(","===(r=e.charAt(s))){for(o=s,s+=1,u(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,n.push(e.substring(t,o)),t=s):s=o+1}else s+=1;(!a||s>=e.length)&&n.push(e.substring(t,e.length))}return n}},40745:(e,t,r)=>{r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>x,CookieAuthStorageAdapter:()=>S,DEFAULT_COOKIE_OPTIONS:()=>y,createSupabaseClient:()=>w,isBrowser:()=>C,parseCookies:()=>A,parseSupabaseCookie:()=>_,serializeCookie:()=>O,stringifySupabaseSession:()=>b});var o=r(79428);new TextEncoder;let i=new TextDecoder;o.Buffer.isEncoding("base64url");let a=e=>o.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=i.decode(t)),t}(e),"base64");var n=r(93939),s=Object.create,u=Object.defineProperty,l=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,p=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,h=(e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of c(t))d.call(e,i)||i===r||u(e,i,{get:()=>t[i],enumerable:!(o=l(t,i))||o.enumerable});return e},f=(e,t,r)=>(r=null!=e?s(p(e)):{},h(!t&&e&&e.__esModule?r:u(r,"default",{value:e,enumerable:!0}),e)),g=((e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},i=(t||{}).decode||o,a=0;a<e.length;){var n=e.indexOf("=",a);if(-1===n)break;var s=e.indexOf(";",a);if(-1===s)s=e.length;else if(s<n){a=e.lastIndexOf(";",n-1)+1;continue}var u=e.slice(a,n).trim();if(void 0===r[u]){var l=e.slice(n+1,s).trim();34===l.charCodeAt(0)&&(l=l.slice(1,-1)),r[u]=function(e,t){try{return t(e)}catch(t){return e}}(l,i)}a=s+1}return r},e.serialize=function(e,o,a){var n=a||{},s=n.encode||i;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var u=s(o);if(u&&!r.test(u))throw TypeError("argument val is invalid");var l=e+"="+u;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){var p=n.expires;if("[object Date]"!==t.call(p)&&!(p instanceof Date)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");l+="; Expires="+p.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function i(e){return encodeURIComponent(e)}}}),k=f(g()),v=f(g());function _(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,o,i]=t[0].split("."),n=a(o),s=new TextDecoder,{exp:u,sub:l,...c}=JSON.parse(s.decode(n));return{expires_at:u,expires_in:u-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:l,factors:t[4],...c}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function b(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function C(){return"undefined"!=typeof window&&void 0!==window.document}var y={path:"/",sameSite:"lax",maxAge:31536e6},m=RegExp(".{1,3180}","g"),S=class{constructor(e){this.cookieOptions={...y,...e,maxAge:y.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(_(t));let r=function(e,t=()=>null){let r=[];for(let o=0;;o++){let i=t(`${e}.${o}`);if(!i)break;r.push(i)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(_(r)):null}setItem(e,t){if(e.endsWith("-code-verifier")){this.setCookie(e,t);return}(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let o=[],i=t.match(m);return null==i||i.forEach((t,r)=>{let i=`${e}.${r}`;o.push({name:i,value:t})}),o})(e,b(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},x=class extends S{constructor(e){super(e)}getCookie(e){return C()?(0,k.parse)(document.cookie)[e]:null}setCookie(e,t){if(!C())return null;document.cookie=(0,k.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!C())return null;document.cookie=(0,k.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function w(e,t,r){var o;let i=C();return(0,n.createClient)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:i,detectSessionInUrl:i,persistSession:!0,storage:r.auth.storage,...(null==(o=r.auth)?void 0:o.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var A=v.parse,O=v.serialize}};