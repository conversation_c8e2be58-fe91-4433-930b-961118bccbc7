import { NextRequest, NextResponse } from 'next/server'
import { embeddingsService } from '@/lib/services/embeddings-service'

export const runtime = 'nodejs'

export async function POST(request: NextRequest) {
  try {
    await embeddingsService.updateStaleEmbeddings()

    return NextResponse.json({
      success: true,
      message: 'Stale embeddings update initiated'
    })
  } catch (error) {
    console.error('Update stale embeddings error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Update failed'
      },
      { status: 500 }
    )
  }
}
