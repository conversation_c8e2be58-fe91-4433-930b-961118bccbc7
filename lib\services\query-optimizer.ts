import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'

interface QueryPerformanceMetrics {
  queryTime: number
  rowCount: number
  cached: boolean
}

export class QueryOptimizer {
  private queryCache: Map<string, { data: any; timestamp: number }> = new Map()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  /**
   * Execute optimized query with caching
   */
  async executeOptimizedQuery<T>(
    queryKey: string,
    queryFn: () => Promise<T>,
    options: {
      cache?: boolean
      cacheTimeout?: number
    } = {}
  ): Promise<{ data: T; metrics: QueryPerformanceMetrics }> {
    const startTime = Date.now()
    const { cache = true, cacheTimeout = this.cacheTimeout } = options

    // Check cache first
    if (cache) {
      const cached = this.queryCache.get(queryKey)
      if (cached && Date.now() - cached.timestamp < cacheTimeout) {
        return {
          data: cached.data as T,
          metrics: {
            queryTime: 0,
            rowCount: Array.isArray(cached.data) ? cached.data.length : 1,
            cached: true
          }
        }
      }
    }

    // Execute query
    const data = await queryFn()
    const queryTime = Date.now() - startTime

    // Cache result
    if (cache) {
      this.queryCache.set(queryKey, {
        data,
        timestamp: Date.now()
      })
    }

    return {
      data,
      metrics: {
        queryTime,
        rowCount: Array.isArray(data) ? data.length : 1,
        cached: false
      }
    }
  }

  /**
   * Clear cache for specific key or all cache
   */
  clearCache(queryKey?: string) {
    if (queryKey) {
      this.queryCache.delete(queryKey)
    } else {
      this.queryCache.clear()
    }
  }

  /**
   * Get department statistics using materialized view
   */
  async getDepartmentStats() {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    return this.executeOptimizedQuery(
      'department-stats',
      async () => {
        const { data, error } = await supabase
          .from('mv_department_request_stats')
          .select('*')
          .order('total_requests', { ascending: false })

        if (error) throw error
        return data
      },
      { cacheTimeout: 15 * 60 * 1000 } // 15 minutes
    )
  }

  /**
   * Get workflow performance metrics using materialized view
   */
  async getWorkflowPerformance() {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    return this.executeOptimizedQuery(
      'workflow-performance',
      async () => {
        const { data, error } = await supabase
          .from('mv_workflow_performance')
          .select('*')

        if (error) throw error
        return data
      },
      { cacheTimeout: 15 * 60 * 1000 } // 15 minutes
    )
  }

  /**
   * Batch fetch users with optimized query
   */
  async batchFetchUsers(userIds: string[]) {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    const cacheKey = `users-batch-${userIds.sort().join('-')}`
    
    return this.executeOptimizedQuery(
      cacheKey,
      async () => {
        const { data, error } = await supabase
          .from('staff')
          .select(`
            id,
            name_en,
            name_jp,
            email,
            divisions!inner (
              id,
              name_en,
              name_jp
            )
          `)
          .in('id', userIds)
          .eq('is_active', true)

        if (error) throw error
        return data
      }
    )
  }

  /**
   * Get active workflows with optimized partial index
   */
  async getActiveWorkflows(limit = 100) {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    return this.executeOptimizedQuery(
      `active-workflows-${limit}`,
      async () => {
        const { data, error } = await supabase
          .from('workflow_instances')
          .select(`
            id,
            status,
            priority,
            created_at,
            request_forms!inner (
              id,
              title
            )
          `)
          .in('status', ['pending', 'in_progress', 'pending_approval'])
          .order('created_at', { ascending: false })
          .limit(limit)

        if (error) throw error
        return data
      },
      { cacheTimeout: 30 * 1000 } // 30 seconds for active data
    )
  }

  /**
   * Search staff with full-text search
   */
  async searchStaff(query: string, departmentId?: string) {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    const cacheKey = `staff-search-${query}-${departmentId || 'all'}`

    return this.executeOptimizedQuery(
      cacheKey,
      async () => {
        let searchQuery = supabase
          .from('staff')
          .select(`
            id,
            name_en,
            name_jp,
            email,
            divisions!inner (
              id,
              name_en,
              name_jp
            )
          `)
          .textSearch('name_en', query, {
            type: 'websearch',
            config: 'simple'
          })
          .eq('is_active', true)

        if (departmentId) {
          searchQuery = searchQuery.eq('division_id', departmentId)
        }

        const { data, error } = await searchQuery.limit(50)

        if (error) throw error
        return data
      },
      { cacheTimeout: 5 * 60 * 1000 } // 5 minutes
    )
  }

  /**
   * Get request forms with pagination
   */
  async getRequestFormsPaginated(
    page: number,
    pageSize: number,
    filters?: {
      status?: string
      departmentId?: string
      startDate?: Date
      endDate?: Date
    }
  ) {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    const offset = (page - 1) * pageSize
    const cacheKey = `requests-${page}-${pageSize}-${JSON.stringify(filters)}`

    return this.executeOptimizedQuery(
      cacheKey,
      async () => {
        let query = supabase
          .from('request_forms')
          .select(`
            *,
            staff!inner (
              id,
              name_en,
              name_jp,
              divisions!inner (
                id,
                name_en,
                name_jp
              )
            ),
            service_categories (
              id,
              name_en,
              name_jp
            )
          `, { count: 'exact' })

        // Apply filters
        if (filters?.status) {
          query = query.eq('status', filters.status)
        }
        if (filters?.departmentId) {
          query = query.eq('staff.division_id', filters.departmentId)
        }
        if (filters?.startDate) {
          query = query.gte('created_at', filters.startDate.toISOString())
        }
        if (filters?.endDate) {
          query = query.lte('created_at', filters.endDate.toISOString())
        }

        // Apply pagination
        query = query
          .order('created_at', { ascending: false })
          .range(offset, offset + pageSize - 1)

        const { data, error, count } = await query

        if (error) throw error
        return { data, count }
      }
    )
  }

  /**
   * Refresh materialized views
   */
  async refreshMaterializedViews() {
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    const { error } = await supabase.rpc('refresh_performance_views')
    if (error) throw error

    // Clear related cache
    this.clearCache('department-stats')
    this.clearCache('workflow-performance')
  }
}

// Export singleton instance
export const queryOptimizer = new QueryOptimizer()
