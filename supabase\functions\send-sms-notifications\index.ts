import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SmsNotification {
  id: string;
  to_phone: string;
  message: string;
  priority: string;
  metadata?: Record<string, any>;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Parse request body
    const { messages } = await req.json() as { messages: SmsNotification[] }

    if (!messages || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No messages to send' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get SMS service configuration
    const smsProvider = Deno.env.get('SMS_PROVIDER') || 'twilio'
    const results = []

    for (const sms of messages) {
      try {
        let result;
        
        switch (smsProvider) {
          case 'twilio':
            result = await sendViaTwilio(sms);
            break;
          case 'aws-sns':
            result = await sendViaAWSSNS(sms);
            break;
          default:
            // For development/testing, just log the SMS
            console.log('SMS would be sent:', sms);
            result = { success: true, messageId: `dev-${Date.now()}` };
        }

        results.push({
          id: sms.id,
          success: true,
          messageId: result.messageId
        });

        // Update SMS queue status
        await supabaseClient
          .from('notification_sms_queue')
          .update({
            status: 'sent',
            sent_at: new Date().toISOString(),
            provider_message_id: result.messageId
          })
          .eq('id', sms.id);

        // Log successful send
        await supabaseClient
          .from('notification_logs')
          .insert({
            notification_id: sms.metadata?.notification_id,
            channel: 'sms',
            status: 'sent',
            recipient: sms.to_phone,
            metadata: { messageId: result.messageId }
          });

      } catch (error) {
        console.error(`Failed to send SMS ${sms.id}:`, error);
        
        results.push({
          id: sms.id,
          success: false,
          error: error.message
        });

        // Update SMS queue status
        await supabaseClient
          .from('notification_sms_queue')
          .update({
            status: 'failed',
            last_error: error.message,
            attempts: sms.attempts + 1
          })
          .eq('id', sms.id);

        // Log failed send
        await supabaseClient
          .from('notification_logs')
          .insert({
            notification_id: sms.metadata?.notification_id,
            channel: 'sms',
            status: 'failed',
            recipient: sms.to_phone,
            error_message: error.message
          });
      }
    }

    return new Response(
      JSON.stringify({ results }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// Twilio integration
async function sendViaTwilio(sms: SmsNotification) {
  const TWILIO_ACCOUNT_SID = Deno.env.get('TWILIO_ACCOUNT_SID');
  const TWILIO_AUTH_TOKEN = Deno.env.get('TWILIO_AUTH_TOKEN');
  const TWILIO_FROM_NUMBER = Deno.env.get('TWILIO_FROM_NUMBER');
  
  if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_FROM_NUMBER) {
    throw new Error('Twilio credentials not configured');
  }

  const credentials = btoa(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`);
  
  const response = await fetch(
    `https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${credentials}`,
      },
      body: new URLSearchParams({
        From: TWILIO_FROM_NUMBER,
        To: sms.to_phone,
        Body: sms.message,
      }),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Twilio API error: ${error.message || 'Unknown error'}`);
  }

  const data = await response.json();
  return { messageId: data.sid };
}

// AWS SNS integration (placeholder)
async function sendViaAWSSNS(sms: SmsNotification) {
  // TODO: Implement AWS SNS integration
  throw new Error('AWS SNS integration not yet implemented');
}
