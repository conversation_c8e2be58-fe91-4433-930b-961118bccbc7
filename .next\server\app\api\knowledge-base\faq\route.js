"use strict";(()=>{var e={};e.id=2541,e.ids=[2541],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},94735:e=>{e.exports=require("events")},81630:e=>{e.exports=require("http")},55591:e=>{e.exports=require("https")},28354:e=>{e.exports=require("util")},74075:e=>{e.exports=require("zlib")},31954:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>d});var a=t(42706),o=t(28203),n=t(45994),i=t(39187),u=t(33406),p=t(44512);async function d(e){try{let r=(0,u.createRouteHandlerClient)({cookies:p.UL}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await r.from("staff").select("roles!inner(name)").eq("auth_id",t.id).single();if(!a||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(a.roles.name))return i.NextResponse.json({error:"Insufficient permissions"},{status:403});let o=await e.json(),{articleIds:n,categories:d,fromSearchPatterns:l,timeRange:c,options:x}=o,{data:f,error:g}=await r.functions.invoke("generate-faq",{body:{articleIds:n,categories:d,fromSearchPatterns:l,timeRange:c,options:x}});if(g)return console.error("Edge function error:",g),i.NextResponse.json({error:g.message},{status:500});return await r.from("faq_generation_logs").insert({generation_type:l?"search_patterns":"kb_articles",parameters:o,total_generated:f.count||0,total_saved:f.saved||0,user_id:t.id}),i.NextResponse.json(f)}catch(e){return console.error("FAQ generation error:",e),i.NextResponse.json({error:"Failed to generate FAQs"},{status:500})}}async function l(e){try{let r=(0,u.createRouteHandlerClient)({cookies:p.UL}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("category"),n=a.get("active"),d=r.from("chatbot_faq").select("*").order("created_at",{ascending:!1});o&&(d=d.eq("category",o)),null!==n&&(d=d.eq("is_active","true"===n));let{data:l,error:c}=await d;if(c)return i.NextResponse.json({error:c.message},{status:500});return i.NextResponse.json({faqs:l})}catch(e){return console.error("FAQ fetch error:",e),i.NextResponse.json({error:"Failed to fetch FAQs"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/knowledge-base/faq/route",pathname:"/api/knowledge-base/faq",filename:"route",bundlePath:"app/api/knowledge-base/faq/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\faq\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:g}=c;function q(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(31954));module.exports=s})();