(()=>{var e={};e.id=2541,e.ids=[2541],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},87175:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>c});var a=t(42706),o=t(28203),n=t(45994),i=t(39187),u=t(33406),p=t(44512);async function c(e){try{let r=(0,u.createRouteHandlerClient)({cookies:p.UL}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await r.from("staff").select("roles!inner(name)").eq("auth_id",t.id).single();if(!a||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(a.roles.name))return i.NextResponse.json({error:"Insufficient permissions"},{status:403});let o=await e.json(),{articleIds:n,categories:c,fromSearchPatterns:d,timeRange:l,options:f}=o,{data:g,error:x}=await r.functions.invoke("generate-faq",{body:{articleIds:n,categories:c,fromSearchPatterns:d,timeRange:l,options:f}});if(x)return console.error("Edge function error:",x),i.NextResponse.json({error:x.message},{status:500});return await r.from("faq_generation_logs").insert({generation_type:d?"search_patterns":"kb_articles",parameters:o,total_generated:g.count||0,total_saved:g.saved||0,user_id:t.id}),i.NextResponse.json(g)}catch(e){return console.error("FAQ generation error:",e),i.NextResponse.json({error:"Failed to generate FAQs"},{status:500})}}async function d(e){try{let r=(0,u.createRouteHandlerClient)({cookies:p.UL}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("category"),n=a.get("active"),c=r.from("chatbot_faq").select("*").order("created_at",{ascending:!1});o&&(c=c.eq("category",o)),null!==n&&(c=c.eq("is_active","true"===n));let{data:d,error:l}=await c;if(l)return i.NextResponse.json({error:l.message},{status:500});return i.NextResponse.json({faqs:d})}catch(e){return console.error("FAQ fetch error:",e),i.NextResponse.json({error:"Failed to fetch FAQs"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/knowledge-base/faq/route",pathname:"/api/knowledge-base/faq",filename:"route",bundlePath:"app/api/knowledge-base/faq/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\faq\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:x}=l;function m(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,3406],()=>t(87175));module.exports=s})();