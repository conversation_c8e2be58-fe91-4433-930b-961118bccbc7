import { SupabaseClient } from '@supabase/supabase-js';
import { AuditService } from '../../audit/audit-service';

interface EscalationRule {
  id?: string;
  name: string;
  description: string;
  triggerType: 'sla_breach' | 'overdue' | 'manual' | 'approval_timeout';
  conditions: EscalationCondition[];
  actions: EscalationAction[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  retryInterval?: number;
  maxRetries?: number;
  applicableToCategories?: string[]; // Service category IDs
  applicableToDepartments?: string[]; // Department IDs
  createdBy: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface EscalationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'age_hours' | 'age_days';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

interface EscalationAction {
  type: 'notify' | 'reassign' | 'change_priority' | 'add_approver' | 'auto_approve' | 'auto_reject' | 'custom_webhook';
  target?: string;
  parameters?: Record<string, any>;
}

export class EscalationRulesManager {
  private supabase: SupabaseClient;
  private auditService: AuditService;

  constructor(supabase: SupabaseClient, auditService: AuditService) {
    this.supabase = supabase;
    this.auditService = auditService;
  }

  // Create a new escalation rule
  async createRule(rule: EscalationRule): Promise<EscalationRule> {
    try {
      const { data, error } = await this.supabase
        .from('escalation_rules')
        .insert({
          ...rule,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      await this.auditService.logActivity({
        userId: rule.createdBy,
        action: 'create_escalation_rule',
        resourceType: 'escalation_rule',
        resourceId: data.id,
        details: { name: rule.name },
      });

      return data;
    } catch (error) {
      console.error('Error creating escalation rule:', error);
      throw error;
    }
  }

  // Update an existing rule
  async updateRule(id: string, updates: Partial<EscalationRule>, userId: string): Promise<EscalationRule> {
    try {
      const { data, error } = await this.supabase
        .from('escalation_rules')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      await this.auditService.logActivity({
        userId,
        action: 'update_escalation_rule',
        resourceType: 'escalation_rule',
        resourceId: id,
        details: { updates },
      });

      return data;
    } catch (error) {
      console.error('Error updating escalation rule:', error);
      throw error;
    }
  }

  // Delete a rule
  async deleteRule(id: string, userId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('escalation_rules')
        .delete()
        .eq('id', id);

      if (error) throw error;

      await this.auditService.logActivity({
        userId,
        action: 'delete_escalation_rule',
        resourceType: 'escalation_rule',
        resourceId: id,
      });
    } catch (error) {
      console.error('Error deleting escalation rule:', error);
      throw error;
    }
  }

  // Get all rules
  async getRules(filters?: {
    isActive?: boolean;
    triggerType?: string;
    priority?: string;
    categoryId?: string;
    departmentId?: string;
  }): Promise<EscalationRule[]> {
    try {
      let query = this.supabase.from('escalation_rules').select('*');

      if (filters?.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }
      if (filters?.triggerType) {
        query = query.eq('trigger_type', filters.triggerType);
      }
      if (filters?.priority) {
        query = query.eq('priority', filters.priority);
      }
      if (filters?.categoryId) {
        query = query.contains('applicable_to_categories', [filters.categoryId]);
      }
      if (filters?.departmentId) {
        query = query.contains('applicable_to_departments', [filters.departmentId]);
      }

      const { data, error } = await query.order('priority', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching escalation rules:', error);
      throw error;
    }
  }

  // Get a single rule
  async getRule(id: string): Promise<EscalationRule | null> {
    try {
      const { data, error } = await this.supabase
        .from('escalation_rules')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching escalation rule:', error);
      throw error;
    }
  }

  // Toggle rule active status
  async toggleRuleStatus(id: string, userId: string): Promise<EscalationRule> {
    try {
      // Get current status
      const { data: rule } = await this.supabase
        .from('escalation_rules')
        .select('is_active')
        .eq('id', id)
        .single();

      if (!rule) throw new Error('Rule not found');

      // Update status
      const { data, error } = await this.supabase
        .from('escalation_rules')
        .update({
          is_active: !rule.is_active,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      await this.auditService.logActivity({
        userId,
        action: 'toggle_escalation_rule',
        resourceType: 'escalation_rule',
        resourceId: id,
        details: { isActive: data.is_active },
      });

      return data;
    } catch (error) {
      console.error('Error toggling rule status:', error);
      throw error;
    }
  }

  // Create default escalation rules
  async createDefaultRules(userId: string): Promise<void> {
    const defaultRules: EscalationRule[] = [
      {
        name: 'SLA Breach Critical Priority',
        description: 'Escalate when critical priority requests breach SLA',
        triggerType: 'sla_breach',
        conditions: [
          { field: 'priority', operator: 'equals', value: 'critical' },
          { field: 'status', operator: 'equals', value: 'breached' },
        ],
        actions: [
          {
            type: 'notify',
            target: 'role:IT Helpdesk Support',
            parameters: { message: 'Critical SLA breach requires immediate attention' },
          },
          {
            type: 'change_priority',
            parameters: { priority: 'critical' },
          },
          {
            type: 'notify',
            target: 'manager',
            parameters: { message: 'Critical request has breached SLA' },
          },
        ],
        priority: 'critical',
        isActive: true,
        retryInterval: 15,
        maxRetries: 3,
        createdBy: userId,
      },
      {
        name: 'Overdue High Priority Requests',
        description: 'Escalate high priority requests overdue by 24 hours',
        triggerType: 'overdue',
        conditions: [
          { field: 'priority', operator: 'equals', value: 'high' },
          { field: 'age_hours', operator: 'greater_than', value: 24 },
        ],
        actions: [
          {
            type: 'reassign',
            target: 'role:IT Helpdesk Support',
          },
          {
            type: 'notify',
            target: 'manager',
            parameters: { message: 'High priority request overdue by 24+ hours' },
          },
        ],
        priority: 'high',
        isActive: true,
        retryInterval: 30,
        maxRetries: 2,
        createdBy: userId,
      },
      {
        name: 'Approval Timeout Auto-Escalate',
        description: 'Escalate to next level when approval times out',
        triggerType: 'approval_timeout',
        conditions: [],
        actions: [
          {
            type: 'add_approver',
            target: 'manager',
          },
          {
            type: 'notify',
            target: 'manager',
            parameters: { message: 'Approval request has timed out and requires your attention' },
          },
        ],
        priority: 'medium',
        isActive: true,
        createdBy: userId,
      },
      {
        name: 'Password Reset Urgent',
        description: 'Escalate password reset requests older than 2 hours',
        triggerType: 'overdue',
        conditions: [
          { field: 'service_category', operator: 'contains', value: 'password' },
          { field: 'age_hours', operator: 'greater_than', value: 2 },
        ],
        actions: [
          {
            type: 'change_priority',
            parameters: { priority: 'high' },
          },
          {
            type: 'notify',
            target: 'role:IT Helpdesk Support',
            parameters: { message: 'Password reset request requires urgent attention' },
          },
        ],
        priority: 'high',
        isActive: true,
        applicableToCategories: ['PWR', 'O365'],
        createdBy: userId,
      },
    ];

    for (const rule of defaultRules) {
      await this.createRule(rule);
    }
  }

  // Validate a rule
  validateRule(rule: EscalationRule): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!rule.name || rule.name.trim().length === 0) {
      errors.push('Rule name is required');
    }

    if (!rule.triggerType) {
      errors.push('Trigger type is required');
    }

    if (!rule.actions || rule.actions.length === 0) {
      errors.push('At least one action is required');
    }

    // Validate conditions
    for (const condition of rule.conditions || []) {
      if (!condition.field || !condition.operator) {
        errors.push('Invalid condition: field and operator are required');
      }
    }

    // Validate actions
    for (const action of rule.actions || []) {
      if (!action.type) {
        errors.push('Invalid action: type is required');
      }
      if (['notify', 'reassign', 'add_approver'].includes(action.type) && !action.target) {
        errors.push(`Invalid action: target is required for ${action.type}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
