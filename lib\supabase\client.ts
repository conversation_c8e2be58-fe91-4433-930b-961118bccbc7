/**
 * Supabase Client Configuration
 * Client-side Supabase instance for browser usage
 */

import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const createClient = () =>
  createBrowserClient(supabaseUrl, supabaseAnonKey)

// Default client instance
export const supabase = createClient()
