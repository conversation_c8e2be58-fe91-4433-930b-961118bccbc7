"use strict";(()=>{var e={};e.id=6891,e.ids=[6891],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},29888:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>w,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var r={};a.r(r),a.d(r,{GET:()=>d});var o=a(42706),s=a(28203),n=a(45994),i=a(39187),c=a(73865),l=a(44512);async function d(e){try{let{searchParams:t}=new URL(e.url),a=t.get("timeRange")||"7d",r=await (0,l.UL)(),o=(0,c.createServerSupabase)(r),s=new Date,n=new Date;switch(a){case"24h":n.setDate(s.getDate()-1);break;case"7d":default:n.setDate(s.getDate()-7);break;case"30d":n.setDate(s.getDate()-30);break;case"90d":n.setDate(s.getDate()-90)}let{count:d}=await o.from("workflow_instances").select("*",{count:"exact",head:!0}).gte("created_at",n.toISOString()),{count:u}=await o.from("workflow_instances").select("*",{count:"exact",head:!0}).in("status",["pending","in_progress","pending_approval"]).gte("created_at",n.toISOString()),{count:p}=await o.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","completed").gte("created_at",n.toISOString()),{count:g}=await o.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","failed").gte("created_at",n.toISOString()),{count:w}=await o.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","on_hold").gte("created_at",n.toISOString()),{count:m}=await o.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","pending_approval"),{data:h}=await o.from("sla_tracking").select("*").eq("sla_status","breached").gte("created_at",n.toISOString()),f=h?.length||0,{data:_}=await o.from("workflow_instances").select("created_at, completed_at").eq("status","completed").gte("created_at",n.toISOString()).not("completed_at","is",null),k=0;if(_&&_.length>0){let e=_.reduce((e,t)=>{let a=new Date(t.created_at).getTime(),r=new Date(t.completed_at).getTime();return e+(r-a)},0);k=Math.round(e/_.length/36e5)}let{data:x}=await o.from("sla_tracking").select("sla_status").gte("created_at",n.toISOString()),S=100;if(x&&x.length>0){let e=x.filter(e=>"breached"!==e.sla_status).length;S=Math.round(e/x.length*100)}let v=Math.round((p||0)/Math.max(1,Math.ceil((s.getTime()-n.getTime())/864e5))),q=Math.round(7*v),D=Math.round(30*v),y={total:d||0,active:u||0,completed:p||0,failed:g||0,onHold:w||0,pendingApproval:m||0,overdue:f,avgCompletionTime:k,slaCompliance:S,throughput:{daily:v,weekly:q,monthly:D}};return i.NextResponse.json(y)}catch(e){return console.error("Error fetching workflow metrics:",e),i.NextResponse.json({error:"Failed to fetch workflow metrics"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/metrics/route",pathname:"/api/workflows/monitoring/metrics",filename:"route",bundlePath:"app/api/workflows/monitoring/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\metrics\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:w}=u;function m(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8096,2076],()=>a(29888));module.exports=r})();