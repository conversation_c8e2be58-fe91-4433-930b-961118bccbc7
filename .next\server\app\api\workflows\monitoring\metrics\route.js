(()=>{var e={};e.id=6891,e.ids=[6891],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3896:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var r={};a.r(r),a.d(r,{GET:()=>l});var s=a(42706),o=a(28203),n=a(45994),i=a(39187),c=a(73865),u=a(44512);async function l(e){try{let{searchParams:t}=new URL(e.url),a=t.get("timeRange")||"7d",r=await (0,u.UL)(),s=(0,c.createServerSupabase)(r),o=new Date,n=new Date;switch(a){case"24h":n.setDate(o.getDate()-1);break;case"7d":default:n.setDate(o.getDate()-7);break;case"30d":n.setDate(o.getDate()-30);break;case"90d":n.setDate(o.getDate()-90)}let{count:l}=await s.from("workflow_instances").select("*",{count:"exact",head:!0}).gte("created_at",n.toISOString()),{count:d}=await s.from("workflow_instances").select("*",{count:"exact",head:!0}).in("status",["pending","in_progress","pending_approval"]).gte("created_at",n.toISOString()),{count:p}=await s.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","completed").gte("created_at",n.toISOString()),{count:g}=await s.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","failed").gte("created_at",n.toISOString()),{count:w}=await s.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","on_hold").gte("created_at",n.toISOString()),{count:m}=await s.from("workflow_instances").select("*",{count:"exact",head:!0}).eq("status","pending_approval"),{data:h}=await s.from("sla_tracking").select("*").eq("sla_status","breached").gte("created_at",n.toISOString()),f=h?.length||0,{data:_}=await s.from("workflow_instances").select("created_at, completed_at").eq("status","completed").gte("created_at",n.toISOString()).not("completed_at","is",null),k=0;if(_&&_.length>0){let e=_.reduce((e,t)=>{let a=new Date(t.created_at).getTime(),r=new Date(t.completed_at).getTime();return e+(r-a)},0);k=Math.round(e/_.length/36e5)}let{data:x}=await s.from("sla_tracking").select("sla_status").gte("created_at",n.toISOString()),S=100;if(x&&x.length>0){let e=x.filter(e=>"breached"!==e.sla_status).length;S=Math.round(e/x.length*100)}let v=Math.round((p||0)/Math.max(1,Math.ceil((o.getTime()-n.getTime())/864e5))),y=Math.round(7*v),q=Math.round(30*v),D={total:l||0,active:d||0,completed:p||0,failed:g||0,onHold:w||0,pendingApproval:m||0,overdue:f,avgCompletionTime:k,slaCompliance:S,throughput:{daily:v,weekly:y,monthly:q}};return i.NextResponse.json(D)}catch(e){return console.error("Error fetching workflow metrics:",e),i.NextResponse.json({error:"Failed to fetch workflow metrics"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/metrics/route",pathname:"/api/workflows/monitoring/metrics",filename:"route",bundlePath:"app/api/workflows/monitoring/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\metrics\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:w}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},73865:(e,t,a)=>{"use strict";a.d(t,{N:()=>r});let r=(0,a(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[5994,5452,4512],()=>a(3896));module.exports=r})();