'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { HistoricalSuggestionsField } from '@/components/forms/historical-suggestions-field'
import { historicalDataService } from '@/lib/historical-data-service'
import { toast } from 'sonner'
import { History, TrendingUp, Brain, Database } from 'lucide-react'

export default function HistoricalAnalysisTestPage() {
  const [formData, setFormData] = useState({
    email: '',
    pc_id: '',
    reason: '',
    department: ''
  })

  const [stats, setStats] = useState<any>(null)

  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    // Simulate form submission recording
    const submissionId = await historicalDataService.recordFormSubmission(
      'test-user-id',
      'it-systems-dept-id',
      'pc-admin-request',
      formData
    )

    if (submissionId) {
      toast.success('データが記録されました / Data recorded successfully')
      
      // Load updated statistics
      const emailStats = await historicalDataService.getFieldStatistics(
        'it-systems-dept-id',
        'email'
      )
      setStats(emailStats)
    } else {
      toast.error('記録に失敗しました / Failed to record data')
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <History className="h-8 w-8 text-purple-600" />
            履歴データ分析デモ
          </h1>
          <p className="text-muted-foreground">
            Historical Data Analysis & Intelligent Suggestions
          </p>
        </div>

        {/* Feature Badges */}
        <div className="flex flex-wrap gap-2 justify-center">
          <Badge variant="secondary">
            <TrendingUp className="h-3 w-3 mr-1" />
            パターン分析 / Pattern Analysis
          </Badge>
          <Badge variant="secondary">
            <Brain className="h-3 w-3 mr-1" />
            AI学習 / AI Learning
          </Badge>
          <Badge variant="secondary">
            <Database className="h-3 w-3 mr-1" />
            履歴データ / Historical Data
          </Badge>
          <Badge variant="secondary">
            部門別提案 / Department-specific
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Form Section */}
          <Card>
            <CardHeader>
              <CardTitle>フォーム入力 / Form Input</CardTitle>
              <CardDescription>
                入力すると履歴データに基づいた提案が表示されます
                <br />
                Suggestions based on historical data will appear as you type
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <HistoricalSuggestionsField
                fieldId="email"
                label="Email Address"
                labelJp="メールアドレス"
                value={formData.email}
                onChange={(value) => handleFieldChange('email', value)}
                departmentId="it-systems-dept-id"
                placeholder="例: <EMAIL>"
                required
              />

              <HistoricalSuggestionsField
                fieldId="pc_id"
                label="PC ID"
                labelJp="PC ID"
                value={formData.pc_id}
                onChange={(value) => handleFieldChange('pc_id', value)}
                departmentId="it-systems-dept-id"
                placeholder="例: **********"
                required
              />

              <HistoricalSuggestionsField
                fieldId="reason"
                label="Reason for Request"
                labelJp="申請理由"
                value={formData.reason}
                onChange={(value) => handleFieldChange('reason', value)}
                departmentId="it-systems-dept-id"
                placeholder="申請理由を入力してください"
                required
              />

              <HistoricalSuggestionsField
                fieldId="department"
                label="Department"
                labelJp="部署"
                value={formData.department}
                onChange={(value) => handleFieldChange('department', value)}
                departmentId="it-systems-dept-id"
                placeholder="例: ITシステム部"
                required
              />

              <Button onClick={handleSubmit} className="w-full">
                送信してデータを記録 / Submit and Record Data
              </Button>
            </CardContent>
          </Card>

          {/* Information Section */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>機能説明 / Feature Explanation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">履歴パターン分析 / Historical Pattern Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    部門内で頻繁に使用される値を分析し、入力候補として表示します。使用頻度と信頼度スコアも表示されます。
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Analyzes frequently used values within the department and displays them as input suggestions with frequency and confidence scores.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">AI提案 / AI Suggestions</h4>
                  <p className="text-sm text-muted-foreground">
                    履歴データとコンテキストを考慮して、AIが最適な値を提案します。
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    AI suggests optimal values based on historical data and context.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">学習機能 / Learning Feature</h4>
                  <p className="text-sm text-muted-foreground">
                    提案の採用/却下がフィードバックとして記録され、今後の提案精度が向上します。
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Acceptance/rejection of suggestions is recorded as feedback to improve future suggestion accuracy.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Statistics Display */}
            {stats && (
              <Card>
                <CardHeader>
                  <CardTitle>フィールド統計 / Field Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs overflow-auto bg-gray-100 dark:bg-gray-900 p-3 rounded">
                    {JSON.stringify(stats, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Form Data Display */}
            <Card>
              <CardHeader>
                <CardTitle>現在のフォームデータ / Current Form Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs overflow-auto bg-gray-100 dark:bg-gray-900 p-3 rounded">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
