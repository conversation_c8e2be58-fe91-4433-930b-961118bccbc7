-- Workflow Engine Tables Migration
-- Date: 2025-05-24
-- Description: Create tables for advanced workflow automation system

-- Workflow definitions table
CREATE TABLE IF NOT EXISTS workflow_definitions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  version INTEGER NOT NULL DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  workflow_json JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name, version)
);

-- Workflow instances table
CREATE TABLE IF NOT EXISTS workflow_instances (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_definition_id UUID REFERENCES workflow_definitions(id),
  request_id UUID REFERENCES request_forms(id),
  current_state VARCHAR(100) NOT NULL,
  status VARCHAR(50) NOT NULL CHECK (status IN ('active', 'completed', 'cancelled', 'suspended')),
  context_data JSONB DEFAULT '{}',
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES staff(id),
  CONSTRAINT valid_completed_at CHECK (
    (status != 'completed' AND completed_at IS NULL) OR 
    (status = 'completed' AND completed_at IS NOT NULL)
  )
);

-- Workflow tasks table
CREATE TABLE IF NOT EXISTS workflow_tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_instance_id UUID REFERENCES workflow_instances(id) ON DELETE CASCADE,
  task_type VARCHAR(100) NOT NULL,
  task_name VARCHAR(255) NOT NULL,
  assigned_to UUID REFERENCES staff(id),
  assigned_role VARCHAR(100),
  status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled', 'escalated')),
  due_date TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  completed_by UUID REFERENCES staff(id),
  task_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT assignment_check CHECK (
    (assigned_to IS NOT NULL AND assigned_role IS NULL) OR 
    (assigned_to IS NULL AND assigned_role IS NOT NULL) OR
    (assigned_to IS NULL AND assigned_role IS NULL)
  )
);

-- Business rules table
CREATE TABLE IF NOT EXISTS business_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('routing', 'approval', 'escalation', 'assignment')),
  conditions JSONB NOT NULL,
  actions JSONB NOT NULL,
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SLA definitions table
CREATE TABLE IF NOT EXISTS sla_definitions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  service_category_id UUID REFERENCES service_categories(id),
  priority VARCHAR(20) CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  response_time_minutes INTEGER NOT NULL,
  resolution_time_minutes INTEGER NOT NULL,
  business_hours_only BOOLEAN DEFAULT TRUE,
  escalation_time_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SLA tracking table
CREATE TABLE IF NOT EXISTS sla_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_instance_id UUID REFERENCES workflow_instances(id) ON DELETE CASCADE,
  sla_definition_id UUID REFERENCES sla_definitions(id),
  target_response_date TIMESTAMP WITH TIME ZONE,
  actual_response_date TIMESTAMP WITH TIME ZONE,
  target_resolution_date TIMESTAMP WITH TIME ZONE,
  actual_resolution_date TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) CHECK (status IN ('on_track', 'at_risk', 'breached')),
  breach_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow transitions log
CREATE TABLE IF NOT EXISTS workflow_transitions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_instance_id UUID REFERENCES workflow_instances(id) ON DELETE CASCADE,
  from_state VARCHAR(100),
  to_state VARCHAR(100) NOT NULL,
  transition_reason TEXT,
  performed_by UUID REFERENCES staff(id),
  transition_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escalation log
CREATE TABLE IF NOT EXISTS escalation_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_task_id UUID REFERENCES workflow_tasks(id) ON DELETE CASCADE,
  escalated_from UUID REFERENCES staff(id),
  escalated_to UUID REFERENCES staff(id),
  escalation_reason TEXT NOT NULL,
  escalation_level INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX idx_workflow_instances_request_id ON workflow_instances(request_id);
CREATE INDEX idx_workflow_tasks_status ON workflow_tasks(status);
CREATE INDEX idx_workflow_tasks_assigned_to ON workflow_tasks(assigned_to);
CREATE INDEX idx_workflow_tasks_due_date ON workflow_tasks(due_date);
CREATE INDEX idx_sla_tracking_status ON sla_tracking(status);
CREATE INDEX idx_business_rules_type ON business_rules(rule_type, is_active);

-- Create update timestamp triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_workflow_definitions_updated_at BEFORE UPDATE ON workflow_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_tasks_updated_at BEFORE UPDATE ON workflow_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_rules_updated_at BEFORE UPDATE ON business_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sla_definitions_updated_at BEFORE UPDATE ON sla_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sla_tracking_updated_at BEFORE UPDATE ON sla_tracking
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default SLA definitions
INSERT INTO sla_definitions (name, service_category_id, priority, response_time_minutes, resolution_time_minutes, business_hours_only, escalation_time_minutes)
SELECT 
  'Standard SLA - ' || sc.name_en,
  sc.id,
  'medium',
  120, -- 2 hours response
  480, -- 8 hours resolution
  true,
  240  -- 4 hours escalation
FROM service_categories sc
WHERE sc.is_active = true;

-- Insert high priority SLA for critical services
INSERT INTO sla_definitions (name, service_category_id, priority, response_time_minutes, resolution_time_minutes, business_hours_only, escalation_time_minutes)
SELECT 
  'Critical SLA - ' || sc.name_en,
  sc.id,
  'critical',
  30,  -- 30 minutes response
  120, -- 2 hours resolution
  false,
  60   -- 1 hour escalation
FROM service_categories sc
WHERE sc.code IN ('PWR', 'PCA', 'MXP'); -- Password reset, PC Admin, MXP are critical

-- Add comments for documentation
COMMENT ON TABLE workflow_definitions IS 'Stores workflow templates and their JSON definitions';
COMMENT ON TABLE workflow_instances IS 'Tracks active workflow executions';
COMMENT ON TABLE workflow_tasks IS 'Individual tasks within workflow instances';
COMMENT ON TABLE business_rules IS 'Configurable business rules for routing and approvals';
COMMENT ON TABLE sla_definitions IS 'Service Level Agreement definitions per service category';
COMMENT ON TABLE sla_tracking IS 'Tracks SLA compliance for workflow instances';
COMMENT ON TABLE workflow_transitions IS 'Audit log of workflow state changes';
COMMENT ON TABLE escalation_log IS 'Records of task escalations';
