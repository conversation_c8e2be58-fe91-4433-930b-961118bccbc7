# ITSync Environment Configuration Template
# Copy this file to .env.local and fill in your actual values
# NEVER commit .env.local to version control

# =============================================================================
# SUPABASE CONFIGURATION (Required)
# =============================================================================
# Get these from your Supabase project settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=ITSync
NODE_ENV=development

# =============================================================================
# AI API CONFIGURATION (Required for AI features)
# =============================================================================
# OpenAI API Key - Get from https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your_openai_key_here

# Anthropic API Key - Get from https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-api03-your_anthropic_key_here

# Optional: Custom API endpoints (use defaults if not specified)
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_BASE_URL=https://api.anthropic.com/v1

# Default AI Provider (openai or anthropic)
DEFAULT_AI_PROVIDER=openai

# AI Model Configuration
OPENAI_MODEL=gpt-4-turbo-preview
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Encryption key for sensitive data (generate a strong random key)
ENCRYPTION_KEY=your_32_character_encryption_key_here

# JWT Secret for session management (generate a strong random secret)
JWT_SECRET=your_jwt_secret_here

# Rate limiting configuration
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# EXTERNAL INTEGRATIONS (Optional)
# =============================================================================
# SharePoint Integration
SHAREPOINT_CLIENT_ID=your_sharepoint_client_id
SHAREPOINT_CLIENT_SECRET=your_sharepoint_client_secret
SHAREPOINT_TENANT_ID=your_tenant_id

# Exchange Online Integration
EXCHANGE_CLIENT_ID=your_exchange_client_id
EXCHANGE_CLIENT_SECRET=your_exchange_client_secret

# =============================================================================
# MONITORING & LOGGING (Production)
# =============================================================================
# Sentry for error tracking
SENTRY_DSN=your_sentry_dsn_here

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# =============================================================================
# REDIS CONFIGURATION (Optional - for caching)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# =============================================================================
# EMAIL CONFIGURATION (For notifications)
# =============================================================================
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Enable debug mode (development only)
DEBUG=false

# Skip SSL verification (development only - NEVER use in production)
NODE_TLS_REJECT_UNAUTHORIZED=1
