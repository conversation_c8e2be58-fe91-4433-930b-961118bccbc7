"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  FolderOpen, 
  Users, 
  Search,
  UserPlus,
  UserMinus,
  Loader2,
  Shield,
  FileText,
  Eye,
  Edit,
  Settings
} from 'lucide-react';
import { createClient } from '@/lib/supabase';
import { useAuth } from '@/lib/auth-context';
import { UserSearchInput } from '@/components/forms/user-search-input';
import { toast } from 'sonner';
import { useDepartmentFilter } from '@/lib/use-department-filter';

interface SharePointLibrary {
  id: string;
  name_jp: string;
  name_en: string;
  description?: string;
  division_id?: string;
  created_at: string;
  updated_at: string;
}

interface SharePointAccess {
  id: string;
  library_id: string;
  staff_id: string;
  access_level: 'read' | 'contribute' | 'full';
  created_at: string;
  staff?: {
    id: string;
    name_jp: string;
    name_en: string;
    email: string;
    staff_id: string;
  };
}

const accessLevelLabels = {
  read: { label: '読み取り', icon: Eye, color: 'bg-blue-100 text-blue-700' },
  contribute: { label: '投稿', icon: Edit, color: 'bg-green-100 text-green-700' },
  full: { label: 'フルコントロール', icon: Shield, color: 'bg-purple-100 text-purple-700' }
};
export default function SharePointManagementPage() {
  const { user, staff } = useAuth();
  const { filterByDepartment } = useDepartmentFilter();
  const [libraries, setLibraries] = useState<SharePointLibrary[]>([]);
  const [selectedLibrary, setSelectedLibrary] = useState<SharePointLibrary | null>(null);
  const [libraryAccess, setLibraryAccess] = useState<SharePointAccess[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]);
  const [selectedAccessLevel, setSelectedAccessLevel] = useState<'read' | 'contribute' | 'full'>('read');
  const supabase = createClient();

  useEffect(() => {
    loadLibraries();
  }, []);

  useEffect(() => {
    if (selectedLibrary) {
      loadLibraryAccess(selectedLibrary.id);
    }
  }, [selectedLibrary]);

  const loadLibraries = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('sharepoint_libraries')
        .select('*')
        .order('name_jp');

      // Apply department filter if not admin
      if (staff && !['Global Administrator', 'Web App System Administrator'].includes(staff.role?.name || '')) {
        query = filterByDepartment(query, 'division_id');
      }

      const { data, error } = await query;

      if (error) {
        toast.error('SharePointライブラリの読み込みに失敗しました');
        return;
      }

      setLibraries(data || []);
    } finally {
      setIsLoading(false);
    }
  };

  const loadLibraryAccess = async (libraryId: string) => {
    try {
      const { data, error } = await supabase
        .from('sharepoint_access')
        .select(`
          *,
          staff:staff_id (
            id,
            name_jp,
            name_en,
            email,
            staff_id
          )
        `)
        .eq('library_id', libraryId)
        .order('created_at', { ascending: false });

      if (error) {
        toast.error('アクセス権限の読み込みに失敗しました');
        return;
      }

      setLibraryAccess(data || []);
    } catch (error) {
      console.error('Error loading access:', error);
    }
  };