"use strict";(()=>{var e={};e.id=4584,e.ids=[4584],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{e.exports=require("crypto")},87938:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>u,runtime:()=>p});var a=t(42706),n=t(28203),o=t(45994),i=t(39187),d=t(75122);let p="nodejs";async function u(e){try{let{articleId:r,language:t,limit:s}=await e.json();if(!r||!t)return i.NextResponse.json({error:"Article ID and language are required"},{status:400});let a=await d.K.findRelatedArticles(r,t,s);return i.NextResponse.json({success:!0,related:a,count:a.length})}catch(e){return console.error("Related articles error:",e),i.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Failed to find related articles"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/embeddings/related/route",pathname:"/api/embeddings/related",filename:"route",bundlePath:"app/api/embeddings/related/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\related\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:g}=l;function m(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(87938));module.exports=s})();