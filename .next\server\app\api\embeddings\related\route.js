(()=>{var e={};e.id=4584,e.ids=[4584],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},66804:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>p,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{POST:()=>l,runtime:()=>c});var n=r(42706),s=r(28203),i=r(45994),o=r(39187),d=r(75122);let c="nodejs";async function l(e){try{let{articleId:t,language:r,limit:a}=await e.json();if(!t||!r)return o.NextResponse.json({error:"Article ID and language are required"},{status:400});let n=await d.K.findRelatedArticles(t,r,a);return o.NextResponse.json({success:!0,related:n,count:n.length})}catch(e){return console.error("Related articles error:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Failed to find related articles"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/embeddings/related/route",pathname:"/api/embeddings/related",filename:"route",bundlePath:"app/api/embeddings/related/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\related\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:u,workUnitAsyncStorage:m,serverHooks:h}=g;function p(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},75122:(e,t,r)=>{"use strict";r.d(t,{K:()=>o});var a=r(73865),n=r(55511);let s=process.env.OPENAI_API_KEY;class i{constructor(){}static getInstance(){return i.instance||(i.instance=new i),i.instance}async generateEmbedding(e){if(!s)throw Error("OpenAI API key is not configured");let t=this.preprocessText(e);try{let e=await fetch("https://api.openai.com/v1/embeddings",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({model:"text-embedding-ada-002",input:t})});if(!e.ok){let t=await e.json();throw Error(`OpenAI API error: ${t.error?.message||"Unknown error"}`)}let r=await e.json();return{embedding:r.data[0].embedding,model:r.model,usage:r.usage}}catch(e){throw console.error("Error generating embedding:",e),e}}async generateArticleEmbeddings(e,t,r){try{let[a,n]=await Promise.all([this.generateEmbedding(t),this.generateEmbedding(r)]),s=this.calculateContentHash(t),i=this.calculateContentHash(r);await Promise.all([this.storeEmbedding({article_id:e,language:"en",embedding:a.embedding,content_hash:s,model_version:a.model}),this.storeEmbedding({article_id:e,language:"jp",embedding:n.embedding,content_hash:i,model_version:n.model})])}catch(e){throw console.error("Error generating article embeddings:",e),e}}async storeEmbedding(e){let{error:t}=await a.N.from("kb_embeddings").upsert({article_id:e.article_id,language:e.language,embedding:`[${e.embedding.join(",")}]`,content_hash:e.content_hash,model_version:e.model_version,updated_at:new Date().toISOString()},{onConflict:"article_id,language"});if(t)throw console.error("Error storing embedding:",t),Error(`Failed to store embedding: ${t.message}`)}async semanticSearch(e,t,r={}){let{matchThreshold:n=.7,matchCount:s=10}=r;try{let r=await this.generateEmbedding(e),{data:i,error:o}=await a.N.rpc("kb_semantic_search",{query_embedding:`[${r.embedding.join(",")}]`,search_language:t,match_threshold:n,match_count:s});if(o)throw console.error("Semantic search error:",o),Error(`Search failed: ${o.message}`);return i||[]}catch(e){throw console.error("Error performing semantic search:",e),e}}async updateStaleEmbeddings(){try{let{data:e,error:t}=await a.N.from("kb_articles").select(`
          id,
          content_en,
          content_jp,
          kb_embeddings!inner(
            article_id,
            language,
            content_hash
          )
        `).eq("status","published");if(t)throw Error(`Failed to fetch articles: ${t.message}`);if(!e||0===e.length){console.log("No articles to update");return}let r=[];for(let t of e){let e=this.calculateContentHash(t.content_en),a=this.calculateContentHash(t.content_jp),n=t.kb_embeddings.find(e=>"en"===e.language),s=t.kb_embeddings.find(e=>"jp"===e.language);n&&n.content_hash===e||r.push(this.generateAndStoreEmbedding(t.id,t.content_en,"en")),s&&s.content_hash===a||r.push(this.generateAndStoreEmbedding(t.id,t.content_jp,"jp"))}r.length>0?(await Promise.all(r),console.log(`Updated ${r.length} embeddings`)):console.log("All embeddings are up to date")}catch(e){throw console.error("Error updating stale embeddings:",e),e}}async generateAndStoreEmbedding(e,t,r){let a=await this.generateEmbedding(t),n=this.calculateContentHash(t);await this.storeEmbedding({article_id:e,language:r,embedding:a.embedding,content_hash:n,model_version:a.model})}preprocessText(e){let t=e.replace(/\s+/g," ").trim();return(t=t.replace(/[\u0000-\u001F\u007F-\u009F]/g,"")).length>3e4&&(t=t.substring(0,3e4)+"..."),t}calculateContentHash(e){return(0,n.createHash)("sha256").update(e).digest("hex")}async findRelatedArticles(e,t,r=5){try{let{data:n,error:s}=await a.N.from("kb_embeddings").select("embedding").eq("article_id",e).eq("language",t).single();if(s||!n)throw Error("Article embedding not found");let{data:i,error:o}=await a.N.rpc("kb_semantic_search",{query_embedding:n.embedding,search_language:t,match_threshold:.5,match_count:r+1});if(o)throw Error(`Related articles search failed: ${o.message}`);let d=(i||[]).filter(t=>t.article_id!==e);if(d.length>0){let t=d.map(t=>({article_id:e,related_article_id:t.article_id,relevance_score:t.similarity,relation_type:"similar"}));await a.N.from("kb_related_articles").upsert(t,{onConflict:"article_id,related_article_id"})}return d.slice(0,r)}catch(e){throw console.error("Error finding related articles:",e),e}}}let o=i.getInstance()},73865:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});let a=(0,r(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5994,5452],()=>r(66804));module.exports=a})();