import { Redis } from '@upstash/redis'

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
})

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  tags?: string[] // Cache tags for invalidation
}

export class CacheService {
  private defaultTTL = 300 // 5 minutes

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await redis.get(key)
      return data as T
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  /**
   * Set value in cache
   */
  async set<T>(
    key: string,
    value: T,
    options: CacheOptions = {}
  ): Promise<void> {
    try {
      const { ttl = this.defaultTTL, tags = [] } = options
      
      // Set the main key
      await redis.setex(key, ttl, JSON.stringify(value))
      
      // Add to tag sets
      if (tags.length > 0) {
        const pipeline = redis.pipeline()
        for (const tag of tags) {
          pipeline.sadd(`tag:${tag}`, key)
          pipeline.expire(`tag:${tag}`, ttl)
        }
        await pipeline.exec()
      }
    } catch (error) {
      console.error('Cache set error:', error)
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<void> {
    try {
      await redis.del(key)
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  }

  /**
   * Clear cache by tag
   */
  async clearByTag(tag: string): Promise<void> {
    try {
      const keys = await redis.smembers(`tag:${tag}`)
      if (keys.length > 0) {
        const pipeline = redis.pipeline()
        for (const key of keys) {
          pipeline.del(key)
        }
        pipeline.del(`tag:${tag}`)
        await pipeline.exec()
      }
    } catch (error) {
      console.error('Cache clear by tag error:', error)
    }
  }

  /**
   * Clear all cache
   */
  async clearAll(): Promise<void> {
    try {
      await redis.flushdb()
    } catch (error) {
      console.error('Cache clear all error:', error)
    }
  }

  /**
   * Cache wrapper for functions
   */
  async cached<T>(
    key: string,
    fn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached
    }

    // Execute function and cache result
    const result = await fn()
    await this.set(key, result, options)
    return result
  }

  /**
   * Invalidate cache patterns
   */
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      // Note: Pattern matching is limited in Upstash Redis
      // For production, consider using tags or structured keys
      console.warn('Pattern invalidation not fully supported in Upstash Redis')
    } catch (error) {
      console.error('Cache invalidate pattern error:', error)
    }
  }

  /**
   * Get multiple values
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await redis.mget(...keys)
      return values.map(v => v as T | null)
    } catch (error) {
      console.error('Cache mget error:', error)
      return keys.map(() => null)
    }
  }

  /**
   * Set multiple values
   */
  async mset(
    items: { key: string; value: any; ttl?: number }[]
  ): Promise<void> {
    try {
      const pipeline = redis.pipeline()
      for (const item of items) {
        const ttl = item.ttl || this.defaultTTL
        pipeline.setex(item.key, ttl, JSON.stringify(item.value))
      }
      await pipeline.exec()
    } catch (error) {
      console.error('Cache mset error:', error)
    }
  }

  /**
   * Increment counter
   */
  async increment(
    key: string,
    amount = 1,
    ttl?: number
  ): Promise<number> {
    try {
      const result = await redis.incrby(key, amount)
      if (ttl) {
        await redis.expire(key, ttl)
      }
      return result
    } catch (error) {
      console.error('Cache increment error:', error)
      return 0
    }
  }

  /**
   * Rate limiting
   */
  async checkRateLimit(
    key: string,
    limit: number,
    window: number // in seconds
  ): Promise<{ allowed: boolean; remaining: number; resetAt: number }> {
    const count = await this.increment(`rate:${key}`, 1, window)
    const remaining = Math.max(0, limit - count)
    const resetAt = Date.now() + window * 1000

    return {
      allowed: count <= limit,
      remaining,
      resetAt
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService()

// Cache key helpers
export const cacheKeys = {
  // User cache keys
  user: (id: string) => `user:${id}`,
  userByEmail: (email: string) => `user:email:${email}`,
  userPermissions: (id: string) => `user:${id}:permissions`,
  
  // Request cache keys
  request: (id: string) => `request:${id}`,
  requestList: (filters: any) => `requests:${JSON.stringify(filters)}`,
  requestStats: (departmentId: string) => `stats:requests:${departmentId}`,
  
  // Workflow cache keys
  workflow: (id: string) => `workflow:${id}`,
  workflowActive: () => 'workflows:active',
  workflowStats: () => 'workflows:stats',
  
  // Department cache keys
  department: (id: string) => `dept:${id}`,
  departmentList: () => 'departments:all',
  departmentStats: () => 'departments:stats',
  
  // Service category cache keys
  serviceCategory: (id: string) => `category:${id}`,
  serviceCategoryList: () => 'categories:all',
  
  // Session cache keys
  session: (id: string) => `session:${id}`,
  
  // Form schema cache keys
  formSchema: (type: string) => `schema:${type}`,
  
  // Knowledge base cache keys
  kbArticle: (id: string) => `kb:article:${id}`,
  kbSearch: (query: string) => `kb:search:${query}`,
}

// Cache tags for invalidation
export const cacheTags = {
  users: 'users',
  requests: 'requests',
  workflows: 'workflows',
  departments: 'departments',
  categories: 'categories',
  knowledge: 'knowledge',
  stats: 'stats',
}
