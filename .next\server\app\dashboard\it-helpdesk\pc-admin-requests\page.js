(()=>{var e={};e.id=4739,e.ids=[4739],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},1538:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>m,tree:()=>p});var t=s(70260),n=s(28203),o=s(25155),a=s.n(o),i=s(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let p=["",{children:["dashboard",{children:["it-helpdesk",{children:["pc-admin-requests",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.t.bind(s,62181,23)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\pc-admin-requests\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],u=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\pc-admin-requests\\page.tsx"],l={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/it-helpdesk/pc-admin-requests/page",pathname:"/dashboard/it-helpdesk/pc-admin-requests",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},62181:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected unicode escape\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\pc-admin-requests\\page.tsx\x1b[0m:68:1]\n \x1b[2m65\x1b[0m │       \n \x1b[2m66\x1b[0m │       let query = supabase\n \x1b[2m67\x1b[0m │         .from('request_items')\n \x1b[2m68\x1b[0m │         .select(\\`\n    \xb7 \x1b[35;1m                ▲\x1b[0m\n \x1b[2m69\x1b[0m │           *,\n \x1b[2m70\x1b[0m │           form_submission:form_submissions!inner(\n \x1b[2m71\x1b[0m │             id,\n    ╰────\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8096,2076],()=>s(1538));module.exports=t})();