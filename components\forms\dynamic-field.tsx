'use client'

import { useState, useEffect } from 'react'
import { FormField, FormData, FormState } from '@/lib/form-types'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Sparkles, AlertCircle, Search } from 'lucide-react'

interface DynamicFieldProps {
  field: FormField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
  aiSuggestion?: any
  onAiAccept?: (fieldId: string, value: any) => void
}

export function DynamicField({
  field,
  value,
  onChange,
  error,
  disabled = false,
  aiSuggestion,
  onAiAccept
}: DynamicFieldProps) {
  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            id={field.id}
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholderJp || field.placeholder}
            disabled={disabled}
            className={error ? 'border-red-500' : ''}
          />
        )

      case 'textarea':
        return (
          <Textarea
            id={field.id}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholderJp || field.placeholder}
            disabled={disabled}
            className={error ? 'border-red-500' : ''}
            rows={3}
          />
        )

      case 'search':
        return (
          <div className="relative">
            <Input
              id={field.id}
              type="text"
              value={value?.name || value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={field.placeholderJp || field.placeholder || '検索...'}
              disabled={disabled}
              className={error ? 'border-red-500' : ''}
            />
            {field.searchable && (
              <div className="absolute right-2 top-2.5">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
            )}
          </div>
        )

      case 'dropdown':
        return (
          <Select
            value={value || ''}
            onValueChange={onChange}
            disabled={disabled}
          >
            <SelectTrigger className={error ? 'border-red-500' : ''}>
              <SelectValue placeholder={field.placeholderJp || field.placeholder || '選択してください'} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.labelJp || option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.id}
              checked={value || false}
              onCheckedChange={onChange}
              disabled={disabled}
            />
            <Label htmlFor={field.id} className="text-sm">
              {field.labelJp || field.label}
            </Label>
          </div>
        )

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  id={`${field.id}-${option.value}`}
                  name={field.id}
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => onChange(e.target.value)}
                  disabled={disabled}
                  className="form-radio"
                />
                <Label htmlFor={`${field.id}-${option.value}`} className="text-sm">
                  {option.labelJp || option.label}
                </Label>
              </div>
            ))}
          </div>
        )

      default:
        return (
          <div className="text-sm text-gray-500">
            Unsupported field type: {field.type}
          </div>
        )
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor={field.id} className="text-sm font-medium">
          {field.labelJp || field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        
        {aiSuggestion && onAiAccept && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onAiAccept(field.id, aiSuggestion)}
            className="text-xs"
          >
            <Sparkles className="h-3 w-3 mr-1" />
            AI提案 / AI Suggest
          </Button>
        )}
      </div>

      {renderField()}

      {aiSuggestion && (
        <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
          <Sparkles className="h-3 w-3 inline mr-1" />
          AI提案: {aiSuggestion}
        </div>
      )}

      {error && (
        <div className="text-xs text-red-600 flex items-center">
          <AlertCircle className="h-3 w-3 mr-1" />
          {error}
        </div>
      )}

      {field.helpTextJp || field.helpText && (
        <div className="text-xs text-gray-500">
          {field.helpTextJp || field.helpText}
        </div>
      )}
    </div>
  )
}
