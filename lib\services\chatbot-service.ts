import { supabase } from '@/lib/supabase'
import type { Database } from '@/lib/database.types'

type Conversation = Database['public']['Tables']['chatbot_conversations']['Row']
type Message = Database['public']['Tables']['chatbot_messages']['Row']
type FAQ = Database['public']['Tables']['chatbot_faq']['Row']
type Tutorial = Database['public']['Tables']['chatbot_tutorials']['Row']

export interface ChatContext {
  currentForm?: string
  currentPage?: string
  userRole?: string
  department?: string
  language?: 'ja' | 'en'
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: Date
}

export interface ChatbotResponse {
  response: string
  conversationId: string
  suggestions?: string[]
  tutorials?: Tutorial[]
  faqs?: FAQ[]
}

export class ChatbotService {
  private static instance: ChatbotService
  private conversationId?: string
  private context: ChatContext = {}

  private constructor() {}

  static getInstance(): ChatbotService {
    if (!ChatbotService.instance) {
      ChatbotService.instance = new ChatbotService()
    }
    return ChatbotService.instance
  }

  setContext(context: Partial<ChatContext>) {
    this.context = { ...this.context, ...context }
  }

  getContext(): ChatContext {
    return this.context
  }

  async sendMessage(message: string): Promise<ChatbotResponse> {
    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session) {
        throw new Error('User not authenticated')
      }

      const response = await supabase.functions.invoke('chatbot-assistant', {
        body: {
          conversationId: this.conversationId,
          message,
          context: this.context
        }
      })

      if (response.error) {
        throw response.error
      }

      const chatbotResponse = response.data as ChatbotResponse
      this.conversationId = chatbotResponse.conversationId

      return chatbotResponse
    } catch (error) {
      console.error('Chatbot service error:', error)
      throw error
    }
  }

  async getConversationHistory(): Promise<Message[]> {
    if (!this.conversationId) {
      return []
    }

    const { data, error } = await supabase
      .from('chatbot_messages')
      .select('*')
      .eq('conversation_id', this.conversationId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Error fetching conversation history:', error)
      return []
    }

    return data || []
  }

  async searchFAQs(query: string): Promise<FAQ[]> {
    const { data, error } = await supabase
      .from('chatbot_faq')
      .select('*')
      .eq('is_active', true)
      .textSearch('keywords', query.split(' ').join(' | '))
      .limit(5)

    if (error) {
      console.error('Error searching FAQs:', error)
      return []
    }

    return data || []
  }

  async getTutorials(category?: string): Promise<Tutorial[]> {
    let query = supabase
      .from('chatbot_tutorials')
      .select('*')
      .eq('is_active', true)

    if (category) {
      query = query.eq('category', category)
    }

    const { data, error } = await query.limit(5)

    if (error) {
      console.error('Error fetching tutorials:', error)
      return []
    }

    return data || []
  }

  async submitFeedback(messageId: string, rating: number, feedbackText?: string) {
    const { data: session } = await supabase.auth.getSession()
    if (!session?.session) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase
      .from('chatbot_feedback')
      .insert({
        message_id: messageId,
        user_id: session.session.user.id,
        rating,
        feedback_text: feedbackText
      })

    if (error) {
      console.error('Error submitting feedback:', error)
      throw error
    }
  }

  async clearConversation() {
    this.conversationId = undefined
    this.context = {}
  }

  async archiveConversation() {
    if (!this.conversationId) {
      return
    }

    const { error } = await supabase
      .from('chatbot_conversations')
      .update({ is_archived: true })
      .eq('id', this.conversationId)

    if (error) {
      console.error('Error archiving conversation:', error)
      throw error
    }

    this.conversationId = undefined
  }
}

export const chatbotService = ChatbotService.getInstance()
