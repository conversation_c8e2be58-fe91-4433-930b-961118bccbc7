-- Multi-Channel Notification System Schema
-- Supports in-app, email, and SMS notifications

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Notification preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email BOOLEAN DEFAULT true,
  sms BO<PERSON>EAN DEFAULT false,
  in_app BOOLEAN DEFAULT true,
  email_address TEXT,
  phone_number TEXT,
  language VARCHAR(2) DEFAULT 'ja' CHECK (language IN ('ja', 'en')),
  quiet_hours JSONB DEFAULT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Main notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  title_jp TEXT,
  message TEXT NOT NULL,
  message_jp TEXT,
  type VARCHAR(20) NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
  priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  channels TEXT[] NOT NULL,
  metadata JSONB DEFAULT '{}',
  action_url TEXT,
  scheduled_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Index for user queries
  INDEX idx_notifications_user_id (user_id),
  INDEX idx_notifications_created_at (created_at DESC),
  INDEX idx_notifications_read_status (user_id, read_at)
);

-- Email queue table
CREATE TABLE IF NOT EXISTS notification_email_queue (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  to_email TEXT NOT NULL,
  cc_emails TEXT[],
  bcc_emails TEXT[],
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  html_body TEXT,
  attachments JSONB DEFAULT '[]',
  priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  metadata JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'cancelled')),
  attempts INTEGER DEFAULT 0,
  last_error TEXT,
  sent_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Index for queue processing
  INDEX idx_email_queue_status (status, priority DESC, created_at)
);

-- SMS queue table
CREATE TABLE IF NOT EXISTS notification_sms_queue (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  to_phone TEXT NOT NULL,
  message TEXT NOT NULL CHECK (LENGTH(message) <= 1600), -- Support for multi-part SMS
  priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  metadata JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'cancelled')),
  attempts INTEGER DEFAULT 0,
  last_error TEXT,
  provider VARCHAR(50), -- 'twilio', 'aws-sns', etc.
  provider_message_id TEXT,
  sent_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Index for queue processing
  INDEX idx_sms_queue_status (status, priority DESC, created_at)
);

-- Scheduled notifications table
CREATE TABLE IF NOT EXISTS scheduled_notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  title_jp TEXT,
  message TEXT NOT NULL,
  message_jp TEXT,
  type VARCHAR(20) NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
  priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  channels TEXT[] NOT NULL,
  metadata JSONB DEFAULT '{}',
  action_url TEXT,
  scheduled_for TIMESTAMPTZ NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'cancelled')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Index for scheduled processing
  INDEX idx_scheduled_notifications (status, scheduled_for)
);

-- Notification templates table
CREATE TABLE IF NOT EXISTS notification_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  channels TEXT[] NOT NULL,
  title_template TEXT NOT NULL,
  title_template_jp TEXT,
  message_template TEXT NOT NULL,
  message_template_jp TEXT,
  type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  metadata JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification logs for audit trail
CREATE TABLE IF NOT EXISTS notification_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
  channel VARCHAR(20) NOT NULL CHECK (channel IN ('in-app', 'email', 'sms')),
  status VARCHAR(20) NOT NULL,
  recipient TEXT NOT NULL,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers
CREATE TRIGGER update_notification_preferences_updated_at
  BEFORE UPDATE ON notification_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_templates_updated_at
  BEFORE UPDATE ON notification_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_email_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_sms_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- Users can only manage their own preferences
CREATE POLICY "Users can view own preferences" ON notification_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON notification_preferences
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON notification_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only view their own notifications
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Only system can insert notifications
CREATE POLICY "System can insert notifications" ON notifications
  FOR INSERT WITH CHECK (true);

-- Email queue policies (only system access)
CREATE POLICY "System can manage email queue" ON notification_email_queue
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- SMS queue policies (only system access)
CREATE POLICY "System can manage SMS queue" ON notification_sms_queue
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Scheduled notifications policies
CREATE POLICY "Users can view own scheduled notifications" ON scheduled_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage scheduled notifications" ON scheduled_notifications
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Templates are viewable by all authenticated users
CREATE POLICY "Authenticated users can view templates" ON notification_templates
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins can manage templates
CREATE POLICY "Admins can manage templates" ON notification_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM roles r
      JOIN staff s ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Notification logs are viewable by the notification owner and admins
CREATE POLICY "Users can view own notification logs" ON notification_logs
  FOR SELECT USING (
    notification_id IN (
      SELECT id FROM notifications WHERE user_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM roles r
      JOIN staff s ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator')
    )
  );

-- Insert default notification templates
INSERT INTO notification_templates (name, channels, title_template, title_template_jp, message_template, message_template_jp, type, priority) VALUES
  ('request_created', ARRAY['in-app', 'email'], 'New Request: {{request_title}}', '新しいリクエスト: {{request_title}}', 'Your request has been created and is being processed.', 'リクエストが作成され、処理中です。', 'info', 'medium'),
  ('request_approved', ARRAY['in-app', 'email'], 'Request Approved: {{request_title}}', 'リクエスト承認: {{request_title}}', 'Your request has been approved by {{approver_name}}.', '{{approver_name}}によってリクエストが承認されました。', 'success', 'high'),
  ('request_rejected', ARRAY['in-app', 'email'], 'Request Rejected: {{request_title}}', 'リクエスト却下: {{request_title}}', 'Your request has been rejected. Reason: {{rejection_reason}}', 'リクエストが却下されました。理由: {{rejection_reason}}', 'warning', 'high'),
  ('request_completed', ARRAY['in-app', 'email', 'sms'], 'Request Completed: {{request_title}}', 'リクエスト完了: {{request_title}}', 'Your request has been completed successfully.', 'リクエストが正常に完了しました。', 'success', 'high'),
  ('password_reset', ARRAY['email'], 'Password Reset Request', 'パスワードリセットリクエスト', 'A password reset has been requested for {{user_email}}.', '{{user_email}}のパスワードリセットが要求されました。', 'warning', 'critical'),
  ('system_maintenance', ARRAY['all'], 'System Maintenance Notice', 'システムメンテナンスのお知らせ', 'System maintenance scheduled for {{maintenance_date}}.', '{{maintenance_date}}にシステムメンテナンスを予定しています。', 'info', 'high')
ON CONFLICT (name) DO NOTHING;

-- Create indexes for performance
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, created_at DESC) WHERE read_at IS NULL;
CREATE INDEX idx_email_queue_pending ON notification_email_queue(priority DESC, created_at) WHERE status = 'pending';
CREATE INDEX idx_sms_queue_pending ON notification_sms_queue(priority DESC, created_at) WHERE status = 'pending';
