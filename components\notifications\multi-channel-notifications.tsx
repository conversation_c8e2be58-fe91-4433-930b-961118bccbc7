"use client";

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { notificationService } from '@/lib/services/notifications/notification-service';
import { Bell, Check, X, AlertCircle, Info, Mail, MessageSquare, Phone, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { createClient } from '@/lib/supabase/client';

interface Notification {
  id: string;
  title: string;
  titleJp?: string;
  message: string;
  messageJp?: string;
  type: 'info' | 'success' | 'warning' | 'error';
  channels: string[];
  timestamp: string;
  readAt?: string;
  actionUrl?: string;
}

export function MultiChannelNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [preferences, setPreferences] = useState({
    email: true,
    sms: false,
    inApp: true,
    emailAddress: '',
    phoneNumber: '',
    language: 'ja' as 'ja' | 'en',
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    }
  });
  const [showSettings, setShowSettings] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();

  useEffect(() => {
    // Load user preferences
    loadPreferences();
    
    // Load notification history
    loadNotificationHistory();
    
    // Subscribe to real-time notifications
    const channel = subscribeToNotifications();
    
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const loadPreferences = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const { data } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (data) {
        setPreferences({
          email: data.email,
          sms: data.sms,
          inApp: data.in_app,
          emailAddress: data.email_address || '',
          phoneNumber: data.phone_number || '',
          language: data.language || 'ja',
          quietHours: data.quiet_hours || {
            enabled: false,
            start: '22:00',
            end: '08:00'
          }
        });
      }
    }
  };

  const loadNotificationHistory = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const { data } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);
      
      if (data) {
        setNotifications(data.map(n => ({
          id: n.id,
          title: n.title,
          titleJp: n.title_jp,
          message: n.message,
          messageJp: n.message_jp,
          type: n.type,
          channels: n.channels,
          timestamp: n.created_at,
          readAt: n.read_at,
          actionUrl: n.action_url
        })));
        setUnreadCount(data.filter(n => !n.read_at).length);
      }
    }
  };

  const subscribeToNotifications = () => {
    const userId = supabase.auth.getUser().then(u => u.data.user?.id);
    const channel = supabase
      .channel(`notifications:${userId}`)
      .on('broadcast', { event: 'new-notification' }, (payload) => {
        handleNewNotification(payload.payload);
      })
      .subscribe();
    
    return channel;
  };

  const handleNewNotification = (notification: Notification) => {
    setNotifications(prev => [notification, ...prev]);
    setUnreadCount(prev => prev + 1);
    
    // Show toast notification
    toast({
      title: preferences.language === 'ja' && notification.titleJp 
        ? notification.titleJp 
        : notification.title,
      description: preferences.language === 'ja' && notification.messageJp 
        ? notification.messageJp 
        : notification.message,
      variant: notification.type === 'error' ? 'destructive' : 'default',
    });
  };

  const markAsRead = async (notificationId: string) => {
    await notificationService.markAsRead(notificationId);
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, readAt: new Date().toISOString() } : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = async () => {
    const unreadNotifications = notifications.filter(n => !n.readAt);
    await Promise.all(unreadNotifications.map(n => notificationService.markAsRead(n.id)));
    setNotifications(prev => prev.map(n => ({ ...n, readAt: new Date().toISOString() })));
    setUnreadCount(0);
  };

  const clearAll = async () => {
    await Promise.all(notifications.map(n => notificationService.deleteNotification(n.id)));
    setNotifications([]);
    setUnreadCount(0);
  };

  const updatePreferences = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      await notificationService.updatePreferences({
        userId: user.id,
        email: preferences.email,
        sms: preferences.sms,
        inApp: preferences.inApp,
        emailAddress: preferences.emailAddress,
        phoneNumber: preferences.phoneNumber,
        language: preferences.language,
        quietHours: preferences.quietHours
      });
      toast({
        title: preferences.language === 'ja' ? '設定を更新しました' : 'Settings updated',
        description: preferences.language === 'ja' 
          ? '通知設定が正常に更新されました。' 
          : 'Your notification preferences have been updated successfully.',
      });
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'error':
        return <X className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email':
        return <Mail className="h-3 w-3" />;
      case 'sms':
        return <Phone className="h-3 w-3" />;
      default:
        return <MessageSquare className="h-3 w-3" />;
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center">
                {unreadCount}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-96" align="end">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span>{preferences.language === 'ja' ? '通知' : 'Notifications'}</span>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(true)}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                disabled={unreadCount === 0}
              >
                {preferences.language === 'ja' ? 'すべて既読' : 'Mark all read'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAll}
                disabled={notifications.length === 0}
              >
                {preferences.language === 'ja' ? 'クリア' : 'Clear'}
              </Button>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <ScrollArea className="h-[400px]">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">
                {preferences.language === 'ja' 
                  ? '通知はありません' 
                  : 'No notifications'}
              </div>
            ) : (
              notifications.map((notification) => (
                <DropdownMenuItem
                  key={notification.id}
                  className={`p-4 cursor-pointer ${!notification.readAt ? 'bg-accent/50' : ''}`}
                  onClick={() => {
                    markAsRead(notification.id);
                    if (notification.actionUrl) {
                      window.location.href = notification.actionUrl;
                    }
                  }}
                >
                  <div className="flex gap-3 w-full">
                    <div className="flex-shrink-0 mt-1">
                      {getIcon(notification.type)}
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">
                        {preferences.language === 'ja' && notification.titleJp 
                          ? notification.titleJp 
                          : notification.title}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {preferences.language === 'ja' && notification.messageJp 
                          ? notification.messageJp 
                          : notification.message}
                      </p>
                      <div className="flex items-center gap-2">
                        <p className="text-xs text-muted-foreground">
                          {new Date(notification.timestamp).toLocaleString(
                            preferences.language === 'ja' ? 'ja-JP' : 'en-US'
                          )}
                        </p>
                        <div className="flex gap-1">
                          {notification.channels.map(channel => (
                            <Badge key={channel} variant="secondary" className="h-5 px-1">
                              {getChannelIcon(channel)}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </DropdownMenuItem>
              ))
            )}
          </ScrollArea>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {preferences.language === 'ja' ? '通知設定' : 'Notification Settings'}
            </DialogTitle>
            <DialogDescription>
              {preferences.language === 'ja' 
                ? '通知の受信方法と設定を管理します。' 
                : 'Manage how you receive notifications.'}
            </DialogDescription>
          </DialogHeader>
          
          <Tabs defaultValue="channels" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="channels">
                {preferences.language === 'ja' ? 'チャンネル' : 'Channels'}
              </TabsTrigger>
              <TabsTrigger value="preferences">
                {preferences.language === 'ja' ? '設定' : 'Preferences'}
              </TabsTrigger>
              <TabsTrigger value="quiet">
                {preferences.language === 'ja' ? '通知停止時間' : 'Quiet Hours'}
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="channels" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="in-app" className="text-base">
                      {preferences.language === 'ja' ? 'アプリ内通知' : 'In-App Notifications'}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {preferences.language === 'ja' 
                        ? 'アプリケーション内でリアルタイム通知を受信します。' 
                        : 'Receive real-time notifications within the application.'}
                    </p>
                  </div>
                  <Switch
                    id="in-app"
                    checked={preferences.inApp}
                    onCheckedChange={(checked) => 
                      setPreferences(prev => ({ ...prev, inApp: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email" className="text-base">
                      {preferences.language === 'ja' ? 'メール通知' : 'Email Notifications'}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {preferences.language === 'ja' 
                        ? '重要な更新をメールで受信します。' 
                        : 'Receive important updates via email.'}
                    </p>
                  </div>
                  <Switch
                    id="email"
                    checked={preferences.email}
                    onCheckedChange={(checked) => 
                      setPreferences(prev => ({ ...prev, email: checked }))
                    }
                  />
                </div>
                
                {preferences.email && (
                  <div className="ml-6">
                    <input
                      type="email"
                      placeholder={preferences.language === 'ja' ? 'メールアドレス' : 'Email address'}
                      value={preferences.emailAddress}
                      onChange={(e) => 
                        setPreferences(prev => ({ ...prev, emailAddress: e.target.value }))
                      }
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms" className="text-base">
                      {preferences.language === 'ja' ? 'SMS通知' : 'SMS Notifications'}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {preferences.language === 'ja' 
                        ? '緊急通知をSMSで受信します。' 
                        : 'Receive urgent notifications via SMS.'}
                    </p>
                  </div>
                  <Switch
                    id="sms"
                    checked={preferences.sms}
                    onCheckedChange={(checked) => 
                      setPreferences(prev => ({ ...prev, sms: checked }))
                    }
                  />
                </div>
                
                {preferences.sms && (
                  <div className="ml-6">
                    <input
                      type="tel"
                      placeholder={preferences.language === 'ja' ? '電話番号' : 'Phone number'}
                      value={preferences.phoneNumber}
                      onChange={(e) => 
                        setPreferences(prev => ({ ...prev, phoneNumber: e.target.value }))
                      }
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="preferences" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="language">
                    {preferences.language === 'ja' ? '言語' : 'Language'}
                  </Label>
                  <select
                    id="language"
                    value={preferences.language}
                    onChange={(e) => 
                      setPreferences(prev => ({ 
                        ...prev, 
                        language: e.target.value as 'ja' | 'en' 
                      }))
                    }
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    <option value="ja">日本語</option>
                    <option value="en">English</option>
                  </select>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="quiet" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="quiet-hours" className="text-base">
                      {preferences.language === 'ja' ? '通知停止時間を有効にする' : 'Enable Quiet Hours'}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {preferences.language === 'ja' 
                        ? '指定した時間帯は通知を送信しません。' 
                        : 'No notifications will be sent during specified hours.'}
                    </p>
                  </div>
                  <Switch
                    id="quiet-hours"
                    checked={preferences.quietHours.enabled}
                    onCheckedChange={(checked) => 
                      setPreferences(prev => ({ 
                        ...prev, 
                        quietHours: { ...prev.quietHours, enabled: checked }
                      }))
                    }
                  />
                </div>
                
                {preferences.quietHours.enabled && (
                  <div className="grid grid-cols-2 gap-4 ml-6">
                    <div className="space-y-2">
                      <Label htmlFor="start-time">
                        {preferences.language === 'ja' ? '開始時刻' : 'Start Time'}
                      </Label>
                      <input
                        id="start-time"
                        type="time"
                        value={preferences.quietHours.start}
                        onChange={(e) => 
                          setPreferences(prev => ({ 
                            ...prev, 
                            quietHours: { ...prev.quietHours, start: e.target.value }
                          }))
                        }
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-time">
                        {preferences.language === 'ja' ? '終了時刻' : 'End Time'}
                      </Label>
                      <input
                        id="end-time"
                        type="time"
                        value={preferences.quietHours.end}
                        onChange={(e) => 
                          setPreferences(prev => ({ 
                            ...prev, 
                            quietHours: { ...prev.quietHours, end: e.target.value }
                          }))
                        }
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-end gap-3 mt-6">
            <Button variant="outline" onClick={() => setShowSettings(false)}>
              {preferences.language === 'ja' ? 'キャンセル' : 'Cancel'}
            </Button>
            <Button onClick={() => {
              updatePreferences();
              setShowSettings(false);
            }}>
              {preferences.language === 'ja' ? '保存' : 'Save'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
