-- Create rate_limit_logs table for tracking API rate limits
CREATE TABLE IF NOT EXISTS rate_limit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key VARCHAR(255) NOT NULL,
  endpoint TEXT,
  method VARCHAR(10),
  ip INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX idx_rate_limit_logs_key_created ON rate_limit_logs(key, created_at DESC);
CREATE INDEX idx_rate_limit_logs_created ON rate_limit_logs(created_at);

-- Add cleanup policy (optional - can be handled by application)
COMMENT ON TABLE rate_limit_logs IS 'Tracks API rate limiting. Records older than 24 hours should be cleaned up regularly.';
