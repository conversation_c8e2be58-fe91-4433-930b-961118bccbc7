"use strict";exports.id=268,exports.ids=[268],exports.modules={77252:(e,t,r)=>{r.d(t,{E:()=>s});var n=r(45512);r(58009);var o=r(21643),a=r(59462);let i=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function s({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:t}),e),...r})}},87021:(e,t,r)=>{r.d(t,{$:()=>l,r:()=>d});var n=r(45512),o=r(58009),a=r(12705),i=r(21643),s=r(59462);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...i},l)=>{let u=o?a.DX:"button";return(0,n.jsx)(u,{className:(0,s.cn)(d({variant:t,size:r,className:e})),ref:l,...i})});l.displayName="Button"},69193:(e,t,r)=>{r.d(t,{Xi:()=>l,av:()=>u,j7:()=>d,tU:()=>s});var n=r(45512),o=r(58009),a=r(55613),i=r(59462);let s=a.bL,d=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(a.B8,{ref:r,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=a.B8.displayName;let l=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(a.l9,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));l.displayName=a.l9.displayName;let u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(a.UC,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));u.displayName=a.UC.displayName},92557:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},48305:(e,t,r)=>{r.d(t,{RG:()=>w,bL:()=>k,q7:()=>T});var n=r(58009),o=r(31412),a=r(39217),i=r(29952),s=r(6004),d=r(30096),l=r(30830),u=r(92828),c=r(13024),f=r(59018),v=r(45512),p="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,h,y]=(0,a.N)(m),[x,w]=(0,s.A)(m,[y]),[R,j]=x(m),F=n.forwardRef((e,t)=>(0,v.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(A,{...e,ref:t})})}));F.displayName=m;var A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:s=!1,dir:d,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:j=!1,...F}=e,A=n.useRef(null),C=(0,i.s)(t,A),I=(0,f.jH)(d),[N,k]=(0,c.i)({prop:g,defaultProp:y??null,onChange:x,caller:m}),[T,G]=n.useState(!1),E=(0,u.c)(w),L=h(r),M=n.useRef(!1),[K,B]=n.useState(0);return n.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,E),()=>e.removeEventListener(p,E)},[E]),(0,v.jsx)(R,{scope:r,orientation:a,dir:I,loop:s,currentTabStopId:N,onItemFocus:n.useCallback(e=>k(e),[k]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>B(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>B(e=>e-1),[]),children:(0,v.jsx)(l.sG.div,{tabIndex:T||0===K?-1:0,"data-orientation":a,...F,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(p,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===N),...e].filter(Boolean).map(e=>e.ref.current),j)}}M.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>G(!1))})})}),C="RovingFocusGroupItem",I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:s,children:u,...c}=e,f=(0,d.B)(),p=s||f,b=j(C,r),m=b.currentTabStopId===p,y=h(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:R}=b;return n.useEffect(()=>{if(a)return x(),()=>w()},[a,x,w]),(0,v.jsx)(g.ItemSlot,{scope:r,id:p,focusable:a,active:i,children:(0,v.jsx)(l.sG.span,{tabIndex:m?0:-1,"data-orientation":b.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?b.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>b.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){b.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return N[o]}(e,b.orientation,b.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=b.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}}),children:"function"==typeof u?u({isCurrentTabStop:m,hasTabStop:null!=R}):u})})});I.displayName=C;var N={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var k=F,T=I},55613:(e,t,r)=>{r.d(t,{B8:()=>D,UC:()=>T,bL:()=>N,l9:()=>k});var n=r(58009),o=r(31412),a=r(6004),i=r(48305),s=r(98060),d=r(30830),l=r(59018),u=r(13024),c=r(30096),f=r(45512),v="Tabs",[p,b]=(0,a.A)(v,[i.RG]),m=(0,i.RG)(),[g,h]=p(v),y=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:s,activationMode:p="automatic",...b}=e,m=(0,l.jH)(s),[h,y]=(0,u.i)({prop:n,onChange:o,defaultProp:a??"",caller:v});return(0,f.jsx)(g,{scope:r,baseId:(0,c.B)(),value:h,onValueChange:y,orientation:i,dir:m,activationMode:p,children:(0,f.jsx)(d.sG.div,{dir:m,"data-orientation":i,...b,ref:t})})});y.displayName=v;var x="TabsList",w=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=h(x,r),s=m(r);return(0,f.jsx)(i.bL,{asChild:!0,...s,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(d.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});w.displayName=x;var R="TabsTrigger",j=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...s}=e,l=h(R,r),u=m(r),c=C(l.baseId,n),v=I(l.baseId,n),p=n===l.value;return(0,f.jsx)(i.q7,{asChild:!0,...u,focusable:!a,active:p,children:(0,f.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":v,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;p||a||!e||l.onValueChange(n)})})})});j.displayName=R;var F="TabsContent",A=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...l}=e,u=h(F,r),c=C(u.baseId,o),v=I(u.baseId,o),p=o===u.value,b=n.useRef(p);return n.useEffect(()=>{let e=requestAnimationFrame(()=>b.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(s.C,{present:a||p,children:({present:r})=>(0,f.jsx)(d.sG.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:v,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:b.current?"0s":void 0},children:r&&i})})});function C(e,t){return`${e}-trigger-${t}`}function I(e,t){return`${e}-content-${t}`}A.displayName=F;var N=y,D=w,k=j,T=A}};