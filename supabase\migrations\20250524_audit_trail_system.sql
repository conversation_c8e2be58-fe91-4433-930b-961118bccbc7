-- Comprehensive Audit Trail System Schema
-- Date: 2025-05-24

-- Create enum types for audit events
CREATE TYPE audit_event_type AS ENUM (
  -- User Events
  'USER_LOGIN', 'USER_LOGOUT', 'USER_CREATED', 'USER_UPDATED', 'USER_DELETED',
  'PASSWORD_CHANGED', 'PASSWORD_RESET', 'ROLE_ASSIGNED', 'ROLE_REMOVED',
  
  -- Request Events
  'REQUEST_CREATED', 'REQUEST_UPDATED', 'REQUEST_DELETED',
  'REQUEST_APPROVED', 'REQUEST_REJECTED', 'REQUEST_COMPLETED', 'REQUEST_CANCELLED',
  'REQUEST_ASSIGNED', 'REQUEST_ESCALATED',
  
  -- Data Events
  'DATA_ACCESSED', 'DATA_EXPORTED', 'DATA_IMPORTED',
  'PERMISSION_GRANTED', 'PERMISSION_REVOKED',
  'CONFIGURATION_CHANGED', 'SETTINGS_UPDATED',
  
  -- System Events
  'SYSTEM_ERROR', 'SYSTEM_WARNING', 'SYSTEM_INFO',
  'INTEGRATION_SUCCESS', 'INTEGRATION_FAILURE',
  'SCHEDULED_TASK_RUN', 'SCHEDULED_TASK_FAILED',
  'SERVICE_STARTED', 'SERVICE_STOPPED'
);

CREATE TYPE audit_severity AS ENUM ('INFO', 'WARNING', 'ERROR', 'CRITICAL');

-- Main audit logs table with immutability constraints
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Event Information
  event_type audit_event_type NOT NULL,
  severity audit_severity NOT NULL DEFAULT 'INFO',
  action TEXT NOT NULL,
  description TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  occurred_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- User Context
  user_id UUID REFERENCES auth.users(id),
  staff_id UUID REFERENCES staff(id),
  role_name TEXT,
  department_id UUID REFERENCES divisions(id),
  department_name TEXT,
  
  -- Entity Information
  entity_type TEXT,
  entity_id TEXT,
  entity_name TEXT,
  old_value JSONB,
  new_value JSONB,
  changes_summary JSONB,
  
  -- Technical Context
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  request_id UUID,
  trace_id TEXT,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  tags TEXT[] DEFAULT '{}',
  
  -- Compliance
  retention_until DATE,
  compliance_flags TEXT[] DEFAULT '{}',
  is_sensitive BOOLEAN DEFAULT FALSE,
  
  -- Integrity
  checksum TEXT,
  
  -- Indexes for performance
  CONSTRAINT audit_logs_immutable CHECK (false) NO INHERIT
);

-- Create indexes for efficient querying
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_logs_severity ON audit_logs(severity);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_staff_id ON audit_logs(staff_id);
CREATE INDEX idx_audit_logs_department_id ON audit_logs(department_id);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_session ON audit_logs(session_id);
CREATE INDEX idx_audit_logs_tags ON audit_logs USING GIN(tags);
CREATE INDEX idx_audit_logs_metadata ON audit_logs USING GIN(metadata);

-- Archive table for long-term storage
CREATE TABLE audit_logs_archive (
  id UUID PRIMARY KEY,
  archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  original_created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  log_data JSONB NOT NULL,
  compressed_data BYTEA,
  checksum TEXT NOT NULL
);

-- Audit log access tracking (audit the auditors)
CREATE TABLE audit_log_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accessed_by UUID REFERENCES staff(id),
  access_type TEXT NOT NULL, -- 'VIEW', 'EXPORT', 'SEARCH'
  query_params JSONB,
  result_count INTEGER,
  ip_address INET,
  user_agent TEXT
);

-- Compliance report templates
CREATE TABLE audit_report_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  template_config JSONB NOT NULL,
  output_format TEXT NOT NULL, -- 'PDF', 'CSV', 'JSON', 'XLSX'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES staff(id),
  is_active BOOLEAN DEFAULT TRUE
);

-- Retention policies
CREATE TABLE audit_retention_policies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  event_types audit_event_type[],
  retention_days INTEGER NOT NULL,
  archive_after_days INTEGER,
  delete_after_days INTEGER,
  compliance_requirement TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to enforce immutability
CREATE OR REPLACE FUNCTION prevent_audit_log_update()
RETURNS TRIGGER AS $$
BEGIN
  RAISE EXCEPTION 'Audit logs cannot be updated or deleted';
END;
$$ LANGUAGE plpgsql;

-- Create triggers to prevent updates and deletes
CREATE TRIGGER audit_logs_no_update
BEFORE UPDATE ON audit_logs
FOR EACH ROW EXECUTE FUNCTION prevent_audit_log_update();

CREATE TRIGGER audit_logs_no_delete
BEFORE DELETE ON audit_logs
FOR EACH ROW EXECUTE FUNCTION prevent_audit_log_update();

-- Function to calculate checksum
CREATE OR REPLACE FUNCTION calculate_audit_checksum(log_data JSONB)
RETURNS TEXT AS $$
BEGIN
  RETURN encode(digest(log_data::TEXT, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql IMMUTABLE;
-- Function to automatically create audit log entries
CREATE OR REPLACE FUNCTION audit_log_trigger()
RETURNS TRIGGER AS $$
DECLARE
  audit_data JSONB;
  event_type_val audit_event_type;
  entity_name_val TEXT;
BEGIN
  -- Determine event type based on operation
  IF TG_OP = 'INSERT' THEN
    event_type_val := (TG_TABLE_NAME || '_CREATED')::audit_event_type;
    audit_data := to_jsonb(NEW);
  ELSIF TG_OP = 'UPDATE' THEN
    event_type_val := (TG_TABLE_NAME || '_UPDATED')::audit_event_type;
    audit_data := jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    );
  ELSIF TG_OP = 'DELETE' THEN
    event_type_val := (TG_TABLE_NAME || '_DELETED')::audit_event_type;
    audit_data := to_jsonb(OLD);
  END IF;
  
  -- Get entity name if available
  IF TG_OP != 'DELETE' AND NEW IS NOT NULL THEN
    entity_name_val := COALESCE(
      (NEW->>'name')::TEXT,
      (NEW->>'title')::TEXT,
      (NEW->>'email')::TEXT,
      NULL
    );
  END IF;
  
  -- Insert audit log (handled by edge function for user context)
  PERFORM net.http_post(
    url := current_setting('app.settings.edge_functions_url') || '/audit-log',
    body := jsonb_build_object(
      'table_name', TG_TABLE_NAME,
      'operation', TG_OP,
      'schema_name', TG_TABLE_SCHEMA,
      'entity_id', COALESCE(NEW.id, OLD.id),
      'entity_name', entity_name_val,
      'data', audit_data
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create views for role-based access
CREATE OR REPLACE VIEW audit_logs_user_view AS
SELECT * FROM audit_logs
WHERE 
  -- Global Admins see all
  EXISTS (
    SELECT 1 FROM staff s
    JOIN roles r ON s.role_id = r.id
    WHERE s.auth_id = auth.uid()
    AND r.name IN ('Global Administrator', 'Web App System Administrator')
  )
  OR
  -- Department Admins see their department only
  (
    department_id IN (
      SELECT division_id FROM staff
      WHERE auth_id = auth.uid()
    )
    AND EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name = 'Department Administrator'
    )
  )
  OR
  -- Users see their own logs
  user_id = auth.uid();

-- Analytics views
CREATE OR REPLACE VIEW audit_log_statistics AS
SELECT 
  DATE_TRUNC('hour', created_at) as hour,
  event_type,
  severity,
  COUNT(*) as event_count,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(DISTINCT department_id) as departments_affected
FROM audit_logs
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY 1, 2, 3;

-- Compliance summary view
CREATE OR REPLACE VIEW audit_compliance_summary AS
SELECT 
  event_type,
  COUNT(*) as total_events,
  MIN(created_at) as oldest_entry,
  MAX(created_at) as newest_entry,
  array_agg(DISTINCT compliance_flags) as compliance_flags,
  COUNT(*) FILTER (WHERE is_sensitive = true) as sensitive_events
FROM audit_logs
GROUP BY event_type;

-- RLS Policies
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_report_templates ENABLE ROW LEVEL SECURITY;

-- Audit logs policy (via view access only)
CREATE POLICY "Audit logs are read-only via views" ON audit_logs
FOR SELECT TO authenticated
USING (false);

-- Audit log access policy
CREATE POLICY "Users can see their own audit access" ON audit_log_access
FOR SELECT TO authenticated
USING (accessed_by IN (SELECT id FROM staff WHERE auth_id = auth.uid()));

-- Report templates policy
CREATE POLICY "Admins can manage report templates" ON audit_report_templates
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM staff s
    JOIN roles r ON s.role_id = r.id
    WHERE s.auth_id = auth.uid()
    AND r.name IN ('Global Administrator', 'Web App System Administrator')
  )
);

-- Insert default retention policies
INSERT INTO audit_retention_policies (name, description, event_types, retention_days, archive_after_days, delete_after_days, compliance_requirement) VALUES
('General Logs', 'Default retention for general system logs', 
 ARRAY['SYSTEM_INFO', 'USER_LOGIN', 'USER_LOGOUT']::audit_event_type[], 
 90, 365, 730, 'General compliance'),
('Security Events', 'Extended retention for security-related events', 
 ARRAY['USER_CREATED', 'USER_DELETED', 'ROLE_ASSIGNED', 'ROLE_REMOVED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED']::audit_event_type[], 
 365, 1095, 2555, 'SOC 2 Type II'),
('Critical Events', 'Long-term retention for critical events', 
 ARRAY['SYSTEM_ERROR', 'SYSTEM_CRITICAL', 'DATA_EXPORTED', 'CONFIGURATION_CHANGED']::audit_event_type[], 
 730, 2555, NULL, 'ISO 27001'),
('Request Lifecycle', 'Request tracking and compliance', 
 ARRAY['REQUEST_CREATED', 'REQUEST_UPDATED', 'REQUEST_APPROVED', 'REQUEST_COMPLETED']::audit_event_type[], 
 180, 365, 1095, 'Internal audit requirements');

-- Insert default report templates
INSERT INTO audit_report_templates (name, description, template_config, output_format) VALUES
('Daily Security Report', 'Daily summary of security-related events', 
 '{"filters": {"severity": ["WARNING", "ERROR", "CRITICAL"], "event_types": ["USER_CREATED", "USER_DELETED", "ROLE_ASSIGNED", "PERMISSION_GRANTED"]}, "groupBy": ["event_type", "department_id"], "includeStats": true}'::JSONB, 
 'PDF'),
('Monthly Compliance Report', 'Monthly compliance audit report', 
 '{"filters": {"compliance_flags": ["SOC2", "ISO27001"]}, "sections": ["summary", "details", "trends"], "includeCharts": true}'::JSONB, 
 'PDF'),
('User Activity Export', 'Export of user activities for analysis', 
 '{"filters": {}, "columns": ["created_at", "user_id", "event_type", "action", "entity_type", "entity_id"], "format": "detailed"}'::JSONB, 
 'CSV'),
('Department Audit Trail', 'Department-specific audit trail', 
 '{"filters": {"groupByDepartment": true}, "includeSubordinates": true, "dateRange": "custom"}'::JSONB, 
 'XLSX');
