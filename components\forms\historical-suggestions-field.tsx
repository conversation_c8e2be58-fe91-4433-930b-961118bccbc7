'use client'

import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Clock, TrendingUp, Sparkles } from 'lucide-react'
import { historicalDataService, PatternAnalysis } from '@/lib/historical-data-service'
import { cn } from '@/lib/utils'

interface HistoricalSuggestionsFieldProps {
  fieldId: string
  label: string
  labelJp?: string
  value: string
  onChange: (value: string) => void
  departmentId?: string
  placeholder?: string
  required?: boolean
  className?: string
}

export function HistoricalSuggestionsField({
  fieldId,
  label,
  labelJp,
  value,
  onChange,
  departmentId,
  placeholder,
  required,
  className
}: HistoricalSuggestionsFieldProps) {
  const [patterns, setPatterns] = useState<PatternAnalysis | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)

  useEffect(() => {
    const loadPatterns = async () => {
      if (!departmentId || !value || value.length < 2) return

      setIsLoading(true)
      try {
        const analysis = await historicalDataService.analyzeFormPatterns(
          departmentId,
          fieldId,
          value
        )
        setPatterns(analysis)
        setShowSuggestions(true)
      } catch (error) {
        console.error('Failed to load patterns:', error)
      } finally {
        setIsLoading(false)
      }
    }

    const debounceTimer = setTimeout(loadPatterns, 500)
    return () => clearTimeout(debounceTimer)
  }, [value, departmentId, fieldId])

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion)
    setShowSuggestions(false)
    
    // Submit positive feedback
    if (departmentId) {
      historicalDataService.submitFeedback({
        userId: 'current-user', // This should come from auth context
        fieldId,
        originalValue: value,
        suggestedValue: suggestion,
        wasAccepted: true,
        finalValue: suggestion,
        departmentId
      })
    }
  }

  const hasHistoricalData = patterns?.commonPatterns && patterns.commonPatterns.length > 0

  return (
    <div className="space-y-2">
      <Label htmlFor={fieldId}>
        {labelJp ? `${labelJp} / ${label}` : label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      <div className="relative">
        <Input
          id={fieldId}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          className={cn(
            className,
            hasHistoricalData && "pr-10"
          )}
        />
        
        {hasHistoricalData && (
          <div className="absolute right-2 top-1/2 -translate-y-1/2">
            <Badge variant="secondary" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {patterns.commonPatterns[0].frequency}
            </Badge>
          </div>
        )}
      </div>

      {/* AI Suggestion */}
      {patterns?.aiSuggestion && patterns.aiSuggestion !== value && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium flex items-center gap-1 text-blue-700 dark:text-blue-300">
                <Sparkles className="h-4 w-4" />
                AI提案 / AI Suggestion
              </p>
              <p className="text-sm mt-1">{patterns.aiSuggestion}</p>
              {patterns.reasoning && (
                <p className="text-xs text-muted-foreground mt-1">{patterns.reasoning}</p>
              )}
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleSuggestionClick(patterns.aiSuggestion!)}
              className="text-blue-700 hover:text-blue-800"
            >
              使用 / Use
            </Button>
          </div>
        </div>
      )}

      {/* Historical Patterns */}
      {showSuggestions && hasHistoricalData && (
        <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-md p-3 space-y-2">
          <p className="text-sm font-medium flex items-center gap-1 text-gray-700 dark:text-gray-300">
            <TrendingUp className="h-4 w-4" />
            よく使用される値 / Common Values
          </p>
          
          <div className="space-y-1">
            {patterns.commonPatterns.slice(0, 3).map((pattern, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(pattern.value)}
                className={cn(
                  "w-full text-left p-2 rounded text-sm",
                  "hover:bg-gray-100 dark:hover:bg-gray-800",
                  "transition-colors cursor-pointer",
                  "flex items-center justify-between"
                )}
              >
                <span className="truncate">{pattern.value}</span>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{pattern.frequency}回使用</span>
                  <span>{Math.round(pattern.confidence * 100)}%</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {isLoading && (
        <p className="text-xs text-muted-foreground">
          履歴データを読み込み中... / Loading historical data...
        </p>
      )}
    </div>
  )
}
