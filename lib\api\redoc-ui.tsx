/**
 * ReDoc UI Component
 * 
 * This component provides a ReDoc interface for displaying OpenAPI documentation.
 * It uses the redoc package to render the UI.
 * 
 * NOTE: You need to install the redoc package:
 * npm install redoc styled-components
 */

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { OpenAPIDocument } from './openapi-documentation';

// Dynamically import ReDoc to avoid SSR issues
// NOTE: This requires the redoc package to be installed
const RedocStandalone = dynamic(
  () => import('redoc').then((mod) => mod.RedocStandalone),
  { ssr: false }
);

/**
 * ReDoc UI props
 */
export interface ReDocUIProps {
  /**
   * OpenAPI document or URL to OpenAPI document
   */
  spec: OpenAPIDocument | string;

  /**
   * Options for ReDoc
   */
  options?: {
    /**
     * Hide hostname in method definition
     */
    hideHostname?: boolean;

    /**
     * Hide download button
     */
    hideDownloadButton?: boolean;

    /**
     * Don't display schema title in the definition
     */
    noAutoAuth?: boolean;

    /**
     * Disable search indexing and search box
     */
    disableSearch?: boolean;

    /**
     * Show only required fields in request schemas
     */
    requiredPropsFirst?: boolean;

    /**
     * Sort properties alphabetically
     */
    sortPropsAlphabetically?: boolean;

    /**
     * Show path in operation id
     */
    pathInMiddlePanel?: boolean;

    /**
     * Hide request sample tab
     */
    hideSchemaTitles?: boolean;

    /**
     * Disable sticky sidebar
     */
    disableStickySidebar?: boolean;

    /**
     * Generate unique operation IDs
     */
    generateCodeSamples?: {
      languages: Array<{
        lang: string;
        label: string;
        source: (operation: any) => string;
      }>;
    };

    /**
     * Theme
     */
    theme?: {
      spacing?: {
        unit?: number;
        sectionHorizontal?: number;
        sectionVertical?: number;
      };
      breakpoints?: {
        small?: string;
        medium?: string;
        large?: string;
      };
      colors?: {
        primary?: {
          main?: string;
          light?: string;
          dark?: string;
          contrastText?: string;
        };
        success?: {
          main?: string;
          light?: string;
          dark?: string;
          contrastText?: string;
        };
        warning?: {
          main?: string;
          light?: string;
          dark?: string;
          contrastText?: string;
        };
        error?: {
          main?: string;
          light?: string;
          dark?: string;
          contrastText?: string;
        };
        gray?: {
          50?: string;
          100?: string;
          200?: string;
          300?: string;
          400?: string;
          500?: string;
          600?: string;
          700?: string;
          800?: string;
          900?: string;
        };
        text?: {
          primary?: string;
          secondary?: string;
        };
        border?: {
          dark?: string;
          light?: string;
        };
        responses?: {
          success?: {
            color?: string;
            backgroundColor?: string;
          };
          error?: {
            color?: string;
            backgroundColor?: string;
          };
          redirect?: {
            color?: string;
            backgroundColor?: string;
          };
          info?: {
            color?: string;
            backgroundColor?: string;
          };
        };
        http?: {
          get?: string;
          post?: string;
          put?: string;
          options?: string;
          patch?: string;
          delete?: string;
          basic?: string;
          link?: string;
          head?: string;
        };
      };
      typography?: {
        fontSize?: string;
        lineHeight?: string;
        fontWeightRegular?: string;
        fontWeightBold?: string;
        fontWeightLight?: string;
        fontFamily?: string;
        smoothing?: string;
        optimizeSpeed?: boolean;
        headings?: {
          fontFamily?: string;
          fontWeight?: string;
          lineHeight?: string;
        };
        code?: {
          fontSize?: string;
          fontFamily?: string;
          lineHeight?: string;
          fontWeight?: string;
          color?: string;
          backgroundColor?: string;
          wrap?: boolean;
        };
        links?: {
          color?: string;
          visited?: string;
          hover?: string;
          textDecoration?: string;
          hoverTextDecoration?: string;
        };
      };
      sidebar?: {
        width?: string;
        backgroundColor?: string;
        textColor?: string;
        activeTextColor?: string;
        groupItems?: {
          textTransform?: string;
        };
        level1Items?: {
          textTransform?: string;
        };
        arrow?: {
          size?: string;
          color?: string;
        };
      };
      logo?: {
        maxHeight?: string;
        maxWidth?: string;
        gutter?: string;
      };
      rightPanel?: {
        backgroundColor?: string;
        width?: string;
        textColor?: string;
      };
    };
  };

  /**
   * Custom styles to apply to ReDoc
   */
  style?: React.CSSProperties;

  /**
   * Custom class name to apply to ReDoc
   */
  className?: string;
}

/**
 * ReDoc UI component
 */
export const ReDocUI: React.FC<ReDocUIProps> = ({
  spec,
  options = {},
  style,
  className
}) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null;
  }

  return (
    <div style={style} className={className}>
      {/* 
        NOTE: The RedocStandalone component expects a 'spec' prop and other configuration options.
        TypeScript errors will be resolved once the redoc package is installed.
      */}
      <RedocStandalone
        spec={spec}
        options={{
          hideHostname: false,
          hideDownloadButton: false,
          noAutoAuth: false,
          disableSearch: false,
          requiredPropsFirst: true,
          sortPropsAlphabetically: false,
          pathInMiddlePanel: false,
          hideSchemaTitles: false,
          disableStickySidebar: false,
          ...options
        }}
      />
    </div>
  );
};

/**
 * API documentation page component using ReDoc
 */
export const ApiDocumentationPageReDoc: React.FC<{
  title?: string;
  description?: string;
  apiUrl?: string;
}> = ({ title = 'API Documentation', description, apiUrl = '/api/docs' }) => {
  const [spec, setSpec] = useState<OpenAPIDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchSpec = async () => {
      try {
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch API documentation: ${response.statusText}`);
        }
        const data = await response.json();
        setSpec(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setLoading(false);
      }
    };

    fetchSpec();
  }, [apiUrl]);

  if (loading) {
    return (
      <div className="api-docs-loading">
        <h1>{title}</h1>
        {description && <p>{description}</p>}
        <p>Loading API documentation...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="api-docs-error">
        <h1>{title}</h1>
        {description && <p>{description}</p>}
        <p>Error loading API documentation: {error.message}</p>
      </div>
    );
  }

  if (!spec) {
    return (
      <div className="api-docs-error">
        <h1>{title}</h1>
        {description && <p>{description}</p>}
        <p>No API documentation available.</p>
      </div>
    );
  }

  return (
    <div className="api-docs">
      <ReDocUI spec={spec} />
    </div>
  );
};

/**
 * Example usage:
 * 
 * ```tsx
 * // pages/api/docs.ts
 * import { NextApiRequest, NextApiResponse } from 'next';
 * import { openApiMiddleware } from '../../lib/api/openapi-documentation';
 * 
 * export default function handler(req: NextApiRequest, res: NextApiResponse) {
 *   return openApiMiddleware({
 *     title: 'My API',
 *     description: 'API documentation',
 *     version: '1.0.0',
 *     servers: [
 *       {
 *         url: 'https://api.example.com',
 *         description: 'Production server'
 *       },
 *       {
 *         url: 'https://staging.example.com',
 *         description: 'Staging server'
 *       }
 *     ]
 *   })(req, res);
 * }
 * 
 * // pages/docs.tsx
 * import { ApiDocumentationPageReDoc } from '../../lib/api/redoc-ui';
 * 
 * export default function DocsPage() {
 *   return (
 *     <ApiDocumentationPageReDoc
 *       title="API Documentation"
 *       description="Documentation for the My API"
 *       apiUrl="/api/docs"
 *     />
 *   );
 * }
 * ```
 */