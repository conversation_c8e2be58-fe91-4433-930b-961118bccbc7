'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth-context'
import { supabase } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Plus, Users, Shield } from 'lucide-react'
import PermissionGate from './permission-gate'

interface Role {
  id: string
  name: string
  description: string | null
  permissions: any
  created_at: string
  updated_at: string
}

interface Staff {
  id: string
  name_jp: string
  name_en: string
  email: string
  division: { name_jp: string; name_en: string } | null
  role: { name: string; description: string | null } | null
}

export default function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([])
  const [staff, setStaff] = useState<Staff[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStaff, setSelectedStaff] = useState<string>('')
  const [selectedRole, setSelectedRole] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')
  const { user } = useAuth()

  useEffect(() => {
    loadRoles()
    loadStaff()
  }, [])

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name')
      
      if (error) throw error
      setRoles(data || [])
    } catch (err: any) {
      setError('Failed to load roles: ' + err.message)
    }
  }
