"use strict";exports.id=9994,exports.ids=[9994],exports.modules={29374:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},41675:(e,t,n)=>{n.d(t,{qW:()=>d});var r,i=n(58009),o=n(31412),l=n(30830),a=n(29952),s=n(92828),u=n(45512),f="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:y,onDismiss:w,...v}=e,x=i.useContext(c),[b,E]=i.useState(null),R=b?.ownerDocument??globalThis?.document,[,A]=i.useState({}),L=(0,a.s)(t,e=>E(e)),C=Array.from(x.layers),[P]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),O=C.indexOf(P),T=b?C.indexOf(b):-1,S=x.layersWithOutsidePointerEventsDisabled.size>0,D=T>=O,k=function(e,t=globalThis?.document){let n=(0,s.c)(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));!D||n||(m?.(e),y?.(e),e.defaultPrevented||w?.())},R),W=function(e,t=globalThis?.document){let n=(0,s.c)(e),r=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(g?.(e),y?.(e),e.defaultPrevented||w?.())},R);return function(e,t=globalThis?.document){let n=(0,s.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T!==x.layers.size-1||(d?.(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},R),i.useEffect(()=>{if(b)return n&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(b)),x.layers.add(b),p(),()=>{n&&1===x.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[b,R,n,x]),i.useEffect(()=>()=>{b&&(x.layers.delete(b),x.layersWithOutsidePointerEventsDisabled.delete(b),p())},[b,x]),i.useEffect(()=>{let e=()=>A({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,u.jsx)(l.sG.div,{...v,ref:L,style:{pointerEvents:S?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function p(){let e=new CustomEvent(f);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?(0,l.hO)(i,o):i.dispatchEvent(o)}d.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(c),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(l.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},53337:(e,t,n)=>{n.d(t,{Mz:()=>eZ,i3:()=>eK,UC:()=>eJ,bL:()=>eU,Bk:()=>eW});var r=n(58009);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),f={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>c[e])}function v(e){return e.replace(/left|right|bottom|top/g,e=>f[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),s=g(a),u=p(t),f="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[s]/2-o[s]/2;switch(u){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=w*(n&&f?-1:1);break;case"end":r[a]+=w*(n&&f?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:c}=E(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:v}=await m({x:f,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});f=null!=g?g:f,c=null!=y?y:c,p={...p,[o]:{...p[o],...w}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(u=!0===v.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):v.rects),{x:f,y:c}=E(u,d,s)),n=-1)}return{x:f,y:c,placement:d,strategy:i,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),g=a[p?"floating"===c?"reference":"floating":c],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:s})),w="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),E=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:v,strategy:s}):w);return{top:(y.top-R.top+m.top)/E.y,bottom:(R.bottom-y.bottom+m.bottom)/E.y,left:(y.left-R.left+m.left)/E.x,right:(R.right-y.right+m.right)/E.x}}function L(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function C(e){return i.some(t=>e[t]>=0)}async function P(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),s="y"===y(n),u=["left","top"].includes(l)?-1:1,f=o&&s?-1:1,c=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),s?{x:g*f,y:m*u}:{x:m*u,y:g*f}}function O(){return"undefined"!=typeof window}function T(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function S(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!O()&&(e instanceof Node||e instanceof S(e).Node)}function W(e){return!!O()&&(e instanceof Element||e instanceof S(e).Element)}function H(e){return!!O()&&(e instanceof HTMLElement||e instanceof S(e).HTMLElement)}function F(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof S(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=$(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function z(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=N(),n=W(e)?$(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function N(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function M(e){return["html","body","#document"].includes(T(e))}function $(e){return S(e).getComputedStyle(e)}function V(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function G(e){if("html"===T(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||D(e);return F(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=G(t);return M(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&j(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=S(i);if(o){let e=I(l);return t.concat(l,l.visualViewport||[],j(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function I(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Y(e){let t=$(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=H(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,s=a(n)!==o||a(r)!==l;return s&&(n=o,r=l),{width:n,height:r,$:s}}function q(e){return W(e)?e:e.contextElement}function _(e){let t=q(e);if(!H(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=Y(t),l=(o?a(n.width):n.width)/r,s=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let U=u(0);function Z(e){let t=S(e);return N()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function J(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=q(e),a=u(1);t&&(r?W(r)&&(a=_(r)):a=_(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===S(l))&&i)?Z(l):u(0),f=(o.left+s.x)/a.x,c=(o.top+s.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=S(l),t=r&&W(r)?S(r):r,n=e,i=I(n);for(;i&&r&&t!==n;){let e=_(i),t=i.getBoundingClientRect(),r=$(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;f*=e.x,c*=e.y,d*=e.x,p*=e.y,f+=o,c+=l,i=I(n=S(i))}}return b({width:d,height:p,x:f,y:c})}function K(e,t){let n=V(e).scrollLeft;return t?t.left+n:J(D(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=S(e),r=D(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,s=0;if(i){o=i.width,l=i.height;let e=N();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=V(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),s=-n.scrollTop;return"rtl"===$(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}(D(e));else if(W(t))r=function(e,t){let n=J(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=H(e)?_(e):u(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===$(e).position}function en(e,t){if(!H(e)||"fixed"===$(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=S(e);if(z(e))return n;if(!H(e)){let t=G(e);for(;t&&!M(t);){if(W(t)&&!et(t))return t;t=G(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(T(r))&&et(r);)r=en(r,t);return r&&M(r)&&et(r)&&!B(r)?n:r||function(e){let t=G(e);for(;H(t)&&!M(t);){if(B(t))return t;if(z(t))break;t=G(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),i=D(t),o="fixed"===n,l=J(e,!0,o,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!o){if(("body"!==T(t)||j(i))&&(a=V(t)),r){let e=J(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=K(i))}o&&!r&&i&&(s.x=K(i));let f=!i||r||o?u(0):Q(i,a);return{x:l.left+a.scrollLeft-s.x-f.x,y:l.top+a.scrollTop-s.y-f.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=D(r),a=!!t&&z(t.floating);if(r===l||a&&o)return n;let s={scrollLeft:0,scrollTop:0},f=u(1),c=u(0),d=H(r);if((d||!d&&!o)&&(("body"!==T(r)||j(l))&&(s=V(r)),H(r))){let e=J(r);f=_(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||o?u(0):Q(l,s,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-s.scrollLeft*f.x+c.x+p.x,y:n.y*f.y-s.scrollTop*f.y+c.y+p.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?z(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>W(e)&&"body"!==T(e)),i=null,o="fixed"===$(e).position,l=o?G(e):e;for(;W(l)&&!M(l);){let t=$(l),n=B(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||j(l)&&!n&&function e(t,n){let r=G(t);return!(r===n||!W(r)||M(r))&&("fixed"===$(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=G(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,s,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=Y(e);return{width:t,height:n}},getScale:_,isElement:W,isRTL:function(e){return"rtl"===$(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:s,elements:u,middlewareData:f}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let w=x(p),v={x:n,y:r},b=m(y(i)),E=g(b),R=await s.getDimensions(c),A="y"===b,L=A?"clientHeight":"clientWidth",C=a.reference[E]+a.reference[b]-v[b]-a.floating[E],P=v[b]-a.reference[b],O=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),T=O?O[L]:0;T&&await (null==s.isElement?void 0:s.isElement(O))||(T=u.floating[L]||a.floating[E]);let S=T/2-R[E]/2-1,D=o(w[A?"top":"left"],S),k=o(w[A?"bottom":"right"],S),W=T-R[E]-k,H=T/2-R[E]/2+(C/2-P/2),F=l(D,o(H,W)),j=!f.arrow&&null!=h(i)&&H!==F&&a.reference[E]/2-(H<D?D:k)-R[E]/2<0,z=j?H<D?H-D:H-W:0;return{[b]:v[b]+z,data:{[b]:F,centerOffset:H-F-z,...j&&{alignmentOffset:z}},reset:j}}}),es=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var eu=n(55740),ef="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ec(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ec(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ec(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ef(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,s=await P(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+s.x,y:o+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=d(e,t),c={x:n,y:r},h=await A(t,f),g=y(p(i)),w=m(g),v=c[w],x=c[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}if(s){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=u.fn({...t,[w]:v,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:s}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=d(e,t),f={x:n,y:r},c=y(i),h=m(c),g=f[h],w=f[c],v=d(a,t),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(s){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:x.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[c])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[c]:w}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l,a;let{placement:s,middlewareData:u,rects:f,initialPlacement:c,platform:x,elements:b}=t,{mainAxis:E=!0,crossAxis:R=!0,fallbackPlacements:L,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:O=!0,...T}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let S=p(s),D=y(c),k=p(c)===c,W=await (null==x.isRTL?void 0:x.isRTL(b.floating)),H=L||(k||!O?[v(c)]:function(e){let t=v(e);return[w(e),t,w(t)]}(c)),F="none"!==P;!L&&F&&H.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(c,O,P,W));let j=[c,...H],z=await A(t,T),B=[],N=(null==(r=u.flip)?void 0:r.overflows)||[];if(E&&B.push(z[S]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=v(l)),[l,v(l)]}(s,f,W);B.push(z[e[0]],z[e[1]])}if(N=[...N,{placement:s,overflows:B}],!B.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=j[e];if(t){let n="alignment"===R&&D!==y(t),r=(null==(l=N[0])?void 0:l.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:N},reset:{placement:t}}}let n=null==(o=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let e=null==(a=N.filter(e=>{if(F){let t=y(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(s!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a;let{placement:s,rects:u,platform:f,elements:c}=t,{apply:m=()=>{},...g}=d(e,t),w=await A(t,g),v=p(s),x=h(s),b="y"===y(s),{width:E,height:R}=u.floating;"top"===v||"bottom"===v?(i=v,a=x===(await (null==f.isRTL?void 0:f.isRTL(c.floating))?"start":"end")?"left":"right"):(a=v,i="end"===x?"top":"bottom");let L=R-w.top-w.bottom,C=E-w.left-w.right,P=o(R-w[i],L),O=o(E-w[a],C),T=!t.middlewareData.shift,S=P,D=O;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=C),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=L),T&&!x){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:l(w.left,w.right)):S=R-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:D,availableHeight:S});let k=await f.getDimensions(c.floating);return E!==k.width||R!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=L(await A(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:C(e)}}}case"escaped":{let e=L(await A(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:C(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eR=n(30830),eA=n(45512),eL=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eR.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eL.displayName="Arrow";var eC=n(29952),eP=n(6004),eO=n(92828),eT=n(49397),eS=n(38762),eD="Popper",[ek,eW]=(0,eP.A)(eD),[eH,eF]=ek(eD),ej=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(eH,{scope:t,anchor:i,onAnchorChange:o,children:n})};ej.displayName=eD;var ez="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eF(ez,n),a=r.useRef(null),s=(0,eC.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(i?.current||a.current)}),i?null:(0,eA.jsx)(eR.sG.div,{...o,ref:s})});eB.displayName=ez;var eN="PopperContent",[eM,e$]=ek(eN),eV=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:u="center",alignOffset:f=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:w,...v}=e,x=eF(eN,n),[b,E]=r.useState(null),R=(0,eC.s)(t,e=>E(e)),[A,L]=r.useState(null),C=(0,eS.X)(A),P=C?.width??0,O=C?.height??0,T="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},S=Array.isArray(p)?p:[p],k=S.length>0,W={padding:T,boundary:S.filter(eY),altBoundary:k},{refs:H,floatingStyles:F,placement:j,isPositioned:z,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:f}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);ec(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),v=r.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),x=r.useCallback(e=>{e!==A.current&&(A.current=e,w(e))},[]),b=l||m,E=a||y,R=r.useRef(null),A=r.useRef(null),L=r.useRef(c),C=null!=u,P=eh(u),O=eh(o),T=eh(f),S=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};O.current&&(e.platform=O.current),es(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};D.current&&!ec(L.current,t)&&(L.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,O,T]);ef(()=>{!1===f&&L.current.isPositioned&&(L.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[f]);let D=r.useRef(!1);ef(()=>(D.current=!0,()=>{D.current=!1}),[]),ef(()=>{if(b&&(R.current=b),E&&(A.current=E),b&&E){if(P.current)return P.current(b,E,S);S()}},[b,E,S,P,C]);let k=r.useMemo(()=>({reference:R,floating:A,setReference:v,setFloating:x}),[v,x]),W=r.useMemo(()=>({reference:b,floating:E}),[b,E]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!W.floating)return e;let t=ep(W.floating,c.x),r=ep(W.floating,c.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(W.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,W.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:S,refs:k,elements:W,floatingStyles:H}),[c,S,k,W,H])}({strategy:"fixed",placement:i+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),h=a||u?[...p?X(p):[],...X(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&c?function(e,t){let n,r=null,i=D(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function u(f,c){void 0===f&&(f=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(f||t(),!m||!g)return;let y=s(h),w=s(i.clientWidth-(p+m)),v={rootMargin:-y+"px "+-w+"px "+-s(i.clientHeight-(h+g))+"px "+-s(p)+"px",threshold:l(0,o(1,c))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,v)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;f&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?J(e):null;return d&&function t(){let r=J(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===y}),elements:{reference:x.anchor},middleware:[eg({mainAxis:a+O,alignmentAxis:f}),d&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ew():void 0,...W}),d&&ev({...W}),ex({...W,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${i}px`),l.setProperty("--radix-popper-anchor-height",`${o}px`)}}),A&&eE({element:A,padding:c}),eq({arrowWidth:P,arrowHeight:O}),g&&eb({strategy:"referenceHidden",...W})]}),[N,M]=e_(j),$=(0,eO.c)(w);(0,eT.N)(()=>{z&&$?.()},[z,$]);let V=B.arrow?.x,G=B.arrow?.y,I=B.arrow?.centerOffset!==0,[Y,_]=r.useState();return(0,eT.N)(()=>{b&&_(window.getComputedStyle(b).zIndex)},[b]),(0,eA.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:z?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eM,{scope:n,placedSide:N,onArrowChange:L,arrowX:V,arrowY:G,shouldHideArrow:I,children:(0,eA.jsx)(eR.sG.div,{"data-side":N,"data-align":M,...v,ref:R,style:{...v.style,animation:z?void 0:"none"}})})})});eV.displayName=eN;var eG="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eI=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e$(eG,n),o=eX[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eL,{...r,ref:t,style:{...r.style,display:"block"}})})});function eY(e){return null!==e}eI.displayName=eG;var eq=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,l=o?0:e.arrowWidth,a=o?0:e.arrowHeight,[s,u]=e_(n),f={start:"0%",center:"50%",end:"100%"}[u],c=(i.arrow?.x??0)+l/2,d=(i.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=o?f:`${c}px`,h=`${-a}px`):"top"===s?(p=o?f:`${c}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=o?f:`${d}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=o?f:`${d}px`),{data:{x:p,y:h}}}});function e_(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=ej,eZ=eB,eJ=eV,eK=eI},80707:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(58009),i=n(55740),o=n(30830),l=n(49397),a=n(45512),s=r.forwardRef((e,t)=>{let{container:n,...s}=e,[u,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let c=n||u&&globalThis?.document?.body;return c?i.createPortal((0,a.jsx)(o.sG.div,{...s,ref:t}),c):null});s.displayName="Portal"},38762:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(58009),i=n(49397);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};