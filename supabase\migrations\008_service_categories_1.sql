-- Insert service categories
INSERT INTO service_categories (name_jp, name_en, code, description, is_department_specific, form_schema, is_active) VALUES
('グループメール', 'Group Mail', 'GM', 'Request to add/remove staff to/from group mail addresses', true, '{
  "fields": [
    {
      "name": "action_type",
      "type": "select",
      "label": "Action Type",
      "options": ["add", "remove"],
      "required": true
    },
    {
      "name": "group_mail_addresses",
      "type": "multi_select",
      "label": "Group Mail Addresses",
      "source": "group_mail_addresses",
      "required": true
    },
    {
      "name": "affected_users",
      "type": "multi_select",
      "label": "Staff Members",
      "source": "staff_by_department",
      "required": true
    }
  ]
}', true),

('メールボックス', 'Mailbox', 'MB', 'Request to add/remove staff to/from mailbox addresses', true, '{
  "fields": [
    {
      "name": "action_type",
      "type": "select",
      "label": "Action Type",
      "options": ["add", "remove"],
      "required": true
    },
    {
      "name": "mailbox_addresses",
      "type": "multi_select",
      "label": "Mailbox Addresses",
      "source": "mailbox_addresses",
      "required": true
    },
    {
      "name": "affected_users",
      "type": "multi_select",
      "label": "Staff Members",
      "source": "staff_by_department",
      "required": true
    }
  ]
}', true);
