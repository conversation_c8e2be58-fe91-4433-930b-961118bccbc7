(()=>{var e={};e.id=4253,e.ids=[4253,7344],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},1533:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var r={};a.r(r),a.d(r,{POST:()=>l});var s=a(42706),o=a(28203),i=a(45994),c=a(39187),n=a(37344),u=a(61487);async function l(e,{params:t}){try{let a=await d();if(a)return a;let r=t.id,{verify_checksum:s=!0,dry_run:o=!1,tables:i}=await e.json(),u=await n.w.restoreFromBackup(r,{verify_checksum:s,dry_run:o,tables:i});return c.NextResponse.json({success:u.success,message:u.message,restored_records:u.restored_records})}catch(e){return console.error("Backup restoration failed:",e),c.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(){let e=(0,u.U)(),{data:{user:t},error:a}=await e.auth.getUser();if(a||!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r}=await e.from("user_profiles").select("role").eq("user_id",t.id).single();return r&&["admin","system_admin"].includes(r.role)?null:c.NextResponse.json({error:"Insufficient permissions"},{status:403})}let p=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/backup/[id]/restore/route",pathname:"/api/admin/backup/[id]/restore",filename:"route",bundlePath:"app/api/admin/backup/[id]/restore/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\[id]\\restore\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:h}=p;function f(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},37344:(e,t,a)=>{"use strict";a.d(t,{BackupManager:()=>o,w:()=>i});var r=a(61487),s=a(55511);void 0===globalThis.crypto&&(globalThis.crypto=s.webcrypto);class o{constructor(e){this.supabase=(0,r.U)(),this.config=e}async createBackup(e){let t=`backup_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,a=new Date().toISOString();try{await this.logBackupEvent(t,"started","Full backup initiated");let e=await this.getBackupTables(),r=0,s=0,o={};for(let a of e)try{let{data:e,error:i}=await this.supabase.from(a).select("*");if(i)throw Error(`Failed to backup table ${a}: ${i.message}`);o[a]=e||[],r+=e?.length||0,s+=JSON.stringify(e).length,await this.logBackupEvent(t,"progress",`Backed up table: ${a} (${e?.length||0} records)`)}catch(e){throw await this.logBackupEvent(t,"error",`Failed to backup table ${a}: ${e}`),e}let i=await this.calculateChecksum(JSON.stringify(o)),c={id:t,timestamp:a,size_bytes:s,tables_count:e.length,records_count:r,checksum:i,status:"completed"};return await this.storeBackupData(t,o,c),await this.logBackupEvent(t,"completed",`Backup completed successfully. Size: ${this.formatBytes(s)}`),c}catch(e){return await this.logBackupEvent(t,"failed",`Backup failed: ${e}`),{id:t,timestamp:a,size_bytes:0,tables_count:0,records_count:0,checksum:"",status:"failed",error_message:e instanceof Error?e.message:"Unknown error"}}}async restoreFromBackup(e,t={}){try{if(!await this.getBackupMetadata(e))throw Error(`Backup ${e} not found`);if(t.verify_checksum&&!await this.verifyBackupIntegrity(e))throw Error("Backup integrity check failed");let a=await this.getBackupData(e);if(!a)throw Error("Backup data not found");let r=0;for(let s of t.tables||Object.keys(a))if(a[s]){if(t.dry_run){console.log(`Would restore ${a[s].length} records to table: ${s}`),r+=a[s].length;continue}try{if(t.dry_run||await this.supabase.from(s).delete().neq("id","00000000-0000-0000-0000-000000000000"),a[s].length>0){let{error:e}=await this.supabase.from(s).insert(a[s]);if(e)throw Error(`Failed to restore table ${s}: ${e.message}`);r+=a[s].length}await this.logBackupEvent(e,"restore_progress",`Restored table: ${s} (${a[s].length} records)`)}catch(t){throw await this.logBackupEvent(e,"restore_error",`Failed to restore table ${s}: ${t}`),t}}let s=t.dry_run?`Dry run completed. Would restore ${r} records`:`Restore completed successfully. Restored ${r} records`;return await this.logBackupEvent(e,"restore_completed",s),{success:!0,message:s,restored_records:r}}catch(a){let t=`Restore failed: ${a instanceof Error?a.message:"Unknown error"}`;return await this.logBackupEvent(e,"restore_failed",t),{success:!1,message:t}}}async listBackups(e=50){let{data:t,error:a}=await this.supabase.from("backup_metadata").select("*").order("timestamp",{ascending:!1}).limit(e);if(a)throw Error(`Failed to list backups: ${a.message}`);return t||[]}async verifyBackupIntegrity(e){try{let t=await this.getBackupMetadata(e);if(!t)return!1;let a=await this.getBackupData(e);if(!a)return!1;return await this.calculateChecksum(JSON.stringify(a))===t.checksum}catch(e){return console.error("Backup verification failed:",e),!1}}async cleanupOldBackups(){let e=new Date;e.setDate(e.getDate()-this.config.retention_days);let{data:t,error:a}=await this.supabase.from("backup_metadata").select("*").lt("timestamp",e.toISOString());if(a)throw Error(`Failed to query old backups: ${a.message}`);let r=0,s=0;for(let e of t||[])try{await this.deleteBackup(e.id),r++,s+=e.size_bytes}catch(t){console.error(`Failed to delete backup ${e.id}:`,t)}return{deleted_count:r,freed_bytes:s}}async getBackupTables(){let{data:e,error:t}=await this.supabase.from("information_schema.tables").select("table_name").eq("table_schema","public").neq("table_name","backup_metadata").neq("table_name","backup_logs");return t?["users","user_profiles","organizations","divisions","groups","tickets","ticket_comments","ticket_attachments","forms","form_submissions","form_fields","audit_logs","rate_limit_logs"].filter(e=>!this.config.exclude_tables?.includes(e)):(e?.map(e=>e.table_name)||[]).filter(e=>!this.config.exclude_tables?.includes(e))}async storeBackupData(e,t,a){await this.supabase.from("backup_metadata").insert(a),console.log(`Backup ${e} would be stored to external storage`)}async getBackupData(e){return console.log(`Would retrieve backup data for ${e} from external storage`),null}async getBackupMetadata(e){let{data:t,error:a}=await this.supabase.from("backup_metadata").select("*").eq("id",e).single();return a?null:t}async deleteBackup(e){await this.supabase.from("backup_metadata").delete().eq("id",e),console.log(`Would delete backup data for ${e} from external storage`)}async calculateChecksum(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("")}async logBackupEvent(e,t,a){try{await this.supabase.from("backup_logs").insert({backup_id:e,event:t,message:a,timestamp:new Date().toISOString()})}catch(e){console.error("Failed to log backup event:",e)}}formatBytes(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}}let i=new o({enabled:!0,schedule:"0 2 * * *",retention_days:30,encryption_enabled:!0,compression_enabled:!0,include_files:!1,exclude_tables:["rate_limit_logs","backup_logs"]})},61487:(e,t,a)=>{"use strict";a.d(t,{U:()=>o});var r=a(49064),s=a(44512);let o=()=>{let e=(0,s.UL)();return(0,r.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:a,options:r})=>e.set(t,a,r))}catch{}}}})}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[5994,5452,4512,9064],()=>a(1533));module.exports=r})();