"use strict";(()=>{var e={};e.id=4253,e.ids=[4253],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{e.exports=require("crypto")},22011:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>k,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{POST:()=>d});var a=s(42706),o=s(28203),n=s(45994),i=s(39187),u=s(37344),p=s(61487);async function d(e,{params:r}){try{let s=await c();if(s)return s;let t=r.id,{verify_checksum:a=!0,dry_run:o=!1,tables:n}=await e.json(),p=await u.w.restoreFromBackup(t,{verify_checksum:a,dry_run:o,tables:n});return i.NextResponse.json({success:p.success,message:p.message,restored_records:p.restored_records})}catch(e){return console.error("Backup restoration failed:",e),i.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function c(){let e=(0,p.U)(),{data:{user:r},error:s}=await e.auth.getUser();if(s||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:t}=await e.from("user_profiles").select("role").eq("user_id",r.id).single();return t&&["admin","system_admin"].includes(t.role)?null:i.NextResponse.json({error:"Insufficient permissions"},{status:403})}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/backup/[id]/restore/route",pathname:"/api/admin/backup/[id]/restore",filename:"route",bundlePath:"app/api/admin/backup/[id]/restore/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\[id]\\restore\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=l;function k(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8096,2076],()=>s(22011));module.exports=t})();