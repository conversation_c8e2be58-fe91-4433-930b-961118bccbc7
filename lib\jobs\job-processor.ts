/**
 * Background Job Processing System
 * Handles heavy operations like backups, AI processing, and bulk operations
 */

import { getRedisClient } from '@/lib/cache/redis-client'
import { metrics } from '@/lib/monitoring/metrics'

interface Job {
  id: string
  type: string
  data: any
  priority: number
  attempts: number
  maxAttempts: number
  delay: number
  createdAt: number
  processedAt?: number
  completedAt?: number
  failedAt?: number
  error?: string
  result?: any
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying'
}

interface JobOptions {
  priority?: number
  delay?: number
  maxAttempts?: number
  timeout?: number
  retryDelay?: number
}

interface JobHandler {
  (job: Job): Promise<any>
}

interface QueueStats {
  pending: number
  processing: number
  completed: number
  failed: number
  retrying: number
}

class JobProcessor {
  private redis = getRedisClient()
  private handlers = new Map<string, JobHandler>()
  private processing = new Map<string, Job>()
  private isRunning = false
  private concurrency = 5
  private pollInterval = 1000 // 1 second
  private defaultTimeout = 30000 // 30 seconds

  constructor(options: { concurrency?: number; pollInterval?: number } = {}) {
    this.concurrency = options.concurrency || 5
    this.pollInterval = options.pollInterval || 1000
  }

  /**
   * Register a job handler
   */
  registerHandler(jobType: string, handler: JobHandler): void {
    this.handlers.set(jobType, handler)
  }

  /**
   * Add a job to the queue
   */
  async addJob(
    type: string,
    data: any,
    options: JobOptions = {}
  ): Promise<string> {
    const job: Job = {
      id: this.generateJobId(),
      type,
      data,
      priority: options.priority || 0,
      attempts: 0,
      maxAttempts: options.maxAttempts || 3,
      delay: options.delay || 0,
      createdAt: Date.now(),
      status: 'pending'
    }

    // Add to Redis queue
    const queueKey = this.getQueueKey(job.priority)
    const jobData = JSON.stringify(job)
    
    if (job.delay > 0) {
      // Schedule for later
      const executeAt = Date.now() + job.delay
      await this.redis.set(`job:delayed:${job.id}`, jobData)
      await this.redis.expire(`job:delayed:${job.id}`, Math.ceil(job.delay / 1000))
    } else {
      // Add to immediate queue
      await this.redis.set(`job:${job.id}`, jobData)
      await this.redis.set(queueKey, job.id)
    }

    metrics.increment('jobs.added', { type, priority: job.priority.toString() })
    
    return job.id
  }

  /**
   * Start processing jobs
   */
  async start(): Promise<void> {
    if (this.isRunning) return

    this.isRunning = true
    console.log(`Job processor started with concurrency: ${this.concurrency}`)

    // Start worker processes
    const workers = Array.from({ length: this.concurrency }, (_, i) => 
      this.workerLoop(i)
    )

    // Start delayed job scheduler
    const scheduler = this.delayedJobScheduler()

    await Promise.all([...workers, scheduler])
  }

  /**
   * Stop processing jobs
   */
  async stop(): Promise<void> {
    this.isRunning = false
    
    // Wait for current jobs to complete
    while (this.processing.size > 0) {
      await this.delay(100)
    }

    console.log('Job processor stopped')
  }

  /**
   * Get job status
   */
  async getJob(jobId: string): Promise<Job | null> {
    try {
      const jobData = await this.redis.get(`job:${jobId}`)
      return jobData ? JSON.parse(jobData) : null
    } catch (error) {
      console.error('Error getting job:', error)
      return null
    }
  }

  /**
   * Get queue statistics
   */
  async getStats(): Promise<QueueStats> {
    // This is a simplified implementation
    // In production, you'd maintain counters in Redis
    return {
      pending: 0, // Would query Redis for pending jobs
      processing: this.processing.size,
      completed: 0, // Would query Redis for completed jobs
      failed: 0, // Would query Redis for failed jobs
      retrying: 0 // Would query Redis for retrying jobs
    }
  }

  /**
   * Worker loop for processing jobs
   */
  private async workerLoop(workerId: number): Promise<void> {
    console.log(`Worker ${workerId} started`)

    while (this.isRunning) {
      try {
        const job = await this.getNextJob()
        
        if (job) {
          await this.processJob(job, workerId)
        } else {
          // No jobs available, wait before polling again
          await this.delay(this.pollInterval)
        }
      } catch (error) {
        console.error(`Worker ${workerId} error:`, error)
        await this.delay(this.pollInterval)
      }
    }

    console.log(`Worker ${workerId} stopped`)
  }

  /**
   * Get next job from queue
   */
  private async getNextJob(): Promise<Job | null> {
    // Try high priority queue first, then normal priority
    const queues = ['queue:high', 'queue:normal', 'queue:low']
    
    for (const queueKey of queues) {
      try {
        const jobId = await this.redis.get(queueKey)
        if (jobId) {
          await this.redis.del(queueKey)
          const jobData = await this.redis.get(`job:${jobId}`)
          
          if (jobData) {
            const job: Job = JSON.parse(jobData)
            return job
          }
        }
      } catch (error) {
        console.error('Error getting next job:', error)
      }
    }

    return null
  }

  /**
   * Process a single job
   */
  private async processJob(job: Job, workerId: number): Promise<void> {
    const startTime = Date.now()
    
    try {
      // Mark job as processing
      job.status = 'processing'
      job.processedAt = startTime
      job.attempts++
      
      this.processing.set(job.id, job)
      await this.updateJob(job)

      console.log(`Worker ${workerId} processing job ${job.id} (${job.type})`)

      // Get handler for job type
      const handler = this.handlers.get(job.type)
      if (!handler) {
        throw new Error(`No handler registered for job type: ${job.type}`)
      }

      // Execute job with timeout
      const result = await this.executeWithTimeout(
        () => handler(job),
        this.defaultTimeout
      )

      // Job completed successfully
      job.status = 'completed'
      job.completedAt = Date.now()
      job.result = result

      const duration = Date.now() - startTime
      metrics.timing('jobs.processing_time', duration, { 
        type: job.type, 
        status: 'completed' 
      })
      metrics.increment('jobs.completed', { type: job.type })

      console.log(`Job ${job.id} completed in ${duration}ms`)

    } catch (error) {
      // Job failed
      const duration = Date.now() - startTime
      job.error = error instanceof Error ? error.message : String(error)
      
      if (job.attempts < job.maxAttempts) {
        // Retry job
        job.status = 'retrying'
        const retryDelay = Math.pow(2, job.attempts) * 1000 // Exponential backoff
        
        setTimeout(async () => {
          await this.addJob(job.type, job.data, {
            priority: job.priority,
            maxAttempts: job.maxAttempts,
            delay: retryDelay
          })
        }, retryDelay)

        metrics.increment('jobs.retried', { type: job.type })
        console.log(`Job ${job.id} will retry in ${retryDelay}ms (attempt ${job.attempts}/${job.maxAttempts})`)
      } else {
        // Job failed permanently
        job.status = 'failed'
        job.failedAt = Date.now()
        
        metrics.increment('jobs.failed', { type: job.type })
        console.error(`Job ${job.id} failed permanently:`, error)
      }

      metrics.timing('jobs.processing_time', duration, { 
        type: job.type, 
        status: job.status 
      })
    } finally {
      // Update job and remove from processing
      await this.updateJob(job)
      this.processing.delete(job.id)
    }
  }

  /**
   * Delayed job scheduler
   */
  private async delayedJobScheduler(): Promise<void> {
    while (this.isRunning) {
      try {
        // This is a simplified implementation
        // In production, you'd use Redis sorted sets for delayed jobs
        await this.delay(5000) // Check every 5 seconds
      } catch (error) {
        console.error('Delayed job scheduler error:', error)
        await this.delay(5000)
      }
    }
  }

  /**
   * Update job in Redis
   */
  private async updateJob(job: Job): Promise<void> {
    try {
      await this.redis.set(`job:${job.id}`, JSON.stringify(job))
      
      // Set TTL for completed/failed jobs
      if (job.status === 'completed' || job.status === 'failed') {
        await this.redis.expire(`job:${job.id}`, 86400) // 24 hours
      }
    } catch (error) {
      console.error('Error updating job:', error)
    }
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return Promise.race([
      fn(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Job timeout')), timeout)
      )
    ])
  }

  // Helper methods
  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getQueueKey(priority: number): string {
    if (priority > 5) return 'queue:high'
    if (priority < -5) return 'queue:low'
    return 'queue:normal'
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Job type definitions and handlers
export const JobTypes = {
  BACKUP_CREATE: 'backup:create',
  BACKUP_RESTORE: 'backup:restore',
  AI_BATCH_PROCESS: 'ai:batch_process',
  EMAIL_SEND: 'email:send',
  REPORT_GENERATE: 'report:generate',
  DATA_EXPORT: 'data:export',
  CLEANUP_OLD_DATA: 'cleanup:old_data'
} as const

// Singleton instance
let jobProcessor: JobProcessor | null = null

export const getJobProcessor = (): JobProcessor => {
  if (!jobProcessor) {
    jobProcessor = new JobProcessor()
  }
  return jobProcessor
}

// Register default handlers
const processor = getJobProcessor()

// Backup job handlers
processor.registerHandler(JobTypes.BACKUP_CREATE, async (job) => {
  const { BackupManager } = await import('@/lib/backup/backup-manager')
  const backupManager = new BackupManager()
  return backupManager.createBackup(job.data.description)
})

processor.registerHandler(JobTypes.BACKUP_RESTORE, async (job) => {
  const { BackupManager } = await import('@/lib/backup/backup-manager')
  const backupManager = new BackupManager()
  return backupManager.restoreFromBackup(job.data.backupId, job.data.options)
})

// AI batch processing handler
processor.registerHandler(JobTypes.AI_BATCH_PROCESS, async (job) => {
  const { getAIOptimizer } = await import('@/lib/ai/ai-performance-optimizer')
  const aiOptimizer = getAIOptimizer()
  
  const results = []
  for (const request of job.data.requests) {
    const result = await aiOptimizer.request(
      request.provider,
      request.model,
      request.prompt,
      request.options,
      'low' // Batch jobs are low priority
    )
    results.push(result)
  }
  
  return results
})

export { JobProcessor }
export type { Job, JobOptions, JobHandler, QueueStats }
