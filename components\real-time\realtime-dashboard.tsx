"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Activity, Users, FileText, Clock } from 'lucide-react';
import { enhancedRealtimeService } from '@/lib/services/enhanced-realtime-service';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

interface DashboardMetrics {
  totalRequests: number;
  pendingRequests: number;
  completedToday: number;
  activeUsers: number;
}

export function RealtimeDashboard() {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalRequests: 0,
    pendingRequests: 0,
    completedToday: 0,
    activeUsers: 0
  });

  const [recentRequests, setRecentRequests] = useState<any[]>([]);

  useEffect(() => {
    // Initial data fetch
    fetchMetrics();
    fetchRecentRequests();

    // Subscribe to real-time updates
    const requestSub = enhancedRealtimeService.subscribeToDepartmentRequests(
      handleRequestUpdate
    );

    return () => {
      enhancedRealtimeService.unsubscribe(requestSub, 'department-requests');
    };
  }, []);

  const fetchMetrics = async () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [totalResult, pendingResult, completedResult] = await Promise.all([
      supabase.from('request_forms').select('id', { count: 'exact' }),
      supabase.from('request_forms').select('id', { count: 'exact' }).eq('status', 'pending'),
      supabase.from('request_forms').select('id', { count: 'exact' })
        .eq('status', 'completed')
        .gte('completed_at', today.toISOString())
    ]);

    setMetrics(prev => ({
      ...prev,
      totalRequests: totalResult.count || 0,
      pendingRequests: pendingResult.count || 0,
      completedToday: completedResult.count || 0
    }));
  };

  const fetchRecentRequests = async () => {
    const { data } = await supabase
      .from('request_forms')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);

    if (data) {
      setRecentRequests(data);
    }
  };

  const handleRequestUpdate = (payload: RealtimePostgresChangesPayload<any>) => {
    // Update metrics based on the event
    fetchMetrics();
    
    // Update recent requests
    if (payload.eventType === 'INSERT' && payload.new) {
      setRecentRequests(prev => [payload.new, ...prev.slice(0, 4)]);
    } else if (payload.eventType === 'UPDATE' && payload.new) {
      setRecentRequests(prev => 
        prev.map(req => req.id === payload.new.id ? payload.new : req)
      );
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              総リクエスト数 / Total Requests
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalRequests}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              保留中 / Pending
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.pendingRequests}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              本日完了 / Completed Today
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.completedToday}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              アクティブユーザー / Active Users
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeUsers}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>最近のリクエスト / Recent Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentRequests.length === 0 ? (
              <p className="text-sm text-muted-foreground">
                リクエストがありません / No requests
              </p>
            ) : (
              recentRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{request.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(request.created_at).toLocaleString('ja-JP')}
                    </p>
                  </div>
                  <Badge variant={
                    request.status === 'completed' ? 'default' :
                    request.status === 'pending' ? 'secondary' :
                    'outline'
                  }>
                    {request.status}
                  </Badge>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
