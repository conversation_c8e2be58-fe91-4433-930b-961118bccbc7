'use client'

import { useState, useEffect } from 'react'
import { FormSchema, FormData, FormState } from '@/lib/form-types'
import { aiFormAssistant } from '@/lib/ai-form-assistant'
import { EnhancedDynamicField } from './enhanced-dynamic-field'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Sparkles, CheckCircle, AlertCircle, HelpCircle } from 'lucide-react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { KBIntegrationWidget } from '@/components/knowledge-base/kb-integration-widget'
import { HelpDeskKBIntegration } from '@/components/knowledge-base/helpdesk-kb-integration'

interface DynamicFormProps {
  schema: FormSchema
  initialData?: FormData
  onSubmit: (data: FormData) => void
  onCancel?: () => void
  loading?: boolean
  aiEnabled?: boolean
  departmentId?: string
}

export default function DynamicForm({
  schema,
  initialData = {},
  onSubmit,
  onCancel,
  loading = false,
  aiEnabled = true,
  departmentId
}: DynamicFormProps) {
  const [formState, setFormState] = useState<FormState>({
    schema,
    data: initialData,
    errors: {},
    loading: false,
    aiSuggestions: {}
  })
  const [currentSection, setCurrentSection] = useState(0)
  const [completedSections, setCompletedSections] = useState<Set<number>>(new Set())
  const [showHelpDialog, setShowHelpDialog] = useState(false)

  useEffect(() => {
    // Convert service category form_schema to FormSchema format
    const convertedSchema: FormSchema = {
      ...schema,
      sections: schema.sections || [
        {
          id: 'main',
          title: schema.name || 'Request Details',
          titleJp: schema.nameJp || 'リクエスト詳細',
          fields: (schema.fields || []).map(field => ({
            ...field,
            id: field.name || field.id || '',
            type: field.type === 'select' ? 'dropdown' : 
                  field.type === 'multi_select' ? 'multiselect' : field.type
          }))
        }
      ]
    }

    setFormState(prev => ({
      ...prev,
      schema: convertedSchema,
      data: { ...initialData }
    }))
  }, [schema, initialData])

  const updateField = (fieldId: string, value: any) => {
    setFormState(prev => ({
      ...prev,
      data: {
        ...prev.data,
        [fieldId]: value
      },
      errors: {
        ...prev.errors,
        [fieldId]: undefined // Clear error when field is updated
      }
    }))

    // Trigger AI suggestions if enabled
    if (aiEnabled) {
      triggerAiSuggestions(fieldId, value)
    }
  }

  const triggerAiSuggestions = async (fieldId: string, value: any) => {
    if (!aiEnabled) return
    
    try {
      const field = schema.sections
        .flatMap(section => section.fields)
        .find(f => f.id === fieldId)
      
      if (!field) return

      const suggestion = await aiFormAssistant.generateFieldSuggestions(
        field, 
        value, 
        {
          userId: 'current-user-id', // Would get from auth context
          departmentId,
          userRole: 'regular_user', // Would get from auth context
          previousFormData: formState.data
        }
      )

      if (suggestion) {
        setFormState(prev => ({
          ...prev,
          aiSuggestions: {
            ...prev.aiSuggestions,
            [fieldId]: suggestion.suggestedValue
          }
        }))
      }
    } catch (error) {
      console.error('AI suggestion failed:', error)
    }
  }

  const validateSection = (sectionIndex: number): boolean => {
    const section = schema.sections[sectionIndex]
    const errors: Record<string, string> = {}
    let isValid = true

    section.fields.forEach(field => {
      if (field.required && !formState.data[field.id]) {
        errors[field.id] = `${field.labelJp || field.label}は必須です / ${field.label} is required`
        isValid = false
      }

      // Add custom validation logic here
      if (field.validation && formState.data[field.id]) {
        // Simple email validation example
        if (field.validation === 'email') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(formState.data[field.id])) {
            errors[field.id] = '有効なメールアドレスを入力してください / Please enter a valid email address'
            isValid = false
          }
        }
      }
    })

    setFormState(prev => ({
      ...prev,
      errors: { ...prev.errors, ...errors }
    }))

    return isValid
  }

  const handleNext = () => {
    if (validateSection(currentSection)) {
      setCompletedSections(prev => new Set([...prev, currentSection]))
      if (currentSection < schema.sections.length - 1) {
        setCurrentSection(currentSection + 1)
      }
    }
  }

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1)
    }
  }

  const handleSubmit = () => {
    // Validate all sections
    let allValid = true
    for (let i = 0; i < schema.sections.length; i++) {
      if (!validateSection(i)) {
        allValid = false
      }
    }

    if (allValid) {
      onSubmit(formState.data)
    }
  }

  const acceptAiSuggestion = (fieldId: string, suggestion: any) => {
    updateField(fieldId, suggestion)
    setFormState(prev => ({
      ...prev,
      aiSuggestions: {
        ...prev.aiSuggestions,
        [fieldId]: undefined
      }
    }))
  }

  const currentSectionData = schema.sections[currentSection]
  const progress = ((currentSection + 1) / schema.sections.length) * 100

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              {aiEnabled && <Sparkles className="mr-2 h-5 w-5 text-blue-500" />}
              {schema.nameJp || schema.name}
            </CardTitle>
            <CardDescription>
              {schema.descriptionJp || schema.description}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowHelpDialog(true)}
            >
              <HelpCircle className="mr-1 h-4 w-4" />
              ヘルプ / Help
            </Button>
            <Badge variant="outline">
              {currentSection + 1} / {schema.sections.length}
            </Badge>
          </div>
        </div>
        <Progress value={progress} className="w-full" />
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold">
            {currentSectionData.titleJp || currentSectionData.title}
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {currentSectionData.fields.map((field) => (
            <div key={field.id} className={field.type === 'textarea' ? 'md:col-span-2' : ''}>
              <EnhancedDynamicField
                field={field}
                value={formState.data[field.id]}
                onChange={(value) => updateField(field.id, value)}
                error={formState.errors[field.id]}
                disabled={loading}
                aiSuggestion={formState.aiSuggestions[field.id]}
                onAiAccept={acceptAiSuggestion}
                departmentId={departmentId}
              />
            </div>
          ))}
        </div>

        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            disabled={currentSection === 0 || loading}
          >
            前へ / Previous
          </Button>

          <div className="flex space-x-2">
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={loading}
              >
                キャンセル / Cancel
              </Button>
            )}

            {currentSection < schema.sections.length - 1 ? (
              <Button
                type="button"
                onClick={handleNext}
                disabled={loading}
              >
                次へ / Next
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                送信 / Submit
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
    
    {/* Help Dialog with KB Widget */}
    <Dialog open={showHelpDialog} onOpenChange={(open) => setShowHelpDialog(open)}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            ヘルプ & ナレッジベース / Help & Knowledge Base
          </DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <KBIntegrationWidget
            context={{
              serviceCategory: schema.name || 'general',
              currentStep: currentSectionData.title || 'form_section',
              formType: schema.name || 'form'
            }}
            className="w-full"
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
