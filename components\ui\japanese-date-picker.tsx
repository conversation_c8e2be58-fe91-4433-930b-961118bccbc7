// Japanese Date Picker Component with 和暦 Support
'use client';

import { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { formatJapaneseDate, toJapaneseEra } from '@/lib/i18n/config';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/i18n/language-provider';

interface JapaneseDatePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  showEra?: boolean;
}

export function JapaneseDatePicker({
  value,
  onChange,
  placeholder,
  showEra = true
}: JapaneseDatePickerProps) {
  const { locale } = useLanguage();
  const [date, setDate] = useState<Date | undefined>(value);

  const handleSelect = (newDate: Date | undefined) => {
    setDate(newDate);
    onChange?.(newDate);
  };

  const formatDisplay = (date: Date | undefined) => {
    if (!date) return placeholder || '日付を選択';
    
    if (locale === 'ja') {
      return showEra 
        ? formatJapaneseDate(date, true)
        : format(date, 'yyyy年MM月dd日', { locale: ja });
    }
    
    return format(date, 'PPP');
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-full justify-start text-left font-normal',
            !date && 'text-muted-foreground'
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formatDisplay(date)}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleSelect}
          locale={locale === 'ja' ? ja : undefined}
          initialFocus
        />
        {showEra && date && locale === 'ja' && (
          <div className="p-3 border-t text-sm text-muted-foreground">
            和暦: {toJapaneseEra(date)}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}