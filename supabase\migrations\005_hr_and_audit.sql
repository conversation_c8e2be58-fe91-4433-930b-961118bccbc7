-- HR request forms
CREATE TABLE hr_request_forms (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  requester_id UUID REFERENCES staff(id),
  request_type VARCHAR(20) NOT NULL, -- 'onboarding', 'offboarding'
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE
);

-- HR request details
CREATE TABLE hr_request_details (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  hr_request_id UUID REFERENCES hr_request_forms(id) ON DELETE CASCADE,
  staff_id UUID REFERENCES staff(id), -- For offboarding
  new_staff_data JSONB, -- For onboarding when no staff record exists yet
  request_details JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit logs table
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES staff(id),
  action VARCHAR(100) NOT NULL,
  action_details JSONB,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indices
CREATE INDEX idx_hr_request_forms_requester_id ON hr_request_forms(requester_id);
CREATE INDEX idx_hr_request_forms_status ON hr_request_forms(status);
CREATE INDEX idx_hr_request_details_hr_request_id ON hr_request_details(hr_request_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
