'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DynamicFieldWithErrorDetection } from '@/components/forms/dynamic-field-with-error-detection'
import { Button } from '@/components/ui/button'
import { AlertCircle, CheckCircle } from 'lucide-react'

export default function TestErrorDetectionPage() {
  const [formData, setFormData] = useState({
    email: '',
    staffId: '',
    pcId: '',
    name_jp: '',
    department: ''
  })

  const testFields = [
    {
      name: 'email',
      type: 'email' as const,
      label: 'Email Address',
      labelJp: 'メールアドレス',
      required: true,
      placeholder: '<EMAIL>'
    },
    {
      name: 'staffId',
      type: 'staff_id' as const,
      label: 'Staff ID',
      labelJp: 'スタッフID',
      required: true,
      placeholder: 'R000123'
    },
    {
      name: 'pcId',
      type: 'pc_id' as const,
      label: 'PC ID',
      labelJp: 'PC ID',
      required: true,
      placeholder: 'M241234'
    },
    {
      name: 'name_jp',
      type: 'text' as const,
      label: 'Name (Japanese)',
      labelJp: '氏名（日本語）',
      required: true,
      placeholder: '山田 太郎'
    },
    {
      name: 'department',
      type: 'text' as const,
      label: 'Department',
      labelJp: '部署',
      required: true,
      placeholder: '人事部'
    }
  ]

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted with data:', formData)
  }

  return (
    <div className="container mx-auto py-10 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>AI-Powered Error Detection Test</CardTitle>
          <CardDescription>
            Test the error detection and correction features with common IT Helpdesk form fields
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Test Instructions
                </h3>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Try entering personal emails (gmail.com) to see domain correction</li>
                  <li>• Enter staff ID without leading zeros (e.g., R123)</li>
                  <li>• Type PC ID without proper format (e.g., 241234)</li>
                  <li>• Enter Japanese names without spaces</li>
                  <li>• Use department abbreviations (e.g., HR, IT)</li>
                </ul>
              </div>

              {testFields.map((field) => (
                <DynamicFieldWithErrorDetection
                  key={field.name}
                  field={field}
                  value={formData[field.name as keyof typeof formData]}
                  onChange={(value) => handleFieldChange(field.name, value)}
                  language="ja"
                />
              ))}
            </div>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setFormData({
                  email: '',
                  staffId: '',
                  pcId: '',
                  name_jp: '',
                  department: ''
                })}
              >
                Reset
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Submit Form
              </Button>
            </div>
          </form>

          <div className="mt-8 pt-8 border-t">
            <h3 className="font-semibold mb-4">Form Data Preview</h3>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
