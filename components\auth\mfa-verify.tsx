'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, Smartphone } from 'lucide-react';
import { useTranslation } from '@/lib/i18n/use-translation';
import { useMFA } from '@/lib/hooks/use-mfa';

interface MFAVerifyProps {
  sessionToken: string;
  method: 'totp' | 'sms' | 'email';
  onSuccess: () => void;
  onCancel?: () => void;
}

export function MFAVerify({ sessionToken, method, onSuccess, onCancel }: MFAVerifyProps) {
  const { t } = useTranslation();
  const { verifyCode, loading, error: mfaError } = useMFA();
  
  const [code, setCode] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [attemptsRemaining, setAttemptsRemaining] = useState<number | null>(null);

  const handleVerify = async () => {
    if (!code || code.length !== 6) {
      setError(t('mfa.errors.invalidCode'));
      return;
    }

    setError(null);
    
    try {
      const result = await verifyCode(sessionToken, code);
      
      if (result.success) {
        onSuccess();
      } else {
        if (result.lockedOut) {
          setError(t('mfa.errors.lockedOut'));
        } else {
          setError(t('mfa.errors.verificationFailed'));
          if (result.attemptsRemaining !== undefined) {
            setAttemptsRemaining(result.attemptsRemaining);
          }
        }
        setCode('');
      }
    } catch (err) {
      setError(err.message || t('mfa.errors.verificationFailed'));
    }
  };

  const getMethodIcon = () => {
    switch (method) {
      case 'totp':
        return <Shield className="h-5 w-5" />;
      case 'sms':
      case 'email':
        return <Smartphone className="h-5 w-5" />;
    }
  };

  const getMethodTitle = () => {
    switch (method) {
      case 'totp':
        return t('mfa.verify.totp.title');
      case 'sms':
        return t('mfa.verify.sms.title');
      case 'email':
        return t('mfa.verify.email.title');
    }
  };

  const getMethodDescription = () => {
    switch (method) {
      case 'totp':
        return t('mfa.verify.totp.description');
      case 'sms':
        return t('mfa.verify.sms.description');
      case 'email':
        return t('mfa.verify.email.description');
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getMethodIcon()}
          {getMethodTitle()}
        </CardTitle>
        <CardDescription>
          {getMethodDescription()}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {(error || mfaError) && (
          <Alert variant="destructive">
            <AlertDescription>
              {error || mfaError}
              {attemptsRemaining !== null && (
                <span className="block mt-1">
                  {t('mfa.errors.attemptsRemaining', { count: attemptsRemaining })}
                </span>
              )}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="mfa-code">{t('mfa.verify.codeLabel')}</Label>
          <Input
            id="mfa-code"
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            maxLength={6}
            value={code}
            onChange={(e) => setCode(e.target.value.replace(/\D/g, ''))}
            placeholder="000000"
            className="text-center text-2xl font-mono"
            autoComplete="one-time-code"
            autoFocus
          />
          <p className="text-xs text-muted-foreground">
            {t('mfa.verify.codeHelp')}
          </p>
        </div>

        <div className="flex gap-2">
          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              className="flex-1"
            >
              {t('common.cancel')}
            </Button>
          )}
          <Button
            onClick={handleVerify}
            disabled={loading || code.length !== 6}
            className="flex-1"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('common.verifying')}
              </>
            ) : (
              t('mfa.verify.submit')
            )}
          </Button>
        </div>

        <div className="text-center">
          <Button
            variant="link"
            className="text-sm"
            onClick={() => {
              // TODO: Implement backup code usage
              console.log('Use backup code');
            }}
          >
            {t('mfa.verify.useBackupCode')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
