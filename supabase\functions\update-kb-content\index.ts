import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UpdateRequest {
  articleId?: string
  updateType?: 'feedback' | 'scheduled' | 'manual'
  forceUpdate?: boolean
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const openAiApiKey = Deno.env.get('OPENAI_API_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    const { articleId, updateType = 'scheduled', forceUpdate = false } = await req.json() as UpdateRequest
    
    if (articleId) {
      // Update specific article
      await updateArticle(supabase, openAiApiKey, articleId, updateType, forceUpdate)
    } else {
      // Periodic update for all eligible articles
      await performPeriodicUpdate(supabase, openAiApiKey)
    }
    
    return new Response(
      JSON.stringify({ success: true, message: 'Content update process completed' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function updateArticle(
  supabase: any,
  openAiApiKey: string,
  articleId: string,
  updateType: string,
  forceUpdate: boolean
) {
  // Get article with feedback
  const { data: article, error: articleError } = await supabase
    .from('kb_articles')
    .select(`
      *,
      kb_article_feedback(
        feedback_type,
        suggestion,
        created_at
      )
    `)
    .eq('id', articleId)
    .single()
  
  if (articleError || !article) {
    throw new Error('Article not found')
  }
  
  // Check if update is needed
  if (!forceUpdate && !shouldUpdateArticle(article)) {
    console.log('Article does not need update:', articleId)
    return
  }
  
  // Get recent search queries
  const { data: searchHistory } = await supabase
    .from('kb_search_history')
    .select('query')
    .contains('clicked_results', [articleId])
    .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
    .limit(100)
  
  // Generate update suggestions
  const suggestions = await generateUpdateSuggestions(
    openAiApiKey,
    article,
    article.kb_article_feedback || [],
    searchHistory || []
  )
  
  if (!suggestions || suggestions.updates.length === 0) {
    console.log('No updates suggested for article:', articleId)
    return
  }
  
  // Calculate confidence
  const confidence = calculateConfidence(suggestions.updates)
  
  if (confidence >= 0.7) {
    // Apply updates automatically
    await applyUpdates(supabase, article, suggestions.updates)
    
    // Log the update
    await supabase.from('kb_update_logs').insert({
      article_id: articleId,
      update_type: updateType,
      changes_made: suggestions.updates.map((u: any) => u.description),
      confidence_score: confidence,
      approved: true,
      created_at: new Date().toISOString()
    })
    
    // Trigger embedding update
    await supabase.rpc('update_article_embeddings', { p_article_id: articleId })
  } else {
    // Queue for manual review
    await supabase.from('kb_update_queue').insert({
      article_id: articleId,
      suggested_updates: suggestions.updates,
      confidence_score: confidence,
      status: 'pending',
      created_at: new Date().toISOString()
    })
  }
}

async function performPeriodicUpdate(supabase: any, openAiApiKey: string) {
  // Get articles that need update
  const { data: articles, error } = await supabase
    .from('kb_articles')
    .select('id')
    .eq('status', 'published')
    .or(`last_auto_update.is.null,last_auto_update.lt.${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()}`)
    .limit(10) // Process in batches
  
  if (error || !articles || articles.length === 0) {
    console.log('No articles need periodic update')
    return
  }
  
  // Process each article
  for (const article of articles) {
    try {
      await updateArticle(supabase, openAiApiKey, article.id, 'scheduled', false)
      // Add delay to avoid rate limits
      await new Promise(resolve => setTimeout(resolve, 2000))
    } catch (error) {
      console.error(`Error updating article ${article.id}:`, error)
    }
  }
}
function shouldUpdateArticle(article: any): boolean {
  const feedback = article.kb_article_feedback || []
  const recentFeedback = feedback.filter((f: any) => 
    new Date(f.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  )
  
  if (recentFeedback.length === 0) return false
  
  const negativeFeedback = recentFeedback.filter((f: any) => 
    ['not_helpful', 'outdated', 'incorrect'].includes(f.feedback_type)
  )
  
  return negativeFeedback.length > 2 || negativeFeedback.length / recentFeedback.length > 0.3
}

async function generateUpdateSuggestions(
  openAiApiKey: string,
  article: any,
  feedback: any[],
  searchHistory: any[]
) {
  const prompt = buildUpdatePrompt(article, feedback, searchHistory)
  
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${openAiApiKey}`
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert content editor for an IT helpdesk knowledge base. Provide suggestions in valid JSON format only.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    })
  })
  
  if (!response.ok) {
    throw new Error('OpenAI API error')
  }
  
  const data = await response.json()
  const content = data.choices[0].message.content
  
  try {
    return JSON.parse(content)
  } catch (error) {
    console.error('Failed to parse AI response:', content)
    return null
  }
}
function buildUpdatePrompt(article: any, feedback: any[], searchHistory: any[]): string {
  const feedbackSummary = feedback.map(f => 
    `- ${f.feedback_type}: ${f.suggestion || 'No suggestion'}`
  ).join('\n')
  
  const queries = searchHistory.map(s => s.query).slice(0, 20).join(', ')
  
  return `Analyze this knowledge base article and suggest improvements:

Title: ${article.title_en} / ${article.title_jp}
Category: ${article.category}
Content (English): ${article.content_en}
Content (Japanese): ${article.content_jp}

Recent Feedback:
${feedbackSummary || 'None'}

Related Search Queries: ${queries || 'None'}

Provide specific updates in this JSON format:
{
  "updates": [
    {
      "type": "content_addition" | "content_revision" | "example_addition" | "clarification",
      "location": "beginning" | "middle" | "end",
      "content_en": "English content",
      "content_jp": "Japanese content",
      "reason": "Why this update is needed",
      "description": "Brief description"
    }
  ]
}`
}

function calculateConfidence(updates: any[]): number {
  let score = 0.5
  
  updates.forEach(update => {
    switch (update.type) {
      case 'clarification':
      case 'example_addition':
        score += 0.15
        break
      case 'content_addition':
        score += 0.1
        break
      case 'content_revision':
        score += 0.05
        break
    }
  })
  
  return Math.min(score, 1.0)
}
async function applyUpdates(supabase: any, article: any, updates: any[]) {
  let contentEn = article.content_en
  let contentJp = article.content_jp
  
  updates.forEach(update => {
    const enPrefix = update.type === 'example_addition' ? '\n\n**Example:**\n' :
                     update.type === 'clarification' ? '\n\n**Note:**\n' : '\n\n'
    const jpPrefix = update.type === 'example_addition' ? '\n\n**例:**\n' :
                     update.type === 'clarification' ? '\n\n**注記:**\n' : '\n\n'
    
    if (update.location === 'end') {
      contentEn += enPrefix + update.content_en
      contentJp += jpPrefix + update.content_jp
    } else if (update.location === 'beginning') {
      contentEn = update.content_en + enPrefix + contentEn
      contentJp = update.content_jp + jpPrefix + contentJp
    }
  })
  
  const { error } = await supabase
    .from('kb_articles')
    .update({
      content_en: contentEn,
      content_jp: contentJp,
      updated_at: new Date().toISOString(),
      last_auto_update: new Date().toISOString()
    })
    .eq('id', article.id)
  
  if (error) {
    throw new Error(`Failed to update article: ${error.message}`)
  }
}
