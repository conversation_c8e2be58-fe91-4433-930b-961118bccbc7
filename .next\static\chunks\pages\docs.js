/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/docs"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Cpages%5Cdocs.tsx&page=%2Fdocs!":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Cpages%5Cdocs.tsx&page=%2Fdocs! ***!
  \*******************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/docs\",\n      function () {\n        return __webpack_require__(/*! ./pages/docs.tsx */ \"./pages/docs.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/docs\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNwcmluYyU1Q2l0c3luYy1wcm9qZWN0JTVDQ2xhdWRlJTVDRGVtbzElNUNwYWdlcyU1Q2RvY3MudHN4JnBhZ2U9JTJGZG9jcyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQywwQ0FBa0I7QUFDekM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzdjYjkiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9kb2NzXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9kb2NzLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvZG9jc1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Cpages%5Cdocs.tsx&page=%2Fdocs!\n"));

/***/ }),

/***/ "./lib/api/api-documentation-page.tsx":
/*!********************************************!*\
  !*** ./lib/api/api-documentation-page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiDocumentationPage: function() { return /* binding */ ApiDocumentationPage; },\n/* harmony export */   ApiDocumentationViewType: function() { return /* binding */ ApiDocumentationViewType; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _swagger_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./swagger-ui */ \"./lib/api/swagger-ui.tsx\");\n/* harmony import */ var _redoc_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./redoc-ui */ \"./lib/api/redoc-ui.tsx\");\n/**\r\n * API Documentation Page\r\n * \r\n * This component provides a unified interface for displaying API documentation\r\n * with support for switching between Swagger UI and ReDoc UI.\r\n * \r\n * NOTE: You need to install the following packages:\r\n * npm install swagger-ui-react redoc styled-components\r\n */ \nvar _s = $RefreshSig$();\n\n\n\nvar ApiDocumentationViewType;\n(function(ApiDocumentationViewType) {\n    ApiDocumentationViewType[\"SWAGGER\"] = \"swagger\";\n    ApiDocumentationViewType[\"REDOC\"] = \"redoc\";\n})(ApiDocumentationViewType || (ApiDocumentationViewType = {}));\n/**\r\n * Default styles\r\n */ const defaultStyles = {\n    container: {\n        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n        maxWidth: \"100%\",\n        margin: \"0 auto\",\n        padding: \"0\"\n    },\n    header: {\n        padding: \"1rem\",\n        borderBottom: \"1px solid #eee\",\n        marginBottom: \"1rem\"\n    },\n    title: {\n        fontSize: \"2rem\",\n        fontWeight: \"bold\",\n        margin: \"0 0 0.5rem 0\"\n    },\n    description: {\n        fontSize: \"1rem\",\n        margin: \"0 0 1rem 0\",\n        color: \"#666\"\n    },\n    viewSwitcher: {\n        display: \"flex\",\n        gap: \"0.5rem\",\n        marginBottom: \"1rem\"\n    },\n    viewSwitcherButton: {\n        padding: \"0.5rem 1rem\",\n        border: \"1px solid #ddd\",\n        borderRadius: \"4px\",\n        backgroundColor: \"#f5f5f5\",\n        cursor: \"pointer\",\n        fontSize: \"0.875rem\",\n        fontWeight: \"normal\"\n    },\n    viewSwitcherButtonActive: {\n        backgroundColor: \"#e0e0e0\",\n        fontWeight: \"bold\"\n    },\n    content: {\n        padding: \"0 1rem\"\n    },\n    loading: {\n        padding: \"2rem\",\n        textAlign: \"center\",\n        color: \"#666\"\n    },\n    error: {\n        padding: \"2rem\",\n        textAlign: \"center\",\n        color: \"#d32f2f\"\n    }\n};\n/**\r\n * API documentation page component\r\n */ const ApiDocumentationPage = (param)=>{\n    let { title = \"API Documentation\", description, apiUrl = \"/api/docs\", defaultViewType = \"swagger\", showViewSwitcher = true, styles = {}, classNames = {}, swaggerUIProps = {}, reDocUIProps = {} } = param;\n    _s();\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultViewType);\n    const [spec, setSpec] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Merge styles with defaults\n    const mergedStyles = {\n        container: {\n            ...defaultStyles.container,\n            ...styles.container\n        },\n        header: {\n            ...defaultStyles.header,\n            ...styles.header\n        },\n        title: {\n            ...defaultStyles.title,\n            ...styles.title\n        },\n        description: {\n            ...defaultStyles.description,\n            ...styles.description\n        },\n        viewSwitcher: {\n            ...defaultStyles.viewSwitcher,\n            ...styles.viewSwitcher\n        },\n        viewSwitcherButton: {\n            ...defaultStyles.viewSwitcherButton,\n            ...styles.viewSwitcherButton\n        },\n        viewSwitcherButtonActive: {\n            ...defaultStyles.viewSwitcherButtonActive,\n            ...styles.viewSwitcherButtonActive\n        },\n        content: {\n            ...defaultStyles.content,\n            ...styles.content\n        },\n        loading: {\n            ...defaultStyles.loading,\n            ...styles.loading\n        },\n        error: {\n            ...defaultStyles.error,\n            ...styles.error\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSpec = async ()=>{\n            try {\n                const response = await fetch(apiUrl);\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch API documentation: \".concat(response.statusText));\n                }\n                const data = await response.json();\n                setSpec(data);\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(String(err)));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSpec();\n    }, [\n        apiUrl\n    ]);\n    const handleViewTypeChange = (type)=>{\n        setViewType(type);\n    };\n    const renderViewSwitcher = ()=>{\n        if (!showViewSwitcher) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: mergedStyles.viewSwitcher,\n            className: classNames.viewSwitcher,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    style: {\n                        ...mergedStyles.viewSwitcherButton,\n                        ...viewType === \"swagger\" ? mergedStyles.viewSwitcherButtonActive : {}\n                    },\n                    className: \"\".concat(classNames.viewSwitcherButton || \"\", \" \").concat(viewType === \"swagger\" ? classNames.viewSwitcherButtonActive || \"\" : \"\"),\n                    onClick: ()=>handleViewTypeChange(\"swagger\"),\n                    children: \"Swagger UI\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    style: {\n                        ...mergedStyles.viewSwitcherButton,\n                        ...viewType === \"redoc\" ? mergedStyles.viewSwitcherButtonActive : {}\n                    },\n                    className: \"\".concat(classNames.viewSwitcherButton || \"\", \" \").concat(viewType === \"redoc\" ? classNames.viewSwitcherButtonActive || \"\" : \"\"),\n                    onClick: ()=>handleViewTypeChange(\"redoc\"),\n                    children: \"ReDoc\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n            lineNumber: 294,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderContent = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.loading,\n                className: classNames.loading,\n                children: \"Loading API documentation...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (error) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.error,\n                className: classNames.error,\n                children: [\n                    \"Error loading API documentation: \",\n                    error.message\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 340,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!spec) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.error,\n                className: classNames.error,\n                children: \"No API documentation available.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (viewType === \"swagger\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_swagger_ui__WEBPACK_IMPORTED_MODULE_2__.SwaggerUI, {\n                spec: spec,\n                ...swaggerUIProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 361,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_redoc_ui__WEBPACK_IMPORTED_MODULE_3__.ReDocUI, {\n            spec: spec,\n            ...reDocUIProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n            lineNumber: 364,\n            columnNumber: 12\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: mergedStyles.container,\n        className: classNames.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.header,\n                className: classNames.header,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: mergedStyles.title,\n                        className: classNames.title,\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: mergedStyles.description,\n                        className: classNames.description,\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, undefined),\n                    renderViewSwitcher()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: mergedStyles.content,\n                className: classNames.content,\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\api-documentation-page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * // pages/api/docs.ts\r\n * import { NextApiRequest, NextApiResponse } from 'next';\r\n * import { openApiMiddleware } from '../../lib/api/openapi-documentation';\r\n * \r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * \r\n * // pages/docs.tsx\r\n * import { ApiDocumentationPage, ApiDocumentationViewType } from '../../lib/api/api-documentation-page';\r\n * \r\n * export default function DocsPage() {\r\n *   return (\r\n *     <ApiDocumentationPage\r\n *       title=\"API Documentation\"\r\n *       description=\"Documentation for the My API\"\r\n *       apiUrl=\"/api/docs\"\r\n *       defaultViewType={ApiDocumentationViewType.SWAGGER}\r\n *       showViewSwitcher={true}\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n_s(ApiDocumentationPage, \"VXVqO/0sfQQERRRDbJT8zY4zg3E=\");\n_c = ApiDocumentationPage;\nvar _c;\n$RefreshReg$(_c, \"ApiDocumentationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api/api-documentation-page.tsx\n"));

/***/ }),

/***/ "./lib/api/redoc-ui.tsx":
/*!******************************!*\
  !*** ./lib/api/redoc-ui.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiDocumentationPageReDoc: function() { return /* binding */ ApiDocumentationPageReDoc; },\n/* harmony export */   ReDocUI: function() { return /* binding */ ReDocUI; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/**\r\n * ReDoc UI Component\r\n * \r\n * This component provides a ReDoc interface for displaying OpenAPI documentation.\r\n * It uses the redoc package to render the UI.\r\n * \r\n * NOTE: You need to install the redoc package:\r\n * npm install redoc styled-components\r\n */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Dynamically import ReDoc to avoid SSR issues\n// NOTE: This requires the redoc package to be installed\nconst RedocStandalone = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/*! import() */ \"node_modules_redoc_bundles_redoc_browser_lib_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! redoc */ \"./node_modules/redoc/bundles/redoc.browser.lib.js\", 23)).then((mod)=>mod.RedocStandalone), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\lib\\\\api\\\\redoc-ui.tsx -> \" + \"redoc\"\n        ]\n    },\n    ssr: false\n});\n_c = RedocStandalone;\n/**\r\n * ReDoc UI component\r\n */ const ReDocUI = (param)=>{\n    let { spec, options = {}, style, className } = param;\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RedocStandalone, {\n            spec: spec,\n            options: {\n                hideHostname: false,\n                hideDownloadButton: false,\n                noAutoAuth: false,\n                disableSearch: false,\n                requiredPropsFirst: true,\n                sortPropsAlphabetically: false,\n                pathInMiddlePanel: false,\n                hideSchemaTitles: false,\n                disableStickySidebar: false,\n                ...options\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReDocUI, \"k460N28PNzD7zo1YW47Q9UigQis=\");\n_c1 = ReDocUI;\n/**\r\n * API documentation page component using ReDoc\r\n */ const ApiDocumentationPageReDoc = (param)=>{\n    let { title = \"API Documentation\", description, apiUrl = \"/api/docs\" } = param;\n    _s1();\n    const [spec, setSpec] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSpec = async ()=>{\n            try {\n                const response = await fetch(apiUrl);\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch API documentation: \".concat(response.statusText));\n                }\n                const data = await response.json();\n                setSpec(data);\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(String(err)));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSpec();\n    }, [\n        apiUrl\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-loading\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading API documentation...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Error loading API documentation: \",\n                        error.message\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!spec) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No API documentation available.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"api-docs\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReDocUI, {\n            spec: spec\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n            lineNumber: 358,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\redoc-ui.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * // pages/api/docs.ts\r\n * import { NextApiRequest, NextApiResponse } from 'next';\r\n * import { openApiMiddleware } from '../../lib/api/openapi-documentation';\r\n * \r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * \r\n * // pages/docs.tsx\r\n * import { ApiDocumentationPageReDoc } from '../../lib/api/redoc-ui';\r\n * \r\n * export default function DocsPage() {\r\n *   return (\r\n *     <ApiDocumentationPageReDoc\r\n *       title=\"API Documentation\"\r\n *       description=\"Documentation for the My API\"\r\n *       apiUrl=\"/api/docs\"\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n_s1(ApiDocumentationPageReDoc, \"qlndYKkq4vtKJ8pUozxoZxQLbm4=\");\n_c2 = ApiDocumentationPageReDoc;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"RedocStandalone\");\n$RefreshReg$(_c1, \"ReDocUI\");\n$RefreshReg$(_c2, \"ApiDocumentationPageReDoc\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api/redoc-ui.tsx\n"));

/***/ }),

/***/ "./lib/api/swagger-ui.tsx":
/*!********************************!*\
  !*** ./lib/api/swagger-ui.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiDocumentationPage: function() { return /* binding */ ApiDocumentationPage; },\n/* harmony export */   SwaggerUI: function() { return /* binding */ SwaggerUI; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/**\r\n * Swagger UI Component\r\n *\r\n * This component provides a Swagger UI interface for displaying OpenAPI documentation.\r\n * It uses the swagger-ui-react package to render the UI.\r\n *\r\n * NOTE: You need to install the swagger-ui-react package:\r\n * npm install swagger-ui-react\r\n */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n// Dynamically import swagger-ui-react to avoid SSR issues\n// NOTE: This requires the swagger-ui-react package to be installed\nconst SwaggerUIReact = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/*! import() */ \"node_modules_swagger-ui-react_index_mjs\").then(__webpack_require__.bind(__webpack_require__, /*! swagger-ui-react */ \"./node_modules/swagger-ui-react/index.mjs\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\lib\\\\api\\\\swagger-ui.tsx -> \" + \"swagger-ui-react\"\n        ]\n    },\n    ssr: false\n});\n_c = SwaggerUIReact;\n// Dynamically import swagger-ui styles\n// NOTE: This requires the swagger-ui-react package to be installed\nconst SwaggerUIStyles = ()=>{\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        __webpack_require__.e(/*! import() */ \"node_modules_swagger-ui-react_swagger-ui_css\").then(__webpack_require__.t.bind(__webpack_require__, /*! swagger-ui-react/swagger-ui.css */ \"./node_modules/swagger-ui-react/swagger-ui.css\", 23));\n    }, []);\n    return null;\n};\n_s(SwaggerUIStyles, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = SwaggerUIStyles;\n/**\r\n * Swagger UI component\r\n */ const SwaggerUI = (param)=>{\n    let { spec, showTopBar = true, showModels = true, showExamples = true, showTryItOut = true, ...props } = param;\n    _s1();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwaggerUIStyles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwaggerUIReact, {\n                spec: spec,\n                docExpansion: \"list\",\n                defaultModelsExpandDepth: showModels ? 1 : -1,\n                displayRequestDuration: true,\n                filter: true,\n                showExtensions: true,\n                showCommonExtensions: true,\n                deepLinking: true,\n                persistAuthorization: true,\n                withCredentials: true,\n                supportedSubmitMethods: showTryItOut ? [\n                    \"get\",\n                    \"post\",\n                    \"put\",\n                    \"delete\",\n                    \"patch\",\n                    \"options\",\n                    \"head\"\n                ] : [],\n                tagsSorter: \"alpha\",\n                operationsSorter: \"alpha\",\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s1(SwaggerUI, \"k460N28PNzD7zo1YW47Q9UigQis=\");\n_c2 = SwaggerUI;\n/**\r\n * API documentation page component\r\n */ const ApiDocumentationPage = (param)=>{\n    let { title = \"API Documentation\", description, apiUrl = \"/api/docs\" } = param;\n    _s2();\n    const [spec, setSpec] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSpec = async ()=>{\n            try {\n                const response = await fetch(apiUrl);\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch API documentation: \".concat(response.statusText));\n                }\n                const data = await response.json();\n                setSpec(data);\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(String(err)));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSpec();\n    }, [\n        apiUrl\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-loading\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading API documentation...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n            lineNumber: 456,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Error loading API documentation: \",\n                        error.message\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n            lineNumber: 466,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!spec) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"api-docs-error\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined),\n                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No API documentation available.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"api-docs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, undefined),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 487,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwaggerUI, {\n                spec: spec\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\lib\\\\api\\\\swagger-ui.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, undefined);\n}; /**\r\n * Example usage:\r\n * \r\n * ```tsx\r\n * // pages/api/docs.ts\r\n * import { NextApiRequest, NextApiResponse } from 'next';\r\n * import { openApiMiddleware } from '../../lib/api/openapi-documentation';\r\n * \r\n * export default function handler(req: NextApiRequest, res: NextApiResponse) {\r\n *   return openApiMiddleware({\r\n *     title: 'My API',\r\n *     description: 'API documentation',\r\n *     version: '1.0.0',\r\n *     servers: [\r\n *       {\r\n *         url: 'https://api.example.com',\r\n *         description: 'Production server'\r\n *       },\r\n *       {\r\n *         url: 'https://staging.example.com',\r\n *         description: 'Staging server'\r\n *       }\r\n *     ]\r\n *   })(req, res);\r\n * }\r\n * \r\n * // pages/docs.tsx\r\n * import { ApiDocumentationPage } from '../../lib/api/swagger-ui';\r\n * \r\n * export default function DocsPage() {\r\n *   return (\r\n *     <ApiDocumentationPage\r\n *       title=\"API Documentation\"\r\n *       description=\"Documentation for the My API\"\r\n *       apiUrl=\"/api/docs\"\r\n *     />\r\n *   );\r\n * }\r\n * ```\r\n */ \n_s2(ApiDocumentationPage, \"qlndYKkq4vtKJ8pUozxoZxQLbm4=\");\n_c3 = ApiDocumentationPage;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"SwaggerUIReact\");\n$RefreshReg$(_c1, \"SwaggerUIStyles\");\n$RefreshReg$(_c2, \"SwaggerUI\");\n$RefreshReg$(_c3, \"ApiDocumentationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api/swagger-ui.tsx\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/dynamic.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/dynamic.js ***!
  \******************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    noSSR: function() {\n        return noSSR;\n    },\n    default: function() {\n        return dynamic;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _loadablesharedruntime = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./loadable.shared-runtime */ \"./node_modules/next/dist/shared/lib/loadable.shared-runtime.js\"));\nconst isServerSide = \"object\" === \"undefined\";\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    return {\n        default: (mod == null ? void 0 : mod.default) || mod\n    };\n}\nfunction noSSR(LoadableInitializer, loadableOptions) {\n    // Removing webpack and modules means react-loadable won't try preloading\n    delete loadableOptions.webpack;\n    delete loadableOptions.modules;\n    // This check is necessary to prevent react-loadable from initializing on the server\n    if (!isServerSide) {\n        return LoadableInitializer(loadableOptions);\n    }\n    const Loading = loadableOptions.loading;\n    // This will only be rendered on the server side\n    return ()=>/*#__PURE__*/ _react.default.createElement(Loading, {\n            error: null,\n            isLoading: true,\n            pastDelay: false,\n            timedOut: false\n        });\n}\nfunction dynamic(dynamicOptions, options) {\n    let loadableFn = _loadablesharedruntime.default;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ _react.default.createElement(\"p\", null, error.message, /*#__PURE__*/ _react.default.createElement(\"br\", null), error.stack);\n                }\n            }\n            return null;\n        }\n    };\n    // Support for direct import(), eg: dynamic(import('../hello-world'))\n    // Note that this is only kept for the edge case where someone is passing in a promise as first argument\n    // The react-loadable babel plugin will turn dynamic(import('../hello-world')) into dynamic(() => import('../hello-world'))\n    // To make sure we don't execute the import without rendering first\n    if (dynamicOptions instanceof Promise) {\n        loadableOptions.loader = ()=>dynamicOptions;\n    // Support for having import as a function, eg: dynamic(() => import('../hello-world'))\n    } else if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    // Support for having first argument being options, eg: dynamic({loader: import('../hello-world')})\n    } else if (typeof dynamicOptions === \"object\") {\n        loadableOptions = {\n            ...loadableOptions,\n            ...dynamicOptions\n        };\n    }\n    // Support for passing options, eg: dynamic(import('../hello-world'), {loading: () => <p>Loading something</p>})\n    loadableOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    const loaderFn = loadableOptions.loader;\n    const loader = ()=>loaderFn != null ? loaderFn().then(convertModule) : Promise.resolve(convertModule(()=>null));\n    // coming from build/babel/plugins/react-loadable-plugin.js\n    if (loadableOptions.loadableGenerated) {\n        loadableOptions = {\n            ...loadableOptions,\n            ...loadableOptions.loadableGenerated\n        };\n        delete loadableOptions.loadableGenerated;\n    }\n    // support for disabling server side rendering, eg: dynamic(() => import('../hello-world'), {ssr: false}).\n    if (typeof loadableOptions.ssr === \"boolean\" && !loadableOptions.ssr) {\n        delete loadableOptions.webpack;\n        delete loadableOptions.modules;\n        return noSSR(loadableFn, loadableOptions);\n    }\n    return loadableFn({\n        ...loadableOptions,\n        loader: loader\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/dynamic.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js ***!
  \******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"LoadableContext\", ({\n    enumerable: true,\n    get: function() {\n        return LoadableContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst LoadableContext = _react.default.createContext(null);\nif (true) {\n    LoadableContext.displayName = \"LoadableContext\";\n} //# sourceMappingURL=loadable-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoicURBRWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILG1EQUFrRDtJQUM5Q0ksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLDJCQUEyQkMsbUJBQU9BLENBQUMsNEdBQXlDO0FBQ2xGLE1BQU1DLFNBQVMsV0FBVyxHQUFHRix5QkFBeUJHLENBQUMsQ0FBQ0YsbUJBQU9BLENBQUMsNENBQU87QUFDdkUsTUFBTUYsa0JBQWtCRyxPQUFPRSxPQUFPLENBQUNDLGFBQWEsQ0FBQztBQUNyRCxJQUFJQyxJQUFxQyxFQUFFO0lBQ3ZDUCxnQkFBZ0JRLFdBQVcsR0FBRztBQUNsQyxFQUVBLDJEQUEyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcz9iYTEwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5cInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkxvYWRhYmxlQ29udGV4dFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gTG9hZGFibGVDb250ZXh0O1xuICAgIH1cbn0pO1xuY29uc3QgX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0ID0gcmVxdWlyZShcIkBzd2MvaGVscGVycy9fL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdFwiKTtcbmNvbnN0IF9yZWFjdCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcInJlYWN0XCIpKTtcbmNvbnN0IExvYWRhYmxlQ29udGV4dCA9IF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUNvbnRleHQobnVsbCk7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgTG9hZGFibGVDb250ZXh0LmRpc3BsYXlOYW1lID0gXCJMb2FkYWJsZUNvbnRleHRcIjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiTG9hZGFibGVDb250ZXh0IiwiX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdCIsIl8iLCJkZWZhdWx0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/loadable.shared-runtime.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/loadable.shared-runtime.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("// TODO: Remove use of `any` type.\n/**\n@copyright (c) 2017-present James Kyle <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/ // https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _loadablecontextsharedruntime = __webpack_require__(/*! ./loadable-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\");\nfunction resolve(obj) {\n    return obj && obj.default ? obj.default : obj;\n}\nconst ALL_INITIALIZERS = [];\nconst READY_INITIALIZERS = [];\nlet initialized = false;\nfunction load(loader) {\n    let promise = loader();\n    let state = {\n        loading: true,\n        loaded: null,\n        error: null\n    };\n    state.promise = promise.then((loaded)=>{\n        state.loading = false;\n        state.loaded = loaded;\n        return loaded;\n    }).catch((err)=>{\n        state.loading = false;\n        state.error = err;\n        throw err;\n    });\n    return state;\n}\nfunction createLoadableComponent(loadFn, options) {\n    var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n    let opts = Object.assign({\n        loader: null,\n        loading: null,\n        delay: 200,\n        timeout: null,\n        webpack: null,\n        modules: null\n    }, options);\n    /** @type LoadableSubscription */ let subscription = null;\n    function init() {\n        if (!subscription) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            const sub = new LoadableSubscription(loadFn, opts);\n            subscription = {\n                getCurrentValue: sub.getCurrentValue.bind(sub),\n                subscribe: sub.subscribe.bind(sub),\n                retry: sub.retry.bind(sub),\n                promise: sub.promise.bind(sub)\n            };\n        }\n        return subscription.promise();\n    }\n    // Server only\n    if (false) {}\n    // Client only\n    if (!initialized && \"object\" !== \"undefined\") {\n        // require.resolveWeak check is needed for environments that don't have it available like Jest\n        const moduleIds = opts.webpack && \"function\" === \"function\" ? opts.webpack() : opts.modules;\n        if (moduleIds) {\n            READY_INITIALIZERS.push((ids)=>{\n                for (const moduleId of moduleIds){\n                    if (ids.includes(moduleId)) {\n                        return init();\n                    }\n                }\n            });\n        }\n    }\n    function useLoadableModule() {\n        _s();\n        init();\n        const context = _react.default.useContext(_loadablecontextsharedruntime.LoadableContext);\n        if (context && Array.isArray(opts.modules)) {\n            opts.modules.forEach((moduleName)=>{\n                context(moduleName);\n            });\n        }\n    }\n    _s(useLoadableModule, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n    function LoadableComponent(props, ref) {\n        _s1();\n        useLoadableModule();\n        const state = _react.default.useSyncExternalStore(subscription.subscribe, subscription.getCurrentValue, subscription.getCurrentValue);\n        _react.default.useImperativeHandle(ref, ()=>({\n                retry: subscription.retry\n            }), []);\n        return _react.default.useMemo(()=>{\n            if (state.loading || state.error) {\n                return /*#__PURE__*/ _react.default.createElement(opts.loading, {\n                    isLoading: state.loading,\n                    pastDelay: state.pastDelay,\n                    timedOut: state.timedOut,\n                    error: state.error,\n                    retry: subscription.retry\n                });\n            } else if (state.loaded) {\n                return /*#__PURE__*/ _react.default.createElement(resolve(state.loaded), props);\n            } else {\n                return null;\n            }\n        }, [\n            props,\n            state\n        ]);\n    }\n    _s1(LoadableComponent, \"FetqI339RA+IfltT8VNzX8RMZ2Q=\", false, function() {\n        return [\n            useLoadableModule\n        ];\n    });\n    LoadableComponent.preload = ()=>init();\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return /*#__PURE__*/ _react.default.forwardRef(LoadableComponent);\n}\nclass LoadableSubscription {\n    promise() {\n        return this._res.promise;\n    }\n    retry() {\n        this._clearTimeouts();\n        this._res = this._loadFn(this._opts.loader);\n        this._state = {\n            pastDelay: false,\n            timedOut: false\n        };\n        const { _res: res, _opts: opts } = this;\n        if (res.loading) {\n            if (typeof opts.delay === \"number\") {\n                if (opts.delay === 0) {\n                    this._state.pastDelay = true;\n                } else {\n                    this._delay = setTimeout(()=>{\n                        this._update({\n                            pastDelay: true\n                        });\n                    }, opts.delay);\n                }\n            }\n            if (typeof opts.timeout === \"number\") {\n                this._timeout = setTimeout(()=>{\n                    this._update({\n                        timedOut: true\n                    });\n                }, opts.timeout);\n            }\n        }\n        this._res.promise.then(()=>{\n            this._update({});\n            this._clearTimeouts();\n        }).catch((_err)=>{\n            this._update({});\n            this._clearTimeouts();\n        });\n        this._update({});\n    }\n    _update(partial) {\n        this._state = {\n            ...this._state,\n            error: this._res.error,\n            loaded: this._res.loaded,\n            loading: this._res.loading,\n            ...partial\n        };\n        this._callbacks.forEach((callback)=>callback());\n    }\n    _clearTimeouts() {\n        clearTimeout(this._delay);\n        clearTimeout(this._timeout);\n    }\n    getCurrentValue() {\n        return this._state;\n    }\n    subscribe(callback) {\n        this._callbacks.add(callback);\n        return ()=>{\n            this._callbacks.delete(callback);\n        };\n    }\n    constructor(loadFn, opts){\n        this._loadFn = loadFn;\n        this._opts = opts;\n        this._callbacks = new Set();\n        this._delay = null;\n        this._timeout = null;\n        this.retry();\n    }\n}\nfunction Loadable(opts) {\n    return createLoadableComponent(load, opts);\n}\n_c = Loadable;\nfunction flushInitializers(initializers, ids) {\n    let promises = [];\n    while(initializers.length){\n        let init = initializers.pop();\n        promises.push(init(ids));\n    }\n    return Promise.all(promises).then(()=>{\n        if (initializers.length) {\n            return flushInitializers(initializers, ids);\n        }\n    });\n}\nLoadable.preloadAll = ()=>{\n    return new Promise((resolveInitializers, reject)=>{\n        flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject);\n    });\n};\nLoadable.preloadReady = (ids)=>{\n    if (ids === void 0) ids = [];\n    return new Promise((resolvePreload)=>{\n        const res = ()=>{\n            initialized = true;\n            return resolvePreload();\n        };\n        // We always will resolve, errors should be handled within loading UIs.\n        flushInitializers(READY_INITIALIZERS, ids).then(res, res);\n    });\n};\nif (true) {\n    window.__NEXT_PRELOADREADY = Loadable.preloadReady;\n}\nconst _default = Loadable; //# sourceMappingURL=loadable.shared-runtime.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/loadable.shared-runtime.js\n"));

/***/ }),

/***/ "./pages/docs.tsx":
/*!************************!*\
  !*** ./pages/docs.tsx ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_api_documentation_page__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/api/api-documentation-page */ \"./lib/api/api-documentation-page.tsx\");\n\n\n\n/**\r\n * API Documentation Page\r\n * \r\n * This page displays the API documentation using both Swagger UI and ReDoc.\r\n */ function DocsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_api_api_documentation_page__WEBPACK_IMPORTED_MODULE_2__.ApiDocumentationPage, {\n        title: \"Enterprise IT Helpdesk API Documentation\",\n        description: \"Comprehensive documentation for the Enterprise IT Helpdesk API\",\n        apiUrl: \"/api/docs\",\n        defaultViewType: _lib_api_api_documentation_page__WEBPACK_IMPORTED_MODULE_2__.ApiDocumentationViewType.SWAGGER,\n        showViewSwitcher: true,\n        styles: {\n            container: {\n                maxWidth: \"1200px\",\n                margin: \"0 auto\",\n                padding: \"2rem 1rem\"\n            },\n            header: {\n                marginBottom: \"2rem\",\n                borderBottom: \"1px solid #eee\",\n                paddingBottom: \"1rem\"\n            },\n            title: {\n                fontSize: \"2.5rem\",\n                color: \"#333\"\n            },\n            description: {\n                fontSize: \"1.2rem\",\n                color: \"#666\"\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\pages\\\\docs.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = DocsPage;\nvar _c;\n$RefreshReg$(_c, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/docs.tsx\n"));

/***/ }),

/***/ "./node_modules/next/dynamic.js":
/*!**************************************!*\
  !*** ./node_modules/next/dynamic.js ***!
  \**************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/dynamic */ \"./node_modules/next/dist/shared/lib/dynamic.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9keW5hbWljLmpzIiwibWFwcGluZ3MiOiJBQUFBLHVIQUFxRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9keW5hbWljLmpzPzczZDQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9keW5hbWljJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dynamic.js\n"));

/***/ }),

/***/ "./node_modules/react/cjs/react-jsx-dev-runtime.development.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    {\n      if (hasOwnProperty.call(props, 'key')) {\n        var componentName = getComponentNameFromType(type);\n        var keys = Object.keys(props).filter(function (k) {\n          return k !== 'key';\n        });\n        var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n        if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n          var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n          error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n          didWarnAboutKeySpread[componentName + beforeExample] = true;\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "./node_modules/react/jsx-dev-runtime.js":
/*!***********************************************!*\
  !*** ./node_modules/react/jsx-dev-runtime.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"./node_modules/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx1SkFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcz81Nzc3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5taW4uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cprinc%5Citsync-project%5CClaude%5CDemo1%5Cpages%5Cdocs.tsx&page=%2Fdocs!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);