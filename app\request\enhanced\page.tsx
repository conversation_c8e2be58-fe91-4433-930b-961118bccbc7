'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import AuthGuard from '@/components/auth/auth-guard'
import ServiceSelection from '@/components/forms/service-selection'
import UserSelection from '@/components/forms/user-selection'
import DynamicForm from '@/components/forms/dynamic-form'
import { EnhancedRequestConfirmation } from '@/components/forms/enhanced-request-confirmation'
import { KBIntegrationWidget } from '@/components/knowledge-base/kb-integration-widget'
import { ChatbotWidget } from '@/components/chatbot/chatbot-widget'
import { ServiceCategory, SelectedUser, RequestItem, RequestConfirmation } from '@/lib/request-types'
import { FormSchema, FormData } from '@/lib/form-types'
import { createFormSchemaFromServiceCategory } from '@/lib/form-utils'
import { useAuth } from '@/lib/auth-context'
import { supabase } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, ArrowLeft, ArrowRight, CheckCircle, AlertCircle, BookOpen, MessageCircle } from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

enum WizardStep {
  SERVICE_SELECTION = 0,
  USER_SELECTION = 1,
  FORM_FILLING = 2,
  CONFIRMATION = 3
}

export default function EnhancedRequestWizardPage() {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.SERVICE_SELECTION)
  const [selectedServices, setSelectedServices] = useState<ServiceCategory[]>([])
  const [selectedUsers, setSelectedUsers] = useState<SelectedUser[]>([])
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0)
  const [requestItems, setRequestItems] = useState<RequestItem[]>([])
  const [confirmation, setConfirmation] = useState<RequestConfirmation | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [showKBWidget, setShowKBWidget] = useState(true)
  
  const { user, staff } = useAuth()
  const router = useRouter()

  // Get current context for KB integration
  const getCurrentContext = () => {
    const currentService = selectedServices[currentServiceIndex];
    return {
      serviceCategory: currentService?.code,
      currentStep: WizardStep[currentStep],
      formType: currentService?.name_en
    };
  };

  useEffect(() => {
    if (currentStep === WizardStep.CONFIRMATION) {
      buildConfirmation()
    }
  }, [currentStep, requestItems])

  const buildConfirmation = () => {
    const totalUsers = new Set(requestItems.flatMap(item => item.users.map(u => u.id))).size
    const totalServices = requestItems.length

    const confirmationData: RequestConfirmation = {
      items: requestItems,
      status: requestItems.length > 0 ? 'ready' : 'draft',
      totalUsers,
      totalServices
    }

    setConfirmation(confirmationData)
  }

  const handleServiceSelection = (services: ServiceCategory[]) => {
    setSelectedServices(services)
    setError('')
  }

  const handleUserSelection = (users: SelectedUser[]) => {
    setSelectedUsers(users)
    setError('')
  }

  const handleFormSubmit = (formData: FormData) => {
    const currentService = selectedServices[currentServiceIndex]
    
    const newRequestItem: RequestItem = {
      serviceCategory: currentService,
      users: selectedUsers,
      formData,
      action: 'add'
    }

    setRequestItems(prev => {
      const updated = [...prev]
      const existingIndex = updated.findIndex(item => 
        item.serviceCategory.id === currentService.id
      )
      
      if (existingIndex >= 0) {
        updated[existingIndex] = newRequestItem
      } else {
        updated.push(newRequestItem)
      }
      
      return updated
    })

    if (currentServiceIndex < selectedServices.length - 1) {
      setCurrentServiceIndex(currentServiceIndex + 1)
    } else {
      setCurrentStep(WizardStep.CONFIRMATION)
    }
  }

  const handleNext = () => {
    if (currentStep === WizardStep.SERVICE_SELECTION && selectedServices.length === 0) {
      setError('Please select at least one service')
      return
    }
    
    if (currentStep === WizardStep.USER_SELECTION && selectedUsers.length === 0) {
      setError('Please select at least one user')
      return
    }

    if (currentStep < WizardStep.CONFIRMATION) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > WizardStep.SERVICE_SELECTION) {
      setCurrentStep(currentStep - 1)
      if (currentStep === WizardStep.FORM_FILLING && currentServiceIndex > 0) {
        setCurrentServiceIndex(currentServiceIndex - 1)
      }
    }
  }

  const handleSubmit = async () => {
    if (!confirmation || confirmation.items.length === 0) {
      setError('No requests to submit')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/requests/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          items: confirmation.items,
          submittedBy: user?.id
        })
      })

      if (!response.ok) {
        throw new Error('Failed to submit requests')
      }

      const result = await response.json()
      router.push(`/request/success?id=${result.requestId}`)
    } catch (err) {
      setError('Failed to submit requests. Please try again.')
      console.error('Submit error:', err)
    } finally {
      setLoading(false)
    }
  }

  const getProgressPercentage = () => {
    return ((currentStep + 1) / (Object.keys(WizardStep).length / 2)) * 100
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case WizardStep.SERVICE_SELECTION:
        return 'Select Services'
      case WizardStep.USER_SELECTION:
        return 'Select Users'
      case WizardStep.FORM_FILLING:
        return `${selectedServices[currentServiceIndex]?.name_en || 'Service'} Details`
      case WizardStep.CONFIRMATION:
        return 'Review & Submit'
      default:
        return 'IT Request'
    }
  }

  return (
    <AuthGuard requiredRoles={['Regular User', 'IT Helpdesk Support', 'Department Administrator']}>
      <div className="container mx-auto py-6 px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content Area */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between mb-4">
                  <CardTitle className="text-2xl">{getStepTitle()}</CardTitle>
                  <Badge variant="outline">
                    Step {currentStep + 1} of {Object.keys(WizardStep).length / 2}
                  </Badge>
                </div>
                <Progress value={getProgressPercentage()} className="h-2" />
              </CardHeader>
              <CardContent>
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {/* Step Content */}
                {currentStep === WizardStep.SERVICE_SELECTION && (
                  <ServiceSelection
                    selectedServices={selectedServices}
                    onSelectionChange={handleServiceSelection}
                  />
                )}

                {currentStep === WizardStep.USER_SELECTION && (
                  <UserSelection
                    selectedUsers={selectedUsers}
                    onSelectionChange={handleUserSelection}
                    departmentId={staff?.department_id}
                  />
                )}

                {currentStep === WizardStep.FORM_FILLING && selectedServices[currentServiceIndex] && (
                  <div>
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold">
                        {selectedServices[currentServiceIndex].name_en}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Service {currentServiceIndex + 1} of {selectedServices.length}
                      </p>
                    </div>
                    <DynamicForm
                      schema={createFormSchemaFromServiceCategory(selectedServices[currentServiceIndex])}
                      onSubmit={handleFormSubmit}
                      selectedUsers={selectedUsers}
                    />
                  </div>
                )}

                {currentStep === WizardStep.CONFIRMATION && confirmation && (
                  <EnhancedRequestConfirmation
                    confirmation={confirmation}
                    onEdit={(serviceId) => {
                      const index = selectedServices.findIndex(s => s.id === serviceId)
                      if (index >= 0) {
                        setCurrentServiceIndex(index)
                        setCurrentStep(WizardStep.FORM_FILLING)
                      }
                    }}
                  />
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-6">
                  <Button
                    variant="outline"
                    onClick={handleBack}
                    disabled={currentStep === WizardStep.SERVICE_SELECTION || loading}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>

                  {currentStep === WizardStep.CONFIRMATION ? (
                    <Button
                      onClick={handleSubmit}
                      disabled={loading || !confirmation || confirmation.items.length === 0}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Submit Request
                        </>
                      )}
                    </Button>
                  ) : currentStep === WizardStep.FORM_FILLING ? (
                    <Button type="submit" form="dynamic-form">
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  ) : (
                    <Button onClick={handleNext}>
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Help Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {/* Toggle Buttons */}
            <div className="flex gap-2">
              <Button
                variant={showKBWidget ? "default" : "outline"}
                size="sm"
                onClick={() => setShowKBWidget(true)}
                className="flex-1"
              >
                <BookOpen className="mr-2 h-4 w-4" />
                Knowledge Base
              </Button>
              <Button
                variant={!showKBWidget ? "default" : "outline"}
                size="sm"
                onClick={() => setShowKBWidget(false)}
                className="flex-1"
              >
                <MessageCircle className="mr-2 h-4 w-4" />
                AI Assistant
              </Button>
            </div>

            {/* Knowledge Base Widget */}
            {showKBWidget ? (
              <KBIntegrationWidget
                context={getCurrentContext()}
                className="sticky top-4"
              />
            ) : (
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5" />
                    AI Assistant
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Get help with your IT request. Ask questions about forms, services, or procedures.
                  </p>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => {
                      // This would open the chatbot widget
                      const chatbotButton = document.querySelector('[data-chatbot-trigger]') as HTMLButtonElement;
                      chatbotButton?.click();
                    }}
                  >
                    Open AI Assistant
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Floating Chatbot */}
      <ChatbotWidget
        currentForm={selectedServices[currentServiceIndex]?.code}
        currentPage="request-wizard"
        defaultOpen={false}
      />
    </AuthGuard>
  )
}