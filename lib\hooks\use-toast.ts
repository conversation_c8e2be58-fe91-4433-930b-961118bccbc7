/**
 * Toast Hook
 * Custom hook for toast notifications
 */

import { useState, useCallback } from 'react'

export interface Toast {
  id: string
  title?: string
  description?: string
  action?: React.ReactNode
  variant?: 'default' | 'destructive'
}

interface ToastState {
  toasts: Toast[]
}

const initialState: ToastState = {
  toasts: [],
}

let toastState = initialState
let listeners: Array<(state: ToastState) => void> = []

function dispatch(action: { type: string; payload?: any }) {
  switch (action.type) {
    case 'ADD_TOAST':
      toastState = {
        ...toastState,
        toasts: [...toastState.toasts, action.payload],
      }
      break
    case 'REMOVE_TOAST':
      toastState = {
        ...toastState,
        toasts: toastState.toasts.filter((toast) => toast.id !== action.payload),
      }
      break
    case 'DISMISS_TOAST':
      toastState = {
        ...toastState,
        toasts: toastState.toasts.map((toast) =>
          toast.id === action.payload ? { ...toast, open: false } : toast
        ),
      }
      break
  }

  listeners.forEach((listener) => listener(toastState))
}

function generateId() {
  return Math.random().toString(36).substr(2, 9)
}

export function useToast() {
  const [state, setState] = useState<ToastState>(toastState)

  const subscribe = useCallback((listener: (state: ToastState) => void) => {
    listeners.push(listener)
    return () => {
      listeners = listeners.filter((l) => l !== listener)
    }
  }, [])

  const toast = useCallback(
    ({ title, description, action, variant = 'default', ...props }: Omit<Toast, 'id'>) => {
      const id = generateId()

      dispatch({
        type: 'ADD_TOAST',
        payload: {
          id,
          title,
          description,
          action,
          variant,
          ...props,
        },
      })

      return {
        id,
        dismiss: () => dispatch({ type: 'DISMISS_TOAST', payload: id }),
        update: (props: Partial<Toast>) =>
          dispatch({
            type: 'UPDATE_TOAST',
            payload: { id, ...props },
          }),
      }
    },
    []
  )

  const dismiss = useCallback((toastId?: string) => {
    dispatch({ type: 'DISMISS_TOAST', payload: toastId })
  }, [])

  return {
    ...state,
    toast,
    dismiss,
    subscribe,
  }
}
