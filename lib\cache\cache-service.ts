/**
 * Cache Service with Invalidation Strategies
 * Provides high-level caching operations with automatic invalidation
 */

import { getRedisClient } from './redis-client'
import { metrics } from '@/lib/monitoring/metrics'

interface CacheOptions {
  ttl?: number // Time to live in seconds
  tags?: string[] // Cache tags for invalidation
  compress?: boolean // Compress large values
  serialize?: boolean // Custom serialization
}

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  tags: string[]
  version: string
}

type CacheKey = string
type CacheTag = string

class CacheService {
  private redis = getRedisClient()
  private memoryCache = new Map<string, any>() // Fallback memory cache
  private tagMap = new Map<string, Set<string>>() // Tag to keys mapping
  private readonly defaultTTL = 3600 // 1 hour
  private readonly maxMemoryCacheSize = 1000
  private readonly version = '1.0.0'

  constructor() {
    // Clean up memory cache periodically
    setInterval(() => this.cleanupMemoryCache(), 5 * 60 * 1000) // Every 5 minutes
  }

  /**
   * Get cached value with fallback to memory cache
   */
  async get<T>(key: <PERSON><PERSON><PERSON><PERSON>): Promise<T | null> {
    const startTime = Date.now()

    try {
      // Try Redis first
      const redisValue = await this.redis.get<CacheEntry<T>>(key)
      
      if (redisValue) {
        const duration = Date.now() - startTime
        metrics.timing('cache.get_time', duration)
        metrics.increment('cache.redis_hits')
        
        // Validate cache entry
        if (this.isValidCacheEntry(redisValue)) {
          return redisValue.data
        } else {
          // Invalid entry, remove it
          await this.redis.del(key)
        }
      }

      // Fallback to memory cache
      if (this.memoryCache.has(key)) {
        const memoryValue = this.memoryCache.get(key)
        if (this.isValidCacheEntry(memoryValue)) {
          metrics.increment('cache.memory_hits')
          return memoryValue.data
        } else {
          this.memoryCache.delete(key)
        }
      }

      metrics.increment('cache.misses')
      return null
    } catch (error) {
      console.error('Cache GET error:', error)
      metrics.increment('cache.errors')
      return null
    }
  }

  /**
   * Set cached value with options
   */
  async set<T>(
    key: CacheKey, 
    value: T, 
    options: CacheOptions = {}
  ): Promise<boolean> {
    const {
      ttl = this.defaultTTL,
      tags = [],
      compress = false,
      serialize = true
    } = options

    const cacheEntry: CacheEntry<T> = {
      data: value,
      timestamp: Date.now(),
      ttl,
      tags,
      version: this.version
    }

    const startTime = Date.now()

    try {
      // Set in Redis
      const redisSuccess = await this.redis.set(key, cacheEntry, ttl)
      
      // Set in memory cache as backup
      this.setMemoryCache(key, cacheEntry)
      
      // Update tag mappings
      this.updateTagMappings(key, tags)

      const duration = Date.now() - startTime
      metrics.timing('cache.set_time', duration)
      metrics.increment('cache.sets')

      return redisSuccess
    } catch (error) {
      console.error('Cache SET error:', error)
      metrics.increment('cache.errors')
      
      // Still try to set in memory cache
      this.setMemoryCache(key, cacheEntry)
      return false
    }
  }

  /**
   * Delete cached value
   */
  async del(key: CacheKey): Promise<boolean> {
    try {
      const redisResult = await this.redis.del(key)
      this.memoryCache.delete(key)
      this.removeFromTagMappings(key)
      
      metrics.increment('cache.deletes')
      return redisResult > 0
    } catch (error) {
      console.error('Cache DEL error:', error)
      metrics.increment('cache.errors')
      return false
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: CacheKey): Promise<boolean> {
    try {
      const redisExists = await this.redis.exists(key)
      if (redisExists) return true
      
      return this.memoryCache.has(key)
    } catch (error) {
      console.error('Cache EXISTS error:', error)
      return this.memoryCache.has(key)
    }
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: CacheTag[]): Promise<number> {
    let invalidatedCount = 0

    for (const tag of tags) {
      const keys = this.tagMap.get(tag)
      if (keys) {
        for (const key of keys) {
          await this.del(key)
          invalidatedCount++
        }
        this.tagMap.delete(tag)
      }
    }

    metrics.increment('cache.tag_invalidations', { count: invalidatedCount })
    return invalidatedCount
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidateByPattern(pattern: string): Promise<number> {
    try {
      const count = await this.redis.flushPattern(pattern)
      
      // Also clear from memory cache
      for (const [key] of this.memoryCache) {
        if (this.matchesPattern(key, pattern)) {
          this.memoryCache.delete(key)
        }
      }

      metrics.increment('cache.pattern_invalidations', { count })
      return count
    } catch (error) {
      console.error('Cache pattern invalidation error:', error)
      return 0
    }
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet<T>(
    key: CacheKey,
    fetchFunction: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached
    }

    // Execute function and cache result
    const startTime = Date.now()
    try {
      const result = await fetchFunction()
      await this.set(key, result, options)
      
      const duration = Date.now() - startTime
      metrics.timing('cache.fetch_time', duration)
      metrics.increment('cache.cache_misses_filled')
      
      return result
    } catch (error) {
      metrics.increment('cache.fetch_errors')
      throw error
    }
  }

  /**
   * Warm up cache with data
   */
  async warmUp(entries: Array<{ key: CacheKey; value: any; options?: CacheOptions }>): Promise<void> {
    const startTime = Date.now()
    
    const promises = entries.map(({ key, value, options }) => 
      this.set(key, value, options)
    )
    
    await Promise.all(promises)
    
    const duration = Date.now() - startTime
    metrics.timing('cache.warmup_time', duration)
    metrics.increment('cache.warmups', { count: entries.length })
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    const redisMetrics = this.redis.getMetrics()
    const redisInfo = await this.redis.getInfo()
    
    return {
      redis: {
        connected: this.redis.isHealthy(),
        hitRate: this.redis.getHitRate(),
        metrics: redisMetrics,
        info: redisInfo
      },
      memory: {
        size: this.memoryCache.size,
        maxSize: this.maxMemoryCacheSize
      },
      tags: {
        count: this.tagMap.size,
        mappings: Array.from(this.tagMap.entries()).map(([tag, keys]) => ({
          tag,
          keyCount: keys.size
        }))
      }
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      await this.redis.flushPattern('*')
      this.memoryCache.clear()
      this.tagMap.clear()
      
      metrics.increment('cache.clears')
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    const redisHealthy = await this.redis.ping()
    const memoryHealthy = this.memoryCache.size < this.maxMemoryCacheSize
    
    return {
      healthy: redisHealthy && memoryHealthy,
      details: {
        redis: redisHealthy,
        memory: {
          healthy: memoryHealthy,
          size: this.memoryCache.size,
          maxSize: this.maxMemoryCacheSize
        }
      }
    }
  }

  // Private helper methods
  private isValidCacheEntry<T>(entry: any): entry is CacheEntry<T> {
    if (!entry || typeof entry !== 'object') return false
    
    const now = Date.now()
    const expiryTime = entry.timestamp + (entry.ttl * 1000)
    
    return now < expiryTime && entry.version === this.version
  }

  private setMemoryCache<T>(key: CacheKey, entry: CacheEntry<T>): void {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      const firstKey = this.memoryCache.keys().next().value
      if (firstKey) {
        this.memoryCache.delete(firstKey)
      }
    }
    
    this.memoryCache.set(key, entry)
  }

  private updateTagMappings(key: CacheKey, tags: CacheTag[]): void {
    for (const tag of tags) {
      if (!this.tagMap.has(tag)) {
        this.tagMap.set(tag, new Set())
      }
      this.tagMap.get(tag)!.add(key)
    }
  }

  private removeFromTagMappings(key: CacheKey): void {
    for (const [tag, keys] of this.tagMap) {
      keys.delete(key)
      if (keys.size === 0) {
        this.tagMap.delete(tag)
      }
    }
  }

  private matchesPattern(key: string, pattern: string): boolean {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    return regex.test(key)
  }

  private cleanupMemoryCache(): void {
    const now = Date.now()
    let cleaned = 0
    
    for (const [key, entry] of this.memoryCache) {
      if (!this.isValidCacheEntry(entry)) {
        this.memoryCache.delete(key)
        cleaned++
      }
    }
    
    if (cleaned > 0) {
      metrics.increment('cache.memory_cleanup', { count: cleaned })
    }
  }
}

// Singleton instance
let cacheService: CacheService | null = null

export const getCacheService = (): CacheService => {
  if (!cacheService) {
    cacheService = new CacheService()
  }
  return cacheService
}

export { CacheService }
export type { CacheOptions, CacheEntry }
