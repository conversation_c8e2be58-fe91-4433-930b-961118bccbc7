import { useAuth } from './auth-context'
import { hasPermission, canAccessDepartment, UserRole } from './auth'

export function usePermissions() {
  const { userRole, staff } = useAuth()

  const checkPermission = (
    resource: string,
    action: string,
    scope?: string
  ): boolean => {
    if (!userRole) return false
    return hasPermission(userRole, resource, action, scope)
  }

  const checkDepartmentAccess = (targetDepartmentId: string): boolean => {
    if (!userRole || !staff) return false
    return canAccessDepartment(userRole, staff.division_id, targetDepartmentId)
  }

  const isGlobalAdmin = (): boolean => {
    return userRole === UserRole.GLOBAL_ADMIN
  }

  const isSystemAdmin = (): boolean => {
    return userRole === UserRole.SYSTEM_ADMIN
  }

  const isDepartmentAdmin = (): boolean => {
    return userRole === UserRole.DEPT_ADMIN
  }

  const isHRStaff = (): boolean => {
    return userRole === UserRole.HR_STAFF
  }

  const isITSupport = (): boolean => {
    return userRole === UserRole.IT_SUPPORT
  }

  const isRegularUser = (): boolean => {
    return userRole === UserRole.REGULAR_USER
  }

  const canManageUsers = (): boolean => {
    return checkPermission('users', 'create') || checkPermission('users', 'update')
  }

  const canViewAllRequests = (): boolean => {
    return checkPermission('requests', 'read', 'all')
  }

  const canCreateRequests = (): boolean => {
    return checkPermission('requests', 'create')
  }

  const canAccessHRFunctions = (): boolean => {
    return checkPermission('hr_requests', 'create') || checkPermission('hr_requests', 'read')
  }

  const canAccessSystemSettings = (): boolean => {
    return isGlobalAdmin() || isSystemAdmin()
  }

  return {
    checkPermission,
    checkDepartmentAccess,
    isGlobalAdmin,
    isSystemAdmin,
    isDepartmentAdmin,
    isHRStaff,
    isITSupport,
    isRegularUser,
    canManageUsers,
    canViewAllRequests,
    canCreateRequests,
    canAccessHRFunctions,
    canAccessSystemSettings
  }
}
