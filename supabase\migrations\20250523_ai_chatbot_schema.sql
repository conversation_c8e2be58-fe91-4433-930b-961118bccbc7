-- AI Chatbot Schema for User Guidance and Support
-- ================================================

-- Create conversations table
CREATE TABLE chatbot_conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT,
  context JSONB DEFAULT '{}',
  language VARCHAR(2) DEFAULT 'ja',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_archived BOOLEAN DEFAULT FALSE
);

-- Create messages table
CREATE TABLE chatbot_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID REFERENCES chatbot_conversations(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create FAQ table for quick responses
CREATE TABLE chatbot_faq (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_ja TEXT NOT NULL,
  question_en TEXT NOT NULL,
  answer_ja TEXT NOT NULL,
  answer_en TEXT NOT NULL,
  category VARCHAR(100),
  keywords TEXT[],
  usage_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tutorials table
CREATE TABLE chatbot_tutorials (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title_ja TEXT NOT NULL,
  title_en TEXT NOT NULL,
  description_ja TEXT,
  description_en TEXT,
  steps JSONB NOT NULL DEFAULT '[]',
  category VARCHAR(100),
  difficulty_level VARCHAR(20),
  estimated_time INTEGER, -- in minutes
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create feedback table
CREATE TABLE chatbot_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID REFERENCES chatbot_messages(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_chatbot_conversations_user_id ON chatbot_conversations(user_id);
CREATE INDEX idx_chatbot_messages_conversation_id ON chatbot_messages(conversation_id);
CREATE INDEX idx_chatbot_messages_created_at ON chatbot_messages(created_at);
CREATE INDEX idx_chatbot_faq_category ON chatbot_faq(category);
CREATE INDEX idx_chatbot_faq_keywords ON chatbot_faq USING GIN(keywords);
CREATE INDEX idx_chatbot_tutorials_category ON chatbot_tutorials(category);

-- Enable RLS
ALTER TABLE chatbot_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chatbot_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE chatbot_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversations
CREATE POLICY "Users can view own conversations"
  ON chatbot_conversations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own conversations"
  ON chatbot_conversations FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own conversations"
  ON chatbot_conversations FOR UPDATE
  USING (auth.uid() = user_id);

-- RLS Policies for messages
CREATE POLICY "Users can view messages in own conversations"
  ON chatbot_messages FOR SELECT
  USING (
    conversation_id IN (
      SELECT id FROM chatbot_conversations WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create messages in own conversations"
  ON chatbot_messages FOR INSERT
  WITH CHECK (
    conversation_id IN (
      SELECT id FROM chatbot_conversations WHERE user_id = auth.uid()
    )
  );

-- RLS Policies for feedback
CREATE POLICY "Users can create own feedback"
  ON chatbot_feedback FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own feedback"
  ON chatbot_feedback FOR SELECT
  USING (auth.uid() = user_id);

-- Insert sample FAQ data
INSERT INTO chatbot_faq (question_ja, question_en, answer_ja, answer_en, category, keywords) VALUES
('パスワードをリセットする方法は？', 'How do I reset my password?', 
 'パスワードリセットは「パスワードリセット」サービスカテゴリーから申請できます。M365 OfficeまたはMulti Factor Authenticatorのいずれかを選択してください。', 
 'You can request a password reset from the "Password Reset" service category. Please select either M365 Office or Multi Factor Authenticator.',
 'password', ARRAY['パスワード', 'リセット', 'password', 'reset']),
 
('グループメールに追加する方法は？', 'How do I add someone to a group mail?',
 'グループメール管理は「グループメール」サービスカテゴリーから行えます。追加したいユーザーとグループメールアドレスを選択してください。',
 'Group mail management can be done from the "Group Mail" service category. Please select the users and group mail addresses you want to add.',
 'email', ARRAY['グループメール', 'メール', 'group mail', 'email', 'add']),
 
('SharePointアクセス権限を申請する方法は？', 'How do I request SharePoint access?',
 'SharePointライブラリアクセスは「SharePointライブラリ」サービスカテゴリーから申請できます。必要なライブラリとアクセスレベルを選択してください。',
 'SharePoint library access can be requested from the "SharePoint Library" service category. Please select the required library and access level.',
 'sharepoint', ARRAY['SharePoint', 'アクセス', '権限', 'access', 'permission']);

-- Insert sample tutorial
INSERT INTO chatbot_tutorials (title_ja, title_en, description_ja, description_en, steps, category, difficulty_level, estimated_time) VALUES
('新入社員のIT設定', 'IT Setup for New Employees',
 '新入社員のための基本的なIT設定手順', 'Basic IT setup procedures for new employees',
 '[
   {"step": 1, "title_ja": "アカウント作成", "title_en": "Account Creation", "description_ja": "HRシステムでアカウントを作成", "description_en": "Create account in HR system"},
   {"step": 2, "title_ja": "メール設定", "title_en": "Email Setup", "description_ja": "メールアドレスとグループメールの設定", "description_en": "Setup email address and group mails"},
   {"step": 3, "title_ja": "SharePointアクセス", "title_en": "SharePoint Access", "description_ja": "必要なSharePointライブラリへのアクセス権限付与", "description_en": "Grant access to required SharePoint libraries"}
 ]',
 'onboarding', 'beginner', 30);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_chatbot_conversations_updated_at
  BEFORE UPDATE ON chatbot_conversations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chatbot_faq_updated_at
  BEFORE UPDATE ON chatbot_faq
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chatbot_tutorials_updated_at
  BEFORE UPDATE ON chatbot_tutorials
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
