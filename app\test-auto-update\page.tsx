'use client'

import { KnowledgeBaseAutoUpdate } from '@/components/knowledge-base/auto-update-manager'
import { ArticleFeedback } from '@/components/knowledge-base/article-feedback'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'

export default function TestAutoUpdatePage() {
  // Sample article ID for testing feedback
  const sampleArticleId = '123e4567-e89b-12d3-a456-426614174000'
  
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">
          Knowledge Base Auto-Update System Test
        </h1>
        <p className="text-muted-foreground">
          AI-powered content auto-updating and feedback system demonstration
        </p>
      </div>

      <Tabs defaultValue="manager" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="manager">Update Manager</TabsTrigger>
          <TabsTrigger value="feedback">Article Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="manager">
          <KnowledgeBaseAutoUpdate />
        </TabsContent>

        <TabsContent value="feedback" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Article Feedback Component Demo</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-6">
                This demonstrates how users can provide feedback on knowledge base articles.
                The feedback triggers automatic content update analysis.
              </p>
              <ArticleFeedback 
                articleId={sampleArticleId}
                articleTitle="How to Reset Your Password"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>How the Auto-Update System Works</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">1. Feedback Collection</h3>
                <p className="text-sm text-muted-foreground">
                  Users provide feedback on articles (helpful, not helpful, outdated, incorrect)
                  with optional suggestions.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">2. AI Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  When negative feedback accumulates, the system uses AI to analyze the article
                  content, feedback, and search patterns to suggest improvements.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">3. Confidence Scoring</h3>
                <p className="text-sm text-muted-foreground">
                  Each suggested update receives a confidence score. High-confidence updates
                  (≥70%) are applied automatically, while lower scores require manual review.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">4. Content Gap Detection</h3>
                <p className="text-sm text-muted-foreground">
                  The system analyzes unsuccessful searches to identify topics that need
                  new articles, helping maintain comprehensive coverage.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">5. Periodic Reviews</h3>
                <p className="text-sm text-muted-foreground">
                  Articles that haven't been updated in 30 days automatically enter the
                  review queue to ensure content stays current.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
