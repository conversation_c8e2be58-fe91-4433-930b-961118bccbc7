-- PC Assets Management Tables

-- Table for PC/Computer assets
CREATE TABLE IF NOT EXISTS pc_assets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  pc_id VARCHAR(50) UNIQUE NOT NULL, -- e.g., M241234
  model VARCHAR(100),
  serial_number VARCHAR(100),
  os_version VARCHAR(50),
  purchase_date DATE,
  assigned_user_id UUID REFERENCES staff(id),
  assigned_date TIMESTAMP WITH TIME ZONE,
  department_id UUID REFERENCES divisions(id),
  status VARCHAR(20) DEFAULT 'active', -- active, inactive, maintenance, retired
  admin_enabled BOOLEAN DEFAULT FALSE,
  last_admin_change TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for PC admin status requests
CREATE TABLE IF NOT EXISTS pc_admin_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  request_id VARCHAR(20) UNIQUE NOT NULL DEFAULT 'PCR' || LPAD(nextval('pc_request_seq')::text, 8, '0'),
  requester_id UUID REFERENCES staff(id) NOT NULL,
  pc_asset_id UUID REFERENCES pc_assets(id),
  pc_id_search VARCHAR(50), -- For searching PC by ID
  action_type VARCHAR(20) NOT NULL, -- grant_admin, revoke_admin
  reason TEXT NOT NULL,
  software_to_install TEXT[], -- Array of software names if applicable
  status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected, completed
  priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, urgent
  approved_by UUID REFERENCES staff(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  completed_by UUID REFERENCES staff(id),
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sequence for PC request IDs
CREATE SEQUENCE IF NOT EXISTS pc_request_seq START WITH 1;

-- Software catalog table
CREATE TABLE IF NOT EXISTS software_catalog (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(200) NOT NULL,
  version VARCHAR(50),
  vendor VARCHAR(100),
  license_type VARCHAR(50), -- perpetual, subscription, freeware
  requires_admin BOOLEAN DEFAULT TRUE,
  category VARCHAR(50), -- productivity, development, security, utility
  description TEXT,
  installation_notes TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PC admin history for audit
CREATE TABLE IF NOT EXISTS pc_admin_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  pc_asset_id UUID REFERENCES pc_assets(id),
  user_id UUID REFERENCES staff(id),
  action VARCHAR(50), -- admin_granted, admin_revoked, software_installed
  performed_by UUID REFERENCES staff(id),
  request_id UUID REFERENCES pc_admin_requests(id),
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indices
CREATE INDEX idx_pc_assets_pc_id ON pc_assets(pc_id);
CREATE INDEX idx_pc_assets_assigned_user ON pc_assets(assigned_user_id);
CREATE INDEX idx_pc_assets_department ON pc_assets(department_id);
CREATE INDEX idx_pc_admin_requests_requester ON pc_admin_requests(requester_id);
CREATE INDEX idx_pc_admin_requests_status ON pc_admin_requests(status);
CREATE INDEX idx_pc_admin_history_pc_asset ON pc_admin_history(pc_asset_id);

-- Function to search PC by ID (supports partial matching)
CREATE OR REPLACE FUNCTION search_pc_by_id(search_term VARCHAR)
RETURNS TABLE(
  id UUID,
  pc_id VARCHAR,
  model VARCHAR,
  assigned_user_name VARCHAR,
  department_name VARCHAR,
  admin_enabled BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pa.id,
    pa.pc_id,
    pa.model,
    s.name_jp || ' (' || s.name_en || ')' as assigned_user_name,
    d.name_jp || ' (' || d.name_en || ')' as department_name,
    pa.admin_enabled
  FROM pc_assets pa
  LEFT JOIN staff s ON pa.assigned_user_id = s.id
  LEFT JOIN divisions d ON pa.department_id = d.id
  WHERE LOWER(pa.pc_id) LIKE LOWER('%' || search_term || '%')
  AND pa.status = 'active'
  ORDER BY pa.pc_id;
END;
$$ LANGUAGE plpgsql;

-- Function to process PC admin request
CREATE OR REPLACE FUNCTION process_pc_admin_request(
  p_request_id UUID,
  p_approved BOOLEAN,
  p_approver_id UUID,
  p_notes TEXT DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  v_request pc_admin_requests;
  v_pc_asset pc_assets;
  v_result JSONB := '{"success": false, "message": ""}';
BEGIN
  -- Get request details
  SELECT * INTO v_request FROM pc_admin_requests WHERE id = p_request_id;
  
  IF NOT FOUND THEN
    v_result := jsonb_build_object('success', false, 'message', 'Request not found');
    RETURN v_result;
  END IF;
  
  -- Update request status
  UPDATE pc_admin_requests
  SET 
    status = CASE WHEN p_approved THEN 'approved' ELSE 'rejected' END,
    approved_by = p_approver_id,
    approved_at = NOW(),
    notes = COALESCE(p_notes, notes),
    updated_at = NOW()
  WHERE id = p_request_id;
  
  -- If approved, update PC admin status
  IF p_approved AND v_request.pc_asset_id IS NOT NULL THEN
    -- Update PC asset
    UPDATE pc_assets
    SET 
      admin_enabled = CASE 
        WHEN v_request.action_type = 'grant_admin' THEN TRUE 
        ELSE FALSE 
      END,
      last_admin_change = NOW(),
      updated_at = NOW()
    WHERE id = v_request.pc_asset_id;
    
    -- Log the change
    INSERT INTO pc_admin_history (pc_asset_id, user_id, action, performed_by, request_id, details)
    VALUES (
      v_request.pc_asset_id,
      (SELECT assigned_user_id FROM pc_assets WHERE id = v_request.pc_asset_id),
      CASE 
        WHEN v_request.action_type = 'grant_admin' THEN 'admin_granted' 
        ELSE 'admin_revoked' 
      END,
      p_approver_id,
      p_request_id,
      jsonb_build_object(
        'reason', v_request.reason,
        'software_to_install', v_request.software_to_install
      )
    );
    
    -- Update request as completed
    UPDATE pc_admin_requests
    SET 
      status = 'completed',
      completed_by = p_approver_id,
      completed_at = NOW()
    WHERE id = p_request_id;
    
    v_result := jsonb_build_object(
      'success', true, 
      'message', 'PC admin request processed successfully'
    );
  ELSE
    v_result := jsonb_build_object(
      'success', true, 
      'message', 'Request ' || CASE WHEN p_approved THEN 'approved' ELSE 'rejected' END
    );
  END IF;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE pc_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pc_admin_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE pc_admin_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE software_catalog ENABLE ROW LEVEL SECURITY;

-- Policy for viewing PC assets (department-based)
CREATE POLICY pc_assets_view_policy ON pc_assets
  FOR SELECT
  TO authenticated
  USING (
    department_id IN (
      SELECT division_id FROM staff WHERE auth_id = auth.uid()
    )
    OR EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Policy for PC admin requests
CREATE POLICY pc_admin_requests_policy ON pc_admin_requests
  FOR ALL
  TO authenticated
  USING (
    requester_id IN (SELECT id FROM staff WHERE auth_id = auth.uid())
    OR EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Insert some sample PC assets
INSERT INTO pc_assets (pc_id, model, serial_number, os_version, status, admin_enabled) VALUES
('M241234', 'Dell OptiPlex 7090', 'DL-2024-001', 'Windows 11 Pro', 'active', false),
('M241235', 'HP EliteDesk 800 G8', 'HP-2024-002', 'Windows 11 Pro', 'active', false),
('M241236', 'Lenovo ThinkCentre M90q', 'LN-2024-003', 'Windows 11 Pro', 'active', true),
('M241237', 'Dell Latitude 5520', 'DL-2024-004', 'Windows 11 Pro', 'active', false),
('M241238', 'MacBook Pro 14"', 'AP-2024-005', 'macOS Sonoma', 'active', true);

-- Insert sample software catalog
INSERT INTO software_catalog (name, version, vendor, license_type, category, requires_admin) VALUES
('Microsoft Office 365', '2024', 'Microsoft', 'subscription', 'productivity', true),
('Visual Studio Code', '1.85', 'Microsoft', 'freeware', 'development', false),
('Adobe Creative Cloud', '2024', 'Adobe', 'subscription', 'productivity', true),
('Slack', '4.35', 'Slack Technologies', 'subscription', 'productivity', false),
('Chrome', '120', 'Google', 'freeware', 'utility', false),
('7-Zip', '23.01', '7-Zip', 'freeware', 'utility', false),
('Git', '2.43', 'Git', 'freeware', 'development', true),
('Docker Desktop', '4.26', 'Docker', 'subscription', 'development', true),
('Zoom', '5.17', 'Zoom', 'subscription', 'productivity', false),
('Norton 360', '2024', 'Norton', 'subscription', 'security', true);
