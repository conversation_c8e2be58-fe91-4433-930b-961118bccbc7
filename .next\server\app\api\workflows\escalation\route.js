(()=>{var e={};e.id=9936,e.ids=[9936],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>u,POST:()=>p});var i=r(42706),s=r(28203),o=r(45994),n=r(39187),l=r(61487),c=r(50867);async function u(e){try{let t=(0,l.U)(),{data:{user:r},error:a}=await t.auth.getUser();if(a||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:i}=new URL(e.url),s={isActive:i.get("isActive")?"true"===i.get("isActive"):void 0,triggerType:i.get("triggerType")||void 0,priority:i.get("priority")||void 0,categoryId:i.get("categoryId")||void 0,departmentId:i.get("departmentId")||void 0},o=o,u=new c.W(t,o),p=await u.getRules(s);return n.NextResponse.json({rules:p})}catch(e){return console.error("Error fetching escalation rules:",e),n.NextResponse.json({error:"Failed to fetch escalation rules"},{status:500})}}async function p(e){try{let t=(0,l.U)(),{data:{user:r},error:a}=await t.auth.getUser();if(a||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:i}=await t.from("staff").select("*, roles(*)").eq("auth_id",r.id).single();if(!i||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(i.roles.name))return n.NextResponse.json({error:"Forbidden"},{status:403});let s=await e.json(),o=o,u=new c.W(t,o),p=u.validateRule(s);if(!p.isValid)return n.NextResponse.json({error:"Invalid rule",details:p.errors},{status:400});let d=await u.createRule({...s,createdBy:i.id});return n.NextResponse.json({rule:d},{status:201})}catch(e){return console.error("Error creating escalation rule:",e),n.NextResponse.json({error:"Failed to create escalation rule"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/route",pathname:"/api/workflows/escalation",filename:"route",bundlePath:"app/api/workflows/escalation/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:h}=d;function v(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},96487:()=>{},78335:()=>{},50867:(e,t,r)=>{"use strict";r.d(t,{W:()=>a});class a{constructor(e,t){this.supabase=e,this.auditService=t}async createRule(e){try{let{data:t,error:r}=await this.supabase.from("escalation_rules").insert({...e,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}).select().single();if(r)throw r;return await this.auditService.logActivity({userId:e.createdBy,action:"create_escalation_rule",resourceType:"escalation_rule",resourceId:t.id,details:{name:e.name}}),t}catch(e){throw console.error("Error creating escalation rule:",e),e}}async updateRule(e,t,r){try{let{data:a,error:i}=await this.supabase.from("escalation_rules").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(i)throw i;return await this.auditService.logActivity({userId:r,action:"update_escalation_rule",resourceType:"escalation_rule",resourceId:e,details:{updates:t}}),a}catch(e){throw console.error("Error updating escalation rule:",e),e}}async deleteRule(e,t){try{let{error:r}=await this.supabase.from("escalation_rules").delete().eq("id",e);if(r)throw r;await this.auditService.logActivity({userId:t,action:"delete_escalation_rule",resourceType:"escalation_rule",resourceId:e})}catch(e){throw console.error("Error deleting escalation rule:",e),e}}async getRules(e){try{let t=this.supabase.from("escalation_rules").select("*");e?.isActive!==void 0&&(t=t.eq("is_active",e.isActive)),e?.triggerType&&(t=t.eq("trigger_type",e.triggerType)),e?.priority&&(t=t.eq("priority",e.priority)),e?.categoryId&&(t=t.contains("applicable_to_categories",[e.categoryId])),e?.departmentId&&(t=t.contains("applicable_to_departments",[e.departmentId]));let{data:r,error:a}=await t.order("priority",{ascending:!1});if(a)throw a;return r||[]}catch(e){throw console.error("Error fetching escalation rules:",e),e}}async getRule(e){try{let{data:t,error:r}=await this.supabase.from("escalation_rules").select("*").eq("id",e).single();if(r)throw r;return t}catch(e){throw console.error("Error fetching escalation rule:",e),e}}async toggleRuleStatus(e,t){try{let{data:r}=await this.supabase.from("escalation_rules").select("is_active").eq("id",e).single();if(!r)throw Error("Rule not found");let{data:a,error:i}=await this.supabase.from("escalation_rules").update({is_active:!r.is_active,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(i)throw i;return await this.auditService.logActivity({userId:t,action:"toggle_escalation_rule",resourceType:"escalation_rule",resourceId:e,details:{isActive:a.is_active}}),a}catch(e){throw console.error("Error toggling rule status:",e),e}}async createDefaultRules(e){for(let t of[{name:"SLA Breach Critical Priority",description:"Escalate when critical priority requests breach SLA",triggerType:"sla_breach",conditions:[{field:"priority",operator:"equals",value:"critical"},{field:"status",operator:"equals",value:"breached"}],actions:[{type:"notify",target:"role:IT Helpdesk Support",parameters:{message:"Critical SLA breach requires immediate attention"}},{type:"change_priority",parameters:{priority:"critical"}},{type:"notify",target:"manager",parameters:{message:"Critical request has breached SLA"}}],priority:"critical",isActive:!0,retryInterval:15,maxRetries:3,createdBy:e},{name:"Overdue High Priority Requests",description:"Escalate high priority requests overdue by 24 hours",triggerType:"overdue",conditions:[{field:"priority",operator:"equals",value:"high"},{field:"age_hours",operator:"greater_than",value:24}],actions:[{type:"reassign",target:"role:IT Helpdesk Support"},{type:"notify",target:"manager",parameters:{message:"High priority request overdue by 24+ hours"}}],priority:"high",isActive:!0,retryInterval:30,maxRetries:2,createdBy:e},{name:"Approval Timeout Auto-Escalate",description:"Escalate to next level when approval times out",triggerType:"approval_timeout",conditions:[],actions:[{type:"add_approver",target:"manager"},{type:"notify",target:"manager",parameters:{message:"Approval request has timed out and requires your attention"}}],priority:"medium",isActive:!0,createdBy:e},{name:"Password Reset Urgent",description:"Escalate password reset requests older than 2 hours",triggerType:"overdue",conditions:[{field:"service_category",operator:"contains",value:"password"},{field:"age_hours",operator:"greater_than",value:2}],actions:[{type:"change_priority",parameters:{priority:"high"}},{type:"notify",target:"role:IT Helpdesk Support",parameters:{message:"Password reset request requires urgent attention"}}],priority:"high",isActive:!0,applicableToCategories:["PWR","O365"],createdBy:e}])await this.createRule(t)}validateRule(e){let t=[];for(let r of(e.name&&0!==e.name.trim().length||t.push("Rule name is required"),e.triggerType||t.push("Trigger type is required"),e.actions&&0!==e.actions.length||t.push("At least one action is required"),e.conditions||[]))r.field&&r.operator||t.push("Invalid condition: field and operator are required");for(let r of e.actions||[])r.type||t.push("Invalid action: type is required"),["notify","reassign","add_approver"].includes(r.type)&&!r.target&&t.push(`Invalid action: target is required for ${r.type}`);return{isValid:0===t.length,errors:t}}}},61487:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(49064),i=r(44512);let s=()=>{let e=(0,i.UL)();return(0,a.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:a})=>e.set(t,r,a))}catch{}}}})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5994,5452,4512,9064],()=>r(94431));module.exports=a})();