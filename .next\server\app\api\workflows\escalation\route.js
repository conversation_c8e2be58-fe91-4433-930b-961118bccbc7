"use strict";(()=>{var e={};e.id=9936,e.ids=[9936],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78572:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>v});var o={};t.r(o),t.d(o,{GET:()=>d,POST:()=>l});var s=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(50867);async function d(e){try{let r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),a={isActive:s.get("isActive")?"true"===s.get("isActive"):void 0,triggerType:s.get("triggerType")||void 0,priority:s.get("priority")||void 0,categoryId:s.get("categoryId")||void 0,departmentId:s.get("departmentId")||void 0},n=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),d=new u.W(r,n),l=await d.getRules(a);return i.NextResponse.json({rules:l})}catch(e){return console.error("Error fetching escalation rules:",e),i.NextResponse.json({error:"Failed to fetch escalation rules"},{status:500})}}async function l(e){try{let r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await r.from("staff").select("*, roles(*)").eq("auth_id",t.id).single();if(!s||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(s.roles.name))return i.NextResponse.json({error:"Forbidden"},{status:403});let a=await e.json(),n=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),d=new u.W(r,n),l=d.validateRule(a);if(!l.isValid)return i.NextResponse.json({error:"Invalid rule",details:l.errors},{status:400});let c=await d.createRule({...a,createdBy:s.id});return i.NextResponse.json({rule:c},{status:201})}catch(e){return console.error("Error creating escalation rule:",e),i.NextResponse.json({error:"Failed to create escalation rule"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let c=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/route",pathname:"/api/workflows/escalation",filename:"route",bundlePath:"app/api/workflows/escalation/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:v,serverHooks:f}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:v})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>t(78572));module.exports=o})();