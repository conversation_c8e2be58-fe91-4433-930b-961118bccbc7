"use strict";(()=>{var e={};e.id=9936,e.ids=[9936],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78572:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>c});var o=t(42706),a=t(28203),i=t(45994),n=t(39187),u=t(61487),d=t(50867);async function l(e){try{let r=(0,u.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:o}=new URL(e.url),a={isActive:o.get("isActive")?"true"===o.get("isActive"):void 0,triggerType:o.get("triggerType")||void 0,priority:o.get("priority")||void 0,categoryId:o.get("categoryId")||void 0,departmentId:o.get("departmentId")||void 0},i=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),l=new d.W(r,i),c=await l.getRules(a);return n.NextResponse.json({rules:c})}catch(e){return console.error("Error fetching escalation rules:",e),n.NextResponse.json({error:"Failed to fetch escalation rules"},{status:500})}}async function c(e){try{let r=(0,u.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("*, roles(*)").eq("auth_id",t.id).single();if(!o||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(o.roles.name))return n.NextResponse.json({error:"Forbidden"},{status:403});let a=await e.json(),i=Object(function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}())(r),l=new d.W(r,i),c=l.validateRule(a);if(!c.isValid)return n.NextResponse.json({error:"Invalid rule",details:c.errors},{status:400});let p=await l.createRule({...a,createdBy:o.id});return n.NextResponse.json({rule:p},{status:201})}catch(e){return console.error("Error creating escalation rule:",e),n.NextResponse.json({error:"Failed to create escalation rule"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit/audit-service'");throw e.code="MODULE_NOT_FOUND",e}();let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/escalation/route",pathname:"/api/workflows/escalation",filename:"route",bundlePath:"app/api/workflows/escalation/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:g,serverHooks:f}=p;function w(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(78572));module.exports=s})();