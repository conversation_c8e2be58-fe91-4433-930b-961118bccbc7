"use strict";exports.id=2210,exports.ids=[2210],exports.modules={78896:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},67418:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97832:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},88976:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},19473:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},86201:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},16873:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},70384:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},61594:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2112:(e,t,r)=>{r.d(t,{C1:()=>g,bL:()=>w});var n=r(58009),a=r(29952),o=r(6004),i=r(31412),l=r(13024),s=r(66582),c=r(38762),d=r(98060),u=r(30830),f=r(45512),p="Checkbox",[y,h]=(0,o.A)(p),[v,m]=y(p);function b(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:o,disabled:i,form:s,name:c,onCheckedChange:d,required:u,value:y="on",internal_do_not_use_render:h}=e,[m,b]=(0,l.i)({prop:r,defaultProp:o??!1,onChange:d,caller:p}),[x,k]=n.useState(null),[w,A]=n.useState(null),g=n.useRef(!1),j=!x||!!s||!!x.closest("form"),C={checked:m,disabled:i,setChecked:b,control:x,setControl:k,name:c,form:s,value:y,hasConsumerStoppedPropagationRef:g,required:u,defaultChecked:!R(o)&&o,isFormControl:j,bubbleInput:w,setBubbleInput:A};return(0,f.jsx)(v,{scope:t,...C,children:"function"==typeof h?h(C):a})}var x="CheckboxTrigger",k=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...o},l)=>{let{control:s,value:c,disabled:d,checked:p,required:y,setControl:h,setChecked:v,hasConsumerStoppedPropagationRef:b,isFormControl:k,bubbleInput:w}=m(x,e),A=(0,a.s)(l,h),g=n.useRef(p);return n.useEffect(()=>{let e=s?.form;if(e){let t=()=>v(g.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,v]),(0,f.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":R(p)?"mixed":p,"aria-required":y,"data-state":I(p),"data-disabled":d?"":void 0,disabled:d,value:c,...o,ref:A,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(r,e=>{v(e=>!!R(e)||!e),w&&k&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});k.displayName=x;var w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:a,defaultChecked:o,required:i,disabled:l,value:s,onCheckedChange:c,form:d,...u}=e;return(0,f.jsx)(b,{__scopeCheckbox:r,checked:a,defaultChecked:o,disabled:l,required:i,onCheckedChange:c,name:n,form:d,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(k,{...u,ref:t,__scopeCheckbox:r}),e&&(0,f.jsx)(C,{__scopeCheckbox:r})]})})});w.displayName=p;var A="CheckboxIndicator",g=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,o=m(A,r);return(0,f.jsx)(d.C,{present:n||R(o.checked)||!0===o.checked,children:(0,f.jsx)(u.sG.span,{"data-state":I(o.checked),"data-disabled":o.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});g.displayName=A;var j="CheckboxBubbleInput",C=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:o,hasConsumerStoppedPropagationRef:i,checked:l,defaultChecked:d,required:p,disabled:y,name:h,value:v,form:b,bubbleInput:x,setBubbleInput:k}=m(j,e),w=(0,a.s)(r,k),A=(0,s.Z)(l),g=(0,c.X)(o);n.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(A!==l&&e){let r=new Event("click",{bubbles:t});x.indeterminate=R(l),e.call(x,!R(l)&&l),x.dispatchEvent(r)}},[x,A,l,i]);let C=n.useRef(!R(l)&&l);return(0,f.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??C.current,required:p,disabled:y,name:h,value:v,form:b,...t,tabIndex:-1,ref:w,style:{...t.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function I(e){return R(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=j},48305:(e,t,r)=>{r.d(t,{RG:()=>w,bL:()=>F,q7:()=>T});var n=r(58009),a=r(31412),o=r(39217),i=r(29952),l=r(6004),s=r(30096),c=r(30830),d=r(92828),u=r(13024),f=r(59018),p=r(45512),y="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,x]=(0,o.N)(v),[k,w]=(0,l.A)(v,[x]),[A,g]=k(v),j=n.forwardRef((e,t)=>(0,p.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(C,{...e,ref:t})})}));j.displayName=v;var C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:l=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:k,onEntryFocus:w,preventScrollOnEntryFocus:g=!1,...j}=e,C=n.useRef(null),R=(0,i.s)(t,C),I=(0,f.jH)(s),[D,F]=(0,u.i)({prop:m,defaultProp:x??null,onChange:k,caller:v}),[T,G]=n.useState(!1),M=(0,d.c)(w),L=b(r),S=n.useRef(!1),[K,N]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(y,M),()=>e.removeEventListener(y,M)},[M]),(0,p.jsx)(A,{scope:r,orientation:o,dir:I,loop:l,currentTabStopId:D,onItemFocus:n.useCallback(e=>F(e),[F]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>N(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:T||0===K?-1:0,"data-orientation":o,...j,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(y,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),g)}}S.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>G(!1))})})}),R="RovingFocusGroupItem",I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:l,children:d,...u}=e,f=(0,s.B)(),y=l||f,h=g(R,r),v=h.currentTabStopId===y,x=b(r),{onFocusableItemAdd:k,onFocusableItemRemove:w,currentTabStopId:A}=h;return n.useEffect(()=>{if(o)return k(),()=>w()},[o,k,w]),(0,p.jsx)(m.ItemSlot,{scope:r,id:y,focusable:o,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...u,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o?h.onItemFocus(y):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>h.onItemFocus(y)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return D[a]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>E(r))}}),children:"function"==typeof d?d({isCurrentTabStop:v,hasTabStop:null!=A}):d})})});I.displayName=R;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=j,T=I},55613:(e,t,r)=>{r.d(t,{B8:()=>E,UC:()=>T,bL:()=>D,l9:()=>F});var n=r(58009),a=r(31412),o=r(6004),i=r(48305),l=r(98060),s=r(30830),c=r(59018),d=r(13024),u=r(30096),f=r(45512),p="Tabs",[y,h]=(0,o.A)(p,[i.RG]),v=(0,i.RG)(),[m,b]=y(p),x=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:y="automatic",...h}=e,v=(0,c.jH)(l),[b,x]=(0,d.i)({prop:n,onChange:a,defaultProp:o??"",caller:p});return(0,f.jsx)(m,{scope:r,baseId:(0,u.B)(),value:b,onValueChange:x,orientation:i,dir:v,activationMode:y,children:(0,f.jsx)(s.sG.div,{dir:v,"data-orientation":i,...h,ref:t})})});x.displayName=p;var k="TabsList",w=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,o=b(k,r),l=v(r);return(0,f.jsx)(i.bL,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:n,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});w.displayName=k;var A="TabsTrigger",g=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...l}=e,c=b(A,r),d=v(r),u=R(c.baseId,n),p=I(c.baseId,n),y=n===c.value;return(0,f.jsx)(i.q7,{asChild:!0,...d,focusable:!o,active:y,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":p,"data-state":y?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...l,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;y||o||!e||c.onValueChange(n)})})})});g.displayName=A;var j="TabsContent",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:o,children:i,...c}=e,d=b(j,r),u=R(d.baseId,a),p=I(d.baseId,a),y=a===d.value,h=n.useRef(y);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.C,{present:o||y,children:({present:r})=>(0,f.jsx)(s.sG.div,{"data-state":y?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&i})})});function R(e,t){return`${e}-trigger-${t}`}function I(e,t){return`${e}-content-${t}`}C.displayName=j;var D=x,E=w,F=g,T=C}};