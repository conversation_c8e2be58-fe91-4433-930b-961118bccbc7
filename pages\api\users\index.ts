import { NextApiRequest, NextApiResponse } from 'next';
import { openApiMiddleware } from '../../../lib/api/openapi-documentation';

// Define schemas, security schemes, and tags
const userSchema = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
    name: { type: 'string' },
    email: { type: 'string', format: 'email' },
    role: { type: 'string', enum: ['admin', 'user'] },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' }
  },
  required: ['id', 'name', 'email', 'role']
};

const createUserRequestSchema = {
  type: 'object',
  properties: {
    name: { type: 'string' },
    email: { type: 'string', format: 'email' },
    role: { type: 'string', enum: ['admin', 'user'] }
  },
  required: ['name', 'email', 'role']
};

// Register schemas, security schemes, and tags with the OpenAPI registry
// Get registry instance
const registry = {
  registerSchema: (name: string, schema: any) => {},
  registerSecurityScheme: (name: string, scheme: any) => {},
  registerTag: (name: string, description?: string) => {},
  registerRoute: (route: any) => {}
};

// Register schemas
registry.registerSchema('User', userSchema);
registry.registerSchema('CreateUserRequest', createUserRequestSchema);

// Register security scheme
registry.registerSecurityScheme('bearerAuth', {
  type: 'http',
  scheme: 'bearer',
  bearerFormat: 'JWT'
});

// Register tag
registry.registerTag('Users', 'User management endpoints');

// Register routes
registry.registerRoute({
  path: '/api/users',
  method: 'get',
  summary: 'Get all users',
  description: 'Returns a list of all users',
  tags: ['Users'],
  operationId: 'getUsers',
  responses: {
    '200': {
      description: 'List of users',
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/User'
            }
          }
        }
      }
    },
    '401': {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: { type: 'string' }
            }
          }
        }
      }
    }
  },
  security: [{ bearerAuth: [] }]
});

registry.registerRoute({
  path: '/api/users',
  method: 'post',
  summary: 'Create a new user',
  description: 'Creates a new user',
  tags: ['Users'],
  operationId: 'createUser',
  requestBody: {
    description: 'User data',
    required: true,
    content: {
      'application/json': {
        schema: {
          $ref: '#/components/schemas/CreateUserRequest'
        }
      }
    }
  },
  responses: {
    '201': {
      description: 'User created',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/User'
          }
        }
      }
    },
    '400': {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: { type: 'string' }
            }
          }
        }
      }
    },
    '401': {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: { type: 'string' }
            }
          }
        }
      }
    }
  },
  security: [{ bearerAuth: [] }]
});

/**
 * Users controller
 */
class UsersController {
  /**
   * Get all users
   */
  async get(req: NextApiRequest, res: NextApiResponse) {
    // Mock data
    const users = [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '223e4567-e89b-12d3-a456-426614174001',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    res.status(200).json(users);
  }

  /**
   * Create a new user
   */
  async post(req: NextApiRequest, res: NextApiResponse) {
    const { name, email, role } = req.body;

    // Validate request
    if (!name || !email || !role) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Create user
    const user = {
      id: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
      name,
      email,
      role,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.status(201).json(user);
  }
}

// Create controller instance
const usersController = new UsersController();

/**
 * API route handler
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Route based on HTTP method
  switch (req.method) {
    case 'GET':
      return usersController.get(req, res);
    case 'POST':
      return usersController.post(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}