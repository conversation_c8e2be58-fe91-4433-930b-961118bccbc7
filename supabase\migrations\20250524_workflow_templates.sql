-- Create workflow templates table
CREATE TABLE IF NOT EXISTS workflow_templates (
  id VARCHAR(100) PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  name_jp VARCHAR(255),
  description TEXT,
  description_jp TEXT,
  category VARCHAR(50) NOT NULL,
  service_category_id VARCHAR(20),
  steps JSONB NOT NULL DEFAULT '[]',
  sla_hours INTEGER DEFAULT 24,
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  required_approvals INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES staff(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create template usage tracking table
CREATE TABLE IF NOT EXISTS workflow_template_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  template_id VARCHAR(100) REFERENCES workflow_templates(id),
  workflow_instance_id UUID REFERENCES workflow_instances(id),
  request_id UUID REFERENCES request_forms(id),
  used_by UUID REFERENCES staff(id),
  customizations JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_workflow_templates_category ON workflow_templates(category);
CREATE INDEX idx_workflow_templates_service ON workflow_templates(service_category_id);
CREATE INDEX idx_workflow_templates_active ON workflow_templates(is_active) WHERE is_active = true;
CREATE INDEX idx_workflow_template_usage_template ON workflow_template_usage(template_id);
CREATE INDEX idx_workflow_template_usage_date ON workflow_template_usage(created_at);

-- Add template_id to request_forms
ALTER TABLE request_forms 
ADD COLUMN IF NOT EXISTS workflow_template_id VARCHAR(100) REFERENCES workflow_templates(id);

-- Update workflow_instances to track template
ALTER TABLE workflow_instances 
ADD COLUMN IF NOT EXISTS template_id VARCHAR(100);

-- Create function to update updated_at
CREATE OR REPLACE FUNCTION update_workflow_template_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_workflow_templates_updated_at
  BEFORE UPDATE ON workflow_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_workflow_template_updated_at();

-- Grant permissions
GRANT SELECT ON workflow_templates TO authenticated;
GRANT INSERT, UPDATE ON workflow_templates TO authenticated;
GRANT SELECT, INSERT ON workflow_template_usage TO authenticated;

-- Row Level Security
ALTER TABLE workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_template_usage ENABLE ROW LEVEL SECURITY;

-- Policies for workflow_templates
CREATE POLICY "All users can view active templates" ON workflow_templates
  FOR SELECT TO authenticated
  USING (is_active = true);

CREATE POLICY "Admin users can manage templates" ON workflow_templates
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Policies for workflow_template_usage
CREATE POLICY "Users can view their own template usage" ON workflow_template_usage
  FOR SELECT TO authenticated
  USING (used_by IN (SELECT id FROM staff WHERE auth_id = auth.uid()));

CREATE POLICY "All authenticated users can track template usage" ON workflow_template_usage
  FOR INSERT TO authenticated
  WITH CHECK (used_by IN (SELECT id FROM staff WHERE auth_id = auth.uid()));

-- Create view for template statistics
CREATE OR REPLACE VIEW workflow_template_stats AS
SELECT 
  wt.id as template_id,
  wt.name as template_name,
  wt.category,
  COUNT(wtu.id) as usage_count,
  COUNT(DISTINCT wtu.used_by) as unique_users,
  COUNT(CASE WHEN wi.status = 'completed' THEN 1 END) as completed_count,
  COUNT(CASE WHEN wi.status = 'failed' THEN 1 END) as failed_count,
  AVG(
    CASE 
      WHEN wi.status = 'completed' AND wi.completed_at IS NOT NULL 
      THEN EXTRACT(EPOCH FROM (wi.completed_at - wi.created_at)) / 3600
      ELSE NULL 
    END
  ) as avg_completion_hours,
  MAX(wtu.created_at) as last_used_at
FROM workflow_templates wt
LEFT JOIN workflow_template_usage wtu ON wt.id = wtu.template_id
LEFT JOIN workflow_instances wi ON wtu.workflow_instance_id = wi.id
GROUP BY wt.id, wt.name, wt.category;

-- Grant access to the view
GRANT SELECT ON workflow_template_stats TO authenticated;
