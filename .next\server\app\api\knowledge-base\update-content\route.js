"use strict";(()=>{var e={};e.id=176,e.ids=[176],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},75513:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>k,routeModule:()=>h,serverHooks:()=>b,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>f});var n=r(42706),o=r(28203),i=r(45994),s=r(39187),c=r(33406),l=r(44512),d=r(73865),u=r(75122),p=r(45895);class g{constructor(){}static getInstance(){return g.instance||(g.instance=new g),g.instance}async processFeedback(e){try{let{error:t}=await d.N.from("kb_article_feedback").insert({article_id:e.article_id,user_id:e.user_id,feedback_type:e.feedback_type,suggestion:e.suggestion,created_at:new Date().toISOString()});if(t)throw Error(`Failed to store feedback: ${t.message}`);await this.shouldUpdateArticle(e.article_id)&&await this.updateArticleContent(e.article_id,"feedback")}catch(e){throw console.error("Error processing feedback:",e),e}}async shouldUpdateArticle(e){let{data:t,error:r}=await d.N.rpc("get_article_feedback_stats",{p_article_id:e,p_days:7});if(r||!t)return console.error("Error getting feedback stats:",r),!1;let a=t.not_helpful_count+t.outdated_count+t.incorrect_count,n=t.total_count;return a>5||(n>0?a/n:0)>.3||t.outdated_count>2||t.incorrect_count>2}async updateArticleContent(e,t="auto"){try{let{data:r,error:a}=await d.N.from("kb_articles").select("*").eq("id",e).single();if(a||!r)throw Error("Article not found");let{data:n}=await d.N.from("kb_article_feedback").select("*").eq("article_id",e).gte("created_at",new Date(Date.now()-6048e5).toISOString()).order("created_at",{ascending:!1}),{data:o}=await d.N.from("kb_search_history").select("query").contains("clicked_results",[e]).gte("created_at",new Date(Date.now()-2592e6).toISOString()),i=this.buildUpdatePrompt(r,n||[],o||[]),s=await p.aiApiService.generateText(i,{temperature:.3,maxTokens:2e3}),c=this.parseSuggestions(s);if(0===c.length){console.log("No updates suggested for article:",e);return}let l=await this.calculateUpdateConfidence(c,r);l>=.7?(await this.applyUpdates(r,c),await this.logContentUpdate({article_id:e,update_type:t,changes_made:c.map(e=>e.description),confidence_score:l,approved:!0,created_at:new Date().toISOString()}),await u.K.generateArticleEmbeddings(e,r.content_en,r.content_jp),await this.updateRelatedArticles(e)):await this.queueForReview(e,c,l)}catch(e){throw console.error("Error updating article content:",e),e}}buildUpdatePrompt(e,t,r){let a=t.map(e=>`- ${e.feedback_type}: ${e.suggestion||"No suggestion provided"}`).join("\n"),n=this.extractCommonQueries(r);return`
You are an expert content editor for an IT helpdesk knowledge base. 
Analyze the following article and suggest updates based on user feedback and search patterns.

Article Title: ${e.title_en} / ${e.title_jp}
Category: ${e.category}
Current Content (English):
${e.content_en}

Current Content (Japanese):
${e.content_jp}

Recent User Feedback:
${a||"No recent feedback"}

Common Search Queries Leading to This Article:
${n.join(", ")||"No search data available"}

Based on this information, suggest specific updates to improve the article's accuracy, clarity, and usefulness.
Format your response as JSON:
{
  "updates": [
    {
      "type": "content_addition" | "content_revision" | "example_addition" | "clarification",
      "location": "beginning" | "middle" | "end" | "specific_section",
      "content_en": "English content to add/revise",
      "content_jp": "Japanese content to add/revise",
      "reason": "Explanation of why this update is needed",
      "description": "Brief description of the change"
    }
  ],
  "metadata_updates": {
    "tags": ["new", "tags"],
    "related_articles": ["article_ids"]
  }
}
`}extractCommonQueries(e){let t=e.map(e=>e.query.toLowerCase()),r=new Map;return t.forEach(e=>{r.set(e,(r.get(e)||0)+1)}),Array.from(r.entries()).sort((e,t)=>t[1]-e[1]).slice(0,10).map(([e])=>e)}parseSuggestions(e){try{return JSON.parse(e).updates||[]}catch(e){return console.error("Error parsing AI suggestions:",e),[]}}async calculateUpdateConfidence(e,t){let r=.5;e.forEach(e=>{switch(e.type){case"clarification":case"example_addition":r+=.1;break;case"content_addition":r+=.05;break;case"content_revision":r-=.05}});let a=await this.getCategoryGuidelines(t.category);if(a){let t=this.checkGuidelineAlignment(e,a);r+=.2*t}return Math.min(r,1)}async applyUpdates(e,t){let r=e.content_en,a=e.content_jp;t.forEach(e=>{switch(e.type){case"content_addition":"end"===e.location?(r+="\n\n"+e.content_en,a+="\n\n"+e.content_jp):"beginning"===e.location&&(r=e.content_en+"\n\n"+r,a=e.content_jp+"\n\n"+a);break;case"content_revision":r+="\n\n**Updated Information:**\n"+e.content_en,a+="\n\n**更新情報:**\n"+e.content_jp;break;case"example_addition":r+="\n\n**Example:**\n"+e.content_en,a+="\n\n**例:**\n"+e.content_jp;break;case"clarification":r+="\n\n**Note:**\n"+e.content_en,a+="\n\n**注記:**\n"+e.content_jp}});let{error:n}=await d.N.from("kb_articles").update({content_en:r,content_jp:a,updated_at:new Date().toISOString(),last_auto_update:new Date().toISOString()}).eq("id",e.id);if(n)throw Error(`Failed to update article: ${n.message}`)}async queueForReview(e,t,r){let{error:a}=await d.N.from("kb_update_queue").insert({article_id:e,suggested_updates:t,confidence_score:r,status:"pending",created_at:new Date().toISOString()});a&&console.error("Error queuing for review:",a)}async logContentUpdate(e){let{error:t}=await d.N.from("kb_update_logs").insert(e);t&&console.error("Error logging update:",t)}async updateRelatedArticles(e){try{await u.K.findRelatedArticles(e,"en",5),await u.K.findRelatedArticles(e,"jp",5)}catch(e){console.error("Error updating related articles:",e)}}async getCategoryGuidelines(e){return({group_mail:{required_sections:["purpose","access_levels","request_process"],tone:"professional",min_examples:1},sharepoint:{required_sections:["overview","permissions","best_practices"],tone:"technical",min_examples:2},password_reset:{required_sections:["steps","requirements","troubleshooting"],tone:"helpful",min_examples:0},pc_admin:{required_sections:["justification","security_notes","process"],tone:"security-focused",min_examples:1}})[e]||null}checkGuidelineAlignment(e,t){if(!t)return .5;let r=0,a=0;return e.forEach(e=>{a++,"content_addition"===e.type||"clarification"===e.type?r+=.8:"example_addition"===e.type&&t.min_examples>0?r+=1:r+=.5}),a>0?r/a:.5}async schedulePeriodicReview(){try{let{data:e,error:t}=await d.N.from("kb_articles").select("id, updated_at").eq("status","published").or(`last_auto_update.is.null,last_auto_update.lt.${new Date(Date.now()-2592e6).toISOString()}`);if(t)throw Error(`Failed to fetch articles for review: ${t.message}`);if(!e||0===e.length){console.log("No articles need periodic review");return}for(let t=0;t<e.length;t+=5){let r=e.slice(t,t+5);await Promise.all(r.map(e=>this.updateArticleContent(e.id,"scheduled"))),t+5<e.length&&await new Promise(e=>setTimeout(e,5e3))}console.log(`Completed periodic review of ${e.length} articles`)}catch(e){throw console.error("Error in periodic review:",e),e}}async analyzeContentGaps(){try{let{data:e,error:t}=await d.N.from("kb_search_history").select("query, search_type").or("clicked_results.is.null,clicked_results.eq.{}").gte("created_at",new Date(Date.now()-6048e5).toISOString());if(t)throw Error(`Failed to fetch search history: ${t.message}`);if(!e||0===e.length){console.log("No content gaps identified");return}let r=this.groupSimilarQueries(e).filter(e=>e.count>=3).map(e=>({topic:e.representative,query_count:e.count,suggested_category:this.suggestCategory(e.representative),priority:this.calculateGapPriority(e)}));if(r.length>0){let{error:e}=await d.N.from("kb_content_gaps").insert(r.map(e=>({...e,status:"identified",created_at:new Date().toISOString()})));e?console.error("Error storing content gaps:",e):console.log(`Identified ${r.length} content gaps`)}}catch(e){throw console.error("Error analyzing content gaps:",e),e}}groupSimilarQueries(e){let t=new Map;return e.forEach(({query:e})=>{let r=e.toLowerCase().trim(),a=!1;for(let[n,o]of t.entries())if(this.queriesAreSimilar(r,n)){o.queries.push(e),o.count++,a=!0;break}a||t.set(r,{queries:[e],count:1})}),Array.from(t.entries()).map(([e,t])=>({representative:e,queries:t.queries,count:t.count}))}queriesAreSimilar(e,t){let r=new Set(e.split(/\s+/)),a=new Set(t.split(/\s+/)),n=new Set([...r].filter(e=>a.has(e))),o=new Set([...r,...a]);return n.size/o.size>.5}suggestCategory(e){let t=e.toLowerCase();for(let[e,r]of Object.entries({group_mail:["group","mail","email","distribution","list"],sharepoint:["sharepoint","spo","library","document","folder"],password_reset:["password","reset","forgot","locked","account"],pc_admin:["admin","administrator","privilege","install","software"]}))if(r.some(e=>t.includes(e)))return e;return"general"}calculateGapPriority(e){return e.count>=10?"high":e.count>=5?"medium":"low"}}let _=g.getInstance();async function f(e){try{let t=(0,c.createRouteHandlerClient)({cookies:l.UL}),{data:{user:r}}=await t.auth.getUser();if(!r)return s.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a}=await t.from("staff").select("role:roles(name)").eq("auth_id",r.id).single();if(!a||!["Global Administrator","Web App System Administrator","IT Helpdesk Support"].includes(a.role?.name))return s.NextResponse.json({error:"Insufficient permissions"},{status:403});let{articleId:n,updateType:o="manual",forceUpdate:i=!1}=await e.json();if(n)return await _.updateArticleContent(n,o),s.NextResponse.json({success:!0,message:`Article ${n} queued for update`});return await _.schedulePeriodicReview(),s.NextResponse.json({success:!0,message:"Periodic review initiated for all articles"})}catch(e){return console.error("Content update error:",e),s.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}async function m(e){try{let e=(0,c.createRouteHandlerClient)({cookies:l.UL}),{data:{user:t}}=await e.auth.getUser();if(!t)return s.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r,error:a}=await e.from("kb_update_queue").select("*").eq("status","pending").order("created_at",{ascending:!1}).limit(10);if(a)throw Error("Failed to fetch update queue");let{data:n,error:o}=await e.from("kb_update_logs").select("*").order("created_at",{ascending:!1}).limit(10);if(o)throw Error("Failed to fetch update logs");let{data:i,error:d}=await e.from("kb_content_gaps").select("*").eq("status","identified").order("priority",{ascending:!0}).limit(10);if(d)throw Error("Failed to fetch content gaps");return s.NextResponse.json({updateQueue:r||[],recentLogs:n||[],contentGaps:i||[],timestamp:new Date().toISOString()})}catch(e){return console.error("Get update status error:",e),s.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/knowledge-base/update-content/route",pathname:"/api/knowledge-base/update-content",filename:"route",bundlePath:"app/api/knowledge-base/update-content/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\update-content\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:y,serverHooks:b}=h;function k(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:y})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>r(75513));module.exports=a})();