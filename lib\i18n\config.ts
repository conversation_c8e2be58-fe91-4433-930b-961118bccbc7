// Japanese Localization Configuration
export const locales = ['ja', 'en'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'ja';

export const localeNames: Record<Locale, string> = {
  ja: '日本語',
  en: 'English'
};

// Japanese date formatting with 和暦 (Japanese Era) support
export const japaneseEras = [
  { name: '令和', romanji: 'Reiwa', startDate: new Date('2019-05-01') },
  { name: '平成', romanji: 'Heisei', startDate: new Date('1989-01-08') },
  { name: '昭和', romanji: 'Showa', startDate: new Date('1926-12-25') }
];

export function toJapaneseEra(date: Date): string {
  for (const era of japaneseEras) {
    if (date >= era.startDate) {
      const year = date.getFullYear() - era.startDate.getFullYear() + 1;
      return `${era.name}${year}年`;
    }
  }
  return date.getFullYear() + '年';
}

export function formatJapaneseDate(date: Date, includeEra = true): string {
  const year = includeEra ? toJapaneseEra(date) : `${date.getFullYear()}年`;
  const month = date.getMonth() + 1;
  const day = date.getDate();
  
  return `${year}${month}月${day}日`;
}

// Japanese business day calculation
export function isJapaneseBusinessDay(date: Date): boolean {
  const day = date.getDay();
  
  // Weekend check
  if (day === 0 || day === 6) return false;
  
  // Japanese national holidays (simplified - add more as needed)
  const holidays = [
    '01-01', // 元日
    '01-02', // 正月
    '01-03', // 正月
    '02-11', // 建国記念の日
    '02-23', // 天皇誕生日
    '03-20', // 春分の日（approximate）
    '04-29', // 昭和の日
    '05-03', // 憲法記念日
    '05-04', // みどりの日
    '05-05', // こどもの日
    '07-15', // 海の日（3rd Monday of July）
    '08-11', // 山の日
    '09-15', // 敬老の日（3rd Monday of September）
    '09-23', // 秋分の日（approximate）
    '10-10', // スポーツの日（2nd Monday of October）
    '11-03', // 文化の日
    '11-23', // 勤労感謝の日
  ];
  
  const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  return !holidays.includes(dateStr);
}

// Load locale dictionary
export async function loadDictionary(locale: Locale) {
  const dictionaries = {
    ja: () => import('./dictionaries/ja.json').then(module => module.default),
    en: () => import('./dictionaries/en.json').then(module => module.default)
  };
  
  return dictionaries[locale]();
}