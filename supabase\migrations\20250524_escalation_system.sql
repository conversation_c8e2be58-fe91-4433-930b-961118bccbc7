-- Create escalation rules table
CREATE TABLE IF NOT EXISTS escalation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  trigger_type VARCHAR(50) NOT NULL CHECK (trigger_type IN ('sla_breach', 'overdue', 'manual', 'approval_timeout')),
  conditions JSONB DEFAULT '[]',
  actions JSONB NOT NULL DEFAULT '[]',
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  is_active BOOLEAN DEFAULT true,
  retry_interval INTEGER, -- minutes
  max_retries INTEGER DEFAULT 3,
  applicable_to_categories TEXT[] DEFAULT '{}',
  applicable_to_departments UUID[] DEFAULT '{}',
  created_by UUID REFERENCES staff(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create escalation instances table
CREATE TABLE IF NOT EXISTS escalation_instances (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rule_id UUID REFERENCES escalation_rules(id) ON DELETE CASCADE,
  workflow_instance_id UUID,
  request_id UUID REFERENCES request_forms(id),
  triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  triggered_by VARCHAR(100) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  retry_count INTEGER DEFAULT 0,
  last_retry_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  failure_reason TEXT,
  action_results JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_escalation_rules_active ON escalation_rules(is_active) WHERE is_active = true;
CREATE INDEX idx_escalation_rules_trigger_type ON escalation_rules(trigger_type);
CREATE INDEX idx_escalation_instances_status ON escalation_instances(status);
CREATE INDEX idx_escalation_instances_request ON escalation_instances(request_id);
CREATE INDEX idx_escalation_instances_rule ON escalation_instances(rule_id);

-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_escalation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER update_escalation_rules_updated_at
  BEFORE UPDATE ON escalation_rules
  FOR EACH ROW
  EXECUTE FUNCTION update_escalation_updated_at();

CREATE TRIGGER update_escalation_instances_updated_at
  BEFORE UPDATE ON escalation_instances
  FOR EACH ROW
  EXECUTE FUNCTION update_escalation_updated_at();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON escalation_rules TO authenticated;
GRANT SELECT, INSERT, UPDATE ON escalation_instances TO authenticated;

-- Row Level Security
ALTER TABLE escalation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE escalation_instances ENABLE ROW LEVEL SECURITY;

-- Policies for escalation_rules
CREATE POLICY "Admin users can manage escalation rules" ON escalation_rules
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

CREATE POLICY "All authenticated users can view active rules" ON escalation_rules
  FOR SELECT TO authenticated
  USING (is_active = true);

-- Policies for escalation_instances
CREATE POLICY "Admin users can manage escalation instances" ON escalation_instances
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

CREATE POLICY "Users can view their own escalations" ON escalation_instances
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM request_forms rf
      WHERE rf.id = escalation_instances.request_id
      AND rf.requester_id IN (
        SELECT id FROM staff WHERE auth_id = auth.uid()
      )
    )
  );

-- Insert default notification templates for escalations
INSERT INTO notification_templates (type, channel, name_en, name_jp, subject_en, subject_jp, body_en, body_jp)
VALUES 
  (
    'escalation',
    'email',
    'Request Escalated',
    'リクエストがエスカレーションされました',
    'Request {{requestId}} has been escalated',
    'リクエスト {{requestId}} がエスカレーションされました',
    'Your request has been escalated due to {{reason}}. Our team is working on resolving this with high priority.',
    'お客様のリクエストが{{reason}}のためエスカレーションされました。私たちのチームは高い優先度で解決に取り組んでいます。'
  ),
  (
    'escalation',
    'in_app',
    'Request Escalated',
    'リクエストがエスカレーションされました',
    'Request Escalated',
    'エスカレーション通知',
    'Request {{requestId}} has been escalated',
    'リクエスト {{requestId}} がエスカレーションされました'
  );
