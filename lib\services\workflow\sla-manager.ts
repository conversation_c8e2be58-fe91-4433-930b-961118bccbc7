import { createClient } from '@/lib/supabase/server';
import { SLADefinition, SLATracking } from './types';
import { addMinutes, isWithinInterval, setHours, setMinutes, addDays, isWeekend, differenceInMinutes, format } from 'date-fns';
import { auditLogger } from '@/lib/services/audit';

export class SLAManager {
  private businessHours = {
    start: { hour: 9, minute: 0 },  // 9:00 AM JST
    end: { hour: 18, minute: 0 },    // 6:00 PM JST
  };

  // Japanese holidays for 2025 (和暦)
  private holidays: Date[] = [
    new Date('2025-01-01'), // 元日
    new Date('2025-01-13'), // 成人の日
    new Date('2025-02-11'), // 建国記念の日
    new Date('2025-02-23'), // 天皇誕生日
    new Date('2025-03-20'), // 春分の日
    new Date('2025-04-29'), // 昭和の日
    new Date('2025-05-03'), // 憲法記念日
    new Date('2025-05-04'), // みどりの日
    new Date('2025-05-05'), // こどもの日
    new Date('2025-07-21'), // 海の日
    new Date('2025-08-11'), // 山の日
    new Date('2025-09-15'), // 敬老の日
    new Date('2025-09-23'), // 秋分の日
    new Date('2025-10-13'), // スポーツの日
    new Date('2025-11-03'), // 文化の日
    new Date('2025-11-23'), // 勤労感謝の日
  ];

  /**
   * Initialize SLA tracking for a workflow instance
   */
  async initializeSLA(
    workflowInstanceId: string,
    workflowDef: any
  ): Promise<void> {
    const supabase = createClient();

    // Get request details to determine SLA
    const { data: instance } = await supabase
      .from('workflow_instances')
      .select('*, request_forms!inner(*)')
      .eq('id', workflowInstanceId)
      .single();

    if (!instance) return;

    // Get applicable SLA definition
    const { data: slaDefinition } = await supabase
      .from('sla_definitions')
      .select('*')
      .eq('service_category_id', instance.request_forms.service_category_id)
      .eq('priority', instance.request_forms.priority || 'medium')
      .single();

    if (!slaDefinition) {
      // Try to get default SLA
      const { data: defaultSLA } = await supabase
        .from('sla_definitions')
        .select('*')
        .is('service_category_id', null)
        .eq('priority', instance.request_forms.priority || 'medium')
        .single();
      
      if (defaultSLA) {
        slaDefinition = defaultSLA;
      }
    }

    if (!slaDefinition) return;

    // Calculate target dates
    const now = new Date();
    const targetResponseDate = this.calculateTargetDate(
      now,
      slaDefinition.response_time_minutes,
      slaDefinition.business_hours_only
    );
    const targetResolutionDate = this.calculateTargetDate(
      now,
      slaDefinition.resolution_time_minutes,
      slaDefinition.business_hours_only
    );

    // Create SLA tracking record
    await supabase.from('sla_tracking').insert({
      workflow_instance_id: workflowInstanceId,
      sla_definition_id: slaDefinition.id,
      target_response_date: targetResponseDate.toISOString(),
      target_resolution_date: targetResolutionDate.toISOString(),
      status: 'on_track',
    });

    // Schedule SLA monitoring
    await this.scheduleSLAMonitoring(workflowInstanceId, slaDefinition.id);
  }

  /**
   * Calculate due date based on SLA minutes
   */
  calculateDueDate(
    slaMinutes?: number,
    businessHoursOnly: boolean = true
  ): Date {
    const now = new Date();
    
    if (!slaMinutes) {
      return addMinutes(now, 240); // Default 4 hours
    }

    return this.calculateTargetDate(now, slaMinutes, businessHoursOnly);
  }

  /**
   * Calculate target date considering business hours and holidays
   */
  private calculateTargetDate(
    startDate: Date,
    minutes: number,
    businessHoursOnly: boolean
  ): Date {
    if (!businessHoursOnly) {
      return addMinutes(startDate, minutes);
    }

    let currentDate = new Date(startDate);
    let remainingMinutes = minutes;

    while (remainingMinutes > 0) {
      // Skip weekends and holidays
      if (this.isNonBusinessDay(currentDate)) {
        currentDate = this.getNextBusinessDay(currentDate);
        currentDate = setHours(currentDate, this.businessHours.start.hour);
        currentDate = setMinutes(currentDate, this.businessHours.start.minute);
        continue;
      }

      // Check if we're within business hours
      const businessStart = setHours(setMinutes(new Date(currentDate), this.businessHours.start.minute), this.businessHours.start.hour);
      const businessEnd = setHours(setMinutes(new Date(currentDate), this.businessHours.end.minute), this.businessHours.end.hour);

      if (currentDate < businessStart) {
        currentDate = businessStart;
      } else if (currentDate >= businessEnd) {
        currentDate = this.getNextBusinessDay(currentDate);
        currentDate = setHours(currentDate, this.businessHours.start.hour);
        currentDate = setMinutes(currentDate, this.businessHours.start.minute);
        continue;
      }

      // Calculate minutes until end of business day
      const minutesUntilEndOfDay = differenceInMinutes(businessEnd, currentDate);

      if (remainingMinutes <= minutesUntilEndOfDay) {
        // Can complete within current business day
        currentDate = addMinutes(currentDate, remainingMinutes);
        remainingMinutes = 0;
      } else {
        // Need to continue to next business day
        remainingMinutes -= minutesUntilEndOfDay;
        currentDate = this.getNextBusinessDay(currentDate);
        currentDate = setHours(currentDate, this.businessHours.start.hour);
        currentDate = setMinutes(currentDate, this.businessHours.start.minute);
      }
    }

    return currentDate;
  }

  /**
   * Check if a date is a non-business day
   */
  private isNonBusinessDay(date: Date): boolean {
    // Check if weekend
    if (isWeekend(date)) {
      return true;
    }

    // Check if holiday
    const dateString = format(date, 'yyyy-MM-dd');
    return this.holidays.some(holiday => format(holiday, 'yyyy-MM-dd') === dateString);
  }

  /**
   * Get the next business day
   */
  private getNextBusinessDay(date: Date): Date {
    let nextDay = addDays(date, 1);
    while (this.isNonBusinessDay(nextDay)) {
      nextDay = addDays(nextDay, 1);
    }
    return nextDay;
  }

  /**
   * Update SLA tracking when a task is completed
   */
  async updateTaskCompletion(taskId: string): Promise<void> {
    const supabase = createClient();

    // Get task details
    const { data: task } = await supabase
      .from('workflow_tasks')
      .select('*, workflow_instances!inner(*)')
      .eq('id', taskId)
      .single();

    if (!task) return;

    // Get SLA tracking
    const { data: slaTracking } = await supabase
      .from('sla_tracking')
      .select('*')
      .eq('workflow_instance_id', task.workflow_instance_id)
      .single();

    if (!slaTracking) return;

    // Update response time if this is the first response
    if (!slaTracking.actual_response_date && task.task_type === 'response') {
      await supabase
        .from('sla_tracking')
        .update({
          actual_response_date: new Date().toISOString(),
          status: this.calculateSLAStatus(slaTracking, { responseCompleted: true }),
        })
        .eq('id', slaTracking.id);
    }
  }

  /**
   * Complete SLA tracking
   */
  async completeSLA(workflowInstanceId: string): Promise<void> {
    const supabase = createClient();
    const now = new Date();

    const { data: slaTracking } = await supabase
      .from('sla_tracking')
      .select('*')
      .eq('workflow_instance_id', workflowInstanceId)
      .single();

    if (!slaTracking) return;

    const updates: any = {
      actual_resolution_date: now.toISOString(),
    };

    // Set response date if not already set
    if (!slaTracking.actual_response_date) {
      updates.actual_response_date = now.toISOString();
    }

    // Calculate final status
    const targetResolution = new Date(slaTracking.target_resolution_date);
    if (now <= targetResolution) {
      updates.status = 'on_track';
    } else {
      updates.status = 'breached';
      updates.breach_reason = 'Resolution time exceeded';
    }

    await supabase
      .from('sla_tracking')
      .update(updates)
      .eq('id', slaTracking.id);

    // Log SLA completion
    await auditLogger.log({
      action: 'SLA_COMPLETED',
      details: {
        workflow_instance_id: workflowInstanceId,
        status: updates.status,
        response_time: slaTracking.actual_response_date ? 
          differenceInMinutes(new Date(slaTracking.actual_response_date), new Date(slaTracking.created_at)) : null,
        resolution_time: differenceInMinutes(now, new Date(slaTracking.created_at)),
      },
    });
  }

  /**
   * Calculate current SLA status
   */
  private calculateSLAStatus(
    tracking: SLATracking,
    updates?: { responseCompleted?: boolean; resolutionCompleted?: boolean }
  ): 'on_track' | 'at_risk' | 'breached' {
    const now = new Date();

    // Check response SLA
    if (!tracking.actual_response_date && !updates?.responseCompleted) {
      const targetResponse = new Date(tracking.target_response_date);
      const timeUntilResponse = differenceInMinutes(targetResponse, now);
      
      if (now > targetResponse) {
        return 'breached';
      } else if (timeUntilResponse < 60) { // Less than 1 hour
        return 'at_risk';
      }
    }

    // Check resolution SLA
    if (!tracking.actual_resolution_date && !updates?.resolutionCompleted) {
      const targetResolution = new Date(tracking.target_resolution_date);
      const timeUntilResolution = differenceInMinutes(targetResolution, now);
      
      if (now > targetResolution) {
        return 'breached';
      } else if (timeUntilResolution < 120) { // Less than 2 hours
        return 'at_risk';
      }
    }

    return 'on_track';
  }

  /**
   * Monitor SLA status and trigger alerts
   */
  async monitorSLA(): Promise<void> {
    const supabase = createClient();

    // Get active SLA trackings
    const { data: trackings } = await supabase
      .from('sla_tracking')
      .select('*, workflow_instances!inner(*), sla_definitions!inner(*)')
      .is('actual_resolution_date', null);

    if (!trackings) return;

    for (const tracking of trackings) {
      const currentStatus = this.calculateSLAStatus(tracking);
      
      if (currentStatus !== tracking.status) {
        // Update status
        await supabase
          .from('sla_tracking')
          .update({ status: currentStatus })
          .eq('id', tracking.id);

        // Trigger alerts based on status change
        if (currentStatus === 'at_risk') {
          await this.triggerSLAAlert(tracking, 'at_risk');
        } else if (currentStatus === 'breached') {
          await this.triggerSLAAlert(tracking, 'breached');
          
          // Update breach reason
          const now = new Date();
          const breachReason = !tracking.actual_response_date && now > new Date(tracking.target_response_date)
            ? 'Response time exceeded'
            : 'Resolution time exceeded';
            
          await supabase
            .from('sla_tracking')
            .update({ breach_reason: breachReason })
            .eq('id', tracking.id);
        }
      }
    }
  }

  /**
   * Trigger SLA alerts
   */
  private async triggerSLAAlert(
    tracking: any,
    alertType: 'at_risk' | 'breached'
  ): Promise<void> {
    // This would integrate with the notification system
    console.log(`SLA Alert: ${alertType} for workflow ${tracking.workflow_instance_id}`);
    
    // Log the alert
    await auditLogger.log({
      action: 'SLA_ALERT',
      details: {
        workflow_instance_id: tracking.workflow_instance_id,
        alert_type: alertType,
        sla_definition_id: tracking.sla_definition_id,
      },
    });
  }

  /**
   * Schedule SLA monitoring
   */
  private async scheduleSLAMonitoring(
    workflowInstanceId: string,
    slaDefinitionId: string
  ): Promise<void> {
    // In a production environment, this would schedule a job
    // For now, we'll just log it
    console.log(`Scheduled SLA monitoring for workflow ${workflowInstanceId}`);
  }

  /**
   * Get SLA metrics and reports
   */
  async getSLAMetrics(
    startDate: Date,
    endDate: Date,
    departmentId?: string
  ): Promise<any> {
    const supabase = createClient();

    const query = supabase
      .from('sla_tracking')
      .select('*, sla_definitions!inner(*), workflow_instances!inner(*, request_forms!inner(*))')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (departmentId) {
      query.eq('workflow_instances.request_forms.department_id', departmentId);
    }

    const { data: trackings } = await query;

    if (!trackings) return null;

    // Calculate metrics
    const metrics = {
      total: trackings.length,
      met: trackings.filter(t => t.status !== 'breached').length,
      breached: trackings.filter(t => t.status === 'breached').length,
      at_risk: trackings.filter(t => t.status === 'at_risk').length,
      compliance_rate: 0,
      average_response_time: 0,
      average_resolution_time: 0,
      by_priority: {} as Record<string, any>,
      by_category: {} as Record<string, any>,
      breach_reasons: {} as Record<string, number>,
    };

    metrics.compliance_rate = metrics.total > 0 ? (metrics.met / metrics.total) * 100 : 0;

    // Calculate average times
    const responseTimes: number[] = [];
    const resolutionTimes: number[] = [];

    trackings.forEach(tracking => {
      if (tracking.actual_response_date) {
        const actualResponse = new Date(tracking.actual_response_date);
        const created = new Date(tracking.created_at);
        responseTimes.push(differenceInMinutes(actualResponse, created));
      }

      if (tracking.actual_resolution_date) {
        const actualResolution = new Date(tracking.actual_resolution_date);
        const created = new Date(tracking.created_at);
        resolutionTimes.push(differenceInMinutes(actualResolution, created));
      }

      // Group by priority
      const priority = tracking.sla_definitions.priority || 'medium';
      if (!metrics.by_priority[priority]) {
        metrics.by_priority[priority] = { total: 0, met: 0, breached: 0, at_risk: 0 };
      }
      metrics.by_priority[priority].total++;
      if (tracking.status === 'on_track') {
        metrics.by_priority[priority].met++;
      } else if (tracking.status === 'breached') {
        metrics.by_priority[priority].breached++;
      } else if (tracking.status === 'at_risk') {
        metrics.by_priority[priority].at_risk++;
      }

      // Track breach reasons
      if (tracking.breach_reason) {
        metrics.breach_reasons[tracking.breach_reason] = (metrics.breach_reasons[tracking.breach_reason] || 0) + 1;
      }
    });

    if (responseTimes.length > 0) {
      metrics.average_response_time = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    }

    if (resolutionTimes.length > 0) {
      metrics.average_resolution_time = resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length;
    }

    return metrics;
  }

  /**
   * Get at-risk SLA items
   */
  async getAtRiskItems(): Promise<any[]> {
    const supabase = createClient();

    const { data: items } = await supabase
      .from('sla_tracking')
      .select(`
        *,
        workflow_instances!inner(
          *,
          request_forms!inner(
            id,
            title,
            requester_id,
            priority,
            staff!inner(name_en, name_jp)
          )
        ),
        sla_definitions!inner(*)
      `)
      .eq('status', 'at_risk')
      .is('actual_resolution_date', null)
      .order('target_resolution_date', { ascending: true });

    return items || [];
  }

  /**
   * Export SLA report
   */
  async exportSLAReport(
    startDate: Date,
    endDate: Date,
    format: 'csv' | 'json' = 'json'
  ): Promise<any> {
    const metrics = await this.getSLAMetrics(startDate, endDate);
    
    if (format === 'json') {
      return metrics;
    }
    
    // Convert to CSV format
    // This is a simplified version - in production, use a proper CSV library
    const csv = [
      'Metric,Value',
      `Total Requests,${metrics.total}`,
      `Met SLA,${metrics.met}`,
      `Breached SLA,${metrics.breached}`,
      `At Risk,${metrics.at_risk}`,
      `Compliance Rate,${metrics.compliance_rate.toFixed(2)}%`,
      `Average Response Time,${metrics.average_response_time.toFixed(0)} minutes`,
      `Average Resolution Time,${metrics.average_resolution_time.toFixed(0)} minutes`,
    ].join('\n');

    return csv;
  }
}

// Export singleton instance
export const slaManager = new SLAManager();
