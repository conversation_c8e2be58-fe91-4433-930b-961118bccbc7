"use strict";exports.id=8535,exports.ids=[8535],exports.modules={41680:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(58009);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:l,className:u="",children:a,iconNode:s,...c},d)=>(0,n.createElement)("svg",{ref:d,...i,width:t,height:t,stroke:e,strokeWidth:l?24*Number(r)/Number(t):r,className:o("lucide",u),...c},[...s.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(a)?a:[a]])),a=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},a)=>(0,n.createElement)(u,{ref:a,iconNode:t,className:o(`lucide-${l(e)}`,r),...i}));return r.displayName=`${e}`,r}},31412:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},39217:(e,t,r)=>{r.d(t,{N:()=>a});var n=r(58009),l=r(6004),o=r(29952),i=r(12705),u=r(45512);function a(e){let t=e+"CollectionProvider",[r,a]=(0,l.A)(t),[s,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,l=n.useRef(null),o=n.useRef(new Map).current;return(0,u.jsx)(s,{scope:t,itemMap:o,collectionRef:l,children:r})};d.displayName=t;let f=e+"CollectionSlot",m=(0,i.TL)(f),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=c(f,r),i=(0,o.s)(t,l.collectionRef);return(0,u.jsx)(m,{ref:i,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",N="data-radix-collection-item",y=(0,i.TL)(v),g=n.forwardRef((e,t)=>{let{scope:r,children:l,...i}=e,a=n.useRef(null),s=(0,o.s)(t,a),d=c(v,r);return n.useEffect(()=>(d.itemMap.set(a,{ref:a,...i}),()=>void d.itemMap.delete(a))),(0,u.jsx)(y,{[N]:"",ref:s,children:l})});return g.displayName=v,[{Provider:d,Slot:p,ItemSlot:g},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${N}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}},29952:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>o});var n=r(58009);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},6004:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>o});var n=r(58009),l=r(45512);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,i=n.useMemo(()=>o,Object.values(o));return(0,l.jsx)(r.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(l){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return o.scopeName=e,[function(t,o){let i=n.createContext(o),u=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,s=r?.[e]?.[u]||i,c=n.useMemo(()=>a,Object.values(a));return(0,l.jsx)(s.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(r,l){let a=l?.[e]?.[u]||i,s=n.useContext(a);if(s)return s;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(o,...t)]}},59018:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(58009);r(45512);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}},30096:(e,t,r)=>{r.d(t,{B:()=>a});var n,l=r(58009),o=r(49397),i=(n||(n=r.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,r]=l.useState(i());return(0,o.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},98060:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(58009),l=r(29952),o=r(49397),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[l,i]=n.useState(),a=n.useRef(null),s=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=u(a.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=a.current,r=s.current;if(r!==e){let n=c.current,l=u(t);e?f("MOUNT"):"none"===l||t?.display==="none"?f("UNMOUNT"):r&&n!==l?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,o.N)(()=>{if(l){let e;let t=l.ownerDocument.defaultView??window,r=r=>{let n=u(a.current).includes(r.animationName);if(r.target===l&&n&&(f("ANIMATION_END"),!s.current)){let r=l.style.animationFillMode;l.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=r)})}},n=e=>{e.target===l&&(c.current=u(a.current))};return l.addEventListener("animationstart",n),l.addEventListener("animationcancel",r),l.addEventListener("animationend",r),()=>{t.clearTimeout(e),l.removeEventListener("animationstart",n),l.removeEventListener("animationcancel",r),l.removeEventListener("animationend",r)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),a="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),s=(0,l.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||i.isPresent?n.cloneElement(a,{ref:s}):null};function u(e){return e?.animationName||"none"}i.displayName="Presence"},30830:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>u});var n=r(58009),l=r(55740),o=r(12705),i=r(45512),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...o}=e,u=l?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(u,{...o,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function a(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},12705:(e,t,r)=>{r.d(t,{DX:()=>u,Dc:()=>s,TL:()=>i});var n=r(58009),l=r(29952),o=r(45512);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e,i;let u=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,a=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{let t=o(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(a.ref=t?(0,l.t)(t,u):u),n.cloneElement(r,a)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...i}=e,u=n.Children.toArray(l),a=u.find(c);if(a){let e=a.props.children,l=u.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...i,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var u=i("Slot"),a=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},92828:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(58009);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},13024:(e,t,r)=>{r.d(t,{i:()=>u});var n,l=r(58009),o=r(49397),i=(n||(n=r.t(l,2)))[" useInsertionEffect ".trim().toString()]||o.N;function u({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,u,a]=function({defaultProp:e,onChange:t}){let[r,n]=l.useState(e),o=l.useRef(r),u=l.useRef(t);return i(()=>{u.current=t},[t]),l.useEffect(()=>{o.current!==r&&(u.current?.(r),o.current=r)},[r,o]),[r,n,u]}({defaultProp:t,onChange:r}),s=void 0!==e,c=s?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[c,l.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else u(t)},[s,e,u,a])]}Symbol("RADIX:SYNC_STATE")},49397:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(58009),l=globalThis?.document?n.useLayoutEffect:()=>{}},21643:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(82281);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:u}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==u?void 0:u[e];if(null===t)return null;let o=l(t)||l(n);return i[e][o]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,a,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...u,...s}[t]):({...u,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}};