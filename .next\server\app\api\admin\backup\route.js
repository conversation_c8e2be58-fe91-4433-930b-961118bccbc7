"use strict";(()=>{var e={};e.id=8452,e.ids=[8452],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{e.exports=require("crypto")},23259:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>k});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>c});var a=t(42706),n=t(28203),o=t(45994),i=t(39187),u=t(37344),p=t(61487);async function c(e){try{let r=await l();if(r)return r;let{description:t}=await e.json(),s=await u.w.createBackup(t);return i.NextResponse.json({success:!0,backup:s})}catch(e){return console.error("Backup creation failed:",e),i.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e){try{let r=await l();if(r)return r;let t=new URL(e.url),s=parseInt(t.searchParams.get("limit")||"50"),a=await u.w.listBackups(s);return i.NextResponse.json({success:!0,backups:a})}catch(e){return console.error("Failed to list backups:",e),i.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function l(){let e=(0,p.U)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await e.from("user_profiles").select("role").eq("user_id",r.id).single();return s&&["admin","system_admin"].includes(s.role)?null:i.NextResponse.json({error:"Insufficient permissions"},{status:403})}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/backup/route",pathname:"/api/admin/backup",filename:"route",bundlePath:"app/api/admin/backup/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:k,serverHooks:f}=x;function w(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:k})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(23259));module.exports=s})();