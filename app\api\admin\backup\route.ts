/**
 * Backup Management API
 * Provides endpoints for backup creation, restoration, and management
 */

import { NextRequest, NextResponse } from 'next/server'
import { backupManager } from '@/lib/backup/backup-manager'
import { createClient } from '@/lib/supabase/server'

/**
 * Create a new backup
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication and authorization
    const authResult = await checkAdminAuth()
    if (authResult) return authResult

    const body = await request.json()
    const { description } = body

    // Create backup
    const metadata = await backupManager.createBackup(description)

    return NextResponse.json({
      success: true,
      backup: metadata
    })

  } catch (error) {
    console.error('Backup creation failed:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * List available backups
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication and authorization
    const authResult = await checkAdmin<PERSON>uth()
    if (authResult) return authResult

    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '50')

    const backups = await backupManager.listBackups(limit)

    return NextResponse.json({
      success: true,
      backups
    })

  } catch (error) {
    console.error('Failed to list backups:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * Check admin authentication
 */
async function checkAdminAuth(): Promise<NextResponse | null> {
  const supabase = createClient()
  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    )
  }

  // Check if user has admin role
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('role')
    .eq('user_id', user.id)
    .single()

  if (!profile || !['admin', 'system_admin'].includes(profile.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }

  return null
}
