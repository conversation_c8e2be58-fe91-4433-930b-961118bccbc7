'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { Locale, defaultLocale } from './config'
import { translations } from './translations'

interface I18nContextType {
  locale: Locale
  setLocale: (locale: Locale) => void
  t: (key: string, params?: Record<string, any>) => string
  formatDate: (date: Date, format?: 'date' | 'time' | 'datetime' | 'wareki') => string
}

const I18nContext = createContext<I18nContextType | undefined>(undefined)

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(defaultLocale)

  useEffect(() => {
    // Load saved locale from localStorage
    const savedLocale = localStorage.getItem('locale') as Locale
    if (savedLocale && (savedLocale === 'ja' || savedLocale === 'en')) {
      setLocaleState(savedLocale)
    }
  }, [])

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale)
    localStorage.setItem('locale', newLocale)
  }

  const t = (key: string, params?: Record<string, any>): string => {
    const keys = key.split('.')
    let value: any = translations[locale]

    for (const k of keys) {
      value = value?.[k]
    }

    if (typeof value !== 'string') {
      console.warn(`Translation key not found: ${key}`)
      return key
    }

    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] || match
      })
    }

    return value
  }

  const formatDate = (date: Date, format?: 'date' | 'time' | 'datetime' | 'wareki') => {
    const options: Intl.DateTimeFormatOptions = {}

    switch (format) {
      case 'time':
        options.hour = '2-digit'
        options.minute = '2-digit'
        break
      case 'datetime':
        options.year = 'numeric'
        options.month = '2-digit'
        options.day = '2-digit'
        options.hour = '2-digit'
        options.minute = '2-digit'
        break
      case 'wareki':
        if (locale === 'ja') {
          return new Intl.DateTimeFormat('ja-JP-u-ca-japanese', {
            era: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }).format(date)
        }
        // Fall through to default for non-Japanese
      default:
        options.year = 'numeric'
        options.month = '2-digit'
        options.day = '2-digit'
    }

    return new Intl.DateTimeFormat(locale === 'ja' ? 'ja-JP' : 'en-US', options).format(date)
  }

  return (
    <I18nContext.Provider value={{ locale, setLocale, t, formatDate }}>
      {children}
    </I18nContext.Provider>
  )
}

export function useI18n() {
  const context = useContext(I18nContext)
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  return context
}
