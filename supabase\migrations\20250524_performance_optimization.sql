-- Performance Optimization Migration
-- Add indexes for frequently queried columns and optimize database performance

-- Request Forms Indexes
CREATE INDEX IF NOT EXISTS idx_request_forms_requester_id ON request_forms(requester_id);
CREATE INDEX IF NOT EXISTS idx_request_forms_status ON request_forms(status);
CREATE INDEX IF NOT EXISTS idx_request_forms_created_at ON request_forms(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_request_forms_service_category ON request_forms(service_category_id);
CREATE INDEX IF NOT EXISTS idx_request_forms_department ON request_forms(requester_id, created_at DESC);

-- Request Items Indexes
CREATE INDEX IF NOT EXISTS idx_request_items_submission_id ON request_items(submission_id);
CREATE INDEX IF NOT EXISTS idx_request_items_service_type ON request_items(service_type);
CREATE INDEX IF NOT EXISTS idx_request_items_status ON request_items(status);
CREATE INDEX IF NOT EXISTS idx_request_items_affected_users ON request_items USING GIN(affected_users);

-- Staff Indexes
CREATE INDEX IF NOT EXISTS idx_staff_email ON staff(email);
CREATE INDEX IF NOT EXISTS idx_staff_division_id ON staff(division_id);
CREATE INDEX IF NOT EXISTS idx_staff_auth_id ON staff(auth_id);
CREATE INDEX IF NOT EXISTS idx_staff_is_active ON staff(is_active);
CREATE INDEX IF NOT EXISTS idx_staff_search ON staff USING GIN(to_tsvector('simple', name_en || ' ' || name_jp || ' ' || COALESCE(email, '')));

-- Workflow Indexes
CREATE INDEX IF NOT EXISTS idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_created_at ON workflow_instances(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_context ON workflow_instances USING GIN(context);
CREATE INDEX IF NOT EXISTS idx_workflow_tasks_instance_id ON workflow_tasks(workflow_instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_tasks_assigned_to ON workflow_tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_workflow_tasks_status ON workflow_tasks(status);

-- SLA Tracking Indexes
CREATE INDEX IF NOT EXISTS idx_sla_tracking_instance_id ON sla_tracking(workflow_instance_id);
CREATE INDEX IF NOT EXISTS idx_sla_tracking_status ON sla_tracking(sla_status);
CREATE INDEX IF NOT EXISTS idx_sla_tracking_target_date ON sla_tracking(target_date);

-- Audit Logs Indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs(entity_type, entity_id);

-- Notification Indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);

-- Composite Indexes for Common Queries
CREATE INDEX IF NOT EXISTS idx_request_forms_composite ON request_forms(requester_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_composite ON workflow_instances(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_staff_composite ON staff(division_id, is_active, name_en);

-- Partial Indexes for Better Performance
CREATE INDEX IF NOT EXISTS idx_active_workflows ON workflow_instances(id) WHERE status IN ('pending', 'in_progress', 'pending_approval');
CREATE INDEX IF NOT EXISTS idx_pending_requests ON request_forms(id) WHERE status = 'pending';
CREATE INDEX IF NOT EXISTS idx_unread_notifications ON notifications(user_id, created_at DESC) WHERE read = false;

-- Function-based Indexes
CREATE INDEX IF NOT EXISTS idx_request_forms_month ON request_forms(DATE_TRUNC('month', created_at));
CREATE INDEX IF NOT EXISTS idx_workflow_instances_day ON workflow_instances(DATE_TRUNC('day', created_at));

-- Materialized Views for Complex Queries
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_department_request_stats AS
SELECT 
  d.id as department_id,
  d.name_en as department_name,
  d.name_jp as department_name_jp,
  COUNT(DISTINCT rf.id) as total_requests,
  COUNT(DISTINCT rf.id) FILTER (WHERE rf.status = 'completed') as completed_requests,
  COUNT(DISTINCT rf.id) FILTER (WHERE rf.status = 'pending') as pending_requests,
  COUNT(DISTINCT rf.id) FILTER (WHERE rf.created_at >= CURRENT_DATE - INTERVAL '30 days') as recent_requests,
  AVG(EXTRACT(EPOCH FROM (rf.completed_at - rf.created_at))/3600)::numeric(10,2) as avg_completion_hours
FROM divisions d
LEFT JOIN staff s ON s.division_id = d.id
LEFT JOIN request_forms rf ON rf.requester_id = s.id
GROUP BY d.id, d.name_en, d.name_jp;

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_department_request_stats ON mv_department_request_stats(department_id);

-- Materialized View for Workflow Performance Metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_workflow_performance AS
SELECT 
  wf.workflow_definition_id,
  COUNT(*) as total_instances,
  COUNT(*) FILTER (WHERE wf.status = 'completed') as completed_count,
  COUNT(*) FILTER (WHERE wf.status = 'failed') as failed_count,
  AVG(EXTRACT(EPOCH FROM (wf.completed_at - wf.created_at))/3600)::numeric(10,2) as avg_duration_hours,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (wf.completed_at - wf.created_at))) as median_duration_seconds,
  PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (wf.completed_at - wf.created_at))) as p95_duration_seconds
FROM workflow_instances wf
WHERE wf.created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY wf.workflow_definition_id;

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_workflow_performance ON mv_workflow_performance(workflow_definition_id);

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_performance_views()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_department_request_stats;
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_workflow_performance;
END;
$$ LANGUAGE plpgsql;

-- Schedule periodic refresh (requires pg_cron extension)
-- SELECT cron.schedule('refresh-performance-views', '*/15 * * * *', 'SELECT refresh_performance_views()');

-- Query optimization hints
-- Analyze tables to update statistics
ANALYZE request_forms;
ANALYZE request_items;
ANALYZE staff;
ANALYZE workflow_instances;
ANALYZE workflow_tasks;
ANALYZE sla_tracking;
ANALYZE audit_logs;
ANALYZE notifications;

-- Vacuum tables to reclaim space
VACUUM ANALYZE request_forms;
VACUUM ANALYZE workflow_instances;
VACUUM ANALYZE audit_logs;

-- Add table partitioning for large tables (audit_logs)
-- This requires more complex migration but is shown here for reference
/*
-- Create partitioned table
CREATE TABLE IF NOT EXISTS audit_logs_partitioned (
  LIKE audit_logs INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- Create partitions for each month
CREATE TABLE IF NOT EXISTS audit_logs_y2025m01 PARTITION OF audit_logs_partitioned
  FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
  
CREATE TABLE IF NOT EXISTS audit_logs_y2025m02 PARTITION OF audit_logs_partitioned
  FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
  
-- Continue for more months...

-- Migrate data
INSERT INTO audit_logs_partitioned SELECT * FROM audit_logs;

-- Rename tables
ALTER TABLE audit_logs RENAME TO audit_logs_old;
ALTER TABLE audit_logs_partitioned RENAME TO audit_logs;
*/

-- Add comments for documentation
COMMENT ON INDEX idx_request_forms_requester_id IS 'Speeds up queries filtering by requester';
COMMENT ON INDEX idx_staff_search IS 'Full-text search index for staff names and emails';
COMMENT ON INDEX idx_active_workflows IS 'Partial index for active workflow queries';
COMMENT ON MATERIALIZED VIEW mv_department_request_stats IS 'Pre-calculated department statistics for dashboard';
