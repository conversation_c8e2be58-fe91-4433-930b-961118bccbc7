import { useCallback } from 'react';
import { notificationService, NotificationData, NotificationChannel } from '@/lib/services/notifications/notification-service';
import { createClient } from '@/lib/supabase/client';
import { useToast } from '@/components/ui/use-toast';

export function useNotifications() {
  const { toast } = useToast();
  const supabase = createClient();

  const sendNotification = useCallback(async (
    title: string,
    message: string,
    options?: {
      titleJp?: string;
      messageJp?: string;
      type?: 'info' | 'success' | 'warning' | 'error';
      priority?: 'low' | 'medium' | 'high' | 'critical';
      channels?: NotificationChannel[];
      actionUrl?: string;
      metadata?: Record<string, any>;
    }
  ) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const notification: NotificationData = {
        userId: user.id,
        title,
        message,
        titleJp: options?.titleJp,
        messageJp: options?.messageJp,
        type: options?.type || 'info',
        priority: options?.priority || 'medium',
        channels: options?.channels || ['in-app'],
        actionUrl: options?.actionUrl,
        metadata: options?.metadata
      };

      await notificationService.sendNotification(notification);

      toast({
        title: 'Notification sent',
        description: 'Your notification has been sent successfully.',
      });
    } catch (error) {
      console.error('Failed to send notification:', error);
      toast({
        title: 'Failed to send notification',
        description: 'There was an error sending your notification.',
        variant: 'destructive',
      });
    }
  }, [supabase, toast]);

  const sendTemplateNotification = useCallback(async (
    templateId: string,
    variables: Record<string, any>,
    userId?: string
  ) => {
    try {
      const targetUserId = userId || (await supabase.auth.getUser()).data.user?.id;
      if (!targetUserId) {
        throw new Error('User not authenticated');
      }

      await notificationService.sendFromTemplate(templateId, targetUserId, variables);

      toast({
        title: 'Notification sent',
        description: 'Template notification sent successfully.',
      });
    } catch (error) {
      console.error('Failed to send template notification:', error);
      toast({
        title: 'Failed to send notification',
        description: 'There was an error sending the template notification.',
        variant: 'destructive',
      });
    }
  }, [supabase, toast]);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await notificationService.deleteNotification(notificationId);
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  }, []);

  const updatePreferences = useCallback(async (preferences: any) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      await notificationService.updatePreferences({
        ...preferences,
        userId: user.id
      });

      toast({
        title: 'Preferences updated',
        description: 'Your notification preferences have been updated.',
      });
    } catch (error) {
      console.error('Failed to update preferences:', error);
      toast({
        title: 'Failed to update preferences',
        description: 'There was an error updating your preferences.',
        variant: 'destructive',
      });
    }
  }, [supabase, toast]);

  return {
    sendNotification,
    sendTemplateNotification,
    markAsRead,
    deleteNotification,
    updatePreferences
  };
}
