# Supabase Configuration
# Replace these with your actual Supabase project credentials

# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=https://pvfymxuzhzlibbnaedgt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB2Znltenh1aHpsaWJibmFlZGd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzUyODgsImV4cCI6MjA0Nzg1MTI4OH0.pdqS0FgFkYiXXW3gfMlrPLYp-jY-RkBLGLLUQiKVqGI
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# API Keys for AI Integration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# SharePoint Integration (optional for now)
SHAREPOINT_CLIENT_ID=your_sharepoint_client_id
SHAREPOINT_CLIENT_SECRET=your_sharepoint_client_secret
SHAREPOINT_TENANT_ID=your_sharepoint_tenant_id

# Exchange Online Integration (optional for now)
EXCHANGE_CLIENT_ID=your_exchange_client_id
EXCHANGE_CLIENT_SECRET=your_exchange_client_secret
EXCHANGE_TENANT_ID=your_exchange_tenant_id

# Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=ITSync
NODE_ENV=development
