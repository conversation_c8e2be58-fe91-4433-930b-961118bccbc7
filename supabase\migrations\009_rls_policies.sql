-- Enable Row Level Security on all tables
ALTER TABLE divisions ENABLE ROW LEVEL SECURITY;
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE unions ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE request_forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE request_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_mail_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_mail_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE mailbox_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE mailbox_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE sharepoint_libraries ENABLE ROW LEVEL SECURITY;
ALTER TABLE sharepoint_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE hr_request_forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE hr_request_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy for Global Administrator and Web App System Administrator
CREATE POLICY admin_all_access ON staff
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM staff s
    JOIN roles r ON s.role_id = r.id
    WHERE s.auth_id = auth.uid()
    AND r.name IN ('Global Administrator', 'Web App System Administrator')
  )
);

-- Policy for Department Administrators
CREATE POLICY dept_admin_access ON staff
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM staff s
    JOIN roles r ON s.role_id = r.id
    WHERE s.auth_id = auth.uid()
    AND r.name = 'Department Administrator'
    AND s.division_id = staff.division_id
  )
);

-- Policy for regular users to view their own data
CREATE POLICY user_own_access ON staff
FOR SELECT TO authenticated
USING (auth.uid() = auth_id);
