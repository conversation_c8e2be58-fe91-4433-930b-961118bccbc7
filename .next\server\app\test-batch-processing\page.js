(()=>{var e={};e.id=8666,e.ids=[8666],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},62028:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=t(70260),n=t(28203),o=t(25155),a=t.n(o),i=t(67292),p={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);t.d(r,p);let d=["",{children:["test-batch-processing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,10952,23)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-batch-processing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-batch-processing\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test-batch-processing/page",pathname:"/test-batch-processing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10952:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expression expected\n     ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-batch-processing\\page.tsx\x1b[0m:112:1]\n \x1b[2m109\x1b[0m │       // Submit batch request      const result = await batchProcessingService.createBatchOperation(\n \x1b[2m110\x1b[0m │         staff.id,\n \x1b[2m111\x1b[0m │         mockRequest\n \x1b[2m112\x1b[0m │       );\n     \xb7 \x1b[35;1m      ─\x1b[0m\n \x1b[2m113\x1b[0m │ \n \x1b[2m114\x1b[0m │       setTestResults(prev => ({\n \x1b[2m115\x1b[0m │         ...prev,\n     ╰────\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(62028));module.exports=s})();