import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type') || 'status';

    if (type === 'status') {
      // Get SLA compliance status
      const { data, error } = await supabase
        .from('sla_tracking')
        .select(`
          *,
          workflow_instance:workflow_instances(
            *,
            request:request_forms(*)
          ),
          sla_definition:sla_definitions(*)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      // Calculate compliance statistics
      const total = data.length;
      const onTrack = data.filter(s => s.status === 'on_track').length;
      const atRisk = data.filter(s => s.status === 'at_risk').length;
      const breached = data.filter(s => s.status === 'breached').length;

      return NextResponse.json({
        summary: {
          total,
          onTrack,
          atRisk,
          breached,
          complianceRate: total > 0 ? (onTrack / total * 100).toFixed(2) : 0,
        },
        data,
      });
    } else if (type === 'at-risk') {
      // Get at-risk items
      const { data, error } = await supabase
        .from('sla_tracking')
        .select(`
          *,
          workflow_instance:workflow_instances(
            *,
            request:request_forms(*),
            tasks:workflow_tasks(*)
          ),
          sla_definition:sla_definitions(*)
        `)
        .eq('status', 'at_risk')
        .order('target_resolution_date', { ascending: true });

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json({ data });
    }

    return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
  } catch (error) {
    console.error('Error fetching SLA data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
