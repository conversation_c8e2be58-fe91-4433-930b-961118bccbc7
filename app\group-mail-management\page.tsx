"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mail, 
  Users, 
  Plus, 
  Minus, 
  Search,
  UserPlus,
  UserMinus,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { createClient } from '@/lib/supabase';
import { useAuth } from '@/lib/auth-context';
import { UserSearchInput } from '@/components/forms/user-search-input';
import { toast } from 'sonner';
import { useDepartmentFilter } from '@/lib/use-department-filter';

interface GroupMailAddress {
  id: string;
  email_address: string;
  description: string;
  division_id?: string;
  created_at: string;
  updated_at: string;
}

interface GroupMailMember {
  id: string;
  group_mail_id: string;
  staff_id: string;
  created_at: string;
  staff?: {
    id: string;
    name_jp: string;
    name_en: string;
    email: string;
    staff_id: string;
  };
}

export default function GroupMailManagementPage() {
  const { user, staff } = useAuth();
  const { filterByDepartment } = useDepartmentFilter();
  const [groupMails, setGroupMails] = useState<GroupMailAddress[]>([]);
  const [selectedGroupMail, setSelectedGroupMail] = useState<GroupMailAddress | null>(null);
  const [groupMembers, setGroupMembers] = useState<GroupMailMember[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]);
  const supabase = createClient();

  useEffect(() => {
    loadGroupMails();
  }, []);

  useEffect(() => {
    if (selectedGroupMail) {
      loadGroupMembers(selectedGroupMail.id);
    }
  }, [selectedGroupMail]);
  const loadGroupMails = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('group_mail_addresses')
        .select('*')
        .order('email_address');

      // Apply department filter if not admin
      if (staff && !['Global Administrator', 'Web App System Administrator'].includes(staff.role?.name || '')) {
        query = filterByDepartment(query, 'division_id');
      }

      const { data, error } = await query;

      if (error) {
        toast.error('グループメールの読み込みに失敗しました');
        return;
      }

      setGroupMails(data || []);
    } finally {
      setIsLoading(false);
    }
  };

  const loadGroupMembers = async (groupMailId: string) => {
    try {
      const { data, error } = await supabase
        .from('group_mail_members')
        .select(`
          *,
          staff:staff_id (
            id,
            name_jp,
            name_en,
            email,
            staff_id
          )
        `)
        .eq('group_mail_id', groupMailId)
        .order('created_at', { ascending: false });

      if (error) {
        toast.error('メンバーの読み込みに失敗しました');
        return;
      }

      setGroupMembers(data || []);
    } catch (error) {
      console.error('Error loading members:', error);
    }
  };

  const addUsersToGroup = async () => {
    if (!selectedGroupMail || selectedUsers.length === 0) return;
    setIsLoading(true);
    try {
      // Check if users are already members
      const existingMembers = groupMembers.map(m => m.staff_id);
      const newUsers = selectedUsers.filter(u => !existingMembers.includes(u.id));

      if (newUsers.length === 0) {
        toast.warning('選択されたユーザーは既にメンバーです');
        return;
      }

      // Add new members
      const membersToAdd = newUsers.map(user => ({
        group_mail_id: selectedGroupMail.id,
        staff_id: user.id
      }));

      const { error } = await supabase
        .from('group_mail_members')
        .insert(membersToAdd);

      if (error) {
        toast.error('メンバーの追加に失敗しました');
        return;
      }

      toast.success(`${newUsers.length}人のユーザーを追加しました`);
      setSelectedUsers([]);
      loadGroupMembers(selectedGroupMail.id);
    } finally {
      setIsLoading(false);
    }
  };

  const removeUserFromGroup = async (memberId: string) => {
    if (!confirm('このユーザーをグループから削除しますか？')) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('group_mail_members')
        .delete()
        .eq('id', memberId);

      if (error) {
        toast.error('メンバーの削除に失敗しました');
        return;
      }

      toast.success('メンバーを削除しました');
      loadGroupMembers(selectedGroupMail!.id);
    } finally {
      setIsLoading(false);
    }
  };
  const filteredGroupMails = groupMails.filter(gm =>
    gm.email_address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (gm.description && gm.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">グループメール管理</h1>
        <p className="text-muted-foreground mt-2">
          グループメールアドレスのメンバー管理
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Group Mail List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>グループメールアドレス</CardTitle>
            <CardDescription>
              全{groupMails.length}件のグループメール
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="グループメールを検索..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>

              <ScrollArea className="h-[600px]">
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex items-center justify-center p-8">
                      <Loader2 className="h-6 w-6 animate-spin" />
                    </div>
                  ) : filteredGroupMails.length === 0 ? (
                    <p className="text-center text-muted-foreground p-4">
                      グループメールが見つかりません
                    </p>
                  ) : (
                    filteredGroupMails.map((groupMail) => (
                      <div
                        key={groupMail.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedGroupMail?.id === groupMail.id
                            ? 'bg-primary/10 border-primary'
                            : 'hover:bg-accent'
                        }`}
                        onClick={() => setSelectedGroupMail(groupMail)}
                      >                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {groupMail.email_address}
                            </p>
                            {groupMail.description && (
                              <p className="text-xs text-muted-foreground truncate">
                                {groupMail.description}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>
          </CardContent>
        </Card>

        {/* Member Management */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>メンバー管理</CardTitle>
                <CardDescription>
                  {selectedGroupMail
                    ? `${selectedGroupMail.email_address} のメンバー`
                    : 'グループメールを選択してください'}
                </CardDescription>
              </div>
              {selectedGroupMail && (
                <Badge variant="outline">
                  {groupMembers.length} メンバー
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {!selectedGroupMail ? (
              <div className="text-center py-12 text-muted-foreground">
                <Mail className="h-12 w-12 mx-auto mb-4 opacity-30" />
                <p>左側からグループメールを選択してください</p>
              </div>
            ) : (
              <Tabs defaultValue="members" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="members">現在のメンバー</TabsTrigger>
                  <TabsTrigger value="add">メンバー追加</TabsTrigger>
                </TabsList>
                <TabsContent value="members" className="mt-6">
                  <ScrollArea className="h-[500px]">
                    <div className="space-y-2">
                      {groupMembers.length === 0 ? (
                        <p className="text-center text-muted-foreground p-8">
                          このグループにはメンバーがいません
                        </p>
                      ) : (
                        groupMembers.map((member) => (
                          <div
                            key={member.id}
                            className="flex items-center justify-between p-3 rounded-lg border"
                          >
                            <div className="flex items-center gap-3">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="font-medium">
                                  {member.staff?.name_jp || member.staff?.name_en}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {member.staff?.email} ({member.staff?.staff_id})
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeUserFromGroup(member.id)}
                              disabled={isLoading}
                            >
                              <UserMinus className="h-4 w-4" />
                            </Button>
                          </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="add" className="mt-6">
                  <div className="space-y-4">
                    <UserSearchInput
                      multiple={true}
                      onSelect={setSelectedUsers}
                      placeholder="追加するユーザーを検索..."
                    />

                    {selectedUsers.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium">
                          選択されたユーザー ({selectedUsers.length}人)
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {selectedUsers.map((user) => (
                            <Badge key={user.id} variant="secondary">
                              {user.name_jp || user.name_en}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <Button
                      onClick={addUsersToGroup}
                      disabled={selectedUsers.length === 0 || isLoading}
                      className="w-full"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          追加中...
                        </>
                      ) : (
                        <>
                          <UserPlus className="w-4 h-4 mr-2" />
                          メンバーを追加
                        </>
                      )}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}