"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Monitor, 
  Search, 
  Shield, 
  ShieldOff,
  Info,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { pcAdminService } from '@/lib/services/pc-admin-service';
import type { 
  PcSearchResult, 
  PcAdminRequestForm,
  SoftwareCatalog 
} from '@/lib/pc-admin-types';
import { useAuth } from '@/lib/auth-context';
import { toast } from 'sonner';

export function PcAdminRequestForm() {
  const { user, staff } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<PcSearchResult[]>([]);
  const [selectedPc, setSelectedPc] = useState<PcSearchResult | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [softwareCatalog, setSoftwareCatalog] = useState<SoftwareCatalog[]>([]);
  const [selectedSoftware, setSelectedSoftware] = useState<string[]>([]);
  
  const [formData, setFormData] = useState<PcAdminRequestForm>({
    pc_id_search: '',
    action_type: 'grant_admin',
    reason: '',
    software_to_install: [],
    priority: 'medium'
  });

  useEffect(() => {
    loadSoftwareCatalog();
  }, []);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {      if (searchTerm.length >= 2) {
        searchPc();
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  const loadSoftwareCatalog = async () => {
    const catalog = await pcAdminService.getSoftwareCatalog();
    setSoftwareCatalog(catalog);
  };

  const searchPc = async () => {
    setIsSearching(true);
    try {
      const results = await pcAdminService.searchPcById(searchTerm);
      setSearchResults(results);
    } finally {
      setIsSearching(false);
    }
  };

  const selectPc = (pc: PcSearchResult) => {
    setSelectedPc(pc);
    setFormData({
      ...formData,
      pc_id_search: pc.pc_id
    });
    setSearchTerm(pc.pc_id);
    setSearchResults([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!staff) {
      toast.error('ユーザー情報が見つかりません');
      return;
    }

    if (!selectedPc) {
      toast.error('PCを選択してください');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await pcAdminService.createPcAdminRequest(
        staff.id,
        {
          ...formData,
          software_to_install: selectedSoftware
        }
      );

      if (result.success) {
        toast.success('PC管理者権限リクエストを送信しました');
        // Reset form
        setFormData({
          pc_id_search: '',
          action_type: 'grant_admin',
          reason: '',
          software_to_install: [],
          priority: 'medium'
        });
        setSelectedPc(null);
        setSearchTerm('');
        setSelectedSoftware([]);
      } else {
        toast.error(result.error || 'リクエストの送信に失敗しました');
      }
    } catch (error) {
      toast.error('エラーが発生しました');
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleSoftware = (softwareId: string) => {
    setSelectedSoftware(prev => 
      prev.includes(softwareId)
        ? prev.filter(id => id !== softwareId)
        : [...prev, softwareId]
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Monitor className="w-5 h-5" />
          PC管理者権限リクエスト / PC Admin Request
        </CardTitle>
        <CardDescription>
          PC管理者権限の付与または削除を申請します
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* PC Search */}
          <div className="space-y-2">
            <Label htmlFor="pc-search">PC ID検索 / PC ID Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="pc-search"
                placeholder="PC IDを入力... (例: M241234)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            
            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-2 border rounded-md p-2 space-y-2 max-h-48 overflow-y-auto">
                {searchResults.map((pc) => (
                  <div
                    key={pc.id}
                    className="flex items-center justify-between p-2 hover:bg-accent rounded cursor-pointer"
                    onClick={() => selectPc(pc)}
                  >
                    <div>
                      <div className="font-medium">{pc.pc_id}</div>
                      <div className="text-sm text-muted-foreground">
                        {pc.model} - {pc.assigned_user_name || '未割当'}
                      </div>
                    </div>
                    <Badge variant={pc.admin_enabled ? "default" : "secondary"}>
                      {pc.admin_enabled ? "管理者権限あり" : "管理者権限なし"}
                    </Badge>
                  </div>
                ))}
              </div>
            )}

            {isSearching && (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="w-4 h-4 animate-spin" />
              </div>
            )}
          </div>

          {/* Selected PC Info */}
          {selectedPc && (
            <Alert>
              <Monitor className="h-4 w-4" />
              <AlertDescription>
                <strong>選択されたPC:</strong> {selectedPc.pc_id}
                <br />
                <strong>モデル:</strong> {selectedPc.model || 'N/A'}
                <br />
                <strong>使用者:</strong> {selectedPc.assigned_user_name || '未割当'}
                <br />
                <strong>現在の状態:</strong> {selectedPc.admin_enabled ? '管理者権限あり' : '管理者権限なし'}
              </AlertDescription>
            </Alert>
          )}
          {/* Action Type */}
          <div className="space-y-2">
            <Label>アクションタイプ / Action Type</Label>
            <RadioGroup
              value={formData.action_type}
              onValueChange={(value) => setFormData({
                ...formData,
                action_type: value as 'grant_admin' | 'revoke_admin'
              })}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="grant_admin" id="grant" />
                <Label htmlFor="grant" className="flex items-center gap-2 cursor-pointer">
                  <Shield className="w-4 h-4 text-green-600" />
                  管理者権限を付与 / Grant Admin Rights
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="revoke_admin" id="revoke" />
                <Label htmlFor="revoke" className="flex items-center gap-2 cursor-pointer">
                  <ShieldOff className="w-4 h-4 text-red-600" />
                  管理者権限を削除 / Revoke Admin Rights
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">優先度 / Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData({
                ...formData,
                priority: value as 'low' | 'medium' | 'high' | 'urgent'
              })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">低 / Low</SelectItem>
                <SelectItem value="medium">中 / Medium</SelectItem>
                <SelectItem value="high">高 / High</SelectItem>
                <SelectItem value="urgent">緊急 / Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">理由 / Reason</Label>
            <Textarea
              id="reason"
              placeholder="管理者権限が必要な理由を入力してください..."
              value={formData.reason}
              onChange={(e) => setFormData({
                ...formData,
                reason: e.target.value
              })}
              rows={4}
              required
            />
          </div>

          {/* Software to Install (only show for grant_admin) */}
          {formData.action_type === 'grant_admin' && (
            <div className="space-y-2">
              <Label>インストール予定のソフトウェア / Software to Install</Label>
              <div className="border rounded-md p-4 space-y-2 max-h-64 overflow-y-auto">
                {softwareCatalog
                  .filter(software => software.requires_admin)
                  .map((software) => (
                    <div key={software.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={software.id}
                        checked={selectedSoftware.includes(software.id)}
                        onCheckedChange={() => toggleSoftware(software.id)}
                      />
                      <Label 
                        htmlFor={software.id} 
                        className="flex-1 cursor-pointer"
                      >
                        <div>
                          <span className="font-medium">{software.name}</span>
                          {software.version && (
                            <span className="text-sm text-muted-foreground ml-2">
                              v{software.version}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {software.vendor} - {software.category}
                        </div>
                      </Label>
                    </div>
                  ))}
              </div>
              <p className="text-sm text-muted-foreground">
                管理者権限が必要なソフトウェアのみ表示されています
              </p>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={!selectedPc || !formData.reason || isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                送信中...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                リクエストを送信 / Submit Request
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}