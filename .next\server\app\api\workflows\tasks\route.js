"use strict";(()=>{var e={};e.id=9827,e.ids=[9827],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},9587:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>c,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>u});var o=t(42706),a=t(28203),n=t(45994),i=t(39187);async function u(e){try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("*, role:roles(*)").eq("auth_id",t.id).single();if(!o)return i.NextResponse.json({error:"User not found"},{status:404});let a=e.nextUrl.searchParams.get("status")||"pending",n=r.from("workflow_tasks").select(`
        *,
        workflow_instance:workflow_instances(
          *,
          workflow_definition:workflow_definitions(*),
          request:request_forms(*)
        )
      `).eq("status",a).order("due_date",{ascending:!0});n=n.or(`assigned_to.eq.${o.id},assigned_role.eq.${o.role?.name}`);let{data:u,error:d}=await n;if(d)return i.NextResponse.json({error:d.message},{status:500});return i.NextResponse.json({data:u})}catch(e){return console.error("Error fetching workflow tasks:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/tasks/route",pathname:"/api/workflows/tasks",filename:"route",bundlePath:"app/api/workflows/tasks/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\tasks\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:c}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(9587));module.exports=s})();