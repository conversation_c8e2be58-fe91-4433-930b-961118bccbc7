(()=>{var e={};e.id=9827,e.ids=[9827],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77068:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>w,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(61487);async function p(e){try{let r=await (0,u.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("*, role:roles(*)").eq("auth_id",t.id).single();if(!o)return i.NextResponse.json({error:"User not found"},{status:404});let a=e.nextUrl.searchParams.get("status")||"pending",n=r.from("workflow_tasks").select(`
        *,
        workflow_instance:workflow_instances(
          *,
          workflow_definition:workflow_definitions(*),
          request:request_forms(*)
        )
      `).eq("status",a).order("due_date",{ascending:!0});n=n.or(`assigned_to.eq.${o.id},assigned_role.eq.${o.role?.name}`);let{data:p,error:l}=await n;if(l)return i.NextResponse.json({error:l.message},{status:500});return i.NextResponse.json({data:p})}catch(e){return console.error("Error fetching workflow tasks:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/tasks/route",pathname:"/api/workflows/tasks",filename:"route",bundlePath:"app/api/workflows/tasks/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\tasks\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:w}=l;function f(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},96487:()=>{},78335:()=>{},61487:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(49064),o=t(44512);let a=()=>{let e=(0,o.UL)();return(0,s.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064],()=>t(77068));module.exports=s})();