// Supabase connection pooling configuration
export const supabasePoolConfig = {
  // Connection pool settings
  connectionPool: {
    // Maximum number of connections in the pool
    max: 20,
    // Minimum number of connections in the pool
    min: 5,
    // Number of milliseconds to wait before timing out when connecting
    acquireTimeoutMillis: 30000,
    // Number of milliseconds a connection can be idle before being removed
    idleTimeoutMillis: 30000,
    // Number of milliseconds to wait before destroying idle connections
    reapIntervalMillis: 1000,
    // Enable connection pooling
    createRetryIntervalMillis: 200,
  },

  // Query optimization settings
  queryOptimization: {
    // Enable prepared statements
    preparedStatements: true,
    // Statement cache size
    statementCacheSize: 100,
    // Query timeout in milliseconds
    queryTimeout: 30000,
    // Enable query result caching
    enableQueryCache: true,
    // Query cache TTL in seconds
    queryCacheTTL: 300,
  },

  // Performance monitoring
  monitoring: {
    // Enable query logging
    logQueries: process.env.NODE_ENV === 'development',
    // Log slow queries (milliseconds)
    slowQueryThreshold: 1000,
    // Enable connection pool monitoring
    monitorConnectionPool: true,
  },

  // Retry configuration
  retry: {
    // Maximum number of retries
    maxRetries: 3,
    // Initial retry delay in milliseconds
    initialDelay: 100,
    // Maximum retry delay in milliseconds
    maxDelay: 5000,
    // Retry multiplier
    multiplier: 2,
  },
}

// Database optimization queries to run on startup
export const optimizationQueries = [
  // Update table statistics
  'ANALYZE request_forms',
  'ANALYZE workflow_instances',
  'ANALYZE staff',
  'ANALYZE audit_logs',
  
  // Set work_mem for complex queries
  "SET work_mem = '256MB'",
  
  // Set statement timeout
  "SET statement_timeout = '30s'",
  
  // Enable parallel queries
  "SET max_parallel_workers_per_gather = 4",
  
  // Optimize for web workload
  "SET random_page_cost = 1.1",
]

// Export function to apply optimizations
export async function applyDatabaseOptimizations(supabase: any) {
  try {
    for (const query of optimizationQueries) {
      await supabase.rpc('exec_sql', { query })
    }
    console.log('Database optimizations applied successfully')
  } catch (error) {
    console.error('Failed to apply database optimizations:', error)
  }
}
