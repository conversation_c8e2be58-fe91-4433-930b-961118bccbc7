'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { PredictiveField } from '@/components/predictive/predictive-field'
import { usePredictiveForm } from '@/lib/hooks/use-predictive-form'
import { Brain, User, Users, Sparkles, TrendingUp, Settings } from 'lucide-react'
import type { FormField } from '@/lib/form-types'

export default function PredictiveFormTestPage() {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const { preferences, updatePreferences } = usePredictiveForm({
    formType: 'group-mail-request'
  })

  const testFields: FormField[] = [
    {
      name: 'requester_name',
      type: 'text',
      label: '申請者名',
      labelJp: '申請者名',
      required: true,
      validation: { required: true }
    },
    {
      name: 'group_mail_address',
      type: 'select',
      label: 'グループメールアドレス',
      labelJp: 'グループメールアドレス',
      required: true,
      options: [
        { value: '<EMAIL>', label: '人事グループ' },
        { value: '<EMAIL>', label: 'ITシステムグループ' },
        { value: '<EMAIL>', label: '総合管理者' }
      ],
      validation: { required: true }
    },
    {
      name: 'action_type',
      type: 'select',
      label: 'アクション',
      labelJp: 'アクション',
      required: true,
      options: [
        { value: 'add', label: '追加' },
        { value: 'remove', label: '削除' }
      ],
      validation: { required: true }
    }    {
      name: 'reason',
      type: 'textarea',
      label: '理由',
      labelJp: '理由',
      required: true,
      validation: { required: true, minLength: 10 }
    }
  ]

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }))
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">予測フォーム補完テスト</h1>
        <p className="text-muted-foreground">
          ユーザーパターンに基づいた予測フォーム補完機能をテストします。
        </p>
      </div>

      {/* Features Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              個人パターン学習
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              ユーザーの過去の入力パターンを学習し、よく使用する値を予測します。
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              部門パターン分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              同じ部門の他のユーザーの入力傾向を分析し、一般的な値を提案します。
            </p>          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI予測
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              AIがコンテキストを理解し、最適な値を予測して提案します。
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Preferences Settings */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            予測設定
          </CardTitle>
          <CardDescription>
            予測機能の有効/無効や信頼度しきい値を設定します
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="enable-predictions">予測機能を有効化</Label>
            <Switch
              id="enable-predictions"
              checked={preferences?.enablePredictions ?? true}
              onCheckedChange={(checked) => 
                updatePreferences({ enablePredictions: checked })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="personal-patterns">個人パターンを使用</Label>
            <Switch
              id="personal-patterns"
              checked={preferences?.enablePersonalPatterns ?? true}
              onCheckedChange={(checked) => 
                updatePreferences({ enablePersonalPatterns: checked })
              }
              disabled={!preferences?.enablePredictions}
            />          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="department-patterns">部門パターンを使用</Label>
            <Switch
              id="department-patterns"
              checked={preferences?.enableDepartmentPatterns ?? true}
              onCheckedChange={(checked) => 
                updatePreferences({ enableDepartmentPatterns: checked })
              }
              disabled={!preferences?.enablePredictions}
            />
          </div>

          <div className="space-y-2">
            <Label>信頼度しきい値: {Math.round((preferences?.minConfidenceThreshold ?? 0.7) * 100)}%</Label>
            <Slider
              value={[(preferences?.minConfidenceThreshold ?? 0.7) * 100]}
              onValueChange={([value]) => 
                updatePreferences({ minConfidenceThreshold: value / 100 })
              }
              min={0}
              max={100}
              step={5}
              disabled={!preferences?.enablePredictions}
            />
          </div>
        </CardContent>
      </Card>

      {/* Test Form */}
      <Card>
        <CardHeader>
          <CardTitle>テストフォーム</CardTitle>
          <CardDescription>
            フィールドをクリックすると予測候補が表示されます
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            {testFields.map((field) => (
              <PredictiveField
                key={field.name}
                field={field}
                value={formData[field.name] || ''}
                onChange={(value) => handleFieldChange(field.name, value)}
                formData={formData}
                formType="group-mail-request"
              />
            ))}
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setFormData({})}
              >
                リセット
              </Button>
              <Button type="submit">
                送信
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Current Form Data */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>現在のフォームデータ</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-sm bg-muted p-4 rounded-md overflow-auto">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}