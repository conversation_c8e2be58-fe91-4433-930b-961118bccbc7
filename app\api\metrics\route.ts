/**
 * Metrics API Endpoint
 * Provides system metrics for monitoring and alerting
 */

import { NextRequest, NextResponse } from 'next/server'
import { metrics, recordSystemMetrics } from '@/lib/monitoring/metrics'
import { createClient } from '@/lib/supabase/server'

/**
 * Get system metrics
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication for metrics access
    const supabase = createClient()
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (!profile || !['admin', 'system_admin'].includes(profile.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Record current system metrics
    recordSystemMetrics()

    // Get query parameters
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'json'
    const window = parseInt(url.searchParams.get('window') || '3600000') // 1 hour default

    // Get system metrics
    const systemMetrics = metrics.getSystemMetrics()

    // Get detailed metrics if requested
    const detailed = url.searchParams.get('detailed') === 'true'
    let detailedMetrics = {}

    if (detailed) {
      const metricNames = metrics.getMetricNames()
      detailedMetrics = metricNames.reduce((acc, name) => {
        acc[name] = metrics.getAggregatedMetrics(name, window)
        return acc
      }, {} as Record<string, any>)
    }

    const response = {
      timestamp: new Date().toISOString(),
      window_ms: window,
      system: systemMetrics,
      ...(detailed && { detailed: detailedMetrics })
    }

    // Return in requested format
    if (format === 'prometheus') {
      return new NextResponse(formatPrometheusMetrics(systemMetrics), {
        headers: {
          'Content-Type': 'text/plain; version=0.0.4',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      })
    }

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })

  } catch (error) {
    console.error('Metrics API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Format metrics for Prometheus
 */
function formatPrometheusMetrics(systemMetrics: any): string {
  const timestamp = Date.now()
  
  return `
# HELP itsync_http_requests_total Total number of HTTP requests
# TYPE itsync_http_requests_total counter
itsync_http_requests_total ${systemMetrics.requests.total} ${timestamp}

# HELP itsync_http_requests_success_total Total number of successful HTTP requests
# TYPE itsync_http_requests_success_total counter
itsync_http_requests_success_total ${systemMetrics.requests.success} ${timestamp}

# HELP itsync_http_requests_error_total Total number of failed HTTP requests
# TYPE itsync_http_requests_error_total counter
itsync_http_requests_error_total ${systemMetrics.requests.errors} ${timestamp}

# HELP itsync_http_response_time_avg Average HTTP response time in milliseconds
# TYPE itsync_http_response_time_avg gauge
itsync_http_response_time_avg ${systemMetrics.requests.response_time_avg || 0} ${timestamp}

# HELP itsync_database_query_time_avg Average database query time in milliseconds
# TYPE itsync_database_query_time_avg gauge
itsync_database_query_time_avg ${systemMetrics.database.query_time_avg || 0} ${timestamp}

# HELP itsync_database_slow_queries_total Total number of slow database queries
# TYPE itsync_database_slow_queries_total counter
itsync_database_slow_queries_total ${systemMetrics.database.slow_queries} ${timestamp}

# HELP itsync_ai_requests_total Total number of AI API requests
# TYPE itsync_ai_requests_total counter
itsync_ai_requests_total ${systemMetrics.ai.requests_total} ${timestamp}

# HELP itsync_ai_cost_total Total estimated AI API cost in USD
# TYPE itsync_ai_cost_total counter
itsync_ai_cost_total ${systemMetrics.ai.cost_estimate || 0} ${timestamp}

# HELP itsync_ai_errors_total Total number of AI API errors
# TYPE itsync_ai_errors_total counter
itsync_ai_errors_total ${systemMetrics.ai.errors} ${timestamp}

# HELP itsync_security_failed_logins_total Total number of failed login attempts
# TYPE itsync_security_failed_logins_total counter
itsync_security_failed_logins_total ${systemMetrics.security.failed_logins} ${timestamp}

# HELP itsync_security_rate_limit_hits_total Total number of rate limit hits
# TYPE itsync_security_rate_limit_hits_total counter
itsync_security_rate_limit_hits_total ${systemMetrics.security.rate_limit_hits} ${timestamp}

# HELP itsync_security_mfa_failures_total Total number of MFA failures
# TYPE itsync_security_mfa_failures_total counter
itsync_security_mfa_failures_total ${systemMetrics.security.mfa_failures} ${timestamp}

# HELP itsync_system_memory_usage_mb Current memory usage in MB
# TYPE itsync_system_memory_usage_mb gauge
itsync_system_memory_usage_mb ${systemMetrics.system.memory_usage || 0} ${timestamp}

# HELP itsync_system_uptime_ms System uptime in milliseconds
# TYPE itsync_system_uptime_ms gauge
itsync_system_uptime_ms ${systemMetrics.system.uptime} ${timestamp}
`.trim()
}
