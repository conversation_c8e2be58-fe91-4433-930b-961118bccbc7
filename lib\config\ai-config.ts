// lib/config/ai-config.ts
// Configuration management for AI services

export interface AIConfig {
  providers: {
    openai?: {
      apiKey: string
      baseUrl?: string
      model?: string
    }
    anthropic?: {
      apiKey: string
      baseUrl?: string
      model?: string
    }
  }
  defaultProvider: 'openai' | 'anthropic'
  parameters: {
    temperature: number
    maxTokens: number
  }
}

export function getAIConfig(): AIConfig {
  return {
    providers: {
      openai: process.env.OPENAI_API_KEY ? {
        apiKey: process.env.OPENAI_API_KEY,
        baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
        model: process.env.OPENAI_MODEL || 'gpt-4-turbo-preview'
      } : undefined,
      anthropic: process.env.ANTHROPIC_API_KEY ? {
        apiKey: process.env.ANTHROPIC_API_KEY,
        baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com/v1',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-opus-20240229'
      } : undefined
    },
    defaultProvider: (process.env.DEFAULT_AI_PROVIDER as 'openai' | 'anthropic') || 'openai',
    parameters: {
      temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.AI_MAX_TOKENS || '1000', 10)
    }
  }
}

export function validateAIConfig(config: AIConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check if at least one provider is configured
  if (!config.providers.openai && !config.providers.anthropic) {
    errors.push('No AI providers configured. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY.')
  }
  // Check if default provider is configured
  if (config.defaultProvider === 'openai' && !config.providers.openai) {
    errors.push('Default provider is set to OpenAI but OpenAI is not configured.')
  }
  
  if (config.defaultProvider === 'anthropic' && !config.providers.anthropic) {
    errors.push('Default provider is set to Anthropic but Anthropic is not configured.')
  }

  // Validate temperature
  if (config.parameters.temperature < 0 || config.parameters.temperature > 2) {
    errors.push('Temperature must be between 0 and 2.')
  }

  // Validate max tokens
  if (config.parameters.maxTokens < 1 || config.parameters.maxTokens > 32000) {
    errors.push('Max tokens must be between 1 and 32000.')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Available models for each provider
export const AI_MODELS = {
  openai: [
    { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo', maxTokens: 128000 },
    { id: 'gpt-4', name: 'GPT-4', maxTokens: 8192 },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', maxTokens: 16385 }
  ],
  anthropic: [
    { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', maxTokens: 200000 },
    { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', maxTokens: 200000 },
    { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', maxTokens: 200000 }
  ]
}