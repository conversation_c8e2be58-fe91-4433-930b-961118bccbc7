/**
 * Environment Variable Validation and Security Utilities
 * Ensures all required environment variables are present and secure
 */

interface EnvValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  securityIssues: string[]
}

interface RequiredEnvVar {
  key: string
  required: boolean
  pattern?: RegExp
  description: string
  securityCheck?: (value: string) => string | null
}

const REQUIRED_ENV_VARS: RequiredEnvVar[] = [
  {
    key: 'NEXT_PUBLIC_SUPABASE_URL',
    required: true,
    pattern: /^https:\/\/[a-z0-9-]+\.supabase\.co$/,
    description: 'Supabase project URL'
  },
  {
    key: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    required: true,
    pattern: /^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/,
    description: 'Supabase anonymous key (JWT format)'
  },
  {
    key: 'SUPABASE_SERVICE_ROLE_KEY',
    required: true,
    pattern: /^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/,
    description: 'Supabase service role key (JWT format)',
    securityCheck: (value) => {
      if (value.includes('anon')) {
        return 'Service role key appears to be an anon key - this is a security risk'
      }
      return null
    }
  },
  {
    key: 'OPENAI_API_KEY',
    required: false,
    pattern: /^sk-proj-[A-Za-z0-9-_]{48,}$/,
    description: 'OpenAI API key',
    securityCheck: (value) => {
      if (value === 'your_openai_api_key_here' || value.length < 50) {
        return 'OpenAI API key appears to be a placeholder or too short'
      }
      return null
    }
  },
  {
    key: 'ANTHROPIC_API_KEY',
    required: false,
    pattern: /^sk-ant-api03-[A-Za-z0-9-_]{48,}$/,
    description: 'Anthropic API key',
    securityCheck: (value) => {
      if (value === 'your_anthropic_api_key_here' || value.length < 50) {
        return 'Anthropic API key appears to be a placeholder or too short'
      }
      return null
    }
  },
  {
    key: 'ENCRYPTION_KEY',
    required: true,
    pattern: /^[A-Za-z0-9]{32,}$/,
    description: 'Encryption key for sensitive data (minimum 32 characters)',
    securityCheck: (value) => {
      if (value.length < 32) {
        return 'Encryption key must be at least 32 characters long'
      }
      if (value === 'your_32_character_encryption_key_here') {
        return 'Encryption key is still using placeholder value'
      }
      return null
    }
  },
  {
    key: 'JWT_SECRET',
    required: true,
    pattern: /^[A-Za-z0-9]{32,}$/,
    description: 'JWT secret for session management (minimum 32 characters)',
    securityCheck: (value) => {
      if (value.length < 32) {
        return 'JWT secret must be at least 32 characters long'
      }
      if (value === 'your_jwt_secret_here') {
        return 'JWT secret is still using placeholder value'
      }
      return null
    }
  }
]

/**
 * Validates all environment variables
 */
export function validateEnvironment(): EnvValidationResult {
  const result: EnvValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    securityIssues: []
  }

  // Check if we're in production
  const isProduction = process.env.NODE_ENV === 'production'

  for (const envVar of REQUIRED_ENV_VARS) {
    const value = process.env[envVar.key]

    // Check if required variable is missing
    if (envVar.required && !value) {
      result.errors.push(`Missing required environment variable: ${envVar.key} - ${envVar.description}`)
      result.isValid = false
      continue
    }

    // Skip validation if optional variable is not set
    if (!value) continue

    // Check pattern if provided
    if (envVar.pattern && !envVar.pattern.test(value)) {
      result.errors.push(`Invalid format for ${envVar.key}: ${envVar.description}`)
      result.isValid = false
    }

    // Run security checks
    if (envVar.securityCheck) {
      const securityIssue = envVar.securityCheck(value)
      if (securityIssue) {
        result.securityIssues.push(`${envVar.key}: ${securityIssue}`)
        if (isProduction) {
          result.isValid = false
        }
      }
    }
  }

  // Additional security checks
  if (isProduction) {
    // Check for development-only settings in production
    if (process.env.DEBUG === 'true') {
      result.securityIssues.push('DEBUG mode is enabled in production')
    }

    if (process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
      result.securityIssues.push('TLS certificate validation is disabled in production')
      result.isValid = false
    }

    // Ensure HTTPS in production
    const appUrl = process.env.NEXT_PUBLIC_APP_URL
    if (appUrl && !appUrl.startsWith('https://')) {
      result.securityIssues.push('App URL must use HTTPS in production')
      result.isValid = false
    }
  }

  // Check for common placeholder values
  const placeholderPatterns = [
    'your_',
    'placeholder',
    'example',
    'test_key',
    'demo_'
  ]

  Object.entries(process.env).forEach(([key, value]) => {
    if (key.includes('KEY') || key.includes('SECRET') || key.includes('PASSWORD')) {
      placeholderPatterns.forEach(pattern => {
        if (value?.toLowerCase().includes(pattern)) {
          result.warnings.push(`${key} appears to contain placeholder value`)
        }
      })
    }
  })

  return result
}

/**
 * Logs validation results
 */
export function logValidationResults(result: EnvValidationResult): void {
  if (result.errors.length > 0) {
    console.error('❌ Environment Validation Errors:')
    result.errors.forEach(error => console.error(`  - ${error}`))
  }

  if (result.securityIssues.length > 0) {
    console.warn('🔒 Security Issues:')
    result.securityIssues.forEach(issue => console.warn(`  - ${issue}`))
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️  Warnings:')
    result.warnings.forEach(warning => console.warn(`  - ${warning}`))
  }

  if (result.isValid && result.errors.length === 0 && result.securityIssues.length === 0) {
    console.log('✅ Environment validation passed')
  }
}

/**
 * Generates secure random keys
 */
export function generateSecureKey(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Validates environment on startup
 */
export function validateOnStartup(): void {
  const result = validateEnvironment()
  logValidationResults(result)

  if (!result.isValid) {
    console.error('❌ Environment validation failed. Please check your .env.local file.')
    if (process.env.NODE_ENV === 'production') {
      process.exit(1)
    }
  }
}
