'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { useEmbeddings } from '@/lib/hooks/use-embeddings'
import { Loader2, Search, Database, RefreshCw, Link } from 'lucide-react'

export default function TestEmbeddingsPage() {
  const [query, setQuery] = useState('')
  const [language, setLanguage] = useState<'en' | 'jp'>('en')
  const [articleId, setArticleId] = useState('')
  const { toast } = useToast()
  
  const {
    loading,
    results,
    generateEmbedding,
    semanticSearch,
    updateStaleEmbeddings,
    findRelatedArticles
  } = useEmbeddings({
    matchThreshold: 0.7,
    matchCount: 10
  })

  const handleGenerateEmbedding = async () => {
    if (!articleId) {
      toast({
        title: 'エラー',
        description: 'Please enter an article ID',
        variant: 'destructive'
      })
      return
    }

    try {
      await generateEmbedding(articleId, 'both')
    } catch (error) {
      // Error handled in hook
    }
  }

  const handleSemanticSearch = async () => {
    if (!query) {
      toast({
        title: 'エラー',
        description: 'Please enter a search query',
        variant: 'destructive'
      })
      return
    }

    try {
      await semanticSearch(query, language)
    } catch (error) {
      // Error handled in hook
    }
  }

  const handleUpdateStale = async () => {
    try {
      await updateStaleEmbeddings()
    } catch (error) {
      // Error handled in hook
    }
  }

  const handleFindRelated = async () => {
    if (!articleId) {
      toast({
        title: 'エラー',
        description: 'Please enter an article ID',
        variant: 'destructive'
      })
      return
    }

    try {
      const related = await findRelatedArticles(articleId, language)
      toast({
        title: '成功',
        description: `Found ${related.length} related articles`,
      })
    } catch (error) {
      // Error handled in hook
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Vector Embeddings Test Page</h1>
      
      <Tabs defaultValue="generate" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate">Generate Embeddings</TabsTrigger>
          <TabsTrigger value="search">Semantic Search</TabsTrigger>
          <TabsTrigger value="update">Update Stale</TabsTrigger>
          <TabsTrigger value="related">Find Related</TabsTrigger>
        </TabsList>

        <TabsContent value="generate">
          <Card>
            <CardHeader>
              <CardTitle>Generate Article Embeddings</CardTitle>
              <CardDescription>
                Generate vector embeddings for an article in both English and Japanese
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="article-id">Article ID</Label>
                <Input
                  id="article-id"
                  placeholder="Enter article UUID"
                  value={articleId}
                  onChange={(e) => setArticleId(e.target.value)}
                />
              </div>
              <Button
                onClick={handleGenerateEmbedding}
                disabled={loading || !articleId}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-4 w-4" />
                    Generate Embeddings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="search">
          <Card>
            <CardHeader>
              <CardTitle>Semantic Search</CardTitle>
              <CardDescription>
                Search for articles using natural language queries
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="search-query">Search Query</Label>
                <Textarea
                  id="search-query"
                  placeholder="Enter your search query..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="search-language">Language</Label>
                <Select value={language} onValueChange={(v) => setLanguage(v as 'en' | 'jp')}>
                  <SelectTrigger id="search-language">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="jp">日本語</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={handleSemanticSearch}
                disabled={loading || !query}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    Search
                  </>
                )}
              </Button>

              {results.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">Search Results</h3>
                  <ScrollArea className="h-[400px] w-full rounded-md border p-4">
                    <div className="space-y-4">
                      {results.map((result, index) => (
                        <div key={result.article_id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{result.title}</h4>
                            <Badge variant="secondary">
                              {(result.similarity * 100).toFixed(1)}% match
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {result.summary}
                          </p>
                          {index < results.length - 1 && <Separator />}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="update">
          <Card>
            <CardHeader>
              <CardTitle>Update Stale Embeddings</CardTitle>
              <CardDescription>
                Regenerate embeddings for articles that have been modified
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleUpdateStale}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Update Stale Embeddings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="related">
          <Card>
            <CardHeader>
              <CardTitle>Find Related Articles</CardTitle>
              <CardDescription>
                Find articles similar to a given article
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="related-article-id">Article ID</Label>
                <Input
                  id="related-article-id"
                  placeholder="Enter article UUID"
                  value={articleId}
                  onChange={(e) => setArticleId(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="related-language">Language</Label>
                <Select value={language} onValueChange={(v) => setLanguage(v as 'en' | 'jp')}>
                  <SelectTrigger id="related-language">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="jp">日本語</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={handleFindRelated}
                disabled={loading || !articleId}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Finding...
                  </>
                ) : (
                  <>
                    <Link className="mr-2 h-4 w-4" />
                    Find Related Articles
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
