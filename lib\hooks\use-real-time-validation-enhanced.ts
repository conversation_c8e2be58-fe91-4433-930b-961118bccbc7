'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { FormField, FormData } from '@/lib/form-types'
import { enhancedAIFormAssistant } from '@/lib/ai-form-assistant-enhanced'

export interface ValidationResult {
  isValid: boolean
  message?: string
  type: 'success' | 'warning' | 'error' | 'info'
  aiInsight?: string
  suggestion?: any
}

export interface ValidationState {
  [fieldId: string]: ValidationResult
}

interface UseRealTimeValidationProps {
  fields: FormField[]
  formData: FormData
  context?: {
    userId?: string
    departmentId?: string
    userRole?: string
  }
  debounceMs?: number
}

export function useRealTimeValidation({
  fields,
  formData,
  context,
  debounceMs = 500
}: UseRealTimeValidationProps) {
  const [validationState, setValidationState] = useState<ValidationState>({})
  const [isValidating, setIsValidating] = useState<Record<string, boolean>>({})
  const timeoutRefs = useRef<Record<string, NodeJS.Timeout>>({})

  // Clear all timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(timeoutRefs.current).forEach(timeout => clearTimeout(timeout))
    }
  }, [])

  const validateField = useCallback(async (
    field: FormField, 
    value: any
  ): Promise<ValidationResult> => {
    try {
      // Use enhanced AI validation
      const result = await enhancedAIFormAssistant.validateFieldWithAI(field, value, context)
      return result
    } catch (error) {
      console.error('Validation error:', error)
      return {
        isValid: false,
        type: 'error',
        message: 'バリデーションエラーが発生しました / Validation error occurred'
      }
    }
  }, [context])

  const debouncedValidate = useCallback((fieldId: string, value: any) => {
    // Clear existing timeout
    if (timeoutRefs.current[fieldId]) {
      clearTimeout(timeoutRefs.current[fieldId])
    }

    // Set validation loading state
    setIsValidating(prev => ({ ...prev, [fieldId]: true }))

    // Create new timeout
    timeoutRefs.current[fieldId] = setTimeout(async () => {
      const field = fields.find(f => f.id === fieldId)
      if (!field) return

      try {
        const result = await validateField(field, value)
        setValidationState(prev => ({
          ...prev,
          [fieldId]: result
        }))
      } catch (error) {
        console.error('Validation error:', error)
        setValidationState(prev => ({
          ...prev,
          [fieldId]: {
            isValid: false,
            type: 'error',
            message: 'バリデーションエラーが発生しました / Validation error occurred'
          }
        }))
      } finally {
        setIsValidating(prev => ({ ...prev, [fieldId]: false }))
      }
    }, debounceMs)
  }, [fields, validateField, debounceMs])

  // Validate field when form data changes
  useEffect(() => {
    Object.keys(formData).forEach(fieldId => {
      const value = formData[fieldId]
      debouncedValidate(fieldId, value)
    })
  }, [formData, debouncedValidate])

  const getFieldValidation = (fieldId: string): ValidationResult | undefined => {
    return validationState[fieldId]
  }

  const isFieldValidating = (fieldId: string): boolean => {
    return isValidating[fieldId] || false
  }

  const validateAllFields = async (): Promise<boolean> => {
    const results = await Promise.all(
      fields.map(async field => {
        const value = formData[field.id]
        return validateField(field, value)
      })
    )

    const newValidationState: ValidationState = {}
    fields.forEach((field, index) => {
      newValidationState[field.id] = results[index]
    })

    setValidationState(newValidationState)
    return results.every(result => result.isValid)
  }

  const getSuggestion = async (fieldId: string): Promise<any> => {
    const field = fields.find(f => f.id === fieldId)
    if (!field) return null

    const value = formData[fieldId]
    const suggestion = await enhancedAIFormAssistant.generateFieldSuggestions(field, value, context || {})
    return suggestion
  }

  return {
    validationState,
    isValidating,
    getFieldValidation,
    isFieldValidating,
    validateAllFields,
    debouncedValidate,
    getSuggestion
  }
}
