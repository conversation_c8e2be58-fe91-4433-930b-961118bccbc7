// Redis Caching Service Implementation
import Redis from 'ioredis';
import { compress, decompress } from 'lz-string';

export class RedisCacheService {
  private static instance: RedisCacheService;
  private redis: Redis | null = null;
  private isConnected = false;

  private constructor() {
    this.initializeRedis();
  }

  static getInstance(): RedisCacheService {
    if (!RedisCacheService.instance) {
      RedisCacheService.instance = new RedisCacheService();
    }
    return RedisCacheService.instance;
  }

  private async initializeRedis() {
    try {
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
        retryStrategy: (times) => Math.min(times * 50, 2000),
        enableOfflineQueue: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      });

      await this.redis.connect();
      this.isConnected = true;
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Redis connection failed:', error);
      this.redis = null;
    }
  }

  // Get compressed data
  async getCompressed(key: string): Promise<any | null> {
    if (!this.redis || !this.isConnected) return null;

    try {
      const compressed = await this.redis.get(key);
      if (!compressed) return null;
      
      const decompressed = decompress(compressed);
      return JSON.parse(decompressed);
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  // Set compressed data
  async setCompressed(key: string, value: any, ttl = 3600): Promise<boolean> {
    if (!this.redis || !this.isConnected) return false;

    try {
      const compressed = compress(JSON.stringify(value));
      await this.redis.setex(key, ttl, compressed);
      return true;
    } catch (error) {
      console.error('Redis set error:', error);
      return false;
    }
  }

  // Cache patterns for ITSync
  async cacheUserData(userId: string, data: any): Promise<boolean> {
    return this.setCompressed(`user:${userId}`, data, 1800); // 30 min
  }

  async getCachedUserData(userId: string): Promise<any | null> {
    return this.getCompressed(`user:${userId}`);
  }

  async cacheRequestForm(formId: string, data: any): Promise<boolean> {
    return this.setCompressed(`form:${formId}`, data, 3600); // 1 hour
  }

  async getCachedRequestForm(formId: string): Promise<any | null> {
    return this.getCompressed(`form:${formId}`);
  }

  async cacheDepartmentData(deptId: string, data: any): Promise<boolean> {
    return this.setCompressed(`dept:${deptId}`, data, 7200); // 2 hours
  }

  async getCachedDepartmentData(deptId: string): Promise<any | null> {
    return this.getCompressed(`dept:${deptId}`);
  }

  // Invalidate patterns
  async invalidateUserCache(userId: string): Promise<void> {
    if (!this.redis || !this.isConnected) return;
    await this.redis.del(`user:${userId}`);
  }

  async invalidateFormCache(formId: string): Promise<void> {
    if (!this.redis || !this.isConnected) return;
    await this.redis.del(`form:${formId}`);
  }

  async invalidateDepartmentCache(deptId: string): Promise<void> {
    if (!this.redis || !this.isConnected) return;
    await this.redis.del(`dept:${deptId}`);
  }
}

export const redisCache = RedisCacheService.getInstance();