import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { KnowledgeBasePermissionsService } from '@/lib/services/kb-permissions-service';
import type { Database } from '@/lib/database.types';

type Article = Database['public']['Tables']['kb_articles']['Row'];
type ArticlePermission = Database['public']['Tables']['kb_article_permissions']['Row'];

interface UseKBPermissionsReturn {
  canView: boolean;
  canEdit: boolean;
  loading: boolean;
  error: Error | null;
  checkPermission: (articleId: string) => Promise<void>;
}

export function useKBPermissions(articleId?: string): UseKBPermissionsReturn {
  const { user } = useAuth();
  const [canView, setCanView] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const checkPermission = useCallback(async (id: string) => {
    if (!user?.id) {
      setCanView(false);
      setCanEdit(false);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const [viewPermission, editPermission] = await Promise.all([
        KnowledgeBasePermissionsService.canUserViewArticle(id, user.id),
        KnowledgeBasePermissionsService.canUserEditArticle(id, user.id)
      ]);

      setCanView(viewPermission);
      setCanEdit(editPermission);
    } catch (err) {
      setError(err as Error);
      setCanView(false);
      setCanEdit(false);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (articleId) {
      checkPermission(articleId);
    } else {
      setLoading(false);
    }
  }, [articleId, checkPermission]);

  return {
    canView,
    canEdit,
    loading,
    error,
    checkPermission
  };
}

interface UseFilteredArticlesOptions {
  categoryId?: string;
  searchQuery?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

interface UseFilteredArticlesReturn {
  articles: Article[];
  count: number;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useFilteredArticles(options?: UseFilteredArticlesOptions): UseFilteredArticlesReturn {
  const { user } = useAuth();
  const [articles, setArticles] = useState<Article[]>([]);
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchArticles = useCallback(async () => {
    if (!user?.id) {
      setArticles([]);
      setCount(0);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await KnowledgeBasePermissionsService.getFilteredArticles(user.id, options)
      setArticles(result.articles)
      setCount(result.count)
    } catch (err) {
      setError(err as Error)
      setArticles([])
      setCount(0)
    } finally {
      setLoading(false)
    }
  }, [user?.id, options])

  useEffect(() => {
    fetchArticles()
  }, [fetchArticles])

  return {
    articles,
    count,
    loading,
    error,
    refetch: fetchArticles
  }
}
