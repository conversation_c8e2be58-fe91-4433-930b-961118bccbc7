"use client"

import { useEffect, useState } from "react"
import { toast as sonnerToast } from "sonner"

type ToastProps = {
  title?: string
  description?: string
  action?: React.ReactNode
  variant?: "default" | "destructive" | "success"
  duration?: number
}

export function useToast() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return {
      toast: (props: ToastProps) => {},
      dismiss: (toastId?: string) => {},
    }
  }

  return {
    toast: ({ title, description, action, variant, duration }: ToastProps) => {
      const toastFn = variant === "destructive" 
        ? sonnerToast.error 
        : variant === "success" 
          ? sonnerToast.success 
          : sonnerToast

      return toastFn(title, {
        description,
        action,
        duration: duration || 5000,
      })
    },
    dismiss: sonnerToast.dismiss,
  }
}
