(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},33873:e=>{"use strict";e.exports=require("path")},56634:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var t=a(70260),i=a(28203),n=a(25155),r=a.n(n),l=a(67292),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let o=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,2155)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},45445:(e,s,a)=>{Promise.resolve().then(a.bind(a,2155))},39365:(e,s,a)=>{Promise.resolve().then(a.bind(a,53870))},53870:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>F});var t=a(45512),i=a(43433),n=a(22417),r=a(81026),l=a(55131),c=a(97643),o=a(87021),d=a(29551),u=a(61075),m=a(8866),h=a(76795),p=a(48458),x=a(94667),g=a(64977),j=a(69855),f=a(70384),N=a(18357),w=a(94520),v=a(6841),y=a(97971),b=a(72301),A=a(58009),_=a(65518),q=a(24559);class S{constructor(){this.supabase=(0,q.U)(),this.emailQueue=[],this.smsQueue=[],this.processingEmail=!1,this.processingSms=!1,this.startNotificationProcessing()}static getInstance(){return S.instance||(S.instance=new S),S.instance}async sendNotification(e){try{let s=await this.getUserPreferences(e.userId);if(this.isInQuietHours(s)){await this.scheduleNotification(e);return}let{data:a,error:t}=await this.supabase.from("notifications").insert({user_id:e.userId,title:e.title,title_jp:e.titleJp,message:e.message,message_jp:e.messageJp,type:e.type,priority:e.priority,channels:e.channels,metadata:e.metadata,action_url:e.actionUrl,scheduled_at:e.scheduledAt,expires_at:e.expiresAt,created_at:new Date().toISOString()}).select().single();if(t)throw t;for(let t of(e.id=a.id,e.channels.includes("all")?["in-app","email","sms"]:e.channels))await this.sendToChannel(t,e,s)}catch(e){throw console.error("Error sending notification:",e),e}}async sendToChannel(e,s,a){switch(e){case"in-app":a.inApp&&await this.sendInAppNotification(s);break;case"email":a.email&&a.emailAddress&&await this.queueEmailNotification(s,a);break;case"sms":a.sms&&a.phoneNumber&&await this.queueSmsNotification(s,a)}}async sendInAppNotification(e){let s=this.supabase.channel(`notifications:${e.userId}`);await s.send({type:"broadcast",event:"new-notification",payload:e})}async queueEmailNotification(e,s){let a={...e,emailAddress:s.emailAddress,language:s.language};await this.supabase.from("notification_email_queue").insert({notification_id:e.id,to_email:s.emailAddress,subject:"ja"===s.language&&e.titleJp?e.titleJp:e.title,body:"ja"===s.language&&e.messageJp?e.messageJp:e.message,priority:e.priority,metadata:e.metadata,status:"pending"}),this.emailQueue.push(a)}async queueSmsNotification(e,s){let a={...e,phoneNumber:s.phoneNumber,language:s.language};await this.supabase.from("notification_sms_queue").insert({notification_id:e.id,to_phone:s.phoneNumber,message:this.formatSmsMessage(e,s.language),priority:e.priority,metadata:e.metadata,status:"pending"}),this.smsQueue.push(a)}formatSmsMessage(e,s){let a="ja"===s&&e.titleJp?e.titleJp:e.title,t="ja"===s&&e.messageJp?e.messageJp:e.message;return`${a}: ${t}`.substring(0,160)}async getUserPreferences(e){let{data:s,error:a}=await this.supabase.from("notification_preferences").select("*").eq("user_id",e).single();return a||!s?{userId:e,email:!0,sms:!1,inApp:!0,language:"ja"}:s}isInQuietHours(e){if(!e.quietHours?.enabled)return!1;let s=new Date,a=60*s.getHours()+s.getMinutes(),[t,i]=e.quietHours.start.split(":").map(Number),[n,r]=e.quietHours.end.split(":").map(Number),l=60*t+i,c=60*n+r;return l<=c?a>=l&&a<=c:a>=l||a<=c}async scheduleNotification(e){await this.supabase.from("scheduled_notifications").insert({...e,scheduled_for:this.getNextAvailableTime(e.userId)})}async getNextAvailableTime(e){let s=await this.getUserPreferences(e);if(!s.quietHours?.enabled)return new Date;let a=new Date,[t,i]=s.quietHours.end.split(":").map(Number),n=new Date(a);return n.setHours(t,i,0,0),n<=a&&n.setDate(n.getDate()+1),n}startNotificationProcessing(){setInterval(()=>this.processEmailQueue(),3e4),setInterval(()=>this.processSmsQueue(),3e4),setInterval(()=>this.processScheduledNotifications(),6e4)}async processEmailQueue(){if(!this.processingEmail&&0!==this.emailQueue.length){this.processingEmail=!0;try{let{data:e}=await this.supabase.from("notification_email_queue").select("*").eq("status","pending").order("priority",{ascending:!1}).limit(10);if(e&&e.length>0){let{error:s}=await this.supabase.functions.invoke("send-email-notifications",{body:{emails:e}});if(!s){let s=e.map(e=>e.id);await this.supabase.from("notification_email_queue").update({status:"sent",sent_at:new Date().toISOString()}).in("id",s)}}}catch(e){console.error("Error processing email queue:",e)}finally{this.processingEmail=!1}}}async processSmsQueue(){if(!this.processingSms&&0!==this.smsQueue.length){this.processingSms=!0;try{let{data:e}=await this.supabase.from("notification_sms_queue").select("*").eq("status","pending").order("priority",{ascending:!1}).limit(10);if(e&&e.length>0){let{error:s}=await this.supabase.functions.invoke("send-sms-notifications",{body:{messages:e}});if(!s){let s=e.map(e=>e.id);await this.supabase.from("notification_sms_queue").update({status:"sent",sent_at:new Date().toISOString()}).in("id",s)}}}catch(e){console.error("Error processing SMS queue:",e)}finally{this.processingSms=!1}}}async processScheduledNotifications(){try{let{data:e}=await this.supabase.from("scheduled_notifications").select("*").lte("scheduled_for",new Date().toISOString()).eq("status","pending");if(e&&e.length>0)for(let s of e)await this.sendNotification(s),await this.supabase.from("scheduled_notifications").update({status:"sent"}).eq("id",s.id)}catch(e){console.error("Error processing scheduled notifications:",e)}}async sendFromTemplate(e,s,a){let{data:t}=await this.supabase.from("notification_templates").select("*").eq("id",e).single();if(!t)throw Error("Template not found");let i={userId:s,title:this.interpolateTemplate(t.title_template,a),titleJp:t.title_template_jp?this.interpolateTemplate(t.title_template_jp,a):void 0,message:this.interpolateTemplate(t.message_template,a),messageJp:t.message_template_jp?this.interpolateTemplate(t.message_template_jp,a):void 0,type:t.type||"info",priority:t.priority||"medium",channels:t.channels,metadata:{...t.metadata,...a}};await this.sendNotification(i)}interpolateTemplate(e,s){return e.replace(/\{\{(\w+)\}\}/g,(e,a)=>s[a]||e)}async updatePreferences(e){await this.supabase.from("notification_preferences").upsert(e)}async getNotificationHistory(e,s=50){let{data:a}=await this.supabase.from("notifications").select("*").eq("user_id",e).order("created_at",{ascending:!1}).limit(s);return a||[]}async markAsRead(e){await this.supabase.from("notifications").update({read_at:new Date().toISOString()}).eq("id",e)}async deleteNotification(e){await this.supabase.from("notifications").delete().eq("id",e)}}let C=S.getInstance();var E=a(24849),k=a(44269),P=a(67418),I=a(86201),$=a(94209),H=a(1372),T=a(16928),J=a(12952),R=a(77252),B=a(666),D=a(55669),M=a(47699),U=a(71965),Q=a(69193);function Z(){let[e,s]=(0,A.useState)([]),[a,i]=(0,A.useState)(0),[n,r]=(0,A.useState)({email:!0,sms:!1,inApp:!0,emailAddress:"",phoneNumber:"",language:"ja",quietHours:{enabled:!1,start:"22:00",end:"08:00"}}),[l,c]=(0,A.useState)(!1),{toast:d}=(0,_.d)(),u=(0,q.U)(),h=async e=>{await C.markAsRead(e),s(s=>s.map(s=>s.id===e?{...s,readAt:new Date().toISOString()}:s)),i(e=>Math.max(0,e-1))},p=async()=>{let a=e.filter(e=>!e.readAt);await Promise.all(a.map(e=>C.markAsRead(e.id))),s(e=>e.map(e=>({...e,readAt:new Date().toISOString()}))),i(0)},x=async()=>{await Promise.all(e.map(e=>C.deleteNotification(e.id))),s([]),i(0)},g=async()=>{let{data:{user:e}}=await u.auth.getUser();e&&(await C.updatePreferences({userId:e.id,email:n.email,sms:n.sms,inApp:n.inApp,emailAddress:n.emailAddress,phoneNumber:n.phoneNumber,language:n.language,quietHours:n.quietHours}),d({title:"ja"===n.language?"設定を更新しました":"Settings updated",description:"ja"===n.language?"通知設定が正常に更新されました。":"Your notification preferences have been updated successfully."}))},j=e=>{switch(e){case"success":return(0,t.jsx)(E.A,{className:"h-4 w-4 text-green-500"});case"error":return(0,t.jsx)(k.A,{className:"h-4 w-4 text-red-500"});case"warning":return(0,t.jsx)(P.A,{className:"h-4 w-4 text-yellow-500"});default:return(0,t.jsx)(I.A,{className:"h-4 w-4 text-blue-500"})}},f=e=>{switch(e){case"email":return(0,t.jsx)(m.A,{className:"h-3 w-3"});case"sms":return(0,t.jsx)($.A,{className:"h-3 w-3"});default:return(0,t.jsx)(H.A,{className:"h-3 w-3"})}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(J.rI,{children:[(0,t.jsx)(J.ty,{asChild:!0,children:(0,t.jsxs)(o.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,t.jsx)(T.A,{className:"h-5 w-5"}),a>0&&(0,t.jsx)(R.E,{className:"absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center",children:a})]})}),(0,t.jsxs)(J.SQ,{className:"w-96",align:"end",children:[(0,t.jsxs)(J.lp,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"ja"===n.language?"通知":"Notifications"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>c(!0),children:(0,t.jsx)(w.A,{className:"h-4 w-4"})}),(0,t.jsx)(o.$,{variant:"ghost",size:"sm",onClick:p,disabled:0===a,children:"ja"===n.language?"すべて既読":"Mark all read"}),(0,t.jsx)(o.$,{variant:"ghost",size:"sm",onClick:x,disabled:0===e.length,children:"ja"===n.language?"クリア":"Clear"})]})]}),(0,t.jsx)(J.mB,{}),(0,t.jsx)(B.F,{className:"h-[400px]",children:0===e.length?(0,t.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"ja"===n.language?"通知はありません":"No notifications"}):e.map(e=>(0,t.jsx)(J._2,{className:`p-4 cursor-pointer ${e.readAt?"":"bg-accent/50"}`,onClick:()=>{h(e.id),e.actionUrl&&(window.location.href=e.actionUrl)},children:(0,t.jsxs)("div",{className:"flex gap-3 w-full",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mt-1",children:j(e.type)}),(0,t.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"ja"===n.language&&e.titleJp?e.titleJp:e.title}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"ja"===n.language&&e.messageJp?e.messageJp:e.message}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString("ja"===n.language?"ja-JP":"en-US")}),(0,t.jsx)("div",{className:"flex gap-1",children:e.channels.map(e=>(0,t.jsx)(R.E,{variant:"secondary",className:"h-5 px-1",children:f(e)},e))})]})]})]})},e.id))})]})]}),(0,t.jsx)(U.lG,{open:l,onOpenChange:c,children:(0,t.jsxs)(U.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(U.c7,{children:[(0,t.jsx)(U.L3,{children:"ja"===n.language?"通知設定":"Notification Settings"}),(0,t.jsx)(U.rr,{children:"ja"===n.language?"通知の受信方法と設定を管理します。":"Manage how you receive notifications."})]}),(0,t.jsxs)(Q.tU,{defaultValue:"channels",className:"w-full",children:[(0,t.jsxs)(Q.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(Q.Xi,{value:"channels",children:"ja"===n.language?"チャンネル":"Channels"}),(0,t.jsx)(Q.Xi,{value:"preferences",children:"ja"===n.language?"設定":"Preferences"}),(0,t.jsx)(Q.Xi,{value:"quiet",children:"ja"===n.language?"通知停止時間":"Quiet Hours"})]}),(0,t.jsx)(Q.av,{value:"channels",className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M.J,{htmlFor:"in-app",className:"text-base",children:"ja"===n.language?"アプリ内通知":"In-App Notifications"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"ja"===n.language?"アプリケーション内でリアルタイム通知を受信します。":"Receive real-time notifications within the application."})]}),(0,t.jsx)(D.d,{id:"in-app",checked:n.inApp,onCheckedChange:e=>r(s=>({...s,inApp:e}))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M.J,{htmlFor:"email",className:"text-base",children:"ja"===n.language?"メール通知":"Email Notifications"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"ja"===n.language?"重要な更新をメールで受信します。":"Receive important updates via email."})]}),(0,t.jsx)(D.d,{id:"email",checked:n.email,onCheckedChange:e=>r(s=>({...s,email:e}))})]}),n.email&&(0,t.jsx)("div",{className:"ml-6",children:(0,t.jsx)("input",{type:"email",placeholder:"ja"===n.language?"メールアドレス":"Email address",value:n.emailAddress,onChange:e=>r(s=>({...s,emailAddress:e.target.value})),className:"w-full px-3 py-2 border rounded-md"})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M.J,{htmlFor:"sms",className:"text-base",children:"ja"===n.language?"SMS通知":"SMS Notifications"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"ja"===n.language?"緊急通知をSMSで受信します。":"Receive urgent notifications via SMS."})]}),(0,t.jsx)(D.d,{id:"sms",checked:n.sms,onCheckedChange:e=>r(s=>({...s,sms:e}))})]}),n.sms&&(0,t.jsx)("div",{className:"ml-6",children:(0,t.jsx)("input",{type:"tel",placeholder:"ja"===n.language?"電話番号":"Phone number",value:n.phoneNumber,onChange:e=>r(s=>({...s,phoneNumber:e.target.value})),className:"w-full px-3 py-2 border rounded-md"})})]})}),(0,t.jsx)(Q.av,{value:"preferences",className:"space-y-4",children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(M.J,{htmlFor:"language",children:"ja"===n.language?"言語":"Language"}),(0,t.jsxs)("select",{id:"language",value:n.language,onChange:e=>r(s=>({...s,language:e.target.value})),className:"w-full px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"ja",children:"日本語"}),(0,t.jsx)("option",{value:"en",children:"English"})]})]})})}),(0,t.jsx)(Q.av,{value:"quiet",className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M.J,{htmlFor:"quiet-hours",className:"text-base",children:"ja"===n.language?"通知停止時間を有効にする":"Enable Quiet Hours"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"ja"===n.language?"指定した時間帯は通知を送信しません。":"No notifications will be sent during specified hours."})]}),(0,t.jsx)(D.d,{id:"quiet-hours",checked:n.quietHours.enabled,onCheckedChange:e=>r(s=>({...s,quietHours:{...s.quietHours,enabled:e}}))})]}),n.quietHours.enabled&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(M.J,{htmlFor:"start-time",children:"ja"===n.language?"開始時刻":"Start Time"}),(0,t.jsx)("input",{id:"start-time",type:"time",value:n.quietHours.start,onChange:e=>r(s=>({...s,quietHours:{...s.quietHours,start:e.target.value}})),className:"w-full px-3 py-2 border rounded-md"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(M.J,{htmlFor:"end-time",children:"ja"===n.language?"終了時刻":"End Time"}),(0,t.jsx)("input",{id:"end-time",type:"time",value:n.quietHours.end,onChange:e=>r(s=>({...s,quietHours:{...s.quietHours,end:e.target.value}})),className:"w-full px-3 py-2 border rounded-md"})]})]})]})})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,t.jsx)(o.$,{variant:"outline",onClick:()=>c(!1),children:"ja"===n.language?"キャンセル":"Cancel"}),(0,t.jsx)(o.$,{onClick:()=>{g(),c(!1)},children:"ja"===n.language?"保存":"Save"})]})]})})]})}var O=a(84358);function F(){let{user:e,staff:s,signOut:a}=(0,i.A)(),{t:A,locale:_}=(0,r.s)(),{isGlobalAdmin:q,isSystemAdmin:S,isDepartmentAdmin:C,isHRStaff:E,isITSupport:k,isRegularUser:P,canManageUsers:I,canViewAllRequests:$,canCreateRequests:H,canAccessHRFunctions:T,canAccessSystemSettings:J}=(0,n.S)(),R=async()=>{await a()};return(0,t.jsxs)(l.A,{children:[(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"ITSync"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["ja"===_?s?.name_jp:s?.name_en||e?.email," - ",s?.role?.name||"User"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(Z,{}),(0,t.jsx)(d.c,{}),(0,t.jsx)(o.$,{onClick:R,variant:"outline",children:A("common.logout")})]})]})})}),(0,t.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(O.Yn,{})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[H()&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-5 w-5"}),A("navigation.requests")]}),(0,t.jsx)(c.BT,{children:"ja"===_?"新しいITサポ�Eトリクエストを作�E":"Create new IT support requests"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"default",onClick:()=>window.location.href="/request",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),A("common.create")," ",A("navigation.requests")]}),(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"outline",onClick:()=>window.location.href="/group-mail-management",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),A("services.groupMail.title")]}),(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"outline",onClick:()=>window.location.href="/dashboard/it-helpdesk/pc-admin-requests",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),A("services.pcAdmin.title")]}),(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"outline",onClick:()=>window.location.href="/dashboard/it-helpdesk/password-reset",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),A("services.passwordReset.title")]})]})})]}),(H()||k())&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"バッチ�E琁E/ Batch Processing"]}),(0,t.jsx)(c.BT,{children:"褁E��リクエスト�E一括処琁E                    "})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"default",onClick:()=>window.location.href="/batch-processing",children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"バッチ�E琁E��開姁E/ Start Batch Processing"]})})]}),I()&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"mr-2 h-5 w-5"}),"ユーザー管琁E/ User Management"]}),(0,t.jsx)(c.BT,{children:"スタチE��とユーザーの管琁E                    "})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"outline",children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Add New User"]}),(0,t.jsxs)(o.$,{className:"w-full justify-start",variant:"outline",onClick:()=>window.location.href="/admin/roles",children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Manage Roles"]})]})})]}),T()&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"mr-2 h-5 w-5"}),"人事管琁E/ HR Management"]}),(0,t.jsx)(c.BT,{children:"入社・退社手続きの管琁E                    "})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"入社手続き / Onboarding"}),(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"退社手続き / Offboarding"})]})})]}),J()&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"mr-2 h-5 w-5"}),"シスチE��設宁E/ System Settings"]}),(0,t.jsx)(c.BT,{children:"シスチE��全体�E設定と管琁E                    "})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"General Settings"}),(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"Security Settings"}),(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",onClick:()=>window.location.href="/dashboard/workflows/monitoring",children:"Workflow Monitoring"})]})})]}),$()&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-5 w-5"}),"リクエスト状況E/ Request Status"]}),(0,t.jsx)(c.BT,{children:"すべてのリクエスト�E状況確誁E                    "})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"View All Requests"}),(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"Pending Approvals"})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"mr-2 h-5 w-5"}),"ナレチE��ベ�Eス / Knowledge Base"]}),(0,t.jsx)(c.BT,{children:"AIを活用したサポ�Eト情報"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"Search Knowledge Base"}),(0,t.jsx)(o.$,{className:"w-full justify-start",variant:"outline",children:"AI Assistant"})]})})]})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsx)(y.Q,{context:{serviceCategory:"dashboard",currentStep:"main",formType:"general"},className:"w-full"})})]})})]}),(0,t.jsx)(b.G,{currentPage:"dashboard",defaultOpen:!1})]})}},2155:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[8096,2076],()=>a(56634));module.exports=t})();