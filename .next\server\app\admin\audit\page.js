(()=>{var e={};e.id=4530,e.ids=[4530],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},1144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(70260),i=r(28203),n=r(25155),a=r.n(n),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d=["",{children:["admin",{children:["audit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33564)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\audit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\audit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/audit/page",pathname:"/admin/audit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73465:(e,t,r)=>{Promise.resolve().then(r.bind(r,33564))},60441:(e,t,r)=>{Promise.resolve().then(r.bind(r,15307))},15307:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(45512),i=r(58009),n=r(78896),a=r(88976),l=r(70384),o=r(64977),d=r(97643),c=r(69193),u=r(29050),p=(r(86772),r(37746));function x(){let{user:e,profile:t}=(0,p.A)(),[r,x]=(0,i.useState)(null),[m,v]=(0,i.useState)(!0),[h,j]=(0,i.useState)(7),g=[{title:"Total Events",value:r?.total_events||0,icon:n.A,color:"text-blue-600",bgColor:"bg-blue-100"},{title:"Unique Users",value:r?.unique_users||0,icon:o.A,color:"text-green-600",bgColor:"bg-green-100"},{title:"Departments",value:r?.departments_affected||0,icon:a.A,color:"text-purple-600",bgColor:"bg-purple-100"},{title:"Security Events",value:r?.events_by_severity?.CRITICAL||0,icon:l.A,color:"text-red-600",bgColor:"bg-red-100"}];return(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Audit Trail Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"System activity monitoring and compliance reporting"})]}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)("select",{className:"px-3 py-2 border rounded-md",value:h,onChange:e=>j(Number(e.target.value)),children:[(0,s.jsx)("option",{value:1,children:"Last 24 hours"}),(0,s.jsx)("option",{value:7,children:"Last 7 days"}),(0,s.jsx)("option",{value:30,children:"Last 30 days"}),(0,s.jsx)("option",{value:90,children:"Last 90 days"})]})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:g.map(e=>(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:e.title}),(0,s.jsx)("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:(0,s.jsx)(e.icon,{className:`h-4 w-4 ${e.color}`})})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:m?"...":e.value.toLocaleString()})})]},e.title))}),(0,s.jsxs)(c.tU,{defaultValue:"all",className:"space-y-4",children:[(0,s.jsxs)(c.j7,{children:[(0,s.jsx)(c.Xi,{value:"all",children:"All Events"}),(0,s.jsx)(c.Xi,{value:"security",children:"Security Events"}),(0,s.jsx)(c.Xi,{value:"requests",children:"Request Events"}),(0,s.jsx)(c.Xi,{value:"errors",children:"Errors & Warnings"})]}),(0,s.jsx)(c.av,{value:"all",children:(0,s.jsx)(u.g,{})}),(0,s.jsx)(c.av,{value:"security",children:(0,s.jsx)(u.g,{showFilters:!1})}),(0,s.jsx)(c.av,{value:"requests",children:(0,s.jsx)(u.g,{showFilters:!1})}),(0,s.jsx)(c.av,{value:"errors",children:(0,s.jsx)(u.g,{showFilters:!1})})]})]})}r(41952),r(74297),r(71813)},33564:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\admin\\\\audit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\audit\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>r(1144));module.exports=s})();