              </div>

              <div>
                <Label htmlFor="actionUrl">Action URL (Optional)</Label>
                <Input
                  id="actionUrl"
                  value={notification.actionUrl}
                  onChange={(e) => setNotification({ ...notification, actionUrl: e.target.value })}
                  placeholder="/requests/123"
                />
              </div>

              <Button onClick={sendTestNotification} className="w-full">
                <Send className="mr-2 h-4 w-4" />
                Send Custom Notification
              </Button>
            </CardContent>
          </Card>

          {/* Template Notification */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Template Notification
              </CardTitle>
              <CardDescription>Send a notification using predefined templates</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="templateId">Template</Label>
                <Select value={template.templateId} onValueChange={(value) => setTemplate({ ...template, templateId: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="request_created">Request Created</SelectItem>
                    <SelectItem value="request_approved">Request Approved</SelectItem>
                    <SelectItem value="request_rejected">Request Rejected</SelectItem>
                    <SelectItem value="request_completed">Request Completed</SelectItem>
                    <SelectItem value="password_reset">Password Reset</SelectItem>
                    <SelectItem value="system_maintenance">System Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Template Variables</Label>
                <div className="space-y-2 mt-2">
                  {Object.entries(template.variables).map(([key, value]) => (
                    <div key={key}>
                      <Label htmlFor={key} className="text-sm">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</Label>
                      <Input
                        id={key}
                        value={value}
                        onChange={(e) => setTemplate({
                          ...template,
                          variables: { ...template.variables, [key]: e.target.value }
                        })}
                      />
                    </div>
                  ))}
                </div>
              </div>

              <Button onClick={sendTestTemplateNotification} className="w-full">
                <Send className="mr-2 h-4 w-4" />
                Send Template Notification
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Features Overview */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Multi-Channel Notification Features</CardTitle>
            <CardDescription>Comprehensive notification system with multiple delivery channels</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-blue-500" />
                  In-App Notifications
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Real-time delivery via WebSocket</li>
                  <li>• Persistent notification history</li>
                  <li>• Mark as read functionality</li>
                  <li>• Action URLs for quick navigation</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Mail className="h-4 w-4 text-green-500" />
                  Email Notifications
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Queue-based processing</li>
                  <li>• HTML and plain text support</li>
                  <li>• Multiple provider support (Resend, SendGrid)</li>
                  <li>• Retry mechanism for failed sends</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Phone className="h-4 w-4 text-purple-500" />
                  SMS Notifications
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Critical alerts via SMS</li>
                  <li>• Character limit handling</li>
                  <li>• Twilio integration ready</li>
                  <li>• Delivery status tracking</li>
                </ul>
              </div>
            </div>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2">Additional Features</h3>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Bilingual support (Japanese/English)</li>
                <li>• Quiet hours configuration</li>
                <li>• User preference management</li>
                <li>• Template-based notifications</li>
                <li>• Comprehensive audit logging</li>
                <li>• Priority-based processing</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
