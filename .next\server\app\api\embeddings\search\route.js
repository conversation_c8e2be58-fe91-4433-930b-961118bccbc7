"use strict";(()=>{var e={};e.id=5707,e.ids=[5707],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{e.exports=require("crypto")},18855:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>d,runtime:()=>p});var a=t(42706),n=t(28203),o=t(45994),i=t(39187),u=t(75122);let p="nodejs";async function d(e){try{let{query:r,language:t,matchThreshold:s,matchCount:a}=await e.json();if(!r||!t)return i.NextResponse.json({error:"Query and language are required"},{status:400});let n=await u.K.semanticSearch(r,t,{matchThreshold:s,matchCount:a});return i.NextResponse.json({success:!0,results:n,count:n.length})}catch(e){return console.error("Search API error:",e),i.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Search failed"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/embeddings/search/route",pathname:"/api/embeddings/search",filename:"route",bundlePath:"app/api/embeddings/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\search\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=c;function m(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(18855));module.exports=s})();