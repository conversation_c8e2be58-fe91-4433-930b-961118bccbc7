/**
 * API Documentation Page
 * 
 * This component provides a unified interface for displaying API documentation
 * with support for switching between Swagger UI and ReDoc UI.
 * 
 * NOTE: You need to install the following packages:
 * npm install swagger-ui-react redoc styled-components
 */

import React, { useEffect, useState } from 'react';
import { OpenAPIDocument } from './openapi-documentation';
import { SwaggerUI } from './swagger-ui';
import { ReDocUI } from './redoc-ui';

/**
 * API documentation view type
 */
export enum ApiDocumentationViewType {
  SWAGGER = 'swagger',
  REDOC = 'redoc'
}

/**
 * API documentation page props
 */
export interface ApiDocumentationPageProps {
  /**
   * Page title
   */
  title?: string;

  /**
   * Page description
   */
  description?: string;

  /**
   * API documentation URL
   */
  apiUrl?: string;

  /**
   * Default view type
   */
  defaultViewType?: ApiDocumentationViewType;

  /**
   * Whether to show the view switcher
   */
  showViewSwitcher?: boolean;

  /**
   * Custom styles for the page
   */
  styles?: {
    /**
     * Container styles
     */
    container?: React.CSSProperties;

    /**
     * Header styles
     */
    header?: React.CSSProperties;

    /**
     * Title styles
     */
    title?: React.CSSProperties;

    /**
     * Description styles
     */
    description?: React.CSSProperties;

    /**
     * View switcher styles
     */
    viewSwitcher?: React.CSSProperties;

    /**
     * View switcher button styles
     */
    viewSwitcherButton?: React.CSSProperties;

    /**
     * View switcher button active styles
     */
    viewSwitcherButtonActive?: React.CSSProperties;

    /**
     * Content styles
     */
    content?: React.CSSProperties;

    /**
     * Loading styles
     */
    loading?: React.CSSProperties;

    /**
     * Error styles
     */
    error?: React.CSSProperties;
  };

  /**
   * Custom class names
   */
  classNames?: {
    /**
     * Container class name
     */
    container?: string;

    /**
     * Header class name
     */
    header?: string;

    /**
     * Title class name
     */
    title?: string;

    /**
     * Description class name
     */
    description?: string;

    /**
     * View switcher class name
     */
    viewSwitcher?: string;

    /**
     * View switcher button class name
     */
    viewSwitcherButton?: string;

    /**
     * View switcher button active class name
     */
    viewSwitcherButtonActive?: string;

    /**
     * Content class name
     */
    content?: string;

    /**
     * Loading class name
     */
    loading?: string;

    /**
     * Error class name
     */
    error?: string;
  };

  /**
   * Swagger UI props
   */
  swaggerUIProps?: Omit<React.ComponentProps<typeof SwaggerUI>, 'spec'>;

  /**
   * ReDoc UI props
   */
  reDocUIProps?: Omit<React.ComponentProps<typeof ReDocUI>, 'spec'>;
}

/**
 * Default styles
 */
const defaultStyles: Required<ApiDocumentationPageProps['styles']> = {
  container: {
    fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    maxWidth: '100%',
    margin: '0 auto',
    padding: '0',
  },
  header: {
    padding: '1rem',
    borderBottom: '1px solid #eee',
    marginBottom: '1rem',
  },
  title: {
    fontSize: '2rem',
    fontWeight: 'bold',
    margin: '0 0 0.5rem 0',
  },
  description: {
    fontSize: '1rem',
    margin: '0 0 1rem 0',
    color: '#666',
  },
  viewSwitcher: {
    display: 'flex',
    gap: '0.5rem',
    marginBottom: '1rem',
  },
  viewSwitcherButton: {
    padding: '0.5rem 1rem',
    border: '1px solid #ddd',
    borderRadius: '4px',
    backgroundColor: '#f5f5f5',
    cursor: 'pointer',
    fontSize: '0.875rem',
    fontWeight: 'normal',
  },
  viewSwitcherButtonActive: {
    backgroundColor: '#e0e0e0',
    fontWeight: 'bold',
  },
  content: {
    padding: '0 1rem',
  },
  loading: {
    padding: '2rem',
    textAlign: 'center',
    color: '#666',
  },
  error: {
    padding: '2rem',
    textAlign: 'center',
    color: '#d32f2f',
  },
};

/**
 * API documentation page component
 */
export const ApiDocumentationPage: React.FC<ApiDocumentationPageProps> = ({
  title = 'API Documentation',
  description,
  apiUrl = '/api/docs',
  defaultViewType = ApiDocumentationViewType.SWAGGER,
  showViewSwitcher = true,
  styles = {},
  classNames = {},
  swaggerUIProps = {},
  reDocUIProps = {},
}) => {
  const [viewType, setViewType] = useState<ApiDocumentationViewType>(defaultViewType);
  const [spec, setSpec] = useState<OpenAPIDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Merge styles with defaults
  const mergedStyles = {
    container: { ...defaultStyles.container, ...styles.container },
    header: { ...defaultStyles.header, ...styles.header },
    title: { ...defaultStyles.title, ...styles.title },
    description: { ...defaultStyles.description, ...styles.description },
    viewSwitcher: { ...defaultStyles.viewSwitcher, ...styles.viewSwitcher },
    viewSwitcherButton: { ...defaultStyles.viewSwitcherButton, ...styles.viewSwitcherButton },
    viewSwitcherButtonActive: { ...defaultStyles.viewSwitcherButtonActive, ...styles.viewSwitcherButtonActive },
    content: { ...defaultStyles.content, ...styles.content },
    loading: { ...defaultStyles.loading, ...styles.loading },
    error: { ...defaultStyles.error, ...styles.error },
  };

  useEffect(() => {
    const fetchSpec = async () => {
      try {
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch API documentation: ${response.statusText}`);
        }
        const data = await response.json();
        setSpec(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setLoading(false);
      }
    };

    fetchSpec();
  }, [apiUrl]);

  const handleViewTypeChange = (type: ApiDocumentationViewType) => {
    setViewType(type);
  };

  const renderViewSwitcher = () => {
    if (!showViewSwitcher) {
      return null;
    }

    return (
      <div
        style={mergedStyles.viewSwitcher}
        className={classNames.viewSwitcher}
      >
        <button
          style={{
            ...mergedStyles.viewSwitcherButton,
            ...(viewType === ApiDocumentationViewType.SWAGGER ? mergedStyles.viewSwitcherButtonActive : {}),
          }}
          className={`${classNames.viewSwitcherButton || ''} ${
            viewType === ApiDocumentationViewType.SWAGGER ? classNames.viewSwitcherButtonActive || '' : ''
          }`}
          onClick={() => handleViewTypeChange(ApiDocumentationViewType.SWAGGER)}
        >
          Swagger UI
        </button>
        <button
          style={{
            ...mergedStyles.viewSwitcherButton,
            ...(viewType === ApiDocumentationViewType.REDOC ? mergedStyles.viewSwitcherButtonActive : {}),
          }}
          className={`${classNames.viewSwitcherButton || ''} ${
            viewType === ApiDocumentationViewType.REDOC ? classNames.viewSwitcherButtonActive || '' : ''
          }`}
          onClick={() => handleViewTypeChange(ApiDocumentationViewType.REDOC)}
        >
          ReDoc
        </button>
      </div>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div
          style={mergedStyles.loading}
          className={classNames.loading}
        >
          Loading API documentation...
        </div>
      );
    }

    if (error) {
      return (
        <div
          style={mergedStyles.error}
          className={classNames.error}
        >
          Error loading API documentation: {error.message}
        </div>
      );
    }

    if (!spec) {
      return (
        <div
          style={mergedStyles.error}
          className={classNames.error}
        >
          No API documentation available.
        </div>
      );
    }

    if (viewType === ApiDocumentationViewType.SWAGGER) {
      return <SwaggerUI spec={spec} {...swaggerUIProps} />;
    }

    return <ReDocUI spec={spec} {...reDocUIProps} />;
  };

  return (
    <div
      style={mergedStyles.container}
      className={classNames.container}
    >
      <div
        style={mergedStyles.header}
        className={classNames.header}
      >
        <h1
          style={mergedStyles.title}
          className={classNames.title}
        >
          {title}
        </h1>
        {description && (
          <p
            style={mergedStyles.description}
            className={classNames.description}
          >
            {description}
          </p>
        )}
        {renderViewSwitcher()}
      </div>
      <div
        style={mergedStyles.content}
        className={classNames.content}
      >
        {renderContent()}
      </div>
    </div>
  );
};

/**
 * Example usage:
 * 
 * ```tsx
 * // pages/api/docs.ts
 * import { NextApiRequest, NextApiResponse } from 'next';
 * import { openApiMiddleware } from '../../lib/api/openapi-documentation';
 * 
 * export default function handler(req: NextApiRequest, res: NextApiResponse) {
 *   return openApiMiddleware({
 *     title: 'My API',
 *     description: 'API documentation',
 *     version: '1.0.0',
 *     servers: [
 *       {
 *         url: 'https://api.example.com',
 *         description: 'Production server'
 *       },
 *       {
 *         url: 'https://staging.example.com',
 *         description: 'Staging server'
 *       }
 *     ]
 *   })(req, res);
 * }
 * 
 * // pages/docs.tsx
 * import { ApiDocumentationPage, ApiDocumentationViewType } from '../../lib/api/api-documentation-page';
 * 
 * export default function DocsPage() {
 *   return (
 *     <ApiDocumentationPage
 *       title="API Documentation"
 *       description="Documentation for the My API"
 *       apiUrl="/api/docs"
 *       defaultViewType={ApiDocumentationViewType.SWAGGER}
 *       showViewSwitcher={true}
 *     />
 *   );
 * }
 * ```
 */