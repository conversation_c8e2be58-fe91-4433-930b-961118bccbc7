'use client'

/**
 * AI Cost Management Dashboard
 * Comprehensive dashboard for monitoring AI usage, costs, and optimization
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  Shield,
  Target,
  Brain,
  Activity,
  Loader2
} from 'lucide-react'

interface AICostDashboardProps {
  organizationId: string
  userRole: string
}

interface DashboardData {
  overview: any
  usage: any
  costs: any
  quotas: any
  predictions: any
  optimizations: any
  reliability: any
  filtering: any
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

export function AICostDashboard({ organizationId, userRole }: AICostDashboardProps) {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [timeRange, setTimeRange] = useState('month')

  useEffect(() => {
    fetchDashboardData()
  }, [organizationId, timeRange])

  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const sections = ['overview', 'usage', 'costs', 'quotas', 'predictions', 'optimizations', 'reliability', 'filtering']
      const promises = sections.map(section =>
        fetch(`/api/admin/ai-cost?section=${section}&entityType=organization&entityId=${organizationId}&period=${timeRange}`)
          .then(res => res.json())
      )

      const results = await Promise.all(promises)
      const dashboardData = sections.reduce((acc, section, index) => {
        acc[section] = results[index].data
        return acc
      }, {} as any)

      setData(dashboardData)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Failed to load AI cost management data.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">AI Cost Management</h1>
          <p className="text-muted-foreground">Monitor usage, costs, and optimize AI operations</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={timeRange === 'week' ? 'default' : 'outline'}
            onClick={() => setTimeRange('week')}
          >
            Week
          </Button>
          <Button
            variant={timeRange === 'month' ? 'default' : 'outline'}
            onClick={() => setTimeRange('month')}
          >
            Month
          </Button>
          <Button
            variant={timeRange === 'quarter' ? 'default' : 'outline'}
            onClick={() => setTimeRange('quarter')}
          >
            Quarter
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${data.overview?.summary?.totalCost?.toFixed(2) || '0.00'}</div>
            <p className="text-xs text-muted-foreground">
              {data.overview?.trends?.length > 1 && (
                <>
                  {data.overview.trends[data.overview.trends.length - 1].cost >
                   data.overview.trends[data.overview.trends.length - 2].cost ? (
                    <TrendingUp className="inline h-3 w-3 text-red-500" />
                  ) : (
                    <TrendingDown className="inline h-3 w-3 text-green-500" />
                  )}
                  {' '}vs last period
                </>
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.overview?.summary?.totalRequests?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              {data.overview?.summary?.successRate?.toFixed(1) || '0'}% success rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.overview?.summary?.avgResponseTime?.toFixed(0) || '0'}ms</div>
            <p className="text-xs text-muted-foreground">
              Average across all models
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.overview?.recentAlerts?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              Budget and quota alerts
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="costs">Costs</TabsTrigger>
          <TabsTrigger value="quotas">Quotas</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="optimizations">Optimize</TabsTrigger>
          <TabsTrigger value="reliability">Reliability</TabsTrigger>
          <TabsTrigger value="filtering">Filtering</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Cost Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Breakdown by Provider</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={Object.entries(data.overview?.costBreakdown?.byProvider || {}).map(([name, value]) => ({
                        name,
                        value: Number(value)
                      }))}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {Object.entries(data.overview?.costBreakdown?.byProvider || {}).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Cost']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={data.overview?.trends || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Cost']} />
                    <Area type="monotone" dataKey="cost" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Quota Status */}
          <Card>
            <CardHeader>
              <CardTitle>Quota Status</CardTitle>
              <CardDescription>Current usage against configured limits</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.overview?.quotas?.map((quota: any, index: number) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">
                        {quota.quota_type} ({quota.period})
                      </span>
                      <Badge variant={
                        quota.status === 'exceeded' ? 'destructive' :
                        quota.status === 'warning' ? 'secondary' : 'default'
                      }>
                        {quota.status}
                      </Badge>
                    </div>
                    <Progress value={quota.usagePercentage} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{quota.current_usage.toFixed(2)} / {quota.limit_value}</span>
                      <span>{quota.usagePercentage.toFixed(1)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Top Models */}
            <Card>
              <CardHeader>
                <CardTitle>Top Models by Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.usage?.topModels || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="model" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="usage" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Top Features */}
            <Card>
              <CardHeader>
                <CardTitle>Top Features by Cost</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.usage?.topFeatures || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="feature" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Cost']} />
                    <Bar dataKey="cost" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {data.usage?.performance?.successRate?.toFixed(1) || '0'}%
                  </div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {data.usage?.performance?.avgResponseTime?.toFixed(0) || '0'}ms
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Response Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {data.usage?.performance?.cacheHitRate?.toFixed(1) || '0'}%
                  </div>
                  <div className="text-sm text-muted-foreground">Cache Hit Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimizations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>Potential cost savings and performance improvements</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.optimizations?.optimizations?.map((opt: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{opt.type.replace('_', ' ').toUpperCase()}</h4>
                        <p className="text-sm text-muted-foreground">{opt.description}</p>
                      </div>
                      <Badge variant={
                        opt.impact === 'high' ? 'default' :
                        opt.impact === 'medium' ? 'secondary' : 'outline'
                      }>
                        {opt.impact} impact
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Potential Savings:</span>
                        <span className="font-semibold text-green-600 ml-2">
                          ${opt.savings.toFixed(2)} ({opt.savingsPercentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Effort:</span>
                        <span className="ml-2">{opt.effort}</span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">{opt.implementation}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reliability" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Service Health */}
            <Card>
              <CardHeader>
                <CardTitle>Service Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.reliability?.serviceHealth?.map((service: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{service.provider}:{service.model}</span>
                        <div className="text-xs text-muted-foreground">
                          {service.responseTime}ms avg • {service.consecutiveFailures} failures
                        </div>
                      </div>
                      <Badge variant={
                        service.status === 'healthy' ? 'default' :
                        service.status === 'degraded' ? 'secondary' : 'destructive'
                      }>
                        {service.status === 'healthy' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {service.status === 'degraded' && <AlertTriangle className="h-3 w-3 mr-1" />}
                        {service.status === 'unhealthy' && <XCircle className="h-3 w-3 mr-1" />}
                        {service.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Circuit Breakers */}
            <Card>
              <CardHeader>
                <CardTitle>Circuit Breakers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.reliability?.circuitBreakers?.map((cb: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{cb.provider}:{cb.model}</span>
                        <div className="text-xs text-muted-foreground">
                          {cb.failureCount} failures • {cb.successCount} successes
                        </div>
                      </div>
                      <Badge variant={
                        cb.state === 'closed' ? 'default' :
                        cb.state === 'half-open' ? 'secondary' : 'destructive'
                      }>
                        {cb.state}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Reliability Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Reliability Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {data.reliability?.summary?.healthyServices || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Healthy Services</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">
                    {data.reliability?.summary?.degradedServices || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Degraded Services</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {data.reliability?.summary?.unhealthyServices || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Unhealthy Services</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {data.reliability?.summary?.openCircuitBreakers || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Open Breakers</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Add other tab contents as needed */}
      </Tabs>
    </div>
  )
}
