import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { aiAgentManager } from '@/lib/agents/agent-manager'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Get agent statuses
    const statuses = aiAgentManager.getAgentStatuses()
    
    // Get recent educational content
    const { data: recentContent } = await supabase
      .from('agent_educational_content')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)
    
    // Get learning progress for current user
    const { data: userProgress } = await supabase
      .from('user_learning_progress')
      .select('*')
      .eq('user_id', user.id)
    
    return NextResponse.json({
      agents: statuses,
      recentContent: recentContent || [],
      userProgress: userProgress || [],
      systemStatus: statuses.some(s => s.status === 'active') ? 'running' : 'stopped'
    })
  } catch (error: any) {
    console.error('AI Agents API error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check authentication and admin permissions
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const { data: staffData } = await supabase
      .from('staff')
      .select('role:roles(name)')
      .eq('auth_id', user.id)
      .single()
    
    const adminRoles = ['Global Administrator', 'Web App System Administrator']
    if (!staffData || !adminRoles.includes(staffData.role?.name)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }
    
    const body = await request.json()
    const { action, agentId, data } = body
    
    switch (action) {
      case 'start_all':
        await aiAgentManager.startAllAgents()
        return NextResponse.json({ success: true, message: 'All agents started' })
        
      case 'stop_all':
        await aiAgentManager.stopAllAgents()
        return NextResponse.json({ success: true, message: 'All agents stopped' })
        
      case 'trigger_research':
        if (agentId && data?.topic) {
          await aiAgentManager.triggerAgentResearch(agentId, data.topic)
          return NextResponse.json({ 
            success: true, 
            message: `Research triggered for ${agentId}` 
          })
        }
        return NextResponse.json({ error: 'Missing agentId or topic' }, { status: 400 })
        
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error: any) {
    console.error('AI Agents API error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}
