-- Knowledge Base Schema for ITSync
-- ================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgvector";

-- Categories table for organizing articles
CREATE TABLE IF NOT EXISTS kb_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name_en TEXT NOT NULL,
  name_jp TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description_en TEXT,
  description_jp TEXT,
  parent_category_id UUID REFERENCES kb_categories(id) ON DELETE SET NULL,
  icon TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Knowledge base articles table
CREATE TABLE IF NOT EXISTS kb_articles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id UUID REFERENCES kb_categories(id) ON DELETE SET NULL,
  title_en TEXT NOT NULL,
  title_jp TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  content_en TEXT,
  content_jp TEXT,
  summary_en TEXT,
  summary_jp TEXT,
  author_id UUID REFERENCES profiles(id),
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'internal', 'restricted')),
  helpful_count INTEGER DEFAULT 0,
  not_helpful_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tags table for article categorization
CREATE TABLE IF NOT EXISTS kb_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name_en TEXT NOT NULL,
  name_jp TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Article tags junction table
CREATE TABLE IF NOT EXISTS kb_article_tags (
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES kb_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (article_id, tag_id)
);

-- Vector embeddings table for semantic search
CREATE TABLE IF NOT EXISTS kb_embeddings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  language VARCHAR(10) NOT NULL CHECK (language IN ('en', 'jp')),
  embedding vector(1536), -- OpenAI ada-002 embeddings dimension
  content_hash TEXT NOT NULL, -- To track when content needs re-embedding
  model_version TEXT NOT NULL DEFAULT 'ada-002',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(article_id, language)
);

-- Article access permissions table
CREATE TABLE IF NOT EXISTS kb_article_permissions (
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  can_view BOOLEAN DEFAULT TRUE,
  can_edit BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (article_id, role_id)
);

-- User feedback on articles
CREATE TABLE IF NOT EXISTS kb_article_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  is_helpful BOOLEAN NOT NULL,
  feedback_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search history for analytics
CREATE TABLE IF NOT EXISTS kb_search_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id),
  query TEXT NOT NULL,
  results_count INTEGER DEFAULT 0,
  clicked_article_id UUID REFERENCES kb_articles(id),
  language VARCHAR(10) NOT NULL CHECK (language IN ('en', 'jp')),
  search_type VARCHAR(20) DEFAULT 'semantic' CHECK (search_type IN ('semantic', 'keyword', 'hybrid')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Related articles table
CREATE TABLE IF NOT EXISTS kb_related_articles (
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  related_article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  relevance_score FLOAT DEFAULT 0.0,
  relation_type VARCHAR(20) DEFAULT 'similar' CHECK (relation_type IN ('similar', 'prerequisite', 'next_step')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (article_id, related_article_id),
  CHECK (article_id != related_article_id)
);

-- FAQ generation queue
CREATE TABLE IF NOT EXISTS kb_faq_generation_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  generated_faqs JSONB,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);

-- Generated FAQs table
CREATE TABLE IF NOT EXISTS kb_faqs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
  question_en TEXT NOT NULL,
  question_jp TEXT NOT NULL,
  answer_en TEXT NOT NULL,
  answer_jp TEXT NOT NULL,
  relevance_score FLOAT DEFAULT 0.0,
  is_approved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_kb_articles_category ON kb_articles(category_id);
CREATE INDEX idx_kb_articles_status ON kb_articles(status);
CREATE INDEX idx_kb_articles_author ON kb_articles(author_id);
CREATE INDEX idx_kb_articles_published_at ON kb_articles(published_at DESC);
CREATE INDEX idx_kb_embeddings_article ON kb_embeddings(article_id);
CREATE INDEX idx_kb_search_history_user ON kb_search_history(user_id);
CREATE INDEX idx_kb_search_history_created ON kb_search_history(created_at DESC);
CREATE INDEX idx_kb_article_permissions_role ON kb_article_permissions(role_id);

-- Vector similarity search index
CREATE INDEX idx_kb_embeddings_vector ON kb_embeddings USING ivfflat (embedding vector_cosine_ops);

-- Functions for automatic timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_kb_categories_updated_at BEFORE UPDATE ON kb_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kb_articles_updated_at BEFORE UPDATE ON kb_articles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kb_embeddings_updated_at BEFORE UPDATE ON kb_embeddings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kb_faqs_updated_at BEFORE UPDATE ON kb_faqs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function for semantic search
CREATE OR REPLACE FUNCTION kb_semantic_search(
  query_embedding vector(1536),
  search_language VARCHAR(10),
  match_threshold FLOAT DEFAULT 0.7,
  match_count INT DEFAULT 10
)
RETURNS TABLE (
  article_id UUID,
  title TEXT,
  summary TEXT,
  content TEXT,
  similarity FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    a.id AS article_id,
    CASE 
      WHEN search_language = 'en' THEN a.title_en
      ELSE a.title_jp
    END AS title,
    CASE 
      WHEN search_language = 'en' THEN a.summary_en
      ELSE a.summary_jp
    END AS summary,
    CASE 
      WHEN search_language = 'en' THEN a.content_en
      ELSE a.content_jp
    END AS content,
    1 - (e.embedding <=> query_embedding) AS similarity
  FROM kb_embeddings e
  JOIN kb_articles a ON e.article_id = a.id
  WHERE 
    e.language = search_language
    AND a.status = 'published'
    AND 1 - (e.embedding <=> query_embedding) > match_threshold
  ORDER BY e.embedding <=> query_embedding
  LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security Policies
ALTER TABLE kb_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_article_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_article_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_article_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_related_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_faq_generation_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE kb_faqs ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies for public read access
CREATE POLICY kb_categories_read_policy ON kb_categories
  FOR SELECT TO authenticated
  USING (is_active = TRUE);

CREATE POLICY kb_articles_read_policy ON kb_articles
  FOR SELECT TO authenticated
  USING (
    status = 'published' 
    AND (
      visibility = 'public' 
      OR (
        visibility = 'internal' 
        AND EXISTS (
          SELECT 1 FROM profiles WHERE auth.uid() = id
        )
      )
      OR (
        visibility = 'restricted' 
        AND EXISTS (
          SELECT 1 FROM kb_article_permissions 
          WHERE article_id = kb_articles.id 
          AND role_id IN (
            SELECT roles.id FROM profiles 
            JOIN roles ON roles.name = profiles.role::text
            WHERE profiles.id = auth.uid()
          )
          AND can_view = TRUE
        )
      )
    )
  );

-- Admin write policies
CREATE POLICY kb_admin_write_policy ON kb_articles
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = auth.uid()
      AND p.role IN ('global_admin', 'system_admin', 'helpdesk_support')
    )
  );

-- User feedback policy
CREATE POLICY kb_feedback_insert_policy ON kb_article_feedback
  FOR INSERT TO authenticated
  WITH CHECK (
    user_id = auth.uid()
  );

-- Search history policy
CREATE POLICY kb_search_history_insert_policy ON kb_search_history
  FOR INSERT TO authenticated
  WITH CHECK (
    user_id = auth.uid()
  );

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT INSERT ON kb_article_feedback, kb_search_history TO authenticated;

-- Initial categories
INSERT INTO kb_categories (name_en, name_jp, slug, description_en, description_jp, sort_order)
VALUES 
  ('Getting Started', 'はじめに', 'getting-started', 'Basic information for new users', '新規ユーザー向けの基本情報', 1),
  ('Group Mail Management', 'グループメール管理', 'group-mail', 'How to manage group mail addresses', 'グループメールアドレスの管理方法', 2),
  ('SharePoint Access', 'SharePointアクセス', 'sharepoint', 'SharePoint library access management', 'SharePointライブラリアクセス管理', 3),
  ('PC Administration', 'PC管理', 'pc-admin', 'PC administrative status and software installation', 'PC管理者権限とソフトウェアインストール', 4),
  ('Password Management', 'パスワード管理', 'password', 'Password reset and authentication', 'パスワードリセットと認証', 5),
  ('Troubleshooting', 'トラブルシューティング', 'troubleshooting', 'Common issues and solutions', '一般的な問題と解決策', 6);
