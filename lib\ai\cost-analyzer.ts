/**
 * AI Cost Analyzer
 * Advanced cost estimation, prediction, and optimization recommendations
 */

import { createClient } from '@/lib/supabase/server'
import { getCacheService } from '@/lib/cache/cache-service'
import { getUsageTracker } from './usage-tracker'

interface CostModel {
  provider: string
  model: string
  inputCostPer1k: number
  outputCostPer1k: number
  requestCost?: number
  lastUpdated: string
}

interface CostPrediction {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly'
  predictedCost: number
  confidence: number
  factors: {
    trend: number
    seasonality: number
    growth: number
  }
  breakdown: {
    provider: string
    model: string
    feature: string
    estimatedCost: number
    percentage: number
  }[]
}

interface CostOptimization {
  type: 'model_switch' | 'caching' | 'batching' | 'quota_adjustment'
  description: string
  currentCost: number
  optimizedCost: number
  savings: number
  savingsPercentage: number
  implementation: string
  impact: 'low' | 'medium' | 'high'
  effort: 'low' | 'medium' | 'high'
}

interface CostBreakdown {
  total: number
  byProvider: { [provider: string]: number }
  byModel: { [model: string]: number }
  byFeature: { [feature: string]: number }
  byUser: { [userId: string]: number }
  byOrganization: { [orgId: string]: number }
  byTimeOfDay: { [hour: string]: number }
  byDayOfWeek: { [day: string]: number }
}

interface CostTrend {
  period: string
  cost: number
  tokens: number
  requests: number
  avgCostPerRequest: number
  avgCostPerToken: number
  changeFromPrevious: number
  changePercentage: number
}

class AICostAnalyzer {
  private supabase = createClient()
  private cache = getCacheService()
  private usageTracker = getUsageTracker()
  private readonly cachePrefix = 'ai_cost:'

  // Current pricing models (updated regularly)
  private costModels: CostModel[] = [
    // OpenAI Models
    { provider: 'openai', model: 'gpt-4', inputCostPer1k: 0.03, outputCostPer1k: 0.06, lastUpdated: '2024-01-01' },
    { provider: 'openai', model: 'gpt-4-turbo', inputCostPer1k: 0.01, outputCostPer1k: 0.03, lastUpdated: '2024-01-01' },
    { provider: 'openai', model: 'gpt-3.5-turbo', inputCostPer1k: 0.0015, outputCostPer1k: 0.002, lastUpdated: '2024-01-01' },
    { provider: 'openai', model: 'gpt-3.5-turbo-16k', inputCostPer1k: 0.003, outputCostPer1k: 0.004, lastUpdated: '2024-01-01' },
    
    // Anthropic Models
    { provider: 'anthropic', model: 'claude-3-opus', inputCostPer1k: 0.015, outputCostPer1k: 0.075, lastUpdated: '2024-01-01' },
    { provider: 'anthropic', model: 'claude-3-sonnet', inputCostPer1k: 0.003, outputCostPer1k: 0.015, lastUpdated: '2024-01-01' },
    { provider: 'anthropic', model: 'claude-3-haiku', inputCostPer1k: 0.00025, outputCostPer1k: 0.00125, lastUpdated: '2024-01-01' },
    
    // Google Models
    { provider: 'google', model: 'gemini-pro', inputCostPer1k: 0.0005, outputCostPer1k: 0.0015, lastUpdated: '2024-01-01' },
    { provider: 'google', model: 'gemini-pro-vision', inputCostPer1k: 0.0025, outputCostPer1k: 0.01, lastUpdated: '2024-01-01' }
  ]

  /**
   * Estimate cost for a request before making it
   */
  estimateRequestCost(
    provider: string,
    model: string,
    inputTokens: number,
    estimatedOutputTokens: number = 0
  ): number {
    const costModel = this.costModels.find(
      m => m.provider === provider && m.model === model
    )

    if (!costModel) {
      console.warn(`No cost model found for ${provider}:${model}`)
      return 0
    }

    const inputCost = (inputTokens / 1000) * costModel.inputCostPer1k
    const outputCost = (estimatedOutputTokens / 1000) * costModel.outputCostPer1k
    const requestCost = costModel.requestCost || 0

    return inputCost + outputCost + requestCost
  }

  /**
   * Get detailed cost breakdown for a period
   */
  async getCostBreakdown(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    startDate: Date,
    endDate: Date
  ): Promise<CostBreakdown> {
    const cacheKey = `${this.cachePrefix}breakdown:${entityType}:${entityId}:${startDate.toISOString()}:${endDate.toISOString()}`
    
    const cached = await this.cache.get<CostBreakdown>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      let query = this.supabase
        .from('ai_usage_logs')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())

      if (entityType === 'user') {
        query = query.eq('user_id', entityId)
      } else if (entityType === 'organization') {
        query = query.eq('organization_id', entityId)
      }

      const { data, error } = await query

      if (error) {
        throw error
      }

      const breakdown = this.calculateCostBreakdown(data || [])

      // Cache for 1 hour
      await this.cache.set(cacheKey, breakdown, {
        ttl: 3600,
        tags: ['ai_cost', entityType, entityId]
      })

      return breakdown

    } catch (error) {
      console.error('Error getting cost breakdown:', error)
      return this.getEmptyBreakdown()
    }
  }

  /**
   * Predict future costs based on historical data
   */
  async predictCosts(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    predictionPeriod: 'daily' | 'weekly' | 'monthly' | 'yearly'
  ): Promise<CostPrediction> {
    try {
      // Get historical data (last 90 days for better prediction)
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 90)

      const breakdown = await this.getCostBreakdown(entityType, entityId, startDate, endDate)
      const trends = await this.getCostTrends(entityType, entityId, startDate, endDate)

      // Calculate prediction using linear regression and seasonal adjustments
      const prediction = this.calculatePrediction(breakdown, trends, predictionPeriod)

      return prediction

    } catch (error) {
      console.error('Error predicting costs:', error)
      return {
        period: predictionPeriod,
        predictedCost: 0,
        confidence: 0,
        factors: { trend: 0, seasonality: 0, growth: 0 },
        breakdown: []
      }
    }
  }

  /**
   * Get cost optimization recommendations
   */
  async getOptimizationRecommendations(
    entityType: 'user' | 'organization' | 'global',
    entityId: string
  ): Promise<CostOptimization[]> {
    const cacheKey = `${this.cachePrefix}optimizations:${entityType}:${entityId}`
    
    const cached = await this.cache.get<CostOptimization[]>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 30) // Last 30 days

      const breakdown = await this.getCostBreakdown(entityType, entityId, startDate, endDate)
      const stats = await this.usageTracker.getUsageStats(entityType, entityId, 'month')

      const optimizations = this.analyzeOptimizations(breakdown, stats)

      // Cache for 6 hours
      await this.cache.set(cacheKey, optimizations, {
        ttl: 21600,
        tags: ['ai_cost', 'optimizations', entityType, entityId]
      })

      return optimizations

    } catch (error) {
      console.error('Error getting optimization recommendations:', error)
      return []
    }
  }

  /**
   * Get cost trends over time
   */
  async getCostTrends(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    startDate: Date,
    endDate: Date,
    granularity: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<CostTrend[]> {
    try {
      let query = this.supabase
        .from('ai_usage_logs')
        .select('created_at, estimated_cost, total_tokens')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at')

      if (entityType === 'user') {
        query = query.eq('user_id', entityId)
      } else if (entityType === 'organization') {
        query = query.eq('organization_id', entityId)
      }

      const { data, error } = await query

      if (error) {
        throw error
      }

      return this.aggregateTrends(data || [], granularity)

    } catch (error) {
      console.error('Error getting cost trends:', error)
      return []
    }
  }

  /**
   * Compare costs between different models
   */
  async compareModelCosts(
    entityType: 'user' | 'organization' | 'global',
    entityId: string,
    period: 'week' | 'month' | 'quarter' = 'month'
  ): Promise<{
    models: Array<{
      provider: string
      model: string
      totalCost: number
      totalTokens: number
      requests: number
      avgCostPerRequest: number
      avgCostPerToken: number
      efficiency: number
    }>
    recommendations: string[]
  }> {
    try {
      const endDate = new Date()
      const startDate = new Date()
      
      switch (period) {
        case 'week':
          startDate.setDate(endDate.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1)
          break
        case 'quarter':
          startDate.setMonth(endDate.getMonth() - 3)
          break
      }

      const breakdown = await this.getCostBreakdown(entityType, entityId, startDate, endDate)
      
      // Get detailed model statistics
      let query = this.supabase
        .from('ai_usage_logs')
        .select('provider, model, estimated_cost, total_tokens, success')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())

      if (entityType === 'user') {
        query = query.eq('user_id', entityId)
      } else if (entityType === 'organization') {
        query = query.eq('organization_id', entityId)
      }

      const { data, error } = await query

      if (error) {
        throw error
      }

      const modelStats = this.calculateModelComparison(data || [])
      const recommendations = this.generateModelRecommendations(modelStats)

      return {
        models: modelStats,
        recommendations
      }

    } catch (error) {
      console.error('Error comparing model costs:', error)
      return { models: [], recommendations: [] }
    }
  }

  /**
   * Calculate ROI for AI features
   */
  async calculateFeatureROI(
    organizationId: string,
    feature: string,
    period: 'month' | 'quarter' | 'year' = 'month'
  ): Promise<{
    cost: number
    estimatedValue: number
    roi: number
    metrics: {
      timeSaved: number
      tasksAutomated: number
      errorReduction: number
      userSatisfaction: number
    }
  }> {
    // This would integrate with business metrics to calculate actual ROI
    // For now, return estimated values based on usage patterns
    
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1)
        break
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3)
        break
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    const breakdown = await this.getCostBreakdown('organization', organizationId, startDate, endDate)
    const featureCost = breakdown.byFeature[feature] || 0

    // Estimate value based on feature type and usage
    const estimatedValue = this.estimateFeatureValue(feature, featureCost)
    const roi = featureCost > 0 ? ((estimatedValue - featureCost) / featureCost) * 100 : 0

    return {
      cost: featureCost,
      estimatedValue,
      roi,
      metrics: {
        timeSaved: Math.round(featureCost * 10), // Estimate 10 hours saved per dollar
        tasksAutomated: Math.round(featureCost * 5), // Estimate 5 tasks per dollar
        errorReduction: Math.min(95, featureCost * 2), // Up to 95% error reduction
        userSatisfaction: Math.min(100, 70 + (roi / 10)) // Base 70% + ROI factor
      }
    }
  }

  // Private helper methods
  private calculateCostBreakdown(data: any[]): CostBreakdown {
    const breakdown: CostBreakdown = {
      total: 0,
      byProvider: {},
      byModel: {},
      byFeature: {},
      byUser: {},
      byOrganization: {},
      byTimeOfDay: {},
      byDayOfWeek: {}
    }

    for (const record of data) {
      const cost = record.estimated_cost || 0
      breakdown.total += cost

      // By provider
      breakdown.byProvider[record.provider] = (breakdown.byProvider[record.provider] || 0) + cost

      // By model
      const modelKey = `${record.provider}:${record.model}`
      breakdown.byModel[modelKey] = (breakdown.byModel[modelKey] || 0) + cost

      // By feature
      breakdown.byFeature[record.feature] = (breakdown.byFeature[record.feature] || 0) + cost

      // By user
      breakdown.byUser[record.user_id] = (breakdown.byUser[record.user_id] || 0) + cost

      // By organization
      breakdown.byOrganization[record.organization_id] = (breakdown.byOrganization[record.organization_id] || 0) + cost

      // By time of day
      const hour = new Date(record.created_at).getHours()
      breakdown.byTimeOfDay[hour] = (breakdown.byTimeOfDay[hour] || 0) + cost

      // By day of week
      const dayOfWeek = new Date(record.created_at).getDay()
      breakdown.byDayOfWeek[dayOfWeek] = (breakdown.byDayOfWeek[dayOfWeek] || 0) + cost
    }

    return breakdown
  }

  private calculatePrediction(
    breakdown: CostBreakdown,
    trends: CostTrend[],
    period: string
  ): CostPrediction {
    if (trends.length === 0) {
      return {
        period: period as any,
        predictedCost: 0,
        confidence: 0,
        factors: { trend: 0, seasonality: 0, growth: 0 },
        breakdown: []
      }
    }

    // Simple linear regression for trend
    const trendFactor = this.calculateTrendFactor(trends)
    const seasonalityFactor = this.calculateSeasonalityFactor(trends)
    const growthFactor = this.calculateGrowthFactor(trends)

    // Predict based on recent average and factors
    const recentAverage = trends.slice(-7).reduce((sum, t) => sum + t.cost, 0) / 7
    let multiplier = 1

    switch (period) {
      case 'daily':
        multiplier = 1
        break
      case 'weekly':
        multiplier = 7
        break
      case 'monthly':
        multiplier = 30
        break
      case 'yearly':
        multiplier = 365
        break
    }

    const predictedCost = recentAverage * multiplier * (1 + trendFactor + seasonalityFactor + growthFactor)
    const confidence = Math.max(0, Math.min(100, 100 - (Math.abs(trendFactor) * 50)))

    // Generate breakdown prediction
    const totalCurrent = breakdown.total
    const breakdownPrediction = Object.entries(breakdown.byProvider).map(([provider, cost]) => ({
      provider,
      model: '',
      feature: '',
      estimatedCost: (cost / totalCurrent) * predictedCost,
      percentage: (cost / totalCurrent) * 100
    }))

    return {
      period: period as any,
      predictedCost,
      confidence,
      factors: {
        trend: trendFactor,
        seasonality: seasonalityFactor,
        growth: growthFactor
      },
      breakdown: breakdownPrediction
    }
  }

  private analyzeOptimizations(breakdown: CostBreakdown, stats: any): CostOptimization[] {
    const optimizations: CostOptimization[] = []

    // Model switching recommendations
    const modelOptimizations = this.analyzeModelSwitching(breakdown)
    optimizations.push(...modelOptimizations)

    // Caching recommendations
    if (stats.successRate > 90) {
      optimizations.push({
        type: 'caching',
        description: 'Increase cache TTL for similar requests',
        currentCost: breakdown.total,
        optimizedCost: breakdown.total * 0.8,
        savings: breakdown.total * 0.2,
        savingsPercentage: 20,
        implementation: 'Increase cache TTL from 1 hour to 4 hours for similar requests',
        impact: 'medium',
        effort: 'low'
      })
    }

    // Batching recommendations
    if (stats.totalRequests > 100) {
      optimizations.push({
        type: 'batching',
        description: 'Implement request batching for similar operations',
        currentCost: breakdown.total,
        optimizedCost: breakdown.total * 0.85,
        savings: breakdown.total * 0.15,
        savingsPercentage: 15,
        implementation: 'Group similar requests and process in batches',
        impact: 'medium',
        effort: 'medium'
      })
    }

    return optimizations.sort((a, b) => b.savings - a.savings)
  }

  private analyzeModelSwitching(breakdown: CostBreakdown): CostOptimization[] {
    const optimizations: CostOptimization[] = []

    // Check if expensive models can be replaced with cheaper alternatives
    for (const [model, cost] of Object.entries(breakdown.byModel)) {
      if (model.includes('gpt-4') && cost > 10) {
        optimizations.push({
          type: 'model_switch',
          description: `Switch from ${model} to gpt-3.5-turbo for non-critical tasks`,
          currentCost: cost,
          optimizedCost: cost * 0.1, // GPT-3.5 is ~10x cheaper
          savings: cost * 0.9,
          savingsPercentage: 90,
          implementation: 'Evaluate task complexity and switch to gpt-3.5-turbo where appropriate',
          impact: 'high',
          effort: 'medium'
        })
      }

      if (model.includes('claude-3-opus') && cost > 5) {
        optimizations.push({
          type: 'model_switch',
          description: `Switch from ${model} to claude-3-haiku for simple tasks`,
          currentCost: cost,
          optimizedCost: cost * 0.02, // Haiku is ~50x cheaper
          savings: cost * 0.98,
          savingsPercentage: 98,
          implementation: 'Use claude-3-haiku for simple text processing tasks',
          impact: 'high',
          effort: 'low'
        })
      }
    }

    return optimizations
  }

  private calculateModelComparison(data: any[]): any[] {
    const modelGroups = data.reduce((acc, record) => {
      const key = `${record.provider}:${record.model}`
      if (!acc[key]) {
        acc[key] = {
          provider: record.provider,
          model: record.model,
          totalCost: 0,
          totalTokens: 0,
          requests: 0,
          successfulRequests: 0
        }
      }
      
      acc[key].totalCost += record.estimated_cost
      acc[key].totalTokens += record.total_tokens
      acc[key].requests += 1
      if (record.success) {
        acc[key].successfulRequests += 1
      }
      
      return acc
    }, {})

    return Object.values(modelGroups).map((group: any) => ({
      ...group,
      avgCostPerRequest: group.requests > 0 ? group.totalCost / group.requests : 0,
      avgCostPerToken: group.totalTokens > 0 ? group.totalCost / group.totalTokens : 0,
      efficiency: group.requests > 0 ? (group.successfulRequests / group.requests) * 100 : 0
    }))
  }

  private generateModelRecommendations(modelStats: any[]): string[] {
    const recommendations: string[] = []

    // Find most expensive models
    const sortedByCost = [...modelStats].sort((a, b) => b.totalCost - a.totalCost)
    if (sortedByCost.length > 0 && sortedByCost[0].totalCost > 50) {
      recommendations.push(`Consider alternatives to ${sortedByCost[0].provider}:${sortedByCost[0].model} - highest cost contributor`)
    }

    // Find least efficient models
    const sortedByEfficiency = [...modelStats].sort((a, b) => a.efficiency - b.efficiency)
    if (sortedByEfficiency.length > 0 && sortedByEfficiency[0].efficiency < 90) {
      recommendations.push(`Review ${sortedByEfficiency[0].provider}:${sortedByEfficiency[0].model} - low success rate (${sortedByEfficiency[0].efficiency.toFixed(1)}%)`)
    }

    return recommendations
  }

  private aggregateTrends(data: any[], granularity: string): CostTrend[] {
    // Group data by time period
    const groups = data.reduce((acc, record) => {
      const date = new Date(record.created_at)
      let key: string

      switch (granularity) {
        case 'hour':
          key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`
          break
        case 'day':
          key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`
          break
        case 'week':
          const weekStart = new Date(date)
          weekStart.setDate(date.getDate() - date.getDay())
          key = weekStart.toISOString().split('T')[0]
          break
        case 'month':
          key = `${date.getFullYear()}-${date.getMonth()}`
          break
        default:
          key = date.toISOString().split('T')[0]
      }

      if (!acc[key]) {
        acc[key] = { cost: 0, tokens: 0, requests: 0 }
      }

      acc[key].cost += record.estimated_cost
      acc[key].tokens += record.total_tokens
      acc[key].requests += 1

      return acc
    }, {})

    // Convert to trend array
    const trends = Object.entries(groups).map(([period, stats]: [string, any]) => ({
      period,
      cost: stats.cost,
      tokens: stats.tokens,
      requests: stats.requests,
      avgCostPerRequest: stats.requests > 0 ? stats.cost / stats.requests : 0,
      avgCostPerToken: stats.tokens > 0 ? stats.cost / stats.tokens : 0,
      changeFromPrevious: 0,
      changePercentage: 0
    }))

    // Calculate changes
    for (let i = 1; i < trends.length; i++) {
      const current = trends[i]
      const previous = trends[i - 1]
      current.changeFromPrevious = current.cost - previous.cost
      current.changePercentage = previous.cost > 0 ? (current.changeFromPrevious / previous.cost) * 100 : 0
    }

    return trends.sort((a, b) => a.period.localeCompare(b.period))
  }

  private calculateTrendFactor(trends: CostTrend[]): number {
    if (trends.length < 2) return 0
    
    const recentTrends = trends.slice(-7) // Last 7 periods
    const avgChange = recentTrends.reduce((sum, t) => sum + t.changePercentage, 0) / recentTrends.length
    return avgChange / 100 // Convert to decimal
  }

  private calculateSeasonalityFactor(trends: CostTrend[]): number {
    // Simple seasonality calculation based on day of week patterns
    // In a real implementation, this would be more sophisticated
    return 0
  }

  private calculateGrowthFactor(trends: CostTrend[]): number {
    if (trends.length < 4) return 0
    
    const firstQuarter = trends.slice(0, Math.floor(trends.length / 4))
    const lastQuarter = trends.slice(-Math.floor(trends.length / 4))
    
    const firstAvg = firstQuarter.reduce((sum, t) => sum + t.cost, 0) / firstQuarter.length
    const lastAvg = lastQuarter.reduce((sum, t) => sum + t.cost, 0) / lastQuarter.length
    
    return firstAvg > 0 ? (lastAvg - firstAvg) / firstAvg : 0
  }

  private estimateFeatureValue(feature: string, cost: number): number {
    // Estimate value based on feature type
    const valueMultipliers: { [key: string]: number } = {
      'ticket_analysis': 15, // High value for automation
      'email_generation': 8,
      'report_generation': 12,
      'chat_support': 10,
      'document_processing': 6,
      'default': 5
    }

    const multiplier = valueMultipliers[feature] || valueMultipliers.default
    return cost * multiplier
  }

  private getEmptyBreakdown(): CostBreakdown {
    return {
      total: 0,
      byProvider: {},
      byModel: {},
      byFeature: {},
      byUser: {},
      byOrganization: {},
      byTimeOfDay: {},
      byDayOfWeek: {}
    }
  }
}

// Singleton instance
let costAnalyzer: AICostAnalyzer | null = null

export const getCostAnalyzer = (): AICostAnalyzer => {
  if (!costAnalyzer) {
    costAnalyzer = new AICostAnalyzer()
  }
  return costAnalyzer
}

export { AICostAnalyzer }
export type { CostModel, CostPrediction, CostOptimization, CostBreakdown, CostTrend }
