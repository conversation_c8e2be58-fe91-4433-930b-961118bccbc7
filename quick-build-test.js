const { spawn } = require('child_process');

console.log('Quick build test...');

const build = spawn('npx', ['next', 'build'], {
  cwd: 'C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1',
  stdio: 'pipe',
  shell: true
});

let output = '';
let errorOutput = '';

build.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log('STDOUT:', text);
});

build.stderr.on('data', (data) => {
  const text = data.toString();
  errorOutput += text;
  console.log('STDERR:', text);
});

build.on('close', (code) => {
  console.log(`\n=== BUILD COMPLETED ===`);
  console.log(`Exit code: ${code}`);
  
  if (code === 0) {
    console.log('✅ BUILD SUCCESSFUL!');
  } else {
    console.log('❌ BUILD FAILED');
    console.log('\nLast few lines of error output:');
    const lines = errorOutput.split('\n').slice(-10);
    lines.forEach(line => console.log(line));
  }
});

// Timeout after 3 minutes
setTimeout(() => {
  console.log('Build timeout - killing process');
  build.kill();
}, 180000);
