/**
 * Authentication Documentation Component
 * 
 * This component provides comprehensive documentation for authentication flows,
 * including OAuth2, JWT, and other security implementations.
 */

import React from 'react';

/**
 * Authentication flow type
 */
export enum AuthFlowType {
  OAUTH2_AUTHORIZATION_CODE = 'oauth2_authorization_code',
  OAUTH2_IMPLICIT = 'oauth2_implicit',
  OAUTH2_CLIENT_CREDENTIALS = 'oauth2_client_credentials',
  OAUTH2_PASSWORD = 'oauth2_password',
  JWT = 'jwt',
  API_KEY = 'api_key',
  BASIC_AUTH = 'basic_auth',
  DIGEST_AUTH = 'digest_auth',
  MFA = 'mfa',
  SOCIAL_AUTH = 'social_auth',
  CUSTOM = 'custom'
}

/**
 * Authentication flow documentation props
 */
export interface AuthFlowDocumentationProps {
  /**
   * Authentication flow type
   */
  flowType: AuthFlowType;

  /**
   * Authentication flow configuration
   */
  config?: {
    /**
     * OAuth2 configuration
     */
    oauth2?: {
      /**
       * Authorization URL
       */
      authorizationUrl?: string;

      /**
       * Token URL
       */
      tokenUrl?: string;

      /**
       * Refresh URL
       */
      refreshUrl?: string;

      /**
       * Scopes
       */
      scopes?: Record<string, string>;

      /**
       * Client ID
       */
      clientId?: string;

      /**
       * Client secret
       */
      clientSecret?: string;

      /**
       * Redirect URI
       */
      redirectUri?: string;

      /**
       * Response type
       */
      responseType?: string;

      /**
       * Grant type
       */
      grantType?: string;

      /**
       * Token endpoint auth method
       */
      tokenEndpointAuthMethod?: string;

      /**
       * PKCE method
       */
      pkceMethod?: 'S256' | 'plain';
    };

    /**
     * JWT configuration
     */
    jwt?: {
      /**
       * Token URL
       */
      tokenUrl?: string;

      /**
       * Token lifetime in seconds
       */
      tokenLifetime?: number;

      /**
       * Refresh token lifetime in seconds
       */
      refreshTokenLifetime?: number;

      /**
       * Token type
       */
      tokenType?: string;

      /**
       * Signing algorithm
       */
      signingAlgorithm?: string;

      /**
       * Issuer
       */
      issuer?: string;

      /**
       * Audience
       */
      audience?: string;
    };

    /**
     * API key configuration
     */
    apiKey?: {
      /**
       * API key name
       */
      name?: string;

      /**
       * API key location
       */
      in?: 'header' | 'query' | 'cookie';

      /**
       * API key prefix
       */
      prefix?: string;
    };

    /**
     * Basic auth configuration
     */
    basicAuth?: {
      /**
       * Username
       */
      username?: string;

      /**
       * Password
       */
      password?: string;
    };

    /**
     * MFA configuration
     */
    mfa?: {
      /**
       * MFA methods
       */
      methods?: Array<'sms' | 'email' | 'totp' | 'push' | 'hardware' | 'recovery'>;

      /**
       * MFA lifetime in seconds
       */
      lifetime?: number;
    };

    /**
     * Social auth configuration
     */
    socialAuth?: {
      /**
       * Providers
       */
      providers?: Array<'google' | 'facebook' | 'twitter' | 'github' | 'linkedin' | 'apple'>;
    };

    /**
     * Custom auth configuration
     */
    custom?: Record<string, any>;
  };

  /**
   * Custom styles
   */
  styles?: {
    /**
     * Container styles
     */
    container?: React.CSSProperties;

    /**
     * Header styles
     */
    header?: React.CSSProperties;

    /**
     * Title styles
     */
    title?: React.CSSProperties;

    /**
     * Description styles
     */
    description?: React.CSSProperties;

    /**
     * Section styles
     */
    section?: React.CSSProperties;

    /**
     * Section title styles
     */
    sectionTitle?: React.CSSProperties;

    /**
     * Section description styles
     */
    sectionDescription?: React.CSSProperties;

    /**
     * Code block styles
     */
    codeBlock?: React.CSSProperties;

    /**
     * Sequence diagram styles
     */
    sequenceDiagram?: React.CSSProperties;
  };

  /**
   * Custom class names
   */
  classNames?: {
    /**
     * Container class name
     */
    container?: string;

    /**
     * Header class name
     */
    header?: string;

    /**
     * Title class name
     */
    title?: string;

    /**
     * Description class name
     */
    description?: string;

    /**
     * Section class name
     */
    section?: string;

    /**
     * Section title class name
     */
    sectionTitle?: string;

    /**
     * Section description class name
     */
    sectionDescription?: string;

    /**
     * Code block class name
     */
    codeBlock?: string;

    /**
     * Sequence diagram class name
     */
    sequenceDiagram?: string;
  };
}

/**
 * Default styles
 */
const defaultStyles = {
  container: {
    fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    maxWidth: '100%',
    margin: '0 auto',
    padding: '1rem',
  },
  header: {
    marginBottom: '2rem',
  },
  title: {
    fontSize: '2rem',
    fontWeight: 'bold',
    margin: '0 0 0.5rem 0',
  },
  description: {
    fontSize: '1rem',
    margin: '0 0 1rem 0',
    color: '#666',
  },
  section: {
    marginBottom: '2rem',
  },
  sectionTitle: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    margin: '0 0 0.5rem 0',
  },
  sectionDescription: {
    fontSize: '1rem',
    margin: '0 0 1rem 0',
    color: '#666',
  },
  codeBlock: {
    fontFamily: 'monospace',
    backgroundColor: '#f5f5f5',
    padding: '1rem',
    borderRadius: '4px',
    overflow: 'auto',
    fontSize: '0.875rem',
    marginBottom: '1rem',
  },
  sequenceDiagram: {
    width: '100%',
    maxWidth: '800px',
    margin: '1rem 0',
    padding: '1rem',
    backgroundColor: '#f9f9f9',
    borderRadius: '4px',
    overflow: 'auto',
  },
};

/**
 * Authentication flow documentation component
 */
export const AuthFlowDocumentation: React.FC<AuthFlowDocumentationProps> = ({
  flowType,
  config = {},
  styles = {},
  classNames = {},
}) => {
  // Merge styles with defaults
  const mergedStyles = {
    container: { ...defaultStyles.container, ...styles.container },
    header: { ...defaultStyles.header, ...styles.header },
    title: { ...defaultStyles.title, ...styles.title },
    description: { ...defaultStyles.description, ...styles.description },
    section: { ...defaultStyles.section, ...styles.section },
    sectionTitle: { ...defaultStyles.sectionTitle, ...styles.sectionTitle },
    sectionDescription: { ...defaultStyles.sectionDescription, ...styles.sectionDescription },
    codeBlock: { ...defaultStyles.codeBlock, ...styles.codeBlock },
    sequenceDiagram: { ...defaultStyles.sequenceDiagram, ...styles.sequenceDiagram },
  };

  /**
   * Get flow title
   */
  const getFlowTitle = (): string => {
    switch (flowType) {
      case AuthFlowType.OAUTH2_AUTHORIZATION_CODE:
        return 'OAuth 2.0 Authorization Code Flow';
      case AuthFlowType.OAUTH2_IMPLICIT:
        return 'OAuth 2.0 Implicit Flow';
      case AuthFlowType.OAUTH2_CLIENT_CREDENTIALS:
        return 'OAuth 2.0 Client Credentials Flow';
      case AuthFlowType.OAUTH2_PASSWORD:
        return 'OAuth 2.0 Password Flow';
      case AuthFlowType.JWT:
        return 'JWT Authentication';
      case AuthFlowType.API_KEY:
        return 'API Key Authentication';
      case AuthFlowType.BASIC_AUTH:
        return 'Basic Authentication';
      case AuthFlowType.DIGEST_AUTH:
        return 'Digest Authentication';
      case AuthFlowType.MFA:
        return 'Multi-Factor Authentication';
      case AuthFlowType.SOCIAL_AUTH:
        return 'Social Authentication';
      case AuthFlowType.CUSTOM:
        return 'Custom Authentication';
      default:
        return 'Authentication Documentation';
    }
  };

  /**
   * Get flow description
   */
  const getFlowDescription = (): string => {
    switch (flowType) {
      case AuthFlowType.OAUTH2_AUTHORIZATION_CODE:
        return 'The Authorization Code flow is used to obtain both access tokens and refresh tokens and is optimized for confidential clients.';
      case AuthFlowType.OAUTH2_IMPLICIT:
        return 'The Implicit flow is used to obtain access tokens and is optimized for public clients known to operate a particular redirection URI.';
      case AuthFlowType.OAUTH2_CLIENT_CREDENTIALS:
        return 'The Client Credentials flow is used to obtain an access token outside the context of a user.';
      case AuthFlowType.OAUTH2_PASSWORD:
        return 'The Password flow is used to exchange a username and password for an access token.';
      case AuthFlowType.JWT:
        return 'JSON Web Token (JWT) is a compact, URL-safe means of representing claims to be transferred between two parties.';
      case AuthFlowType.API_KEY:
        return 'API keys are simple encrypted strings that identify an application or project without any additional information.';
      case AuthFlowType.BASIC_AUTH:
        return 'Basic Authentication is a simple authentication scheme built into the HTTP protocol.';
      case AuthFlowType.DIGEST_AUTH:
        return 'Digest Authentication is an authentication scheme built into the HTTP protocol that improves upon Basic Authentication by not sending passwords in plaintext.';
      case AuthFlowType.MFA:
        return 'Multi-Factor Authentication (MFA) is an authentication method that requires the user to provide two or more verification factors to gain access.';
      case AuthFlowType.SOCIAL_AUTH:
        return 'Social Authentication allows users to log in using their existing social media accounts.';
      case AuthFlowType.CUSTOM:
        return 'Custom Authentication is a bespoke authentication scheme designed for specific requirements.';
      default:
        return 'This document provides comprehensive documentation for authentication flows and security implementations.';
    }
  };

  return (
    <div style={mergedStyles.container} className={classNames.container}>
      <div style={mergedStyles.header} className={classNames.header}>
        <h1 style={mergedStyles.title} className={classNames.title}>
          {getFlowTitle()}
        </h1>
        <p style={mergedStyles.description}>
          {getFlowDescription()}
        </p>
      </div>

      {/* Flow-specific content will be rendered here */}
      {flowType === AuthFlowType.OAUTH2_AUTHORIZATION_CODE && (
        <div>
          <div style={mergedStyles.section} className={classNames.section}>
            <h2 style={mergedStyles.sectionTitle}>Overview</h2>
            <p style={mergedStyles.sectionDescription}>
              The Authorization Code flow is the most secure OAuth 2.0 flow and is recommended for server-side applications.
              It involves the following steps:
            </p>
            <ol>
              <li>The client redirects the user to the authorization server</li>
              <li>The user authenticates and grants permissions</li>
              <li>The authorization server redirects back to the client with an authorization code</li>
              <li>The client exchanges the authorization code for an access token and refresh token</li>
              <li>The client uses the access token to access protected resources</li>
            </ol>
          </div>

          <div style={mergedStyles.section} className={classNames.section}>
            <h2 style={mergedStyles.sectionTitle}>Sequence Diagram</h2>
            <div style={mergedStyles.sequenceDiagram} className={classNames.sequenceDiagram}>
              <pre>
{`Client                    Authorization Server                Resource Server
  |                               |                                |
  |---(A)- Authorization Request->|                                |
  |                               |                                |
  |<-(B)-- Authorization Code -----|                                |
  |                               |                                |
  |---(C)-- Authorization Code ---|                                |
  |       & Client Credentials    |                                |
  |                               |                                |
  |<-(D)------ Access Token ------|                                |
  |       & Refresh Token         |                                |
  |                               |                                |
  |---(E)------ Access Token ----------------------------->|       |
  |                               |                                |
  |<-(F)---- Protected Resource ----------------------------------|`}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* Add more flow-specific content for other flow types */}
    </div>
  );
};

/**
 * Example usage:
 * 
 * ```tsx
 * import { AuthFlowDocumentation, AuthFlowType } from '../lib/api/auth-documentation';
 * 
 * export default function AuthDocumentationPage() {
 *   return (
 *     <AuthFlowDocumentation
 *       flowType={AuthFlowType.OAUTH2_AUTHORIZATION_CODE}
 *       config={{
 *         oauth2: {
 *           authorizationUrl: 'https://auth.example.com/authorize',
 *           tokenUrl: 'https://auth.example.com/token',
 *           clientId: 'your-client-id',
 *           clientSecret: 'your-client-secret',
 *           redirectUri: 'https://your-app.com/callback',
 *           scopes: {
 *             'read': 'Read access',
 *             'write': 'Write access'
 *           },
 *           pkceMethod: 'S256'
 *         }
 *       }}
 *     />
 *   );
 * }
 * ```
 */