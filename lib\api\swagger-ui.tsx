/**
 * Swagger UI Component
 *
 * This component provides a Swagger UI interface for displaying OpenAPI documentation.
 * It uses the swagger-ui-react package to render the UI.
 *
 * NOTE: You need to install the swagger-ui-react package:
 * npm install swagger-ui-react
 */

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { OpenAPIDocument } from './openapi-documentation';

// Dynamically import swagger-ui-react to avoid SSR issues
// NOTE: This requires the swagger-ui-react package to be installed
const SwaggerUIReact = dynamic(
  () => import('swagger-ui-react').then((mod) => mod.default),
  { ssr: false }
);

// Dynamically import swagger-ui styles
// NOTE: This requires the swagger-ui-react package to be installed
const SwaggerUIStyles = () => {
  useEffect(() => {
    import('swagger-ui-react/swagger-ui.css');
  }, []);
  return null;
};

/**
 * Swagger UI props
 */
export interface SwaggerUIProps {
  /**
   * OpenAPI document or URL to OpenAPI document
   */
  spec: OpenAPIDocument | string;

  /**
   * Whether to show the top bar
   */
  showTopBar?: boolean;

  /**
   * Whether to show the models section
   */
  showModels?: boolean;

  /**
   * Whether to show the request/response examples
   */
  showExamples?: boolean;

  /**
   * Whether to show the "Try it out" button
   */
  showTryItOut?: boolean;

  /**
   * Whether to show the operation summary
   */
  showOperationSummary?: boolean;

  /**
   * Whether to show the operation description
   */
  showOperationDescription?: boolean;

  /**
   * Whether to show the operation tags
   */
  showOperationTags?: boolean;

  /**
   * Whether to show the operation ID
   */
  showOperationId?: boolean;

  /**
   * Whether to show the operation security
   */
  showOperationSecurity?: boolean;

  /**
   * Whether to show the operation servers
   */
  showOperationServers?: boolean;

  /**
   * Whether to show the operation extensions
   */
  showOperationExtensions?: boolean;

  /**
   * Whether to show the operation deprecated badge
   */
  showOperationDeprecated?: boolean;

  /**
   * Whether to show the operation method
   */
  showOperationMethod?: boolean;

  /**
   * Whether to show the operation path
   */
  showOperationPath?: boolean;

  /**
   * Whether to show the operation parameters
   */
  showOperationParameters?: boolean;

  /**
   * Whether to show the operation request body
   */
  showOperationRequestBody?: boolean;

  /**
   * Whether to show the operation responses
   */
  showOperationResponses?: boolean;

  /**
   * Whether to show the operation callbacks
   */
  showOperationCallbacks?: boolean;

  /**
   * Whether to show the operation links
   */
  showOperationLinks?: boolean;

  /**
   * Whether to show the operation external docs
   */
  showOperationExternalDocs?: boolean;

  /**
   * Whether to show the operation deprecated warning
   */
  showOperationDeprecatedWarning?: boolean;

  /**
   * Whether to show the operation security definitions
   */
  showOperationSecurityDefinitions?: boolean;

  /**
   * Whether to show the operation servers section
   */
  showOperationServersSection?: boolean;

  /**
   * Whether to show the operation extensions section
   */
  showOperationExtensionsSection?: boolean;

  /**
   * Whether to show the operation external docs section
   */
  showOperationExternalDocsSection?: boolean;

  /**
   * Whether to show the operation callbacks section
   */
  showOperationCallbacksSection?: boolean;

  /**
   * Whether to show the operation links section
   */
  showOperationLinksSection?: boolean;

  /**
   * Whether to show the operation security section
   */
  showOperationSecuritySection?: boolean;

  /**
   * Whether to show the operation parameters section
   */
  showOperationParametersSection?: boolean;

  /**
   * Whether to show the operation request body section
   */
  showOperationRequestBodySection?: boolean;

  /**
   * Whether to show the operation responses section
   */
  showOperationResponsesSection?: boolean;

  /**
   * Whether to show the operation deprecated section
   */
  showOperationDeprecatedSection?: boolean;

  /**
   * Whether to show the operation external docs link
   */
  showOperationExternalDocsLink?: boolean;

  /**
   * Whether to show the operation servers dropdown
   */
  showOperationServersDropdown?: boolean;

  /**
   * Whether to show the operation security dropdown
   */
  showOperationSecurityDropdown?: boolean;

  /**
   * Whether to show the operation parameters table
   */
  showOperationParametersTable?: boolean;

  /**
   * Whether to show the operation request body table
   */
  showOperationRequestBodyTable?: boolean;

  /**
   * Whether to show the operation responses table
   */
  showOperationResponsesTable?: boolean;

  /**
   * Whether to show the operation callbacks table
   */
  showOperationCallbacksTable?: boolean;

  /**
   * Whether to show the operation links table
   */
  showOperationLinksTable?: boolean;

  /**
   * Whether to show the operation security table
   */
  showOperationSecurityTable?: boolean;

  /**
   * Whether to show the operation servers table
   */
  showOperationServersTable?: boolean;

  /**
   * Whether to show the operation extensions table
   */
  showOperationExtensionsTable?: boolean;

  /**
   * Whether to show the operation external docs table
   */
  showOperationExternalDocsTable?: boolean;

  /**
   * Whether to show the operation deprecated table
   */
  showOperationDeprecatedTable?: boolean;

  /**
   * Whether to show the operation summary table
   */
  showOperationSummaryTable?: boolean;

  /**
   * Whether to show the operation description table
   */
  showOperationDescriptionTable?: boolean;

  /**
   * Whether to show the operation tags table
   */
  showOperationTagsTable?: boolean;

  /**
   * Whether to show the operation ID table
   */
  showOperationIdTable?: boolean;

  /**
   * Whether to show the operation method table
   */
  showOperationMethodTable?: boolean;

  /**
   * Whether to show the operation path table
   */
  showOperationPathTable?: boolean;

  /**
   * Custom plugins to add to Swagger UI
   */
  plugins?: any[];

  /**
   * Custom layout to use for Swagger UI
   */
  layout?: string;

  /**
   * Custom theme to use for Swagger UI
   */
  theme?: any;

  /**
   * Custom presets to use for Swagger UI
   */
  presets?: any[];

  /**
   * Custom components to use for Swagger UI
   */
  components?: any;

  /**
   * Custom styles to apply to Swagger UI
   */
  styles?: React.CSSProperties;

  /**
   * Custom class name to apply to Swagger UI
   */
  className?: string;

  /**
   * Custom ID to apply to Swagger UI
   */
  id?: string;

  /**
   * Custom attributes to apply to Swagger UI
   */
  attributes?: Record<string, any>;

  /**
   * Custom data attributes to apply to Swagger UI
   */
  dataAttributes?: Record<string, any>;

  /**
   * Custom ARIA attributes to apply to Swagger UI
   */
  ariaAttributes?: Record<string, any>;

  /**
   * Custom event handlers to apply to Swagger UI
   */
  eventHandlers?: Record<string, any>;

  /**
   * Custom ref to apply to Swagger UI
   */
  ref?: React.Ref<any>;

  /**
   * Custom key to apply to Swagger UI
   */
  key?: React.Key;

  /**
   * Custom children to render inside Swagger UI
   */
  children?: React.ReactNode;
}

/**
 * Swagger UI component
 */
export const SwaggerUI: React.FC<SwaggerUIProps> = ({
  spec,
  showTopBar = true,
  showModels = true,
  showExamples = true,
  showTryItOut = true,
  ...props
}) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null;
  }

  return (
    <>
      <SwaggerUIStyles />
      {/*
        NOTE: The SwaggerUIReact component expects a 'spec' prop and other configuration options.
        TypeScript errors will be resolved once the swagger-ui-react package is installed.
      */}
      <SwaggerUIReact
        spec={spec}
        docExpansion="list"
        defaultModelsExpandDepth={showModels ? 1 : -1}
        displayRequestDuration={true}
        filter={true}
        showExtensions={true}
        showCommonExtensions={true}
        deepLinking={true}
        persistAuthorization={true}
        withCredentials={true}
        supportedSubmitMethods={
          showTryItOut
            ? ['get', 'post', 'put', 'delete', 'patch', 'options', 'head']
            : []
        }
        tagsSorter="alpha"
        operationsSorter="alpha"
        {...props}
      />
    </>
  );
};

/**
 * API documentation page component
 */
export const ApiDocumentationPage: React.FC<{
  title?: string;
  description?: string;
  apiUrl?: string;
}> = ({ title = 'API Documentation', description, apiUrl = '/api/docs' }) => {
  const [spec, setSpec] = useState<OpenAPIDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchSpec = async () => {
      try {
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch API documentation: ${response.statusText}`);
        }
        const data = await response.json();
        setSpec(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setLoading(false);
      }
    };

    fetchSpec();
  }, [apiUrl]);

  if (loading) {
    return (
      <div className="api-docs-loading">
        <h1>{title}</h1>
        {description && <p>{description}</p>}
        <p>Loading API documentation...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="api-docs-error">
        <h1>{title}</h1>
        {description && <p>{description}</p>}
        <p>Error loading API documentation: {error.message}</p>
      </div>
    );
  }

  if (!spec) {
    return (
      <div className="api-docs-error">
        <h1>{title}</h1>
        {description && <p>{description}</p>}
        <p>No API documentation available.</p>
      </div>
    );
  }

  return (
    <div className="api-docs">
      <h1>{title}</h1>
      {description && <p>{description}</p>}
      <SwaggerUI spec={spec} />
    </div>
  );
};

/**
 * Example usage:
 * 
 * ```tsx
 * // pages/api/docs.ts
 * import { NextApiRequest, NextApiResponse } from 'next';
 * import { openApiMiddleware } from '../../lib/api/openapi-documentation';
 * 
 * export default function handler(req: NextApiRequest, res: NextApiResponse) {
 *   return openApiMiddleware({
 *     title: 'My API',
 *     description: 'API documentation',
 *     version: '1.0.0',
 *     servers: [
 *       {
 *         url: 'https://api.example.com',
 *         description: 'Production server'
 *       },
 *       {
 *         url: 'https://staging.example.com',
 *         description: 'Staging server'
 *       }
 *     ]
 *   })(req, res);
 * }
 * 
 * // pages/docs.tsx
 * import { ApiDocumentationPage } from '../../lib/api/swagger-ui';
 * 
 * export default function DocsPage() {
 *   return (
 *     <ApiDocumentationPage
 *       title="API Documentation"
 *       description="Documentation for the My API"
 *       apiUrl="/api/docs"
 *     />
 *   );
 * }
 * ```
 */