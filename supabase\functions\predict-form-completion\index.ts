import { serve } from 'https://deno.land/std@0.131.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { OpenAI } from 'https://esm.sh/openai@4'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PredictionRequest {
  formType: string
  currentField: string
  formData: Record<string, any>
  includeSequences?: boolean
}

interface PredictionResponse {
  predictions: Array<{
    fieldName: string
    suggestions: Array<{
      value: string
      confidence: number
      source: 'personal' | 'department' | 'ai'
    }>
  }>
  sequences: Array<{
    fields: Record<string, string>
    confidence: number
  }>
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!

    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const openai = new OpenAI({ apiKey: openaiApiKey })

    const { formType, currentField, formData, includeSequences = false } = await req.json() as PredictionRequest
    
    const authHeader = req.headers.get('Authorization')
    const token = authHeader?.replace('Bearer ', '')
    
    if (!token) {
      throw new Error('No authorization token')
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      throw new Error('Invalid token')
    }

    // Get user preferences
    const { data: preferences } = await supabase
      .from('prediction_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single()

    const enablePredictions = preferences?.enable_predictions ?? true
    const minConfidence = preferences?.min_confidence_threshold ?? 0.7

    if (!enablePredictions) {
      return new Response(
        JSON.stringify({ predictions: [], sequences: [] }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const predictions: PredictionResponse['predictions'] = []

    // Get user's personal patterns
    if (preferences?.enable_personal_patterns !== false) {
      const { data: userPatterns } = await supabase
        .from('user_form_patterns')
        .select('*')
        .eq('user_id', user.id)
        .eq('form_type', formType)
        .eq('field_name', currentField)
        .gte('confidence_score', minConfidence)
        .order('usage_count', { ascending: false })
        .order('confidence_score', { ascending: false })
        .limit(5)

      if (userPatterns && userPatterns.length > 0) {
        predictions.push({
          fieldName: currentField,
          suggestions: userPatterns.map(pattern => ({
            value: pattern.field_value,
            confidence: pattern.confidence_score,
            source: 'personal' as const
          }))
        })
      }
    }

    // Get department patterns
    if (preferences?.enable_department_patterns !== false) {
      const { data: staffInfo } = await supabase
        .from('staff')
        .select('department_id')
        .eq('auth_id', user.id)
        .single()

      if (staffInfo?.department_id) {
        const { data: deptPatterns } = await supabase
          .from('department_patterns')
          .select('*')
          .eq('department_id', staffInfo.department_id)
          .eq('form_type', formType)
          .eq('field_name', currentField)
          .single()

        if (deptPatterns?.common_values) {
          const deptSuggestions = deptPatterns.common_values
            .filter((item: any) => item.percentage >= minConfidence * 100)
            .slice(0, 3)
            .map((item: any) => ({
              value: item.value,
              confidence: item.percentage / 100,
              source: 'department' as const
            }))

          if (deptSuggestions.length > 0) {
            const existingPrediction = predictions.find(p => p.fieldName === currentField)
            if (existingPrediction) {
              existingPrediction.suggestions.push(...deptSuggestions)
            } else {
              predictions.push({
                fieldName: currentField,
                suggestions: deptSuggestions
              })
            }
          }
        }
      }
    }

    // Get AI predictions based on context
    if (predictions.length === 0 || predictions[0].suggestions.length < 3) {
      const contextPrompt = `Given a form of type "${formType}" with current data: ${JSON.stringify(formData)}, 
        suggest the most likely value for the field "${currentField}". 
        Consider this is for a Japanese IT helpdesk system.
        Provide up to 3 suggestions in order of likelihood.`

      const aiResponse = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          { role: 'system', content: 'You are an AI assistant helping predict form field values based on context.' },
          { role: 'user', content: contextPrompt }
        ],
        temperature: 0.3,
        max_tokens: 200
      })

      const aiContent = aiResponse.choices[0].message.content || ''
      // Parse AI suggestions (simplified - in production, use structured output)
      const aiSuggestions = aiContent.split('\n')
        .filter(line => line.trim())
        .slice(0, 3)
        .map((suggestion, index) => ({
          value: suggestion.trim(),
          confidence: 0.8 - (index * 0.1),
          source: 'ai' as const
        }))

      if (aiSuggestions.length > 0) {
        const existingPrediction = predictions.find(p => p.fieldName === currentField)
        if (existingPrediction) {
          existingPrediction.suggestions.push(...aiSuggestions)
        } else {
          predictions.push({
            fieldName: currentField,
            suggestions: aiSuggestions
          })
        }
      }
    }

    // Get pattern sequences if requested
    let sequences: PredictionResponse['sequences'] = []
    if (includeSequences && Object.keys(formData).length > 0) {
      const sequenceHash = await calculateSequenceHash(formData)
      
      const { data: patternSeqs } = await supabase
        .from('pattern_sequences')
        .select('*')
        .eq('user_id', user.id)
        .eq('form_type', formType)
        .gte('confidence_score', minConfidence)
        .order('occurrence_count', { ascending: false })
        .limit(3)

      if (patternSeqs) {
        sequences = patternSeqs.map(seq => ({
          fields: seq.field_sequence,
          confidence: seq.confidence_score
        }))
      }
    }

    // Record the prediction request for learning
    await supabase
      .from('user_form_patterns')
      .upsert({
        user_id: user.id,
        form_type: formType,
        field_name: currentField,
        field_value: formData[currentField] || '',
        usage_count: 1
      }, {
        onConflict: 'user_id,form_type,field_name,field_value',
        ignoreDuplicates: false
      })

    const response: PredictionResponse = {
      predictions,
      sequences
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Prediction error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

async function calculateSequenceHash(data: Record<string, any>): Promise<string> {
  const sortedData = Object.keys(data).sort().reduce((acc, key) => {
    acc[key] = data[key]
    return acc
  }, {} as Record<string, any>)
  
  const encoder = new TextEncoder()
  const data = encoder.encode(JSON.stringify(sortedData))
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}
