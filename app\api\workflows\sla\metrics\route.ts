import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { slaManager } from '@/lib/services/workflow/sla-manager';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const departmentId = searchParams.get('departmentId');

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    // Get SLA metrics
    const metrics = await slaManager.getSLAMetrics(
      new Date(startDate),
      new Date(endDate),
      departmentId || undefined
    );

    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching SLA metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SLA metrics' },
      { status: 500 }
    );
  }
}
