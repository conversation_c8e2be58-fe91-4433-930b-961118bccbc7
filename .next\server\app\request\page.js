(()=>{var e={};e.id=3144,e.ids=[3144],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},33873:e=>{"use strict";e.exports=require("path")},33980:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=t(70260),a=t(28203),i=t(25155),n=t.n(i),l=t(67292),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o=["",{children:["request",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74146)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/request/page",pathname:"/request",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},48542:(e,s,t)=>{Promise.resolve().then(t.bind(t,74146))},31238:(e,s,t)=>{Promise.resolve().then(t.bind(t,79304))},79304:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D});var r=t(45512),a=t(58009),i=t(79334),n=t(55131),l=t(98804),c=t(57270),o=t(31319),d=t(32664),u=t(66937),x=t(43433),p=t(58957),m=t(87021),h=t(3328),j=t(77252),f=t(75339),v=t(67418),N=t(35668),g=t(86235),b=t(15907),_=t(97971),y=t(97643),C=t(69193),w=t(666),q=t(39154),S=t(37133),A=(t(4269),t(31575),t(6841)),E=t(39094),F=t(86201),I=t(16873),k=t(42163);function P({serviceCategory:e,currentStep:s,className:t}){let{language:i}=(0,q.ok)(),[n,l]=(0,a.useState)([]),[c,o]=(0,a.useState)([]),[d,u]=(0,a.useState)(!0),x=async e=>{await S.N.from("chatbot_faq").update({usage_count:e.usage_count+1}).eq("id",e.id)};return(0,r.jsxs)(y.Zp,{className:t,children:[(0,r.jsx)(y.aR,{children:(0,r.jsxs)(y.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)(E.A,{className:"h-5 w-5"}),"ja"===i?"クイックヘルプ":"Quick Help"]})}),(0,r.jsx)(y.Wu,{children:(0,r.jsxs)(C.tU,{defaultValue:"faqs",className:"w-full",children:[(0,r.jsxs)(C.j7,{className:"grid w-full grid-cols-3",children:[(0,r.jsx)(C.Xi,{value:"faqs",children:"ja"===i?"よくある質問":"FAQs"}),(0,r.jsx)(C.Xi,{value:"tips",children:"ja"===i?"ヒント":"Tips"}),(0,r.jsx)(C.Xi,{value:"links",children:"ja"===i?"リンク":"Links"})]}),(0,r.jsx)(C.av,{value:"faqs",children:(0,r.jsx)(w.F,{className:"h-[250px]",children:d?(0,r.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:"ja"===i?"読み込み中...":"Loading..."}):n.length>0?(0,r.jsx)("div",{className:"space-y-3",children:n.map(e=>(0,r.jsxs)("div",{className:"p-3 rounded-lg border hover:bg-accent cursor-pointer transition-colors",onClick:()=>x(e),children:[(0,r.jsxs)("h4",{className:"font-medium text-sm flex items-start gap-2",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mt-0.5 text-primary"}),"ja"===i?e.question_ja:e.question_en]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1 ml-6",children:"ja"===i?e.answer_ja:e.answer_en}),(0,r.jsxs)(j.E,{variant:"secondary",className:"mt-2 ml-6 text-xs",children:[Math.round(100*e.confidence_score),"% ","ja"===i?"関連性":"Relevant"]})]},e.id))}):(0,r.jsxs)(f.Fc,{children:[(0,r.jsx)(F.A,{className:"h-4 w-4"}),(0,r.jsx)(f.XL,{children:"ja"===i?"FAQがありません":"No FAQs Available"}),(0,r.jsx)(f.TN,{children:"ja"===i?"現在のコンテキストに関連するFAQはありません。":"No FAQs found for the current context."})]})})}),(0,r.jsx)(C.av,{value:"tips",children:(0,r.jsx)(w.F,{className:"h-[250px]",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(f.Fc,{children:[(0,r.jsx)(E.A,{className:"h-4 w-4"}),(0,r.jsx)(f.XL,{children:"ja"===i?"フォーム入力のヒント":"Form Filling Tips"}),(0,r.jsx)(f.TN,{children:"ja"===i?"ユーザー名を入力すると、関連フィールドが自動的に入力されます。":"Enter a user name to auto-populate related fields."})]}),(0,r.jsxs)(f.Fc,{children:[(0,r.jsx)(I.A,{className:"h-4 w-4"}),(0,r.jsx)(f.XL,{children:"ja"===i?"検索機能":"Search Feature"}),(0,r.jsx)(f.TN,{children:"ja"===i?"PC IDは検索可能です。部分的なIDでも検索できます。":"PC IDs are searchable. You can search with partial IDs."})]}),"SPO"===e&&(0,r.jsxs)(f.Fc,{children:[(0,r.jsx)(F.A,{className:"h-4 w-4"}),(0,r.jsx)(f.XL,{children:"ja"===i?"SharePointアクセス":"SharePoint Access"}),(0,r.jsx)(f.TN,{children:"ja"===i?"複数のライブラリへのアクセスを一度にリクエストできます。":"You can request access to multiple libraries at once."})]})]})})}),(0,r.jsx)(C.av,{value:"links",children:(0,r.jsx)(w.F,{className:"h-[250px]",children:(0,r.jsx)("div",{className:"space-y-2",children:c.map((e,s)=>(0,r.jsxs)(m.$,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open(e.url,"_blank"),children:[e.icon,(0,r.jsxs)("div",{className:"ml-2 text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"ja"===i?e.titleJp:e.title}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"ja"===i?e.descriptionJp:e.description})]}),(0,r.jsx)(k.A,{className:"h-4 w-4 ml-auto"})]},s))})})})]})})]})}var T=t(72301),R=function(e){return e[e.SERVICE_SELECTION=0]="SERVICE_SELECTION",e[e.USER_SELECTION=1]="USER_SELECTION",e[e.FORM_FILLING=2]="FORM_FILLING",e[e.CONFIRMATION=3]="CONFIRMATION",e}(R||{});function D(){let[e,s]=(0,a.useState)(0),[t,y]=(0,a.useState)([]),[C,w]=(0,a.useState)([]),[q,S]=(0,a.useState)(0),[A,E]=(0,a.useState)([]),[F,I]=(0,a.useState)(null),[k,R]=(0,a.useState)(!1),[D,L]=(0,a.useState)(""),{user:O,staff:U}=(0,x.A)(),$=(0,i.useRouter)(),M=async()=>{if(F)try{R(!0),L("");let{data:e,error:s}=await p.ND.from("request_forms").insert({requester_id:O?.id,title:`Multi-service request: ${t.map(e=>e.name_jp).join(", ")}`,description:`Request for ${C.length} users across ${t.length} services`,status:"submitted",priority:"medium"}).select().single();if(s)throw s;let r=[];for(let s of A)for(let t of s.users)r.push({request_form_id:e.id,service_category_id:s.serviceCategory.id,affected_user_id:t.id,action_type:s.action,target_resource:JSON.stringify(s.formData),request_details:s.formData,status:"pending"});let{error:a}=await p.ND.from("request_items").insert(r);if(a)throw a;$.push(`/dashboard?success=request_submitted&id=${e.id}`)}catch(e){L("リクエストの送信に失敗しました / Failed to submit request: "+e.message)}finally{R(!1)}},X=()=>{$.push("/dashboard")},G=t[q],Q=(e+(2===e?q/t.length:0))/4*100;return(0,r.jsxs)(n.A,{children:[(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:(()=>{switch(e){case 0:return"サービス選択 / Service Selection";case 1:return"ユーザー選択 / User Selection";case 2:return`詳細入力 / Form Details (${q+1}/${t.length})`;case 3:return"確認 / Confirmation";default:return"ITリクエスト / IT Request"}})()}),(0,r.jsxs)(j.E,{variant:"outline",children:["ステップ ",e+1," / 4"]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:(()=>{switch(e){case 0:return"リクエストするサービスを選択してください";case 1:return"リクエストの対象となるユーザーを選択してください";case 2:return`${t[q]?.name_jp}の詳細を入力してください`;case 3:return"リクエスト内容を確認して送信してください";default:return"AIを活用したITサポートリクエストシステム"}})()}),(0,r.jsx)(h.k,{value:Q,className:"w-full h-2"})]}),D&&(0,r.jsxs)(f.Fc,{variant:"destructive",className:"mb-6",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)(f.TN,{children:D})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[0===e&&(0,r.jsx)(l.A,{onServiceSelect:e=>{y(e),L("")},selectedServices:t,disabled:k}),1===e&&(0,r.jsx)(c.A,{onUserSelect:e=>{w(e),L("")},selectedUsers:C,disabled:k}),2===e&&G&&(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"xl:col-span-3 space-y-6",children:(0,r.jsx)(o.A,{schema:(0,u.td)(G),onSubmit:e=>{let r=t[q],a={serviceCategory:r,users:C,formData:e,action:"add"};E(e=>{let s=[...e],t=s.findIndex(e=>e.serviceCategory.id===r.id);return t>=0?s[t]=a:s.push(a),s}),q<t.length-1?S(q+1):s(3)},onCancel:()=>s(1),loading:k,aiEnabled:!0,departmentId:U?.department_id})}),(0,r.jsxs)("div",{className:"xl:col-span-1 space-y-6",children:[(0,r.jsx)(P,{serviceCategory:G.code,currentStep:"form_filling",className:"sticky top-6"}),(0,r.jsx)(_.Q,{context:{serviceCategory:G.code,currentStep:"form_filling",formType:G.name_en},className:"sticky top-[360px]"})]})]}),3===e&&F&&(0,r.jsx)(d.EnhancedRequestConfirmation,{confirmation:F,onSubmit:M,onEdit:()=>{s(0),S(0)},onCancel:X,loading:k}),2!==e&&3!==e&&(0,r.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,r.jsxs)(m.$,{type:"button",variant:"outline",onClick:()=>{switch(e){case 1:s(0);break;case 2:q>0?S(q-1):s(1);break;case 3:s(2),S(t.length-1)}},disabled:0===e||k,children:[(0,r.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"戻る / Previous"]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(m.$,{type:"button",variant:"ghost",onClick:X,disabled:k,children:"キャンセル / Cancel"}),(0,r.jsxs)(m.$,{type:"button",onClick:()=>{switch(e){case 0:if(0===t.length){L("少なくとも1つのサービスを選択してください / Please select at least one service");return}s(1);break;case 1:if(0===C.length){L("少なくとも1人のユーザーを選択してください / Please select at least one user");return}s(2),S(0)}},disabled:k,children:[k&&(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"次へ / Next",(0,r.jsx)(b.A,{className:"ml-2 h-4 w-4"})]})]})]})]})]})})}),(0,r.jsx)(T.G,{currentForm:G?.name_en,currentPage:"request_wizard",defaultOpen:!1})]})}},74146:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\request\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8096,2076],()=>t(33980));module.exports=r})();