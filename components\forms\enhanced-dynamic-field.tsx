'use client'

import { useState, useEffect, useCallback } from 'react'
import { FormField } from '@/lib/form-types'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Loader2, Sparkles, Search, Check } from 'lucide-react'
import { supabase } from '@/lib/auth'
import { cn } from '@/lib/utils'

interface EnhancedDynamicFieldProps {
  field: FormField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
  aiSuggestion?: any
  onAiAccept?: (fieldId: string, value: any) => void
  departmentId?: string
}

export function EnhancedDynamicField({
  field, value, onChange, error, disabled = false, 
  aiSuggestion, onAiAccept, departmentId
}: EnhancedDynamicFieldProps) {
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchOpen, setSearchOpen] = useState(false)

  const performSearch = useCallback(async (query: string) => {
    if (!query || query.length < 2) return
    
    setIsSearching(true)
    try {
      let results: any[] = []
      
      switch (field.source) {
        case 'staff_by_department':
          const { data } = await supabase
            .from('profiles')
            .select('id, name, email')
            .or(`name.ilike.%${query}%,email.ilike.%${query}%`)
            .eq('department_id', departmentId)
            .limit(10)
          results = data || []
          break
        case 'group_mail_addresses':
          const { data: mailData } = await supabase
            .from('group_mail_addresses')
            .select('*')
            .ilike('email_address', `%${query}%`)
            .limit(10)
          results = mailData?.map(m => ({ id: m.id, name: m.email_address })) || []
          break
      }
      
      setSearchResults(results)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setIsSearching(false)
    }
  }, [field.source, departmentId])

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholderJp || field.placeholder}
            disabled={disabled}
            className={error ? 'border-red-500' : ''}
          />
        )
      
      case 'search':
        return (
          <Popover open={searchOpen} onOpenChange={setSearchOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-between">
                {value?.name || value || field.placeholderJp || "検索..."}
                <Search className="ml-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput 
                  placeholder="検索..."
                  onValueChange={performSearch}
                />
                <CommandEmpty>
                  {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : "結果なし"}
                </CommandEmpty>
                <CommandGroup>
                  {searchResults.map((result) => (
                    <CommandItem
                      key={result.id}
                      onSelect={() => {
                        onChange(result)
                        setSearchOpen(false)
                      }}
                    >
                      <Check className={cn("mr-2 h-4 w-4", value?.id === result.id ? "opacity-100" : "opacity-0")} />
                      {result.name}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        )
      
      case 'dropdown':
        return (
          <Select value={value || ''} onValueChange={onChange}>
            <SelectTrigger>
              <SelectValue placeholder={field.placeholderJp || "選択"} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.labelJp || option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      
      case 'textarea':
        return (
          <Textarea
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholderJp || field.placeholder}
            disabled={disabled}
            rows={3}
          />
        )
      
      default:
        return <Input value={value || ''} onChange={(e) => onChange(e.target.value)} />
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">
          {field.labelJp || field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {aiSuggestion && onAiAccept && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onAiAccept(field.id, aiSuggestion)}
            className="text-xs"
          >
            <Sparkles className="h-3 w-3 mr-1" />
            AI提案
          </Button>
        )}
      </div>
      {renderField()}
      {error && <div className="text-xs text-red-600">{error}</div>}
      {field.helpTextJp && <div className="text-xs text-gray-500">{field.helpTextJp}</div>}
    </div>
  )
}
