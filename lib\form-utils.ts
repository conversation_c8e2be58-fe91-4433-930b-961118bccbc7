import { FormField, FormSchema, FormSection } from '@/lib/form-types'

/**
 * Utility functions to normalize form schemas from different sources
 */

export function normalizeFormField(field: any): FormField {
  return {
    id: field.id || field.name || '',
    name: field.name || field.id || '',
    type: normalizeFieldType(field.type),
    label: field.label || field.name || '',
    labelJp: field.labelJp || field.label || '',
    required: field.required || false,
    validation: field.validation,
    defaultValue: field.defaultValue,
    options: normalizeFieldOptions(field.options),
    source: field.source,
    searchable: field.searchable,
    placeholder: field.placeholder,
    placeholderJp: field.placeholderJp,
    helpText: field.helpText,
    helpTextJp: field.helpTextJp
  }
}

export function normalizeFieldType(type: string): FormField['type'] {
  switch (type) {
    case 'select':
      return 'dropdown'
    case 'multi_select':
      return 'multiselect'
    default:
      return type as Form<PERSON>ield['type']
  }
}

export function normalizeFieldOptions(options: any): any {
  if (!options) return undefined
  
  if (Array.isArray(options)) {
    return options.map(opt => 
      typeof opt === 'string' 
        ? { value: opt, label: opt, labelJp: opt }
        : opt
    )
  }
  
  return options
}

export function normalizeFormSchema(schema: any): FormSchema {
  // If schema has direct fields (service category format)
  if (schema.fields && !schema.sections) {
    return {
      ...schema,
      sections: [
        {
          id: 'main',
          title: schema.name || 'Request Details',
          titleJp: schema.nameJp || 'リクエスト詳細',
          fields: schema.fields.map(normalizeFormField)
        }
      ]
    }
  }
  
  // If schema has sections (full form format)
  if (schema.sections) {
    return {
      ...schema,
      sections: schema.sections.map((section: any) => ({
        ...section,
        fields: section.fields.map(normalizeFormField)
      }))
    }
  }
  
  return schema
}

export function createFormSchemaFromServiceCategory(serviceCategory: any): FormSchema {
  return normalizeFormSchema({
    id: serviceCategory.id,
    name: serviceCategory.name_en,
    nameJp: serviceCategory.name_jp,
    description: serviceCategory.description,
    fields: serviceCategory.form_schema?.fields || [],
    aiEnabled: true,
    departmentSpecific: serviceCategory.is_department_specific
  })
}
