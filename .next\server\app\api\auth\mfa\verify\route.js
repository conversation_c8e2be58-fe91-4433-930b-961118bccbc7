(()=>{var e={};e.id=4193,e.ids=[4193],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},29021:e=>{"use strict";e.exports=require("fs")},27910:e=>{"use strict";e.exports=require("stream")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},37664:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(42706),i=r(28203),o=r(45994),n=r(33406),u=r(44512),c=r(39187),d=r(44053);async function p(e){try{let{sessionToken:t,code:r}=await e.json();if(!t||!r)return c.NextResponse.json({error:"Missing required fields"},{status:400});let s=(0,n.createRouteHandlerClient)({cookies:u.UL}),a=new d.q(s),i=await a.verifyCode(t,r);if(i.success){let e=c.NextResponse.json(i);return e.cookies.set("mfa_verified","true",{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:43200}),e}return c.NextResponse.json(i)}catch(e){return console.error("MFA verify error:",e),c.NextResponse.json({error:e.message||"Failed to verify MFA code"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/mfa/verify/route",pathname:"/api/auth/mfa/verify",filename:"route",bundlePath:"app/api/auth/mfa/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\verify\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:l,serverHooks:m}=f;function _(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:l})}},96487:()=>{},78335:()=>{},44053:(e,t,r)=>{"use strict";r.d(t,{q:()=>p});var s=r(46063),a=r(12959),i=r(55511);let o=(0,r(28354).promisify)(i.scrypt),n="aes-256-gcm",u=async()=>{let e=process.env.ENCRYPTION_KEY||process.env.SUPABASE_ANON_KEY;if(!e)throw Error("Encryption key not configured");let t=Buffer.from("itsync-mfa-salt-v1","utf8");return await o(e,t,32)};async function c(e){try{let t=await u(),r=(0,i.randomBytes)(16),s=(0,i.createCipheriv)(n,t,r),a=Buffer.concat([s.update(e,"utf8"),s.final()]),o=s.getAuthTag();return Buffer.concat([r,o,a]).toString("base64")}catch(e){throw Error(`Encryption failed: ${e.message}`)}}async function d(e){try{let t=await u(),r=Buffer.from(e,"base64"),s=r.slice(0,16),a=r.slice(16,32),o=r.slice(32),c=(0,i.createDecipheriv)(n,t,s);return c.setAuthTag(a),Buffer.concat([c.update(o),c.final()]).toString("utf8")}catch(e){throw Error(`Decryption failed: ${e.message}`)}}class p{constructor(e){this.MAX_ATTEMPTS=5,this.SESSION_DURATION=6e5,this.CODE_LENGTH=6,this.BACKUP_CODE_LENGTH=8,this.BACKUP_CODE_COUNT=10,this.supabase=e}async setupTOTP(e,t){try{let r=s.generateSecret({name:`ITSync (${t})`,issuer:"ITSync",length:32}),i=s.otpauthURL({secret:r.ascii,label:`ITSync:${t}`,issuer:"ITSync",algorithm:"sha256"}),o=await a.toDataURL(i),n=this.generateBackupCodes(),u=await c(r.base32),d=await Promise.all(n.map(e=>c(e)));return await this.supabase.from("mfa_configurations").upsert({user_id:e,method:"totp",secret_encrypted:u,backup_codes:d,is_verified:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),await this.logMFAEvent(e,"setup_initiated","totp",!0),{secret:r.base32,qrCode:o,backupCodes:n}}catch(t){throw await this.logMFAEvent(e,"setup_initiated","totp",!1,t.message),t}}async verifyTOTPSetup(e,t){try{let{data:r}=await this.supabase.from("mfa_configurations").select("*").eq("user_id",e).eq("method","totp").single();if(!r||!r.secret_encrypted)throw Error("TOTP configuration not found");let a=await d(r.secret_encrypted);if(s.totp.verify({secret:a,encoding:"base32",token:t,algorithm:"sha256",window:2}))return await this.supabase.from("mfa_configurations").update({is_verified:!0,is_primary:!0,verified_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",r.id),await this.supabase.from("profiles").update({mfa_enabled:!0,updated_at:new Date().toISOString()}).eq("auth_id",e),await this.logMFAEvent(e,"setup_completed","totp",!0),!0;return await this.logMFAEvent(e,"setup_failed","totp",!1,"Invalid code"),!1}catch(t){throw await this.logMFAEvent(e,"setup_failed","totp",!1,t.message),t}}async createChallenge(e){try{let{data:t}=await this.supabase.from("mfa_configurations").select("method, is_primary").eq("user_id",e).eq("is_verified",!0);if(!t||0===t.length)throw Error("No MFA methods configured");let r=this.generateSessionToken(),s=new Date(Date.now()+this.SESSION_DURATION);return await this.supabase.from("mfa_sessions").insert({user_id:e,session_token:r,method:t.find(e=>e.is_primary)?.method||t[0].method,expires_at:s.toISOString(),attempts:0,is_verified:!1}),{sessionToken:r,methods:t.map(e=>({method:e.method,isPrimary:e.is_primary}))}}catch(e){throw e}}async verifyCode(e,t){try{let{data:r}=await this.supabase.from("mfa_sessions").select("*").eq("session_token",e).single();if(!r)throw Error("Invalid session");if(new Date(r.expires_at)<new Date)throw Error("Session expired");if(r.attempts>=this.MAX_ATTEMPTS)return await this.logMFAEvent(r.user_id,"verify_failed",r.method,!1,"Locked out"),{success:!1,lockedOut:!0};let s=!1;if("totp"===r.method?s=await this.verifyTOTP(r.user_id,t):("sms"===r.method||"email"===r.method)&&(s=await this.verifyOTP(r,t)),s)return await this.supabase.from("mfa_sessions").update({is_verified:!0}).eq("id",r.id),await this.supabase.from("mfa_configurations").update({last_used_at:new Date().toISOString()}).eq("user_id",r.user_id).eq("method",r.method),await this.logMFAEvent(r.user_id,"verify_success",r.method,!0),{success:!0};{let e=r.attempts+1;return await this.supabase.from("mfa_sessions").update({attempts:e}).eq("id",r.id),await this.logMFAEvent(r.user_id,"verify_failed",r.method,!1),{success:!1,attemptsRemaining:this.MAX_ATTEMPTS-e}}}catch(e){throw e}}async verifyTOTP(e,t){let{data:r}=await this.supabase.from("mfa_configurations").select("secret_encrypted").eq("user_id",e).eq("method","totp").eq("is_verified",!0).single();if(!r||!r.secret_encrypted)return!1;let a=await d(r.secret_encrypted);return s.totp.verify({secret:a,encoding:"base32",token:t,algorithm:"sha256",window:2})}async verifyOTP(e,t){return!!e.challenge_code&&await d(e.challenge_code)===t}generateBackupCodes(){let e=[];for(let t=0;t<this.BACKUP_CODE_COUNT;t++)e.push(this.generateRandomCode(this.BACKUP_CODE_LENGTH));return e}generateRandomCode(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="";for(let s=0;s<e;s++)r+=t.charAt(Math.floor(Math.random()*t.length));return r}generateSessionToken(){return`mfa_${Date.now()}_${this.generateRandomCode(32)}`}async logMFAEvent(e,t,r,s,a,i){try{await this.supabase.from("mfa_audit_logs").insert({user_id:e,event_type:t,method:r,success:s,error_message:a,metadata:i,created_at:new Date().toISOString()})}catch(e){console.error("Failed to log MFA event:",e)}}async checkMFAStatus(e){let{data:t}=await this.supabase.from("profiles").select("mfa_enabled, require_mfa_for_role").eq("auth_id",e).single(),{data:r}=await this.supabase.from("mfa_configurations").select("method").eq("user_id",e).eq("is_verified",!0);return{enabled:t?.mfa_enabled||!1,required:t?.require_mfa_for_role||!1,methods:r?.map(e=>e.method)||[]}}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,5452,4512,3406,1073],()=>r(37664));module.exports=s})();