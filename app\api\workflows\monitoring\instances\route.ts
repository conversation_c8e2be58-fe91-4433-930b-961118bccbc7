import { NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'all'
    const department = searchParams.get('department') || 'all'

    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    // Build query
    let query = supabase
      .from('workflow_instances')
      .select(`
        id,
        workflow_definition_id,
        current_state,
        status,
        priority,
        context,
        created_at,
        updated_at,
        completed_at,
        request_forms!workflow_instances_context_fkey (
          id,
          title,
          requester_id,
          staff!request_forms_requester_id_fkey (
            id,
            name_en,
            name_jp,
            divisions!staff_division_id_fkey (
              id,
              name_en,
              name_jp
            )
          ),
          service_categories!request_forms_service_category_id_fkey (
            id,
            name_en,
            name_jp
          )
        ),
        workflow_tasks (
          id,
          task_type,
          status,
          assigned_to
        ),
        sla_tracking (
          id,
          sla_status,
          target_date
        )
      `)
      .order('created_at', { ascending: false })
      .limit(100)

    // Apply status filter
    if (status !== 'all') {
      query = query.eq('status', status)
    }

    // Apply department filter
    if (department !== 'all') {
      query = query.eq('request_forms.staff.divisions.name_en', department)
    }

    const { data, error } = await query

    if (error) {
      throw error
    }

    // Transform data to match the expected format
    const workflows = data?.map(workflow => {
      const requestForm = workflow.request_forms?.[0]
      const staff = requestForm?.staff?.[0]
      const division = staff?.divisions?.[0]
      const category = requestForm?.service_categories?.[0]
      const slaTracking = workflow.sla_tracking?.[0]
      const tasks = workflow.workflow_tasks || []

      const completedTasks = tasks.filter(t => t.status === 'completed').length
      const totalTasks = tasks.length

      return {
        id: workflow.id,
        title: requestForm?.title || category?.name_en || 'Untitled',
        titleJp: category?.name_jp || requestForm?.title || '無題',
        status: workflow.status,
        priority: workflow.priority || 'medium',
        createdAt: workflow.created_at,
        updatedAt: workflow.updated_at,
        completedAt: workflow.completed_at,
        currentStep: completedTasks,
        totalSteps: totalTasks || 1,
        slaStatus: slaTracking?.sla_status || 'on_track',
        assignedTo: tasks.find(t => t.status === 'in_progress')?.assigned_to,
        department: division?.name_en || 'Unknown',
        category: category?.name_en || 'Unknown'
      }
    }) || []

    return NextResponse.json(workflows)
  } catch (error) {
    console.error('Error fetching workflow instances:', error)
    return NextResponse.json(
      { error: 'Failed to fetch workflow instances' },
      { status: 500 }
    )
  }
}
