import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Session timeout configuration (in milliseconds)
const SESSION_TIMEOUTS = {
  ADMIN: 2 * 60 * 60 * 1000, // 2 hours for admin roles
  REGULAR: 24 * 60 * 60 * 1000, // 24 hours for regular users
};

const ADMIN_ROLES = ['Global Administrator', 'Web App System Administrator', 'Department Administrator'];

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  
  // Check if Supabase is properly configured
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  
  if (!supabaseUrl || !supabaseKey || supabaseUrl === 'your_supabase_project_url') {
    // If Supabase is not configured, show error page
    if (req.nextUrl.pathname !== '/setup-required') {
      return NextResponse.redirect(new URL('/setup-required', req.url))
    }
    return res
  }

  const supabase = createMiddlewareClient({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Public routes that don't require authentication
  const publicRoutes = ['/login', '/mfa-verify', '/api/auth/mfa/verify', '/api/auth/mfa/challenge', '/setup-required']
  const isPublicRoute = publicRoutes.some(route => req.nextUrl.pathname.startsWith(route))

  // If no session and trying to access protected routes, redirect to login
  if (!session && !isPublicRoute) {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  // If session exists
  if (session) {
    // Check session timeout based on role
    const { data: userRole } = await supabase
      .from('staff')
      .select('roles!inner(name)')
      .eq('auth_id', session.user.id)
      .single();

    const isAdmin = userRole?.roles?.name && ADMIN_ROLES.includes(userRole.roles.name);
    const maxSessionAge = isAdmin ? SESSION_TIMEOUTS.ADMIN : SESSION_TIMEOUTS.REGULAR;
    
    // Check if session is expired
    const sessionAge = Date.now() - new Date(session.created_at).getTime();
    if (sessionAge > maxSessionAge) {
      // Log session timeout
      await supabase.from('audit_logs').insert({
        action: 'SESSION_TIMEOUT',
        action_details: { 
          role: userRole?.roles?.name,
          session_age_minutes: Math.floor(sessionAge / 60000)
        },
        user_id: session.user.id,
      });

      // Sign out user
      await supabase.auth.signOut();
      return NextResponse.redirect(new URL('/login?expired=true', req.url));
    }

    // Check if MFA is required but not verified
    const mfaSession = req.cookies.get('mfa_session')
    const mfaVerified = req.cookies.get('mfa_verified')
    
    // Get user's MFA status from database
    const { data: profile } = await supabase
      .from('profiles')
      .select('mfa_enabled, require_mfa_for_role')
      .eq('auth_id', session.user.id)
      .single()

    const requiresMFA = profile?.mfa_enabled || profile?.require_mfa_for_role
    
    // If MFA is required but not verified, and not on MFA verification page
    if (requiresMFA && !mfaVerified && !req.nextUrl.pathname.startsWith('/mfa-verify')) {
      // Store the original URL they were trying to access
      const redirectUrl = new URL('/mfa-verify', req.url)
      redirectUrl.searchParams.set('redirect', req.nextUrl.pathname)
      return NextResponse.redirect(redirectUrl)
    }

    // If trying to access login while authenticated, redirect to dashboard
    if (req.nextUrl.pathname === '/login') {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }
  }

  // Enhanced Security headers
  res.headers.set('X-Content-Type-Options', 'nosniff')
  res.headers.set('X-Frame-Options', 'DENY')
  res.headers.set('X-XSS-Protection', '1; mode=block')
  res.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  res.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  res.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none';"
  )
  res.headers.set(
    'Permissions-Policy',
    'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), accelerometer=(), gyroscope=()'
  )

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
