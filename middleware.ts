import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Session timeout configuration (in milliseconds)
const SESSION_TIMEOUTS = {
  ADMIN: 2 * 60 * 60 * 1000, // 2 hours for admin roles
  REGULAR: 24 * 60 * 60 * 1000, // 24 hours for regular users
};

const ADMIN_ROLES = ['Global Administrator', 'Web App System Administrator', 'Department Administrator'];

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  
  // Check if Supabase is properly configured
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  
  if (!supabaseUrl || !supabaseKey || 
      supabaseUrl === 'your_supabase_project_url' || 
      supabaseKey === 'your_supabase_anon_key' ||
      supabaseUrl.includes('your_') || 
      supabaseKey.includes('your_')) {
    // Documentation pages and sample API endpoints don't require Supabase configuration
    const allowedRoutes = ['/docs', '/auth-docs', '/api/docs', '/api/users']
    const isAllowedRoute = allowedRoutes.some(route => req.nextUrl.pathname.startsWith(route))
    
    if (isAllowedRoute) {
      console.log('[AUTH_MIDDLEWARE_INFO] Allowing access without Supabase configuration. Path:', req.nextUrl.pathname);
      return res
    }
    
    // If Supabase is not configured, show error page
    if (req.nextUrl.pathname !== '/setup-required') {
      console.error('[AUTH_MIDDLEWARE_ERROR] Supabase URL or Key not configured. Redirecting to /setup-required. Path:', req.nextUrl.pathname);
      return NextResponse.redirect(new URL('/setup-required', req.url))
    }
    console.warn('[AUTH_MIDDLEWARE_INFO] Supabase not configured, but already on /setup-required. Path:', req.nextUrl.pathname);
    return res
  }

  const supabase = createMiddlewareClient({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Public routes that don't require authentication
  const publicRoutes = [
    '/login',
    '/mfa-verify',
    '/api/auth/mfa/verify',
    '/api/auth/mfa/challenge',
    '/setup-required',
    '/docs',
    '/auth-docs',
    '/api/docs',
    '/api/users'
  ]
  const isPublicRoute = publicRoutes.some(route => req.nextUrl.pathname.startsWith(route))

  // If no session and trying to access protected routes, redirect to login
  if (!session && !isPublicRoute) {
    console.log(`[AUTH_MIDDLEWARE_INFO] No session found for protected route: ${req.nextUrl.pathname}. Redirecting to /login.`);
    return NextResponse.redirect(new URL('/login', req.url))
  }
  
  if (session) {
    console.log(`[AUTH_MIDDLEWARE_INFO] Session found for user: ${session.user.id}. Path: ${req.nextUrl.pathname}`);
    // Check session timeout based on role
    let userRoleData = null;
    let userRoleError = null;
    try {
      const { data, error } = await supabase
        .from('staff')
        .select('roles!inner(name)')
        .eq('auth_id', session.user.id)
        .single();
      userRoleData = data;
      userRoleError = error;
    } catch (e) {
      userRoleError = e;
    }

    if (userRoleError) {
      console.error(`[AUTH_MIDDLEWARE_ERROR] Error fetching user role for session timeout check. User ID: ${session.user.id}. Error:`, userRoleError);
      // Potentially sign out or redirect to error page if role is critical
    }
    
    const roleName = userRoleData?.roles && Array.isArray(userRoleData.roles) && userRoleData.roles.length > 0
      ? userRoleData.roles[0]?.name
      : (userRoleData?.roles as any)?.name; // Fallback for non-array or direct object case, though TS error suggests array.
    const isAdmin = roleName && ADMIN_ROLES.includes(roleName);
    const maxSessionAge = isAdmin ? SESSION_TIMEOUTS.ADMIN : SESSION_TIMEOUTS.REGULAR;
    console.log(`[AUTH_MIDDLEWARE_SESSION] Timeout check for User ID: ${session.user.id}. Role: ${roleName || 'N/A'}. IsAdmin: ${isAdmin}. MaxAge: ${maxSessionAge}ms.`);
    
    // Check if session is expired
    let sessionAge = Infinity;
    // Try to use session.user.created_at as it's a common field.
    // The original code had session.created_at, which might also be an option depending on the exact Session type.
    const sessionUserCreatedAt = session.user?.created_at;

    if (sessionUserCreatedAt) {
      sessionAge = Date.now() - new Date(sessionUserCreatedAt).getTime();
      console.log(`[AUTH_MIDDLEWARE_SESSION] User ID: ${session.user.id}. Current session age: ${sessionAge}ms (using session.user.created_at).`);
    } else {
      // If session.user.created_at is not available, log a warning.
      // The session object itself might have a 'created_at' or similar property.
      // For now, we'll log that we couldn't determine the age accurately.
      console.warn(`[AUTH_MIDDLEWARE_SESSION_TIMEOUT] Could not determine session start time (session.user.created_at is undefined) for User ID: ${session.user.id}. Session object:`, JSON.stringify(session, null, 2));
    }
    
    if (sessionAge > maxSessionAge) {
      console.warn(`[AUTH_MIDDLEWARE_SESSION_TIMEOUT] Session timed out for User ID: ${session.user.id}. Role: ${roleName || 'N/A'}. Session Age: ${sessionAge}ms, Max Age: ${maxSessionAge}ms.`);
      // Log session timeout
      await supabase.from('audit_logs').insert({
        action: 'SESSION_TIMEOUT',
        action_details: {
          role: roleName || 'N/A', // Use the derived roleName
          session_age_minutes: Math.floor(sessionAge / 60000)
        },
        user_id: session.user.id,
      });

      // Sign out user
      await supabase.auth.signOut();
      return NextResponse.redirect(new URL('/login?expired=true', req.url));
    }

    // Check if MFA is required but not verified
    const mfaSession = req.cookies.get('mfa_session')
    const mfaVerified = req.cookies.get('mfa_verified')
    
    // Get user's MFA status from database
    let profileData = null;
    let profileError = null;
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('mfa_enabled, require_mfa_for_role')
        .eq('auth_id', session.user.id)
        .single();
      profileData = data;
      profileError = error;
    } catch (e) {
      profileError = e;
    }

    if (profileError) {
      console.error(`[AUTH_MIDDLEWARE_ERROR] Error fetching profile for MFA check. User ID: ${session.user.id}. Error:`, profileError);
      // Potentially sign out or redirect to error page if profile is critical for MFA
    }

    const requiresMFA = profileData?.mfa_enabled || profileData?.require_mfa_for_role;
    console.log(`[AUTH_MIDDLEWARE_MFA] MFA Check for User ID: ${session.user.id}. Path: ${req.nextUrl.pathname}. RequiresMFA: ${requiresMFA}. MFA_Verified_Cookie: ${!!mfaVerified}.`);
    
    // If MFA is required but not verified, and not on MFA verification page
    if (requiresMFA && !mfaVerified && !req.nextUrl.pathname.startsWith('/mfa-verify')) {
      console.log(`[AUTH_MIDDLEWARE_MFA_REDIRECT] MFA required but not verified for User ID: ${session.user.id}. Redirecting to /mfa-verify. Original path: ${req.nextUrl.pathname}`);
      // Store the original URL they were trying to access
      const redirectUrl = new URL('/mfa-verify', req.url)
      redirectUrl.searchParams.set('redirect', req.nextUrl.pathname)
      return NextResponse.redirect(redirectUrl)
    }

    // If trying to access login while authenticated, redirect to dashboard
    if (req.nextUrl.pathname === '/login') {
      console.log(`[AUTH_MIDDLEWARE_INFO] Authenticated user ${session.user.id} attempting to access /login. Redirecting to /dashboard.`);
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }
  }

  // Enhanced Security headers
  res.headers.set('X-Content-Type-Options', 'nosniff')
  res.headers.set('X-Frame-Options', 'DENY')
  res.headers.set('X-XSS-Protection', '1; mode=block')
  res.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  res.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  res.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none';"
  )
  res.headers.set(
    'Permissions-Policy',
    'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), accelerometer=(), gyroscope=()'
  )

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
