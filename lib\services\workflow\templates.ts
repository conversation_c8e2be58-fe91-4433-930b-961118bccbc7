// Workflow Templates for Common IT Scenarios

export const workflowTemplates = {
  // Password Reset Workflow
  passwordReset: {
    id: 'password-reset-workflow',
    name: 'Password Reset Workflow',
    version: 1,
    triggers: {
      service_category: 'PWR',
    },
    states: {
      initial: {
        type: 'start',
        transitions: [
          {
            to: 'check_user_type',
          },
        ],
      },
      check_user_type: {
        type: 'automatic',
        name: 'Check User Type',
        transitions: [
          {
            to: 'manager_approval',
            condition: 'request_user_role === "Global Administrator" || request_user_role === "Web App System Administrator"',
          },
          {
            to: 'auto_approve',
            condition: 'request_user_role !== "Global Administrator" && request_user_role !== "Web App System Administrator"',
          },
        ],
      },
      manager_approval: {
        type: 'approval',
        name: 'Manager Approval Required',
        assignee: {
          role: 'manager',
        },
        sla_minutes: 60,
        escalation: {
          levels: [
            {
              minutes: 30,
              assignee: { role: 'Department Administrator' },
            },
            {
              minutes: 60,
              assignee: { role: 'Web App System Administrator' },
            },
          ],
        },
        transitions: [
          {
            to: 'execute_reset',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      auto_approve: {
        type: 'automatic',
        name: 'Auto Approved',
        transitions: [
          {
            to: 'execute_reset',
          },
        ],
      },
      execute_reset: {
        type: 'task',
        name: 'Execute Password Reset',
        handler: 'passwordResetHandler',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 30,
        transitions: [
          {
            to: 'notify_user',
          },
        ],
      },
      notify_user: {
        type: 'automatic',
        name: 'Notify User',
        actions: [
          {
            type: 'notify',
            parameters: {
              template: 'password_reset_complete',
              recipient: '{{request.user.email}}',
            },
          },
        ],
        transitions: [
          {
            to: 'completed',
          },
        ],
      },
      completed: {
        type: 'end',
        name: 'Completed',
      },
      rejected: {
        type: 'end',
        name: 'Rejected',
      },
    },
  },

  // Group Mail Access Workflow
  groupMailAccess: {
    id: 'group-mail-access-workflow',
    name: 'Group Mail Access Workflow',
    version: 1,
    triggers: {
      service_category: 'GM',
    },
    states: {
      initial: {
        type: 'start',
        transitions: [
          {
            to: 'department_approval',
          },
        ],
      },
      department_approval: {
        type: 'approval',
        name: 'Department Head Approval',
        assignee: {
          role: 'Department Administrator',
          department: '{{request.user.department}}',
        },
        sla_minutes: 240, // 4 hours
        transitions: [
          {
            to: 'it_review',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      it_review: {
        type: 'task',
        name: 'IT Security Review',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 120, // 2 hours
        transitions: [
          {
            to: 'implement_access',
            condition: 'security_check_passed === true',
          },
          {
            to: 'security_concern',
            condition: 'security_check_passed === false',
          },
        ],
      },
      security_concern: {
        type: 'approval',
        name: 'Security Exception Approval',
        assignee: {
          role: 'Web App System Administrator',
        },
        sla_minutes: 480, // 8 hours
        transitions: [
          {
            to: 'implement_access',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      implement_access: {
        type: 'task',
        name: 'Implement Group Mail Access',
        handler: 'groupMailAccessHandler',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 60,
        transitions: [
          {
            to: 'verify_access',
          },
        ],
      },
      verify_access: {
        type: 'task',
        name: 'Verify Access Working',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 30,
        automatic: true,
        handler: 'verifyGroupMailAccess',
        transitions: [
          {
            to: 'completed',
          },
        ],
      },
      completed: {
        type: 'end',
        name: 'Completed',
      },
      rejected: {
        type: 'end',
        name: 'Rejected',
      },
    },
  },

  // PC Admin Rights Workflow
  pcAdminRights: {
    id: 'pc-admin-rights-workflow',
    name: 'PC Administrative Rights Workflow',
    version: 1,
    triggers: {
      service_category: 'PCA',
    },
    states: {
      initial: {
        type: 'start',
        transitions: [
          {
            to: 'validate_request',
          },
        ],
      },
      validate_request: {
        type: 'task',
        name: 'Validate PC and User',
        handler: 'validatePCAdminRequest',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 30,
        transitions: [
          {
            to: 'manager_approval',
            condition: 'validation_passed === true',
          },
          {
            to: 'invalid_request',
            condition: 'validation_passed === false',
          },
        ],
      },
      manager_approval: {
        type: 'approval',
        name: 'Manager Approval',
        assignee: {
          role: 'manager',
        },
        sla_minutes: 240, // 4 hours
        escalation: {
          levels: [
            {
              minutes: 120,
              assignee: { role: 'Department Administrator' },
            },
          ],
        },
        transitions: [
          {
            to: 'security_review',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      security_review: {
        type: 'approval',
        name: 'IT Security Review',
        assignee: {
          role: 'Web App System Administrator',
        },
        sla_minutes: 480, // 8 hours
        transitions: [
          {
            to: 'grant_rights',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      grant_rights: {
        type: 'task',
        name: 'Grant Administrative Rights',
        handler: 'grantPCAdminRights',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 60,
        transitions: [
          {
            to: 'schedule_review',
          },
        ],
      },
      schedule_review: {
        type: 'automatic',
        name: 'Schedule Periodic Review',
        actions: [
          {
            type: 'schedule_task',
            parameters: {
              task: 'review_admin_rights',
              delay_days: 90,
            },
          },
        ],
        transitions: [
          {
            to: 'completed',
          },
        ],
      },
      completed: {
        type: 'end',
        name: 'Completed',
      },
      rejected: {
        type: 'end',
        name: 'Rejected',
      },
      invalid_request: {
        type: 'end',
        name: 'Invalid Request',
      },
    },
  },

  // SharePoint Access Workflow
  sharepointAccess: {
    id: 'sharepoint-access-workflow',
    name: 'SharePoint Library Access Workflow',
    version: 1,
    triggers: {
      service_category: 'SPO',
    },
    states: {
      initial: {
        type: 'start',
        transitions: [
          {
            to: 'check_sensitivity',
          },
        ],
      },
      check_sensitivity: {
        type: 'automatic',
        name: 'Check Library Sensitivity',
        handler: 'checkLibrarySensitivity',
        transitions: [
          {
            to: 'auto_approve',
            condition: 'library_sensitivity === "low"',
          },
          {
            to: 'manager_approval',
            condition: 'library_sensitivity === "medium"',
          },
          {
            to: 'executive_approval',
            condition: 'library_sensitivity === "high"',
          },
        ],
      },
      auto_approve: {
        type: 'automatic',
        name: 'Auto Approved',
        transitions: [
          {
            to: 'grant_access',
          },
        ],
      },
      manager_approval: {
        type: 'approval',
        name: 'Manager Approval',
        assignee: {
          role: 'Department Administrator',
          department: '{{request.user.department}}',
        },
        sla_minutes: 240, // 4 hours
        transitions: [
          {
            to: 'grant_access',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      executive_approval: {
        type: 'approval',
        name: 'Executive Approval Required',
        assignee: {
          role: 'Global Administrator',
        },
        sla_minutes: 1440, // 24 hours
        transitions: [
          {
            to: 'grant_access',
            condition: 'approved === true',
          },
          {
            to: 'rejected',
            condition: 'approved === false',
          },
        ],
      },
      grant_access: {
        type: 'task',
        name: 'Grant SharePoint Access',
        handler: 'grantSharePointAccess',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 120, // 2 hours
        transitions: [
          {
            to: 'notify_user',
          },
        ],
      },
      notify_user: {
        type: 'automatic',
        name: 'Notify User',
        actions: [
          {
            type: 'notify',
            parameters: {
              template: 'sharepoint_access_granted',
              recipient: '{{request.user.email}}',
            },
          },
        ],
        transitions: [
          {
            to: 'completed',
          },
        ],
      },
      completed: {
        type: 'end',
        name: 'Completed',
      },
      rejected: {
        type: 'end',
        name: 'Rejected',
      },
    },
  },

  // Onboarding Workflow
  employeeOnboarding: {
    id: 'employee-onboarding-workflow',
    name: 'Employee Onboarding Workflow',
    version: 1,
    triggers: {
      request_type: 'onboarding',
    },
    states: {
      initial: {
        type: 'start',
        transitions: [
          {
            to: 'hr_validation',
          },
        ],
      },
      hr_validation: {
        type: 'task',
        name: 'HR Validation',
        assignee: {
          role: 'Human Resources',
        },
        sla_minutes: 480, // 8 hours
        transitions: [
          {
            to: 'parallel_setup',
            condition: 'validated === true',
          },
          {
            to: 'rejected',
            condition: 'validated === false',
          },
        ],
      },
      parallel_setup: {
        type: 'parallel',
        name: 'Parallel Account Setup',
        branches: [
          {
            id: 'email_setup',
            states: {
              create_email: {
                type: 'task',
                name: 'Create Email Account',
                handler: 'createEmailAccount',
                assignee: { role: 'IT Helpdesk Support' },
                sla_minutes: 60,
              },
            },
          },
          {
            id: 'pc_setup',
            states: {
              assign_pc: {
                type: 'task',
                name: 'Assign PC',
                handler: 'assignPC',
                assignee: { role: 'IT Helpdesk Support' },
                sla_minutes: 120,
              },
            },
          },
          {
            id: 'access_setup',
            states: {
              setup_access: {
                type: 'task',
                name: 'Setup Basic Access',
                handler: 'setupBasicAccess',
                assignee: { role: 'IT Helpdesk Support' },
                sla_minutes: 60,
              },
            },
          },
        ],
        transitions: [
          {
            to: 'final_review',
          },
        ],
      },
      final_review: {
        type: 'task',
        name: 'Final Setup Review',
        assignee: {
          role: 'IT Helpdesk Support',
        },
        sla_minutes: 30,
        transitions: [
          {
            to: 'completed',
          },
        ],
      },
      completed: {
        type: 'end',
        name: 'Onboarding Complete',
      },
      rejected: {
        type: 'end',
        name: 'Onboarding Rejected',
      },
    },
  },
};

// Export function to get template by service category
export function getWorkflowTemplate(serviceCategory: string): any {
  switch (serviceCategory) {
    case 'PWR':
      return workflowTemplates.passwordReset;
    case 'GM':
      return workflowTemplates.groupMailAccess;
    case 'PCA':
      return workflowTemplates.pcAdminRights;
    case 'SPO':
      return workflowTemplates.sharepointAccess;
    default:
      return null;
  }
}

// Export function to list all templates
export function listWorkflowTemplates(): Array<{ id: string; name: string; category: string }> {
  return [
    { id: 'password-reset-workflow', name: 'Password Reset Workflow', category: 'PWR' },
    { id: 'group-mail-access-workflow', name: 'Group Mail Access Workflow', category: 'GM' },
    { id: 'pc-admin-rights-workflow', name: 'PC Administrative Rights Workflow', category: 'PCA' },
    { id: 'sharepoint-access-workflow', name: 'SharePoint Library Access Workflow', category: 'SPO' },
    { id: 'employee-onboarding-workflow', name: 'Employee Onboarding Workflow', category: 'onboarding' },
  ];
}
