"use strict";(()=>{var e={};e.id=9259,e.ids=[9259],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},48140:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(55977);async function p(e){try{let e=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let s=await u.i.getAtRiskItems();return i.NextResponse.json(s)}catch(e){return console.error("Error fetching at-risk items:",e),i.NextResponse.json({error:"Failed to fetch at-risk items"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/sla/at-risk/route",pathname:"/api/workflows/sla/at-risk",filename:"route",bundlePath:"app/api/workflows/sla/at-risk/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\at-risk\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:x}=d;function k(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(48140));module.exports=s})();