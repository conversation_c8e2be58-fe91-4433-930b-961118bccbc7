"use strict";(()=>{var e={};e.id=9259,e.ids=[9259],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},5482:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>k,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>d});var a=t(42706),o=t(28203),n=t(45994),i=t(39187),p=t(61487),u=t(92537);async function d(e){try{let e=(0,p.U)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return i.NextResponse.json({error:"Unauthorized"},{status:401});let s=await u.i.getAtRiskItems();return i.NextResponse.json(s)}catch(e){return console.error("Error fetching at-risk items:",e),i.NextResponse.json({error:"Failed to fetch at-risk items"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/sla/at-risk/route",pathname:"/api/workflows/sla/at-risk",filename:"route",bundlePath:"app/api/workflows/sla/at-risk/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\at-risk\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:k}=l;function w(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064,3744,9389],()=>t(5482));module.exports=s})();