'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { useEmbeddings } from '@/lib/hooks/use-embeddings'
import { useLanguage } from '@/lib/LanguageContext'
import { ArrowRight, Sparkles } from 'lucide-react'
import Link from 'next/link'

interface RelatedArticle {
  article_id: string
  title: string
  summary: string
  similarity: number
}

interface RelatedArticlesProps {
  articleId: string
  limit?: number
}

export function RelatedArticles({ articleId, limit = 5 }: RelatedArticlesProps) {
  const [relatedArticles, setRelatedArticles] = useState<RelatedArticle[]>([])
  const { language } = useLanguage()
  const { loading, findRelatedArticles } = useEmbeddings()

  useEffect(() => {
    const loadRelatedArticles = async () => {
      try {
        const articles = await findRelatedArticles(
          articleId, 
          language as 'en' | 'jp', 
          limit
        )
        setRelatedArticles(articles)
      } catch (error) {
        console.error('Error loading related articles:', error)
      }
    }

    if (articleId) {
      loadRelatedArticles()
    }
  }, [articleId, language, limit, findRelatedArticles])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            {language === 'en' ? 'Related Articles' : '関連記事'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  if (relatedArticles.length === 0) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          {language === 'en' ? 'Related Articles' : '関連記事'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {relatedArticles.map((article) => (
          <Link
            key={article.article_id}
            href={`/knowledge-base/article/${article.article_id}`}
            className="group block space-y-1 rounded-lg p-3 transition-colors hover:bg-accent"
          >
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1 space-y-1">
                <h4 className="text-sm font-medium line-clamp-1 group-hover:text-primary">
                  {article.title}
                </h4>
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {article.summary}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {(article.similarity * 100).toFixed(0)}%
                </Badge>
                <ArrowRight className="h-4 w-4 text-muted-foreground transition-transform group-hover:translate-x-1" />
              </div>
            </div>
          </Link>
        ))}
      </CardContent>
    </Card>
  )
}