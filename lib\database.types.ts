export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      divisions: {
        Row: {
          id: string
          name_jp: string
          name_en: string
          code: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name_jp: string
          name_en: string
          code?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name_jp?: string
          name_en?: string
          code?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      groups: {
        Row: {
          id: string
          division_id: string
          name_jp: string
          name_en: string
          code: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          division_id: string
          name_jp: string
          name_en: string
          code?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          division_id?: string
          name_jp?: string
          name_en?: string
          code?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      unions: {
        Row: {
          id: string
          name_jp: string
          name_en: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name_jp: string
          name_en: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name_jp?: string
          name_en?: string
          created_at?: string
          updated_at?: string
        }
      }
      roles: {
        Row: {
          id: string
          name: string
          description: string | null
          permissions: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          permissions?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          permissions?: Json
          created_at?: string
          updated_at?: string
        }
      }
      staff: {
        Row: {
          id: string
          auth_id: string | null
          staff_id: string
          name_jp: string
          name_en: string
          name_kana: string | null
          email: string
          pc_login_id: string | null
          division_id: string | null
          group_id: string | null
          role_id: string | null
          position: string | null
          gender: string | null
          birth_date: string | null
          employment_type: string | null
          union_id: string | null
          is_active: boolean
          pc_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          auth_id?: string | null
          staff_id: string
          name_jp: string
          name_en: string
          name_kana?: string | null
          email: string
          pc_login_id?: string | null
          division_id?: string | null
          group_id?: string | null
          role_id?: string | null
          position?: string | null
          gender?: string | null
          birth_date?: string | null
          employment_type?: string | null
          union_id?: string | null
          is_active?: boolean
          pc_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          auth_id?: string | null
          staff_id?: string
          name_jp?: string
          name_en?: string
          name_kana?: string | null
          email?: string
          pc_login_id?: string | null
          division_id?: string | null
          group_id?: string | null
          role_id?: string | null
          position?: string | null
          gender?: string | null
          birth_date?: string | null
          employment_type?: string | null
          union_id?: string | null
          is_active?: boolean
          pc_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      service_categories: {
        Row: {
          id: string
          name_jp: string
          name_en: string
          code: string | null
          description: string | null
          is_department_specific: boolean
          form_schema: Json | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name_jp: string
          name_en: string
          code?: string | null
          description?: string | null
          is_department_specific?: boolean
          form_schema?: Json | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name_jp?: string
          name_en?: string
          code?: string | null
          description?: string | null
          is_department_specific?: boolean
          form_schema?: Json | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      request_forms: {
        Row: {
          id: string
          requester_id: string | null
          status: string
          title: string
          description: string | null
          priority: string
          created_at: string
          updated_at: string
          submitted_at: string | null
          completed_at: string | null
        }
        Insert: {
          id?: string
          requester_id?: string | null
          status?: string
          title: string
          description?: string | null
          priority?: string
          created_at?: string
          updated_at?: string
          submitted_at?: string | null
          completed_at?: string | null
        }
        Update: {
          id?: string
          requester_id?: string | null
          status?: string
          title?: string
          description?: string | null
          priority?: string
          created_at?: string
          updated_at?: string
          submitted_at?: string | null
          completed_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
