/**
 * Secrets Management Service
 * 
 * This service provides secure management of application secrets with
 * automated rotation capabilities and graceful fallback mechanisms.
 */

import { ErrorService, ErrorSeverity, ErrorCategory, ErrorContext } from './error-service';
import crypto from 'crypto';

/**
 * Secret interface representing a stored secret
 */
export interface Secret {
  id: string;
  name: string;
  value: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * Secret rotation options
 */
export interface RotationOptions {
  /** Duration in milliseconds before secret expires (default: 90 days) */
  expiryDuration?: number;
  
  /** Duration in milliseconds for overlap period (default: 24 hours) */
  overlapDuration?: number;
  
  /** Whether to keep old secrets for fallback (default: true) */
  keepOldSecrets?: boolean;
  
  /** Maximum number of old secrets to keep (default: 2) */
  maxOldSecrets?: number;
  
  /** Custom metadata to attach to the secret */
  metadata?: Record<string, any>;
}

/**
 * Default rotation options
 */
const DEFAULT_ROTATION_OPTIONS: Required<RotationOptions> = {
  expiryDuration: 90 * 24 * 60 * 60 * 1000, // 90 days
  overlapDuration: 24 * 60 * 60 * 1000, // 24 hours
  keepOldSecrets: true,
  maxOldSecrets: 2,
  metadata: {}
};

/**
 * Secret storage provider interface
 */
export interface SecretStorageProvider {
  getSecret(name: string): Promise<Secret | null>;
  getSecrets(name: string): Promise<Secret[]>;
  saveSecret(secret: Secret): Promise<void>;
  deleteSecret(id: string): Promise<void>;
  listSecretNames(): Promise<string[]>;
}

/**
 * In-memory secret storage provider (for development/testing)
 */
export class InMemorySecretStorage implements SecretStorageProvider {
  private secrets: Map<string, Secret[]> = new Map();
  
  async getSecret(name: string): Promise<Secret | null> {
    const secrets = this.secrets.get(name) || [];
    return secrets.find(s => s.isActive) || null;
  }
  
  async getSecrets(name: string): Promise<Secret[]> {
    return this.secrets.get(name) || [];
  }
  
  async saveSecret(secret: Secret): Promise<void> {
    const secrets = this.secrets.get(secret.name) || [];
    secrets.push(secret);
    this.secrets.set(secret.name, secrets);
  }
  
  async deleteSecret(id: string): Promise<void> {
    for (const [name, secrets] of this.secrets.entries()) {
      const filteredSecrets = secrets.filter(s => s.id !== id);
      if (filteredSecrets.length !== secrets.length) {
        this.secrets.set(name, filteredSecrets);
        break;
      }
    }
  }
  
  async listSecretNames(): Promise<string[]> {
    return Array.from(this.secrets.keys());
  }
}

/**
 * Database secret storage provider (for production)
 */
export class DatabaseSecretStorage implements SecretStorageProvider {
  private supabase: any;
  
  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }
  
  async getSecret(name: string): Promise<Secret | null> {
    try {
      const { data, error } = await this.supabase
        .from('secrets')
        .select('*')
        .eq('name', name)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
      
      if (error) {
        throw error;
      }
      
      if (!data) {
        return null;
      }
      
      return {
        id: data.id,
        name: data.name,
        value: data.value,
        createdAt: new Date(data.created_at),
        expiresAt: new Date(data.expires_at),
        isActive: data.is_active,
        metadata: data.metadata
      };
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'get_secret', additionalData: { secretName: name } }
      );
      return null;
    }
  }
  
  async getSecrets(name: string): Promise<Secret[]> {
    try {
      const { data, error } = await this.supabase
        .from('secrets')
        .select('*')
        .eq('name', name)
        .order('created_at', { ascending: false });
      
      if (error) {
        throw error;
      }
      
      if (!data || !Array.isArray(data)) {
        return [];
      }
      
      return data.map((item: any) => ({
        id: item.id,
        name: item.name,
        value: item.value,
        createdAt: new Date(item.created_at),
        expiresAt: new Date(item.expires_at),
        isActive: item.is_active,
        metadata: item.metadata
      }));
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'get_secrets', additionalData: { secretName: name } }
      );
      return [];
    }
  }
  
  async saveSecret(secret: Secret): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('secrets')
        .insert({
          id: secret.id,
          name: secret.name,
          value: secret.value,
          created_at: secret.createdAt.toISOString(),
          expires_at: secret.expiresAt.toISOString(),
          is_active: secret.isActive,
          metadata: secret.metadata || {}
        });
      
      if (error) {
        throw error;
      }
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'save_secret', additionalData: { secretName: secret.name } }
      );
      throw error;
    }
  }
  
  async deleteSecret(id: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('secrets')
        .delete()
        .eq('id', id);
      
      if (error) {
        throw error;
      }
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'delete_secret', additionalData: { secretId: id } }
      );
      throw error;
    }
  }
  
  async listSecretNames(): Promise<string[]> {
    try {
      const { data, error } = await this.supabase
        .from('secrets')
        .select('name')
        .distinct();
      
      if (error) {
        throw error;
      }
      
      if (!data || !Array.isArray(data)) {
        return [];
      }
      
      return data.map((item: any) => item.name);
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'list_secret_names' }
      );
      return [];
    }
  }
}

/**
 * Secrets Manager Service
 */
export class SecretsManager {
  private static instance: SecretsManager;
  private storageProvider: SecretStorageProvider;
  private secretGenerators: Map<string, () => Promise<string>> = new Map();
  private rotationSchedules: Map<string, NodeJS.Timeout> = new Map();
  
  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor(storageProvider: SecretStorageProvider) {
    this.storageProvider = storageProvider;
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(storageProvider?: SecretStorageProvider): SecretsManager {
    if (!SecretsManager.instance) {
      if (!storageProvider) {
        storageProvider = new InMemorySecretStorage();
      }
      SecretsManager.instance = new SecretsManager(storageProvider);
    }
    return SecretsManager.instance;
  }
  
  /**
   * Register a custom secret generator for a specific secret
   */
  public registerSecretGenerator(name: string, generator: () => Promise<string>): void {
    this.secretGenerators.set(name, generator);
  }
  
  /**
   * Get a secret by name
   */
  public async getSecret(name: string): Promise<string | null> {
    try {
      // Get the active secret
      const secret = await this.storageProvider.getSecret(name);
      
      if (!secret) {
        return null;
      }
      
      // Check if secret is about to expire and trigger rotation if needed
      const now = new Date();
      const expirationThreshold = DEFAULT_ROTATION_OPTIONS.overlapDuration;
      
      if (secret.expiresAt.getTime() - now.getTime() < expirationThreshold) {
        // Trigger async rotation without blocking current request
        this.rotateSecret(name).catch(error => {
          ErrorService.logError(
            error instanceof Error ? error : new Error(String(error)),
            ErrorSeverity.ERROR,
            ErrorCategory.SECURITY,
            { action: 'auto_rotate_secret', additionalData: { secretName: name } }
          );
        });
      }
      
      return secret.value;
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'get_secret', additionalData: { secretName: name } }
      );
      return null;
    }
  }
  
  /**
   * Rotate a secret
   */
  public async rotateSecret(name: string, options: RotationOptions = {}): Promise<void> {
    const rotationOptions = { ...DEFAULT_ROTATION_OPTIONS, ...options };
    
    try {
      // Generate new secret value
      let newValue: string;
      
      // Use custom generator if registered
      if (this.secretGenerators.has(name)) {
        const generator = this.secretGenerators.get(name)!;
        newValue = await generator();
      } else {
        // Use default generator
        newValue = this.generateSecureSecret();
      }
      
      // Get existing secrets
      const existingSecrets = await this.storageProvider.getSecrets(name);
      
      // Create new secret
      const now = new Date();
      const expiresAt = new Date(now.getTime() + rotationOptions.expiryDuration);
      
      const newSecret: Secret = {
        id: crypto.randomUUID(),
        name,
        value: newValue,
        createdAt: now,
        expiresAt,
        isActive: true,
        metadata: rotationOptions.metadata
      };
      
      // Deactivate old secrets
      for (const secret of existingSecrets) {
        if (secret.isActive) {
          secret.isActive = false;
          await this.storageProvider.saveSecret(secret);
        }
      }
      
      // Save new secret
      await this.storageProvider.saveSecret(newSecret);
      
      // Clean up old secrets if needed
      if (!rotationOptions.keepOldSecrets) {
        for (const secret of existingSecrets) {
          await this.storageProvider.deleteSecret(secret.id);
        }
      } else if (rotationOptions.maxOldSecrets > 0) {
        // Keep only the specified number of old secrets
        const secretsToDelete = existingSecrets
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(rotationOptions.maxOldSecrets);
        
        for (const secret of secretsToDelete) {
          await this.storageProvider.deleteSecret(secret.id);
        }
      }
    } catch (error) {
      ErrorService.logError(
        error instanceof Error ? error : new Error(String(error)),
        ErrorSeverity.ERROR,
        ErrorCategory.SECURITY,
        { action: 'rotate_secret', additionalData: { secretName: name } }
      );
      throw error;
    }
  }
  
  /**
   * Schedule automatic rotation for a secret
   */
  public scheduleRotation(name: string, intervalMs: number, options: RotationOptions = {}): void {
    // Clear existing schedule if any
    this.clearRotationSchedule(name);
    
    // Schedule new rotation
    const timer = setInterval(async () => {
      try {
        await this.rotateSecret(name, options);
      } catch (error) {
        ErrorService.logError(
          error instanceof Error ? error : new Error(String(error)),
          ErrorSeverity.ERROR,
          ErrorCategory.SECURITY,
          { action: 'scheduled_rotation', additionalData: { secretName: name } }
        );
      }
    }, intervalMs);
    
    // Store the timer
    this.rotationSchedules.set(name, timer);
  }
  
  /**
   * Clear rotation schedule for a secret
   */
  public clearRotationSchedule(name: string): void {
    const timer = this.rotationSchedules.get(name);
    if (timer) {
      clearInterval(timer);
      this.rotationSchedules.delete(name);
    }
  }
  
  /**
   * Generate a secure random secret
   */
  private generateSecureSecret(length: number = 64): string {
    const bytes = crypto.randomBytes(length);
    return bytes.toString('base64').replace(/[+/=]/g, '').slice(0, length);
  }
}

export default SecretsManager;