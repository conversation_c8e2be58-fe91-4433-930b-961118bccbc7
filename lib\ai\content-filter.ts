/**
 * AI Content Filter & Validation
 * Comprehensive input/output filtering and safety validation
 */

import { getCacheService } from '@/lib/cache/cache-service'
import { metrics } from '@/lib/monitoring/metrics'
import { webcrypto } from 'crypto'

// Polyfill for Node.js environments
if (typeof globalThis.crypto === 'undefined') {
  globalThis.crypto = webcrypto as any
}

interface FilterResult {
  allowed: boolean
  reason?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  categories: string[]
  confidence: number
  sanitizedContent?: string
}

interface ContentPolicy {
  category: string
  enabled: boolean
  action: 'block' | 'warn' | 'sanitize'
  severity: 'low' | 'medium' | 'high' | 'critical'
  patterns: string[]
  description: string
}

interface ValidationRule {
  name: string
  type: 'length' | 'format' | 'content' | 'language' | 'custom'
  enabled: boolean
  parameters: any
  errorMessage: string
}

interface SimilarityResult {
  isDuplicate: boolean
  similarity: number
  existingRequestId?: string
  cacheKey?: string
}

class AIContentFilter {
  private cache = getCacheService()
  private readonly cachePrefix = 'content_filter:'
  private readonly similarityThreshold = 0.85
  private readonly maxContentLength = 50000 // 50KB
  private readonly minContentLength = 1

  // Content policies for different categories
  private contentPolicies: ContentPolicy[] = [
    {
      category: 'hate_speech',
      enabled: true,
      action: 'block',
      severity: 'critical',
      patterns: [
        'hate', 'racist', 'sexist', 'homophobic', 'transphobic',
        'nazi', 'fascist', 'supremacist', 'genocide', 'ethnic cleansing'
      ],
      description: 'Content promoting hate speech or discrimination'
    },
    {
      category: 'violence',
      enabled: true,
      action: 'block',
      severity: 'high',
      patterns: [
        'kill', 'murder', 'assassinate', 'bomb', 'terrorist',
        'violence', 'harm', 'hurt', 'attack', 'weapon'
      ],
      description: 'Content promoting violence or harm'
    },
    {
      category: 'harassment',
      enabled: true,
      action: 'warn',
      severity: 'medium',
      patterns: [
        'harass', 'bully', 'threaten', 'intimidate', 'stalk',
        'doxx', 'doxing', 'revenge', 'blackmail'
      ],
      description: 'Content that could constitute harassment'
    },
    {
      category: 'spam',
      enabled: true,
      action: 'sanitize',
      severity: 'low',
      patterns: [
        'click here', 'buy now', 'limited time', 'act fast',
        'guaranteed', 'risk free', 'no obligation'
      ],
      description: 'Spam or promotional content'
    },
    {
      category: 'personal_info',
      enabled: true,
      action: 'sanitize',
      severity: 'medium',
      patterns: [
        '\\b\\d{3}-\\d{2}-\\d{4}\\b', // SSN
        '\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b', // Credit card
        '\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b', // Email
        '\\b\\d{3}[\\s.-]?\\d{3}[\\s.-]?\\d{4}\\b' // Phone
      ],
      description: 'Personal identifiable information'
    },
    {
      category: 'profanity',
      enabled: true,
      action: 'sanitize',
      severity: 'low',
      patterns: [
        'damn', 'hell', 'crap', 'shit', 'fuck', 'bitch',
        'ass', 'bastard', 'piss', 'cock', 'dick'
      ],
      description: 'Profane language'
    }
  ]

  // Validation rules for input content
  private validationRules: ValidationRule[] = [
    {
      name: 'content_length',
      type: 'length',
      enabled: true,
      parameters: { min: 1, max: 50000 },
      errorMessage: 'Content must be between 1 and 50,000 characters'
    },
    {
      name: 'no_html_injection',
      type: 'content',
      enabled: true,
      parameters: { pattern: '<script|<iframe|<object|<embed|javascript:|data:' },
      errorMessage: 'HTML/JavaScript injection detected'
    },
    {
      name: 'no_sql_injection',
      type: 'content',
      enabled: true,
      parameters: { pattern: '(union|select|insert|update|delete|drop|create|alter)\\s+' },
      errorMessage: 'SQL injection pattern detected'
    },
    {
      name: 'language_detection',
      type: 'language',
      enabled: true,
      parameters: { allowedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt'] },
      errorMessage: 'Content language not supported'
    },
    {
      name: 'encoding_validation',
      type: 'format',
      enabled: true,
      parameters: { encoding: 'utf-8' },
      errorMessage: 'Invalid character encoding'
    }
  ]

  /**
   * Filter and validate input content
   */
  async filterInput(
    content: string,
    userId: string,
    organizationId: string,
    feature: string
  ): Promise<FilterResult> {
    const startTime = Date.now()

    try {
      // Basic validation
      const validationResult = this.validateInput(content)
      if (!validationResult.allowed) {
        return validationResult
      }

      // Content policy filtering
      const policyResult = await this.applyContentPolicies(content)
      if (!policyResult.allowed) {
        await this.logFilterEvent(userId, organizationId, feature, 'policy_violation', policyResult)
        return policyResult
      }

      // Check for duplicates/similarity
      const similarityResult = await this.checkSimilarity(content, userId, feature)
      if (similarityResult.isDuplicate) {
        return {
          allowed: false,
          reason: 'Duplicate or very similar request detected',
          severity: 'low',
          categories: ['duplicate'],
          confidence: similarityResult.similarity
        }
      }

      // Advanced content analysis (would integrate with external services)
      const advancedResult = await this.performAdvancedAnalysis(content)
      if (!advancedResult.allowed) {
        await this.logFilterEvent(userId, organizationId, feature, 'advanced_filter', advancedResult)
        return advancedResult
      }

      // Content passed all filters
      const duration = Date.now() - startTime
      metrics.timing('content_filter.input_processing_time', duration)
      metrics.increment('content_filter.input_allowed', { feature })

      return {
        allowed: true,
        severity: 'low',
        categories: [],
        confidence: 1.0,
        sanitizedContent: policyResult.sanitizedContent || content
      }

    } catch (error) {
      console.error('Content filtering error:', error)
      metrics.increment('content_filter.errors')

      // Fail safe - allow content but log error
      return {
        allowed: true,
        reason: 'Filter error - content allowed by default',
        severity: 'low',
        categories: ['filter_error'],
        confidence: 0.5
      }
    }
  }

  /**
   * Validate AI response output
   */
  async validateOutput(
    content: string,
    originalPrompt: string,
    provider: string,
    model: string
  ): Promise<FilterResult> {
    const startTime = Date.now()

    try {
      // Check for harmful content in response
      const harmfulContentResult = await this.checkHarmfulContent(content)
      if (!harmfulContentResult.allowed) {
        return harmfulContentResult
      }

      // Validate response quality
      const qualityResult = this.validateResponseQuality(content, originalPrompt)
      if (!qualityResult.allowed) {
        return qualityResult
      }

      // Check for data leakage
      const leakageResult = this.checkDataLeakage(content)
      if (!leakageResult.allowed) {
        return leakageResult
      }

      // Validate response format
      const formatResult = this.validateResponseFormat(content)
      if (!formatResult.allowed) {
        return formatResult
      }

      const duration = Date.now() - startTime
      metrics.timing('content_filter.output_processing_time', duration)
      metrics.increment('content_filter.output_allowed', { provider, model })

      return {
        allowed: true,
        severity: 'low',
        categories: [],
        confidence: 1.0,
        sanitizedContent: content
      }

    } catch (error) {
      console.error('Output validation error:', error)
      metrics.increment('content_filter.output_errors')

      return {
        allowed: false,
        reason: 'Output validation failed',
        severity: 'high',
        categories: ['validation_error'],
        confidence: 0.8
      }
    }
  }

  /**
   * Check for request similarity/duplication
   */
  async checkSimilarity(
    content: string,
    userId: string,
    feature: string
  ): Promise<SimilarityResult> {
    const cacheKey = `${this.cachePrefix}similarity:${userId}:${feature}`

    try {
      // Get recent requests from cache
      const recentRequests = await this.cache.get<string[]>(cacheKey) || []

      // Calculate similarity with recent requests
      for (const recentRequest of recentRequests) {
        const similarity = this.calculateSimilarity(content, recentRequest)
        if (similarity >= this.similarityThreshold) {
          metrics.increment('content_filter.duplicate_detected', { feature })
          return {
            isDuplicate: true,
            similarity,
            existingRequestId: this.hashString(recentRequest),
            cacheKey
          }
        }
      }

      // Add current request to cache
      const updatedRequests = [content, ...recentRequests.slice(0, 9)] // Keep last 10 requests
      await this.cache.set(cacheKey, updatedRequests, {
        ttl: 3600, // 1 hour
        tags: ['similarity_check', userId]
      })

      return {
        isDuplicate: false,
        similarity: 0
      }

    } catch (error) {
      console.error('Similarity check error:', error)
      return {
        isDuplicate: false,
        similarity: 0
      }
    }
  }

  /**
   * Update content policies
   */
  updateContentPolicy(category: string, policy: Partial<ContentPolicy>): void {
    const index = this.contentPolicies.findIndex(p => p.category === category)
    if (index >= 0) {
      this.contentPolicies[index] = { ...this.contentPolicies[index], ...policy }
    } else {
      this.contentPolicies.push(policy as ContentPolicy)
    }

    console.log(`Updated content policy for category: ${category}`)
  }

  /**
   * Get content filtering statistics
   */
  async getFilteringStats(
    period: 'day' | 'week' | 'month' = 'day'
  ): Promise<{
    totalFiltered: number
    byCategory: { [category: string]: number }
    bySeverity: { [severity: string]: number }
    byAction: { [action: string]: number }
    trends: Array<{ date: string; count: number }>
  }> {
    // This would query actual filtering logs
    // For now, return mock data
    return {
      totalFiltered: 45,
      byCategory: {
        spam: 20,
        personal_info: 15,
        profanity: 8,
        harassment: 2
      },
      bySeverity: {
        low: 28,
        medium: 15,
        high: 2,
        critical: 0
      },
      byAction: {
        sanitize: 35,
        warn: 8,
        block: 2
      },
      trends: [
        { date: '2024-01-01', count: 12 },
        { date: '2024-01-02', count: 15 },
        { date: '2024-01-03', count: 18 }
      ]
    }
  }

  // Private helper methods
  private validateInput(content: string): FilterResult {
    for (const rule of this.validationRules) {
      if (!rule.enabled) continue

      const result = this.applyValidationRule(content, rule)
      if (!result.allowed) {
        return result
      }
    }

    return {
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 1.0
    }
  }

  private applyValidationRule(content: string, rule: ValidationRule): FilterResult {
    switch (rule.type) {
      case 'length':
        const length = content.length
        if (length < rule.parameters.min || length > rule.parameters.max) {
          return {
            allowed: false,
            reason: rule.errorMessage,
            severity: 'medium',
            categories: ['validation'],
            confidence: 1.0
          }
        }
        break

      case 'content':
        const regex = new RegExp(rule.parameters.pattern, 'i')
        if (regex.test(content)) {
          return {
            allowed: false,
            reason: rule.errorMessage,
            severity: 'high',
            categories: ['injection'],
            confidence: 0.9
          }
        }
        break

      case 'language':
        // Simple language detection (would use proper library in production)
        const detectedLang = this.detectLanguage(content)
        if (!rule.parameters.allowedLanguages.includes(detectedLang)) {
          return {
            allowed: false,
            reason: rule.errorMessage,
            severity: 'low',
            categories: ['language'],
            confidence: 0.7
          }
        }
        break
    }

    return {
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 1.0
    }
  }

  private async applyContentPolicies(content: string): Promise<FilterResult> {
    let sanitizedContent = content
    const violatedCategories: string[] = []
    let maxSeverity: 'low' | 'medium' | 'high' | 'critical' = 'low'
    let shouldBlock = false

    for (const policy of this.contentPolicies) {
      if (!policy.enabled) continue

      const violations = this.checkPolicyViolations(content, policy)
      if (violations.length > 0) {
        violatedCategories.push(policy.category)

        if (this.compareSeverity(policy.severity, maxSeverity) > 0) {
          maxSeverity = policy.severity
        }

        switch (policy.action) {
          case 'block':
            shouldBlock = true
            break
          case 'sanitize':
            sanitizedContent = this.sanitizeContent(sanitizedContent, policy)
            break
          case 'warn':
            // Log warning but allow content
            console.warn(`Content policy warning: ${policy.category}`)
            break
        }
      }
    }

    if (shouldBlock) {
      return {
        allowed: false,
        reason: `Content violates policies: ${violatedCategories.join(', ')}`,
        severity: maxSeverity,
        categories: violatedCategories,
        confidence: 0.9
      }
    }

    return {
      allowed: true,
      severity: maxSeverity,
      categories: violatedCategories,
      confidence: 0.8,
      sanitizedContent
    }
  }

  private checkPolicyViolations(content: string, policy: ContentPolicy): string[] {
    const violations: string[] = []
    const lowerContent = content.toLowerCase()

    for (const pattern of policy.patterns) {
      const regex = new RegExp(pattern, 'gi')
      const matches = lowerContent.match(regex)
      if (matches) {
        violations.push(...matches)
      }
    }

    return violations
  }

  private sanitizeContent(content: string, policy: ContentPolicy): string {
    let sanitized = content

    for (const pattern of policy.patterns) {
      const regex = new RegExp(pattern, 'gi')
      sanitized = sanitized.replace(regex, (match) => {
        if (policy.category === 'personal_info') {
          return '[REDACTED]'
        } else if (policy.category === 'profanity') {
          return '*'.repeat(match.length)
        } else {
          return '[FILTERED]'
        }
      })
    }

    return sanitized
  }

  private async performAdvancedAnalysis(content: string): Promise<FilterResult> {
    // This would integrate with external content moderation APIs
    // For now, return a simple analysis

    const suspiciousPatterns = [
      'bypass', 'circumvent', 'hack', 'exploit', 'vulnerability',
      'jailbreak', 'prompt injection', 'ignore instructions'
    ]

    for (const pattern of suspiciousPatterns) {
      if (content.toLowerCase().includes(pattern)) {
        return {
          allowed: false,
          reason: 'Suspicious content pattern detected',
          severity: 'high',
          categories: ['suspicious'],
          confidence: 0.8
        }
      }
    }

    return {
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 0.9
    }
  }

  private checkHarmfulContent(content: string): Promise<FilterResult> {
    // Check for harmful content in AI responses
    const harmfulPatterns = [
      'how to make', 'instructions for', 'step by step',
      'recipe for', 'guide to', 'tutorial on'
    ]

    const harmfulContexts = [
      'bomb', 'explosive', 'poison', 'drug', 'weapon',
      'hack', 'illegal', 'fraud', 'scam'
    ]

    const lowerContent = content.toLowerCase()

    for (const pattern of harmfulPatterns) {
      if (lowerContent.includes(pattern)) {
        for (const context of harmfulContexts) {
          if (lowerContent.includes(context)) {
            return Promise.resolve({
              allowed: false,
              reason: 'Response contains potentially harmful instructions',
              severity: 'critical',
              categories: ['harmful_instructions'],
              confidence: 0.9
            })
          }
        }
      }
    }

    return Promise.resolve({
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 1.0
    })
  }

  private validateResponseQuality(content: string, originalPrompt: string): FilterResult {
    // Check response quality and relevance
    if (content.length < 10) {
      return {
        allowed: false,
        reason: 'Response too short',
        severity: 'medium',
        categories: ['quality'],
        confidence: 1.0
      }
    }

    if (content.includes('I cannot') && content.includes('I\'m sorry')) {
      return {
        allowed: false,
        reason: 'AI refused to respond',
        severity: 'medium',
        categories: ['refusal'],
        confidence: 0.8
      }
    }

    return {
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 0.9
    }
  }

  private checkDataLeakage(content: string): FilterResult {
    // Check for potential data leakage in responses
    const leakagePatterns = [
      '\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b', // Email
      '\\b\\d{3}[\\s.-]?\\d{3}[\\s.-]?\\d{4}\\b', // Phone
      '\\b\\d{3}-\\d{2}-\\d{4}\\b', // SSN
      'password', 'secret', 'token', 'key', 'credential'
    ]

    for (const pattern of leakagePatterns) {
      const regex = new RegExp(pattern, 'i')
      if (regex.test(content)) {
        return {
          allowed: false,
          reason: 'Potential data leakage detected',
          severity: 'high',
          categories: ['data_leakage'],
          confidence: 0.8
        }
      }
    }

    return {
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 1.0
    }
  }

  private validateResponseFormat(content: string): FilterResult {
    // Validate response format and structure
    if (content.includes('<script>') || content.includes('javascript:')) {
      return {
        allowed: false,
        reason: 'Response contains executable code',
        severity: 'high',
        categories: ['code_injection'],
        confidence: 1.0
      }
    }

    return {
      allowed: true,
      severity: 'low',
      categories: [],
      confidence: 1.0
    }
  }

  private calculateSimilarity(text1: string, text2: string): number {
    // Simple similarity calculation using Jaccard similarity
    const words1 = new Set(text1.toLowerCase().split(/\s+/))
    const words2 = new Set(text2.toLowerCase().split(/\s+/))

    const intersection = new Set([...words1].filter(x => words2.has(x)))
    const union = new Set([...words1, ...words2])

    return intersection.size / union.size
  }

  private detectLanguage(content: string): string {
    // Simple language detection (would use proper library in production)
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
    const words = content.toLowerCase().split(/\s+/)
    const englishCount = words.filter(word => englishWords.includes(word)).length

    return englishCount > words.length * 0.1 ? 'en' : 'unknown'
  }

  private compareSeverity(severity1: string, severity2: string): number {
    const severityOrder = { low: 1, medium: 2, high: 3, critical: 4 }
    return (severityOrder[severity1] || 0) - (severityOrder[severity2] || 0)
  }

  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(36)
  }

  private async logFilterEvent(
    userId: string,
    organizationId: string,
    feature: string,
    eventType: string,
    result: FilterResult
  ): Promise<void> {
    try {
      // Log to database or external service
      console.log('Filter event:', {
        userId,
        organizationId,
        feature,
        eventType,
        severity: result.severity,
        categories: result.categories,
        timestamp: new Date().toISOString()
      })

      metrics.increment('content_filter.violations', {
        feature,
        severity: result.severity,
        category: result.categories[0] || 'unknown'
      })
    } catch (error) {
      console.error('Error logging filter event:', error)
    }
  }
}

// Singleton instance
let contentFilter: AIContentFilter | null = null

export const getContentFilter = (): AIContentFilter => {
  if (!contentFilter) {
    contentFilter = new AIContentFilter()
  }
  return contentFilter
}

export { AIContentFilter }
export type { FilterResult, ContentPolicy, ValidationRule, SimilarityResult }
