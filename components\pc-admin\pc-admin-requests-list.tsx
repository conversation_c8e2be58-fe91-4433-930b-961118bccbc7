"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Monitor, 
  Shield, 
  ShieldOff, 
  Clock, 
  CheckCircle,
  XCircle,
  RefreshCw,
  FileText,
  Calendar,
  User
} from 'lucide-react';
import { pcAdminService } from '@/lib/services/pc-admin-service';
import type { PcAdminRequest } from '@/lib/pc-admin-types';
import { useAuth } from '@/lib/auth-context';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';

interface PcAdminRequestsListProps {
  showAll?: boolean;
  onRequestUpdate?: () => void;
}

export function PcAdminRequestsList({ showAll = false, onRequestUpdate }: PcAdminRequestsListProps) {
  const { staff, isITSupport, isGlobalAdmin, isSystemAdmin } = useAuth();
  const [requests, setRequests] = useState<PcAdminRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('all');

  useEffect(() => {
    loadRequests();
    
    // Subscribe to real-time updates
    if (staff) {
      const unsubscribe = pcAdminService.subscribeToPcAdminRequests(
        () => loadRequests(),
        showAll ? undefined : { requester_id: staff.id }
      );
      
      return () => unsubscribe();
    }
  }, [staff, showAll]);

  const loadRequests = async () => {
    if (!staff) return;
    
    setLoading(true);
    try {
      const filter = showAll || isITSupport() || isGlobalAdmin() || isSystemAdmin()
        ? {}
        : { requester_id: staff.id };
      
      const data = await pcAdminService.getPcAdminRequests(filter);
      setRequests(data);
    } finally {
      setLoading(false);
    }
  };
  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: "secondary",
      approved: "default",
      rejected: "destructive",
      completed: "default"
    };

    const labels: Record<string, string> = {
      pending: "承認待ち",
      approved: "承認済み",
      rejected: "却下",
      completed: "完了"
    };

    return (
      <Badge variant={variants[status] || "outline"}>
        {labels[status] || status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      low: "outline",
      medium: "secondary",
      high: "default",
      urgent: "destructive"
    };

    const labels: Record<string, string> = {
      low: "低",
      medium: "中",
      high: "高",
      urgent: "緊急"
    };

    return (
      <Badge variant={variants[priority] || "outline"}>
        {labels[priority] || priority}
      </Badge>
    );
  };

  const getActionIcon = (actionType: string) => {
    return actionType === 'grant_admin' ? (
      <Shield className="w-4 h-4 text-green-600" />
    ) : (
      <ShieldOff className="w-4 h-4 text-red-600" />
    );
  };

  const getActionLabel = (actionType: string) => {
    return actionType === 'grant_admin' ? '権限付与' : '権限削除';
  };

  const filteredRequests = activeTab === 'all'
    ? requests
    : requests.filter(req => req.status === activeTab);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <RefreshCw className="w-6 h-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>PC管理者権限リクエスト一覧</CardTitle>
        <CardDescription>
          {showAll ? 'すべてのリクエスト' : '自分のリクエスト'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">
              すべて ({requests.length})
            </TabsTrigger>
            <TabsTrigger value="pending">
              承認待ち ({requests.filter(r => r.status === 'pending').length})
            </TabsTrigger>
            <TabsTrigger value="approved">
              承認済み ({requests.filter(r => r.status === 'approved').length})
            </TabsTrigger>
            <TabsTrigger value="rejected">
              却下 ({requests.filter(r => r.status === 'rejected').length})
            </TabsTrigger>
            <TabsTrigger value="completed">
              完了 ({requests.filter(r => r.status === 'completed').length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-4">
            {filteredRequests.length === 0 ? (
              <Alert>
                <AlertDescription>
                  該当するリクエストはありません
                </AlertDescription>
              </Alert>
            ) : (
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-4">
                  {filteredRequests.map((request: any) => (
                    <Card key={request.id} className="p-4">
                      <div className="space-y-3">
                        {/* Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Monitor className="w-5 h-5" />
                            <span className="font-medium">
                              {request.request_id}
                            </span>
                            {getStatusBadge(request.status)}
                            {getPriorityBadge(request.priority)}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {format(new Date(request.created_at), 'yyyy/MM/dd HH:mm', { locale: ja })}
                          </span>
                        </div>

                        {/* PC Info */}
                        <div className="flex items-center gap-6 text-sm">
                          <div className="flex items-center gap-2">
                            <Monitor className="w-4 h-4 text-muted-foreground" />
                            <span>{request.pc_id_search}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {getActionIcon(request.action_type)}
                            <span>{getActionLabel(request.action_type)}</span>
                          </div>
                        </div>

                        {/* Requester Info */}
                        {request.staff && (
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <User className="w-4 h-4" />
                            <span>
                              {request.staff.name_jp} ({request.staff.email})
                            </span>
                          </div>
                        )}

                        {/* Reason */}
                        <div className="p-3 bg-muted rounded-md">
                          <p className="text-sm">{request.reason}</p>
                        </div>

                        {/* Software to Install */}
                        {request.software_to_install && request.software_to_install.length > 0 && (
                          <div className="space-y-1">
                            <p className="text-sm font-medium">インストール予定ソフトウェア:</p>
                            <div className="flex flex-wrap gap-2">
                              {request.software_to_install.map((software: string, index: number) => (
                                <Badge key={index} variant="outline">
                                  {software}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Notes */}
                        {request.notes && (
                          <Alert>
                            <FileText className="h-4 w-4" />
                            <AlertDescription>{request.notes}</AlertDescription>
                          </Alert>
                        )}

                        {/* Status Details */}
                        {request.approved_at && (
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">承認日時:</span>{' '}
                            {format(new Date(request.approved_at), 'yyyy/MM/dd HH:mm', { locale: ja })}
                          </div>
                        )}
                        {request.completed_at && (
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">完了日時:</span>{' '}
                            {format(new Date(request.completed_at), 'yyyy/MM/dd HH:mm', { locale: ja })}
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}