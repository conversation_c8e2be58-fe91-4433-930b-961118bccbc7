// Simple test to check if basic Next.js build works
const { execSync } = require('child_process');

try {
  console.log('Testing Next.js build...');
  
  // Try to run Next.js build with minimal configuration
  const result = execSync('npx next build --no-lint', { 
    encoding: 'utf8',
    stdio: 'pipe',
    timeout: 300000 // 5 minutes
  });
  
  console.log('Build successful!');
  console.log(result);
} catch (error) {
  console.error('Build failed:');
  console.error('Exit code:', error.status);
  console.error('Error output:', error.stderr);
  console.error('Standard output:', error.stdout);
}
