// lib/hooks/use-nlp-form.ts
// React hook for integrating NLP with forms

import { useState, useCallback } from 'react'
import { nlpFormService } from '@/lib/services/nlp-form-service'

interface UseNLPFormOptions {
  availableForms: string[]
  language?: 'ja' | 'en'
  onFormDetected?: (formType: string, data: Record<string, any>) => void
}

export function useNLPForm({
  availableForms,
  language = 'ja',
  onFormDetected
}: UseNLPFormOptions) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [parsedData, setParsedData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const parseInput = useCallback(async (input: string) => {
    if (!input.trim()) {
      setError('Input is required')
      return null
    }

    setIsProcessing(true)
    setError(null)
    
    try {
      const result = await nlpFormService.parseNaturalLanguageInput(
        input,
        {
          language,
          availableForms
        }
      )
      
      setParsedData(result)
      
      // Notify if form detected with good confidence
      if (result.suggestedForm && result.confidence > 0.5 && onFormDetected) {
        onFormDetected(result.suggestedForm, result.extractedData)
      }
      
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to parse input'
      setError(errorMessage)
      return null
    } finally {
      setIsProcessing(false)
    }
  }, [availableForms, language, onFormDetected])

  const extractSpecificData = useCallback((input: string, formType: string) => {
    switch (formType) {
      case 'group_mail':
        return nlpFormService.parseGroupMailRequest(input)
      case 'password_reset':
        return nlpFormService.parsePasswordResetRequest(input)
      case 'sharepoint':
        return nlpFormService.parseSharePointRequest(input)
      default:
        return {}
    }
  }, [])

  const suggestForm = useCallback((input: string) => {
    return nlpFormService.suggestFormType(input)
  }, [])

  const extractEntities = useCallback((input: string) => {
    return {
      users: nlpFormService.extractUserNames(input),
      dates: nlpFormService.extractDates(input)
    }
  }, [])

  const mapToFormValues = useCallback((formSchema: any) => {
    if (!parsedData) return {}
    return nlpFormService.mapToFormFields(parsedData, formSchema)
  }, [parsedData])

  const reset = useCallback(() => {
    setParsedData(null)
    setError(null)
  }, [])

  return {
    isProcessing,
    parsedData,
    error,
    parseInput,
    extractSpecificData,
    suggestForm,
    extractEntities,
    mapToFormValues,
    reset,
    confidence: parsedData?.confidence || 0,
    suggestedForm: parsedData?.suggestedForm || null,
    extractedData: parsedData?.extractedData || {}
  }
}
