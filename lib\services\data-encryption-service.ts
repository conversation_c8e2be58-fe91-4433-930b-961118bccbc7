import { createClient } from '@supabase/supabase-js';
import { Database } from '../database.types';

export interface EncryptedField {
  fieldName: string;
  value: string | null;
  encrypted: boolean;
}

export class DataEncryptionService {
  private supabase: ReturnType<typeof createClient<Database>>;

  constructor(supabaseClient: ReturnType<typeof createClient<Database>>) {
    this.supabase = supabaseClient;
  }

  /**
   * Encrypt sensitive data before storing
   */
  async encryptData(data: string, keyName: string = 'default'): Promise<string> {
    try {
      const { data: result, error } = await this.supabase.rpc('encrypt_sensitive', {
        data,
        key_name: keyName
      });

      if (error) throw error;
      return result as string;
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decryptData(encryptedData: string, keyName: string = 'default'): Promise<string> {
    try {
      const { data: result, error } = await this.supabase.rpc('decrypt_sensitive', {
        encrypted_data: encryptedData,
        key_name: keyName
      });

      if (error) throw error;
      return result as string;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Prepare staff data for secure storage
   */
  async prepareStaffDataForStorage(staffData: any): Promise<any> {
    const sensitiveFields = ['phone', 'personal_email', 'address', 'emergency_contact'];
    const preparedData = { ...staffData };

    for (const field of sensitiveFields) {
      if (preparedData[field]) {
        // The database trigger will handle encryption
        // We just need to ensure the data is in the correct field
        preparedData[field] = staffData[field];
      }
    }

    return preparedData;
  }

  /**
   * Get decrypted staff data (using the view)
   */
  async getDecryptedStaffData(staffId: string): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('staff_decrypted')
        .select('*')
        .eq('id', staffId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching decrypted staff data:', error);
      throw error;
    }
  }

  /**
   * Create encrypted API key
   */
  async createApiKey(
    userId: string,
    name: string,
    expiresInDays: number = 365
  ): Promise<{
    id: string;
    keyPrefix: string;
    expiresAt: string;
    fullKey?: string;
  }> {
    try {
      const { data, error } = await this.supabase.rpc('create_encrypted_api_key', {
        p_user_id: userId,
        p_name: name,
        p_expires_in_days: expiresInDays
      });

      if (error) throw error;
      
      // Generate the full key for display (only shown once)
      const fullKey = this.generateSecureApiKey();
      
      return {
        id: data[0].id,
        keyPrefix: data[0].key_prefix,
        expiresAt: data[0].expires_at,
        fullKey // This should only be shown once to the user
      };
    } catch (error) {
      console.error('Error creating API key:', error);
      throw error;
    }
  }

  /**
   * Verify API key
   */
  async verifyApiKey(apiKey: string): Promise<{
    valid: boolean;
    userId?: string;
    keyId?: string;
  }> {
    try {
      // Hash the provided key
      const keyHash = await this.hashApiKey(apiKey);

      const { data, error } = await this.supabase
        .from('encrypted_api_keys')
        .select('id, user_id, expires_at')
        .eq('key_hash', keyHash)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return { valid: false };
      }

      // Check expiration
      if (new Date(data.expires_at) < new Date()) {
        return { valid: false };
      }

      // Update last used
      await this.supabase
        .from('encrypted_api_keys')
        .update({ last_used_at: new Date().toISOString() })
        .eq('id', data.id);

      return {
        valid: true,
        userId: data.user_id,
        keyId: data.id
      };
    } catch (error) {
      console.error('Error verifying API key:', error);
      return { valid: false };
    }
  }

  /**
   * Handle encrypted file attachments
   */
  async saveEncryptedAttachment(
    requestId: string,
    fileName: string,
    filePath: string,
    fileSize: number,
    mimeType: string,
    uploadedBy: string
  ): Promise<string> {
    try {
      const { data, error } = await this.supabase
        .from('encrypted_attachments')
        .insert({
          request_id: requestId,
          file_name_encrypted: await this.encryptData(fileName),
          file_path_encrypted: await this.encryptData(filePath),
          file_size: fileSize,
          mime_type: mimeType,
          uploaded_by: uploadedBy
        })
        .select('id')
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error saving encrypted attachment:', error);
      throw error;
    }
  }

  /**
   * Get decrypted attachment info
   */
  async getDecryptedAttachment(attachmentId: string): Promise<{
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('encrypted_attachments')
        .select('*')
        .eq('id', attachmentId)
        .single();

      if (error) throw error;

      return {
        fileName: await this.decryptData(data.file_name_encrypted),
        filePath: await this.decryptData(data.file_path_encrypted),
        fileSize: data.file_size,
        mimeType: data.mime_type
      };
    } catch (error) {
      console.error('Error getting decrypted attachment:', error);
      throw error;
    }
  }

  /**
   * Log encryption event
   */
  private async logEncryptionEvent(
    eventType: string,
    userId: string,
    tableName: string,
    recordId: string,
    keyName: string,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      await this.supabase.rpc('log_encryption_event', {
        p_event_type: eventType,
        p_user_id: userId,
        p_table_name: tableName,
        p_record_id: recordId,
        p_key_name: keyName,
        p_success: success,
        p_error_message: errorMessage
      });
    } catch (error) {
      console.error('Failed to log encryption event:', error);
    }
  }

  /**
   * Generate secure API key
   */
  private generateSecureApiKey(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Hash API key for storage
   */
  private async hashApiKey(apiKey: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Mask sensitive data for display
   */
  maskSensitiveData(data: string, visibleChars: number = 4): string {
    if (!data || data.length <= visibleChars) {
      return data;
    }
    
    const visible = data.slice(0, visibleChars);
    const masked = '*'.repeat(Math.min(data.length - visibleChars, 8));
    return `${visible}${masked}`;
  }

  /**
   * Check if data needs encryption based on field type
   */
  isEncryptionRequired(fieldName: string): boolean {
    const encryptedFields = [
      'phone',
      'personal_email',
      'address',
      'emergency_contact',
      'ssn',
      'tax_id',
      'bank_account',
      'credit_card'
    ];
    
    return encryptedFields.includes(fieldName.toLowerCase());
  }
}
