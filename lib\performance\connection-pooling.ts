// Supabase Connection Pooling Configuration
// Optimized settings for enterprise-scale deployment

export const connectionPoolConfig = {
  // PgBouncer Configuration for Supabase
  poolMode: 'transaction' as const,
  
  // Connection limits
  defaultPoolSize: 25,
  minPoolSize: 10,
  maxPoolSize: 100,
  
  // Client configuration
  maxClientConn: 1000,
  poolTimeout: 10,
  
  // Connection lifecycle
  serverLifetime: 3600, // 1 hour
  idleTimeout: 900, // 15 minutes
  
  // Database limits
  maxDbConnections: 100,
  maxUserConnections: 100,
  
  // Statement timeouts
  statementTimeout: '30s',
  idleInTransactionSessionTimeout: '60s',
  
  // Query optimization
  queryTimeout: 30000, // 30 seconds
  connectionTimeoutMillis: 5000,
  
  // Retry configuration
  acquireConnectionTimeout: 10000,
  createTimeoutMillis: 30000,
  reapIntervalMillis: 1000,
  createRetryIntervalMillis: 100,
  
  // Performance settings
  propagateCreateError: false,
  allowExitOnIdle: true,
  
  // Monitoring
  log: (message: string, level: string) => {
    if (level === 'error') {
      console.error(`[Pool] ${message}`);
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`[Pool] ${message}`);
    }
  }
};

// Horizontal scaling configuration
export const scalingConfig = {
  // Auto-scaling triggers
  cpuThreshold: 70, // Scale up at 70% CPU
  memoryThreshold: 80, // Scale up at 80% memory
  connectionThreshold: 80, // Scale up at 80% connections
  
  // Scaling parameters
  minInstances: 2,
  maxInstances: 10,
  scaleUpIncrement: 2,
  scaleDownIncrement: 1,
  cooldownPeriod: 300, // 5 minutes
  
  // Load balancing
  loadBalancerConfig: {
    algorithm: 'least_connections',
    healthCheckInterval: 30,
    healthCheckTimeout: 5,
    unhealthyThreshold: 3,
    healthyThreshold: 2
  }
};