-- Create tables for historical data analysis and AI learning

-- Table to store form submission history for pattern analysis
CREATE TABLE IF NOT EXISTS form_submission_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  department_id UUID REFERENCES departments(id),
  service_category_id UUID REFERENCES service_categories(id),
  form_data JSONB NOT NULL,
  field_values JSONB NOT NULL, -- Normalized field values for analysis
  submission_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table to store AI-learned patterns
CREATE TABLE IF NOT EXISTS ai_form_patterns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  pattern_type VARCHAR(50) NOT NULL, -- 'field_value', 'field_sequence', 'department_preference'
  pattern_key VARCHAR(255) NOT NULL, -- e.g., 'department:it-systems:field:reason'
  pattern_data JSONB NOT NULL,
  confidence_score DECIMAL(3,2) DEFAULT 0.50,
  usage_count INTEGER DEFAULT 0,
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(pattern_type, pattern_key)
);

-- Table to store field suggestion history
CREATE TABLE IF NOT EXISTS ai_field_suggestions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  field_id VARCHAR(255) NOT NULL,
  original_value TEXT,
  suggested_value TEXT,
  was_accepted BOOLEAN DEFAULT FALSE,
  confidence_score DECIMAL(3,2),
  suggestion_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for aggregated field statistics
CREATE TABLE IF NOT EXISTS field_usage_statistics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  department_id UUID REFERENCES departments(id),
  field_id VARCHAR(255) NOT NULL,
  common_values JSONB DEFAULT '[]', -- Array of {value, frequency, last_used}
  validation_patterns JSONB DEFAULT '{}',
  average_completion_time INTEGER, -- in seconds
  error_rate DECIMAL(3,2) DEFAULT 0.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(department_id, field_id)
);

-- Indexes for performance
CREATE INDEX idx_form_submission_history_user_dept ON form_submission_history(user_id, department_id);
CREATE INDEX idx_form_submission_history_created ON form_submission_history(created_at DESC);
CREATE INDEX idx_ai_patterns_key ON ai_form_patterns(pattern_key);
CREATE INDEX idx_ai_patterns_confidence ON ai_form_patterns(confidence_score DESC);
CREATE INDEX idx_field_suggestions_user_field ON ai_field_suggestions(user_id, field_id);
CREATE INDEX idx_field_usage_stats_dept_field ON field_usage_statistics(department_id, field_id);

-- Enable Row Level Security
ALTER TABLE form_submission_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_form_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_field_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_usage_statistics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Form submission history - users can see their own, admins can see all
CREATE POLICY "Users can view own submission history" ON form_submission_history
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM staff s
    JOIN roles r ON s.role_id = r.id
    WHERE s.auth_id = auth.uid() AND r.name IN ('Global Administrator', 'Web App System Administrator')
  ));

-- AI patterns - readable by all authenticated users
CREATE POLICY "Authenticated users can read patterns" ON ai_form_patterns
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only system can write patterns
CREATE POLICY "System can manage patterns" ON ai_form_patterns
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Field suggestions - users can see their own
CREATE POLICY "Users can view own suggestions" ON ai_field_suggestions
  FOR SELECT USING (auth.uid() = user_id);

-- Field statistics - department members can view their dept stats
CREATE POLICY "Department members can view statistics" ON field_usage_statistics
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM staff s
    WHERE s.auth_id = auth.uid() AND s.department_id = field_usage_statistics.department_id
  ));

-- Function to record form submission for learning
CREATE OR REPLACE FUNCTION record_form_submission(
  p_user_id UUID,
  p_department_id UUID,
  p_service_category_id UUID,
  p_form_data JSONB
) RETURNS UUID AS $$
DECLARE
  v_submission_id UUID;
  v_field_values JSONB = '{}';
  v_key TEXT;
  v_value TEXT;
BEGIN
  -- Extract and normalize field values
  FOR v_key, v_value IN SELECT * FROM jsonb_each_text(p_form_data)
  LOOP
    IF v_value IS NOT NULL AND v_value != '' THEN
      v_field_values = v_field_values || jsonb_build_object(v_key, v_value);
    END IF;
  END LOOP;

  -- Insert submission history
  INSERT INTO form_submission_history (
    user_id, department_id, service_category_id, 
    form_data, field_values
  ) VALUES (
    p_user_id, p_department_id, p_service_category_id,
    p_form_data, v_field_values
  ) RETURNING id INTO v_submission_id;

  -- Update field usage statistics
  PERFORM update_field_statistics(p_department_id, v_field_values);

  RETURN v_submission_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update field statistics
CREATE OR REPLACE FUNCTION update_field_statistics(
  p_department_id UUID,
  p_field_values JSONB
) RETURNS VOID AS $$
DECLARE
  v_field_id TEXT;
  v_field_value TEXT;
  v_current_stats RECORD;
  v_common_values JSONB;
  v_value_found BOOLEAN;
  v_value_entry JSONB;
  i INTEGER;
BEGIN
  -- Process each field
  FOR v_field_id, v_field_value IN SELECT * FROM jsonb_each_text(p_field_values)
  LOOP
    -- Get current statistics
    SELECT * INTO v_current_stats
    FROM field_usage_statistics
    WHERE department_id = p_department_id AND field_id = v_field_id;

    IF v_current_stats IS NULL THEN
      -- Create new statistics entry
      INSERT INTO field_usage_statistics (
        department_id, field_id, common_values
      ) VALUES (
        p_department_id, v_field_id, 
        jsonb_build_array(jsonb_build_object(
          'value', v_field_value,
          'frequency', 1,
          'last_used', NOW()
        ))
      );
    ELSE
      -- Update existing statistics
      v_common_values = v_current_stats.common_values;
      v_value_found = FALSE;

      -- Check if value exists and update frequency
      FOR i IN 0..jsonb_array_length(v_common_values) - 1
      LOOP
        v_value_entry = v_common_values->i;
        IF v_value_entry->>'value' = v_field_value THEN
          v_value_entry = jsonb_set(v_value_entry, '{frequency}', 
            to_jsonb((v_value_entry->>'frequency')::integer + 1));
          v_value_entry = jsonb_set(v_value_entry, '{last_used}', to_jsonb(NOW()));
          v_common_values = jsonb_set(v_common_values, ARRAY[i::text], v_value_entry);
          v_value_found = TRUE;
          EXIT;
        END IF;
      END LOOP;

      -- Add new value if not found
      IF NOT v_value_found THEN
        v_common_values = v_common_values || jsonb_build_object(
          'value', v_field_value,
          'frequency', 1,
          'last_used', NOW()
        );
      END IF;

      -- Keep only top 20 most frequent values
      v_common_values = (
        SELECT jsonb_agg(value ORDER BY (value->>'frequency')::integer DESC)
        FROM jsonb_array_elements(v_common_values) AS value
        LIMIT 20
      );

      -- Update the record
      UPDATE field_usage_statistics
      SET common_values = v_common_values,
          updated_at = NOW()
      WHERE department_id = p_department_id AND field_id = v_field_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
