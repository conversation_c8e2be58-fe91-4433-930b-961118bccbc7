// Database Query Optimization Module
// Strategic indexing and query optimization for enterprise-grade performance

import { createClient } from '@/lib/supabase/client';

export class DatabaseOptimizer {
  private static instance: DatabaseOptimizer;
  private queryCache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 60000; // 1 minute cache TTL

  private constructor() {}

  static getInstance(): DatabaseOptimizer {
    if (!DatabaseOptimizer.instance) {
      DatabaseOptimizer.instance = new DatabaseOptimizer();
    }
    return DatabaseOptimizer.instance;
  }

  // Create strategic indexes for frequently queried columns
  async createPerformanceIndexes() {
    const supabase = createClient();
    
    const indexes = [
      // User queries
      'CREATE INDEX IF NOT EXISTS idx_staff_department_id ON staff(department_id)',
      'CREATE INDEX IF NOT EXISTS idx_staff_group_id ON staff(group_id)',
      'CREATE INDEX IF NOT EXISTS idx_staff_email ON staff(email)',
      'CREATE INDEX IF NOT EXISTS idx_staff_active ON staff(is_active)',
      
      // Request forms
      'CREATE INDEX IF NOT EXISTS idx_request_forms_requester_id ON request_forms(requester_id)',
      'CREATE INDEX IF NOT EXISTS idx_request_forms_status ON request_forms(status)',
      'CREATE INDEX IF NOT EXISTS idx_request_forms_created_at ON request_forms(created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_request_forms_priority ON request_forms(priority)',
      
      // Request items
      'CREATE INDEX IF NOT EXISTS idx_request_items_form_id ON request_items(request_form_id)',
      'CREATE INDEX IF NOT EXISTS idx_request_items_status ON request_items(status)',
      'CREATE INDEX IF NOT EXISTS idx_request_items_service_category ON request_items(service_category_id)',
      
      // Workflow instances
      'CREATE INDEX IF NOT EXISTS idx_workflow_instances_status ON workflow_instances(status)',
      'CREATE INDEX IF NOT EXISTS idx_workflow_instances_request_id ON workflow_instances(request_id)',
      'CREATE INDEX IF NOT EXISTS idx_workflow_instances_sla_breach ON workflow_instances(sla_breach_time)',
    ];

    console.log('Creating performance indexes...');
    
    for (const indexQuery of indexes) {
      try {
        await supabase.rpc('execute_sql', { query: indexQuery });
        console.log(`✓ Created index: ${indexQuery.match(/idx_\w+/)?.[0]}`);
      } catch (error) {
        console.error(`Error creating index: ${error}`);
      }
    }
  }

  // Optimized query with caching
  async cachedQuery<T>(
    key: string,
    queryFn: () => Promise<{ data: T | null; error: any }>
  ): Promise<{ data: T | null; error: any }> {
    // Check cache first
    const cached = this.queryCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return { data: cached.data, error: null };
    }

    // Execute query
    const result = await queryFn();
    
    // Cache successful results
    if (result.data && !result.error) {
      this.queryCache.set(key, {
        data: result.data,
        timestamp: Date.now()
      });
    }

    return result;
  }

  // Invalidate cache for specific patterns
  invalidateCache(pattern?: string) {
    if (!pattern) {
      this.queryCache.clear();
      return;
    }

    for (const key of this.queryCache.keys()) {
      if (key.includes(pattern)) {
        this.queryCache.delete(key);
      }
    }
  }
}
