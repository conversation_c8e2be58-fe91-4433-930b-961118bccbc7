(()=>{var e={};e.id=3011,e.ids=[3011],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},64410:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>d});var s=r(70260),n=r(28203),i=r(25155),o=r.n(i),a=r(67292),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41419)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],p=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9229:(e,t,r)=>{Promise.resolve().then(r.bind(r,41419))},7021:(e,t,r)=>{Promise.resolve().then(r.bind(r,10543))},10543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(45512),n=r(87021);function i(){return(0,s.jsxs)("main",{className:"flex min-h-screen flex-col items-center justify-center p-24 gap-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"shadcn/ui Integration Test"}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n.$,{children:"Default Button"}),(0,s.jsx)(n.$,{variant:"secondary",children:"Secondary"}),(0,s.jsx)(n.$,{variant:"outline",children:"Outline"}),(0,s.jsx)(n.$,{variant:"destructive",children:"Destructive"}),(0,s.jsx)(n.$,{variant:"ghost",children:"Ghost"}),(0,s.jsx)(n.$,{variant:"link",children:"Link"})]}),(0,s.jsxs)("div",{className:"flex gap-4 mt-4",children:[(0,s.jsx)(n.$,{size:"sm",children:"Small"}),(0,s.jsx)(n.$,{size:"default",children:"Default"}),(0,s.jsx)(n.$,{size:"lg",children:"Large"}),(0,s.jsx)(n.$,{size:"icon",children:"\uD83C\uDFAF"})]})]})}},41419:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>r(64410));module.exports=s})();