/**
 * Security Headers Middleware
 * 
 * This middleware adds security headers to API responses to protect against
 * common web vulnerabilities like XSS, clickjacking, and content sniffing.
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { ErrorService, ErrorSeverity, ErrorCategory } from '../services/error-service';

/**
 * Security header configuration options
 */
export interface SecurityHeadersOptions {
  contentSecurityPolicy?: boolean | string;
  xFrameOptions?: boolean | string;
  xContentTypeOptions?: boolean | string;
  referrerPolicy?: boolean | string;
  permissionsPolicy?: boolean | string;
  strictTransportSecurity?: boolean | string;
  xXssProtection?: boolean | string;
  cacheControl?: boolean | string;
  crossOriginEmbedderPolicy?: boolean | string;
  crossOriginOpenerPolicy?: boolean | string;
  crossOriginResourcePolicy?: boolean | string;
  originAgentCluster?: boolean;
  reportTo?: boolean | string;
}

/**
 * Default security header values
 */
const DEFAULT_SECURITY_HEADERS: Record<string, string> = {
  // Content Security Policy
  'Content-Security-Policy': 
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' https://trusted-cdn.com; " +
    "style-src 'self' 'unsafe-inline' https://trusted-cdn.com; " +
    "img-src 'self' data: https://trusted-cdn.com; " +
    "connect-src 'self' https://*.supabase.co https://api.example.com; " +
    "frame-ancestors 'none'; " +
    "form-action 'self';",
  
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Control referrer information
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Control browser features
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  
  // Enforce HTTPS
  'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
  
  // Prevent XSS attacks
  'X-XSS-Protection': '1; mode=block',
  
  // Control caching
  'Cache-Control': 'no-store, max-age=0',
  
  // Cross-Origin isolation
  'Cross-Origin-Embedder-Policy': 'require-corp',
  'Cross-Origin-Opener-Policy': 'same-origin',
  'Cross-Origin-Resource-Policy': 'same-origin',
  
  // Origin isolation
  'Origin-Agent-Cluster': '?1',
  
  // Reporting API
  'Report-To': '{"group":"default","max_age":31536000,"endpoints":[{"url":"/api/csp-report"}],"include_subdomains":true}'
};

/**
 * Map of option keys to header names
 */
const OPTION_TO_HEADER_MAP: Record<string, string> = {
  contentSecurityPolicy: 'Content-Security-Policy',
  xFrameOptions: 'X-Frame-Options',
  xContentTypeOptions: 'X-Content-Type-Options',
  referrerPolicy: 'Referrer-Policy',
  permissionsPolicy: 'Permissions-Policy',
  strictTransportSecurity: 'Strict-Transport-Security',
  xXssProtection: 'X-XSS-Protection',
  cacheControl: 'Cache-Control',
  crossOriginEmbedderPolicy: 'Cross-Origin-Embedder-Policy',
  crossOriginOpenerPolicy: 'Cross-Origin-Opener-Policy',
  crossOriginResourcePolicy: 'Cross-Origin-Resource-Policy',
  originAgentCluster: 'Origin-Agent-Cluster',
  reportTo: 'Report-To'
};

/**
 * Middleware to add security headers to responses
 */
export function securityHeaders(
  req: NextRequest,
  options: SecurityHeadersOptions = {}
) {
  try {
    // Get the next response
    const response = NextResponse.next();
    
    // Add security headers based on options
    Object.entries(OPTION_TO_HEADER_MAP).forEach(([optionKey, headerName]) => {
      const optionValue = options[optionKey as keyof SecurityHeadersOptions];
      
      // Skip if option is explicitly set to false
      if (optionValue === false) {
        return;
      }
      
      // Use custom value if provided as string
      if (typeof optionValue === 'string') {
        response.headers.set(headerName, optionValue);
        return;
      }
      
      // Use default value if option is true or undefined
      if (optionValue === true || optionValue === undefined) {
        const defaultValue = DEFAULT_SECURITY_HEADERS[headerName];
        if (defaultValue) {
          response.headers.set(headerName, defaultValue);
        }
      }
    });
    
    // Special case for HSTS header in non-production environments
    if (process.env.NODE_ENV !== 'production' && !options.strictTransportSecurity) {
      response.headers.delete('Strict-Transport-Security');
    }
    
    return response;
  } catch (error) {
    // Log error but don't block the response
    ErrorService.logError(
      error instanceof Error ? error : new Error(String(error)),
      ErrorSeverity.ERROR,
      ErrorCategory.SECURITY,
      { path: req.url, action: 'security_headers' }
    );
    
    // Return response without security headers in case of error
    return NextResponse.next();
  }
}

/**
 * Helper function to create a Content-Security-Policy header value
 */
export function createCSP(options: {
  defaultSrc?: string[];
  scriptSrc?: string[];
  styleSrc?: string[];
  imgSrc?: string[];
  connectSrc?: string[];
  fontSrc?: string[];
  objectSrc?: string[];
  mediaSrc?: string[];
  frameSrc?: string[];
  frameAncestors?: string[];
  formAction?: string[];
  baseUri?: string[];
  reportUri?: string;
  reportTo?: string;
}): string {
  const directives: string[] = [];
  
  // Add directives based on options
  if (options.defaultSrc) {
    directives.push(`default-src ${options.defaultSrc.join(' ')}`);
  }
  
  if (options.scriptSrc) {
    directives.push(`script-src ${options.scriptSrc.join(' ')}`);
  }
  
  if (options.styleSrc) {
    directives.push(`style-src ${options.styleSrc.join(' ')}`);
  }
  
  if (options.imgSrc) {
    directives.push(`img-src ${options.imgSrc.join(' ')}`);
  }
  
  if (options.connectSrc) {
    directives.push(`connect-src ${options.connectSrc.join(' ')}`);
  }
  
  if (options.fontSrc) {
    directives.push(`font-src ${options.fontSrc.join(' ')}`);
  }
  
  if (options.objectSrc) {
    directives.push(`object-src ${options.objectSrc.join(' ')}`);
  }
  
  if (options.mediaSrc) {
    directives.push(`media-src ${options.mediaSrc.join(' ')}`);
  }
  
  if (options.frameSrc) {
    directives.push(`frame-src ${options.frameSrc.join(' ')}`);
  }
  
  if (options.frameAncestors) {
    directives.push(`frame-ancestors ${options.frameAncestors.join(' ')}`);
  }
  
  if (options.formAction) {
    directives.push(`form-action ${options.formAction.join(' ')}`);
  }
  
  if (options.baseUri) {
    directives.push(`base-uri ${options.baseUri.join(' ')}`);
  }
  
  if (options.reportUri) {
    directives.push(`report-uri ${options.reportUri}`);
  }
  
  if (options.reportTo) {
    directives.push(`report-to ${options.reportTo}`);
  }
  
  return directives.join('; ');
}

/**
 * Example usage:
 * 
 * // In middleware.ts
 * import { NextResponse } from 'next/server';
 * import type { NextRequest } from 'next/server';
 * import { securityHeaders } from './lib/middleware/security-headers';
 * 
 * export function middleware(request: NextRequest) {
 *   // Apply security headers
 *   return securityHeaders(request);
 * }
 * 
 * // Specify which routes to apply the middleware to
 * export const config = {
 *   matcher: ['/api/:path*', '/((?!_next/static|_next/image|favicon.ico).*)'],
 * };
 */