(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35644:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(70260),n=t(28203),o=t(25155),a=t.n(o),i=t(67292),p={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);t.d(r,p);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35104)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},31595:(e,r,t)=>{Promise.resolve().then(t.bind(t,35104))},30915:(e,r,t)=>{Promise.resolve().then(t.bind(t,30676))},30676:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(45512);t(58009);var n=t(79334),o=t(43433),a=t(97643),i=t(88502);function p(){let{user:e,loading:r}=(0,o.A)();return(0,n.useRouter)(),(0,s.jsx)("main",{className:"flex min-h-screen flex-col items-center justify-center p-24",children:(0,s.jsx)(a.Zp,{children:(0,s.jsxs)(a.Wu,{className:"p-8 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,s.jsx)(i.k,{className:"h-6 w-6 animate-spin"}),(0,s.jsx)("span",{className:"text-lg",children:"読み込み中... / Loading..."})]}),(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"ITSync"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"Enterprise IT Helpdesk & Support Platform"}),(0,s.jsx)("p",{className:"mt-4 text-gray-500",children:"AI-powered, Database-driven IT Support"})]})})})}},35104:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\page.tsx","default")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(35644));module.exports=s})();