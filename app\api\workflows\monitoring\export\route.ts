import { NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'
import { format } from 'date-fns'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'
    const format = searchParams.get('format') || 'csv'
    
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case '24h':
        startDate.setDate(now.getDate() - 1)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      default:
        startDate.setDate(now.getDate() - 7)
    }

    // Fetch workflow data with relations
    const { data: workflows, error } = await supabase
      .from('workflow_instances')
      .select(`
        id,
        status,
        priority,
        created_at,
        updated_at,
        completed_at,
        request_forms!workflow_instances_context_fkey (
          title,
          staff!request_forms_requester_id_fkey (
            name_en,
            name_jp,
            divisions!staff_division_id_fkey (
              name_en,
              name_jp
            )
          ),
          service_categories!request_forms_service_category_id_fkey (
            name_en,
            name_jp
          )
        ),
        sla_tracking (
          sla_status,
          target_date
        )
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', now.toISOString())
      .order('created_at', { ascending: false })

    if (error) throw error

    if (format === 'csv') {
      // Generate CSV
      const headers = [
        'Workflow ID',
        'Title',
        'Status',
        'Priority',
        'Department',
        'Category',
        'Requester',
        'SLA Status',
        'Created At',
        'Updated At',
        'Completed At'
      ]

      const rows = workflows?.map(wf => {
        const requestForm = wf.request_forms?.[0]
        const staff = requestForm?.staff?.[0]
        const division = staff?.divisions?.[0]
        const category = requestForm?.service_categories?.[0]
        const slaTracking = wf.sla_tracking?.[0]

        return [
          wf.id,
          requestForm?.title || '',
          wf.status,
          wf.priority || 'medium',
          division?.name_en || '',
          category?.name_en || '',
          staff?.name_en || '',
          slaTracking?.sla_status || 'on_track',
          wf.created_at,
          wf.updated_at,
          wf.completed_at || ''
        ]
      }) || []

      const csv = [
        headers.join(','),
        ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n')

      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="workflow-monitoring-${new Date().toISOString()}.csv"`
        }
      })
    } else {
      // Return JSON
      return NextResponse.json(workflows)
    }
  } catch (error) {
    console.error('Error exporting workflow data:', error)
    return NextResponse.json(
      { error: 'Failed to export workflow data' },
      { status: 500 }
    )
  }
}
