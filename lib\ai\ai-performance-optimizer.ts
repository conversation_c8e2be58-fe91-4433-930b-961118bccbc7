/**
 * AI Performance Optimizer
 * Provides caching, batching, and cost optimization for AI services
 */

import { getCacheService } from '@/lib/cache/cache-service'
import { trackAIMetrics } from '@/lib/monitoring/metrics'
import { openaiClient } from './openai-client'
import { anthropicClient } from './anthropic-client'

interface AIRequest {
  id: string
  provider: 'openai' | 'anthropic'
  model: string
  prompt: string
  options: any
  priority: 'low' | 'normal' | 'high'
  timestamp: number
  retries: number
}

interface AIResponse {
  id: string
  content: string
  usage: {
    inputTokens: number
    outputTokens: number
    totalTokens: number
  }
  cost: number
  duration: number
  fromCache: boolean
  provider: string
  model: string
}

interface BatchRequest {
  requests: AIRequest[]
  batchId: string
  createdAt: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
}

interface CostTracker {
  daily: { [date: string]: number }
  monthly: { [month: string]: number }
  byProvider: { [provider: string]: number }
  byModel: { [model: string]: number }
  total: number
}

class AIPerformanceOptimizer {
  private cache = getCacheService()
  private requestQueue: AIRequest[] = []
  private batchQueue: BatchRequest[] = []
  private costTracker: CostTracker = {
    daily: {},
    monthly: {},
    byProvider: {},
    byModel: {},
    total: 0
  }
  private readonly batchSize = 10
  private readonly batchTimeout = 5000 // 5 seconds
  private readonly maxRetries = 3
  private readonly cacheTTL = 3600 // 1 hour
  private processingBatch = false

  constructor() {
    // Process batch queue periodically
    setInterval(() => this.processBatchQueue(), this.batchTimeout)
    
    // Load cost tracker from storage
    this.loadCostTracker()
  }

  /**
   * Optimized AI request with caching and batching
   */
  async request(
    provider: 'openai' | 'anthropic',
    model: string,
    prompt: string,
    options: any = {},
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<AIResponse> {
    const requestId = this.generateRequestId()
    const startTime = Date.now()

    // Check cache first
    const cacheKey = this.generateCacheKey(provider, model, prompt, options)
    const cached = await this.cache.get<AIResponse>(cacheKey)
    
    if (cached) {
      // Update metrics for cache hit
      trackAIMetrics(provider, model, Date.now() - startTime, cached.usage.totalTokens, cached.cost)
      
      return {
        ...cached,
        fromCache: true,
        duration: Date.now() - startTime
      }
    }

    // For high priority requests, process immediately
    if (priority === 'high') {
      return this.processRequest({
        id: requestId,
        provider,
        model,
        prompt,
        options,
        priority,
        timestamp: startTime,
        retries: 0
      })
    }

    // For normal/low priority, add to queue for batching
    return new Promise((resolve, reject) => {
      const request: AIRequest = {
        id: requestId,
        provider,
        model,
        prompt,
        options: {
          ...options,
          resolve,
          reject
        },
        priority,
        timestamp: startTime,
        retries: 0
      }

      this.requestQueue.push(request)
      
      // If queue is full or has high priority items, process immediately
      if (this.requestQueue.length >= this.batchSize || priority === 'normal') {
        this.processBatchQueue()
      }
    })
  }

  /**
   * Process individual AI request
   */
  private async processRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now()
    
    try {
      let response: any
      let usage: any
      let cost: number

      if (request.provider === 'openai') {
        response = await this.callOpenAI(request)
        usage = response.usage
        cost = this.calculateOpenAICost(request.model, usage)
      } else {
        response = await this.callAnthropic(request)
        usage = response.usage
        cost = this.calculateAnthropicCost(request.model, usage)
      }

      const duration = Date.now() - startTime
      const content = this.extractContent(response, request.provider)

      const aiResponse: AIResponse = {
        id: request.id,
        content,
        usage: {
          inputTokens: usage.prompt_tokens || usage.input_tokens || 0,
          outputTokens: usage.completion_tokens || usage.output_tokens || 0,
          totalTokens: usage.total_tokens || (usage.input_tokens + usage.output_tokens) || 0
        },
        cost,
        duration,
        fromCache: false,
        provider: request.provider,
        model: request.model
      }

      // Cache successful responses
      const cacheKey = this.generateCacheKey(request.provider, request.model, request.prompt, request.options)
      await this.cache.set(cacheKey, aiResponse, {
        ttl: this.cacheTTL,
        tags: ['ai_responses', request.provider, request.model]
      })

      // Track metrics and costs
      trackAIMetrics(request.provider, request.model, duration, aiResponse.usage.totalTokens, cost)
      this.updateCostTracker(request.provider, request.model, cost)

      return aiResponse

    } catch (error) {
      // Handle retries
      if (request.retries < this.maxRetries) {
        request.retries++
        await this.delay(Math.pow(2, request.retries) * 1000) // Exponential backoff
        return this.processRequest(request)
      }

      const duration = Date.now() - startTime
      trackAIMetrics(request.provider, request.model, duration, 0, 0)
      
      throw error
    }
  }

  /**
   * Process batch queue
   */
  private async processBatchQueue(): Promise<void> {
    if (this.processingBatch || this.requestQueue.length === 0) {
      return
    }

    this.processingBatch = true

    try {
      // Group requests by provider and model for optimal batching
      const batches = this.groupRequestsForBatching()
      
      for (const batch of batches) {
        await this.processBatch(batch)
      }
    } finally {
      this.processingBatch = false
    }
  }

  /**
   * Group requests for optimal batching
   */
  private groupRequestsForBatching(): AIRequest[][] {
    const groups: { [key: string]: AIRequest[] } = {}
    
    // Take requests from queue
    const requests = this.requestQueue.splice(0, this.batchSize * 3) // Process up to 3 batches
    
    // Group by provider and model
    for (const request of requests) {
      const key = `${request.provider}:${request.model}`
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(request)
    }

    // Split large groups into batches
    const batches: AIRequest[][] = []
    for (const group of Object.values(groups)) {
      for (let i = 0; i < group.length; i += this.batchSize) {
        batches.push(group.slice(i, i + this.batchSize))
      }
    }

    return batches
  }

  /**
   * Process a batch of requests
   */
  private async processBatch(requests: AIRequest[]): Promise<void> {
    const promises = requests.map(request => 
      this.processRequest(request)
        .then(response => {
          if (request.options.resolve) {
            request.options.resolve(response)
          }
        })
        .catch(error => {
          if (request.options.reject) {
            request.options.reject(error)
          }
        })
    )

    await Promise.allSettled(promises)
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(request: AIRequest): Promise<any> {
    return openaiClient.chat.completions.create({
      model: request.model,
      messages: [{ role: 'user', content: request.prompt }],
      ...request.options
    })
  }

  /**
   * Call Anthropic API
   */
  private async callAnthropic(request: AIRequest): Promise<any> {
    return anthropicClient.messages.create({
      model: request.model,
      max_tokens: request.options.max_tokens || 1000,
      messages: [{ role: 'user', content: request.prompt }],
      ...request.options
    })
  }

  /**
   * Extract content from AI response
   */
  private extractContent(response: any, provider: string): string {
    if (provider === 'openai') {
      return response.choices[0]?.message?.content || ''
    } else {
      return response.content[0]?.text || ''
    }
  }

  /**
   * Calculate OpenAI cost
   */
  private calculateOpenAICost(model: string, usage: any): number {
    const pricing: { [key: string]: { input: number; output: number } } = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 }
    }

    const modelPricing = pricing[model] || pricing['gpt-3.5-turbo']
    const inputCost = (usage.prompt_tokens / 1000) * modelPricing.input
    const outputCost = (usage.completion_tokens / 1000) * modelPricing.output
    
    return inputCost + outputCost
  }

  /**
   * Calculate Anthropic cost
   */
  private calculateAnthropicCost(model: string, usage: any): number {
    const pricing: { [key: string]: { input: number; output: number } } = {
      'claude-3-opus': { input: 0.015, output: 0.075 },
      'claude-3-sonnet': { input: 0.003, output: 0.015 },
      'claude-3-haiku': { input: 0.00025, output: 0.00125 }
    }

    const modelPricing = pricing[model] || pricing['claude-3-haiku']
    const inputCost = (usage.input_tokens / 1000) * modelPricing.input
    const outputCost = (usage.output_tokens / 1000) * modelPricing.output
    
    return inputCost + outputCost
  }

  /**
   * Update cost tracker
   */
  private updateCostTracker(provider: string, model: string, cost: number): void {
    const today = new Date().toISOString().split('T')[0]
    const month = today.substring(0, 7)

    this.costTracker.daily[today] = (this.costTracker.daily[today] || 0) + cost
    this.costTracker.monthly[month] = (this.costTracker.monthly[month] || 0) + cost
    this.costTracker.byProvider[provider] = (this.costTracker.byProvider[provider] || 0) + cost
    this.costTracker.byModel[model] = (this.costTracker.byModel[model] || 0) + cost
    this.costTracker.total += cost

    // Save to storage
    this.saveCostTracker()
  }

  /**
   * Get cost analytics
   */
  getCostAnalytics(): any {
    const today = new Date().toISOString().split('T')[0]
    const month = today.substring(0, 7)

    return {
      today: this.costTracker.daily[today] || 0,
      thisMonth: this.costTracker.monthly[month] || 0,
      total: this.costTracker.total,
      byProvider: this.costTracker.byProvider,
      byModel: this.costTracker.byModel,
      trends: this.calculateCostTrends()
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    const cacheStats = await this.cache.getStats()
    
    return {
      queue: {
        pending: this.requestQueue.length,
        processing: this.processingBatch
      },
      cache: {
        hitRate: cacheStats.redis?.hitRate || 0,
        size: cacheStats.memory?.size || 0
      },
      costs: this.getCostAnalytics()
    }
  }

  /**
   * Clear AI response cache
   */
  async clearCache(): Promise<void> {
    await this.cache.invalidateByTags(['ai_responses'])
  }

  // Private helper methods
  private generateRequestId(): string {
    return `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateCacheKey(provider: string, model: string, prompt: string, options: any): string {
    const optionsHash = this.hashObject(options)
    const promptHash = this.hashString(prompt)
    return `ai:${provider}:${model}:${promptHash}:${optionsHash}`
  }

  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(36)
  }

  private hashObject(obj: any): string {
    return this.hashString(JSON.stringify(obj))
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private calculateCostTrends(): any {
    // Calculate daily trends for the last 7 days
    const trends: any = {}
    const today = new Date()
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      trends[dateStr] = this.costTracker.daily[dateStr] || 0
    }
    
    return trends
  }

  private loadCostTracker(): void {
    // In a real implementation, load from persistent storage
    // For now, keep in memory
  }

  private saveCostTracker(): void {
    // In a real implementation, save to persistent storage
    // For now, keep in memory
  }
}

// Singleton instance
let aiOptimizer: AIPerformanceOptimizer | null = null

export const getAIOptimizer = (): AIPerformanceOptimizer => {
  if (!aiOptimizer) {
    aiOptimizer = new AIPerformanceOptimizer()
  }
  return aiOptimizer
}

export { AIPerformanceOptimizer }
export type { AIRequest, AIResponse, CostTracker }
