-- Batch Processing Tables for Multi-User Request Handling

-- Batch operation groups for atomic processing
CREATE TABLE batch_operations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  batch_id VARCHAR(20) UNIQUE NOT NULL DEFAULT 'BAT' || LPAD(nextval('batch_seq')::text, 8, '0'),
  requester_id UUID REFERENCES staff(id) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed, partially_completed
  operation_type VARCHAR(50) NOT NULL, -- multi_user_single_service, multi_user_multi_service, etc.
  total_items INTEGER NOT NULL DEFAULT 0,
  completed_items INTEGER NOT NULL DEFAULT 0,
  failed_items INTEGER NOT NULL DEFAULT 0,
  error_details JSONB DEFAULT '[]',
  progress_percentage DECIMAL(5,2) DEFAULT 0.00,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sequence for batch IDs
CREATE SEQUENCE IF NOT EXISTS batch_seq START WITH 1;

-- Batch items for tracking individual operations within a batch
CREATE TABLE batch_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  batch_operation_id UUID REFERENCES batch_operations(id) ON DELETE CASCADE,
  request_item_id UUID REFERENCES request_items(id),
  item_order INTEGER NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed, skipped
  error_message TEXT,
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Batch validation rules for pre-processing checks
CREATE TABLE batch_validation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  service_category_id UUID REFERENCES service_categories(id),
  rule_type VARCHAR(50) NOT NULL, -- permission_check, resource_availability, duplicate_check
  rule_config JSONB NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Batch progress logs for detailed tracking
CREATE TABLE batch_progress_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  batch_operation_id UUID REFERENCES batch_operations(id) ON DELETE CASCADE,
  log_level VARCHAR(20) NOT NULL, -- info, warning, error
  message TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indices for performance
CREATE INDEX idx_batch_operations_requester_id ON batch_operations(requester_id);
CREATE INDEX idx_batch_operations_status ON batch_operations(status);
CREATE INDEX idx_batch_operations_batch_id ON batch_operations(batch_id);
CREATE INDEX idx_batch_items_batch_operation_id ON batch_items(batch_operation_id);
CREATE INDEX idx_batch_items_status ON batch_items(status);
CREATE INDEX idx_batch_progress_logs_batch_operation_id ON batch_progress_logs(batch_operation_id);

-- Function to update batch operation progress
CREATE OR REPLACE FUNCTION update_batch_progress()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE batch_operations
  SET 
    completed_items = (
      SELECT COUNT(*) FROM batch_items 
      WHERE batch_operation_id = NEW.batch_operation_id 
      AND status = 'completed'
    ),
    failed_items = (
      SELECT COUNT(*) FROM batch_items 
      WHERE batch_operation_id = NEW.batch_operation_id 
      AND status = 'failed'
    ),
    progress_percentage = (
      SELECT 
        CASE WHEN total_items > 0 
        THEN ROUND((COUNT(*) FILTER (WHERE status IN ('completed', 'failed', 'skipped'))::numeric / total_items * 100), 2)
        ELSE 0
        END
      FROM batch_items 
      WHERE batch_operation_id = NEW.batch_operation_id
    ),
    updated_at = NOW()
  WHERE id = NEW.batch_operation_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update batch progress
CREATE TRIGGER batch_item_status_update
AFTER UPDATE OF status ON batch_items
FOR EACH ROW
WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION update_batch_progress();

-- Function for atomic batch transaction processing
CREATE OR REPLACE FUNCTION process_batch_transaction(
  p_batch_operation_id UUID,
  p_request_items UUID[]
) RETURNS JSONB AS $$
DECLARE
  v_result JSONB := '{"success": false, "message": "", "processed_items": []}';
  v_item_id UUID;
  v_error_count INTEGER := 0;
BEGIN
  -- Start transaction
  UPDATE batch_operations 
  SET status = 'processing', started_at = NOW() 
  WHERE id = p_batch_operation_id;
  
  -- Process each item
  FOREACH v_item_id IN ARRAY p_request_items
  LOOP
    BEGIN
      -- Update batch item status
      UPDATE batch_items 
      SET status = 'processing' 
      WHERE batch_operation_id = p_batch_operation_id 
      AND request_item_id = v_item_id;
      
      -- Process the actual request item
      UPDATE request_items 
      SET status = 'processing', updated_at = NOW() 
      WHERE id = v_item_id;
      
      -- Simulate processing (in real implementation, call actual service handlers)
      -- This would be replaced with actual business logic
      
      -- Mark as completed
      UPDATE batch_items 
      SET status = 'completed', processed_at = NOW() 
      WHERE batch_operation_id = p_batch_operation_id 
      AND request_item_id = v_item_id;
      
      UPDATE request_items 
      SET status = 'completed', completed_at = NOW() 
      WHERE id = v_item_id;
      
    EXCEPTION WHEN OTHERS THEN
      -- Handle errors
      v_error_count := v_error_count + 1;
      
      UPDATE batch_items 
      SET status = 'failed', 
          error_message = SQLERRM,
          processed_at = NOW() 
      WHERE batch_operation_id = p_batch_operation_id 
      AND request_item_id = v_item_id;
      
      -- Log the error
      INSERT INTO batch_progress_logs (batch_operation_id, log_level, message, details)
      VALUES (p_batch_operation_id, 'error', 'Failed to process item', 
              jsonb_build_object('item_id', v_item_id, 'error', SQLERRM));
    END;
  END LOOP;
  
  -- Update batch operation status
  IF v_error_count = 0 THEN
    UPDATE batch_operations 
    SET status = 'completed', completed_at = NOW() 
    WHERE id = p_batch_operation_id;
    
    v_result := jsonb_build_object(
      'success', true,
      'message', 'Batch processed successfully',
      'processed_items', array_length(p_request_items, 1)
    );
  ELSIF v_error_count = array_length(p_request_items, 1) THEN
    UPDATE batch_operations 
    SET status = 'failed', completed_at = NOW() 
    WHERE id = p_batch_operation_id;
    
    v_result := jsonb_build_object(
      'success', false,
      'message', 'All items in batch failed',
      'processed_items', 0
    );
  ELSE
    UPDATE batch_operations 
    SET status = 'partially_completed', completed_at = NOW() 
    WHERE id = p_batch_operation_id;
    
    v_result := jsonb_build_object(
      'success', true,
      'message', 'Batch partially completed',
      'processed_items', array_length(p_request_items, 1) - v_error_count
    );
  END IF;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to validate batch before processing
CREATE OR REPLACE FUNCTION validate_batch_request(
  p_requester_id UUID,
  p_request_items JSONB
) RETURNS JSONB AS $$
DECLARE
  v_result JSONB := '{"valid": true, "errors": []}';
  v_errors JSONB[] := '{}';
  v_item JSONB;
  v_user_dept_id UUID;
  v_affected_user_dept_id UUID;
BEGIN
  -- Get requester's department
  SELECT division_id INTO v_user_dept_id 
  FROM staff 
  WHERE id = p_requester_id;
  
  -- Validate each request item
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_request_items)
  LOOP
    -- Check if affected user belongs to same department
    SELECT division_id INTO v_affected_user_dept_id 
    FROM staff 
    WHERE id = (v_item->>'affected_user_id')::UUID;
    
    IF v_affected_user_dept_id != v_user_dept_id THEN
      v_errors := array_append(v_errors, jsonb_build_object(
        'item', v_item,
        'error', 'User does not belong to your department'
      ));
    END IF;
    
    -- Add more validation rules as needed
  END LOOP;
  
  IF array_length(v_errors, 1) > 0 THEN
    v_result := jsonb_build_object(
      'valid', false,
      'errors', v_errors
    );
  END IF;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- RLS policies for batch operations
ALTER TABLE batch_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_progress_logs ENABLE ROW LEVEL SECURITY;

-- Policy for batch operations (users can see their own batches)
CREATE POLICY batch_operations_policy ON batch_operations
  FOR ALL
  TO authenticated
  USING (
    requester_id = auth.uid()
    OR EXISTS (
      SELECT 1 FROM staff s
      JOIN roles r ON s.role_id = r.id
      WHERE s.auth_id = auth.uid()
      AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
    )
  );

-- Policy for batch items (same as batch operations)
CREATE POLICY batch_items_policy ON batch_items
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM batch_operations bo
      WHERE bo.id = batch_items.batch_operation_id
      AND (
        bo.requester_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM staff s
          JOIN roles r ON s.role_id = r.id
          WHERE s.auth_id = auth.uid()
          AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
        )
      )
    )
  );

-- Policy for batch progress logs
CREATE POLICY batch_progress_logs_policy ON batch_progress_logs
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM batch_operations bo
      WHERE bo.id = batch_progress_logs.batch_operation_id
      AND (
        bo.requester_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM staff s
          JOIN roles r ON s.role_id = r.id
          WHERE s.auth_id = auth.uid()
          AND r.name IN ('Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support')
        )
      )
    )
  );
