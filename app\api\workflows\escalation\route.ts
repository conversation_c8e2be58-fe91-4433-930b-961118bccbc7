import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { EscalationRulesManager } from '@/lib/services/workflow/escalation/escalation-rules-manager';
import { auditService } from '@/lib/services/audit';

export async function GET(request: Request) {
  try {
    const supabase = createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const filters = {
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      triggerType: searchParams.get('triggerType') || undefined,
      priority: searchParams.get('priority') || undefined,
      categoryId: searchParams.get('categoryId') || undefined,
      departmentId: searchParams.get('departmentId') || undefined,
    };

    const rulesManager = new EscalationRulesManager(supabase, auditService);

    const rules = await rulesManager.getRules(filters);

    return NextResponse.json({ rules });
  } catch (error) {
    console.error('Error fetching escalation rules:', error);
    return NextResponse.json(
      { error: 'Failed to fetch escalation rules' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = createClient();

    // Check authentication and authorization
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user role
    const { data: staff } = await supabase
      .from('staff')
      .select('*, roles(*)')
      .eq('auth_id', user.id)
      .single();

    if (!staff || !['Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support'].includes(staff.roles.name)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const rulesManager = new EscalationRulesManager(supabase, auditService);

    // Validate rule
    const validation = rulesManager.validateRule(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Invalid rule', details: validation.errors },
        { status: 400 }
      );
    }

    const rule = await rulesManager.createRule({
      ...body,
      createdBy: staff.id,
    });

    return NextResponse.json({ rule }, { status: 201 });
  } catch (error) {
    console.error('Error creating escalation rule:', error);
    return NextResponse.json(
      { error: 'Failed to create escalation rule' },
      { status: 500 }
    );
  }
}
