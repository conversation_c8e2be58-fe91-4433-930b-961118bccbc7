import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { contentAutoUpdateService } from '@/lib/services/content-auto-update-service'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check authentication and permissions
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Check if user has IT Helpdesk Support or Admin role
    const { data: staffData } = await supabase
      .from('staff')
      .select('role:roles(name)')
      .eq('auth_id', user.id)
      .single()
    
    const allowedRoles = ['Global Administrator', 'Web App System Administrator', 'IT Helpdesk Support']
    if (!staffData || !allowedRoles.includes(staffData.role?.name)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }
    
    const body = await request.json()
    const { articleId, updateType = 'manual', forceUpdate = false } = body
    
    if (articleId) {
      // Update specific article
      await contentAutoUpdateService.updateArticleContent(articleId, updateType)
      
      return NextResponse.json({ 
        success: true, 
        message: `Article ${articleId} queued for update` 
      })
    } else {
      // Trigger periodic review
      await contentAutoUpdateService.schedulePeriodicReview()
      
      return NextResponse.json({ 
        success: true, 
        message: 'Periodic review initiated for all articles' 
      })
    }
  } catch (error: any) {
    console.error('Content update error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Get update queue status
    const { data: queueData, error: queueError } = await supabase
      .from('kb_update_queue')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (queueError) {
      throw new Error('Failed to fetch update queue')
    }
    
    // Get recent update logs
    const { data: logsData, error: logsError } = await supabase
      .from('kb_update_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (logsError) {
      throw new Error('Failed to fetch update logs')
    }
    
    // Get content gaps
    const { data: gapsData, error: gapsError } = await supabase
      .from('kb_content_gaps')
      .select('*')
      .eq('status', 'identified')
      .order('priority', { ascending: true })
      .limit(10)
    
    if (gapsError) {
      throw new Error('Failed to fetch content gaps')
    }
    
    return NextResponse.json({
      updateQueue: queueData || [],
      recentLogs: logsData || [],
      contentGaps: gapsData || [],
      timestamp: new Date().toISOString()
    })
  } catch (error: any) {
    console.error('Get update status error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}