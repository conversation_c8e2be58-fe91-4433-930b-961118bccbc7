exports.id=9389,exports.ids=[9389],exports.modules={96487:()=>{},78335:()=>{},68967:(e,t,a)=>{"use strict";a.d(t,{H:()=>r});class s{async logAction(e){try{console.log("Audit log entry:",{...e,timestamp:e.timestamp||new Date}),await new Promise(e=>setTimeout(e,100))}catch(e){console.error("Failed to log audit entry:",e)}}async getAuditLogs(e={}){try{return await new Promise(e=>setTimeout(e,200)),[{id:"1",user_id:"user1",action:"login",resource_type:"auth",timestamp:new Date,ip_address:"***********"}]}catch(e){return console.error("Failed to fetch audit logs:",e),[]}}async getUserActivity(e,t=50){return this.getAuditLogs({userId:e,limit:t})}async getResourceActivity(e,t){return this.getAuditLogs({resourceType:e})}async logLogin(e,t,a){await this.logAction({user_id:e,action:"login",resource_type:"auth",ip_address:t,user_agent:a})}async logLogout(e,t){await this.logAction({user_id:e,action:"logout",resource_type:"auth",ip_address:t})}async logPasswordReset(e,t){await this.logAction({user_id:e,action:"password_reset",resource_type:"auth",ip_address:t})}async logTicketCreated(e,t){await this.logAction({user_id:e,action:"create",resource_type:"ticket",resource_id:t})}async logTicketUpdated(e,t,a){await this.logAction({user_id:e,action:"update",resource_type:"ticket",resource_id:t,details:a})}async logWorkflowAction(e,t,a,s){await this.logAction({user_id:e,action:a,resource_type:"workflow",resource_id:t,details:s})}}let r=new s},92537:(e,t,a)=>{"use strict";a.d(t,{y:()=>w,i:()=>f});var s=a(61487),r=a(75971),i=a(55657);function n(e,t){let a=(0,i.a)(e);return a.setHours(t),a}function o(e,t){let a=(0,i.a)(e);return a.setMinutes(t),a}var l=a(66025);function u(e,t,a){var s;let r=(+(0,i.a)(e)-+(0,i.a)(t))/l.Cg;return(s=a?.roundingMethod,e=>{let t=(s?Math[s]:Math.trunc)(e);return 0===t?0:t})(r)}var c=a(3744),_=a(69353);function d(e,t){let a=(0,i.a)(e);return isNaN(t)?(0,_.w)(e,NaN):(t&&a.setDate(a.getDate()+t),a)}var g=a(68967);class w{async initializeSLA(e,t){let a=(0,s.U)(),{data:r}=await a.from("workflow_instances").select("*, request_forms!inner(*)").eq("id",e).single();if(!r)return;let{data:i}=await a.from("sla_definitions").select("*").eq("service_category_id",r.request_forms.service_category_id).eq("priority",r.request_forms.priority||"medium").single();if(!i){let{data:e}=await a.from("sla_definitions").select("*").is("service_category_id",null).eq("priority",r.request_forms.priority||"medium").single();e&&(i=e)}if(!i)return;let n=new Date,o=this.calculateTargetDate(n,i.response_time_minutes,i.business_hours_only),l=this.calculateTargetDate(n,i.resolution_time_minutes,i.business_hours_only);await a.from("sla_tracking").insert({workflow_instance_id:e,sla_definition_id:i.id,target_response_date:o.toISOString(),target_resolution_date:l.toISOString(),status:"on_track"}),await this.scheduleSLAMonitoring(e,i.id)}calculateDueDate(e,t=!0){let a=new Date;return e?this.calculateTargetDate(a,e,t):(0,r.z)(a,240)}calculateTargetDate(e,t,a){if(!a)return(0,r.z)(e,t);let s=new Date(e),i=t;for(;i>0;){if(this.isNonBusinessDay(s)){s=o(s=n(s=this.getNextBusinessDay(s),this.businessHours.start.hour),this.businessHours.start.minute);continue}let e=n(o(new Date(s),this.businessHours.start.minute),this.businessHours.start.hour),t=n(o(new Date(s),this.businessHours.end.minute),this.businessHours.end.hour);if(s<e)s=e;else if(s>=t){s=o(s=n(s=this.getNextBusinessDay(s),this.businessHours.start.hour),this.businessHours.start.minute);continue}let a=u(t,s);i<=a?(s=(0,r.z)(s,i),i=0):(i-=a,s=o(s=n(s=this.getNextBusinessDay(s),this.businessHours.start.hour),this.businessHours.start.minute))}return s}isNonBusinessDay(e){if(function(e){let t=(0,i.a)(e).getDay();return 0===t||6===t}(e))return!0;let t=(0,c.GP)(e,"yyyy-MM-dd");return this.holidays.some(e=>(0,c.GP)(e,"yyyy-MM-dd")===t)}getNextBusinessDay(e){let t=d(e,1);for(;this.isNonBusinessDay(t);)t=d(t,1);return t}async updateTaskCompletion(e){let t=(0,s.U)(),{data:a}=await t.from("workflow_tasks").select("*, workflow_instances!inner(*)").eq("id",e).single();if(!a)return;let{data:r}=await t.from("sla_tracking").select("*").eq("workflow_instance_id",a.workflow_instance_id).single();r&&!r.actual_response_date&&"response"===a.task_type&&await t.from("sla_tracking").update({actual_response_date:new Date().toISOString(),status:this.calculateSLAStatus(r,{responseCompleted:!0})}).eq("id",r.id)}async completeSLA(e){let t=(0,s.U)(),a=new Date,{data:r}=await t.from("sla_tracking").select("*").eq("workflow_instance_id",e).single();if(!r)return;let i={actual_resolution_date:a.toISOString()};r.actual_response_date||(i.actual_response_date=a.toISOString()),a<=new Date(r.target_resolution_date)?i.status="on_track":(i.status="breached",i.breach_reason="Resolution time exceeded"),await t.from("sla_tracking").update(i).eq("id",r.id),await g.auditLogger.log({action:"SLA_COMPLETED",details:{workflow_instance_id:e,status:i.status,response_time:r.actual_response_date?u(new Date(r.actual_response_date),new Date(r.created_at)):null,resolution_time:u(a,new Date(r.created_at))}})}calculateSLAStatus(e,t){let a=new Date;if(!e.actual_response_date&&!t?.responseCompleted){let t=new Date(e.target_response_date),s=u(t,a);if(a>t)return"breached";if(s<60)return"at_risk"}if(!e.actual_resolution_date&&!t?.resolutionCompleted){let t=new Date(e.target_resolution_date),s=u(t,a);if(a>t)return"breached";if(s<120)return"at_risk"}return"on_track"}async monitorSLA(){let e=(0,s.U)(),{data:t}=await e.from("sla_tracking").select("*, workflow_instances!inner(*), sla_definitions!inner(*)").is("actual_resolution_date",null);if(t)for(let a of t){let t=this.calculateSLAStatus(a);if(t!==a.status){if(await e.from("sla_tracking").update({status:t}).eq("id",a.id),"at_risk"===t)await this.triggerSLAAlert(a,"at_risk");else if("breached"===t){await this.triggerSLAAlert(a,"breached");let t=new Date,s=!a.actual_response_date&&t>new Date(a.target_response_date)?"Response time exceeded":"Resolution time exceeded";await e.from("sla_tracking").update({breach_reason:s}).eq("id",a.id)}}}}async triggerSLAAlert(e,t){console.log(`SLA Alert: ${t} for workflow ${e.workflow_instance_id}`),await g.auditLogger.log({action:"SLA_ALERT",details:{workflow_instance_id:e.workflow_instance_id,alert_type:t,sla_definition_id:e.sla_definition_id}})}async scheduleSLAMonitoring(e,t){console.log(`Scheduled SLA monitoring for workflow ${e}`)}async getSLAMetrics(e,t,a){let r=(0,s.U)().from("sla_tracking").select("*, sla_definitions!inner(*), workflow_instances!inner(*, request_forms!inner(*))").gte("created_at",e.toISOString()).lte("created_at",t.toISOString());a&&r.eq("workflow_instances.request_forms.department_id",a);let{data:i}=await r;if(!i)return null;let n={total:i.length,met:i.filter(e=>"breached"!==e.status).length,breached:i.filter(e=>"breached"===e.status).length,at_risk:i.filter(e=>"at_risk"===e.status).length,compliance_rate:0,average_response_time:0,average_resolution_time:0,by_priority:{},by_category:{},breach_reasons:{}};n.compliance_rate=n.total>0?n.met/n.total*100:0;let o=[],l=[];return i.forEach(e=>{if(e.actual_response_date){let t=new Date(e.actual_response_date),a=new Date(e.created_at);o.push(u(t,a))}if(e.actual_resolution_date){let t=new Date(e.actual_resolution_date),a=new Date(e.created_at);l.push(u(t,a))}let t=e.sla_definitions.priority||"medium";n.by_priority[t]||(n.by_priority[t]={total:0,met:0,breached:0,at_risk:0}),n.by_priority[t].total++,"on_track"===e.status?n.by_priority[t].met++:"breached"===e.status?n.by_priority[t].breached++:"at_risk"===e.status&&n.by_priority[t].at_risk++,e.breach_reason&&(n.breach_reasons[e.breach_reason]=(n.breach_reasons[e.breach_reason]||0)+1)}),o.length>0&&(n.average_response_time=o.reduce((e,t)=>e+t,0)/o.length),l.length>0&&(n.average_resolution_time=l.reduce((e,t)=>e+t,0)/l.length),n}async getAtRiskItems(){let e=(0,s.U)(),{data:t}=await e.from("sla_tracking").select(`
        *,
        workflow_instances!inner(
          *,
          request_forms!inner(
            id,
            title,
            requester_id,
            priority,
            staff!inner(name_en, name_jp)
          )
        ),
        sla_definitions!inner(*)
      `).eq("status","at_risk").is("actual_resolution_date",null).order("target_resolution_date",{ascending:!0});return t||[]}async exportSLAReport(e,t,a="json"){let s=await this.getSLAMetrics(e,t);return"json"===a?s:`Metric,Value
Total Requests,${s.total}
Met SLA,${s.met}
Breached SLA,${s.breached}
At Risk,${s.at_risk}
Compliance Rate,${s.compliance_rate.toFixed(2)}%
Average Response Time,${s.average_response_time.toFixed(0)} minutes
Average Resolution Time,${s.average_resolution_time.toFixed(0)} minutes`}constructor(){this.businessHours={start:{hour:9,minute:0},end:{hour:18,minute:0}},this.holidays=[new Date("2025-01-01"),new Date("2025-01-13"),new Date("2025-02-11"),new Date("2025-02-23"),new Date("2025-03-20"),new Date("2025-04-29"),new Date("2025-05-03"),new Date("2025-05-04"),new Date("2025-05-05"),new Date("2025-07-21"),new Date("2025-08-11"),new Date("2025-09-15"),new Date("2025-09-23"),new Date("2025-10-13"),new Date("2025-11-03"),new Date("2025-11-23")]}}let f=new w},61487:(e,t,a)=>{"use strict";a.d(t,{U:()=>i});var s=a(49064),r=a(44512);let i=()=>{let e=(0,r.UL)();return(0,s.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:a,options:s})=>e.set(t,a,s))}catch{}}}})}},75971:(e,t,a)=>{"use strict";a.d(t,{z:()=>n});var s=a(55657),r=a(69353),i=a(66025);function n(e,t){return function(e,t){let a=+(0,s.a)(e);return(0,r.w)(e,a+t)}(e,t*i.Cg)}}};