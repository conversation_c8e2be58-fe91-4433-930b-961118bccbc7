import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { approvalEngine } from '@/lib/services/workflow/approval-engine';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get approval chain status
    const chainStatus = await approvalEngine.getApprovalChainStatus(params.id);

    return NextResponse.json(chainStatus);
  } catch (error) {
    console.error('Error fetching approval chain:', error);
    return NextResponse.json(
      { error: 'Failed to fetch approval chain status' },
      { status: 500 }
    );
  }
}
