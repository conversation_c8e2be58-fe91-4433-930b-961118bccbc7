// Workflow system type definitions

export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  version: number;
  is_active: boolean;
  workflow_json: WorkflowSchema;
  created_at: string;
  updated_at: string;
}

export interface WorkflowSchema {
  id: string;
  name: string;
  version: number;
  triggers: WorkflowTrigger;
  states: Record<string, WorkflowState>;
  metadata?: Record<string, any>;
}

export interface WorkflowTrigger {
  request_type?: string;
  service_category?: string;
  conditions?: any;
}

export interface WorkflowState {
  type: 'start' | 'approval' | 'task' | 'automatic' | 'end' | 'parallel' | 'wait';
  name?: string;
  description?: string;
  transitions?: StateTransition[];
  assignee?: AssigneeConfig;
  sla_minutes?: number;
  escalation?: EscalationConfig;
  handler?: string;
  parameters?: Record<string, any>;
  actions?: WorkflowAction[];
  approval_options?: string[];
  automatic?: boolean;
}

export interface StateTransition {
  to: string;
  condition?: string;
  label?: string;
}

export interface AssigneeConfig {
  userId?: string;
  role?: string;
  department?: string;
  expression?: string;
}

export interface EscalationConfig {
  levels: EscalationLevel[];
}

export interface EscalationLevel {
  minutes: number;
  assignee: AssigneeConfig;
  notification?: string;
}

export interface WorkflowAction {
  type: string;
  parameters: Record<string, any>;
}

export interface WorkflowInstance {
  id: string;
  workflow_definition_id: string;
  request_id: string;
  current_state: string;
  status: 'active' | 'completed' | 'cancelled' | 'suspended';
  context_data: Record<string, any>;
  started_at: string;
  completed_at?: string;
  created_by: string;
}

export interface WorkflowTask {
  id: string;
  workflow_instance_id: string;
  task_type: string;
  task_name: string;
  assigned_to?: string;
  assigned_role?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'escalated';
  due_date?: string;
  started_at?: string;
  completed_at?: string;
  completed_by?: string;
  task_data: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface BusinessRule {
  id: string;
  name: string;
  description?: string;
  rule_type: 'routing' | 'approval' | 'escalation' | 'assignment';
  conditions: RuleCondition[];
  actions: RuleAction[];
  priority: number;
  is_active: boolean;
}

export interface RuleCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  logic?: 'AND' | 'OR';
}

export interface RuleAction {
  type: string;
  parameters: Record<string, any>;
}

export interface SLADefinition {
  id: string;
  name: string;
  service_category_id?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  response_time_minutes: number;
  resolution_time_minutes: number;
  business_hours_only: boolean;
  escalation_time_minutes?: number;
}

export interface SLATracking {
  id: string;
  workflow_instance_id: string;
  sla_definition_id: string;
  target_response_date?: string;
  actual_response_date?: string;
  target_resolution_date?: string;
  actual_resolution_date?: string;
  status: 'on_track' | 'at_risk' | 'breached';
  breach_reason?: string;
}

export interface WorkflowTransition {
  id: string;
  workflow_instance_id: string;
  from_state?: string;
  to_state: string;
  transition_reason?: string;
  performed_by?: string;
  transition_data?: Record<string, any>;
  created_at: string;
}

export interface EscalationLog {
  id: string;
  workflow_task_id: string;
  escalated_from?: string;
  escalated_to?: string;
  escalation_reason: string;
  escalation_level: number;
  created_at: string;
}
