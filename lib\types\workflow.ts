export type WorkflowStatus = 
  | 'pending'
  | 'in_progress'
  | 'pending_approval'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'on_hold'

export type WorkflowPriority = 'low' | 'medium' | 'high' | 'urgent'

export type SLAStatus = 'on_track' | 'at_risk' | 'breached'

export type WorkflowEvent = 
  | 'workflow_created'
  | 'workflow_updated'
  | 'workflow_completed'
  | 'workflow_failed'
  | 'approval_required'
  | 'approval_completed'
  | 'sla_warning'
  | 'sla_breached'
  | 'workflow_escalated'

export interface WorkflowInstance {
  id: string
  workflow_definition_id: string
  context: any
  current_state: string
  status: WorkflowStatus
  priority: WorkflowPriority
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface WorkflowTask {
  id: string
  workflow_instance_id: string
  task_type: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped'
  assigned_to?: string
  data?: any
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface WorkflowNotification {
  id: string
  workflow_instance_id: string
  event: WorkflowEvent
  recipient_id: string
  channel: 'email' | 'sms' | 'in_app'
  sent_at: string
  read_at?: string
  data: any
}
