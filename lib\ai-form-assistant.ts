// AI Service Integration for Dynamic Forms
import { <PERSON><PERSON>ield, FormData } from '@/lib/form-types'
import { SelectedUser } from '@/lib/request-types'

interface AIContext {
  userId?: string
  departmentId?: string
  userRole?: string
  previousFormData?: FormData
  selectedUsers?: SelectedUser[]
}

interface AISuggestion {
  fieldId: string
  suggestedValue: any
  confidence: number
  reasoning: string
}

interface AIValidationResult {
  isValid: boolean
  message?: string
  type: 'success' | 'warning' | 'error' | 'info'
  aiInsight?: string
  suggestion?: any
}

export class AIFormAssistant {
  private static instance: AIFormAssistant
  private apiKey: string | null = null

  private constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || null
  }

  public static getInstance(): AIFormAssistant {
    if (!AIFormAssistant.instance) {
      AIFormAssistant.instance = new AIFormAssistant()
    }
    return AIFormAssistant.instance
  }

  /**
   * Generate AI suggestions for form fields based on context
   */
  async generateFieldSuggestions(
    field: Form<PERSON>ield, 
    currentValue: any, 
    context: AIContext
  ): Promise<AISuggestion | null> {
    try {
      // For now, use rule-based suggestions
      // In production, this would call OpenAI API
      return this.generateRuleBasedSuggestion(field, currentValue, context)
    } catch (error) {
      console.error('AI suggestion failed:', error)
      return null
    }
  }

  /**
   * Auto-populate form fields using AI
   */
  async autoPopulateFields(
    fields: FormField[],
    context: AIContext
  ): Promise<FormData> {
    const populatedData: FormData = {}

    for (const field of fields) {
      const suggestion = await this.generateFieldSuggestions(field, null, context)
      if (suggestion && suggestion.confidence > 0.7) {
        populatedData[field.id] = suggestion.suggestedValue
      }
    }

    return populatedData
  }

  /**
   * Validate form data using AI
   */
  async validateFormData(
    fields: FormField[],
    formData: FormData,
    context: AIContext
  ): Promise<Record<string, string>> {
    const errors: Record<string, string> = {}

    for (const field of fields) {
      const error = await this.validateField(field, formData[field.id], context)
      if (error) {
        errors[field.id] = error
      }
    }

    return errors
  }

  /**
   * Generate smart suggestions for PC ID based on context
   */
  private generatePCIdSuggestion(input: string, context: AIContext): string {
    const now = new Date()
    const year = now.getFullYear().toString().slice(-2)
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    
    if (input.toLowerCase().includes('laptop') || input.toLowerCase().includes('ノート')) {
      return `LT${year}${month}${day}${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`
    }
    
    if (input.toLowerCase().includes('desktop') || input.toLowerCase().includes('デスク')) {
      return `DT${year}${month}${day}${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`
    }
    
    return `PC${year}${month}${day}${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`
  }

  /**
   * Generate email suggestions based on context
   */
  private generateEmailSuggestion(input: string, context: AIContext): string {
    if (input.includes('@')) return input
    
    const cleanInput = input.toLowerCase().replace(/[^a-z0-9]/g, '')
    return `${cleanInput}@company.com`
  }

  /**
   * Generate reason/description suggestions
   */
  private generateReasonSuggestion(input: string, context: AIContext): string {
    const lowerInput = input.toLowerCase()
    
    const suggestions = {
      'software': '業務に必要なソフトウェアのインストールのため / Required for business software installation',
      'access': 'プロジェクト作業のためアクセス権限が必要 / Access permission needed for project work',
      'permission': 'システム管理作業のため管理者権限が必要 / Administrator permission needed for system management',
      'email': 'メール配信リストへの参加のため / To join mailing list',
      'document': '文書共有のためのアクセス権限 / Access permission for document sharing',
      'meeting': '会議資料共有のため / For meeting material sharing'
    }

    for (const [key, suggestion] of Object.entries(suggestions)) {
      if (lowerInput.includes(key) || lowerInput.includes(key.replace(/[aeiou]/g, ''))) {
        return suggestion
      }
    }

    return input.charAt(0).toUpperCase() + input.slice(1)
  }

  /**
   * Rule-based suggestion generation
   */
  private generateRuleBasedSuggestion(
    field: FormField, 
    currentValue: any, 
    context: AIContext
  ): AISuggestion | null {
    const input = currentValue?.toString() || ''
    
    switch (field.id) {
      case 'pc_id':
        return {
          fieldId: field.id,
          suggestedValue: this.generatePCIdSuggestion(input, context),
          confidence: 0.9,
          reasoning: 'PC ID generated based on current date and device type'
        }
      
      case 'email':
        if (input && !input.includes('@')) {
          return {
            fieldId: field.id,
            suggestedValue: this.generateEmailSuggestion(input, context),
            confidence: 0.8,
            reasoning: 'Added company domain to email address'
          }
        }
        break
      
      case 'reason':
      case 'description':
        if (input && input.length > 2) {
          return {
            fieldId: field.id,
            suggestedValue: this.generateReasonSuggestion(input, context),
            confidence: 0.7,
            reasoning: 'Enhanced description based on keywords'
          }
        }
        break
    }

    return null
  }

  /**
   * Validate individual field
   */
  private async validateField(
    field: FormField, 
    value: any, 
    context: AIContext
  ): Promise<string | null> {
    if (field.required && (!value || value.toString().trim() === '')) {
      return `${field.labelJp || field.label}は必須です / ${field.label} is required`
    }

    switch (field.type) {
      case 'text':
        if (field.validation === 'email' && value) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(value)) {
            return '有効なメールアドレスを入力してください / Please enter a valid email address'
          }
        }
        break
      
      case 'search':
        if (field.required && (!value || !value.id)) {
          return `${field.labelJp || field.label}から選択してください / Please select from ${field.label}`
        }
        break
      
      case 'multiselect':
        if (field.required && (!value || !Array.isArray(value) || value.length === 0)) {
          return `${field.labelJp || field.label}を少なくとも1つ選択してください / Please select at least one ${field.label}`
        }
        break
    }

    return null
  }

  /**
   * Get contextual help text for a field
   */
  getContextualHelp(field: FormField, context: AIContext): string {
    const helpTexts: Record<string, string> = {
      'pc_id': 'PC ID は機器種別 + 日付 + 連番で構成されます (例: LT250523001)',
      'email': '会社のメールアドレスを入力してください (@company.com)',
      'reason': '具体的な理由や目的を日本語と英語で記載してください',
      'group_mail': '部署に関連するグループメールを選択してください',
      'sharepoint_library': 'アクセスが必要なSharePointライブラリを選択してください'
    }

    return helpTexts[field.id] || field.helpTextJp || field.helpText || ''
  }
}

// Export singleton instance
export const aiFormAssistant = AIFormAssistant.getInstance()
