import { SupabaseClient } from '@supabase/supabase-js';
import { NotificationService } from '../../notifications/notification-service';
import { AuditService } from '../../audit/audit-service';
import { WorkflowEngine } from '../workflow-engine';
import { SLAManager } from '../sla-manager';

interface EscalationRule {
  id: string;
  name: string;
  description: string;
  triggerType: 'sla_breach' | 'overdue' | 'manual' | 'approval_timeout';
  conditions: EscalationCondition[];
  actions: EscalationAction[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  retryInterval?: number; // minutes
  maxRetries?: number;
}

interface EscalationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'age_hours' | 'age_days';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

interface EscalationAction {
  type: 'notify' | 'reassign' | 'change_priority' | 'add_approver' | 'auto_approve' | 'auto_reject' | 'custom_webhook';
  target?: string; // user_id, role, email, webhook_url
  parameters?: Record<string, any>;
}

interface EscalationInstance {
  id: string;
  ruleId: string;
  workflowInstanceId: string;
  requestId: string;
  triggeredAt: Date;
  triggeredBy: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  retryCount: number;
  lastRetryAt?: Date;
  completedAt?: Date;
  failureReason?: string;
  actionResults: ActionResult[];
}

interface ActionResult {
  actionType: string;
  status: 'success' | 'failed';
  executedAt: Date;
  details?: any;
  error?: string;
}

export class EscalationEngine {
  private supabase: SupabaseClient;
  private notificationService: NotificationService;
  private auditService: AuditService;
  private workflowEngine: WorkflowEngine;
  private slaManager: SLAManager;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(
    supabase: SupabaseClient,
    notificationService: NotificationService,
    auditService: AuditService,
    workflowEngine: WorkflowEngine,
    slaManager: SLAManager
  ) {
    this.supabase = supabase;
    this.notificationService = notificationService;
    this.auditService = auditService;
    this.workflowEngine = workflowEngine;
    this.slaManager = slaManager;
  }

  // Start continuous monitoring for escalation triggers
  async startMonitoring(intervalMinutes: number = 5): Promise<void> {
    console.log(`Starting escalation monitoring with ${intervalMinutes} minute interval`);
    
    // Clear existing interval if any
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    // Initial check
    await this.checkEscalationTriggers();

    // Set up recurring checks
    this.monitoringInterval = setInterval(
      async () => {
        await this.checkEscalationTriggers();
      },
      intervalMinutes * 60 * 1000
    );
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('Escalation monitoring stopped');
    }
  }

  // Check all active escalation triggers
  async checkEscalationTriggers(): Promise<void> {
    try {
      console.log('Checking escalation triggers...');

      // Get active escalation rules
      const { data: rules, error: rulesError } = await this.supabase
        .from('escalation_rules')
        .select('*')
        .eq('is_active', true);

      if (rulesError) throw rulesError;

      for (const rule of rules || []) {
        await this.processEscalationRule(rule);
      }

      // Check for stuck escalations and retry if needed
      await this.retryFailedEscalations();

    } catch (error) {
      console.error('Error checking escalation triggers:', error);
      await this.auditService.logActivity({
        userId: 'system',
        action: 'escalation_check_failed',
        resourceType: 'escalation',
        details: { error: error.message },
      });
    }
  }

  // Process a specific escalation rule
  private async processEscalationRule(rule: EscalationRule): Promise<void> {
    try {
      let triggerCandidates: any[] = [];

      switch (rule.triggerType) {
        case 'sla_breach':
          triggerCandidates = await this.getSLABreachCandidates(rule);
          break;
        case 'overdue':
          triggerCandidates = await this.getOverdueCandidates(rule);
          break;
        case 'approval_timeout':
          triggerCandidates = await this.getApprovalTimeoutCandidates(rule);
          break;
        case 'manual':
          // Manual escalations are triggered explicitly, not through monitoring
          break;
      }

      // Process each candidate
      for (const candidate of triggerCandidates) {
        // Check if escalation already exists for this candidate
        const existing = await this.checkExistingEscalation(rule.id, candidate.id);
        if (!existing) {
          await this.createEscalationInstance(rule, candidate);
        }
      }

    } catch (error) {
      console.error(`Error processing escalation rule ${rule.id}:`, error);
    }
  }

  // Get candidates for SLA breach escalation
  private async getSLABreachCandidates(rule: EscalationRule): Promise<any[]> {
    const atRiskItems = await this.slaManager.getAtRiskItems();
    const candidates = [];

    for (const item of atRiskItems) {
      if (item.status === 'breached' && this.evaluateConditions(rule.conditions, item)) {
        candidates.push(item);
      }
    }

    return candidates;
  }

  // Get overdue request candidates
  private async getOverdueCandidates(rule: EscalationRule): Promise<any[]> {
    const { data: requests, error } = await this.supabase
      .from('request_forms')
      .select(`
        *,
        request_items(*),
        workflow_instances(*)
      `)
      .in('status', ['pending', 'in-progress'])
      .not('completed_at', 'is', null);

    if (error) throw error;

    const candidates = [];
    const now = new Date();

    for (const request of requests || []) {
      const createdAt = new Date(request.created_at);
      const ageHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

      const context = {
        ...request,
        age_hours: ageHours,
        age_days: ageHours / 24,
      };

      if (this.evaluateConditions(rule.conditions, context)) {
        candidates.push(request);
      }
    }

    return candidates;
  }

  // Get approval timeout candidates
  private async getApprovalTimeoutCandidates(rule: EscalationRule): Promise<any[]> {
    const { data: approvals, error } = await this.supabase
      .from('approval_chains')
      .select(`
        *,
        approval_levels(*),
        workflow_instances(
          *,
          request_forms(*)
        )
      `)
      .eq('status', 'pending')
      .not('current_level', 'is', null);

    if (error) throw error;

    const candidates = [];
    const now = new Date();

    for (const approval of approvals || []) {
      const currentLevel = approval.approval_levels?.find(
        level => level.level === approval.current_level
      );

      if (currentLevel?.timeout_hours) {
        const levelStartTime = new Date(approval.updated_at);
        const timeoutTime = new Date(levelStartTime);
        timeoutTime.setHours(timeoutTime.getHours() + currentLevel.timeout_hours);

        if (now > timeoutTime && this.evaluateConditions(rule.conditions, approval)) {
          candidates.push(approval);
        }
      }
    }

    return candidates;
  }

  // Evaluate escalation conditions
  private evaluateConditions(conditions: EscalationCondition[], context: any): boolean {
    if (!conditions || conditions.length === 0) return true;

    let result = true;
    let previousOperator: 'AND' | 'OR' = 'AND';

    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, context);

      if (previousOperator === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      previousOperator = condition.logicalOperator || 'AND';
    }

    return result;
  }

  // Evaluate a single condition
  private evaluateCondition(condition: EscalationCondition, context: any): boolean {
    const fieldValue = this.getFieldValue(context, condition.field);

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      case 'not_equals':
        return fieldValue !== condition.value;
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
      case 'contains':
        return String(fieldValue).includes(String(condition.value));
      case 'age_hours':
        return context.age_hours > Number(condition.value);
      case 'age_days':
        return context.age_days > Number(condition.value);
      default:
        return false;
    }
  }

  // Get field value from context using dot notation
  private getFieldValue(context: any, field: string): any {
    const parts = field.split('.');
    let value = context;

    for (const part of parts) {
      value = value?.[part];
      if (value === undefined) break;
    }

    return value;
  }

  // Check if escalation already exists
  private async checkExistingEscalation(ruleId: string, candidateId: string): Promise<boolean> {
    const { data, error } = await this.supabase
      .from('escalation_instances')
      .select('id')
      .eq('rule_id', ruleId)
      .eq('request_id', candidateId)
      .in('status', ['pending', 'in_progress'])
      .single();

    return !error && !!data;
  }

  // Create new escalation instance
  async createEscalationInstance(rule: EscalationRule, candidate: any): Promise<void> {
    try {
      const instance: Partial<EscalationInstance> = {
        ruleId: rule.id,
        workflowInstanceId: candidate.workflow_instance_id || candidate.id,
        requestId: candidate.request_id || candidate.id,
        triggeredAt: new Date(),
        triggeredBy: 'system',
        status: 'pending',
        retryCount: 0,
        actionResults: [],
      };

      const { data, error } = await this.supabase
        .from('escalation_instances')
        .insert(instance)
        .select()
        .single();

      if (error) throw error;

      // Execute escalation actions
      await this.executeEscalationActions(data, rule, candidate);

      // Log the escalation
      await this.auditService.logActivity({
        userId: 'system',
        action: 'escalation_triggered',
        resourceType: 'escalation',
        resourceId: data.id,
        details: {
          rule: rule.name,
          triggerType: rule.triggerType,
          candidateId: candidate.id,
        },
      });

    } catch (error) {
      console.error('Error creating escalation instance:', error);
      throw error;
    }
  }

  // Execute escalation actions
  private async executeEscalationActions(
    instance: EscalationInstance,
    rule: EscalationRule,
    context: any
  ): Promise<void> {
    try {
      // Update status to in_progress
      await this.updateEscalationStatus(instance.id, 'in_progress');

      const actionResults: ActionResult[] = [];

      for (const action of rule.actions) {
        try {
          const result = await this.executeAction(action, context, instance);
          actionResults.push({
            actionType: action.type,
            status: 'success',
            executedAt: new Date(),
            details: result,
          });
        } catch (error) {
          console.error(`Error executing action ${action.type}:`, error);
          actionResults.push({
            actionType: action.type,
            status: 'failed',
            executedAt: new Date(),
            error: error.message,
          });
        }
      }

      // Update instance with results
      const allSuccessful = actionResults.every(r => r.status === 'success');
      await this.updateEscalationInstance(instance.id, {
        status: allSuccessful ? 'completed' : 'failed',
        completedAt: allSuccessful ? new Date() : null,
        actionResults,
        failureReason: allSuccessful ? null : 'Some actions failed',
      });

    } catch (error) {
      console.error('Error executing escalation actions:', error);
      await this.updateEscalationInstance(instance.id, {
        status: 'failed',
        failureReason: error.message,
      });
    }
  }

  // Execute a single escalation action
  private async executeAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<any> {
    switch (action.type) {
      case 'notify':
        return await this.executeNotifyAction(action, context, instance);
      case 'reassign':
        return await this.executeReassignAction(action, context, instance);
      case 'change_priority':
        return await this.executeChangePriorityAction(action, context, instance);
      case 'add_approver':
        return await this.executeAddApproverAction(action, context, instance);
      case 'auto_approve':
        return await this.executeAutoApproveAction(action, context, instance);
      case 'auto_reject':
        return await this.executeAutoRejectAction(action, context, instance);
      case 'custom_webhook':
        return await this.executeWebhookAction(action, context, instance);
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  // Execute notification action
  private async executeNotifyAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    const recipients = await this.resolveRecipients(action.target, context);
    
    for (const recipient of recipients) {
      await this.notificationService.sendNotification({
        userId: recipient.id,
        type: 'escalation',
        title: 'Request Escalated',
        message: action.parameters?.message || 
          `Request ${context.id} has been escalated due to ${instance.ruleId}`,
        data: {
          requestId: context.id,
          escalationId: instance.id,
          priority: action.parameters?.priority || 'high',
        },
        channels: ['in_app', 'email'],
      });
    }
  }

  // Execute reassignment action
  private async executeReassignAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    const newAssignees = await this.resolveRecipients(action.target, context);
    
    if (newAssignees.length > 0) {
      // Update request assignment
      await this.supabase
        .from('request_forms')
        .update({ 
          assigned_to: newAssignees[0].id,
          updated_at: new Date().toISOString(),
        })
        .eq('id', context.id);

      // Notify new assignee
      await this.notificationService.sendNotification({
        userId: newAssignees[0].id,
        type: 'assignment',
        title: 'New Request Assignment',
        message: `You have been assigned request ${context.id} through escalation`,
        data: { requestId: context.id },
        channels: ['in_app', 'email'],
      });
    }
  }

  // Execute priority change action
  private async executeChangePriorityAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    const newPriority = action.parameters?.priority || 'high';
    
    await this.supabase
      .from('request_forms')
      .update({ 
        priority: newPriority,
        updated_at: new Date().toISOString(),
      })
      .eq('id', context.id);

    // Update SLA if needed
    await this.slaManager.updatePriority(context.id, newPriority);
  }

  // Execute add approver action
  private async executeAddApproverAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    const approvers = await this.resolveRecipients(action.target, context);
    
    if (approvers.length > 0 && context.approval_chain_id) {
      // Add approver to current level
      const { data: chain } = await this.supabase
        .from('approval_chains')
        .select('*, approval_levels(*)')
        .eq('id', context.approval_chain_id)
        .single();

      if (chain && chain.current_level) {
        const currentLevel = chain.approval_levels.find(
          l => l.level === chain.current_level
        );

        if (currentLevel) {
          const updatedApprovers = [
            ...currentLevel.approver_ids,
            ...approvers.map(a => a.id),
          ];

          await this.supabase
            .from('approval_levels')
            .update({ 
              approver_ids: updatedApprovers,
              updated_at: new Date().toISOString(),
            })
            .eq('id', currentLevel.id);
        }
      }
    }
  }

  // Execute auto-approve action
  private async executeAutoApproveAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    if (context.approval_chain_id) {
      await this.workflowEngine.approveRequest(
        context.approval_chain_id,
        'system',
        'Auto-approved due to escalation',
        { escalationId: instance.id }
      );
    }
  }

  // Execute auto-reject action
  private async executeAutoRejectAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    if (context.approval_chain_id) {
      await this.workflowEngine.rejectRequest(
        context.approval_chain_id,
        'system',
        action.parameters?.reason || 'Auto-rejected due to escalation timeout',
        { escalationId: instance.id }
      );
    }
  }

  // Execute webhook action
  private async executeWebhookAction(
    action: EscalationAction,
    context: any,
    instance: EscalationInstance
  ): Promise<void> {
    if (!action.target) throw new Error('Webhook URL not specified');

    const payload = {
      escalationId: instance.id,
      ruleId: instance.ruleId,
      context,
      timestamp: new Date().toISOString(),
      ...action.parameters,
    };

    const response = await fetch(action.target, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-ITSync-Escalation': instance.id,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
    }
  }

  // Resolve recipients based on target specification
  private async resolveRecipients(target: string | undefined, context: any): Promise<any[]> {
    if (!target) return [];

    // If target is a user ID
    if (target.startsWith('user:')) {
      const userId = target.substring(5);
      const { data } = await this.supabase
        .from('staff')
        .select('*')
        .eq('id', userId)
        .single();
      return data ? [data] : [];
    }

    // If target is a role
    if (target.startsWith('role:')) {
      const role = target.substring(5);
      const { data } = await this.supabase
        .from('staff')
        .select('*')
        .eq('role', role);
      return data || [];
    }

    // If target is a department
    if (target.startsWith('department:')) {
      const departmentId = target.substring(11);
      const { data } = await this.supabase
        .from('staff')
        .select('*')
        .eq('department_id', departmentId);
      return data || [];
    }

    // If target is "manager" - get department manager
    if (target === 'manager') {
      const requesterId = context.requester_id || context.submitted_by;
      if (requesterId) {
        const { data: requester } = await this.supabase
          .from('staff')
          .select('department_id')
          .eq('id', requesterId)
          .single();

        if (requester?.department_id) {
          const { data: managers } = await this.supabase
            .from('staff')
            .select('*')
            .eq('department_id', requester.department_id)
            .eq('role', 'Department Administrator');
          return managers || [];
        }
      }
    }

    // If target is an email
    if (target.includes('@')) {
      return [{ id: null, email: target }];
    }

    return [];
  }

  // Retry failed escalations
  private async retryFailedEscalations(): Promise<void> {
    const { data: failedEscalations, error } = await this.supabase
      .from('escalation_instances')
      .select(`
        *,
        escalation_rules(*)
      `)
      .eq('status', 'failed')
      .lt('retry_count', 3); // Max 3 retries

    if (error || !failedEscalations) return;

    for (const escalation of failedEscalations) {
      const rule = escalation.escalation_rules;
      if (!rule?.retryInterval) continue;

      const lastRetry = escalation.last_retry_at 
        ? new Date(escalation.last_retry_at) 
        : new Date(escalation.triggered_at);
      
      const nextRetryTime = new Date(lastRetry);
      nextRetryTime.setMinutes(nextRetryTime.getMinutes() + rule.retryInterval);

      if (new Date() >= nextRetryTime) {
        await this.retryEscalation(escalation, rule);
      }
    }
  }

  // Retry a failed escalation
  private async retryEscalation(
    instance: EscalationInstance,
    rule: EscalationRule
  ): Promise<void> {
    try {
      console.log(`Retrying escalation ${instance.id}`);

      // Update retry count
      await this.updateEscalationInstance(instance.id, {
        retryCount: instance.retryCount + 1,
        lastRetryAt: new Date(),
        status: 'in_progress',
      });

      // Get the context
      const { data: context } = await this.supabase
        .from('request_forms')
        .select('*')
        .eq('id', instance.requestId)
        .single();

      if (context) {
        await this.executeEscalationActions(instance, rule, context);
      }

    } catch (error) {
      console.error(`Error retrying escalation ${instance.id}:`, error);
    }
  }

  // Update escalation status
  private async updateEscalationStatus(id: string, status: string): Promise<void> {
    await this.supabase
      .from('escalation_instances')
      .update({ 
        status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);
  }

  // Update escalation instance
  private async updateEscalationInstance(
    id: string, 
    updates: Partial<EscalationInstance>
  ): Promise<void> {
    await this.supabase
      .from('escalation_instances')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);
  }

  // Manual escalation trigger
  async triggerManualEscalation(
    requestId: string,
    ruleId: string,
    userId: string,
    reason?: string
  ): Promise<void> {
    try {
      // Get the rule
      const { data: rule, error: ruleError } = await this.supabase
        .from('escalation_rules')
        .select('*')
        .eq('id', ruleId)
        .single();

      if (ruleError || !rule) {
        throw new Error('Escalation rule not found');
      }

      // Get the request context
      const { data: request, error: requestError } = await this.supabase
        .from('request_forms')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError || !request) {
        throw new Error('Request not found');
      }

      // Create escalation instance
      const instance: Partial<EscalationInstance> = {
        ruleId,
        workflowInstanceId: request.workflow_instance_id || request.id,
        requestId,
        triggeredAt: new Date(),
        triggeredBy: userId,
        status: 'pending',
        retryCount: 0,
        actionResults: [],
      };

      const { data, error } = await this.supabase
        .from('escalation_instances')
        .insert(instance)
        .select()
        .single();

      if (error) throw error;

      // Execute actions
      await this.executeEscalationActions(data, rule, request);

      // Log the manual escalation
      await this.auditService.logActivity({
        userId,
        action: 'manual_escalation',
        resourceType: 'escalation',
        resourceId: data.id,
        details: {
          requestId,
          ruleId,
          reason,
        },
      });

    } catch (error) {
      console.error('Error triggering manual escalation:', error);
      throw error;
    }
  }

  // Get escalation history for a request
  async getEscalationHistory(requestId: string): Promise<EscalationInstance[]> {
    const { data, error } = await this.supabase
      .from('escalation_instances')
      .select(`
        *,
        escalation_rules(*)
      `)
      .eq('request_id', requestId)
      .order('triggered_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // Get active escalations
  async getActiveEscalations(): Promise<EscalationInstance[]> {
    const { data, error } = await this.supabase
      .from('escalation_instances')
      .select(`
        *,
        escalation_rules(*),
        request_forms(*)
      `)
      .in('status', ['pending', 'in_progress'])
      .order('triggered_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
}
