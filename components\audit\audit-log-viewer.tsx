"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Download, Search } from 'lucide-react';
import { format } from 'date-fns';
import { auditLogService, AuditLogEntry } from '@/lib/services/audit-log-service';

export function AuditLogViewer() {
  const [logs, setLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => { searchLogs(); }, []);

  const searchLogs = async () => {
    setLoading(true);
    const results = await auditLogService.search({ limit: 100 });
    setLogs(results);
    setLoading(false);
  };

  const exportLogs = async () => {
    const blob = await auditLogService.exportLogs({});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit_logs_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'ERROR': return 'destructive';
      case 'WARNING': return 'warning';
      case 'INFO': return 'default';
      case 'CRITICAL': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>監査ログ / Audit Logs</CardTitle>
        <div className="flex gap-2">
          <Button onClick={searchLogs} disabled={loading}>
            <Search className="h-4 w-4 mr-2" />検索</Button>