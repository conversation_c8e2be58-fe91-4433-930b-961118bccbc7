// detect-form-errors/index.ts
// Edge function for AI-powered error detection and correction

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ErrorDetectionRequest {
  fieldName: string
  fieldType: string
  value: string
  departmentId?: string
  context?: Record<string, any>
  language?: 'ja' | 'en'
}

interface DetectedError {
  type: string
  message: string
  confidence: number
  suggestions: string[]
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { fieldName, fieldType, value, departmentId, context, language = 'ja' } = 
      await req.json() as ErrorDetectionRequest

    // Get error patterns from database
    const { data: patterns } = await supabaseClient
      .from('error_patterns')
      .select('*')
      .eq('field_type', fieldType)
      .order('confidence_score', { ascending: false })

    // Get autocorrect mappings
    const { data: autocorrects } = await supabaseClient
      .from('autocorrect_mappings')
      .select('*')
      .eq('field_type', fieldType)
      .eq('language', language)

    const detectedErrors: DetectedError[] = []

    // Check against error patterns
    patterns?.forEach(pattern => {
      if (pattern.pattern_regex) {
        const regex = new RegExp(pattern.pattern_regex)
        if (regex.test(value)) {
          detectedErrors.push({
            type: pattern.error_type,
            message: pattern.common_mistake,
            confidence: pattern.confidence_score,
            suggestions: [pattern.suggested_correction]
          })
        }
      }
    })

    // Check for autocorrect suggestions
    const autocorrect = autocorrects?.find(
      ac => ac.incorrect_value.toLowerCase() === value.toLowerCase()
    )
    if (autocorrect) {
      detectedErrors.push({
        type: 'autocorrect',
        message: 'Common typo detected',
        confidence: 0.95,
        suggestions: [autocorrect.correct_value]
      })
    }

    // AI-based error detection using OpenAI
    if (detectedErrors.length === 0 && Deno.env.get('OPENAI_API_KEY')) {
      const aiErrors = await detectErrorsWithAI(
        fieldName,
        fieldType,
        value,
        context,
        language
      )
      detectedErrors.push(...aiErrors)
    }

    // Store error detection results
    if (detectedErrors.length > 0) {
      const { data: { user } } = await supabaseClient.auth.getUser()
      
      if (user) {
        const { data: staffData } = await supabaseClient
          .from('staff')
          .select('id')
          .eq('auth_id', user.id)
          .single()

        if (staffData) {
          await supabaseClient
            .from('error_corrections')
            .insert({
              user_id: staffData.id,
              field_name: fieldName,
              original_value: value,
              detected_errors: detectedErrors,
              suggested_corrections: detectedErrors.flatMap(e => e.suggestions),
              ai_confidence: Math.max(...detectedErrors.map(e => e.confidence)),
              context_data: context
            })
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        errors: detectedErrors,
        hasErrors: detectedErrors.length > 0
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

async function detectErrorsWithAI(
  fieldName: string,
  fieldType: string,
  value: string,
  context?: Record<string, any>,
  language: string = 'ja'
): Promise<DetectedError[]> {
  const apiKey = Deno.env.get('OPENAI_API_KEY')
  if (!apiKey) return []

  const systemPrompt = `You are an expert at detecting errors in Japanese corporate IT helpdesk forms.
Field type: ${fieldType}
Field name: ${fieldName}
Language: ${language}

Analyze the input value and detect any errors, typos, or formatting issues.
Consider Japanese business conventions and common mistakes.
Provide specific, actionable corrections.`

  const userPrompt = `Value: "${value}"
Context: ${JSON.stringify(context || {})}

Detect any errors and suggest corrections. Return JSON array of errors.`

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        response_format: { type: 'json_object' }
      }),
    })

    const data = await response.json()
    const aiResponse = JSON.parse(data.choices[0].message.content)
    
    return aiResponse.errors || []
  } catch (error) {
    console.error('AI error detection failed:', error)
    return []
  }
}
