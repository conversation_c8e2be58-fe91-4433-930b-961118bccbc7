(()=>{var e={};e.id=4344,e.ids=[4344],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},14064:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(70260),r=s(28203),i=s(25155),o=s.n(i),n=s(67292),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["test-audit-trail",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,91500)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-audit-trail\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-audit-trail\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-audit-trail/page",pathname:"/test-audit-trail",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},85816:(e,t,s)=>{Promise.resolve().then(s.bind(s,91500))},95544:(e,t,s)=>{Promise.resolve().then(s.bind(s,98134))},98134:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(45512),r=s(58009),i=s(87021),o=s(97643),n=s(69193),l=s(29050),c=s(86772),d=s(37133);class u{constructor(){this.initialized=!1}static getInstance(){return u.instance||(u.instance=new u),u.instance}async initialize(){this.initialized||(this.setupDatabaseChangeListeners(),this.setupErrorHandler(),this.setupNavigationTracking(),this.setupAPIInterceptors(),this.initialized=!0)}setupDatabaseChangeListeners(){["users","staff","roles","request_forms","request_items","group_mail_members","mailbox_members","sharepoint_access"].forEach(e=>{d.N.channel(`db-changes-${e}`).on("postgres_changes",{event:"*",schema:"public",table:e},async e=>{await this.handleDatabaseChange({schema:e.schema,table:e.table,operation:e.eventType,oldRecord:e.old,newRecord:e.new,timestamp:new Date().toISOString()})}).subscribe()})}async handleDatabaseChange(e){let t=e.table.toUpperCase(),s=`${t}${{INSERT:"_CREATED",UPDATE:"_UPDATED",DELETE:"_DELETED"}[e.operation]}`;await c.H.log({event_type:s,severity:"INFO",action:`${e.operation} on ${e.table}`,entity_type:e.table,entity_id:e.newRecord?.id||e.oldRecord?.id,old_value:e.oldRecord,new_value:e.newRecord,metadata:{schema:e.schema,operation:e.operation}})}setupErrorHandler(){}setupNavigationTracking(){}setupAPIInterceptors(){}shouldSkipAPILogging(e){return["/api/audit-logs","/supabase/functions/process-audit-logs","/_next/","/favicon"].some(t=>e.includes(t))}async logSystemEvent(e){await c.H.log({event_type:"error"===e.type?"SYSTEM_ERROR":"SYSTEM_INFO",severity:{startup:"INFO",shutdown:"INFO",error:"ERROR",config_change:"WARNING",integration:"INFO"}[e.type],action:`System ${e.type}`,description:e.message,metadata:e.metadata})}async logAPIRequest(e){("GET"!==e.method||e.statusCode>=400)&&await c.H.log({event_type:e.statusCode>=400?"SYSTEM_ERROR":"DATA_ACCESSED",severity:e.statusCode>=500?"ERROR":"INFO",action:`API ${e.method} ${e.path}`,description:`${e.method} request to ${e.path} returned ${e.statusCode}`,metadata:{method:e.method,path:e.path,statusCode:e.statusCode,duration:e.duration,requestBody:e.requestBody,responseBody:e.responseBody}})}async collectUserAction(e,t){await c.H.log({event_type:"DATA_ACCESSED",severity:"INFO",action:e,metadata:t})}async collectFormSubmission(e,t,s){await c.H.log({event_type:"REQUEST_CREATED",severity:s?"INFO":"ERROR",action:`Form submission: ${e}`,description:s?"Form submitted successfully":"Form submission failed",entity_type:"form_submission",metadata:{formType:e,fields:Object.keys(t),success:s}})}async collectAuthEvent(e,t,s){await c.H.log({event_type:{login:"USER_LOGIN",logout:"USER_LOGOUT",signup:"USER_CREATED"}[e],severity:t?"INFO":"WARNING",action:`User ${e}`,description:t?`${e} successful`:`${e} failed`,metadata:s})}}let p=u.getInstance();var m=s(78896),g=s(67418),h=s(46583),x=s(88976),v=s(61075),y=s(70384),j=s(65518);function f(){let e={logAction:e=>c.H.log(e),logLogin:c.r.logLogin,logLogout:c.r.logLogout,logRequestCreated:c.r.logRequestCreated,logRequestUpdated:c.r.logRequestUpdated,logError:c.r.logError,logSecurityEvent:c.r.logSecurityEvent,logDataExport:(e,t,s)=>c.H.log({event_type:"DATA_EXPORTED",severity:"INFO",action:"Data exported",description:`Exported ${s} ${e} records as ${t}`,entity_type:e,metadata:{format:t,count:s}}),logPermissionChange:(e,t,s)=>c.H.log({event_type:"grant"===s?"PERMISSION_GRANTED":"PERMISSION_REVOKED",severity:"WARNING",action:`Permission ${s}ed`,description:`${t} permission ${s}ed for user`,entity_type:"user",entity_id:e,metadata:{permission:t,action:s}}),logConfigChange:(e,t,s)=>c.H.log({event_type:"CONFIGURATION_CHANGED",severity:"WARNING",action:"Configuration updated",description:`Changed ${e} configuration`,entity_type:"system_config",old_value:t,new_value:s,metadata:{setting:e}})},[t,s]=(0,r.useState)([]),d=e=>{s(t=>[...t,`${new Date().toLocaleTimeString()}: ${e}`])},u=async()=>{try{await e.logAction({event_type:"DATA_ACCESSED",severity:"INFO",action:"Test user action",description:"User performed a test action in the audit trail test page"}),d("✅ User action logged successfully"),(0,j.toast)({title:"Success",description:"User action logged"})}catch(e){d("❌ Failed to log user action"),(0,j.toast)({title:"Error",description:"Failed to log user action",variant:"destructive"})}},f=async()=>{try{await p.collectFormSubmission("test_form",{field1:"value1",field2:"value2"},!0),d("✅ Form submission logged successfully"),(0,j.toast)({title:"Success",description:"Form submission logged"})}catch(e){d("❌ Failed to log form submission"),(0,j.toast)({title:"Error",description:"Failed to log form submission",variant:"destructive"})}},E=async()=>{try{await e.logSecurityEvent("Unauthorized access attempt detected","WARNING"),d("✅ Security event logged successfully"),(0,j.toast)({title:"Success",description:"Security event logged"})}catch(e){d("❌ Failed to log security event"),(0,j.toast)({title:"Error",description:"Failed to log security event",variant:"destructive"})}},N=async()=>{try{await e.logError(Error("Test error for audit trail"),{context:"test_page"}),d("✅ Error logged successfully"),(0,j.toast)({title:"Success",description:"Error logged"})}catch(e){d("❌ Failed to log error"),(0,j.toast)({title:"Error",description:"Failed to log error",variant:"destructive"})}},_=async()=>{try{await e.logDataExport("test_data","CSV",100),d("✅ Data export logged successfully"),(0,j.toast)({title:"Success",description:"Data export logged"})}catch(e){d("❌ Failed to log data export"),(0,j.toast)({title:"Error",description:"Failed to log data export",variant:"destructive"})}},A=async()=>{try{await e.logPermissionChange("test-user-123","admin_access","grant"),d("✅ Permission change logged successfully"),(0,j.toast)({title:"Success",description:"Permission change logged"})}catch(e){d("❌ Failed to log permission change"),(0,j.toast)({title:"Error",description:"Failed to log permission change",variant:"destructive"})}},C=[{icon:y.A,title:"Immutable Logging",description:"Logs cannot be modified or deleted once written"},{icon:m.A,title:"Real-time Collection",description:"Automatic collection from all system sources"},{icon:x.A,title:"Comprehensive Storage",description:"Secure storage with encryption and archival"},{icon:v.A,title:"Compliance Reporting",description:"Export logs in multiple formats for compliance"}];return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Audit Trail System Test"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Test and demonstrate the enterprise-grade audit trail functionality"})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:C.map(e=>(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"space-y-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 text-primary"}),(0,a.jsx)(o.ZB,{className:"text-lg",children:e.title})]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})})]},e.title))}),(0,a.jsxs)(n.tU,{defaultValue:"test",className:"space-y-4",children:[(0,a.jsxs)(n.j7,{children:[(0,a.jsx)(n.Xi,{value:"test",children:"Test Actions"}),(0,a.jsx)(n.Xi,{value:"viewer",children:"Log Viewer"}),(0,a.jsx)(n.Xi,{value:"results",children:"Test Results"})]}),(0,a.jsxs)(n.av,{value:"test",className:"space-y-4",children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{children:"Audit Log Test Actions"}),(0,a.jsx)(o.BT,{children:"Click the buttons below to test different audit logging scenarios"})]}),(0,a.jsx)(o.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)(i.$,{onClick:u,className:"w-full",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Log User Action"]}),(0,a.jsxs)(i.$,{onClick:f,className:"w-full",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Log Form Submission"]}),(0,a.jsxs)(i.$,{onClick:E,variant:"warning",className:"w-full",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Log Security Event"]}),(0,a.jsxs)(i.$,{onClick:N,variant:"destructive",className:"w-full",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Log Error Event"]}),(0,a.jsxs)(i.$,{onClick:_,className:"w-full",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Log Data Export"]}),(0,a.jsxs)(i.$,{onClick:A,className:"w-full",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Log Permission Change"]})]})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{children:"Automatic Log Collection"}),(0,a.jsx)(o.BT,{children:"The following events are automatically logged by the system"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Page navigation and access"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Database changes (INSERT, UPDATE, DELETE)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"API requests and responses"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"System errors and exceptions"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm",children:"Authentication events"})]})]})})]})]}),(0,a.jsx)(n.av,{value:"viewer",children:(0,a.jsx)(l.g,{maxHeight:"500px"})}),(0,a.jsx)(n.av,{value:"results",children:(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{children:"Test Results"}),(0,a.jsx)(o.BT,{children:"Results from test actions performed"})]}),(0,a.jsx)(o.Wu,{children:0===t.length?(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No tests have been run yet. Go to the Test Actions tab to run tests."}):(0,a.jsx)("div",{className:"space-y-2",children:t.map((e,t)=>(0,a.jsx)("div",{className:"text-sm font-mono",children:e},t))})})]})})]})]})}},91500:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-audit-trail\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-audit-trail\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8096,2076],()=>s(14064));module.exports=a})();