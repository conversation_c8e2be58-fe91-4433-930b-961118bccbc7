// Edge Function: Analyze Form Patterns
// Analyzes historical form data to identify patterns and generate intelligent suggestions

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import OpenAI from 'https://deno.land/x/openai@v4.20.1/mod.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AnalyzeRequest {
  departmentId: string
  fieldId: string
  currentValue?: string
  formContext?: Record<string, any>
  limit?: number
}

interface PatternAnalysis {
  commonPatterns: Array<{
    value: string
    frequency: number
    confidence: number
  }>
  aiSuggestion?: string
  reasoning?: string
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const openAIKey = Deno.env.get('OPENAI_API_KEY')!

    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const openai = new OpenAI({ apiKey: openAIKey })

    const { departmentId, fieldId, currentValue, formContext, limit = 10 } = await req.json() as AnalyzeRequest

    // Fetch field usage statistics
    const { data: fieldStats, error: statsError } = await supabase
      .from('field_usage_statistics')
      .select('common_values')
      .eq('department_id', departmentId)
      .eq('field_id', fieldId)
      .single()

    if (statsError) {
      console.log('No existing statistics for field:', fieldId)
    }

    // Fetch recent submission patterns
    const { data: recentSubmissions, error: submissionsError } = await supabase
      .from('form_submission_history')
      .select('field_values, created_at')
      .eq('department_id', departmentId)
      .order('created_at', { ascending: false })
      .limit(limit)

    // Extract patterns from recent submissions
    const fieldPatterns = new Map<string, number>()
    
    if (recentSubmissions) {
      recentSubmissions.forEach(submission => {
        const value = submission.field_values?.[fieldId]
        if (value) {
          fieldPatterns.set(value, (fieldPatterns.get(value) || 0) + 1)
        }
      })
    }

    // Combine with statistical data
    let commonPatterns: Array<{ value: string; frequency: number; confidence: number }> = []
    
    if (fieldStats?.common_values) {
      commonPatterns = fieldStats.common_values.map((item: any) => ({
        value: item.value,
        frequency: item.frequency,
        confidence: Math.min(item.frequency / 100, 0.95) // Cap at 95% confidence
      }))
    }

    // Add recent patterns
    fieldPatterns.forEach((frequency, value) => {
      const existing = commonPatterns.find(p => p.value === value)
      if (!existing) {
        commonPatterns.push({
          value,
          frequency,
          confidence: Math.min(frequency / limit, 0.8)
        })
      }
    })

    // Sort by frequency
    commonPatterns.sort((a, b) => b.frequency - a.frequency)
    commonPatterns = commonPatterns.slice(0, 5) // Top 5 patterns

    // Use AI to generate contextual suggestion if current value is provided
    let aiSuggestion: string | undefined
    let reasoning: string | undefined

    if (currentValue && openAIKey) {
      const prompt = buildAIPrompt(fieldId, currentValue, commonPatterns, formContext)
      
      try {
        const completion = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: `You are an AI assistant helping with IT helpdesk form completion in a Japanese enterprise. 
              Analyze patterns and suggest improvements. Respond in JSON format:
              {
                "suggestion": "improved value",
                "reasoning": "brief explanation"
              }`
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.3,
          max_tokens: 150,
          response_format: { type: "json_object" }
        })

        const result = JSON.parse(completion.choices[0].message.content || '{}')
        aiSuggestion = result.suggestion
        reasoning = result.reasoning
      } catch (error) {
        console.error('AI suggestion error:', error)
      }
    }

    // Store the pattern analysis
    if (commonPatterns.length > 0) {
      const patternKey = `department:${departmentId}:field:${fieldId}`
      await supabase
        .from('ai_form_patterns')
        .upsert({
          pattern_type: 'field_value',
          pattern_key: patternKey,
          pattern_data: { patterns: commonPatterns },
          confidence_score: commonPatterns[0]?.confidence || 0.5,
          usage_count: commonPatterns.reduce((sum, p) => sum + p.frequency, 0),
          last_used: new Date().toISOString()
        }, {
          onConflict: 'pattern_type,pattern_key'
        })
    }

    const response: PatternAnalysis = {
      commonPatterns,
      aiSuggestion,
      reasoning
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

function buildAIPrompt(
  fieldId: string,
  currentValue: string,
  patterns: Array<{ value: string; frequency: number }>,
  context?: Record<string, any>
): string {
  let prompt = `Field: ${fieldId}\nCurrent Value: ${currentValue}\n\n`
  
  if (patterns.length > 0) {
    prompt += `Common patterns for this field:\n`
    patterns.forEach(p => {
      prompt += `- "${p.value}" (used ${p.frequency} times)\n`
    })
    prompt += '\n'
  }
  
  if (context) {
    prompt += `Form context:\n${JSON.stringify(context, null, 2)}\n\n`
  }
  
  prompt += `Based on the patterns and context, suggest an improvement for the current value.`
  
  return prompt
}
