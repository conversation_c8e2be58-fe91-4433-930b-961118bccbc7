'use client'

import { ReactNode } from 'react'
import { usePermissions } from '@/lib/use-permissions'
import { UserRole } from '@/lib/auth'

interface PermissionGateProps {
  children: ReactNode
  resource: string
  action: string
  scope?: string
  role?: UserRole
  departmentId?: string
  fallback?: ReactNode
}

export default function PermissionGate({
  children,
  resource,
  action,
  scope,
  role,
  departmentId,
  fallback = null
}: PermissionGateProps) {
  const { 
    checkPermission, 
    checkDepartmentAccess,
    isGlobalAdmin,
    isSystemAdmin,
    isDepartmentAdmin,
    isHRStaff,
    isITSupport,
    isRegularUser
  } = usePermissions()

  // Check specific role if provided
  if (role) {
    let hasRole = false
    switch (role) {
      case UserRole.GLOBAL_ADMIN:
        hasRole = isGlobalAdmin()
        break
      case UserRole.SYSTEM_ADMIN:
        hasRole = isSystemAdmin()
        break
      case UserRole.DEPT_ADMIN:
        hasRole = isDepartmentAdmin()
        break
      case UserRole.HR_STAFF:
        hasRole = isHRStaff()
        break
      case UserRole.IT_SUPPORT:
        hasRole = isITSupport()
        break
      case UserRole.REGULAR_USER:
        hasRole = isRegularUser()
        break
    }
    
    if (!hasRole) {
      return <>{fallback}</>
    }
  }

  // Check department access if provided
  if (departmentId && !checkDepartmentAccess(departmentId)) {
    return <>{fallback}</>
  }

  // Check permission
  if (!checkPermission(resource, action, scope)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
