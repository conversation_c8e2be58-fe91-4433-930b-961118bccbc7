"use strict";(()=>{var e={};e.id=2266,e.ids=[2266],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{e.exports=require("assert")},79428:e=>{e.exports=require("buffer")},55511:e=>{e.exports=require("crypto")},14985:e=>{e.exports=require("dns")},94735:e=>{e.exports=require("events")},91645:e=>{e.exports=require("net")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},41204:e=>{e.exports=require("string_decoder")},34631:e=>{e.exports=require("tls")},83997:e=>{e.exports=require("tty")},79551:e=>{e.exports=require("url")},28354:e=>{e.exports=require("util")},49976:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>N,routeModule:()=>B,serverHooks:()=>L,workAsyncStorage:()=>H,workUnitAsyncStorage:()=>Q});var r={};a.r(r),a.d(r,{GET:()=>q,POST:()=>S});var s=a(42706),i=a(28203),o=a(45994),n=a(39187),c=a(61487),l=a(68335),u=a(56262);class d{async recordUsage(e,t,a,r,s,i,o,n,c,u,d,h,g={}){let p={user_id:e,organization_id:t,provider:a,model:r,feature:s,request_type:i,input_tokens:o,output_tokens:n,total_tokens:o+n,estimated_cost:c,request_duration:u,success:d,error_type:h,metadata:g,created_at:new Date().toISOString()};try{let{error:i}=await this.supabase.from("ai_usage_logs").insert(p);if(i){console.error("Failed to record AI usage:",i);return}await this.updateQuotaUsage(e,t,c,o+n),await this.updateCachedStats(e,t,c,o+n),await this.checkBudgetAlerts(e,t),l.qd.increment("ai.usage_recorded",{provider:a,model:r,feature:s,success:d.toString()})}catch(e){console.error("Error recording AI usage:",e),l.qd.increment("ai.usage_recording_errors")}}async canMakeRequest(e,t,a,r){try{let s=await this.checkQuotas("organization",t,a,r);if(!s.allowed)return s;let i=await this.checkQuotas("user",e,a,r);if(!i.allowed)return i;let o=await this.checkQuotas("global","global",a,r);if(!o.allowed)return o;return{allowed:!0}}catch(e){return console.error("Error checking AI request permission:",e),{allowed:!0}}}async getUsageStats(e,t,a="month"){let r=`${this.cachePrefix}stats:${e}:${t}:${a}`,s=await this.cache.get(r);if(s)return s;try{let s=this.getStartDate(a),i=this.supabase.from("ai_usage_logs").select("*").gte("created_at",s.toISOString());"user"===e?i=i.eq("user_id",t):"organization"===e&&(i=i.eq("organization_id",t));let{data:o,error:n}=await i;if(n)throw n;let c=this.calculateStats(o||[]);return await this.cache.set(r,c,{ttl:300,tags:["ai_usage",e,t]}),c}catch(e){return console.error("Error getting usage stats:",e),this.getEmptyStats()}}async getQuotaUsage(e,t){try{let{data:a,error:r}=await this.supabase.from("ai_usage_quotas").select("*").eq("entity_type",e).eq("entity_id",t);if(r)throw r;return a||[]}catch(e){return console.error("Error getting quota usage:",e),[]}}async setQuota(e,t,a,r,s){try{let i={entity_type:e,entity_id:t,period:a,quota_type:r,limit_value:s,current_usage:0,reset_at:this.calculateResetDate(a).toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{error:o}=await this.supabase.from("ai_usage_quotas").upsert(i,{onConflict:"entity_type,entity_id,period,quota_type"});if(o)throw o;await this.cache.invalidateByTags([`quota:${e}:${t}`])}catch(e){throw console.error("Error setting quota:",e),e}}async getBudgetAlerts(e,t){try{let{data:a,error:r}=await this.supabase.from("ai_budget_alerts").select("*").eq("entity_type",e).eq("entity_id",t).order("created_at",{ascending:!1}).limit(50);if(r)throw r;return a||[]}catch(e){return console.error("Error getting budget alerts:",e),[]}}async checkQuotas(e,t,a,r){for(let s of(await this.getQuotaUsage(e,t))){if(this.isQuotaExpired(s)){await this.resetQuota(s);continue}let t=!1;switch(s.quota_type){case"cost":t=s.current_usage+a>s.limit_value;break;case"tokens":t=s.current_usage+r>s.limit_value;break;case"requests":t=s.current_usage+1>s.limit_value}if(t)return{allowed:!1,reason:`${s.quota_type} quota exceeded for ${e}`,quotaInfo:{type:s.quota_type,current:s.current_usage,limit:s.limit_value,period:s.period,resetAt:s.reset_at}}}return{allowed:!0}}async updateQuotaUsage(e,t,a,r){for(let{entityType:s,entityId:i}of[{entityType:"user",entityId:e},{entityType:"organization",entityId:t},{entityType:"global",entityId:"global"}])for(let e of(await this.getQuotaUsage(s,i))){let t=0;switch(e.quota_type){case"cost":t=a;break;case"tokens":t=r;break;case"requests":t=1}t>0&&await this.supabase.from("ai_usage_quotas").update({current_usage:e.current_usage+t,updated_at:new Date().toISOString()}).eq("id",e.id)}}async updateCachedStats(e,t,a,r){let s=new Date().toISOString().split("T")[0];for(let{type:i,id:o}of[{type:"user",id:e},{type:"organization",id:t},{type:"global",id:"global"}]){let e=`${this.cachePrefix}cost:${i}:${o}:${s}`,t=`${this.cachePrefix}tokens:${i}:${o}:${s}`,n=`${this.cachePrefix}requests:${i}:${o}:${s}`,c=new Date;c.setHours(23,59,59,999);let l=Math.floor((c.getTime()-Date.now())/1e3);await Promise.all([this.incrementCounter(e,a,l),this.incrementCounter(t,r,l),this.incrementCounter(n,1,l)])}}async incrementCounter(e,t,a){try{let r=await this.cache.get(e)||0;await this.cache.set(e,r+t,{ttl:a})}catch(e){console.error("Error incrementing counter:",e)}}async checkBudgetAlerts(e,t){}calculateStats(e){let t=e.length,a=e.reduce((e,t)=>e+t.total_tokens,0),r=e.reduce((e,t)=>e+t.estimated_cost,0),s=e.filter(e=>e.success).length,i=e.length>0?e.reduce((e,t)=>e+t.request_duration,0)/e.length:0,o=this.aggregateByField(e,"model"),n=this.aggregateByField(e,"feature"),c=this.calculateCostTrend(e);return{totalRequests:t,totalTokens:a,totalCost:r,successRate:t>0?s/t*100:0,avgResponseTime:i,topModels:o.slice(0,5),topFeatures:n.slice(0,5),costTrend:c}}aggregateByField(e,t){return Object.entries(e.reduce((e,a)=>{let r=a[t];return e[r]||(e[r]={usage:0,cost:0}),e[r].usage+=1,e[r].cost+=a.estimated_cost,e},{})).map(([e,a])=>({[t]:e,usage:a.usage,cost:a.cost})).sort((e,t)=>t.cost-e.cost)}calculateCostTrend(e){return Object.entries(e.reduce((e,t)=>{let a=t.created_at.split("T")[0];return e[a]=(e[a]||0)+t.estimated_cost,e},{})).map(([e,t])=>({date:e,cost:t})).sort((e,t)=>e.date.localeCompare(t.date))}getStartDate(e){let t=new Date;switch(e){case"day":return new Date(t.getFullYear(),t.getMonth(),t.getDate());case"week":let a=new Date(t);return a.setDate(t.getDate()-t.getDay()),a;case"month":default:return new Date(t.getFullYear(),t.getMonth(),1);case"year":return new Date(t.getFullYear(),0,1)}}calculateResetDate(e){let t=new Date;switch(e){case"daily":let a=new Date(t);return a.setDate(t.getDate()+1),a.setHours(0,0,0,0),a;case"weekly":let r=new Date(t);return r.setDate(t.getDate()+(7-t.getDay())),r.setHours(0,0,0,0),r;default:return new Date(t.getFullYear(),t.getMonth()+1,1)}}isQuotaExpired(e){return new Date(e.reset_at)<=new Date}async resetQuota(e){let t=this.calculateResetDate(e.period);await this.supabase.from("ai_usage_quotas").update({current_usage:0,reset_at:t.toISOString(),updated_at:new Date().toISOString()}).eq("id",e.id)}getEmptyStats(){return{totalRequests:0,totalTokens:0,totalCost:0,successRate:0,avgResponseTime:0,topModels:[],topFeatures:[],costTrend:[]}}constructor(){this.supabase=(0,c.U)(),this.cache=(0,u.Q)(),this.cachePrefix="ai_usage:",this.quotaCachePrefix="ai_quota:"}}let h=null,g=()=>(h||(h=new d),h);class p{estimateRequestCost(e,t,a,r=0){let s=this.costModels.find(a=>a.provider===e&&a.model===t);return s?a/1e3*s.inputCostPer1k+r/1e3*s.outputCostPer1k+(s.requestCost||0):(console.warn(`No cost model found for ${e}:${t}`),0)}async getCostBreakdown(e,t,a,r){let s=`${this.cachePrefix}breakdown:${e}:${t}:${a.toISOString()}:${r.toISOString()}`,i=await this.cache.get(s);if(i)return i;try{let i=this.supabase.from("ai_usage_logs").select("*").gte("created_at",a.toISOString()).lte("created_at",r.toISOString());"user"===e?i=i.eq("user_id",t):"organization"===e&&(i=i.eq("organization_id",t));let{data:o,error:n}=await i;if(n)throw n;let c=this.calculateCostBreakdown(o||[]);return await this.cache.set(s,c,{ttl:3600,tags:["ai_cost",e,t]}),c}catch(e){return console.error("Error getting cost breakdown:",e),this.getEmptyBreakdown()}}async predictCosts(e,t,a){try{let r=new Date,s=new Date;s.setDate(r.getDate()-90);let i=await this.getCostBreakdown(e,t,s,r),o=await this.getCostTrends(e,t,s,r);return this.calculatePrediction(i,o,a)}catch(e){return console.error("Error predicting costs:",e),{period:a,predictedCost:0,confidence:0,factors:{trend:0,seasonality:0,growth:0},breakdown:[]}}}async getOptimizationRecommendations(e,t){let a=`${this.cachePrefix}optimizations:${e}:${t}`,r=await this.cache.get(a);if(r)return r;try{let r=new Date,s=new Date;s.setDate(r.getDate()-30);let i=await this.getCostBreakdown(e,t,s,r),o=await this.usageTracker.getUsageStats(e,t,"month"),n=this.analyzeOptimizations(i,o);return await this.cache.set(a,n,{ttl:21600,tags:["ai_cost","optimizations",e,t]}),n}catch(e){return console.error("Error getting optimization recommendations:",e),[]}}async getCostTrends(e,t,a,r,s="day"){try{let i=this.supabase.from("ai_usage_logs").select("created_at, estimated_cost, total_tokens").gte("created_at",a.toISOString()).lte("created_at",r.toISOString()).order("created_at");"user"===e?i=i.eq("user_id",t):"organization"===e&&(i=i.eq("organization_id",t));let{data:o,error:n}=await i;if(n)throw n;return this.aggregateTrends(o||[],s)}catch(e){return console.error("Error getting cost trends:",e),[]}}async compareModelCosts(e,t,a="month"){try{let r=new Date,s=new Date;switch(a){case"week":s.setDate(r.getDate()-7);break;case"month":s.setMonth(r.getMonth()-1);break;case"quarter":s.setMonth(r.getMonth()-3)}await this.getCostBreakdown(e,t,s,r);let i=this.supabase.from("ai_usage_logs").select("provider, model, estimated_cost, total_tokens, success").gte("created_at",s.toISOString()).lte("created_at",r.toISOString());"user"===e?i=i.eq("user_id",t):"organization"===e&&(i=i.eq("organization_id",t));let{data:o,error:n}=await i;if(n)throw n;let c=this.calculateModelComparison(o||[]),l=this.generateModelRecommendations(c);return{models:c,recommendations:l}}catch(e){return console.error("Error comparing model costs:",e),{models:[],recommendations:[]}}}async calculateFeatureROI(e,t,a="month"){let r=new Date,s=new Date;switch(a){case"month":s.setMonth(r.getMonth()-1);break;case"quarter":s.setMonth(r.getMonth()-3);break;case"year":s.setFullYear(r.getFullYear()-1)}let i=(await this.getCostBreakdown("organization",e,s,r)).byFeature[t]||0,o=this.estimateFeatureValue(t,i),n=i>0?(o-i)/i*100:0;return{cost:i,estimatedValue:o,roi:n,metrics:{timeSaved:Math.round(10*i),tasksAutomated:Math.round(5*i),errorReduction:Math.min(95,2*i),userSatisfaction:Math.min(100,70+n/10)}}}calculateCostBreakdown(e){let t={total:0,byProvider:{},byModel:{},byFeature:{},byUser:{},byOrganization:{},byTimeOfDay:{},byDayOfWeek:{}};for(let a of e){let e=a.estimated_cost||0;t.total+=e,t.byProvider[a.provider]=(t.byProvider[a.provider]||0)+e;let r=`${a.provider}:${a.model}`;t.byModel[r]=(t.byModel[r]||0)+e,t.byFeature[a.feature]=(t.byFeature[a.feature]||0)+e,t.byUser[a.user_id]=(t.byUser[a.user_id]||0)+e,t.byOrganization[a.organization_id]=(t.byOrganization[a.organization_id]||0)+e;let s=new Date(a.created_at).getHours();t.byTimeOfDay[s]=(t.byTimeOfDay[s]||0)+e;let i=new Date(a.created_at).getDay();t.byDayOfWeek[i]=(t.byDayOfWeek[i]||0)+e}return t}calculatePrediction(e,t,a){if(0===t.length)return{period:a,predictedCost:0,confidence:0,factors:{trend:0,seasonality:0,growth:0},breakdown:[]};let r=this.calculateTrendFactor(t),s=this.calculateSeasonalityFactor(t),i=this.calculateGrowthFactor(t),o=t.slice(-7).reduce((e,t)=>e+t.cost,0)/7,n=1;switch(a){case"daily":n=1;break;case"weekly":n=7;break;case"monthly":n=30;break;case"yearly":n=365}let c=o*n*(1+r+s+i),l=Math.max(0,Math.min(100,100-50*Math.abs(r))),u=e.total,d=Object.entries(e.byProvider).map(([e,t])=>({provider:e,model:"",feature:"",estimatedCost:t/u*c,percentage:t/u*100}));return{period:a,predictedCost:c,confidence:l,factors:{trend:r,seasonality:s,growth:i},breakdown:d}}analyzeOptimizations(e,t){let a=[];return a.push(...this.analyzeModelSwitching(e)),t.successRate>90&&a.push({type:"caching",description:"Increase cache TTL for similar requests",currentCost:e.total,optimizedCost:.8*e.total,savings:.2*e.total,savingsPercentage:20,implementation:"Increase cache TTL from 1 hour to 4 hours for similar requests",impact:"medium",effort:"low"}),t.totalRequests>100&&a.push({type:"batching",description:"Implement request batching for similar operations",currentCost:e.total,optimizedCost:.85*e.total,savings:.15*e.total,savingsPercentage:15,implementation:"Group similar requests and process in batches",impact:"medium",effort:"medium"}),a.sort((e,t)=>t.savings-e.savings)}analyzeModelSwitching(e){let t=[];for(let[a,r]of Object.entries(e.byModel))a.includes("gpt-4")&&r>10&&t.push({type:"model_switch",description:`Switch from ${a} to gpt-3.5-turbo for non-critical tasks`,currentCost:r,optimizedCost:.1*r,savings:.9*r,savingsPercentage:90,implementation:"Evaluate task complexity and switch to gpt-3.5-turbo where appropriate",impact:"high",effort:"medium"}),a.includes("claude-3-opus")&&r>5&&t.push({type:"model_switch",description:`Switch from ${a} to claude-3-haiku for simple tasks`,currentCost:r,optimizedCost:.02*r,savings:.98*r,savingsPercentage:98,implementation:"Use claude-3-haiku for simple text processing tasks",impact:"high",effort:"low"});return t}calculateModelComparison(e){return Object.values(e.reduce((e,t)=>{let a=`${t.provider}:${t.model}`;return e[a]||(e[a]={provider:t.provider,model:t.model,totalCost:0,totalTokens:0,requests:0,successfulRequests:0}),e[a].totalCost+=t.estimated_cost,e[a].totalTokens+=t.total_tokens,e[a].requests+=1,t.success&&(e[a].successfulRequests+=1),e},{})).map(e=>({...e,avgCostPerRequest:e.requests>0?e.totalCost/e.requests:0,avgCostPerToken:e.totalTokens>0?e.totalCost/e.totalTokens:0,efficiency:e.requests>0?e.successfulRequests/e.requests*100:0}))}generateModelRecommendations(e){let t=[],a=[...e].sort((e,t)=>t.totalCost-e.totalCost);a.length>0&&a[0].totalCost>50&&t.push(`Consider alternatives to ${a[0].provider}:${a[0].model} - highest cost contributor`);let r=[...e].sort((e,t)=>e.efficiency-t.efficiency);return r.length>0&&r[0].efficiency<90&&t.push(`Review ${r[0].provider}:${r[0].model} - low success rate (${r[0].efficiency.toFixed(1)}%)`),t}aggregateTrends(e,t){let a=Object.entries(e.reduce((e,a)=>{let r;let s=new Date(a.created_at);switch(t){case"hour":r=`${s.getFullYear()}-${s.getMonth()}-${s.getDate()}-${s.getHours()}`;break;case"day":r=`${s.getFullYear()}-${s.getMonth()}-${s.getDate()}`;break;case"week":let i=new Date(s);i.setDate(s.getDate()-s.getDay()),r=i.toISOString().split("T")[0];break;case"month":r=`${s.getFullYear()}-${s.getMonth()}`;break;default:r=s.toISOString().split("T")[0]}return e[r]||(e[r]={cost:0,tokens:0,requests:0}),e[r].cost+=a.estimated_cost,e[r].tokens+=a.total_tokens,e[r].requests+=1,e},{})).map(([e,t])=>({period:e,cost:t.cost,tokens:t.tokens,requests:t.requests,avgCostPerRequest:t.requests>0?t.cost/t.requests:0,avgCostPerToken:t.tokens>0?t.cost/t.tokens:0,changeFromPrevious:0,changePercentage:0}));for(let e=1;e<a.length;e++){let t=a[e],r=a[e-1];t.changeFromPrevious=t.cost-r.cost,t.changePercentage=r.cost>0?t.changeFromPrevious/r.cost*100:0}return a.sort((e,t)=>e.period.localeCompare(t.period))}calculateTrendFactor(e){if(e.length<2)return 0;let t=e.slice(-7);return t.reduce((e,t)=>e+t.changePercentage,0)/t.length/100}calculateSeasonalityFactor(e){return 0}calculateGrowthFactor(e){if(e.length<4)return 0;let t=e.slice(0,Math.floor(e.length/4)),a=e.slice(-Math.floor(e.length/4)),r=t.reduce((e,t)=>e+t.cost,0)/t.length,s=a.reduce((e,t)=>e+t.cost,0)/a.length;return r>0?(s-r)/r:0}estimateFeatureValue(e,t){let a={ticket_analysis:15,email_generation:8,report_generation:12,chat_support:10,document_processing:6,default:5};return t*(a[e]||a.default)}getEmptyBreakdown(){return{total:0,byProvider:{},byModel:{},byFeature:{},byUser:{},byOrganization:{},byTimeOfDay:{},byDayOfWeek:{}}}constructor(){this.supabase=(0,c.U)(),this.cache=(0,u.Q)(),this.usageTracker=g(),this.cachePrefix="ai_cost:",this.costModels=[{provider:"openai",model:"gpt-4",inputCostPer1k:.03,outputCostPer1k:.06,lastUpdated:"2024-01-01"},{provider:"openai",model:"gpt-4-turbo",inputCostPer1k:.01,outputCostPer1k:.03,lastUpdated:"2024-01-01"},{provider:"openai",model:"gpt-3.5-turbo",inputCostPer1k:.0015,outputCostPer1k:.002,lastUpdated:"2024-01-01"},{provider:"openai",model:"gpt-3.5-turbo-16k",inputCostPer1k:.003,outputCostPer1k:.004,lastUpdated:"2024-01-01"},{provider:"anthropic",model:"claude-3-opus",inputCostPer1k:.015,outputCostPer1k:.075,lastUpdated:"2024-01-01"},{provider:"anthropic",model:"claude-3-sonnet",inputCostPer1k:.003,outputCostPer1k:.015,lastUpdated:"2024-01-01"},{provider:"anthropic",model:"claude-3-haiku",inputCostPer1k:25e-5,outputCostPer1k:.00125,lastUpdated:"2024-01-01"},{provider:"google",model:"gemini-pro",inputCostPer1k:5e-4,outputCostPer1k:.0015,lastUpdated:"2024-01-01"},{provider:"google",model:"gemini-pro-vision",inputCostPer1k:.0025,outputCostPer1k:.01,lastUpdated:"2024-01-01"}]}}let m=null,y=()=>(m||(m=new p),m);class f{constructor(){this.cache=(0,u.Q)(),this.usageTracker=g(),this.serviceHealth=new Map,this.circuitBreakers=new Map,this.failureThreshold=5,this.recoveryTimeout=6e4,this.healthCheckInterval=3e4,this.maxResponseTime=3e4,this.errorRateThreshold=.5,this.fallbackStrategies={ticket_analysis:{primary:{provider:"openai",model:"gpt-4-turbo"},fallbacks:[{provider:"anthropic",model:"claude-3-sonnet",priority:1},{provider:"openai",model:"gpt-3.5-turbo",priority:2},{provider:"anthropic",model:"claude-3-haiku",priority:3}],degradedMode:{enabled:!0,cacheOnly:!0,simplifiedResponse:!1}},email_generation:{primary:{provider:"anthropic",model:"claude-3-sonnet"},fallbacks:[{provider:"openai",model:"gpt-3.5-turbo",priority:1},{provider:"anthropic",model:"claude-3-haiku",priority:2}],degradedMode:{enabled:!0,cacheOnly:!1,simplifiedResponse:!0}},chat_support:{primary:{provider:"openai",model:"gpt-3.5-turbo"},fallbacks:[{provider:"anthropic",model:"claude-3-haiku",priority:1}],degradedMode:{enabled:!0,cacheOnly:!0,simplifiedResponse:!0}},default:{primary:{provider:"openai",model:"gpt-3.5-turbo"},fallbacks:[{provider:"anthropic",model:"claude-3-haiku",priority:1}],degradedMode:{enabled:!0,cacheOnly:!0,simplifiedResponse:!0}}},this.startHealthMonitoring()}async makeReliableRequest(e){let t=this.fallbackStrategies[e.feature]||this.fallbackStrategies.default,a=Date.now(),r=await this.usageTracker.canMakeRequest(e.userId,e.organizationId,.01,100);if(!r.allowed)return this.handleQuotaExceeded(e,r.reason||"Quota exceeded");try{let r=await this.tryService(t.primary.provider,t.primary.model,e);if(r)return await this.recordSuccess(t.primary.provider,t.primary.model,Date.now()-a),r}catch(e){await this.recordFailure(t.primary.provider,t.primary.model,e),console.warn(`Primary service failed: ${t.primary.provider}:${t.primary.model}`,e)}for(let r of t.fallbacks.sort((e,t)=>e.priority-t.priority))if(this.isServiceAvailable(r.provider,r.model))try{let t=await this.tryService(r.provider,r.model,e);if(t)return await this.recordSuccess(r.provider,r.model,Date.now()-a),t.fromFallback=!0,t}catch(e){await this.recordFailure(r.provider,r.model,e),console.warn(`Fallback service failed: ${r.provider}:${r.model}`,e)}if(t.degradedMode?.enabled)return this.handleDegradedMode(e,t.degradedMode);throw Error("All AI services are unavailable")}isServiceAvailable(e,t){let a=`${e}:${t}`,r=this.circuitBreakers.get(a);if(!r)return!0;switch(r.state){case"closed":case"half-open":default:return!0;case"open":if(Date.now()>=r.nextRetryTime.getTime())return r.state="half-open",r.successCount=0,!0;return!1}}getServiceHealth(){return Array.from(this.serviceHealth.values())}getCircuitBreakerStates(){return Array.from(this.circuitBreakers.values())}resetCircuitBreaker(e,t){let a=`${e}:${t}`,r=this.circuitBreakers.get(a);r&&(r.state="closed",r.failureCount=0,r.successCount=0,console.log(`Circuit breaker reset for ${a}`))}updateFallbackStrategy(e,t){this.fallbackStrategies[e]=t,console.log(`Updated fallback strategy for feature: ${e}`)}async tryService(e,t,a){let r=`${e}:${t}`;if(!this.isServiceAvailable(e,t))throw Error(`Service ${r} is circuit broken`);if("critical"!==a.priority){let r=await this.getCachedResponse(a);if(r)return{...r,provider:e,model:t,fromCache:!0,fromFallback:!1,degraded:!1}}let s=await this.callAIService(e,t,a);return s&&"critical"!==a.priority&&await this.cacheResponse(a,s),s}async callAIService(e,t,a){let r=Date.now();try{if(await new Promise(e=>setTimeout(e,2e3*Math.random())),.05>Math.random())throw Error("Simulated AI service error");let s=Date.now()-r;return{content:`AI response from ${e}:${t} for: ${a.prompt.substring(0,50)}...`,provider:e,model:t,fromCache:!1,fromFallback:!1,degraded:!1,metadata:{duration:s,tokens:Math.floor(1e3*Math.random())+100}}}catch(a){throw Date.now(),await this.recordFailure(e,t,a),a}}async getCachedResponse(e){let t=this.generateCacheKey(e);return await this.cache.get(t)}async cacheResponse(e,t){let a=this.generateCacheKey(e);await this.cache.set(a,{content:t.content,metadata:t.metadata},{ttl:3600,tags:["ai_responses",e.feature]})}generateCacheKey(e){let t=this.hashString(e.prompt+JSON.stringify(e.options));return`ai_response:${e.feature}:${t}`}hashString(e){let t=0;for(let a=0;a<e.length;a++)t=(t<<5)-t+e.charCodeAt(a),t&=t;return Math.abs(t).toString(36)}async recordSuccess(e,t,a){let r=`${e}:${t}`,s=this.serviceHealth.get(r)||this.createServiceHealth(e,t);s.status="healthy",s.lastCheck=new Date,s.responseTime=a,s.consecutiveFailures=0,this.serviceHealth.set(r,s);let i=this.circuitBreakers.get(r);i&&"half-open"===i.state&&(i.successCount++,i.successCount>=3&&(i.state="closed",i.failureCount=0,console.log(`Circuit breaker closed for ${r}`))),l.qd.increment("ai.service_success",{provider:e,model:t}),l.qd.timing("ai.service_response_time",a,{provider:e,model:t})}async recordFailure(e,t,a){let r=`${e}:${t}`,s=this.serviceHealth.get(r)||this.createServiceHealth(e,t);s.consecutiveFailures++,s.lastError=a.message,s.lastCheck=new Date,s.consecutiveFailures>=this.failureThreshold?s.status="unhealthy":s.consecutiveFailures>=2&&(s.status="degraded"),this.serviceHealth.set(r,s);let i=this.circuitBreakers.get(r);i||(i=this.createCircuitBreaker(e,t),this.circuitBreakers.set(r,i)),i.failureCount++,i.lastFailureTime=new Date,i.failureCount>=this.failureThreshold&&(i.state="open",i.nextRetryTime=new Date(Date.now()+this.recoveryTimeout),console.warn(`Circuit breaker opened for ${r}`)),l.qd.increment("ai.service_failure",{provider:e,model:t}),l.qd.increment("ai.service_errors",{provider:e,model:t,error_type:a.name})}createServiceHealth(e,t){return{provider:e,model:t,status:"healthy",lastCheck:new Date,responseTime:0,errorRate:0,consecutiveFailures:0}}createCircuitBreaker(e,t){return{provider:e,model:t,state:"closed",failureCount:0,lastFailureTime:new Date,nextRetryTime:new Date,successCount:0}}async handleQuotaExceeded(e,t){let a=await this.getCachedResponse(e);return a?{content:a.content||"Cached response due to quota limits",provider:"cache",model:"cached",fromCache:!0,fromFallback:!1,degraded:!0,metadata:{reason:"quota_exceeded",...a.metadata}}:{content:"Service temporarily unavailable due to usage limits. Please try again later.",provider:"system",model:"degraded",fromCache:!1,fromFallback:!1,degraded:!0,metadata:{reason:"quota_exceeded"}}}async handleDegradedMode(e,t){if(t.cacheOnly){let t=await this.getCachedResponse(e);if(t)return{content:t.content||"Cached response (degraded mode)",provider:"cache",model:"cached",fromCache:!0,fromFallback:!1,degraded:!0,metadata:t.metadata}}if(t.simplifiedResponse)return{content:this.generateSimplifiedResponse(e),provider:"system",model:"simplified",fromCache:!1,fromFallback:!1,degraded:!0,metadata:{mode:"simplified"}};throw Error("No degraded mode available")}generateSimplifiedResponse(e){switch(e.feature){case"ticket_analysis":return"This ticket requires manual review. AI analysis is temporarily unavailable.";case"email_generation":return"Thank you for your inquiry. We will respond to your request shortly.";case"chat_support":return"I apologize, but I'm experiencing technical difficulties. Please contact support directly.";default:return"AI service is temporarily unavailable. Please try again later."}}startHealthMonitoring(){setInterval(async()=>{await this.performHealthChecks()},this.healthCheckInterval)}async performHealthChecks(){for(let[e,t]of this.serviceHealth)try{let e=Date.now();await this.pingService(t.provider,t.model);let a=Date.now()-e;t.responseTime=a,t.lastCheck=new Date,"unhealthy"===t.status&&t.consecutiveFailures>0&&(t.consecutiveFailures=Math.max(0,t.consecutiveFailures-1),0===t.consecutiveFailures&&(t.status="healthy"))}catch(e){t.consecutiveFailures++,t.lastError=e.message,t.status=t.consecutiveFailures>=this.failureThreshold?"unhealthy":"degraded"}}async pingService(e,t){if(.95>Math.random())return Promise.resolve();throw Error("Health check failed")}}let w=null,v=()=>(w||(w=new f),w);var k=a(55511);void 0===globalThis.crypto&&(globalThis.crypto=k.webcrypto);class b{async filterInput(e,t,a,r){let s=Date.now();try{let i=this.validateInput(e);if(!i.allowed)return i;let o=await this.applyContentPolicies(e);if(!o.allowed)return await this.logFilterEvent(t,a,r,"policy_violation",o),o;let n=await this.checkSimilarity(e,t,r);if(n.isDuplicate)return{allowed:!1,reason:"Duplicate or very similar request detected",severity:"low",categories:["duplicate"],confidence:n.similarity};let c=await this.performAdvancedAnalysis(e);if(!c.allowed)return await this.logFilterEvent(t,a,r,"advanced_filter",c),c;let u=Date.now()-s;return l.qd.timing("content_filter.input_processing_time",u),l.qd.increment("content_filter.input_allowed",{feature:r}),{allowed:!0,severity:"low",categories:[],confidence:1,sanitizedContent:o.sanitizedContent||e}}catch(e){return console.error("Content filtering error:",e),l.qd.increment("content_filter.errors"),{allowed:!0,reason:"Filter error - content allowed by default",severity:"low",categories:["filter_error"],confidence:.5}}}async validateOutput(e,t,a,r){let s=Date.now();try{let i=await this.checkHarmfulContent(e);if(!i.allowed)return i;let o=this.validateResponseQuality(e,t);if(!o.allowed)return o;let n=this.checkDataLeakage(e);if(!n.allowed)return n;let c=this.validateResponseFormat(e);if(!c.allowed)return c;let u=Date.now()-s;return l.qd.timing("content_filter.output_processing_time",u),l.qd.increment("content_filter.output_allowed",{provider:a,model:r}),{allowed:!0,severity:"low",categories:[],confidence:1,sanitizedContent:e}}catch(e){return console.error("Output validation error:",e),l.qd.increment("content_filter.output_errors"),{allowed:!1,reason:"Output validation failed",severity:"high",categories:["validation_error"],confidence:.8}}}async checkSimilarity(e,t,a){let r=`${this.cachePrefix}similarity:${t}:${a}`;try{let s=await this.cache.get(r)||[];for(let t of s){let s=this.calculateSimilarity(e,t);if(s>=this.similarityThreshold)return l.qd.increment("content_filter.duplicate_detected",{feature:a}),{isDuplicate:!0,similarity:s,existingRequestId:this.hashString(t),cacheKey:r}}let i=[e,...s.slice(0,9)];return await this.cache.set(r,i,{ttl:3600,tags:["similarity_check",t]}),{isDuplicate:!1,similarity:0}}catch(e){return console.error("Similarity check error:",e),{isDuplicate:!1,similarity:0}}}updateContentPolicy(e,t){let a=this.contentPolicies.findIndex(t=>t.category===e);a>=0?this.contentPolicies[a]={...this.contentPolicies[a],...t}:this.contentPolicies.push(t),console.log(`Updated content policy for category: ${e}`)}async getFilteringStats(e="day"){return{totalFiltered:45,byCategory:{spam:20,personal_info:15,profanity:8,harassment:2},bySeverity:{low:28,medium:15,high:2,critical:0},byAction:{sanitize:35,warn:8,block:2},trends:[{date:"2024-01-01",count:12},{date:"2024-01-02",count:15},{date:"2024-01-03",count:18}]}}validateInput(e){for(let t of this.validationRules){if(!t.enabled)continue;let a=this.applyValidationRule(e,t);if(!a.allowed)return a}return{allowed:!0,severity:"low",categories:[],confidence:1}}applyValidationRule(e,t){switch(t.type){case"length":let a=e.length;if(a<t.parameters.min||a>t.parameters.max)return{allowed:!1,reason:t.errorMessage,severity:"medium",categories:["validation"],confidence:1};break;case"content":if(RegExp(t.parameters.pattern,"i").test(e))return{allowed:!1,reason:t.errorMessage,severity:"high",categories:["injection"],confidence:.9};break;case"language":let r=this.detectLanguage(e);if(!t.parameters.allowedLanguages.includes(r))return{allowed:!1,reason:t.errorMessage,severity:"low",categories:["language"],confidence:.7}}return{allowed:!0,severity:"low",categories:[],confidence:1}}async applyContentPolicies(e){let t=e,a=[],r="low",s=!1;for(let i of this.contentPolicies)if(i.enabled&&this.checkPolicyViolations(e,i).length>0)switch(a.push(i.category),this.compareSeverity(i.severity,r)>0&&(r=i.severity),i.action){case"block":s=!0;break;case"sanitize":t=this.sanitizeContent(t,i);break;case"warn":console.warn(`Content policy warning: ${i.category}`)}return s?{allowed:!1,reason:`Content violates policies: ${a.join(", ")}`,severity:r,categories:a,confidence:.9}:{allowed:!0,severity:r,categories:a,confidence:.8,sanitizedContent:t}}checkPolicyViolations(e,t){let a=[],r=e.toLowerCase();for(let e of t.patterns){let t=RegExp(e,"gi"),s=r.match(t);s&&a.push(...s)}return a}sanitizeContent(e,t){let a=e;for(let e of t.patterns){let r=RegExp(e,"gi");a=a.replace(r,e=>"personal_info"===t.category?"[REDACTED]":"profanity"===t.category?"*".repeat(e.length):"[FILTERED]")}return a}async performAdvancedAnalysis(e){for(let t of["bypass","circumvent","hack","exploit","vulnerability","jailbreak","prompt injection","ignore instructions"])if(e.toLowerCase().includes(t))return{allowed:!1,reason:"Suspicious content pattern detected",severity:"high",categories:["suspicious"],confidence:.8};return{allowed:!0,severity:"low",categories:[],confidence:.9}}checkHarmfulContent(e){let t=["bomb","explosive","poison","drug","weapon","hack","illegal","fraud","scam"],a=e.toLowerCase();for(let e of["how to make","instructions for","step by step","recipe for","guide to","tutorial on"])if(a.includes(e)){for(let e of t)if(a.includes(e))return Promise.resolve({allowed:!1,reason:"Response contains potentially harmful instructions",severity:"critical",categories:["harmful_instructions"],confidence:.9})}return Promise.resolve({allowed:!0,severity:"low",categories:[],confidence:1})}validateResponseQuality(e,t){return e.length<10?{allowed:!1,reason:"Response too short",severity:"medium",categories:["quality"],confidence:1}:e.includes("I cannot")&&e.includes("I'm sorry")?{allowed:!1,reason:"AI refused to respond",severity:"medium",categories:["refusal"],confidence:.8}:{allowed:!0,severity:"low",categories:[],confidence:.9}}checkDataLeakage(e){for(let t of["\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b","\\b\\d{3}[\\s.-]?\\d{3}[\\s.-]?\\d{4}\\b","\\b\\d{3}-\\d{2}-\\d{4}\\b","password","secret","token","key","credential"])if(RegExp(t,"i").test(e))return{allowed:!1,reason:"Potential data leakage detected",severity:"high",categories:["data_leakage"],confidence:.8};return{allowed:!0,severity:"low",categories:[],confidence:1}}validateResponseFormat(e){return e.includes("<script>")||e.includes("javascript:")?{allowed:!1,reason:"Response contains executable code",severity:"high",categories:["code_injection"],confidence:1}:{allowed:!0,severity:"low",categories:[],confidence:1}}calculateSimilarity(e,t){let a=new Set(e.toLowerCase().split(/\s+/)),r=new Set(t.toLowerCase().split(/\s+/)),s=new Set([...a].filter(e=>r.has(e))),i=new Set([...a,...r]);return s.size/i.size}detectLanguage(e){let t=["the","and","or","but","in","on","at","to","for","of","with","by"],a=e.toLowerCase().split(/\s+/);return a.filter(e=>t.includes(e)).length>.1*a.length?"en":"unknown"}compareSeverity(e,t){let a={low:1,medium:2,high:3,critical:4};return(a[e]||0)-(a[t]||0)}hashString(e){let t=0;for(let a=0;a<e.length;a++)t=(t<<5)-t+e.charCodeAt(a),t&=t;return Math.abs(t).toString(36)}async logFilterEvent(e,t,a,r,s){try{console.log("Filter event:",{userId:e,organizationId:t,feature:a,eventType:r,severity:s.severity,categories:s.categories,timestamp:new Date().toISOString()}),l.qd.increment("content_filter.violations",{feature:a,severity:s.severity,category:s.categories[0]||"unknown"})}catch(e){console.error("Error logging filter event:",e)}}constructor(){this.cache=(0,u.Q)(),this.cachePrefix="content_filter:",this.similarityThreshold=.85,this.maxContentLength=5e4,this.minContentLength=1,this.contentPolicies=[{category:"hate_speech",enabled:!0,action:"block",severity:"critical",patterns:["hate","racist","sexist","homophobic","transphobic","nazi","fascist","supremacist","genocide","ethnic cleansing"],description:"Content promoting hate speech or discrimination"},{category:"violence",enabled:!0,action:"block",severity:"high",patterns:["kill","murder","assassinate","bomb","terrorist","violence","harm","hurt","attack","weapon"],description:"Content promoting violence or harm"},{category:"harassment",enabled:!0,action:"warn",severity:"medium",patterns:["harass","bully","threaten","intimidate","stalk","doxx","doxing","revenge","blackmail"],description:"Content that could constitute harassment"},{category:"spam",enabled:!0,action:"sanitize",severity:"low",patterns:["click here","buy now","limited time","act fast","guaranteed","risk free","no obligation"],description:"Spam or promotional content"},{category:"personal_info",enabled:!0,action:"sanitize",severity:"medium",patterns:["\\b\\d{3}-\\d{2}-\\d{4}\\b","\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b","\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b","\\b\\d{3}[\\s.-]?\\d{3}[\\s.-]?\\d{4}\\b"],description:"Personal identifiable information"},{category:"profanity",enabled:!0,action:"sanitize",severity:"low",patterns:["damn","hell","crap","shit","fuck","bitch","ass","bastard","piss","cock","dick"],description:"Profane language"}],this.validationRules=[{name:"content_length",type:"length",enabled:!0,parameters:{min:1,max:5e4},errorMessage:"Content must be between 1 and 50,000 characters"},{name:"no_html_injection",type:"content",enabled:!0,parameters:{pattern:"<script|<iframe|<object|<embed|javascript:|data:"},errorMessage:"HTML/JavaScript injection detected"},{name:"no_sql_injection",type:"content",enabled:!0,parameters:{pattern:"(union|select|insert|update|delete|drop|create|alter)\\s+"},errorMessage:"SQL injection pattern detected"},{name:"language_detection",type:"language",enabled:!0,parameters:{allowedLanguages:["en","es","fr","de","it","pt"]},errorMessage:"Content language not supported"},{name:"encoding_validation",type:"format",enabled:!0,parameters:{encoding:"utf-8"},errorMessage:"Invalid character encoding"}]}}let _=null,C=()=>(_||(_=new b),_);async function q(e){try{let t=(0,c.U)(),{data:{user:a},error:r}=await t.auth.getUser();if(r||!a)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await t.from("user_profiles").select("role, organization_id").eq("user_id",a.id).single();if(!s||!["admin","system_admin"].includes(s.role))return n.NextResponse.json({error:"Insufficient permissions"},{status:403});let i=new URL(e.url),o=i.searchParams.get("section")||"overview",l=i.searchParams.get("entityType")||"organization",u=i.searchParams.get("entityId")||s.organization_id,d=i.searchParams.get("period")||"month",h=i.searchParams.get("startDate"),p=i.searchParams.get("endDate"),m=g(),f=y(),w=v(),k=C(),b={};switch(o){case"overview":b=await D(m,f,l,u,d);break;case"usage":b=await P(m,l,u,d);break;case"costs":b=await R(f,l,u,h,p);break;case"quotas":b=await x(m,l,u);break;case"predictions":b=await T(f,l,u,d);break;case"optimizations":b=await $(f,l,u);break;case"reliability":b=await F(w);break;case"filtering":b=await M(k,d);break;case"models":b=await I(f,l,u,d);break;case"alerts":b=await z(m,l,u);break;default:return n.NextResponse.json({error:"Invalid section"},{status:400})}return n.NextResponse.json({success:!0,section:o,data:b,metadata:{entityType:l,entityId:u,period:d,timestamp:new Date().toISOString()}})}catch(e){return console.error("AI Cost API error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function S(e){try{let t=(0,c.U)(),{data:{user:a},error:r}=await t.auth.getUser();if(r||!a)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await t.from("user_profiles").select("role, organization_id").eq("user_id",a.id).single();if(!s||!["admin","system_admin"].includes(s.role))return n.NextResponse.json({error:"Insufficient permissions"},{status:403});let{action:i,...o}=await e.json(),l=g(),u=v(),d=C(),h={};switch(i){case"set_quota":h=await O(l,o);break;case"reset_circuit_breaker":h=await A(u,o);break;case"update_content_policy":h=await E(d,o);break;case"update_fallback_strategy":h=await U(u,o);break;default:return n.NextResponse.json({error:"Invalid action"},{status:400})}return n.NextResponse.json({success:!0,action:i,result:h,timestamp:new Date().toISOString()})}catch(e){return console.error("AI Cost API POST error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function D(e,t,a,r,s){let[i,o,n]=await Promise.all([e.getUsageStats(a,r,s),e.getQuotaUsage(a,r),e.getBudgetAlerts(a,r)]),c=new Date,l=new Date;l.setDate(c.getDate()-30);let u=await t.getCostBreakdown(a,r,l,c);return{summary:{totalRequests:i.totalRequests,totalCost:i.totalCost,totalTokens:i.totalTokens,successRate:i.successRate,avgResponseTime:i.avgResponseTime},quotas:o.map(e=>({...e,usagePercentage:e.limit_value>0?e.current_usage/e.limit_value*100:0,status:j(e)})),recentAlerts:n.slice(0,5),costBreakdown:{byProvider:u.byProvider,byFeature:u.byFeature,total:u.total},trends:i.costTrend}}async function P(e,t,a,r){let s=await e.getUsageStats(t,a,r);return{overview:{totalRequests:s.totalRequests,successfulRequests:Math.round(s.totalRequests*(s.successRate/100)),failedRequests:s.totalRequests-Math.round(s.totalRequests*(s.successRate/100)),totalTokens:s.totalTokens,totalCost:s.totalCost,avgResponseTime:s.avgResponseTime},topModels:s.topModels,topFeatures:s.topFeatures,trends:s.costTrend,performance:{successRate:s.successRate,avgResponseTime:s.avgResponseTime,cacheHitRate:0}}}async function R(e,t,a,r,s){let i=s?new Date(s):new Date,o=new Date(r||i.getTime()-2592e6),[n,c,l]=await Promise.all([e.getCostBreakdown(t,a,o,i),e.getCostTrends(t,a,o,i),e.compareModelCosts(t,a,"month")]);return{breakdown:n,trends:c,modelComparison:l,insights:{mostExpensive:Object.entries(n.byProvider).sort(([,e],[,t])=>t-e)[0],fastestGrowing:c.length>1?c[c.length-1].changePercentage:0,efficiency:l.models.length>0?l.models.reduce((e,t)=>e+t.efficiency,0)/l.models.length:0}}}async function x(e,t,a){let r=await e.getQuotaUsage(t,a);return{quotas:r.map(e=>({...e,usagePercentage:e.limit_value>0?e.current_usage/e.limit_value*100:0,status:j(e),timeUntilReset:new Date(e.reset_at).getTime()-Date.now()})),recommendations:function(e){let t=[];for(let a of e){let e=a.limit_value>0?a.current_usage/a.limit_value*100:0;e>80&&t.push(`Consider increasing ${a.quota_type} quota for ${a.entity_type}`),e<20&&t.push(`${a.quota_type} quota for ${a.entity_type} may be too high`)}return t}(r)}}async function T(e,t,a,r){let s=await e.predictCosts(t,a,r);return{prediction:s,scenarios:{conservative:{...s,predictedCost:.8*s.predictedCost},aggressive:{...s,predictedCost:1.3*s.predictedCost}},recommendations:function(e){let t=[];return e.confidence<.7&&t.push("Prediction confidence is low - consider gathering more historical data"),e.factors.trend>.2&&t.push("Costs are trending upward - consider optimization strategies"),e.factors.growth>.3&&t.push("High growth rate detected - review budget allocations"),t}(s)}}async function $(e,t,a){let r=await e.getOptimizationRecommendations(t,a),s=r.reduce((e,t)=>e+t.savings,0),i=r.reduce((e,t)=>e+("low"===t.effort?1:"medium"===t.effort?2:3),0);return{optimizations:r,summary:{totalPotentialSavings:s,averageImplementationEffort:i/r.length,quickWins:r.filter(e=>"low"===e.effort&&"low"!==e.impact),highImpact:r.filter(e=>"high"===e.impact)}}}async function F(e){let[t,a]=await Promise.all([e.getServiceHealth(),e.getCircuitBreakerStates()]);return{serviceHealth:t,circuitBreakers:a,summary:{healthyServices:t.filter(e=>"healthy"===e.status).length,degradedServices:t.filter(e=>"degraded"===e.status).length,unhealthyServices:t.filter(e=>"unhealthy"===e.status).length,openCircuitBreakers:a.filter(e=>"open"===e.state).length}}}async function M(e,t){let a=await e.getFilteringStats(t);return{stats:a,insights:{topViolationCategory:Object.entries(a.byCategory).sort(([,e],[,t])=>t-e)[0]?.[0]||"none",filteringRate:a.totalFiltered>0?a.totalFiltered/(a.totalFiltered+1e3)*100:0,severityDistribution:a.bySeverity}}}async function I(e,t,a,r){let s=await e.compareModelCosts(t,a,r);return{...s,insights:{mostCostEffective:s.models.length>0?s.models.reduce((e,t)=>t.avgCostPerToken<e.avgCostPerToken?t:e):null,leastEfficient:s.models.length>0?s.models.reduce((e,t)=>t.efficiency<e.efficiency?t:e):null}}}async function z(e,t,a){let r=await e.getBudgetAlerts(t,a);return{alerts:r,summary:{total:r.length,critical:r.filter(e=>"exceeded"===e.alert_type).length,warnings:r.filter(e=>"warning"===e.alert_type).length,recent:r.filter(e=>new Date(e.created_at).getTime()>Date.now()-864e5).length}}}async function O(e,t){let{entityType:a,entityId:r,period:s,quotaType:i,limitValue:o}=t;return await e.setQuota(a,r,s,i,o),{message:"Quota updated successfully"}}async function A(e,t){let{provider:a,model:r}=t;return e.resetCircuitBreaker(a,r),{message:"Circuit breaker reset successfully"}}async function E(e,t){let{category:a,policy:r}=t;return e.updateContentPolicy(a,r),{message:"Content policy updated successfully"}}async function U(e,t){let{feature:a,strategy:r}=t;return e.updateFallbackStrategy(a,r),{message:"Fallback strategy updated successfully"}}function j(e){let t=e.limit_value>0?e.current_usage/e.limit_value*100:0;return new Date(e.reset_at)<=new Date?"expired":t>=100?"exceeded":t>=90?"warning":"normal"}let B=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/ai-cost/route",pathname:"/api/admin/ai-cost",filename:"route",bundlePath:"app/api/admin/ai-cost/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\ai-cost\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:H,workUnitAsyncStorage:Q,serverHooks:L}=B;function N(){return(0,o.patchFetch)({workAsyncStorage:H,workUnitAsyncStorage:Q})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8096,2076],()=>a(49976));module.exports=r})();