import React from 'react';
import { ApiDocumentationPage, ApiDocumentationViewType } from '../lib/api/api-documentation-page';

/**
 * API Documentation Page
 * 
 * This page displays the API documentation using both Swagger UI and ReDoc.
 */
export default function DocsPage() {
  return (
    <ApiDocumentationPage
      title="Enterprise IT Helpdesk API Documentation"
      description="Comprehensive documentation for the Enterprise IT Helpdesk API"
      apiUrl="/api/docs"
      defaultViewType={ApiDocumentationViewType.SWAGGER}
      showViewSwitcher={true}
      styles={{
        container: {
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '2rem 1rem',
        },
        header: {
          marginBottom: '2rem',
          borderBottom: '1px solid #eee',
          paddingBottom: '1rem',
        },
        title: {
          fontSize: '2.5rem',
          color: '#333',
        },
        description: {
          fontSize: '1.2rem',
          color: '#666',
        },
      }}
    />
  );
}