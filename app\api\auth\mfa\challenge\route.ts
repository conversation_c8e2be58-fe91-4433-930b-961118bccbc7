import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { MFAService } from '@/lib/services/mfa-service';

export async function POST() {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const mfaService = new MFAService(supabase);
    const result = await mfaService.createChallenge(session.user.id);

    return NextResponse.json(result);
  } catch (error) {
    console.error('MFA challenge error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create MFA challenge' },
      { status: 500 }
    );
  }
}
