// Enhanced AI Form Assistant with Historical Data Integration
import { FormField, FormData } from '@/lib/form-types'
import { SelectedUser } from '@/lib/request-types'
import { historicalDataService } from '@/lib/historical-data-service'
import OpenAI from 'openai'

interface AIContext {
  userId?: string
  departmentId?: string
  userRole?: string
  previousFormData?: FormData
  selectedUsers?: SelectedUser[]
}

interface AISuggestion {
  fieldId: string
  suggestedValue: any
  confidence: number
  reasoning: string
  source: 'ai' | 'historical' | 'hybrid'
}

export interface AIValidationResult {
  isValid: boolean
  message?: string
  type: 'success' | 'warning' | 'error' | 'info'
  aiInsight?: string
  suggestion?: any
  historicalSuggestions?: string[]
}

export class IntegratedAIFormAssistant {
  private static instance: IntegratedAIFormAssistant
  private openai: OpenAI | null = null
  private apiKey: string | null = null
  private validationCache: Map<string, AIValidationResult> = new Map()
  private suggestionCache: Map<string, AISuggestion> = new Map()

  private constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || null
    if (this.apiKey) {
      this.openai = new OpenAI({
        apiKey: this.apiKey,
        dangerouslyAllowBrowser: true
      })
    }
  }

  public static getInstance(): IntegratedAIFormAssistant {
    if (!IntegratedAIFormAssistant.instance) {
      IntegratedAIFormAssistant.instance = new IntegratedAIFormAssistant()
    }
    return IntegratedAIFormAssistant.instance
  }

  /**
   * Generate AI suggestions with historical data integration
   */
  async generateFieldSuggestionsWithHistory(
    field: FormField,
    currentValue: any,
    context: AIContext
  ): Promise<AISuggestion | null> {
    try {
      // Check cache first
      const cacheKey = `${field.id}-${currentValue}-${context.departmentId}`
      if (this.suggestionCache.has(cacheKey)) {
        return this.suggestionCache.get(cacheKey)!
      }

      // Get historical patterns
      let historicalAnalysis = null
      if (context.departmentId) {
        historicalAnalysis = await historicalDataService.analyzeFormPatterns(
          context.departmentId,
          field.id,
          currentValue,
          context.previousFormData
        )
      }

      // If we have high-confidence historical data, use it
      if (historicalAnalysis?.commonPatterns && historicalAnalysis.commonPatterns.length > 0) {
        const topPattern = historicalAnalysis.commonPatterns[0]
        if (topPattern.confidence > 0.8) {
          const suggestion: AISuggestion = {
            fieldId: field.id,
            suggestedValue: topPattern.value,
            confidence: topPattern.confidence,
            reasoning: `Based on ${topPattern.frequency} similar submissions in your department`,
            source: 'historical'
          }
          this.suggestionCache.set(cacheKey, suggestion)
          return suggestion
        }
      }

      // Use AI with historical context
      if (this.openai && currentValue && currentValue.toString().trim().length > 2) {
        const aiSuggestion = await this.generateAISuggestionWithContext(
          field,
          currentValue,
          context,
          historicalAnalysis
        )
        
        if (aiSuggestion) {
          this.suggestionCache.set(cacheKey, aiSuggestion)
          return aiSuggestion
        }
      }

      // Fall back to rule-based suggestions
      return this.generateRuleBasedSuggestion(field, currentValue, context)
    } catch (error) {
      console.error('Suggestion generation error:', error)
      return this.generateRuleBasedSuggestion(field, currentValue, context)
    }
  }

  /**
   * Validate field with AI and historical insights
   */
  async validateFieldWithHistoricalInsights(
    field: FormField,
    value: any,
    context?: AIContext
  ): Promise<AIValidationResult> {
    try {
      // Basic validation first
      const basicResult = this.performBasicValidation(field, value)
      if (!basicResult.isValid) {
        return basicResult
      }

      // Get historical suggestions
      let historicalSuggestions: string[] = []
      if (context?.departmentId && value) {
        historicalSuggestions = await historicalDataService.getFieldSuggestions(
          context.departmentId,
          field.id,
          3
        )
      }

      // AI validation with historical context
      if (this.openai && value && value.toString().trim() !== '') {
        const aiResult = await this.performAIValidationWithHistory(
          field,
          value,
          context,
          historicalSuggestions
        )
        return {
          ...aiResult,
          historicalSuggestions: historicalSuggestions.length > 0 ? historicalSuggestions : undefined
        }
      }

      return {
        ...basicResult,
        historicalSuggestions: historicalSuggestions.length > 0 ? historicalSuggestions : undefined
      }
    } catch (error) {
      console.error('Validation error:', error)
      return this.performBasicValidation(field, value)
    }
  }

  /**
   * Generate AI suggestion with historical context
   */
  private async generateAISuggestionWithContext(
    field: FormField,
    currentValue: any,
    context: AIContext,
    historicalAnalysis: any
  ): Promise<AISuggestion | null> {
    if (!this.openai) return null

    try {
      const prompt = this.buildContextualPrompt(field, currentValue, context, historicalAnalysis)
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant helping with IT helpdesk form completion in a Japanese enterprise.
            Consider historical patterns when suggesting improvements. Respond in JSON format:
            {
              "suggestedValue": "improved value",
              "confidence": 0.0-1.0,
              "reasoning": "explanation"
            }`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.4,
        max_tokens: 150,
        response_format: { type: "json_object" }
      })

      const response = completion.choices[0].message.content
      if (!response) return null

      const result = JSON.parse(response)
      return {
        fieldId: field.id,
        suggestedValue: result.suggestedValue,
        confidence: result.confidence,
        reasoning: result.reasoning,
        source: 'hybrid'
      }
    } catch (error) {
      console.error('AI suggestion error:', error)
      return null
    }
  }

  /**
   * Build contextual prompt with historical data
   */
  private buildContextualPrompt(
    field: FormField,
    currentValue: any,
    context: AIContext,
    historicalAnalysis: any
  ): string {
    let prompt = `Field: ${field.label} (${field.labelJp})\n`
    prompt += `Current Value: ${currentValue}\n`
    
    if (historicalAnalysis?.commonPatterns && historicalAnalysis.commonPatterns.length > 0) {
      prompt += `\nHistorical patterns from this department:\n`
      historicalAnalysis.commonPatterns.slice(0, 3).forEach((p: any) => {
        prompt += `- "${p.value}" (used ${p.frequency} times, ${Math.round(p.confidence * 100)}% confidence)\n`
      })
    }
    
    if (historicalAnalysis?.aiSuggestion) {
      prompt += `\nPrevious AI suggestion: ${historicalAnalysis.aiSuggestion}\n`
    }
    
    if (context.previousFormData) {
      prompt += `\nOther form fields:\n`
      Object.entries(context.previousFormData).slice(0, 3).forEach(([key, value]) => {
        if (key !== field.id && value) {
          prompt += `- ${key}: ${value}\n`
        }
      })
    }
    
    prompt += `\nConsider the historical patterns and suggest an improvement that follows department conventions.`
    
    return prompt
  }

  /**
   * Submit feedback on suggestions
   */
  async submitSuggestionFeedback(
    userId: string,
    fieldId: string,
    originalValue: string,
    suggestedValue: string,
    wasAccepted: boolean,
    finalValue: string,
    departmentId?: string,
    confidence?: number
  ): Promise<boolean> {
    return historicalDataService.submitFeedback({
      userId,
      fieldId,
      originalValue,
      suggestedValue,
      wasAccepted,
      finalValue,
      departmentId,
      confidence
    })
  }

  /**
   * Record form submission for learning
   */
  async recordFormSubmission(
    userId: string,
    departmentId: string,
    serviceCategoryId: string,
    formData: FormData
  ): Promise<string | null> {
    return historicalDataService.recordFormSubmission(
      userId,
      departmentId,
      serviceCategoryId,
      formData
    )
  }

  // ... (keep existing methods like performBasicValidation, etc.)
}

// Export singleton instance
export const integratedAIFormAssistant = IntegratedAIFormAssistant.getInstance()
