# ITSync Quick Setup Guide

## Environment Configuration Error Fix

You're seeing the "Invalid URL" error because the Supabase environment variables are not configured. Follow these steps:

## Step 1: Copy Environment Template

Copy `.env.local.example` to `.env.local`:

```bash
cp .env.local.example .env.local
```

## Step 2: Get Supabase Credentials

1. Go to [https://app.supabase.com](https://app.supabase.com)
2. Create a new project or use existing one
3. Go to Settings → API
4. Copy these values:
   - Project URL → `NEXT_PUBLIC_SUPABASE_URL`
   - anon public key → `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - service_role key → `SUPABASE_SERVICE_ROLE_KEY`

## Step 3: Update .env.local

Replace the placeholder values in `.env.local` with your actual credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=https://pvfymxuzhzlibbnaedgt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Step 4: Restart Development Server

```bash
# Stop the server (Ctrl+C)
# Start it again
npm run dev
```

## Step 5: Run Database Migrations

Go to your Supabase SQL Editor and run the migration scripts from:
`/supabase/migrations/`

## Troubleshooting

If you still see errors:
1. Make sure `.env.local` file exists (not `.env.local.example`)
2. Check that values don't contain "your_" placeholders
3. Verify the Supabase project is active
4. Clear Next.js cache: `rm -rf .next`

## Need Help?

- Visit `/setup-required` page for detailed instructions
- Check Supabase dashboard for correct credentials
- Ensure all environment variables are properly set
