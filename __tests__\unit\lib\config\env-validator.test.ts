/**
 * Unit tests for Environment Validator
 * Tests environment validation, security checks, and configuration
 */

import { 
  validateEnvironment, 
  logValidationResults, 
  generateSecureKey,
  validateOnStartup 
} from '@/lib/config/env-validator'

describe('Environment Validator', () => {
  let originalEnv: NodeJS.ProcessEnv
  let consoleSpy: jest.SpyInstance

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env }
    
    // Mock console methods
    consoleSpy = jest.spyOn(console, 'log').mockImplementation()
    jest.spyOn(console, 'error').mockImplementation()
    jest.spyOn(console, 'warn').mockImplementation()
  })

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv
    
    // Restore console methods
    consoleSpy.mockRestore()
    jest.restoreAllMocks()
  })

  describe('validateEnvironment', () => {
    it('should pass validation with all required variables set correctly', () => {
      // Set valid environment variables
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.validanonkey'
      process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.validservicekey'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'
      process.env.NODE_ENV = 'development'

      const result = validateEnvironment()

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.securityIssues).toHaveLength(0)
    })

    it('should fail validation when required variables are missing', () => {
      // Clear required environment variables
      delete process.env.NEXT_PUBLIC_SUPABASE_URL
      delete process.env.ENCRYPTION_KEY
      delete process.env.JWT_SECRET

      const result = validateEnvironment()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain(
        expect.stringContaining('Missing required environment variable: NEXT_PUBLIC_SUPABASE_URL')
      )
      expect(result.errors).toContain(
        expect.stringContaining('Missing required environment variable: ENCRYPTION_KEY')
      )
      expect(result.errors).toContain(
        expect.stringContaining('Missing required environment variable: JWT_SECRET')
      )
    })

    it('should detect invalid URL format', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'invalid-url'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain(
        expect.stringContaining('Invalid format for NEXT_PUBLIC_SUPABASE_URL')
      )
    })

    it('should detect invalid JWT format', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'invalid-jwt-format'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain(
        expect.stringContaining('Invalid format for NEXT_PUBLIC_SUPABASE_ANON_KEY')
      )
    })

    it('should detect short encryption keys', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'short'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.securityIssues).toContain(
        expect.stringContaining('Encryption key must be at least 32 characters long')
      )
    })

    it('should detect placeholder values', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'your_32_character_encryption_key_here'
      process.env.JWT_SECRET = 'your_jwt_secret_here'

      const result = validateEnvironment()

      expect(result.securityIssues).toContain(
        expect.stringContaining('Encryption key is still using placeholder value')
      )
      expect(result.securityIssues).toContain(
        expect.stringContaining('JWT secret is still using placeholder value')
      )
    })

    it('should detect service role key that looks like anon key', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.anon.signature'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.securityIssues).toContain(
        expect.stringContaining('Service role key appears to be an anon key')
      )
    })
  })

  describe('Production Security Checks', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production'
    })

    it('should fail validation in production with debug mode enabled', () => {
      process.env.DEBUG = 'true'
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.securityIssues).toContain('DEBUG mode is enabled in production')
    })

    it('should fail validation in production with TLS disabled', () => {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.isValid).toBe(false)
      expect(result.securityIssues).toContain('TLS certificate validation is disabled in production')
    })

    it('should require HTTPS in production', () => {
      process.env.NEXT_PUBLIC_APP_URL = 'http://example.com'
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.isValid).toBe(false)
      expect(result.securityIssues).toContain('App URL must use HTTPS in production')
    })

    it('should make security issues fatal in production', () => {
      process.env.ENCRYPTION_KEY = 'short'
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.isValid).toBe(false)
    })
  })

  describe('AI API Key Validation', () => {
    it('should validate OpenAI API key format', () => {
      process.env.OPENAI_API_KEY = 'sk-proj-' + 'a'.repeat(48)
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.securityIssues).not.toContain(
        expect.stringContaining('OpenAI API key appears to be a placeholder')
      )
    })

    it('should detect placeholder OpenAI API key', () => {
      process.env.OPENAI_API_KEY = 'your_openai_api_key_here'
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.securityIssues).toContain(
        expect.stringContaining('OpenAI API key appears to be a placeholder')
      )
    })

    it('should validate Anthropic API key format', () => {
      process.env.ANTHROPIC_API_KEY = 'sk-ant-api03-' + 'a'.repeat(48)
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.securityIssues).not.toContain(
        expect.stringContaining('Anthropic API key appears to be a placeholder')
      )
    })
  })

  describe('Placeholder Detection', () => {
    it('should detect common placeholder patterns', () => {
      process.env.TEST_KEY = 'your_test_key'
      process.env.DEMO_SECRET = 'demo_secret_value'
      process.env.EXAMPLE_PASSWORD = 'example_password'
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
      process.env.ENCRYPTION_KEY = 'abcdef1234567890abcdef1234567890'
      process.env.JWT_SECRET = 'abcdef1234567890abcdef1234567890'

      const result = validateEnvironment()

      expect(result.warnings).toContain('TEST_KEY appears to contain placeholder value')
      expect(result.warnings).toContain('DEMO_SECRET appears to contain placeholder value')
      expect(result.warnings).toContain('EXAMPLE_PASSWORD appears to contain placeholder value')
    })
  })

  describe('logValidationResults', () => {
    it('should log errors correctly', () => {
      const result = {
        isValid: false,
        errors: ['Error 1', 'Error 2'],
        warnings: [],
        securityIssues: []
      }

      logValidationResults(result)

      expect(console.error).toHaveBeenCalledWith('❌ Environment Validation Errors:')
      expect(console.error).toHaveBeenCalledWith('  - Error 1')
      expect(console.error).toHaveBeenCalledWith('  - Error 2')
    })

    it('should log security issues correctly', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: [],
        securityIssues: ['Security Issue 1']
      }

      logValidationResults(result)

      expect(console.warn).toHaveBeenCalledWith('🔒 Security Issues:')
      expect(console.warn).toHaveBeenCalledWith('  - Security Issue 1')
    })

    it('should log success when validation passes', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: [],
        securityIssues: []
      }

      logValidationResults(result)

      expect(console.log).toHaveBeenCalledWith('✅ Environment validation passed')
    })
  })

  describe('generateSecureKey', () => {
    it('should generate key of correct length', () => {
      const key = generateSecureKey(32)
      expect(key).toHaveLength(32)
    })

    it('should generate different keys on each call', () => {
      const key1 = generateSecureKey(32)
      const key2 = generateSecureKey(32)
      expect(key1).not.toBe(key2)
    })

    it('should only contain valid characters', () => {
      const key = generateSecureKey(100)
      expect(key).toMatch(/^[A-Za-z0-9]+$/)
    })

    it('should use default length of 32', () => {
      const key = generateSecureKey()
      expect(key).toHaveLength(32)
    })
  })

  describe('validateOnStartup', () => {
    it('should exit process in production when validation fails', () => {
      const mockExit = jest.spyOn(process, 'exit').mockImplementation()
      process.env.NODE_ENV = 'production'
      delete process.env.ENCRYPTION_KEY // Force validation failure

      validateOnStartup()

      expect(mockExit).toHaveBeenCalledWith(1)
      mockExit.mockRestore()
    })

    it('should not exit process in development when validation fails', () => {
      const mockExit = jest.spyOn(process, 'exit').mockImplementation()
      process.env.NODE_ENV = 'development'
      delete process.env.ENCRYPTION_KEY // Force validation failure

      validateOnStartup()

      expect(mockExit).not.toHaveBeenCalled()
      mockExit.mockRestore()
    })
  })
})
