import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY')
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

const supabase = createClient(SUPABASE_URL!, SUPABASE_SERVICE_ROLE_KEY!)

interface GenerateEmbeddingRequest {
  articleId: string
  language?: 'en' | 'jp' | 'both'
  forceRegenerate?: boolean
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { articleId, language = 'both', forceRegenerate = false } = await req.json() as GenerateEmbeddingRequest

    if (!articleId) {
      throw new Error('Article ID is required')
    }

    // Fetch article content
    const { data: article, error: articleError } = await supabase
      .from('kb_articles')
      .select('id, title_en, title_jp, content_en, content_jp, summary_en, summary_jp')
      .eq('id', articleId)
      .single()

    if (articleError || !article) {
      throw new Error('Article not found')
    }

    const embeddings = []

    // Generate embeddings for requested languages
    if (language === 'en' || language === 'both') {
      const contentEn = `${article.title_en}\n\n${article.summary_en}\n\n${article.content_en}`
      const embeddingEn = await generateEmbedding(contentEn)
      embeddings.push({
        article_id: articleId,
        language: 'en',
        embedding: `[${embeddingEn.embedding.join(',')}]`,
        content_hash: await hashContent(contentEn),
        model_version: embeddingEn.model
      })
    }

    if (language === 'jp' || language === 'both') {
      const contentJp = `${article.title_jp}\n\n${article.summary_jp}\n\n${article.content_jp}`
      const embeddingJp = await generateEmbedding(contentJp)
      embeddings.push({
        article_id: articleId,
        language: 'jp',
        embedding: `[${embeddingJp.embedding.join(',')}]`,
        content_hash: await hashContent(contentJp),
        model_version: embeddingJp.model
      })
    }

    // Store embeddings
    const { error: insertError } = await supabase
      .from('kb_embeddings')
      .upsert(embeddings, {
        onConflict: 'article_id,language'
      })

    if (insertError) {
      throw new Error(`Failed to store embeddings: ${insertError.message}`)
    }

    // Find and store related articles
    await findRelatedArticles(articleId, language)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Embeddings generated successfully',
        embeddings: embeddings.length
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      }
    )
  }
})

async function generateEmbedding(text: string) {
  const response = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${OPENAI_API_KEY}`
    },
    body: JSON.stringify({
      model: 'text-embedding-ada-002',
      input: preprocessText(text)
    })
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(`OpenAI API error: ${error.error?.message || 'Unknown error'}`)
  }

  const data = await response.json()
  
  return {
    embedding: data.data[0].embedding,
    model: data.model,
    usage: data.usage
  }
}

function preprocessText(text: string): string {
  // Remove excessive whitespace
  let processed = text.replace(/\s+/g, ' ').trim()
  
  // Remove special characters
  processed = processed.replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
  
  // Limit length (8191 tokens for ada-002, ~30k chars)
  const maxChars = 30000
  if (processed.length > maxChars) {
    processed = processed.substring(0, maxChars) + '...'
  }
  
  return processed
}

async function hashContent(content: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(content)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  return hashHex
}

async function findRelatedArticles(articleId: string, language: 'en' | 'jp' | 'both') {
  const languages = language === 'both' ? ['en', 'jp'] : [language]
  
  for (const lang of languages) {
    // Get the article's embedding
    const { data: embedding, error } = await supabase
      .from('kb_embeddings')
      .select('embedding')
      .eq('article_id', articleId)
      .eq('language', lang)
      .single()

    if (error || !embedding) continue

    // Find similar articles
    const { data: similar } = await supabase
      .rpc('kb_semantic_search', {
        query_embedding: embedding.embedding,
        search_language: lang,
        match_threshold: 0.5,
        match_count: 6
      })

    if (!similar || similar.length <= 1) continue

    // Store relationships (excluding self)
    const relationships = similar
      .filter((item: any) => item.article_id !== articleId)
      .slice(0, 5)
      .map((item: any) => ({
        article_id: articleId,
        related_article_id: item.article_id,
        relevance_score: item.similarity,
        relation_type: 'similar'
      }))

    if (relationships.length > 0) {
      await supabase
        .from('kb_related_articles')
        .upsert(relationships, {
          onConflict: 'article_id,related_article_id'
        })
    }
  }
}
