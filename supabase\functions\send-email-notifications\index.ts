import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailNotification {
  id: string;
  to_email: string;
  cc_emails?: string[];
  bcc_emails?: string[];
  subject: string;
  body: string;
  html_body?: string;
  attachments?: any[];
  priority: string;
  metadata?: Record<string, any>;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Parse request body
    const { emails } = await req.json() as { emails: EmailNotification[] }

    if (!emails || emails.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No emails to send' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get email service configuration
    const emailProvider = Deno.env.get('EMAIL_PROVIDER') || 'resend'
    const results = []

    for (const email of emails) {
      try {
        let result;
        
        switch (emailProvider) {
          case 'resend':
            result = await sendViaResend(email);
            break;
          case 'sendgrid':
            result = await sendViaSendGrid(email);
            break;
          case 'ses':
            result = await sendViaAWSSES(email);
            break;
          default:
            // For development/testing, just log the email
            console.log('Email would be sent:', email);
            result = { success: true, messageId: `dev-${Date.now()}` };
        }

        results.push({
          id: email.id,
          success: true,
          messageId: result.messageId
        });

        // Log successful send
        await supabaseClient
          .from('notification_logs')
          .insert({
            notification_id: email.metadata?.notification_id,
            channel: 'email',
            status: 'sent',
            recipient: email.to_email,
            metadata: { messageId: result.messageId }
          });

      } catch (error) {
        console.error(`Failed to send email ${email.id}:`, error);
        
        results.push({
          id: email.id,
          success: false,
          error: error.message
        });

        // Log failed send
        await supabaseClient
          .from('notification_logs')
          .insert({
            notification_id: email.metadata?.notification_id,
            channel: 'email',
            status: 'failed',
            recipient: email.to_email,
            error_message: error.message
          });

        // Update email queue status
        await supabaseClient
          .from('notification_email_queue')
          .update({
            status: 'failed',
            last_error: error.message,
            attempts: email.attempts + 1
          })
          .eq('id', email.id);
      }
    }

    return new Response(
      JSON.stringify({ results }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// Resend integration
async function sendViaResend(email: EmailNotification) {
  const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY');
  
  if (!RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY not configured');
  }

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${RESEND_API_KEY}`,
    },
    body: JSON.stringify({
      from: Deno.env.get('EMAIL_FROM') || 'ITSync <<EMAIL>>',
      to: email.to_email,
      cc: email.cc_emails,
      bcc: email.bcc_emails,
      subject: email.subject,
      text: email.body,
      html: email.html_body || formatHtmlEmail(email.subject, email.body),
      attachments: email.attachments,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Resend API error: ${error}`);
  }

  const data = await response.json();
  return { messageId: data.id };
}

// SendGrid integration
async function sendViaSendGrid(email: EmailNotification) {
  const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY');
  
  if (!SENDGRID_API_KEY) {
    throw new Error('SENDGRID_API_KEY not configured');
  }

  const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SENDGRID_API_KEY}`,
    },
    body: JSON.stringify({
      personalizations: [{
        to: [{ email: email.to_email }],
        cc: email.cc_emails?.map(e => ({ email: e })),
        bcc: email.bcc_emails?.map(e => ({ email: e })),
      }],
      from: {
        email: Deno.env.get('EMAIL_FROM_ADDRESS') || '<EMAIL>',
        name: Deno.env.get('EMAIL_FROM_NAME') || 'ITSync'
      },
      subject: email.subject,
      content: [
        { type: 'text/plain', value: email.body },
        { type: 'text/html', value: email.html_body || formatHtmlEmail(email.subject, email.body) }
      ],
      attachments: email.attachments,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`SendGrid API error: ${error}`);
  }

  return { messageId: response.headers.get('X-Message-Id') || 'sendgrid-' + Date.now() };
}

// AWS SES integration (placeholder)
async function sendViaAWSSES(email: EmailNotification) {
  // TODO: Implement AWS SES integration
  throw new Error('AWS SES integration not yet implemented');
}

// Format plain text email as HTML
function formatHtmlEmail(subject: string, body: string): string {
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${subject}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #0ea5e9;
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 8px 8px 0 0;
    }
    .content {
      background-color: #f8fafc;
      padding: 30px;
      border-radius: 0 0 8px 8px;
    }
    .footer {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>ITSync</h1>
  </div>
  <div class="content">
    <h2>${subject}</h2>
    <div>${body.replace(/\n/g, '<br>')}</div>
  </div>
  <div class="footer">
    <p>This is an automated message from ITSync. Please do not reply to this email.</p>
    <p>&copy; 2025 ITSync. All rights reserved.</p>
  </div>
</body>
</html>
  `.trim();
}
