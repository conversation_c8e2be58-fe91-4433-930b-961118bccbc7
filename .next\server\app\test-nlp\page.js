(()=>{var e={};e.id=9988,e.ids=[9988],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},18752:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(70260),a=t(28203),n=t(25155),i=t.n(n),l=t(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["test-nlp",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95700)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-nlp\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-nlp\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-nlp/page",pathname:"/test-nlp",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74416:(e,s,t)=>{Promise.resolve().then(t.bind(t,95700))},32560:(e,s,t)=>{Promise.resolve().then(t.bind(t,37128))},37128:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(45512),a=t(58009),n=t(97643),i=t(87021),l=t(48859),o=t(77252),d=t(91274);class c{async parseNaturalLanguageInput(e,s){let t=`${e}-${s.language}`;if(this.parseCache.has(t))return this.parseCache.get(t);try{let{data:r,error:a}=await this.supabase.functions.invoke("parse-natural-language",{body:{input:e,context:s}});if(a)throw a;return this.parseCache.set(t,r),setTimeout(()=>{this.parseCache.delete(t)},6e5),r}catch(e){return console.error("NLP parsing failed:",e),{intent:"unknown",entities:{},confidence:0,suggestedForm:"",extractedData:{}}}}parseGroupMailRequest(e){let s={};for(let t of[/(.+)を(.+)グループメールに追加/,/(.+)さんを(.+)に登録/,/(.+)のグループメール登録/]){let r=e.match(t);if(r){s.action="add",s.userName=r[1],r[2]&&(s.groupMail=r[2]);break}}if(!s.action)for(let t of[/(.+)を(.+)グループメールから削除/,/(.+)さんを(.+)から外す/,/(.+)のグループメール解除/]){let r=e.match(t);if(r){s.action="remove",s.userName=r[1],r[2]&&(s.groupMail=r[2]);break}}return s}parsePasswordResetRequest(e){let s={};for(let t of[/(.+)のパスワードリセット/,/(.+)さんのパスワード再設定/,/(.+)の(M365|MFA|Windows)パスワード/,/reset password for (.+)/i,/(.+) password reset/i]){let r=e.match(t);if(r){s.userName=r[1],r[2]&&(s.passwordType=r[2]);break}}return e.includes("M365")||e.includes("Office")?s.passwordType="M365 Office":e.includes("MFA")||e.includes("認証")?s.passwordType="Multi Factor Authenticator":e.includes("Windows")&&(s.passwordType="Windows"),s}parseSharePointRequest(e){let s={};for(let t of[/(.+)に(.+)のアクセス権を付与/,/(.+)さんが(.+)にアクセス/,/(.+)への(.+)権限/]){let r=e.match(t);if(r){s.action="grant",s.userName=r[1],s.library=r[2];break}}if(!s.action)for(let t of[/(.+)から(.+)のアクセス権を削除/,/(.+)さんの(.+)アクセスを解除/]){let r=e.match(t);if(r){s.action="revoke",s.userName=r[1],s.library=r[2];break}}return e.includes("読み取り")||e.includes("read")?s.accessLevel="read":e.includes("編集")||e.includes("contribute")?s.accessLevel="contribute":(e.includes("フル")||e.includes("full"))&&(s.accessLevel="full"),s}suggestFormType(e){let s=e.toLowerCase();for(let e of[{keywords:["グループメール","group mail","メーリングリスト"],formType:"group_mail"},{keywords:["メールボックス","mailbox","共有メール"],formType:"mailbox"},{keywords:["sharepoint","シェアポイント","文書","ドキュメント"],formType:"sharepoint"},{keywords:["pc","管理者","admin","権限"],formType:"pc_admin"},{keywords:["パスワード","password","リセット","reset"],formType:"password_reset"},{keywords:["mxp","登録"],formType:"mxp_registration"},{keywords:["ソフト","software","インストール"],formType:"software_install"},{keywords:["web","ウェブ","閲覧","browsing"],formType:"web_browsing"}]){let t=e.keywords.filter(e=>s.includes(e));if(t.length>0)return{formType:e.formType,confidence:Math.min(.3*t.length,.9)}}return{formType:"unknown",confidence:0}}extractUserNames(e){let s=[],t=e.match(/([一-龯ぁ-ゔァ-ヴー]{2,}\s*[一-龯ぁ-ゔァ-ヴー]{2,})/g);t&&s.push(...t);let r=e.match(/([一-龯ぁ-ゔァ-ヴーa-zA-Z]+)さん/g);return r&&s.push(...r.map(e=>e.replace("さん",""))),[...new Set(s)]}extractDates(e){let s=[];for(let t of[/\d{4}年\d{1,2}月\d{1,2}日/g,/\d{4}\/\d{1,2}\/\d{1,2}/g,/\d{4}-\d{1,2}-\d{1,2}/g,/\d{1,2}月\d{1,2}日/g]){let r=e.match(t);r&&s.push(...r)}return s}mapToFormFields(e,s){let t={};for(let[r,a]of Object.entries(e.extractedData)){let e=s.fields?.find(e=>e.name===r||e.name.toLowerCase()===r.toLowerCase()||e.label?.toLowerCase().includes(r.toLowerCase()));e&&(t[e.name]=a)}return t}constructor(){this.supabase=(0,d.createClientComponentClient)(),this.parseCache=new Map}}let u=new c;var p=t(15907),m=t(86235),x=t(1372),h=t(12214),g=t(59462);function f({onFormSuggested:e,availableForms:s,language:t="ja",className:d}){let[c,f]=(0,a.useState)(""),[j,w]=(0,a.useState)(!1),[b,y]=(0,a.useState)(null),[v,N]=(0,a.useState)(null),C="ja"===t?["山田太郎さんをITシステムグループのグループメールに追加してください","田中花子のM365パスワードをリセットしたい","営業部の佐藤さんに経理フォルダのアクセス権を付与","PC M241234の管理者権限が必要です"]:["Add John Smith to the IT System Group email list","Reset password for Mary Johnson M365 account","Grant access to accounting folder for sales department staff","Need admin privileges for PC M241234"],k=async()=>{if(c.trim()){w(!0),N(null),y(null);try{let r=await u.parseNaturalLanguageInput(c,{language:t,availableForms:s});y(r),r.suggestedForm&&r.confidence>.5&&e(r.suggestedForm,r.extractedData)}catch(e){N("Failed to parse input. Please try again."),console.error("NLP parsing error:",e)}finally{w(!1)}}},P=e=>{f(e)};return(0,r.jsxs)(n.Zp,{className:(0,g.cn)("w-full",d),children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{className:"h-5 w-5"}),"ja"===t?"自然言語でリクエスト":"Natural Language Request"]}),(0,r.jsx)(n.BT,{children:"ja"===t?"お手伝いが必要なことを自然な言葉で入力してください":"Type what you need help with in natural language"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.T,{value:c,onChange:e=>f(e.target.value),placeholder:"ja"===t?"例: 山田さんをグループメールに追加したい...":"e.g., I need to add Yamada-san to the group email...",className:"min-h-[100px]"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(i.$,{onClick:k,disabled:!c.trim()||j,className:"gap-2",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin"}),"ja"===t?"解析中...":"Parsing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),"ja"===t?"解析する":"Parse Request"]})}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[c.length," / 500"]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"ja"===t?"例:":"Examples:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:C.map((e,s)=>(0,r.jsx)(i.$,{variant:"outline",size:"sm",className:"text-xs h-auto py-1 px-2",onClick:()=>P(e),children:e},s))})]}),b&&(0,r.jsxs)("div",{className:"space-y-3 p-4 bg-muted/50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium",children:"ja"===t?"解析結果":"Parsed Result"}),(0,r.jsxs)(o.E,{variant:b.confidence>.7?"default":"secondary",children:[Math.round(100*b.confidence),"% confidence"]})]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"ja"===t?"意図: ":"Intent: "}),(0,r.jsx)("span",{className:"text-muted-foreground",children:b.intent})]}),b.suggestedForm&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"ja"===t?"推奨フォーム: ":"Suggested Form: "}),(0,r.jsx)(o.E,{variant:"outline",children:b.suggestedForm})]}),Object.keys(b.extractedData).length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"ja"===t?"抽出データ:":"Extracted Data:"}),(0,r.jsx)("pre",{className:"mt-1 p-2 bg-background rounded text-xs overflow-auto",children:JSON.stringify(b.extractedData,null,2)})]})]}),b.suggestedForm&&b.confidence>.5&&(0,r.jsxs)(i.$,{className:"w-full gap-2",onClick:()=>e(b.suggestedForm,b.extractedData),children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"ja"===t?`${b.suggestedForm}フォームを開く`:`Open ${b.suggestedForm} form`]})]}),v&&(0,r.jsx)("div",{className:"p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md text-sm",children:v})]})]})}var j=t(69193),w=t(26401);function b(){let[e,s]=(0,a.useState)(null),[t,l]=(0,a.useState)({}),[d,c]=(0,a.useState)("ja"),u=["group_mail","mailbox","sharepoint","pc_admin","password_reset","mxp_registration","software_install","web_browsing"],p={group_mail:{ja:"グループメール",en:"Group Mail"},mailbox:{ja:"メールボックス",en:"Mailbox"},sharepoint:{ja:"SharePoint",en:"SharePoint"},pc_admin:{ja:"PC管理者権限",en:"PC Admin"},password_reset:{ja:"パスワードリセット",en:"Password Reset"},mxp_registration:{ja:"MXP登録",en:"MXP Registration"},software_install:{ja:"ソフトウェアインストール",en:"Software Install"},web_browsing:{ja:"Web閲覧",en:"Web Browsing"}};return(0,r.jsx)("div",{className:"container mx-auto py-10 max-w-6xl",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Natural Language Processing Test"}),(0,r.jsx)(n.BT,{children:"Test the NLP capabilities for parsing IT helpdesk requests"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)(j.tU,{value:d,onValueChange:e=>c(e),children:[(0,r.jsxs)(j.j7,{className:"grid w-full grid-cols-2 max-w-[200px]",children:[(0,r.jsx)(j.Xi,{value:"ja",children:"日本語"}),(0,r.jsx)(j.Xi,{value:"en",children:"English"})]}),(0,r.jsx)(j.av,{value:d,className:"space-y-6",children:e?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"ja"===d?"選択されたフォーム":"Selected Form"}),(0,r.jsx)(o.E,{className:"mt-1",children:p[e]?.[d]||e})]}),(0,r.jsxs)(i.$,{variant:"outline",onClick:()=>{s(null),l({})},className:"gap-2",children:[(0,r.jsx)(w.n,{className:"h-4 w-4"}),"ja"===d?"戻る":"Back"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"ja"===d?"抽出されたデータ":"Extracted Data"}),(0,r.jsx)("div",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-lg",children:(0,r.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(t,null,2)})})]}),(0,r.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm",children:"ja"===d?"実際のアプリケーションでは、このデータが対応するフォームフィールドに自動的に入力されます。":"In the actual application, this data would be automatically populated into the corresponding form fields."})})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"ja"===d?"使い方":"How to use"}),(0,r.jsx)("ul",{className:"text-sm space-y-1 text-muted-foreground",children:"ja"===d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("li",{children:"• 自然な日本語でITサポートリクエストを入力してください"}),(0,r.jsx)("li",{children:"• システムが意図を解析し、適切なフォームを提案します"}),(0,r.jsx)("li",{children:"• 人名、アクション、対象などが自動的に抽出されます"}),(0,r.jsx)("li",{children:"• 例文をクリックして試すこともできます"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("li",{children:"• Enter IT support requests in natural language"}),(0,r.jsx)("li",{children:"• The system will parse intent and suggest appropriate forms"}),(0,r.jsx)("li",{children:"• Names, actions, and targets will be extracted automatically"}),(0,r.jsx)("li",{children:"• Click example requests to try them out"})]})})]}),(0,r.jsx)(f,{onFormSuggested:(e,t)=>{s(e),l(t)},availableForms:u,language:d}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h3",{className:"font-semibold",children:"ja"===d?"利用可能なフォーム":"Available Forms"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(e=>(0,r.jsx)(o.E,{variant:"secondary",children:p[e]?.[d]||e},e))})]})]})})]})})]})})}},95700:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-nlp\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-nlp\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8096,2076],()=>t(18752));module.exports=r})();