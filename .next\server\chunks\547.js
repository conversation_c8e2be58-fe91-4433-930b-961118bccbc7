exports.id=547,exports.ids=[547],exports.modules={60670:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getKeyIndexes=t.hasFlag=t.exists=t.list=void 0;let i=r(s(48957));t.list=Object.keys(i.default);let n={};function o(e){"string"!=typeof e&&(e=String(e));let t=e.indexOf("->");return -1===t?e.length:t}t.list.forEach(e=>{n[e]=i.default[e].flags.reduce(function(e,t){return e[t]=!0,e},{})}),t.exists=function(e){return!!i.default[e]},t.hasFlag=function(e,t){if(!n[e])throw Error("Unknown command "+e);return!!n[e][t]},t.getKeyIndexes=function(e,t,s){let r=i.default[e];if(!r)throw Error("Unknown command "+e);if(!Array.isArray(t))throw Error("Expect args to be an array");let n=[],a=!!(s&&s.parseExternalKey),l=(e,t)=>{let s=[],r=Number(e[t]);for(let e=0;e<r;e++)s.push(e+t+1);return s},c=(e,t,s)=>{for(let r=t;r<e.length-1;r+=1)if(String(e[r]).toLowerCase()===s.toLowerCase())return r+1;return null};switch(e){case"zunionstore":case"zinterstore":case"zdiffstore":n.push(0,...l(t,1));break;case"eval":case"evalsha":case"eval_ro":case"evalsha_ro":case"fcall":case"fcall_ro":case"blmpop":case"bzmpop":n.push(...l(t,1));break;case"sintercard":case"lmpop":case"zunion":case"zinter":case"zmpop":case"zintercard":case"zdiff":n.push(...l(t,0));break;case"georadius":{n.push(0);let e=c(t,5,"STORE");e&&n.push(e);let s=c(t,5,"STOREDIST");s&&n.push(s);break}case"georadiusbymember":{n.push(0);let e=c(t,4,"STORE");e&&n.push(e);let s=c(t,4,"STOREDIST");s&&n.push(s);break}case"sort":case"sort_ro":n.push(0);for(let e=1;e<t.length-1;e++){let s=t[e];if("string"!=typeof s)continue;let r=s.toUpperCase();"GET"===r?(e+=1,"#"!==(s=t[e])&&(a?n.push([e,o(s)]):n.push(e))):"BY"===r?(e+=1,a?n.push([e,o(t[e])]):n.push(e)):"STORE"===r&&(e+=1,n.push(e))}break;case"migrate":if(""===t[2])for(let e=5;e<t.length-1;e++){let s=t[e];if("string"==typeof s&&"KEYS"===s.toUpperCase()){for(let s=e+1;s<t.length;s++)n.push(s);break}}else n.push(2);break;case"xreadgroup":case"xread":for(let s="xread"===e?0:3;s<t.length-1;s++)if("STREAMS"===String(t[s]).toUpperCase()){for(let e=s+1;e<=s+(t.length-1-s)/2;e++)n.push(e);break}break;default:if(r.step>0){let e=r.keyStart-1,s=r.keyStop>0?r.keyStop:t.length+r.keyStop+1;for(let t=e;t<s;t+=r.step)n.push(t)}}return n}},90438:e=>{var t=[0,4129,8258,12387,16516,20645,24774,28903,33032,37161,41290,45419,49548,53677,57806,61935,4657,528,12915,8786,21173,17044,29431,25302,37689,33560,45947,41818,54205,50076,62463,58334,9314,13379,1056,5121,25830,29895,17572,21637,42346,46411,34088,38153,58862,62927,50604,54669,13907,9842,5649,1584,30423,26358,22165,18100,46939,42874,38681,34616,63455,59390,55197,51132,18628,22757,26758,30887,2112,6241,10242,14371,51660,55789,59790,63919,35144,39273,43274,47403,23285,19156,31415,27286,6769,2640,14899,10770,56317,52188,64447,60318,39801,35672,47931,43802,27814,31879,19684,23749,11298,15363,3168,7233,60846,64911,52716,56781,44330,48395,36200,40265,32407,28342,24277,20212,15891,11826,7761,3696,65439,61374,57309,53244,48923,44858,40793,36728,37256,33193,45514,41451,53516,49453,61774,57711,4224,161,12482,8419,20484,16421,28742,24679,33721,37784,41979,46042,49981,54044,58239,62302,689,4752,8947,13010,16949,21012,25207,29270,46570,42443,38312,34185,62830,58703,54572,50445,13538,9411,5280,1153,29798,25671,21540,17413,42971,47098,34713,38840,59231,63358,50973,55100,9939,14066,1681,5808,26199,30326,17941,22068,55628,51565,63758,59695,39368,35305,47498,43435,22596,18533,30726,26663,6336,2273,14466,10403,52093,56156,60223,64286,35833,39896,43963,48026,19061,23124,27191,31254,2801,6864,10931,14994,64814,60687,56684,52557,48554,44427,40424,36297,31782,27655,23652,19525,15522,11395,7392,3265,61215,65342,53085,57212,44955,49082,36825,40952,28183,32310,20053,24180,11923,16050,3793,7920],s=function(e){for(var t,s=0,r=0,i=[],n=e.length;s<n;s++)(t=e.charCodeAt(s))<128?i[r++]=t:(t<2048?i[r++]=t>>6|192:((64512&t)==55296&&s+1<e.length&&(64512&e.charCodeAt(s+1))==56320?(t=65536+((1023&t)<<10)+(1023&e.charCodeAt(++s)),i[r++]=t>>18|240,i[r++]=t>>12&63|128):i[r++]=t>>12|224,i[r++]=t>>6&63|128),i[r++]=63&t|128);return i},r=e.exports=function(e){for(var r,i=0,n=-1,o=0,a=0,l="string"==typeof e?s(e):e,c=l.length;i<c;){if(r=l[i++],-1===n)123===r&&(n=i);else if(125!==r)a=t[(r^a>>8)&255]^a<<8;else if(i-1!==n)return 16383&a;o=t[(r^o>>8)&255]^o<<8}return 16383&o};e.exports.generateMulti=function(e){for(var t=1,s=e.length,i=r(e[0]);t<s;)if(r(e[t++])!==i)return -1;return i}},15401:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(8824)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8824:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e,t){let s=0,r=0,i=-1,n=0;for(;s<e.length;)if(r<t.length&&(t[r]===e[s]||"*"===t[r]))"*"===t[r]?(i=r,n=s):s++,r++;else{if(-1===i)return!1;r=i+1,s=++n}for(;r<t.length&&"*"===t[r];)r++;return r===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let s of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===s[0]?t.skips.push(s.slice(1)):t.names.push(s)},t.enabled=function(e){for(let s of t.skips)if(i(e,s))return!1;for(let s of t.names)if(i(e,s))return!0;return!1},t.humanize=s(83337),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},23481:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(15401):e.exports=s(54897)},54897:(e,t,s)=>{let r=s(83997),i=s(28354);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(23203);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(8824)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},60537:e=>{"use strict";function t(e,t){var t=t||{};this._capacity=t.capacity,this._head=0,this._tail=0,Array.isArray(e)?this._fromArray(e):(this._capacityMask=3,this._list=[,,,,])}t.prototype.peekAt=function(e){var t=e;if(t===(0|t)){var s=this.size();if(!(t>=s)&&!(t<-s))return t<0&&(t+=s),t=this._head+t&this._capacityMask,this._list[t]}},t.prototype.get=function(e){return this.peekAt(e)},t.prototype.peek=function(){if(this._head!==this._tail)return this._list[this._head]},t.prototype.peekFront=function(){return this.peek()},t.prototype.peekBack=function(){return this.peekAt(-1)},Object.defineProperty(t.prototype,"length",{get:function(){return this.size()}}),t.prototype.size=function(){return this._head===this._tail?0:this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},t.prototype.unshift=function(e){if(0==arguments.length)return this.size();var t=this._list.length;return(this._head=this._head-1+t&this._capacityMask,this._list[this._head]=e,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.pop(),this._head<this._tail)?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},t.prototype.shift=function(){var e=this._head;if(e!==this._tail){var t=this._list[e];return this._list[e]=void 0,this._head=e+1&this._capacityMask,e<2&&this._tail>1e4&&this._tail<=this._list.length>>>2&&this._shrinkArray(),t}},t.prototype.push=function(e){if(0==arguments.length)return this.size();var t=this._tail;return(this._list[t]=e,this._tail=t+1&this._capacityMask,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.shift(),this._head<this._tail)?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},t.prototype.pop=function(){var e=this._tail;if(e!==this._head){var t=this._list.length;this._tail=e-1+t&this._capacityMask;var s=this._list[this._tail];return this._list[this._tail]=void 0,this._head<2&&e>1e4&&e<=t>>>2&&this._shrinkArray(),s}},t.prototype.removeOne=function(e){var t,s=e;if(s===(0|s)&&this._head!==this._tail){var r=this.size(),i=this._list.length;if(!(s>=r)&&!(s<-r)){s<0&&(s+=r),s=this._head+s&this._capacityMask;var n=this._list[s];if(e<r/2){for(t=e;t>0;t--)this._list[s]=this._list[s=s-1+i&this._capacityMask];this._list[s]=void 0,this._head=this._head+1+i&this._capacityMask}else{for(t=r-1-e;t>0;t--)this._list[s]=this._list[s=s+1+i&this._capacityMask];this._list[s]=void 0,this._tail=this._tail-1+i&this._capacityMask}return n}}},t.prototype.remove=function(e,t){var s,r,i=e,n=t;if(i===(0|i)&&this._head!==this._tail){var o=this.size(),a=this._list.length;if(!(i>=o)&&!(i<-o)&&!(t<1)){if(i<0&&(i+=o),1===t||!t)return(s=[,])[0]=this.removeOne(i),s;if(0===i&&i+t>=o)return s=this.toArray(),this.clear(),s;for(i+t>o&&(t=o-i),s=Array(t),r=0;r<t;r++)s[r]=this._list[this._head+i+r&this._capacityMask];if(i=this._head+i&this._capacityMask,e+t===o){for(this._tail=this._tail-t+a&this._capacityMask,r=t;r>0;r--)this._list[i=i+1+a&this._capacityMask]=void 0;return s}if(0===e){for(this._head=this._head+t+a&this._capacityMask,r=t-1;r>0;r--)this._list[i=i+1+a&this._capacityMask]=void 0;return s}if(i<o/2){for(this._head=this._head+e+t+a&this._capacityMask,r=e;r>0;r--)this.unshift(this._list[i=i-1+a&this._capacityMask]);for(i=this._head-1+a&this._capacityMask;n>0;)this._list[i=i-1+a&this._capacityMask]=void 0,n--;e<0&&(this._tail=i)}else{for(this._tail=i,i=i+t+a&this._capacityMask,r=o-(t+e);r>0;r--)this.push(this._list[i++]);for(i=this._tail;n>0;)this._list[i=i+1+a&this._capacityMask]=void 0,n--}return this._head<2&&this._tail>1e4&&this._tail<=a>>>2&&this._shrinkArray(),s}}},t.prototype.splice=function(e,t){var s=e;if(s===(0|s)){var r=this.size();if(s<0&&(s+=r),!(s>r)){if(!(arguments.length>2))return this.remove(s,t);var i,n,o,a=arguments.length,l=this._list.length,c=2;if(!r||s<r/2){for(i=0,n=Array(s);i<s;i++)n[i]=this._list[this._head+i&this._capacityMask];for(0===t?(o=[],s>0&&(this._head=this._head+s+l&this._capacityMask)):(o=this.remove(s,t),this._head=this._head+s+l&this._capacityMask);a>c;)this.unshift(arguments[--a]);for(i=s;i>0;i--)this.unshift(n[i-1])}else{var u=(n=Array(r-(s+t))).length;for(i=0;i<u;i++)n[i]=this._list[this._head+s+t+i&this._capacityMask];for(0===t?(o=[],s!=r&&(this._tail=this._head+s+l&this._capacityMask)):(o=this.remove(s,t),this._tail=this._tail-u+l&this._capacityMask);c<a;)this.push(arguments[c++]);for(i=0;i<u;i++)this.push(n[i])}return o}}},t.prototype.clear=function(){this._list=Array(this._list.length),this._head=0,this._tail=0},t.prototype.isEmpty=function(){return this._head===this._tail},t.prototype.toArray=function(){return this._copyArray(!1)},t.prototype._fromArray=function(e){var t=e.length,s=this._nextPowerOf2(t);this._list=Array(s),this._capacityMask=s-1,this._tail=t;for(var r=0;r<t;r++)this._list[r]=e[r]},t.prototype._copyArray=function(e,t){var s,r=this._list,i=r.length,n=this.length;if((t|=n)==n&&this._head<this._tail)return this._list.slice(this._head,this._tail);var o=Array(t),a=0;if(e||this._head>this._tail){for(s=this._head;s<i;s++)o[a++]=r[s];for(s=0;s<this._tail;s++)o[a++]=r[s]}else for(s=this._head;s<this._tail;s++)o[a++]=r[s];return o},t.prototype._growArray=function(){if(0!=this._head){var e=this._copyArray(!0,this._list.length<<1);this._tail=this._list.length,this._head=0,this._list=e}else this._tail=this._list.length,this._list.length<<=1;this._capacityMask=this._capacityMask<<1|1},t.prototype._shrinkArray=function(){this._list.length>>>=1,this._capacityMask>>>=1},t.prototype._nextPowerOf2=function(e){return Math.max(1<<Math.log(e)/Math.log(2)+1,4)},e.exports=t},58684:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(s+e),i=t.indexOf("--");return -1!==r&&(-1===i||r<i)}},68058:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(60670),i=s(90438),n=s(84542),o=s(44945);class a{constructor(e,t=[],s={},r){if(this.name=e,this.inTransaction=!1,this.isResolved=!1,this.transformed=!1,this.replyEncoding=s.replyEncoding,this.errorStack=s.errorStack,this.args=t.flat(),this.callback=r,this.initPromise(),s.keyPrefix){let e=s.keyPrefix instanceof Buffer,t=e?s.keyPrefix:null;this._iterateKeys(r=>r instanceof Buffer?(null===t&&(t=Buffer.from(s.keyPrefix)),Buffer.concat([t,r])):e?Buffer.concat([s.keyPrefix,Buffer.from(String(r))]):s.keyPrefix+r)}s.readOnly&&(this.isReadOnly=!0)}static checkFlag(e,t){return!!this.getFlagMap()[e][t]}static setArgumentTransformer(e,t){this._transformer.argument[e]=t}static setReplyTransformer(e,t){this._transformer.reply[e]=t}static getFlagMap(){return this.flagMap||(this.flagMap=Object.keys(a.FLAGS).reduce((e,t)=>(e[t]={},a.FLAGS[t].forEach(s=>{e[t][s]=!0}),e),{})),this.flagMap}getSlot(){if(void 0===this.slot){let e=this.getKeys()[0];this.slot=null==e?null:i(e)}return this.slot}getKeys(){return this._iterateKeys()}toWritable(e){let t;let s="*"+(this.args.length+1)+"\r\n$"+Buffer.byteLength(this.name)+"\r\n"+this.name+"\r\n";if(this.bufferMode){let e=new u;e.push(s);for(let t=0;t<this.args.length;++t){let s=this.args[t];s instanceof Buffer?0===s.length?e.push("$0\r\n\r\n"):(e.push("$"+s.length+"\r\n"),e.push(s),e.push("\r\n")):e.push("$"+Buffer.byteLength(s)+"\r\n"+s+"\r\n")}t=e.toBuffer()}else{t=s;for(let e=0;e<this.args.length;++e){let s=this.args[e];t+="$"+Buffer.byteLength(s)+"\r\n"+s+"\r\n"}}return t}stringifyArguments(){for(let e=0;e<this.args.length;++e){let t=this.args[e];"string"==typeof t||(t instanceof Buffer?this.bufferMode=!0:this.args[e]=(0,o.toArg)(t))}}transformReply(e){this.replyEncoding&&(e=(0,o.convertBufferToString)(e,this.replyEncoding));let t=a._transformer.reply[this.name];return t&&(e=t(e)),e}setTimeout(e){this._commandTimeoutTimer||(this._commandTimeoutTimer=setTimeout(()=>{this.isResolved||this.reject(Error("Command timed out"))},e))}initPromise(){let e=new Promise((e,t)=>{if(!this.transformed){this.transformed=!0;let e=a._transformer.argument[this.name];e&&(this.args=e(this.args)),this.stringifyArguments()}this.resolve=this._convertValue(e),this.errorStack?this.reject=e=>{t((0,o.optimizeErrorStack)(e,this.errorStack.stack,__dirname))}:this.reject=t});this.promise=(0,n.default)(e,this.callback)}_iterateKeys(e=e=>e){if(void 0===this.keys&&(this.keys=[],(0,r.exists)(this.name)))for(let t of(0,r.getKeyIndexes)(this.name,this.args))this.args[t]=e(this.args[t]),this.keys.push(this.args[t]);return this.keys}_convertValue(e){return t=>{try{let s=this._commandTimeoutTimer;s&&(clearTimeout(s),delete this._commandTimeoutTimer),e(this.transformReply(t)),this.isResolved=!0}catch(e){this.reject(e)}return this.promise}}}t.default=a,a.FLAGS={VALID_IN_SUBSCRIBER_MODE:["subscribe","psubscribe","unsubscribe","punsubscribe","ssubscribe","sunsubscribe","ping","quit"],VALID_IN_MONITOR_MODE:["monitor","auth"],ENTER_SUBSCRIBER_MODE:["subscribe","psubscribe","ssubscribe"],EXIT_SUBSCRIBER_MODE:["unsubscribe","punsubscribe","sunsubscribe"],WILL_DISCONNECT:["quit"]},a._transformer={argument:{},reply:{}};let l=function(e){if(1===e.length){if(e[0]instanceof Map)return(0,o.convertMapToArray)(e[0]);if("object"==typeof e[0]&&null!==e[0])return(0,o.convertObjectToArray)(e[0])}return e},c=function(e){if(2===e.length){if(e[1]instanceof Map)return[e[0]].concat((0,o.convertMapToArray)(e[1]));if("object"==typeof e[1]&&null!==e[1])return[e[0]].concat((0,o.convertObjectToArray)(e[1]))}return e};a.setArgumentTransformer("mset",l),a.setArgumentTransformer("msetnx",l),a.setArgumentTransformer("hset",c),a.setArgumentTransformer("hmset",c),a.setReplyTransformer("hgetall",function(e){if(Array.isArray(e)){let t={};for(let s=0;s<e.length;s+=2){let r=e[s],i=e[s+1];r in t?Object.defineProperty(t,r,{value:i,configurable:!0,enumerable:!0,writable:!0}):t[r]=i}return t}return e});class u{constructor(){this.length=0,this.items=[]}push(e){this.length+=Buffer.byteLength(e),this.items.push(e)}toBuffer(){let e=Buffer.allocUnsafe(this.length),t=0;for(let s of this.items){let r=Buffer.byteLength(s);Buffer.isBuffer(s)?s.copy(e,t):e.write(s,t,r),t+=r}return e}}},30059:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(68058),i=s(44945),n=s(79244),o=s(81416),a=(0,i.Debug)("dataHandler");class l{constructor(e,t){this.redis=e;let s=new n({stringNumbers:t.stringNumbers,returnBuffers:!0,returnError:e=>{this.returnError(e)},returnFatalError:e=>{this.returnFatalError(e)},returnReply:e=>{this.returnReply(e)}});e.stream.prependListener("data",e=>{s.execute(e)}),e.stream.resume()}returnFatalError(e){e.message+=". Please report this.",this.redis.recoverFromFatalError(e,e,{offlineQueue:!1})}returnError(e){let t=this.shiftCommand(e);t&&(e.command={name:t.command.name,args:t.command.args},this.redis.handleReconnection(e,t))}returnReply(e){if(this.handleMonitorReply(e)||this.handleSubscriberReply(e))return;let t=this.shiftCommand(e);t&&(r.default.checkFlag("ENTER_SUBSCRIBER_MODE",t.command.name)?(this.redis.condition.subscriber=new o.default,this.redis.condition.subscriber.add(t.command.name,e[1].toString()),u(t.command,e[2])||this.redis.commandQueue.unshift(t)):r.default.checkFlag("EXIT_SUBSCRIBER_MODE",t.command.name)?h(t.command,e[2])||this.redis.commandQueue.unshift(t):t.command.resolve(e))}handleSubscriberReply(e){if(!this.redis.condition.subscriber)return!1;let t=Array.isArray(e)?e[0].toString():null;switch(a('receive reply "%s" in subscriber mode',t),t){case"message":this.redis.listeners("message").length>0&&this.redis.emit("message",e[1].toString(),e[2]?e[2].toString():""),this.redis.emit("messageBuffer",e[1],e[2]);break;case"pmessage":{let t=e[1].toString();this.redis.listeners("pmessage").length>0&&this.redis.emit("pmessage",t,e[2].toString(),e[3].toString()),this.redis.emit("pmessageBuffer",t,e[2],e[3]);break}case"smessage":this.redis.listeners("smessage").length>0&&this.redis.emit("smessage",e[1].toString(),e[2]?e[2].toString():""),this.redis.emit("smessageBuffer",e[1],e[2]);break;case"ssubscribe":case"subscribe":case"psubscribe":{let s=e[1].toString();this.redis.condition.subscriber.add(t,s);let r=this.shiftCommand(e);if(!r)return;u(r.command,e[2])||this.redis.commandQueue.unshift(r);break}case"sunsubscribe":case"unsubscribe":case"punsubscribe":{let s=e[1]?e[1].toString():null;s&&this.redis.condition.subscriber.del(t,s);let r=e[2];0===Number(r)&&(this.redis.condition.subscriber=!1);let i=this.shiftCommand(e);if(!i)return;h(i.command,r)||this.redis.commandQueue.unshift(i);break}default:{let t=this.shiftCommand(e);if(!t)return;t.command.resolve(e)}}return!0}handleMonitorReply(e){if("monitoring"!==this.redis.status)return!1;let t=e.toString();if("OK"===t)return!1;let s=t.indexOf(" "),r=t.slice(0,s),i=t.indexOf('"'),n=t.slice(i+1,-1).split('" "').map(e=>e.replace(/\\"/g,'"')),o=t.slice(s+2,i-2).split(" ");return this.redis.emit("monitor",r,n,o[1],o[0]),!0}shiftCommand(e){let t=this.redis.commandQueue.shift();if(!t){let t=Error("Command queue state error. If you can reproduce this, please report it."+(e instanceof Error?` Last error: ${e.message}`:` Last reply: ${e.toString()}`));return this.redis.emit("error",t),null}return t}}t.default=l;let c=new WeakMap;function u(e,t){let s=c.has(e)?c.get(e):e.args.length;return(s-=1)<=0?(e.resolve(t),c.delete(e),!0):(c.set(e,s),!1)}function h(e,t){let s=c.has(e)?c.get(e):e.args.length;return 0===s?0===Number(t)&&(c.delete(e),e.resolve(t),!0):(s-=1)<=0?(e.resolve(t),!0):(c.set(e,s),!1)}},41221:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(90438),i=s(60670),n=s(84542),o=s(28354),a=s(68058),l=s(44945),c=s(31459);class u extends c.default{constructor(e){super(),this.redis=e,this.isPipeline=!0,this.replyPending=0,this._queue=[],this._result=[],this._transactions=0,this._shaToScript={},this.isCluster="Cluster"===this.redis.constructor.name||this.redis.isCluster,this.options=e.options,Object.keys(e.scriptsSet).forEach(t=>{let s=e.scriptsSet[t];this._shaToScript[s.sha]=s,this[t]=e[t],this[t+"Buffer"]=e[t+"Buffer"]}),e.addedBuiltinSet.forEach(t=>{this[t]=e[t],this[t+"Buffer"]=e[t+"Buffer"]}),this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t});let t=this;Object.defineProperty(this,"length",{get:function(){return t._queue.length}})}fillResult(e,t){if("exec"===this._queue[t].name&&Array.isArray(e[1])){let s=e[1].length;for(let r=0;r<s;r++){if(e[1][r]instanceof Error)continue;let i=this._queue[t-(s-r)];try{e[1][r]=i.transformReply(e[1][r])}catch(t){e[1][r]=t}}}if(this._result[t]=e,--this.replyPending)return;if(this.isCluster){let e,t=!0;for(let s=0;s<this._result.length;++s){let r=this._result[s][0],n=this._queue[s];if(r){if("exec"===n.name&&"EXECABORT Transaction discarded because of previous errors."===r.message)continue;if(e){if(e.name!==r.name||e.message!==r.message){t=!1;break}}else e={name:r.name,message:r.message}}else if(!n.inTransaction&&!((0,i.exists)(n.name)&&(0,i.hasFlag)(n.name,"readonly"))){t=!1;break}}if(e&&t){let t=this,s=e.message.split(" "),r=this._queue,i=!1;this._queue=[];for(let e=0;e<r.length;++e){if("ASK"===s[0]&&!i&&"asking"!==r[e].name&&(!r[e-1]||"asking"!==r[e-1].name)){let e=new a.default("asking");e.ignore=!0,this.sendCommand(e)}r[e].initPromise(),this.sendCommand(r[e]),i=r[e].inTransaction}let n=!0;void 0===this.leftRedirections&&(this.leftRedirections={});let o=function(){t.exec()},l=this.redis;if(l.handleError(e,this.leftRedirections,{moved:function(e,r){t.preferKey=r,l.slots[s[1]]=[r],l._groupsBySlot[s[1]]=l._groupsIds[l.slots[s[1]].join(";")],l.refreshSlotsCache(),t.exec()},ask:function(e,s){t.preferKey=s,t.exec()},tryagain:o,clusterDown:o,connectionClosed:o,maxRedirections:()=>{n=!1},defaults:()=>{n=!1}}),n)return}}let s=0;for(let e=0;e<this._queue.length-s;++e)this._queue[e+s].ignore&&(s+=1),this._result[e]=this._result[e+s];this.resolve(this._result.slice(0,this._result.length-s))}sendCommand(e){this._transactions>0&&(e.inTransaction=!0);let t=this._queue.length;return e.pipelineIndex=t,e.promise.then(e=>{this.fillResult([null,e],t)}).catch(e=>{this.fillResult([e],t)}),this._queue.push(e),this}addBatch(e){let t,s,r;for(let i=0;i<e.length;++i)s=(t=e[i])[0],r=t.slice(1),this[s].apply(this,r);return this}}t.default=u;let h=u.prototype.multi;u.prototype.multi=function(){return this._transactions+=1,h.apply(this,arguments)};let f=u.prototype.execBuffer;u.prototype.execBuffer=(0,o.deprecate)(function(){return this._transactions>0&&(this._transactions-=1),f.apply(this,arguments)},"Pipeline#execBuffer: Use Pipeline#exec instead"),u.prototype.exec=function(e){let t;if(this.isCluster&&!this.redis.slots.length)return"wait"===this.redis.status&&this.redis.connect().catch(l.noop),e&&!this.nodeifiedPromise&&(this.nodeifiedPromise=!0,(0,n.default)(this.promise,e)),this.redis.delayUntilReady(t=>{if(t){this.reject(t);return}this.exec(e)}),this.promise;if(this._transactions>0)return this._transactions-=1,f.apply(this,arguments);if(this.nodeifiedPromise||(this.nodeifiedPromise=!0,(0,n.default)(this.promise,e)),this._queue.length||this.resolve([]),this.isCluster){let e=[];for(let t=0;t<this._queue.length;t++){let s=this._queue[t].getKeys();if(s.length&&e.push(s[0]),s.length&&0>r.generateMulti(s))return this.reject(Error("All the keys in a pipeline command should belong to the same slot")),this.promise}if(e.length){if((t=function(e,t){let s=r(t[0]),i=e._groupsBySlot[s];for(let s=1;s<t.length;s++)if(e._groupsBySlot[r(t[s])]!==i)return -1;return s}(this.redis,e))<0)return this.reject(Error("All keys in the pipeline should belong to the same slots allocation group")),this.promise}else t=16384*Math.random()|0}let s=this;return function(){let e,r,i=s.replyPending=s._queue.length;s.isCluster&&(e={slot:t,redis:s.redis.connectionPool.nodes.all[s.preferKey]});let n="",o={isPipeline:!0,destination:s.isCluster?e:{redis:s.redis},write(e){"string"!=typeof e?(r||(r=[]),n&&(r.push(Buffer.from(n,"utf8")),n=""),r.push(e)):n+=e,--i||(r?(n&&r.push(Buffer.from(n,"utf8")),o.destination.redis.stream.write(Buffer.concat(r))):o.destination.redis.stream.write(n),i=s._queue.length,n="",r=void 0)}};for(let t=0;t<s._queue.length;++t)s.redis.sendCommand(s._queue[t],o,e);s.promise}(),this.promise}},14276:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(60670),i=s(94735),n=s(84542),o=s(3198),a=s(68058),l=s(34980),c=s(90564),u=s(5632),h=s(85220),f=s(34498),p=s(45455),d=s(44945),y=s(26992),m=s(31459),g=s(39704),b=s(60537),S=(0,d.Debug)("redis");class k extends m.default{constructor(e,t,s){if(super(),this.status="wait",this.isCluster=!1,this.reconnectTimeout=null,this.connectionEpoch=0,this.retryAttempts=0,this.manuallyClosing=!1,this._autoPipelines=new Map,this._runningAutoPipelines=new Set,this.parseOptions(e,t,s),i.EventEmitter.call(this),this.resetCommandQueue(),this.resetOfflineQueue(),this.options.Connector)this.connector=new this.options.Connector(this.options);else if(this.options.sentinels){let e=new c.default(this.options);e.emitter=this,this.connector=e}else this.connector=new l.StandaloneConnector(this.options);this.options.scripts&&Object.entries(this.options.scripts).forEach(([e,t])=>{this.defineCommand(e,t)}),this.options.lazyConnect?this.setStatus("wait"):this.connect().catch(g.noop)}static createClient(...e){return new k(...e)}get autoPipelineQueueSize(){let e=0;for(let t of this._autoPipelines.values())e+=t.length;return e}connect(e){let t=new Promise((e,t)=>{if("connecting"===this.status||"connect"===this.status||"ready"===this.status){t(Error("Redis is already connecting/connected"));return}this.connectionEpoch+=1,this.setStatus("connecting");let{options:s}=this;this.condition={select:s.db,auth:s.username?[s.username,s.password]:s.password,subscriber:!1};let r=this;(0,n.default)(this.connector.connect(function(e,t){r.silentEmit(e,t)}),function(i,n){if(i){r.flushQueue(i),r.silentEmit("error",i),t(i),r.setStatus("end");return}let o=s.tls?"secureConnect":"connect";if("sentinels"in s&&s.sentinels&&!s.enableTLSForSentinelMode&&(o="connect"),r.stream=n,s.noDelay&&n.setNoDelay(!0),"number"==typeof s.keepAlive&&(n.connecting?n.once(o,()=>{n.setKeepAlive(!0,s.keepAlive)}):n.setKeepAlive(!0,s.keepAlive)),n.connecting){if(n.once(o,u.connectHandler(r)),s.connectTimeout){let e=!1;n.setTimeout(s.connectTimeout,function(){if(e)return;n.setTimeout(0),n.destroy();let t=Error("connect ETIMEDOUT");t.errorno="ETIMEDOUT",t.code="ETIMEDOUT",t.syscall="connect",u.errorHandler(r)(t)}),n.once(o,function(){e=!0,n.setTimeout(0)})}}else if(n.destroyed){let e=r.connector.firstError;e&&process.nextTick(()=>{u.errorHandler(r)(e)}),process.nextTick(u.closeHandler(r))}else process.nextTick(u.connectHandler(r));n.destroyed||(n.once("error",u.errorHandler(r)),n.once("close",u.closeHandler(r)));let a=function(){r.removeListener("close",l),e()};var l=function(){r.removeListener("ready",a),t(Error(d.CONNECTION_CLOSED_ERROR_MSG))};r.once("ready",a),r.once("close",l)})});return(0,n.default)(t,e)}disconnect(e=!1){e||(this.manuallyClosing=!0),this.reconnectTimeout&&!e&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),"wait"===this.status?u.closeHandler(this)():this.connector.disconnect()}end(){this.disconnect()}duplicate(e){return new k({...this.options,...e})}get mode(){var e;return this.options.monitor?"monitor":(null===(e=this.condition)||void 0===e?void 0:e.subscriber)?"subscriber":"normal"}monitor(e){let t=this.duplicate({monitor:!0,lazyConnect:!1});return(0,n.default)(new Promise(function(e,s){t.once("error",s),t.once("monitoring",function(){e(t)})}),e)}sendCommand(e,t){var s,i;if("wait"===this.status&&this.connect().catch(g.noop),"end"===this.status)return e.reject(Error(d.CONNECTION_CLOSED_ERROR_MSG)),e.promise;if((null===(s=this.condition)||void 0===s?void 0:s.subscriber)&&!a.default.checkFlag("VALID_IN_SUBSCRIBER_MODE",e.name))return e.reject(Error("Connection in subscriber mode, only subscriber commands may be used")),e.promise;"number"==typeof this.options.commandTimeout&&e.setTimeout(this.options.commandTimeout);let n="ready"===this.status||!t&&"connect"===this.status&&(0,r.exists)(e.name)&&(0,r.hasFlag)(e.name,"loading");if(this.stream&&this.stream.writable?this.stream._writableState&&this.stream._writableState.ended&&(n=!1):n=!1,n)S.enabled&&S("write command[%s]: %d -> %s(%o)",this._getDescription(),null===(i=this.condition)||void 0===i?void 0:i.select,e.name,e.args),t?"isPipeline"in t&&t.isPipeline?t.write(e.toWritable(t.destination.redis.stream)):t.write(e.toWritable(t)):this.stream.write(e.toWritable(this.stream)),this.commandQueue.push({command:e,stream:t,select:this.condition.select}),a.default.checkFlag("WILL_DISCONNECT",e.name)&&(this.manuallyClosing=!0),void 0!==this.options.socketTimeout&&void 0===this.socketTimeoutTimer&&this.setSocketTimeout();else{if(!this.options.enableOfflineQueue)return e.reject(Error("Stream isn't writeable and enableOfflineQueue options is false")),e.promise;if("quit"===e.name&&0===this.offlineQueue.length)return this.disconnect(),e.resolve(Buffer.from("OK")),e.promise;S.enabled&&S("queue command[%s]: %d -> %s(%o)",this._getDescription(),this.condition.select,e.name,e.args),this.offlineQueue.push({command:e,stream:t,select:this.condition.select})}if("select"===e.name&&(0,d.isInt)(e.args[0])){let t=parseInt(e.args[0],10);this.condition.select!==t&&(this.condition.select=t,this.emit("select",t),S("switch to db [%d]",this.condition.select))}return e.promise}setSocketTimeout(){this.socketTimeoutTimer=setTimeout(()=>{this.stream.destroy(Error(`Socket timeout. Expecting data, but didn't receive any in ${this.options.socketTimeout}ms.`)),this.socketTimeoutTimer=void 0},this.options.socketTimeout),this.stream.once("data",()=>{clearTimeout(this.socketTimeoutTimer),this.socketTimeoutTimer=void 0,0!==this.commandQueue.length&&this.setSocketTimeout()})}scanStream(e){return this.createScanStream("scan",{options:e})}scanBufferStream(e){return this.createScanStream("scanBuffer",{options:e})}sscanStream(e,t){return this.createScanStream("sscan",{key:e,options:t})}sscanBufferStream(e,t){return this.createScanStream("sscanBuffer",{key:e,options:t})}hscanStream(e,t){return this.createScanStream("hscan",{key:e,options:t})}hscanBufferStream(e,t){return this.createScanStream("hscanBuffer",{key:e,options:t})}zscanStream(e,t){return this.createScanStream("zscan",{key:e,options:t})}zscanBufferStream(e,t){return this.createScanStream("zscanBuffer",{key:e,options:t})}silentEmit(e,t){let s;return"error"===e&&(s=t,"end"===this.status||this.manuallyClosing&&s instanceof Error&&(s.message===d.CONNECTION_CLOSED_ERROR_MSG||"connect"===s.syscall||"read"===s.syscall))?void 0:this.listeners(e).length>0?this.emit.apply(this,arguments):(s&&s instanceof Error&&console.error("[ioredis] Unhandled error event:",s.stack),!1)}recoverFromFatalError(e,t,s){this.flushQueue(t,s),this.silentEmit("error",t),this.disconnect(!0)}handleReconnection(e,t){var s;let r=!1;switch(this.options.reconnectOnError&&(r=this.options.reconnectOnError(e)),r){case 1:case!0:"reconnecting"!==this.status&&this.disconnect(!0),t.command.reject(e);break;case 2:"reconnecting"!==this.status&&this.disconnect(!0),(null===(s=this.condition)||void 0===s?void 0:s.select)!==t.select&&"select"!==t.command.name&&this.select(t.select),this.sendCommand(t.command);break;default:t.command.reject(e)}}_getDescription(){let e;return e="path"in this.options&&this.options.path?this.options.path:this.stream&&this.stream.remoteAddress&&this.stream.remotePort?this.stream.remoteAddress+":"+this.stream.remotePort:"host"in this.options&&this.options.host?this.options.host+":"+this.options.port:"",this.options.connectionName&&(e+=` (${this.options.connectionName})`),e}resetCommandQueue(){this.commandQueue=new b}resetOfflineQueue(){this.offlineQueue=new b}parseOptions(...e){let t={},s=!1;for(let r=0;r<e.length;++r){let i=e[r];if(null!=i){if("object"==typeof i)(0,g.defaults)(t,i);else if("string"==typeof i)(0,g.defaults)(t,(0,d.parseURL)(i)),i.startsWith("rediss://")&&(s=!0);else if("number"==typeof i)t.port=i;else throw Error("Invalid argument "+i)}}s&&(0,g.defaults)(t,{tls:!0}),(0,g.defaults)(t,k.defaultOptions),"string"==typeof t.port&&(t.port=parseInt(t.port,10)),"string"==typeof t.db&&(t.db=parseInt(t.db,10)),this.options=(0,d.resolveTLSProfile)(t)}setStatus(e,t){S.enabled&&S("status[%s]: %s -> %s",this._getDescription(),this.status||"[empty]",e),this.status=e,process.nextTick(this.emit.bind(this,e,t))}createScanStream(e,{key:t,options:s={}}){return new f.default({objectMode:!0,key:t,redis:this,command:e,...s})}flushQueue(e,t){let s;if((t=(0,g.defaults)({},t,{offlineQueue:!0,commandQueue:!0})).offlineQueue)for(;s=this.offlineQueue.shift();)s.command.reject(e);if(t.commandQueue&&this.commandQueue.length>0)for(this.stream&&this.stream.removeAllListeners("data");s=this.commandQueue.shift();)s.command.reject(e)}_readyCheck(e){let t=this;this.info(function(s,r){if(s)return s.message&&s.message.includes("NOPERM")?(console.warn(`Skipping the ready check because INFO command fails: "${s.message}". You can disable ready check with "enableReadyCheck". More: https://github.com/luin/ioredis/wiki/Disable-ready-check.`),e(null,{})):e(s);if("string"!=typeof r)return e(null,r);let i={},n=r.split("\r\n");for(let e=0;e<n.length;++e){let[t,...s]=n[e].split(":"),r=s.join(":");r&&(i[t]=r)}if(i.loading&&"0"!==i.loading){let s=1e3*(i.loading_eta_seconds||1),r=t.options.maxLoadingRetryTime&&t.options.maxLoadingRetryTime<s?t.options.maxLoadingRetryTime:s;S("Redis server still loading, trying again in "+r+"ms"),setTimeout(function(){t._readyCheck(e)},r)}else e(null,i)}).catch(g.noop)}}k.Cluster=o.default,k.Command=a.default,k.defaultOptions=h.DEFAULT_REDIS_OPTIONS,(0,y.default)(k,i.EventEmitter),(0,p.addTransactionSupport)(k.prototype),t.default=k},34498:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(27910);class i extends r.Readable{constructor(e){super(e),this.opt=e,this._redisCursor="0",this._redisDrained=!1}_read(){if(this._redisDrained){this.push(null);return}let e=[this._redisCursor];this.opt.key&&e.unshift(this.opt.key),this.opt.match&&e.push("MATCH",this.opt.match),this.opt.type&&e.push("TYPE",this.opt.type),this.opt.count&&e.push("COUNT",String(this.opt.count)),this.opt.noValues&&e.push("NOVALUES"),this.opt.redis[this.opt.command](e,(e,t)=>{if(e){this.emit("error",e);return}this._redisCursor=t[0]instanceof Buffer?t[0].toString():t[0],"0"===this._redisCursor&&(this._redisDrained=!0),this.push(t[1])})}close(){this._redisDrained=!0}}t.default=i},54924:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(55511),i=s(68058),n=s(84542);class o{constructor(e,t=null,s="",n=!1){this.lua=e,this.numberOfKeys=t,this.keyPrefix=s,this.readOnly=n,this.sha=(0,r.createHash)("sha1").update(e).digest("hex");let o=this.sha,a=new WeakSet;this.Command=class extends i.default{toWritable(t){let s=this.reject;return this.reject=e=>{-1!==e.message.indexOf("NOSCRIPT")&&a.delete(t),s.call(this,e)},a.has(t)?"eval"===this.name&&(this.name="evalsha",this.args[0]=o):(a.add(t),this.name="eval",this.args[0]=e),super.toWritable(t)}}}execute(e,t,s,r){"number"==typeof this.numberOfKeys&&t.unshift(this.numberOfKeys),this.keyPrefix&&(s.keyPrefix=this.keyPrefix),this.readOnly&&(s.readOnly=!0);let i=new this.Command("evalsha",[this.sha,...t],s);return i.promise=i.promise.catch(r=>{if(-1===r.message.indexOf("NOSCRIPT"))throw r;let i=new this.Command("evalsha",[this.sha,...t],s);return(e.isPipeline?e.redis:e).sendCommand(i)}),(0,n.default)(i.promise,r),e.sendCommand(i)}}t.default=o},81416:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class s{constructor(){this.set={subscribe:{},psubscribe:{},ssubscribe:{}}}add(e,t){this.set[r(e)][t]=!0}del(e,t){delete this.set[r(e)][t]}channels(e){return Object.keys(this.set[r(e)])}isEmpty(){return 0===this.channels("subscribe").length&&0===this.channels("psubscribe").length&&0===this.channels("ssubscribe").length}}function r(e){return"unsubscribe"===e?"subscribe":"punsubscribe"===e?"psubscribe":"sunsubscribe"===e?"ssubscribe":e}t.default=s},41497:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.executeWithAutoPipelining=t.getFirstValueInFlattenedArray=t.shouldUseAutoPipelining=t.notAllowedAutoPipelineCommands=t.kCallbacks=t.kExec=void 0;let r=s(39704),i=s(90438),n=s(84542);function o(e){for(let t=0;t<e.length;t++){let s=e[t];if("string"==typeof s)return s;if(Array.isArray(s)||(0,r.isArguments)(s)){if(0===s.length)continue;return s[0]}let i=[s].flat();if(i.length>0)return i[0]}}t.kExec=Symbol("exec"),t.kCallbacks=Symbol("callbacks"),t.notAllowedAutoPipelineCommands=["auth","info","script","quit","cluster","pipeline","multi","subscribe","psubscribe","unsubscribe","unpsubscribe","select"],t.shouldUseAutoPipelining=function(e,s,r){return s&&e.options.enableAutoPipelining&&!e.isPipeline&&!t.notAllowedAutoPipelineCommands.includes(r)&&!e.options.autoPipeliningIgnoredCommands.includes(r)},t.getFirstValueInFlattenedArray=o,t.executeWithAutoPipelining=function e(s,a,l,c,u){if(s.isCluster&&!s.slots.length)return"wait"===s.status&&s.connect().catch(r.noop),(0,n.default)(new Promise(function(t,r){s.delayUntilReady(i=>{if(i){r(i);return}e(s,a,l,c,null).then(t,r)})}),u);let h=s.options.keyPrefix||"",f=s.isCluster?s.slots[i(`${h}${o(c)}`)].join(","):"main";if(!s._autoPipelines.has(f)){let e=s.pipeline();e[t.kExec]=!1,e[t.kCallbacks]=[],s._autoPipelines.set(f,e)}let p=s._autoPipelines.get(f);p[t.kExec]||(p[t.kExec]=!0,setImmediate(function e(s,r){if(s._runningAutoPipelines.has(r)||!s._autoPipelines.has(r))return;s._runningAutoPipelines.add(r);let i=s._autoPipelines.get(r);s._autoPipelines.delete(r);let n=i[t.kCallbacks];i[t.kCallbacks]=null,i.exec(function(t,i){if(s._runningAutoPipelines.delete(r),t)for(let e=0;e<n.length;e++)process.nextTick(n[e],t);else for(let e=0;e<n.length;e++)process.nextTick(n[e],...i[e]);s._autoPipelines.has(r)&&e(s,r)})},s,f));let d=new Promise(function(e,s){p[t.kCallbacks].push(function(t,r){if(t){s(t);return}e(r)}),"call"===a&&c.unshift(l),p[a](...c)});return(0,n.default)(d,u)}},15004:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_CLUSTER_OPTIONS=void 0;let r=s(14985);t.DEFAULT_CLUSTER_OPTIONS={clusterRetryStrategy:e=>Math.min(100+2*e,2e3),enableOfflineQueue:!0,enableReadyCheck:!0,scaleReads:"master",maxRedirections:16,retryDelayOnMoved:0,retryDelayOnFailover:100,retryDelayOnClusterDown:100,retryDelayOnTryAgain:100,slotsRefreshTimeout:1e3,useSRVRecords:!1,resolveSrv:r.resolveSrv,dnsLookup:r.lookup,enableAutoPipelining:!1,autoPipeliningIgnoredCommands:[],shardedSubscribers:!1}},80354:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(99228),i=s(44945),n=s(14276),o=(0,i.Debug)("cluster:subscriber");class a{constructor(e,t,s=!1){this.connectionPool=e,this.emitter=t,this.isSharded=s,this.started=!1,this.subscriber=null,this.slotRange=[],this.onSubscriberEnd=()=>{if(!this.started){o("subscriber has disconnected, but ClusterSubscriber is not started, so not reconnecting.");return}o("subscriber has disconnected, selecting a new one..."),this.selectSubscriber()},this.connectionPool.on("-node",(e,t)=>{this.started&&this.subscriber&&(0,r.getNodeKey)(this.subscriber.options)===t&&(o("subscriber has left, selecting a new one..."),this.selectSubscriber())}),this.connectionPool.on("+node",()=>{this.started&&!this.subscriber&&(o("a new node is discovered and there is no subscriber, selecting a new one..."),this.selectSubscriber())})}getInstance(){return this.subscriber}associateSlotRange(e){return this.isSharded&&(this.slotRange=e),this.slotRange}start(){this.started=!0,this.selectSubscriber(),o("started")}stop(){this.started=!1,this.subscriber&&(this.subscriber.disconnect(),this.subscriber=null)}isStarted(){return this.started}selectSubscriber(){let e=this.lastActiveSubscriber;e&&(e.off("end",this.onSubscriberEnd),e.disconnect()),this.subscriber&&(this.subscriber.off("end",this.onSubscriberEnd),this.subscriber.disconnect());let t=(0,i.sample)(this.connectionPool.getNodes());if(!t){o("selecting subscriber failed since there is no node discovered in the cluster yet"),this.subscriber=null;return}let{options:s}=t;o("selected a subscriber %s:%s",s.host,s.port);let a="subscriber";this.isSharded&&(a="ssubscriber"),this.subscriber=new n.default({port:s.port,host:s.host,username:s.username,password:s.password,enableReadyCheck:!0,connectionName:(0,r.getConnectionName)(a,s.connectionName),lazyConnect:!0,tls:s.tls,retryStrategy:null}),this.subscriber.on("error",i.noop),this.subscriber.once("end",this.onSubscriberEnd);let l={subscribe:[],psubscribe:[],ssubscribe:[]};if(e){let t=e.condition||e.prevCondition;t&&t.subscriber&&(l.subscribe=t.subscriber.channels("subscribe"),l.psubscribe=t.subscriber.channels("psubscribe"),l.ssubscribe=t.subscriber.channels("ssubscribe"))}if(l.subscribe.length||l.psubscribe.length||l.ssubscribe.length){let e=0;for(let t of["subscribe","psubscribe","ssubscribe"]){let s=l[t];s.length&&(e+=1,o("%s %d channels",t,s.length),this.subscriber[t](s).then(()=>{--e||(this.lastActiveSubscriber=this.subscriber)}).catch(()=>{o("failed to %s %d channels",t,s.length)}))}}else this.lastActiveSubscriber=this.subscriber;for(let e of["message","messageBuffer"])this.subscriber.on(e,(t,s)=>{this.emitter.emit(e,t,s)});for(let e of["pmessage","pmessageBuffer"])this.subscriber.on(e,(t,s,r)=>{this.emitter.emit(e,t,s,r)});if(!0==this.isSharded)for(let e of["smessage","smessageBuffer"])this.subscriber.on(e,(t,s)=>{this.emitter.emit(e,t,s)})}}t.default=a},57231:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(44945),i=s(80354),n=s(51638),o=s(99228),a=s(90438),l=(0,r.Debug)("cluster:subscriberGroup");class c{constructor(e){this.cluster=e,this.shardedSubscribers=new Map,this.clusterSlots=[],this.subscriberToSlotsIndex=new Map,this.channels=new Map,e.on("+node",e=>{this._addSubscriber(e)}),e.on("-node",e=>{this._removeSubscriber(e)}),e.on("refresh",()=>{this._refreshSlots(e)})}getResponsibleSubscriber(e){let t=this.clusterSlots[e][0];return this.shardedSubscribers.get(t)}addChannels(e){let t=a(e[0]);e.forEach(e=>{if(a(e)!=t)return -1});let s=this.channels.get(t);return s?this.channels.set(t,s.concat(e)):this.channels.set(t,e),[...this.channels.values()].flatMap(e=>e).length}removeChannels(e){let t=a(e[0]);e.forEach(e=>{if(a(e)!=t)return -1});let s=this.channels.get(t);if(s){let r=s.filter(t=>!e.includes(t));this.channels.set(t,r)}return[...this.channels.values()].flatMap(e=>e).length}stop(){for(let e of this.shardedSubscribers.values())e.stop()}start(){for(let e of this.shardedSubscribers.values())e.isStarted()||e.start()}_addSubscriber(e){let t=new n.default(e.options);if(t.addMasterNode(e)){let s=new i.default(t,this.cluster,!0),r=(0,o.getNodeKey)(e.options);return this.shardedSubscribers.set(r,s),s.start(),this._resubscribe(),this.cluster.emit("+subscriber"),s}return null}_removeSubscriber(e){let t=(0,o.getNodeKey)(e.options),s=this.shardedSubscribers.get(t);return s&&(s.stop(),this.shardedSubscribers.delete(t),this._resubscribe(),this.cluster.emit("-subscriber")),this.shardedSubscribers}_refreshSlots(e){if(this._slotsAreEqual(e.slots))l("Nothing to refresh because the new cluster map is equal to the previous one.");else{l("Refreshing the slots of the subscriber group."),this.subscriberToSlotsIndex=new Map;for(let t=0;t<e.slots.length;t++){let s=e.slots[t][0];this.subscriberToSlotsIndex.has(s)||this.subscriberToSlotsIndex.set(s,[]),this.subscriberToSlotsIndex.get(s).push(Number(t))}return this._resubscribe(),this.clusterSlots=JSON.parse(JSON.stringify(e.slots)),this.cluster.emit("subscribersReady"),!0}return!1}_resubscribe(){this.shardedSubscribers&&this.shardedSubscribers.forEach((e,t)=>{let s=this.subscriberToSlotsIndex.get(t);s&&(e.associateSlotRange(s),s.forEach(t=>{let s=e.getInstance(),r=this.channels.get(t);r&&r.length>0&&s&&(s.ssubscribe(r),s.on("ready",()=>{s.ssubscribe(r)}))}))})}_slotsAreEqual(e){return void 0!==this.clusterSlots&&JSON.stringify(this.clusterSlots)===JSON.stringify(e)}}t.default=c},51638:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(94735),i=s(44945),n=s(99228),o=s(14276),a=(0,i.Debug)("cluster:connectionPool");class l extends r.EventEmitter{constructor(e){super(),this.redisOptions=e,this.nodes={all:{},master:{},slave:{}},this.specifiedOptions={}}getNodes(e="all"){let t=this.nodes[e];return Object.keys(t).map(e=>t[e])}getInstanceByKey(e){return this.nodes.all[e]}getSampleInstance(e){let t=Object.keys(this.nodes[e]),s=(0,i.sample)(t);return this.nodes[e][s]}addMasterNode(e){let t=(0,n.getNodeKey)(e.options),s=this.createRedisFromOptions(e,e.options.readOnly);return!e.options.readOnly&&(this.nodes.all[t]=s,this.nodes.master[t]=s,!0)}createRedisFromOptions(e,t){return new o.default((0,i.defaults)({retryStrategy:null,enableOfflineQueue:!0,readOnly:t},e,this.redisOptions,{lazyConnect:!0}))}findOrCreate(e,t=!1){let s;let r=(0,n.getNodeKey)(e);return t=!!t,this.specifiedOptions[r]?Object.assign(e,this.specifiedOptions[r]):this.specifiedOptions[r]=e,this.nodes.all[r]?(s=this.nodes.all[r]).options.readOnly!==t&&(s.options.readOnly=t,a("Change role of %s to %s",r,t?"slave":"master"),s[t?"readonly":"readwrite"]().catch(i.noop),t?(delete this.nodes.master[r],this.nodes.slave[r]=s):(delete this.nodes.slave[r],this.nodes.master[r]=s)):(a("Connecting to %s as %s",r,t?"slave":"master"),s=this.createRedisFromOptions(e,t),this.nodes.all[r]=s,this.nodes[t?"slave":"master"][r]=s,s.once("end",()=>{this.removeNode(r),this.emit("-node",s,r),Object.keys(this.nodes.all).length||this.emit("drain")}),this.emit("+node",s,r),s.on("error",function(e){this.emit("nodeError",e,r)})),s}reset(e){a("Reset with %O",e);let t={};e.forEach(e=>{let s=(0,n.getNodeKey)(e);e.readOnly&&t[s]||(t[s]=e)}),Object.keys(this.nodes.all).forEach(e=>{t[e]||(a("Disconnect %s because the node does not hold any slot",e),this.nodes.all[e].disconnect(),this.removeNode(e))}),Object.keys(t).forEach(e=>{let s=t[e];this.findOrCreate(s,s.readOnly)})}removeNode(e){let{nodes:t}=this;t.all[e]&&(a("Remove %s from the pool",e),delete t.all[e]),delete t.master[e],delete t.slave[e]}}t.default=l},77762:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(44945),i=s(60537),n=(0,r.Debug)("delayqueue");class o{constructor(){this.queues={},this.timeouts={}}push(e,t,s){let r=s.callback||process.nextTick;this.queues[e]||(this.queues[e]=new i),this.queues[e].push(t),this.timeouts[e]||(this.timeouts[e]=setTimeout(()=>{r(()=>{this.timeouts[e]=null,this.execute(e)})},s.timeout))}execute(e){let t=this.queues[e];if(!t)return;let{length:s}=t;if(s)for(n("send %d commands in %s queue",s,e),this.queues[e]=null;t.length>0;)t.shift()()}}t.default=o},3198:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(60670),i=s(94735),n=s(90560),o=s(84542),a=s(68058),l=s(78149),c=s(14276),u=s(34498),h=s(45455),f=s(44945),p=s(26992),d=s(31459),y=s(15004),m=s(80354),g=s(51638),b=s(77762),S=s(99228),k=s(60537),v=s(57231),C=(0,f.Debug)("cluster"),w=new WeakSet;class E extends d.default{constructor(e,t={}){if(super(),this.slots=[],this._groupsIds={},this._groupsBySlot=Array(16384),this.isCluster=!0,this.retryAttempts=0,this.delayQueue=new b.default,this.offlineQueue=new k,this.isRefreshing=!1,this._refreshSlotsCacheCallbacks=[],this._autoPipelines=new Map,this._runningAutoPipelines=new Set,this._readyDelayedCallbacks=[],this.connectionEpoch=0,i.EventEmitter.call(this),this.startupNodes=e,this.options=(0,f.defaults)({},t,y.DEFAULT_CLUSTER_OPTIONS,this.options),!0==this.options.shardedSubscribers&&(this.shardedSubscribers=new v.default(this)),this.options.redisOptions&&this.options.redisOptions.keyPrefix&&!this.options.keyPrefix&&(this.options.keyPrefix=this.options.redisOptions.keyPrefix),"function"!=typeof this.options.scaleReads&&-1===["all","master","slave"].indexOf(this.options.scaleReads))throw Error('Invalid option scaleReads "'+this.options.scaleReads+'". Expected "all", "master", "slave" or a custom function');this.connectionPool=new g.default(this.options.redisOptions),this.connectionPool.on("-node",(e,t)=>{this.emit("-node",e)}),this.connectionPool.on("+node",e=>{this.emit("+node",e)}),this.connectionPool.on("drain",()=>{this.setStatus("close")}),this.connectionPool.on("nodeError",(e,t)=>{this.emit("node error",e,t)}),this.subscriber=new m.default(this.connectionPool,this),this.options.scripts&&Object.entries(this.options.scripts).forEach(([e,t])=>{this.defineCommand(e,t)}),this.options.lazyConnect?this.setStatus("wait"):this.connect().catch(e=>{C("connecting failed: %s",e)})}connect(){return new Promise((e,t)=>{if("connecting"===this.status||"connect"===this.status||"ready"===this.status){t(Error("Redis is already connecting/connected"));return}let s=++this.connectionEpoch;this.setStatus("connecting"),this.resolveStartupNodeHostnames().then(r=>{let i;if(this.connectionEpoch!==s){C("discard connecting after resolving startup nodes because epoch not match: %d != %d",s,this.connectionEpoch),t(new n.RedisError("Connection is discarded because a new connection is made"));return}if("connecting"!==this.status){C("discard connecting after resolving startup nodes because the status changed to %s",this.status),t(new n.RedisError("Connection is aborted"));return}this.connectionPool.reset(r);let o=()=>{this.setStatus("ready"),this.retryAttempts=0,this.executeOfflineCommands(),this.resetNodesRefreshInterval(),e()},a=()=>{this.invokeReadyDelayedCallbacks(void 0),this.removeListener("close",i),this.manuallyClosing=!1,this.setStatus("connect"),this.options.enableReadyCheck?this.readyCheck((e,t)=>{e||t?(C("Ready check failed (%s). Reconnecting...",e||t),"connect"===this.status&&this.disconnect(!0)):o()}):o()};i=()=>{let e=Error("None of startup nodes is available");this.removeListener("refresh",a),this.invokeReadyDelayedCallbacks(e),t(e)},this.once("refresh",a),this.once("close",i),this.once("close",this.handleCloseEvent.bind(this)),this.refreshSlotsCache(e=>{e&&e.message===l.default.defaultMessage&&(c.default.prototype.silentEmit.call(this,"error",e),this.connectionPool.reset([]))}),this.subscriber.start(),this.options.shardedSubscribers&&this.shardedSubscribers.start()}).catch(e=>{this.setStatus("close"),this.handleCloseEvent(e),this.invokeReadyDelayedCallbacks(e),t(e)})})}disconnect(e=!1){let t=this.status;this.setStatus("disconnecting"),e||(this.manuallyClosing=!0),this.reconnectTimeout&&!e&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null,C("Canceled reconnecting attempts")),this.clearNodesRefreshInterval(),this.subscriber.stop(),this.options.shardedSubscribers&&this.shardedSubscribers.stop(),"wait"===t?(this.setStatus("close"),this.handleCloseEvent()):this.connectionPool.reset([])}quit(e){let t=this.status;if(this.setStatus("disconnecting"),this.manuallyClosing=!0,this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.clearNodesRefreshInterval(),this.subscriber.stop(),this.options.shardedSubscribers&&this.shardedSubscribers.stop(),"wait"===t){let t=(0,o.default)(Promise.resolve("OK"),e);return setImmediate((function(){this.setStatus("close"),this.handleCloseEvent()}).bind(this)),t}return(0,o.default)(Promise.all(this.nodes().map(e=>e.quit().catch(e=>{if(e.message===f.CONNECTION_CLOSED_ERROR_MSG)return"OK";throw e}))).then(()=>"OK"),e)}duplicate(e=[],t={}){return new E(e.length>0?e:this.startupNodes.slice(0),Object.assign({},this.options,t))}nodes(e="all"){if("all"!==e&&"master"!==e&&"slave"!==e)throw Error('Invalid role "'+e+'". Expected "all", "master" or "slave"');return this.connectionPool.getNodes(e)}delayUntilReady(e){this._readyDelayedCallbacks.push(e)}get autoPipelineQueueSize(){let e=0;for(let t of this._autoPipelines.values())e+=t.length;return e}refreshSlotsCache(e){if(e&&this._refreshSlotsCacheCallbacks.push(e),this.isRefreshing)return;this.isRefreshing=!0;let t=this,s=e=>{for(let t of(this.isRefreshing=!1,this._refreshSlotsCacheCallbacks))t(e);this._refreshSlotsCacheCallbacks=[]},r=(0,f.shuffle)(this.connectionPool.getNodes()),i=null;!function e(n){if(n===r.length)return s(new l.default(l.default.defaultMessage,i));let o=r[n],a=`${o.options.host}:${o.options.port}`;C("getting slot cache from %s",a),t.getInfoFromNode(o,function(r){switch(t.status){case"close":case"end":return s(Error("Cluster is disconnected."));case"disconnecting":return s(Error("Cluster is disconnecting."))}r?(t.emit("node error",r,a),i=r,e(n+1)):(t.emit("refresh"),s())})}(0)}sendCommand(e,t,s){if("wait"===this.status&&this.connect().catch(f.noop),"end"===this.status)return e.reject(Error(f.CONNECTION_CLOSED_ERROR_MSG)),e.promise;let i=this.options.scaleReads;"master"===i||e.isReadOnly||(0,r.exists)(e.name)&&(0,r.hasFlag)(e.name,"readonly")||(i="master");let o=s?s.slot:e.getSlot(),l={},c=this;if(!s&&!w.has(e)){w.add(e);let t=e.reject;e.reject=function(s){let r=u.bind(null,!0);c.handleError(s,l,{moved:function(t,s){C("command %s is moved to %s",e.name,s),o=Number(t),c.slots[t]?c.slots[t][0]=s:c.slots[t]=[s],c._groupsBySlot[t]=c._groupsIds[c.slots[t].join(";")],c.connectionPool.findOrCreate(c.natMapper(s)),u(),C("refreshing slot caches... (triggered by MOVED error)"),c.refreshSlotsCache()},ask:function(t,s){C("command %s is required to ask %s:%s",e.name,s);let r=c.natMapper(s);c.connectionPool.findOrCreate(r),u(!1,`${r.host}:${r.port}`)},tryagain:r,clusterDown:r,connectionClosed:r,maxRedirections:function(s){t.call(e,s)},defaults:function(){t.call(e,s)}})}}function u(r,l){let u;if("end"===c.status){e.reject(new n.AbortError("Cluster is ended."));return}if("ready"===c.status||"cluster"===e.name){if(s&&s.redis)u=s.redis;else if(a.default.checkFlag("ENTER_SUBSCRIBER_MODE",e.name)||a.default.checkFlag("EXIT_SUBSCRIBER_MODE",e.name)){if(!0==c.options.shardedSubscribers&&("ssubscribe"==e.name||"sunsubscribe"==e.name)){let t=c.shardedSubscribers.getResponsibleSubscriber(o),s=-1;"ssubscribe"==e.name&&(s=c.shardedSubscribers.addChannels(e.getKeys())),"sunsubscribe"==e.name&&(s=c.shardedSubscribers.removeChannels(e.getKeys())),-1!==s?u=t.getInstance():e.reject(new n.AbortError("Can't add or remove the given channels. Are they in the same slot?"))}else u=c.subscriber.getInstance();if(!u){e.reject(new n.AbortError("No subscriber for the cluster"));return}}else{if(!r){if("number"==typeof o&&c.slots[o]){let t=c.slots[o];if("function"==typeof i){let s=t.map(function(e){return c.connectionPool.getInstanceByKey(e)});Array.isArray(u=i(s,e))&&(u=(0,f.sample)(u)),u||(u=s[0])}else{let e;e="all"===i?(0,f.sample)(t):"slave"===i&&t.length>1?(0,f.sample)(t,1):t[0],u=c.connectionPool.getInstanceByKey(e)}}l&&(u=c.connectionPool.getInstanceByKey(l)).asking()}u||(u=("function"==typeof i?null:c.connectionPool.getSampleInstance(i))||c.connectionPool.getSampleInstance("all"))}s&&!s.redis&&(s.redis=u)}u?u.sendCommand(e,t):c.options.enableOfflineQueue?c.offlineQueue.push({command:e,stream:t,node:s}):e.reject(Error("Cluster isn't ready and enableOfflineQueue options is false"))}return u(),e.promise}sscanStream(e,t){return this.createScanStream("sscan",{key:e,options:t})}sscanBufferStream(e,t){return this.createScanStream("sscanBuffer",{key:e,options:t})}hscanStream(e,t){return this.createScanStream("hscan",{key:e,options:t})}hscanBufferStream(e,t){return this.createScanStream("hscanBuffer",{key:e,options:t})}zscanStream(e,t){return this.createScanStream("zscan",{key:e,options:t})}zscanBufferStream(e,t){return this.createScanStream("zscanBuffer",{key:e,options:t})}handleError(e,t,s){if(void 0===t.value?t.value=this.options.maxRedirections:t.value-=1,t.value<=0){s.maxRedirections(Error("Too many Cluster redirections. Last error: "+e));return}let r=e.message.split(" ");if("MOVED"===r[0]){let e=this.options.retryDelayOnMoved;e&&"number"==typeof e?this.delayQueue.push("moved",s.moved.bind(null,r[1],r[2]),{timeout:e}):s.moved(r[1],r[2])}else"ASK"===r[0]?s.ask(r[1],r[2]):"TRYAGAIN"===r[0]?this.delayQueue.push("tryagain",s.tryagain,{timeout:this.options.retryDelayOnTryAgain}):"CLUSTERDOWN"===r[0]&&this.options.retryDelayOnClusterDown>0?this.delayQueue.push("clusterdown",s.connectionClosed,{timeout:this.options.retryDelayOnClusterDown,callback:this.refreshSlotsCache.bind(this)}):e.message===f.CONNECTION_CLOSED_ERROR_MSG&&this.options.retryDelayOnFailover>0&&"ready"===this.status?this.delayQueue.push("failover",s.connectionClosed,{timeout:this.options.retryDelayOnFailover,callback:this.refreshSlotsCache.bind(this)}):s.defaults()}resetOfflineQueue(){this.offlineQueue=new k}clearNodesRefreshInterval(){this.slotsTimer&&(clearTimeout(this.slotsTimer),this.slotsTimer=null)}resetNodesRefreshInterval(){if(this.slotsTimer||!this.options.slotsRefreshInterval)return;let e=()=>{this.slotsTimer=setTimeout(()=>{C('refreshing slot caches... (triggered by "slotsRefreshInterval" option)'),this.refreshSlotsCache(()=>{e()})},this.options.slotsRefreshInterval)};e()}setStatus(e){C("status: %s -> %s",this.status||"[empty]",e),this.status=e,process.nextTick(()=>{this.emit(e)})}handleCloseEvent(e){let t;e&&C("closed because %s",e),this.manuallyClosing||"function"!=typeof this.options.clusterRetryStrategy||(t=this.options.clusterRetryStrategy.call(this,++this.retryAttempts,e)),"number"==typeof t?(this.setStatus("reconnecting"),this.reconnectTimeout=setTimeout(()=>{this.reconnectTimeout=null,C("Cluster is disconnected. Retrying after %dms",t),this.connect().catch(function(e){C("Got error %s when reconnecting. Ignoring...",e)})},t)):(this.setStatus("end"),this.flushQueue(Error("None of startup nodes is available")))}flushQueue(e){let t;for(;t=this.offlineQueue.shift();)t.command.reject(e)}executeOfflineCommands(){if(this.offlineQueue.length){let e;C("send %d commands in offline queue",this.offlineQueue.length);let t=this.offlineQueue;for(this.resetOfflineQueue();e=t.shift();)this.sendCommand(e.command,e.stream,e.node)}}natMapper(e){let t="string"==typeof e?e:`${e.host}:${e.port}`,s=null;return(this.options.natMap&&"function"==typeof this.options.natMap?s=this.options.natMap(t):this.options.natMap&&"object"==typeof this.options.natMap&&(s=this.options.natMap[t]),s)?(C("NAT mapping %s -> %O",t,s),Object.assign({},s)):"string"==typeof e?(0,S.nodeKeyToRedisOptions)(e):e}getInfoFromNode(e,t){if(!e)return t(Error("Node is disconnected"));let s=e.duplicate({enableOfflineQueue:!0,enableReadyCheck:!1,retryStrategy:null,connectionName:(0,S.getConnectionName)("refresher",this.options.redisOptions&&this.options.redisOptions.connectionName)});s.on("error",f.noop),s.cluster("SLOTS",(0,f.timeout)((e,r)=>{if(s.disconnect(),e)return C("error encountered running CLUSTER.SLOTS: %s",e),t(e);if("disconnecting"===this.status||"close"===this.status||"end"===this.status){C("ignore CLUSTER.SLOTS results (count: %d) since cluster status is %s",r.length,this.status),t();return}let i=[];C("cluster slots result count: %d",r.length);for(let e=0;e<r.length;++e){let t=r[e],s=t[0],n=t[1],o=[];for(let e=2;e<t.length;e++){if(!t[e][0])continue;let s=this.natMapper({host:t[e][0],port:t[e][1]});s.readOnly=2!==e,i.push(s),o.push(s.host+":"+s.port)}C("cluster slots result [%d]: slots %d~%d served by %s",e,s,n,o);for(let e=s;e<=n;e++)this.slots[e]=o}this._groupsIds=Object.create(null);let n=0;for(let e=0;e<16384;e++){let t=(this.slots[e]||[]).join(";");if(!t.length){this._groupsBySlot[e]=void 0;continue}this._groupsIds[t]||(this._groupsIds[t]=++n),this._groupsBySlot[e]=this._groupsIds[t]}this.connectionPool.reset(i),t()},this.options.slotsRefreshTimeout))}invokeReadyDelayedCallbacks(e){for(let t of this._readyDelayedCallbacks)process.nextTick(t,e);this._readyDelayedCallbacks=[]}readyCheck(e){this.cluster("INFO",(t,s)=>{let r;if(t)return e(t);if("string"!=typeof s)return e();let i=s.split("\r\n");for(let e=0;e<i.length;++e){let t=i[e].split(":");if("cluster_state"===t[0]){r=t[1];break}}"fail"===r?(C("cluster state not ok (%s)",r),e(null,r)):e()})}resolveSrv(e){return new Promise((t,s)=>{this.options.resolveSrv(e,(e,r)=>{if(e)return s(e);let i=this,n=(0,S.groupSrvRecords)(r),o=Object.keys(n).sort((e,t)=>parseInt(e)-parseInt(t));!function e(r){if(!o.length)return s(r);let a=n[o[0]],l=(0,S.weightSrvRecords)(a);a.records.length||o.shift(),i.dnsLookup(l.name).then(e=>t({host:e,port:l.port}),e)}()})})}dnsLookup(e){return new Promise((t,s)=>{this.options.dnsLookup(e,(r,i)=>{r?(C("failed to resolve hostname %s to IP: %s",e,r.message),s(r)):(C("resolved hostname %s to IP %s",e,i),t(i))})})}async resolveStartupNodeHostnames(){if(!Array.isArray(this.startupNodes)||0===this.startupNodes.length)throw Error("`startupNodes` should contain at least one node.");let e=(0,S.normalizeNodeOptions)(this.startupNodes),t=(0,S.getUniqueHostnamesFromOptions)(e);if(0===t.length)return e;let s=await Promise.all(t.map((this.options.useSRVRecords?this.resolveSrv:this.dnsLookup).bind(this))),r=(0,f.zipMap)(t,s);return e.map(e=>{let t=r.get(e.host);return t?this.options.useSRVRecords?Object.assign({},e,t):Object.assign({},e,{host:t}):e})}createScanStream(e,{key:t,options:s={}}){return new u.default({objectMode:!0,key:t,redis:this,command:e,...s})}}(0,p.default)(E,i.EventEmitter),(0,h.addTransactionSupport)(E.prototype),t.default=E},99228:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getConnectionName=t.weightSrvRecords=t.groupSrvRecords=t.getUniqueHostnamesFromOptions=t.normalizeNodeOptions=t.nodeKeyToRedisOptions=t.getNodeKey=void 0;let r=s(44945),i=s(91645);t.getNodeKey=function(e){return e.port=e.port||6379,e.host=e.host||"127.0.0.1",e.host+":"+e.port},t.nodeKeyToRedisOptions=function(e){let t=e.lastIndexOf(":");if(-1===t)throw Error(`Invalid node key ${e}`);return{host:e.slice(0,t),port:Number(e.slice(t+1))}},t.normalizeNodeOptions=function(e){return e.map(e=>{let t={};if("object"==typeof e)Object.assign(t,e);else if("string"==typeof e)Object.assign(t,(0,r.parseURL)(e));else if("number"==typeof e)t.port=e;else throw Error("Invalid argument "+e);return"string"==typeof t.port&&(t.port=parseInt(t.port,10)),delete t.db,t.port||(t.port=6379),t.host||(t.host="127.0.0.1"),(0,r.resolveTLSProfile)(t)})},t.getUniqueHostnamesFromOptions=function(e){let t={};return e.forEach(e=>{t[e.host]=!0}),Object.keys(t).filter(e=>!(0,i.isIP)(e))},t.groupSrvRecords=function(e){let t={};for(let s of e)t.hasOwnProperty(s.priority)?(t[s.priority].totalWeight+=s.weight,t[s.priority].records.push(s)):t[s.priority]={totalWeight:s.weight,records:[s]};return t},t.weightSrvRecords=function(e){if(1===e.records.length)return e.totalWeight=0,e.records.shift();let t=Math.floor(Math.random()*(e.totalWeight+e.records.length)),s=0;for(let[r,i]of e.records.entries())if((s+=1+i.weight)>t)return e.totalWeight-=i.weight,e.records.splice(r,1),i},t.getConnectionName=function(e,t){let s=`ioredis-cluster(${e})`;return t?`${s}:${t}`:s}},49893:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=(0,s(44945).Debug)("AbstractConnector");class i{constructor(e){this.connecting=!1,this.disconnectTimeout=e}check(e){return!0}disconnect(){if(this.connecting=!1,this.stream){let e=this.stream,t=setTimeout(()=>{r("stream %s:%s still open, destroying it",e.remoteAddress,e.remotePort),e.destroy()},this.disconnectTimeout);e.on("close",()=>clearTimeout(t)),e.end()}}}t.default=i},31518:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FailoverDetector=void 0;let r=(0,s(44945).Debug)("FailoverDetector"),i="+switch-master";class n{constructor(e,t){this.isDisconnected=!1,this.connector=e,this.sentinels=t}cleanup(){for(let e of(this.isDisconnected=!0,this.sentinels))e.client.disconnect()}async subscribe(){r("Starting FailoverDetector");let e=[];for(let t of this.sentinels){let s=t.client.subscribe(i).catch(e=>{r("Failed to subscribe to failover messages on sentinel %s:%s (%s)",t.address.host||"127.0.0.1",t.address.port||26739,e.message)});e.push(s),t.client.on("message",e=>{this.isDisconnected||e!==i||this.disconnect()})}await Promise.all(e)}disconnect(){this.isDisconnected=!0,r("Failover detected, disconnecting"),this.connector.disconnect()}}t.FailoverDetector=n},70896:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class s{constructor(e){this.cursor=0,this.sentinels=e.slice(0)}next(){let e=this.cursor>=this.sentinels.length;return{done:e,value:e?void 0:this.sentinels[this.cursor++]}}reset(e){e&&this.sentinels.length>1&&1!==this.cursor&&this.sentinels.unshift(...this.sentinels.splice(this.cursor-1)),this.cursor=0}add(e){for(let s=0;s<this.sentinels.length;s++){var t;if(t=this.sentinels[s],(e.host||"127.0.0.1")===(t.host||"127.0.0.1")&&(e.port||26379)===(t.port||26379))return!1}return this.sentinels.push(e),!0}toString(){return`${JSON.stringify(this.sentinels)} @${this.cursor}`}}t.default=s},90564:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SentinelIterator=void 0;let r=s(91645),i=s(44945),n=s(34631),o=s(70896);t.SentinelIterator=o.default;let a=s(49893),l=s(14276),c=s(31518),u=(0,i.Debug)("SentinelConnector");class h extends a.default{constructor(e){if(super(e.disconnectTimeout),this.options=e,this.emitter=null,this.failoverDetector=null,!this.options.sentinels.length)throw Error("Requires at least one sentinel to connect to.");if(!this.options.name)throw Error("Requires the name of master.");this.sentinelIterator=new o.default(this.options.sentinels)}check(e){let t=!e.role||this.options.role===e.role;return t||(u("role invalid, expected %s, but got %s",this.options.role,e.role),this.sentinelIterator.next(),this.sentinelIterator.next(),this.sentinelIterator.reset(!0)),t}disconnect(){super.disconnect(),this.failoverDetector&&this.failoverDetector.cleanup()}connect(e){let t;this.connecting=!0,this.retryAttempts=0;let s=async()=>{let o=this.sentinelIterator.next();if(o.done){this.sentinelIterator.reset(!1);let r="function"==typeof this.options.sentinelRetryStrategy?this.options.sentinelRetryStrategy(++this.retryAttempts):null,i="number"!=typeof r?"All sentinels are unreachable and retry is disabled.":`All sentinels are unreachable. Retrying from scratch after ${r}ms.`;t&&(i+=` Last error: ${t.message}`),u(i);let n=Error(i);if("number"==typeof r)return e("error",n),await new Promise(e=>setTimeout(e,r)),s();throw n}let a=null,l=null;try{a=await this.resolve(o.value)}catch(e){l=e}if(!this.connecting)throw Error(i.CONNECTION_CLOSED_ERROR_MSG);let c=o.value.host+":"+o.value.port;if(a)return u("resolved: %s:%s from sentinel %s",a.host,a.port,c),this.options.enableTLSForSentinelMode&&this.options.tls?(Object.assign(a,this.options.tls),this.stream=(0,n.connect)(a),this.stream.once("secureConnect",this.initFailoverDetector.bind(this))):(this.stream=(0,r.createConnection)(a),this.stream.once("connect",this.initFailoverDetector.bind(this))),this.stream.once("error",e=>{this.firstError=e}),this.stream;{let r=l?"failed to connect to sentinel "+c+" because "+l.message:"connected to sentinel "+c+" successfully, but got an invalid reply: "+a;return u(r),e("sentinelError",Error(r)),l&&(t=l),s()}};return s()}async updateSentinels(e){if(!this.options.updateSentinels)return;let t=await e.sentinel("sentinels",this.options.name);Array.isArray(t)&&(t.map(i.packObject).forEach(e=>{if(-1===(e.flags?e.flags.split(","):[]).indexOf("disconnected")&&e.ip&&e.port){let t=this.sentinelNatResolve(f(e));this.sentinelIterator.add(t)&&u("adding sentinel %s:%s",t.host,t.port)}}),u("Updated internal sentinels: %s",this.sentinelIterator))}async resolveMaster(e){let t=await e.sentinel("get-master-addr-by-name",this.options.name);return await this.updateSentinels(e),this.sentinelNatResolve(Array.isArray(t)?{host:t[0],port:Number(t[1])}:null)}async resolveSlave(e){let t=await e.sentinel("slaves",this.options.name);if(!Array.isArray(t))return null;let s=t.map(i.packObject).filter(e=>e.flags&&!e.flags.match(/(disconnected|s_down|o_down)/));return this.sentinelNatResolve(function(e,t){let s;if(0===e.length)return null;if("function"==typeof t)s=t(e);else if(null!==t&&"object"==typeof t){let r=Array.isArray(t)?t:[t];r.sort((e,t)=>(e.prio||(e.prio=1),t.prio||(t.prio=1),e.prio<t.prio)?-1:e.prio>t.prio?1:0);for(let t=0;t<r.length;t++){for(let i=0;i<e.length;i++){let n=e[i];if(n.ip===r[t].ip&&n.port===r[t].port){s=n;break}}if(s)break}}return s||(s=(0,i.sample)(e)),f(s)}(s,this.options.preferredSlaves))}sentinelNatResolve(e){if(!e||!this.options.natMap)return e;let t=`${e.host}:${e.port}`,s=e;return"function"==typeof this.options.natMap?s=this.options.natMap(t)||e:"object"==typeof this.options.natMap&&(s=this.options.natMap[t]||e),s}connectToSentinel(e,t){return new l.default({port:e.port||26379,host:e.host,username:this.options.sentinelUsername||null,password:this.options.sentinelPassword||null,family:e.family||("path"in this.options&&this.options.path?void 0:this.options.family),tls:this.options.sentinelTLS,retryStrategy:null,enableReadyCheck:!1,connectTimeout:this.options.connectTimeout,commandTimeout:this.options.sentinelCommandTimeout,...t})}async resolve(e){let t=this.connectToSentinel(e);t.on("error",p);try{if("slave"===this.options.role)return await this.resolveSlave(t);return await this.resolveMaster(t)}finally{t.disconnect()}}async initFailoverDetector(){var e;if(!this.options.failoverDetector)return;this.sentinelIterator.reset(!0);let t=[];for(;t.length<this.options.sentinelMaxConnections;){let{done:e,value:s}=this.sentinelIterator.next();if(e)break;let r=this.connectToSentinel(s,{lazyConnect:!0,retryStrategy:this.options.sentinelReconnectStrategy});r.on("reconnecting",()=>{var e;null===(e=this.emitter)||void 0===e||e.emit("sentinelReconnecting")}),t.push({address:s,client:r})}this.sentinelIterator.reset(!1),this.failoverDetector&&this.failoverDetector.cleanup(),this.failoverDetector=new c.FailoverDetector(this,t),await this.failoverDetector.subscribe(),null===(e=this.emitter)||void 0===e||e.emit("failoverSubscribed")}}function f(e){return{host:e.ip,port:Number(e.port)}}function p(){}t.default=h},33610:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(91645),i=s(34631),n=s(44945),o=s(49893);class a extends o.default{constructor(e){super(e.disconnectTimeout),this.options=e}connect(e){let t;let{options:s}=this;return this.connecting=!0,"path"in s&&s.path?t={path:s.path}:(t={},"port"in s&&null!=s.port&&(t.port=s.port),"host"in s&&null!=s.host&&(t.host=s.host),"family"in s&&null!=s.family&&(t.family=s.family)),s.tls&&Object.assign(t,s.tls),new Promise((e,o)=>{process.nextTick(()=>{if(!this.connecting){o(Error(n.CONNECTION_CLOSED_ERROR_MSG));return}try{s.tls?this.stream=(0,i.connect)(t):this.stream=(0,r.createConnection)(t)}catch(e){o(e);return}this.stream.once("error",e=>{this.firstError=e}),e(this.stream)})})}}t.default=a},34980:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SentinelConnector=t.StandaloneConnector=void 0;let r=s(33610);t.StandaloneConnector=r.default;let i=s(90564);t.SentinelConnector=i.default},47050:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let s=`-----BEGIN CERTIFICATE-----
MIIDTzCCAjegAwIBAgIJAKSVpiDswLcwMA0GCSqGSIb3DQEBBQUAMD4xFjAUBgNV
BAoMDUdhcmFudGlhIERhdGExJDAiBgNVBAMMG1NTTCBDZXJ0aWZpY2F0aW9uIEF1
dGhvcml0eTAeFw0xMzEwMDExMjE0NTVaFw0yMzA5MjkxMjE0NTVaMD4xFjAUBgNV
BAoMDUdhcmFudGlhIERhdGExJDAiBgNVBAMMG1NTTCBDZXJ0aWZpY2F0aW9uIEF1
dGhvcml0eTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALZqkh/DczWP
JnxnHLQ7QL0T4B4CDKWBKCcisriGbA6ZePWVNo4hfKQC6JrzfR+081NeD6VcWUiz
rmd+jtPhIY4c+WVQYm5PKaN6DT1imYdxQw7aqO5j2KUCEh/cznpLxeSHoTxlR34E
QwF28Wl3eg2vc5ct8LjU3eozWVk3gb7alx9mSA2SgmuX5lEQawl++rSjsBStemY2
BDwOpAMXIrdEyP/cVn8mkvi/BDs5M5G+09j0gfhyCzRWMQ7Hn71u1eolRxwVxgi3
TMn+/vTaFSqxKjgck6zuAYjBRPaHe7qLxHNr1So/Mc9nPy+3wHebFwbIcnUojwbp
4nctkWbjb2cCAwEAAaNQME4wHQYDVR0OBBYEFP1whtcrydmW3ZJeuSoKZIKjze3w
MB8GA1UdIwQYMBaAFP1whtcrydmW3ZJeuSoKZIKjze3wMAwGA1UdEwQFMAMBAf8w
DQYJKoZIhvcNAQEFBQADggEBAG2erXhwRAa7+ZOBs0B6X57Hwyd1R4kfmXcs0rta
lbPpvgULSiB+TCbf3EbhJnHGyvdCY1tvlffLjdA7HJ0PCOn+YYLBA0pTU/dyvrN6
Su8NuS5yubnt9mb13nDGYo1rnt0YRfxN+8DM3fXIVr038A30UlPX2Ou1ExFJT0MZ
uFKY6ZvLdI6/1cbgmguMlAhM+DhKyV6Sr5699LM3zqeI816pZmlREETYkGr91q7k
BpXJu/dtHaGxg1ZGu6w/PCsYGUcECWENYD4VQPd8N32JjOfu6vEgoEAwfPP+3oGp
Z4m3ewACcWOAenqflb+cQYC4PsF7qbXDmRaWrbKntOlZ3n0=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFujCCA6KgAwIBAgIJAJ1aTT1lu2ScMA0GCSqGSIb3DQEBCwUAMGoxCzAJBgNV
BAYTAlVTMQswCQYDVQQIDAJDQTELMAkGA1UEBwwCQ0ExEjAQBgNVBAoMCVJlZGlz
TGFiczEtMCsGA1UEAwwkUmVkaXNMYWJzIFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9y
aXR5MB4XDTE4MDIyNTE1MjA0MloXDTM4MDIyMDE1MjA0MlowajELMAkGA1UEBhMC
VVMxCzAJBgNVBAgMAkNBMQswCQYDVQQHDAJDQTESMBAGA1UECgwJUmVkaXNMYWJz
MS0wKwYDVQQDDCRSZWRpc0xhYnMgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkw
ggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDLEjXy7YrbN5Waau5cd6g1
G5C2tMmeTpZ0duFAPxNU4oE3RHS5gGiok346fUXuUxbZ6QkuzeN2/2Z+RmRcJhQY
Dm0ZgdG4x59An1TJfnzKKoWj8ISmoHS/TGNBdFzXV7FYNLBuqZouqePI6ReC6Qhl
pp45huV32Q3a6IDrrvx7Wo5ZczEQeFNbCeCOQYNDdTmCyEkHqc2AGo8eoIlSTutT
ULOC7R5gzJVTS0e1hesQ7jmqHjbO+VQS1NAL4/5K6cuTEqUl+XhVhPdLWBXJQ5ag
54qhX4v+ojLzeU1R/Vc6NjMvVtptWY6JihpgplprN0Yh2556ewcXMeturcKgXfGJ
xeYzsjzXerEjrVocX5V8BNrg64NlifzTMKNOOv4fVZszq1SIHR8F9ROrqiOdh8iC
JpUbLpXH9hWCSEO6VRMB2xJoKu3cgl63kF30s77x7wLFMEHiwsQRKxooE1UhgS9K
2sO4TlQ1eWUvFvHSTVDQDlGQ6zu4qjbOpb3Q8bQwoK+ai2alkXVR4Ltxe9QlgYK3
StsnPhruzZGA0wbXdpw0bnM+YdlEm5ffSTpNIfgHeaa7Dtb801FtA71ZlH7A6TaI
SIQuUST9EKmv7xrJyx0W1pGoPOLw5T029aTjnICSLdtV9bLwysrLhIYG5bnPq78B
cS+jZHFGzD7PUVGQD01nOQIDAQABo2MwYTAdBgNVHQ4EFgQUIuNsB+oTOC9rLoxL
yzNP7vJUV08wHwYDVR0jBBgwFoAUIuNsB+oTOC9rLoxLyzNP7vJUV08wDwYDVR0T
AQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAYYwDQYJKoZIhvcNAQELBQADggIBAHfg
z5pMNUAKdMzK1aS1EDdK9yKz4qicILz5czSLj1mC7HKDRy8cVADUxEICis++CsCu
rYOvyCVergHQLREcxPq4rc5Nq1uj6J6649NEeh4WazOOjL4ZfQ1jVznMbGy+fJm3
3Hoelv6jWRG9iqeJZja7/1s6YC6bWymI/OY1e4wUKeNHAo+Vger7MlHV+RuabaX+
hSJ8bJAM59NCM7AgMTQpJCncrcdLeceYniGy5Q/qt2b5mJkQVkIdy4TPGGB+AXDJ
D0q3I/JDRkDUFNFdeW0js7fHdsvCR7O3tJy5zIgEV/o/BCkmJVtuwPYOrw/yOlKj
TY/U7ATAx9VFF6/vYEOMYSmrZlFX+98L6nJtwDqfLB5VTltqZ4H/KBxGE3IRSt9l
FXy40U+LnXzhhW+7VBAvyYX8GEXhHkKU8Gqk1xitrqfBXY74xKgyUSTolFSfFVgj
mcM/X4K45bka+qpkj7Kfv/8D4j6aZekwhN2ly6hhC1SmQ8qjMjpG/mrWOSSHZFmf
ybu9iD2AYHeIOkshIl6xYIa++Q/00/vs46IzAbQyriOi0XxlSMMVtPx0Q3isp+ji
n8Mq9eOuxYOEQ4of8twUkUDd528iwGtEdwf0Q01UyT84S62N8AySl1ZBKXJz6W4F
UhWfa/HQYOAPDdEjNgnVwLI23b8t0TozyCWw7q8h
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEjzCCA3egAwIBAgIQe55B/ALCKJDZtdNT8kD6hTANBgkqhkiG9w0BAQsFADBM
MSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xv
YmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0yMjAxMjYxMjAwMDBaFw0y
NTAxMjYwMDAwMDBaMFgxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWdu
IG52LXNhMS4wLAYDVQQDEyVHbG9iYWxTaWduIEF0bGFzIFIzIE9WIFRMUyBDQSAy
MDIyIFEyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmGmg1LW9b7Lf
8zDD83yBDTEkt+FOxKJZqF4veWc5KZsQj9HfnUS2e5nj/E+JImlGPsQuoiosLuXD
BVBNAMcUFa11buFMGMeEMwiTmCXoXRrXQmH0qjpOfKgYc5gHG3BsRGaRrf7VR4eg
ofNMG9wUBw4/g/TT7+bQJdA4NfE7Y4d5gEryZiBGB/swaX6Jp/8MF4TgUmOWmalK
dZCKyb4sPGQFRTtElk67F7vU+wdGcrcOx1tDcIB0ncjLPMnaFicagl+daWGsKqTh
counQb6QJtYHa91KvCfKWocMxQ7OIbB5UARLPmC4CJ1/f8YFm35ebfzAeULYdGXu
jE9CLor0OwIDAQABo4IBXzCCAVswDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQG
CCsGAQUFBwMBBggrBgEFBQcDAjASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQW
BBSH5Zq7a7B/t95GfJWkDBpA8HHqdjAfBgNVHSMEGDAWgBSP8Et/qC5FJK5NUPpj
move4t0bvDB7BggrBgEFBQcBAQRvMG0wLgYIKwYBBQUHMAGGImh0dHA6Ly9vY3Nw
Mi5nbG9iYWxzaWduLmNvbS9yb290cjMwOwYIKwYBBQUHMAKGL2h0dHA6Ly9zZWN1
cmUuZ2xvYmFsc2lnbi5jb20vY2FjZXJ0L3Jvb3QtcjMuY3J0MDYGA1UdHwQvMC0w
K6ApoCeGJWh0dHA6Ly9jcmwuZ2xvYmFsc2lnbi5jb20vcm9vdC1yMy5jcmwwIQYD
VR0gBBowGDAIBgZngQwBAgIwDAYKKwYBBAGgMgoBAjANBgkqhkiG9w0BAQsFAAOC
AQEAKRic9/f+nmhQU/wz04APZLjgG5OgsuUOyUEZjKVhNGDwxGTvKhyXGGAMW2B/
3bRi+aElpXwoxu3pL6fkElbX3B0BeS5LoDtxkyiVEBMZ8m+sXbocwlPyxrPbX6mY
0rVIvnuUeBH8X0L5IwfpNVvKnBIilTbcebfHyXkPezGwz7E1yhUULjJFm2bt0SdX
y+4X/WeiiYIv+fTVgZZgl+/2MKIsu/qdBJc3f3TvJ8nz+Eax1zgZmww+RSQWeOj3
15Iw6Z5FX+NwzY/Ab+9PosR5UosSeq+9HhtaxZttXG1nVh+avYPGYddWmiMT90J5
ZgKnO/Fx2hBgTxhOTMYaD312kg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----`;t.default={RedisCloudFixed:{ca:s},RedisCloudFlexible:{ca:s}}},78149:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(90560);class i extends r.RedisError{constructor(e,t){super(e),this.lastNodeError=t,Error.captureStackTrace(this,this.constructor)}get name(){return this.constructor.name}}t.default=i,i.defaultMessage="Failed to refresh slots cache."},88229:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(90560);class i extends r.AbortError{constructor(e){super(`Reached the max retries per request limit (which is ${e}). Refer to "maxRetriesPerRequest" option for details.`),Error.captureStackTrace(this,this.constructor)}get name(){return this.constructor.name}}t.default=i},6195:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MaxRetriesPerRequestError=void 0;let r=s(88229);t.MaxRetriesPerRequestError=r.default},50547:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.print=t.ReplyError=t.SentinelIterator=t.SentinelConnector=t.AbstractConnector=t.Pipeline=t.ScanStream=t.Command=t.Cluster=t.Redis=t.default=void 0,t=e.exports=s(14276).default;var r=s(14276);Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r.default}});var i=s(14276);Object.defineProperty(t,"Redis",{enumerable:!0,get:function(){return i.default}});var n=s(3198);Object.defineProperty(t,"Cluster",{enumerable:!0,get:function(){return n.default}});var o=s(68058);Object.defineProperty(t,"Command",{enumerable:!0,get:function(){return o.default}});var a=s(34498);Object.defineProperty(t,"ScanStream",{enumerable:!0,get:function(){return a.default}});var l=s(41221);Object.defineProperty(t,"Pipeline",{enumerable:!0,get:function(){return l.default}});var c=s(49893);Object.defineProperty(t,"AbstractConnector",{enumerable:!0,get:function(){return c.default}});var u=s(90564);Object.defineProperty(t,"SentinelConnector",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"SentinelIterator",{enumerable:!0,get:function(){return u.SentinelIterator}}),t.ReplyError=s(90560).ReplyError,Object.defineProperty(t,"Promise",{get:()=>(console.warn("ioredis v5 does not support plugging third-party Promise library anymore. Native Promise will be used."),Promise),set(e){console.warn("ioredis v5 does not support plugging third-party Promise library anymore. Native Promise will be used.")}}),t.print=function(e,t){e?console.log("Error: "+e):console.log("Reply: "+t)}},85220:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_REDIS_OPTIONS=void 0,t.DEFAULT_REDIS_OPTIONS={port:6379,host:"localhost",family:4,connectTimeout:1e4,disconnectTimeout:2e3,retryStrategy:function(e){return Math.min(50*e,2e3)},keepAlive:0,noDelay:!0,connectionName:null,sentinels:null,name:null,role:"master",sentinelRetryStrategy:function(e){return Math.min(10*e,1e3)},sentinelReconnectStrategy:function(){return 6e4},natMap:null,enableTLSForSentinelMode:!1,updateSentinels:!0,failoverDetector:!1,username:null,password:null,db:0,enableOfflineQueue:!0,enableReadyCheck:!0,autoResubscribe:!0,autoResendUnfulfilledCommands:!0,lazyConnect:!1,keyPrefix:"",reconnectOnError:null,readOnly:!1,stringNumbers:!1,maxRetriesPerRequest:20,maxLoadingRetryTime:1e4,enableAutoPipelining:!1,autoPipeliningIgnoredCommands:[],sentinelMaxConnections:10}},5632:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.readyHandler=t.errorHandler=t.closeHandler=t.connectHandler=void 0;let r=s(90560),i=s(68058),n=s(6195),o=s(44945),a=s(30059),l=(0,o.Debug)("connection");function c(e){let t=new r.AbortError("Command aborted due to connection close");return t.command={name:e.name,args:e.args},t}t.connectHandler=function(e){return function(){e.setStatus("connect"),e.resetCommandQueue();let s=!1,{connectionEpoch:r}=e;e.condition.auth&&e.auth(e.condition.auth,function(t){r===e.connectionEpoch&&t&&(-1!==t.message.indexOf("no password is set")?console.warn("[WARN] Redis server does not require a password, but a password was supplied."):-1!==t.message.indexOf("without any password configured for the default user")?console.warn("[WARN] This Redis server's `default` user does not require a password, but a password was supplied"):-1!==t.message.indexOf("wrong number of arguments for 'auth' command")?console.warn("[ERROR] The server returned \"wrong number of arguments for 'auth' command\". You are probably passing both username and password to Redis version 5 or below. You should only pass the 'password' option for Redis version 5 and under."):(s=!0,e.recoverFromFatalError(t,t)))}),e.condition.select&&e.select(e.condition.select).catch(t=>{e.silentEmit("error",t)}),e.options.enableReadyCheck||t.readyHandler(e)(),new a.default(e,{stringNumbers:e.options.stringNumbers}),e.options.enableReadyCheck&&e._readyCheck(function(i,n){r===e.connectionEpoch&&(i?s||e.recoverFromFatalError(Error("Ready check failed: "+i.message),i):e.connector.check(n)?t.readyHandler(e)():e.disconnect(!0))})}},t.closeHandler=function(e){return function(){let s=e.status;if(e.setStatus("close"),e.commandQueue.length&&function(e){var t;let s=0;for(let r=0;r<e.length;){let i=null===(t=e.peekAt(r))||void 0===t?void 0:t.command,n=i.pipelineIndex;if((void 0===n||0===n)&&(s=0),void 0!==n&&n!==s++){e.remove(r,1),i.reject(c(i));continue}r++}}(e.commandQueue),e.offlineQueue.length&&function(e){var t;for(let s=0;s<e.length;){let r=null===(t=e.peekAt(s))||void 0===t?void 0:t.command;if("multi"===r.name)break;if("exec"===r.name){e.remove(s,1),r.reject(c(r));break}r.inTransaction?(e.remove(s,1),r.reject(c(r))):s++}}(e.offlineQueue),"ready"===s&&(e.prevCondition||(e.prevCondition=e.condition),e.commandQueue.length&&(e.prevCommandQueue=e.commandQueue)),e.manuallyClosing)return e.manuallyClosing=!1,l("skip reconnecting since the connection is manually closed."),t();if("function"!=typeof e.options.retryStrategy)return l("skip reconnecting because `retryStrategy` is not a function"),t();let r=e.options.retryStrategy(++e.retryAttempts);if("number"!=typeof r)return l("skip reconnecting because `retryStrategy` doesn't return a number"),t();l("reconnect in %sms",r),e.setStatus("reconnecting",r),e.reconnectTimeout=setTimeout(function(){e.reconnectTimeout=null,e.connect().catch(o.noop)},r);let{maxRetriesPerRequest:i}=e.options;"number"==typeof i&&(i<0?l("maxRetriesPerRequest is negative, ignoring..."):0==e.retryAttempts%(i+1)&&(l("reach maxRetriesPerRequest limitation, flushing command queue..."),e.flushQueue(new n.MaxRetriesPerRequestError(i))))};function t(){e.setStatus("end"),e.flushQueue(Error(o.CONNECTION_CLOSED_ERROR_MSG))}},t.errorHandler=function(e){return function(t){l("error: %s",t),e.silentEmit("error",t)}},t.readyHandler=function(e){return function(){if(e.setStatus("ready"),e.retryAttempts=0,e.options.monitor){e.call("monitor").then(()=>e.setStatus("monitoring"),t=>e.emit("error",t));let{sendCommand:t}=e;e.sendCommand=function(s){return i.default.checkFlag("VALID_IN_MONITOR_MODE",s.name)?t.call(e,s):(s.reject(Error("Connection is in monitoring mode, can't process commands.")),s.promise)},e.once("close",function(){delete e.sendCommand});return}let t=e.prevCondition?e.prevCondition.select:e.condition.select;if(e.options.connectionName&&(l("set the connection name [%s]",e.options.connectionName),e.client("setname",e.options.connectionName).catch(o.noop)),e.options.readOnly&&(l("set the connection to readonly mode"),e.readonly().catch(o.noop)),e.prevCondition){let s=e.prevCondition;if(e.prevCondition=null,s.subscriber&&e.options.autoResubscribe){e.condition.select!==t&&(l("connect to db [%d]",t),e.select(t));let r=s.subscriber.channels("subscribe");r.length&&(l("subscribe %d channels",r.length),e.subscribe(r));let i=s.subscriber.channels("psubscribe");i.length&&(l("psubscribe %d channels",i.length),e.psubscribe(i));let n=s.subscriber.channels("ssubscribe");n.length&&(l("ssubscribe %d channels",n.length),e.ssubscribe(n))}}if(e.prevCommandQueue){if(e.options.autoResendUnfulfilledCommands)for(l("resend %d unfulfilled commands",e.prevCommandQueue.length);e.prevCommandQueue.length>0;){let t=e.prevCommandQueue.shift();t.select!==e.condition.select&&"select"!==t.command.name&&e.select(t.select),e.sendCommand(t.command,t.stream)}else e.prevCommandQueue=null}if(e.offlineQueue.length){l("send %d commands in offline queue",e.offlineQueue.length);let t=e.offlineQueue;for(e.resetOfflineQueue();t.length>0;){let s=t.shift();s.select!==e.condition.select&&"select"!==s.command.name&&e.select(s.select),e.sendCommand(s.command,s.stream)}}e.condition.select!==t&&(l("connect to db [%d]",t),e.select(t))}}},45455:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addTransactionSupport=void 0;let r=s(44945),i=s(84542),n=s(41221);t.addTransactionSupport=function(e){e.pipeline=function(e){let t=new n.default(this);return Array.isArray(e)&&t.addBatch(e),t};let{multi:t}=e;e.multi=function(e,s){if(void 0!==s||Array.isArray(e)||(s=e,e=null),s&&!1===s.pipeline)return t.call(this);let o=new n.default(this);o.multi(),Array.isArray(e)&&o.addBatch(e);let a=o.exec;o.exec=function(e){if(this.isCluster&&!this.redis.slots.length)return"wait"===this.redis.status&&this.redis.connect().catch(r.noop),(0,i.default)(new Promise((e,t)=>{this.redis.delayUntilReady(s=>{if(s){t(s);return}this.exec(o).then(e,t)})}),e);if(this._transactions>0&&a.call(o),this.nodeifiedPromise)return a.call(o);let t=a.call(o);return(0,i.default)(t.then(function(e){let t=e[e.length-1];if(void 0===t)throw Error("Pipeline cannot be used to send any commands when the `exec()` has been called on it.");if(t[0]){t[0].previousErrors=[];for(let s=0;s<e.length-1;++s)e[s][0]&&t[0].previousErrors.push(e[s][0]);throw t[0]}return(0,r.wrapMultiResult)(t[1])}),e)};let{execBuffer:l}=o;return o.execBuffer=function(e){return this._transactions>0&&l.call(o),o.exec(e)},o};let{exec:s}=e;e.exec=function(e){return(0,i.default)(s.call(this).then(function(e){return Array.isArray(e)&&(e=(0,r.wrapMultiResult)(e)),e}),e)}}},31459:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(60670),i=s(41497),n=s(68058),o=s(54924);class a{constructor(){this.options={},this.scriptsSet={},this.addedBuiltinSet=new Set}getBuiltinCommands(){return l.slice(0)}createBuiltinCommand(e){return{string:c(null,e,"utf8"),buffer:c(null,e,null)}}addBuiltinCommand(e){this.addedBuiltinSet.add(e),this[e]=c(e,e,"utf8"),this[e+"Buffer"]=c(e+"Buffer",e,null)}defineCommand(e,t){let s=new o.default(t.lua,t.numberOfKeys,this.options.keyPrefix,t.readOnly);this.scriptsSet[e]=s,this[e]=u(e,e,s,"utf8"),this[e+"Buffer"]=u(e+"Buffer",e,s,null)}sendCommand(e,t,s){throw Error('"sendCommand" is not implemented')}}let l=r.list.filter(e=>"monitor"!==e);function c(e,t,s){return void 0===s&&(s=t,t=null),function(...r){let o=t||r.shift(),a=r[r.length-1];"function"==typeof a?r.pop():a=void 0;let l={errorStack:this.options.showFriendlyErrorStack?Error():void 0,keyPrefix:this.options.keyPrefix,replyEncoding:s};return(0,i.shouldUseAutoPipelining)(this,e,o)?(0,i.executeWithAutoPipelining)(this,e,o,r,a):this.sendCommand(new n.default(o,r,l,a))}}function u(e,t,s,r){return function(...n){let o="function"==typeof n[n.length-1]?n.pop():void 0,a={replyEncoding:r};return(this.options.showFriendlyErrorStack&&(a.errorStack=Error()),(0,i.shouldUseAutoPipelining)(this,e,t))?(0,i.executeWithAutoPipelining)(this,e,t,n,o):s.execute(this,n,a,o)}}l.push("sentinel"),l.forEach(function(e){a.prototype[e]=c(e,e,"utf8"),a.prototype[e+"Buffer"]=c(e+"Buffer",e,null)}),a.prototype.call=c("call","utf8"),a.prototype.callBuffer=c("callBuffer",null),a.prototype.send_command=a.prototype.call,t.default=a},26992:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){Object.getOwnPropertyNames(t.prototype).forEach(s=>{Object.defineProperty(e.prototype,s,Object.getOwnPropertyDescriptor(t.prototype,s))})}},55666:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.genRedactedString=t.getStringValue=t.MAX_ARGUMENT_LENGTH=void 0;let r=s(23481);function i(e){if(null!==e)switch(typeof e){case"boolean":case"number":return;case"object":if(Buffer.isBuffer(e))return e.toString("hex");if(Array.isArray(e))return e.join(",");try{return JSON.stringify(e)}catch(e){return}case"string":return e}}function n(e,t){let{length:s}=e;return s<=t?e:e.slice(0,t)+' ... <REDACTED full-length="'+s+'">'}t.MAX_ARGUMENT_LENGTH=200,t.getStringValue=i,t.genRedactedString=n,t.default=function(e){let t=(0,r.default)(`ioredis:${e}`);function s(...e){if(t.enabled){for(let t=1;t<e.length;t++){let s=i(e[t]);"string"==typeof s&&s.length>200&&(e[t]=n(s,200))}return t.apply(null,e)}}return Object.defineProperties(s,{namespace:{get:()=>t.namespace},enabled:{get:()=>t.enabled},destroy:{get:()=>t.destroy},log:{get:()=>t.log,set(e){t.log=e}}}),s}},44945:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.noop=t.defaults=t.Debug=t.zipMap=t.CONNECTION_CLOSED_ERROR_MSG=t.shuffle=t.sample=t.resolveTLSProfile=t.parseURL=t.optimizeErrorStack=t.toArg=t.convertMapToArray=t.convertObjectToArray=t.timeout=t.packObject=t.isInt=t.wrapMultiResult=t.convertBufferToString=void 0;let r=s(79551),i=s(39704);Object.defineProperty(t,"defaults",{enumerable:!0,get:function(){return i.defaults}}),Object.defineProperty(t,"noop",{enumerable:!0,get:function(){return i.noop}});let n=s(55666);t.Debug=n.default;let o=s(47050);function a(e){let t=parseFloat(e);return!isNaN(e)&&(0|t)===t}t.convertBufferToString=function e(t,s){if(t instanceof Buffer)return t.toString(s);if(Array.isArray(t)){let r=t.length,i=Array(r);for(let n=0;n<r;++n)i[n]=t[n]instanceof Buffer&&"utf8"===s?t[n].toString():e(t[n],s);return i}return t},t.wrapMultiResult=function(e){if(!e)return null;let t=[],s=e.length;for(let r=0;r<s;++r){let s=e[r];s instanceof Error?t.push([s]):t.push([null,s])}return t},t.isInt=a,t.packObject=function(e){let t={},s=e.length;for(let r=1;r<s;r+=2)t[e[r-1]]=e[r];return t},t.timeout=function(e,t){let s=null,r=function(){s&&(clearTimeout(s),s=null,e.apply(this,arguments))};return s=setTimeout(r,t,Error("timeout")),r},t.convertObjectToArray=function(e){let t=[],s=Object.keys(e);for(let r=0,i=s.length;r<i;r++)t.push(s[r],e[s[r]]);return t},t.convertMapToArray=function(e){let t=[],s=0;return e.forEach(function(e,r){t[s]=r,t[s+1]=e,s+=2}),t},t.toArg=function(e){return null==e?"":String(e)},t.optimizeErrorStack=function(e,t,s){let r;let i=t.split("\n"),n="";for(r=1;r<i.length&&-1!==i[r].indexOf(s);++r);for(let e=r;e<i.length;++e)n+="\n"+i[e];if(e.stack){let t=e.stack.indexOf("\n");e.stack=e.stack.slice(0,t)+n}return e},t.parseURL=function(e){if(a(e))return{port:e};let t=(0,r.parse)(e,!0,!0);t.slashes||"/"===e[0]||(e="//"+e,t=(0,r.parse)(e,!0,!0));let s=t.query||{},n={};if(t.auth){let e=t.auth.indexOf(":");n.username=-1===e?t.auth:t.auth.slice(0,e),n.password=-1===e?"":t.auth.slice(e+1)}if(t.pathname&&("redis:"===t.protocol||"rediss:"===t.protocol?t.pathname.length>1&&(n.db=t.pathname.slice(1)):n.path=t.pathname),t.host&&(n.host=t.hostname),t.port&&(n.port=t.port),"string"==typeof s.family){let e=Number.parseInt(s.family,10);Number.isNaN(e)||(n.family=e)}return(0,i.defaults)(n,s),n},t.resolveTLSProfile=function(e){let t=null==e?void 0:e.tls;"string"==typeof t&&(t={profile:t});let s=o.default[null==t?void 0:t.profile];return s&&(t=Object.assign({},s,t),delete t.profile,e=Object.assign({},e,{tls:t})),e},t.sample=function(e,t=0){let s=e.length;return t>=s?null:e[t+Math.floor(Math.random()*(s-t))]},t.shuffle=function(e){let t=e.length;for(;t>0;){let s=Math.floor(Math.random()*t);t--,[e[t],e[s]]=[e[s],e[t]]}return e},t.CONNECTION_CLOSED_ERROR_MSG="Connection is closed.",t.zipMap=function(e,t){let s=new Map;return e.forEach((e,r)=>{s.set(e,t[r])}),s}},39704:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isArguments=t.defaults=t.noop=void 0;let r=s(7020);t.defaults=r;let i=s(16022);t.isArguments=i,t.noop=function(){}},7020:e=>{var t=/^(?:0|[1-9]\d*)$/;function s(e,t,s){switch(s.length){case 0:return e.call(t);case 1:return e.call(t,s[0]);case 2:return e.call(t,s[0],s[1]);case 3:return e.call(t,s[0],s[1],s[2])}return e.apply(t,s)}var r=Object.prototype,i=r.hasOwnProperty,n=r.toString,o=r.propertyIsEnumerable,a=Math.max;function l(e,t,s,n){return void 0===e||h(e,r[s])&&!i.call(n,s)?t:e}function c(e,t){return t=a(void 0===t?e.length-1:t,0),function(){for(var r=arguments,i=-1,n=a(r.length-t,0),o=Array(n);++i<n;)o[i]=r[t+i];i=-1;for(var l=Array(t+1);++i<t;)l[i]=r[i];return l[t]=o,s(e,this,l)}}function u(e,s){return!!(s=null==s?0x1fffffffffffff:s)&&("number"==typeof e||t.test(e))&&e>-1&&e%1==0&&e<s}function h(e,t){return e===t||e!=e&&t!=t}var f=Array.isArray;function p(e){var t,s;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=0x1fffffffffffff&&!("[object Function]"==(s=d(e)?n.call(e):"")||"[object GeneratorFunction]"==s)}function d(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}var y=function(e){return c(function(t,s){var r=-1,i=s.length,n=i>1?s[i-1]:void 0,o=i>2?s[2]:void 0;for(n=e.length>3&&"function"==typeof n?(i--,n):void 0,o&&function(e,t,s){if(!d(s))return!1;var r=typeof t;return("number"==r?!!(p(s)&&u(t,s.length)):"string"==r&&t in s)&&h(s[t],e)}(s[0],s[1],o)&&(n=i<3?void 0:n,i=1),t=Object(t);++r<i;){var a=s[r];a&&e(t,a,r,n)}return t})}(function(e,t,s,a){!function(e,t,s,r){s||(s={});for(var n=-1,o=t.length;++n<o;){var a=t[n],l=r?r(s[a],e[a],a,s,e):void 0;!function(e,t,s){var r=e[t];i.call(e,t)&&h(r,s)&&(void 0!==s||t in e)||(e[t]=s)}(s,a,void 0===l?e[a]:l)}}(t,p(t)?function(e,t){var s=f(e)||e&&"object"==typeof e&&p(e)&&i.call(e,"callee")&&(!o.call(e,"callee")||"[object Arguments]"==n.call(e))?function(e,t){for(var s=-1,r=Array(e);++s<e;)r[s]=t(s);return r}(e.length,String):[],r=s.length,a=!!r;for(var l in e)(t||i.call(e,l))&&!(a&&("length"==l||u(l,r)))&&s.push(l);return s}(t,!0):function(e){if(!d(e))return function(e){var t=[];if(null!=e)for(var s in Object(e))t.push(s);return t}(e);var t,s=(t=e&&e.constructor,e===("function"==typeof t&&t.prototype||r)),n=[];for(var o in e)"constructor"==o&&(s||!i.call(e,o))||n.push(o);return n}(t),e,a)}),m=c(function(e){return e.push(void 0,l),s(y,void 0,e)});e.exports=m},16022:e=>{var t=Object.prototype,s=t.hasOwnProperty,r=t.toString,i=t.propertyIsEnumerable;e.exports=function(e){var t,n,o,a,l,c;return!!e&&"object"==typeof e&&null!=(t=e)&&"number"==typeof(n=t.length)&&n>-1&&n%1==0&&n<=0x1fffffffffffff&&!("[object Function]"==(l=typeof(a=o=t),c=a&&("object"==l||"function"==l)?r.call(o):"")||"[object GeneratorFunction]"==c)&&s.call(e,"callee")&&(!i.call(e,"callee")||"[object Arguments]"==r.call(e))}},83337:e=>{function t(e,t,s,r){return Math.round(e/s)+" "+r+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var r,i,n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return 864e5*s;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*s;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*s;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}(e);if("number"===n&&isFinite(e))return s.long?(r=Math.abs(e))>=864e5?t(e,r,864e5,"day"):r>=36e5?t(e,r,36e5,"hour"):r>=6e4?t(e,r,6e4,"minute"):r>=1e3?t(e,r,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},90560:(e,t,s)=>{"use strict";let r=55>process.version.charCodeAt(1)&&46===process.version.charCodeAt(2)?s(11645):s(66769);e.exports=r},66769:(e,t,s)=>{"use strict";let r=s(12412);class i extends Error{get name(){return this.constructor.name}}class n extends i{constructor(e,t,s){r(t),r.strictEqual(typeof s,"number");let i=Error.stackTraceLimit;Error.stackTraceLimit=2,super(e),Error.stackTraceLimit=i,this.offset=s,this.buffer=t}get name(){return this.constructor.name}}class o extends i{constructor(e){let t=Error.stackTraceLimit;Error.stackTraceLimit=2,super(e),Error.stackTraceLimit=t}get name(){return this.constructor.name}}class a extends i{get name(){return this.constructor.name}}class l extends a{get name(){return this.constructor.name}}e.exports={RedisError:i,ParserError:n,ReplyError:o,AbortError:a,InterruptError:l}},11645:(e,t,s)=>{"use strict";let r=s(12412),i=s(28354);function n(e){Object.defineProperty(this,"message",{value:e||"",configurable:!0,writable:!0}),Error.captureStackTrace(this,this.constructor)}function o(e,t,s){r(t),r.strictEqual(typeof s,"number"),Object.defineProperty(this,"message",{value:e||"",configurable:!0,writable:!0});let i=Error.stackTraceLimit;Error.stackTraceLimit=2,Error.captureStackTrace(this,this.constructor),Error.stackTraceLimit=i,this.offset=s,this.buffer=t}function a(e){Object.defineProperty(this,"message",{value:e||"",configurable:!0,writable:!0});let t=Error.stackTraceLimit;Error.stackTraceLimit=2,Error.captureStackTrace(this,this.constructor),Error.stackTraceLimit=t}function l(e){Object.defineProperty(this,"message",{value:e||"",configurable:!0,writable:!0}),Error.captureStackTrace(this,this.constructor)}function c(e){Object.defineProperty(this,"message",{value:e||"",configurable:!0,writable:!0}),Error.captureStackTrace(this,this.constructor)}i.inherits(n,Error),Object.defineProperty(n.prototype,"name",{value:"RedisError",configurable:!0,writable:!0}),i.inherits(o,n),Object.defineProperty(o.prototype,"name",{value:"ParserError",configurable:!0,writable:!0}),i.inherits(a,n),Object.defineProperty(a.prototype,"name",{value:"ReplyError",configurable:!0,writable:!0}),i.inherits(l,n),Object.defineProperty(l.prototype,"name",{value:"AbortError",configurable:!0,writable:!0}),i.inherits(c,l),Object.defineProperty(c.prototype,"name",{value:"InterruptError",configurable:!0,writable:!0}),e.exports={RedisError:n,ParserError:o,ReplyError:a,AbortError:l,InterruptError:c}},79244:(e,t,s)=>{"use strict";e.exports=s(50549)},50549:(e,t,s)=>{"use strict";let r=s(79428).Buffer,i=new(s(41204)).StringDecoder,n=s(90560),o=n.ReplyError,a=n.ParserError;var l=r.allocUnsafe(32768),c=0,u=null,h=0,f=0;function p(e){let t=e.offset,s=e.buffer,r=s.length-1;for(var i=t;i<r;)if(13===s[i++]){if(e.offset=i+1,!0===e.optionReturnBuffers)return e.buffer.slice(t,i-1);return e.buffer.toString("utf8",t,i-1)}}function d(e){let t=e.buffer.length-1;for(var s=e.offset,r=0;s<t;){let t=e.buffer[s++];if(13===t)return e.offset=s+1,r;r=10*r+(t-48)}}function y(e,t,s){e.arrayCache.push(t),e.arrayPos.push(s)}function m(e){let t=e.arrayCache.pop();var s=e.arrayPos.pop();if(e.arrayCache.length){let r=m(e);if(void 0===r){y(e,t,s);return}t[s++]=r}return g(e,t,s)}function g(e,t,s){let r=e.buffer.length;for(;s<t.length;){let i=e.offset;if(e.offset>=r){y(e,t,s);return}let n=b(e,e.buffer[e.offset++]);if(void 0===n){e.arrayCache.length||e.bufferCache.length||(e.offset=i),y(e,t,s);return}t[s]=n,s++}return t}function b(e,t){switch(t){case 36:return function(e){let t=d(e);if(void 0===t)return;if(t<0)return null;let s=e.offset+t;if(s+2>e.buffer.length){e.bigStrSize=s+2,e.totalChunkSize=e.buffer.length,e.bufferCache.push(e.buffer);return}let r=e.offset;return(e.offset=s+2,!0===e.optionReturnBuffers)?e.buffer.slice(r,s):e.buffer.toString("utf8",r,s)}(e);case 43:return p(e);case 42:return function(e){let t=d(e);return void 0===t?void 0:t<0?null:g(e,Array(t),0)}(e);case 58:return!0===e.optionStringNumbers?function(e){let t=e.buffer.length-1;var s=e.offset,r=0,i="";for(45===e.buffer[s]&&(i+="-",s++);s<t;){var n=e.buffer[s++];if(13===n)return e.offset=s+1,0!==r&&(i+=r),i;r>0x19999998?(i+=10*r+(n-48),r=0):48===n&&0===r?i+=0:r=10*r+(n-48)}}(e):function(e){let t=e.buffer.length-1;var s=e.offset,r=0,i=1;for(45===e.buffer[s]&&(i=-1,s++);s<t;){let t=e.buffer[s++];if(13===t)return e.offset=s+1,i*r;r=10*r+(t-48)}}(e);case 45:return function(e){var t=p(e);if(void 0!==t)return!0===e.optionReturnBuffers&&(t=t.toString()),new o(t)}(e);default:return function(e,t){let s=new a("Protocol error, got "+JSON.stringify(String.fromCharCode(t))+" as reply type byte",JSON.stringify(e.buffer),e.offset);e.buffer=null,e.returnFatalError(s)}(e,t)}}function S(){if(l.length>51200){if(1===h||f>2*h){let e=Math.floor(l.length/10),t=e<c?c:e;c=0,l=l.slice(t,l.length)}else f++,h--}else clearInterval(u),h=0,f=0,u=null}class k{constructor(e){if(!e)throw TypeError("Options are mandatory.");if("function"!=typeof e.returnError||"function"!=typeof e.returnReply)throw TypeError("The returnReply and returnError options have to be functions.");this.setReturnBuffers(!!e.returnBuffers),this.setStringNumbers(!!e.stringNumbers),this.returnError=e.returnError,this.returnFatalError=e.returnFatalError||e.returnError,this.returnReply=e.returnReply,this.reset()}reset(){this.offset=0,this.buffer=null,this.bigStrSize=0,this.totalChunkSize=0,this.bufferCache=[],this.arrayCache=[],this.arrayPos=[]}setReturnBuffers(e){if("boolean"!=typeof e)throw TypeError("The returnBuffers argument has to be a boolean");this.optionReturnBuffers=e}setStringNumbers(e){if("boolean"!=typeof e)throw TypeError("The stringNumbers argument has to be a boolean");this.optionStringNumbers=e}execute(e){if(null===this.buffer)this.buffer=e,this.offset=0;else if(0===this.bigStrSize){let t=this.buffer.length,s=t-this.offset,i=r.allocUnsafe(s+e.length);if(this.buffer.copy(i,0,this.offset,t),e.copy(i,s,0,e.length),this.buffer=i,this.offset=0,this.arrayCache.length){let e=m(this);if(void 0===e)return;this.returnReply(e)}}else if(this.totalChunkSize+e.length>=this.bigStrSize){this.bufferCache.push(e);var t=this.optionReturnBuffers?function(e){let t=e.bufferCache,s=e.offset,i=e.bigStrSize-s-2;var n=t.length,o=e.bigStrSize-e.totalChunkSize;if(e.offset=o,o<=2){if(2===n)return t[0].slice(s,t[0].length+o-2);n--,o=t[t.length-2].length+o}l.length<i+c&&(c>0x6f00000&&(c=0x3200000),l=r.allocUnsafe(i*(i>0x4b00000?2:3)+c),c=0,h++,null===u&&(u=setInterval(S,50)));let a=c;t[0].copy(l,a,s,t[0].length),c+=t[0].length-s;for(var f=1;f<n-1;f++)t[f].copy(l,c),c+=t[f].length;return t[f].copy(l,c,0,o-2),c+=o-2,l.slice(a,c)}(this):function(e){let t=e.bufferCache,s=e.offset;var r=t.length,n=e.bigStrSize-e.totalChunkSize;if(e.offset=n,n<=2){if(2===r)return t[0].toString("utf8",s,t[0].length+n-2);r--,n=t[t.length-2].length+n}for(var o=i.write(t[0].slice(s)),a=1;a<r-1;a++)o+=i.write(t[a]);return o+i.end(t[a].slice(0,n-2))}(this);if(this.bigStrSize=0,this.bufferCache=[],this.buffer=e,this.arrayCache.length&&(this.arrayCache[0][this.arrayPos[0]++]=t,void 0===(t=m(this))))return;this.returnReply(t)}else{this.bufferCache.push(e),this.totalChunkSize+=e.length;return}for(;this.offset<this.buffer.length;){let e=this.offset,t=this.buffer[this.offset++],s=b(this,t);if(void 0===s){this.arrayCache.length||this.bufferCache.length||(this.offset=e);return}45===t?this.returnError(s):this.returnReply(s)}this.buffer=null}}e.exports=k},84542:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=s(11815);function i(e){setTimeout(function(){throw e},0)}t.default=function(e,t,s){return"function"==typeof t&&e.then(e=>{let n;(n=void 0!==s&&Object(s).spread&&Array.isArray(e)?r.tryCatch(t).apply(void 0,[null].concat(e)):void 0===e?r.tryCatch(t)(null):r.tryCatch(t)(null,e))===r.errorObj&&i(n.e)},e=>{if(!e){let t=Error(e+"");Object.assign(t,{cause:e}),e=t}let s=r.tryCatch(t)(e);s===r.errorObj&&i(s.e)}),e}},11815:(e,t)=>{"use strict";let s;function r(e,r){try{let e=s;return s=null,e.apply(this,arguments)}catch(e){return t.errorObj.e=e,t.errorObj}}Object.defineProperty(t,"__esModule",{value:!0}),t.tryCatch=t.errorObj=void 0,t.errorObj={e:{}},t.tryCatch=function(e){return s=e,r}},23203:(e,t,s)=>{"use strict";let r;let i=s(21820),n=s(83997),o=s(58684),{env:a}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function c(e,t){if(0===r)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===r)return 0;let s=r||0;if("dumb"===a.TERM)return s;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:s;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:s}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?r=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(r=1),"FORCE_COLOR"in a&&(r="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return l(c(e,e&&e.isTTY))},stdout:l(c(!0,n.isatty(1))),stderr:l(c(!0,n.isatty(2)))}},48957:e=>{"use strict";e.exports=JSON.parse('{"acl":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"append":{"arity":3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"asking":{"arity":1,"flags":["fast"],"keyStart":0,"keyStop":0,"step":0},"auth":{"arity":-2,"flags":["noscript","loading","stale","fast","no_auth","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"bgrewriteaof":{"arity":1,"flags":["admin","noscript","no_async_loading"],"keyStart":0,"keyStop":0,"step":0},"bgsave":{"arity":-1,"flags":["admin","noscript","no_async_loading"],"keyStart":0,"keyStop":0,"step":0},"bitcount":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"bitfield":{"arity":-2,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"bitfield_ro":{"arity":-2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"bitop":{"arity":-4,"flags":["write","denyoom"],"keyStart":2,"keyStop":-1,"step":1},"bitpos":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"blmove":{"arity":6,"flags":["write","denyoom","noscript","blocking"],"keyStart":1,"keyStop":2,"step":1},"blmpop":{"arity":-5,"flags":["write","blocking","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"blpop":{"arity":-3,"flags":["write","noscript","blocking"],"keyStart":1,"keyStop":-2,"step":1},"brpop":{"arity":-3,"flags":["write","noscript","blocking"],"keyStart":1,"keyStop":-2,"step":1},"brpoplpush":{"arity":4,"flags":["write","denyoom","noscript","blocking"],"keyStart":1,"keyStop":2,"step":1},"bzmpop":{"arity":-5,"flags":["write","blocking","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"bzpopmax":{"arity":-3,"flags":["write","noscript","blocking","fast"],"keyStart":1,"keyStop":-2,"step":1},"bzpopmin":{"arity":-3,"flags":["write","noscript","blocking","fast"],"keyStart":1,"keyStop":-2,"step":1},"client":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"cluster":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"command":{"arity":-1,"flags":["loading","stale"],"keyStart":0,"keyStop":0,"step":0},"config":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"copy":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":2,"step":1},"dbsize":{"arity":1,"flags":["readonly","fast"],"keyStart":0,"keyStop":0,"step":0},"debug":{"arity":-2,"flags":["admin","noscript","loading","stale"],"keyStart":0,"keyStop":0,"step":0},"decr":{"arity":2,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"decrby":{"arity":3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"del":{"arity":-2,"flags":["write"],"keyStart":1,"keyStop":-1,"step":1},"discard":{"arity":1,"flags":["noscript","loading","stale","fast","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"dump":{"arity":2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"echo":{"arity":2,"flags":["fast"],"keyStart":0,"keyStop":0,"step":0},"eval":{"arity":-3,"flags":["noscript","stale","skip_monitor","no_mandatory_keys","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"eval_ro":{"arity":-3,"flags":["readonly","noscript","stale","skip_monitor","no_mandatory_keys","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"evalsha":{"arity":-3,"flags":["noscript","stale","skip_monitor","no_mandatory_keys","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"evalsha_ro":{"arity":-3,"flags":["readonly","noscript","stale","skip_monitor","no_mandatory_keys","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"exec":{"arity":1,"flags":["noscript","loading","stale","skip_slowlog"],"keyStart":0,"keyStop":0,"step":0},"exists":{"arity":-2,"flags":["readonly","fast"],"keyStart":1,"keyStop":-1,"step":1},"expire":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"expireat":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"expiretime":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"failover":{"arity":-1,"flags":["admin","noscript","stale"],"keyStart":0,"keyStop":0,"step":0},"fcall":{"arity":-3,"flags":["noscript","stale","skip_monitor","no_mandatory_keys","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"fcall_ro":{"arity":-3,"flags":["readonly","noscript","stale","skip_monitor","no_mandatory_keys","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"flushall":{"arity":-1,"flags":["write"],"keyStart":0,"keyStop":0,"step":0},"flushdb":{"arity":-1,"flags":["write"],"keyStart":0,"keyStop":0,"step":0},"function":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"geoadd":{"arity":-5,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"geodist":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"geohash":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"geopos":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"georadius":{"arity":-6,"flags":["write","denyoom","movablekeys"],"keyStart":1,"keyStop":1,"step":1},"georadius_ro":{"arity":-6,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"georadiusbymember":{"arity":-5,"flags":["write","denyoom","movablekeys"],"keyStart":1,"keyStop":1,"step":1},"georadiusbymember_ro":{"arity":-5,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"geosearch":{"arity":-7,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"geosearchstore":{"arity":-8,"flags":["write","denyoom"],"keyStart":1,"keyStop":2,"step":1},"get":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"getbit":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"getdel":{"arity":2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"getex":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"getrange":{"arity":4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"getset":{"arity":3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"hdel":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"hello":{"arity":-1,"flags":["noscript","loading","stale","fast","no_auth","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"hexists":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"hget":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"hgetall":{"arity":2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"hincrby":{"arity":4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"hincrbyfloat":{"arity":4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"hkeys":{"arity":2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"hlen":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"hmget":{"arity":-3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"hmset":{"arity":-4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"hrandfield":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"hscan":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"hset":{"arity":-4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"hsetnx":{"arity":4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"hstrlen":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"hvals":{"arity":2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"incr":{"arity":2,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"incrby":{"arity":3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"incrbyfloat":{"arity":3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"info":{"arity":-1,"flags":["loading","stale"],"keyStart":0,"keyStop":0,"step":0},"keys":{"arity":2,"flags":["readonly"],"keyStart":0,"keyStop":0,"step":0},"lastsave":{"arity":1,"flags":["loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"latency":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"lcs":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":2,"step":1},"lindex":{"arity":3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"linsert":{"arity":5,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"llen":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"lmove":{"arity":5,"flags":["write","denyoom"],"keyStart":1,"keyStop":2,"step":1},"lmpop":{"arity":-4,"flags":["write","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"lolwut":{"arity":-1,"flags":["readonly","fast"],"keyStart":0,"keyStop":0,"step":0},"lpop":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"lpos":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"lpush":{"arity":-3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"lpushx":{"arity":-3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"lrange":{"arity":4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"lrem":{"arity":4,"flags":["write"],"keyStart":1,"keyStop":1,"step":1},"lset":{"arity":4,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"ltrim":{"arity":4,"flags":["write"],"keyStart":1,"keyStop":1,"step":1},"memory":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"mget":{"arity":-2,"flags":["readonly","fast"],"keyStart":1,"keyStop":-1,"step":1},"migrate":{"arity":-6,"flags":["write","movablekeys"],"keyStart":3,"keyStop":3,"step":1},"module":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"monitor":{"arity":1,"flags":["admin","noscript","loading","stale"],"keyStart":0,"keyStop":0,"step":0},"move":{"arity":3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"mset":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":-1,"step":2},"msetnx":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":-1,"step":2},"multi":{"arity":1,"flags":["noscript","loading","stale","fast","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"object":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"persist":{"arity":2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"pexpire":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"pexpireat":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"pexpiretime":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"pfadd":{"arity":-2,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"pfcount":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":-1,"step":1},"pfdebug":{"arity":3,"flags":["write","denyoom","admin"],"keyStart":2,"keyStop":2,"step":1},"pfmerge":{"arity":-2,"flags":["write","denyoom"],"keyStart":1,"keyStop":-1,"step":1},"pfselftest":{"arity":1,"flags":["admin"],"keyStart":0,"keyStop":0,"step":0},"ping":{"arity":-1,"flags":["fast"],"keyStart":0,"keyStop":0,"step":0},"psetex":{"arity":4,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"psubscribe":{"arity":-2,"flags":["pubsub","noscript","loading","stale"],"keyStart":0,"keyStop":0,"step":0},"psync":{"arity":-3,"flags":["admin","noscript","no_async_loading","no_multi"],"keyStart":0,"keyStop":0,"step":0},"pttl":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"publish":{"arity":3,"flags":["pubsub","loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"pubsub":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"punsubscribe":{"arity":-1,"flags":["pubsub","noscript","loading","stale"],"keyStart":0,"keyStop":0,"step":0},"quit":{"arity":-1,"flags":["noscript","loading","stale","fast","no_auth","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"randomkey":{"arity":1,"flags":["readonly"],"keyStart":0,"keyStop":0,"step":0},"readonly":{"arity":1,"flags":["loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"readwrite":{"arity":1,"flags":["loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"rename":{"arity":3,"flags":["write"],"keyStart":1,"keyStop":2,"step":1},"renamenx":{"arity":3,"flags":["write","fast"],"keyStart":1,"keyStop":2,"step":1},"replconf":{"arity":-1,"flags":["admin","noscript","loading","stale","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"replicaof":{"arity":3,"flags":["admin","noscript","stale","no_async_loading"],"keyStart":0,"keyStop":0,"step":0},"reset":{"arity":1,"flags":["noscript","loading","stale","fast","no_auth","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"restore":{"arity":-4,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"restore-asking":{"arity":-4,"flags":["write","denyoom","asking"],"keyStart":1,"keyStop":1,"step":1},"role":{"arity":1,"flags":["noscript","loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"rpop":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"rpoplpush":{"arity":3,"flags":["write","denyoom"],"keyStart":1,"keyStop":2,"step":1},"rpush":{"arity":-3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"rpushx":{"arity":-3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"sadd":{"arity":-3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"save":{"arity":1,"flags":["admin","noscript","no_async_loading","no_multi"],"keyStart":0,"keyStop":0,"step":0},"scan":{"arity":-2,"flags":["readonly"],"keyStart":0,"keyStop":0,"step":0},"scard":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"script":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"sdiff":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":-1,"step":1},"sdiffstore":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":-1,"step":1},"select":{"arity":2,"flags":["loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"set":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"setbit":{"arity":4,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"setex":{"arity":4,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"setnx":{"arity":3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"setrange":{"arity":4,"flags":["write","denyoom"],"keyStart":1,"keyStop":1,"step":1},"shutdown":{"arity":-1,"flags":["admin","noscript","loading","stale","no_multi","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"sinter":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":-1,"step":1},"sintercard":{"arity":-3,"flags":["readonly","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"sinterstore":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":-1,"step":1},"sismember":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"slaveof":{"arity":3,"flags":["admin","noscript","stale","no_async_loading"],"keyStart":0,"keyStop":0,"step":0},"slowlog":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"smembers":{"arity":2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"smismember":{"arity":-3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"smove":{"arity":4,"flags":["write","fast"],"keyStart":1,"keyStop":2,"step":1},"sort":{"arity":-2,"flags":["write","denyoom","movablekeys"],"keyStart":1,"keyStop":1,"step":1},"sort_ro":{"arity":-2,"flags":["readonly","movablekeys"],"keyStart":1,"keyStop":1,"step":1},"spop":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"spublish":{"arity":3,"flags":["pubsub","loading","stale","fast"],"keyStart":1,"keyStop":1,"step":1},"srandmember":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"srem":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"sscan":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"ssubscribe":{"arity":-2,"flags":["pubsub","noscript","loading","stale"],"keyStart":1,"keyStop":-1,"step":1},"strlen":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"subscribe":{"arity":-2,"flags":["pubsub","noscript","loading","stale"],"keyStart":0,"keyStop":0,"step":0},"substr":{"arity":4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"sunion":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":-1,"step":1},"sunionstore":{"arity":-3,"flags":["write","denyoom"],"keyStart":1,"keyStop":-1,"step":1},"sunsubscribe":{"arity":-1,"flags":["pubsub","noscript","loading","stale"],"keyStart":1,"keyStop":-1,"step":1},"swapdb":{"arity":3,"flags":["write","fast"],"keyStart":0,"keyStop":0,"step":0},"sync":{"arity":1,"flags":["admin","noscript","no_async_loading","no_multi"],"keyStart":0,"keyStop":0,"step":0},"time":{"arity":1,"flags":["loading","stale","fast"],"keyStart":0,"keyStop":0,"step":0},"touch":{"arity":-2,"flags":["readonly","fast"],"keyStart":1,"keyStop":-1,"step":1},"ttl":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"type":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"unlink":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":-1,"step":1},"unsubscribe":{"arity":-1,"flags":["pubsub","noscript","loading","stale"],"keyStart":0,"keyStop":0,"step":0},"unwatch":{"arity":1,"flags":["noscript","loading","stale","fast","allow_busy"],"keyStart":0,"keyStop":0,"step":0},"wait":{"arity":3,"flags":["noscript"],"keyStart":0,"keyStop":0,"step":0},"watch":{"arity":-2,"flags":["noscript","loading","stale","fast","allow_busy"],"keyStart":1,"keyStop":-1,"step":1},"xack":{"arity":-4,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"xadd":{"arity":-5,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"xautoclaim":{"arity":-6,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"xclaim":{"arity":-6,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"xdel":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"xgroup":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"xinfo":{"arity":-2,"flags":[],"keyStart":0,"keyStop":0,"step":0},"xlen":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"xpending":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"xrange":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"xread":{"arity":-4,"flags":["readonly","blocking","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"xreadgroup":{"arity":-7,"flags":["write","blocking","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"xrevrange":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"xsetid":{"arity":-3,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"xtrim":{"arity":-4,"flags":["write"],"keyStart":1,"keyStop":1,"step":1},"zadd":{"arity":-4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"zcard":{"arity":2,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zcount":{"arity":4,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zdiff":{"arity":-3,"flags":["readonly","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"zdiffstore":{"arity":-4,"flags":["write","denyoom","movablekeys"],"keyStart":1,"keyStop":1,"step":1},"zincrby":{"arity":4,"flags":["write","denyoom","fast"],"keyStart":1,"keyStop":1,"step":1},"zinter":{"arity":-3,"flags":["readonly","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"zintercard":{"arity":-3,"flags":["readonly","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"zinterstore":{"arity":-4,"flags":["write","denyoom","movablekeys"],"keyStart":1,"keyStop":1,"step":1},"zlexcount":{"arity":4,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zmpop":{"arity":-4,"flags":["write","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"zmscore":{"arity":-3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zpopmax":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"zpopmin":{"arity":-2,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"zrandmember":{"arity":-2,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrange":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrangebylex":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrangebyscore":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrangestore":{"arity":-5,"flags":["write","denyoom"],"keyStart":1,"keyStop":2,"step":1},"zrank":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zrem":{"arity":-3,"flags":["write","fast"],"keyStart":1,"keyStop":1,"step":1},"zremrangebylex":{"arity":4,"flags":["write"],"keyStart":1,"keyStop":1,"step":1},"zremrangebyrank":{"arity":4,"flags":["write"],"keyStart":1,"keyStop":1,"step":1},"zremrangebyscore":{"arity":4,"flags":["write"],"keyStart":1,"keyStop":1,"step":1},"zrevrange":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrevrangebylex":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrevrangebyscore":{"arity":-4,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zrevrank":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zscan":{"arity":-3,"flags":["readonly"],"keyStart":1,"keyStop":1,"step":1},"zscore":{"arity":3,"flags":["readonly","fast"],"keyStart":1,"keyStop":1,"step":1},"zunion":{"arity":-3,"flags":["readonly","movablekeys"],"keyStart":0,"keyStop":0,"step":0},"zunionstore":{"arity":-4,"flags":["write","denyoom","movablekeys"],"keyStart":1,"keyStop":1,"step":1}}')}};