import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';
import { z } from 'zod';

// Validation schema for workflow definition
const WorkflowDefinitionSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  workflow_json: z.object({
    id: z.string(),
    name: z.string(),
    version: z.number(),
    triggers: z.record(z.any()),
    states: z.record(z.any()),
  }),
});

export async function GET() {
  try {
    const supabase = await createServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch workflow definitions
    const { data, error } = await supabase
      .from('workflow_definitions')
      .select('*')
      .eq('is_active', true)
      .order('name', { ascending: true });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error fetching workflow definitions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient();

    // Check authentication and authorization
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const { data: userData } = await supabase
      .from('staff')
      .select('role:roles(name)')
      .eq('auth_id', user.id)
      .single();

    const userRole = userData?.role?.name;
    if (!['Global Administrator', 'Web App System Administrator'].includes(userRole)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = WorkflowDefinitionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { name, description, workflow_json } = validation.data;

    // Check if workflow with same name already exists
    const { data: existing } = await supabase
      .from('workflow_definitions')
      .select('id')
      .eq('name', name)
      .eq('is_active', true)
      .single();

    if (existing) {
      return NextResponse.json(
        { error: 'Workflow definition with this name already exists' },
        { status: 409 }
      );
    }

    // Create workflow definition
    const { data, error } = await supabase
      .from('workflow_definitions')
      .insert({
        name,
        description,
        workflow_json,
        version: 1,
      })
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Initialize workflow engine to validate the definition
    const engine = new WorkflowEngine();
    try {
      await engine.validateWorkflowDefinition(data);
    } catch (validationError: any) {
      // Delete the invalid workflow definition
      await supabase
        .from('workflow_definitions')
        .delete()
        .eq('id', data.id);

      return NextResponse.json(
        { error: 'Invalid workflow definition', details: validationError.message },
        { status: 400 }
      );
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error('Error creating workflow definition:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
