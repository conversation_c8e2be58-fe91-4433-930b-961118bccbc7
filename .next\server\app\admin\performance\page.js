(()=>{var e={};e.id=8591,e.ids=[8591],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53899:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(70260),n=r(28203),a=r(25155),i=r.n(a),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d=["",{children:["admin",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69473)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\performance\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\performance\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/performance/page",pathname:"/admin/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56946:(e,t,r)=>{Promise.resolve().then(r.bind(r,69473))},3802:(e,t,r)=>{Promise.resolve().then(r.bind(r,89643))},19644:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},77788:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},42125:()=>{},66093:()=>{},89643:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(45512),n=r(58009),a=r(97643),i=r(3328),l=r(77252),o=r(78896),d=r(41680);let c=(0,d.A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]]);var u=r(26658),m=r(74464);let p=(0,d.A)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var x=r(88976);function f(){let[e,t]=(0,n.useState)({responseTime:0,throughput:0,errorRate:0,activeConnections:0,cacheHitRate:0,dbQueryTime:0}),r=(e,t)=>e<=t.good?"text-green-500":e<=t.warning?"text-yellow-500":"text-red-500";return(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Performance Monitoring"}),(0,s.jsxs)(l.E,{variant:"outline",className:"text-sm",children:[(0,s.jsx)(o.A,{className:"w-3 h-3 mr-1"}),"Live Monitoring Active"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Response Time"}),(0,s.jsx)(c,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:`text-2xl font-bold ${r(e.responseTime,{good:100,warning:200})}`,children:[e.responseTime.toFixed(0),"ms"]}),(0,s.jsx)(i.k,{value:Math.min(e.responseTime/300*100,100),className:"h-2 mt-2"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Target: <200ms"})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Throughput"}),(0,s.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-green-500",children:[e.throughput.toFixed(0)," req/s"]}),(0,s.jsx)(i.k,{value:e.throughput/1500*100,className:"h-2 mt-2"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Peak: 1500 req/s"})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Error Rate"}),(0,s.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:`text-2xl font-bold ${r(e.errorRate,{good:1,warning:3})}`,children:[e.errorRate.toFixed(2),"%"]}),(0,s.jsx)(i.k,{value:20*e.errorRate,className:"h-2 mt-2"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Target: <1%"})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Active Connections"}),(0,s.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.activeConnections}),(0,s.jsx)(i.k,{value:e.activeConnections/150*100,className:"h-2 mt-2"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Max: 150 connections"})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Cache Hit Rate"}),(0,s.jsx)(p,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-green-500",children:[e.cacheHitRate.toFixed(1),"%"]}),(0,s.jsx)(i.k,{value:e.cacheHitRate,className:"h-2 mt-2"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Target: >80%"})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"DB Query Time"}),(0,s.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:`text-2xl font-bold ${r(e.dbQueryTime,{good:50,warning:100})}`,children:[e.dbQueryTime.toFixed(0),"ms"]}),(0,s.jsx)(i.k,{value:Math.min(e.dbQueryTime/150*100,100),className:"h-2 mt-2"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Target: <100ms"})]})]})]}),(0,s.jsxs)(a.Zp,{className:"border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20",children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{className:"text-lg",children:"Performance Recommendations"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:"• Consider implementing Redis caching for frequently accessed data"}),(0,s.jsx)("li",{children:"• Enable database query optimization for complex joins"}),(0,s.jsx)("li",{children:"• Implement CDN for static assets to reduce server load"}),(0,s.jsx)("li",{children:"• Configure connection pooling for better resource utilization"})]})})]})]})}},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(45512);r(58009);var n=r(21643),a=r(59462);let i=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,a.cn)(i({variant:t}),e),...r})}},97643:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l,wL:()=>u});var s=r(45512),n=r(58009),a=r(59462);let i=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));i.displayName="Card";let l=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},3328:(e,t,r)=>{"use strict";r.d(t,{k:()=>l});var s=r(45512),n=r(58009),a=r(41022),i=r(59462);let l=n.forwardRef(({className:e,value:t,...r},n)=>(0,s.jsx)(a.bL,{ref:n,className:(0,i.cn)("relative h-2 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,s.jsx)(a.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));l.displayName=a.bL.displayName},59462:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(82281),n=r(94805);function a(...e){return(0,n.QP)((0,s.$)(e))}},41680:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(58009);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:o,iconNode:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:a("lucide",l),...c},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(o)?o:[o]])),o=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},o)=>(0,s.createElement)(l,{ref:o,iconNode:t,className:a(`lucide-${n(e)}`,r),...i}));return r.displayName=`${e}`,r}},78896:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},88976:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},74464:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},26658:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(41680).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},69473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\admin\\\\performance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\performance\\page.tsx","default")},19611:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>i});var s=r(62740),n=r(61421),a=r.n(n);r(82704);let i={title:"ITSync - Enterprise IT Helpdesk",description:"AI-powered IT Helpdesk & Support Platform"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:a().className,children:e})})}},82704:()=>{},29952:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>a});var s=r(58009);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return s.useCallback(a(...e),e)}},6004:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,q:()=>a});var s=r(58009),n=r(45512);function a(e,t){let r=s.createContext(t),a=e=>{let{children:t,...a}=e,i=s.useMemo(()=>a,Object.values(a));return(0,n.jsx)(r.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(n){let a=s.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let i=s.createContext(a),l=r.length;r=[...r,a];let o=t=>{let{scope:r,children:a,...o}=t,d=r?.[e]?.[l]||i,c=s.useMemo(()=>o,Object.values(o));return(0,n.jsx)(d.Provider,{value:c,children:a})};return o.displayName=t+"Provider",[o,function(r,n){let o=n?.[e]?.[l]||i,d=s.useContext(o);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:s})=>{let n=r(e)[`__scope${s}`];return{...t,...n}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}},30830:(e,t,r)=>{"use strict";r.d(t,{hO:()=>o,sG:()=>l});var s=r(58009),n=r(55740),a=r(12705),i=r(45512),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),n=s.forwardRef((e,s)=>{let{asChild:n,...a}=e,l=n?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...a,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function o(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},41022:(e,t,r)=>{"use strict";r.d(t,{C1:()=>b,bL:()=>j});var s=r(58009),n=r(6004),a=r(30830),i=r(45512),l="Progress",[o,d]=(0,n.A)(l),[c,u]=o(l),m=s.forwardRef((e,t)=>{var r,s;let{__scopeProgress:n,value:l=null,max:o,getValueLabel:d=f,...u}=e;(o||0===o)&&!y(o)&&console.error((r=`${o}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=y(o)?o:100;null===l||g(l,m)||console.error((s=`${l}`,`Invalid prop \`value\` of value \`${s}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=g(l,m)?l:null,x=v(p)?d(p,m):void 0;return(0,i.jsx)(c,{scope:n,value:p,max:m,children:(0,i.jsx)(a.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":v(p)?p:void 0,"aria-valuetext":x,role:"progressbar","data-state":h(p,m),"data-value":p??void 0,"data-max":m,...u,ref:t})})});m.displayName=l;var p="ProgressIndicator",x=s.forwardRef((e,t)=>{let{__scopeProgress:r,...s}=e,n=u(p,r);return(0,i.jsx)(a.sG.div,{"data-state":h(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...s,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function v(e){return"number"==typeof e}function y(e){return v(e)&&!isNaN(e)&&e>0}function g(e,t){return v(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=p;var j=m,b=x},12705:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>i});var s=r(58009),n=r(29952),a=r(45512);function i(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let s in t){let n=e[s],a=t[s];/^on[A-Z]/.test(s)?n&&a?r[s]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...a}:"className"===s&&(r[s]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(o.ref=t?(0,n.t)(t,l):l),s.cloneElement(r,o)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...i}=e,l=s.Children.toArray(n),o=l.find(c);if(o){let e=o.props.children,n=l.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},21643:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(82281);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let a=n(t)||n(s);return i[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return a(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,5447,8621],()=>r(53899));module.exports=s})();