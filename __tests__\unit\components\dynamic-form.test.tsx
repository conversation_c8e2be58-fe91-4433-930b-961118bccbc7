import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DynamicForm } from '@/components/forms/dynamic-form'
import { AuthProvider } from '@/lib/auth-context'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

// Mock Supabase client
jest.mock('@supabase/auth-helpers-nextjs', () => ({
  createClientComponentClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: null }, error: null })),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
        })),
      })),
    })),
  })),
}))

describe('DynamicForm Component', () => {
  const mockFormData = {
    id: 'test-form',
    title: 'Test Form',
    fields: [
      {
        id: 'name',
        type: 'text',
        label: 'Name',
        labelJp: '名前',
        required: true,
        validation: 'string',
      },
      {
        id: 'department',
        type: 'select',
        label: 'Department',
        labelJp: '部署',
        required: true,
        options: [
          { value: 'it', label: 'IT Systems' },
          { value: 'hr', label: 'Human Resources' },
        ],
      },
    ],
  }

  const mockOnSubmit = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders form fields correctly', () => {
    render(
      <AuthProvider>
        <DynamicForm formData={mockFormData} onSubmit={mockOnSubmit} />
      </AuthProvider>
    )

    expect(screen.getByLabelText(/Name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/Department/i)).toBeInTheDocument()
  })

  it('shows Japanese labels when language is set to Japanese', () => {
    // Mock localStorage for language preference
    Storage.prototype.getItem = jest.fn(() => 'ja')

    render(
      <AuthProvider>
        <DynamicForm formData={mockFormData} onSubmit={mockOnSubmit} />
      </AuthProvider>
    )

    expect(screen.getByText('名前')).toBeInTheDocument()
    expect(screen.getByText('部署')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()

    render(
      <AuthProvider>
        <DynamicForm formData={mockFormData} onSubmit={mockOnSubmit} />
      </AuthProvider>
    )

    const submitButton = screen.getByRole('button', { name: /submit/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/this field is required/i)).toBeInTheDocument()
    })

    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()

    render(
      <AuthProvider>
        <DynamicForm formData={mockFormData} onSubmit={mockOnSubmit} />
      </AuthProvider>
    )

    const nameInput = screen.getByLabelText(/Name/i)
    const departmentSelect = screen.getByLabelText(/Department/i)
    const submitButton = screen.getByRole('button', { name: /submit/i })

    await user.type(nameInput, 'John Doe')
    await user.selectOptions(departmentSelect, 'it')
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        department: 'it',
      })
    })
  })

  it('handles AI auto-population when enabled', async () => {
    const mockAIResponse = {
      name: 'AI Generated Name',
      department: 'hr',
    }

    // Mock AI service
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockAIResponse),
      })
    )

    render(
      <AuthProvider>
        <DynamicForm 
          formData={mockFormData} 
          onSubmit={mockOnSubmit}
          enableAI={true}
        />
      </AuthProvider>
    )

    const aiButton = screen.getByRole('button', { name: /auto-fill/i })
    await userEvent.click(aiButton)

    await waitFor(() => {
      expect(screen.getByDisplayValue('AI Generated Name')).toBeInTheDocument()
    })
  })

  it('filters user selection by department', async () => {
    const mockUsersData = [
      { id: '1', name: 'User 1', department: 'it' },
      { id: '2', name: 'User 2', department: 'hr' },
      { id: '3', name: 'User 3', department: 'it' },
    ]

    const formWithUserSelect = {
      ...mockFormData,
      fields: [
        ...mockFormData.fields,
        {
          id: 'users',
          type: 'multi_select',
          label: 'Users',
          labelJp: 'ユーザー',
          source: 'staff_by_department',
          required: true,
        },
      ],
    }

    // Mock Supabase query for users
    const mockSupabase = createClientComponentClient()
    mockSupabase.from = jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          data: mockUsersData.filter(u => u.department === 'it'),
          error: null,
        })),
      })),
    }))

    render(
      <AuthProvider>
        <DynamicForm 
          formData={formWithUserSelect} 
          onSubmit={mockOnSubmit}
          userDepartment="it"
        />
      </AuthProvider>
    )

    // Should only show IT department users
    await waitFor(() => {
      expect(screen.getByText('User 1')).toBeInTheDocument()
      expect(screen.getByText('User 3')).toBeInTheDocument()
      expect(screen.queryByText('User 2')).not.toBeInTheDocument()
    })
  })
})
