import { NextApiRequest, NextApiResponse } from 'next';
import { openApiMiddleware } from '../../lib/api/openapi-documentation';

/**
 * API Documentation endpoint
 * 
 * This endpoint serves the OpenAPI documentation for the API.
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  return openApiMiddleware({
    title: 'Enterprise IT Helpdesk API',
    description: 'API documentation for the Enterprise IT Helpdesk application',
    version: '1.0.0',
    servers: [
      {
        url: 'https://api.example.com',
        description: 'Production server'
      },
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    basePath: '/api',
    termsOfService: 'https://example.com/terms',
    contact: {
      name: 'API Support',
      url: 'https://example.com/support',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    },
    externalDocs: {
      description: 'Find more info here',
      url: 'https://example.com/docs'
    },
    security: [
      { bearerAuth: [] }
    ]
  })(req, res);
}