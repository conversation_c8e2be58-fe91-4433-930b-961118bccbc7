// Password validation
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Japanese name validation
export const validateJapaneseName = (name: string): boolean => {
  // Check if string contains Japanese characters (Hiragana, Katakana, Kanji)
  const japaneseRegex = /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/
  return japaneseRegex.test(name)
}

// Staff ID validation (format: R000123)
export const validateStaffId = (staffId: string): boolean => {
  const staffIdRegex = /^[A-Z]\d{6}$/
  return staffIdRegex.test(staffId)
}

// PC ID validation 
export const validatePcId = (pcId: string): boolean => {
  const pcIdRegex = /^[A-Z]\d{7}$/
  return pcIdRegex.test(pcId)
}

// Login ID validation (format: <EMAIL>)
export const validateLoginId = (loginId: string): boolean => {
  const loginIdRegex = /^[A-Z]\d{7}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return loginIdRegex.test(loginId)
}
