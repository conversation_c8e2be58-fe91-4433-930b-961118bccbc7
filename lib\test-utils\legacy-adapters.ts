/**
 * Legacy Adapters for Test Compatibility
 * 
 * This module provides adapter functions to bridge old test expectations
 * with the new architecture. It helps maintain backward compatibility
 * while we transition to the new patterns.
 */

import { Database } from '../database.types.enhanced';
import { ErrorService, ErrorSeverity, ErrorCategory } from '../services/error-service';

/**
 * Creates a legacy-compatible Supabase client adapter
 * that mimics the old client interface but uses the new architecture internally
 */
export function createLegacySupabaseAdapter(mockResponses: Record<string, any> = {}) {
  // Create a proxy object that mimics the old Supabase client interface
  return {
    from: (table: string) => ({
      select: (columns: string = '*') => ({
        eq: (field: string, value: any) => ({
          single: () => {
            // Get the mock response for this table
            const response = mockResponses[table];
            
            if (!response) {
              return Promise.resolve({ 
                data: null, 
                error: { message: `No mock data for table: ${table}` } 
              });
            }
            
            if (response.error) {
              return Promise.resolve({ data: null, error: response.error });
            }
            
            return Promise.resolve({ data: response.data, error: null });
          },
          maybeSingle: () => {
            // Get the mock response for this table
            const response = mockResponses[table];
            
            if (!response) {
              return Promise.resolve({ data: null, error: null });
            }
            
            if (response.error) {
              return Promise.resolve({ data: null, error: response.error });
            }
            
            return Promise.resolve({ data: response.data, error: null });
          }
        }),
        filter: (field: string, operator: string, value: any) => ({
          order: (column: string, { ascending = true } = {}) => ({
            limit: (limit: number) => {
              // Get the mock response for this table
              const response = mockResponses[`${table}_filtered`] || mockResponses[table];
              
              if (!response) {
                return Promise.resolve({ data: [], error: null });
              }
              
              if (response.error) {
                return Promise.resolve({ data: null, error: response.error });
              }
              
              // If the response is an array, return it directly
              if (Array.isArray(response.data)) {
                return Promise.resolve({ 
                  data: response.data.slice(0, limit), 
                  error: null 
                });
              }
              
              // If it's a single object, wrap it in an array
              return Promise.resolve({ 
                data: response.data ? [response.data] : [], 
                error: null 
              });
            }
          })
        })
      }),
      insert: (data: any) => {
        const response = mockResponses[`${table}_insert`] || { data, error: null };
        return Promise.resolve(response);
      },
      update: (data: any) => ({
        eq: (field: string, value: any) => {
          const response = mockResponses[`${table}_update`] || { data, error: null };
          return Promise.resolve(response);
        }
      }),
      delete: () => ({
        eq: (field: string, value: any) => {
          const response = mockResponses[`${table}_delete`] || { data: null, error: null };
          return Promise.resolve(response);
        }
      })
    }),
    auth: {
      getUser: () => {
        const response = mockResponses.user || { data: { user: null }, error: null };
        return Promise.resolve(response);
      },
      getSession: () => {
        const response = mockResponses.session || { data: { session: null }, error: null };
        return Promise.resolve(response);
      },
      signInWithPassword: (credentials: { email: string; password: string }) => {
        const response = mockResponses.signIn || { 
          data: { user: null, session: null }, 
          error: null 
        };
        return Promise.resolve(response);
      },
      signOut: () => {
        const response = mockResponses.signOut || { error: null };
        return Promise.resolve(response);
      }
    },
    storage: {
      from: (bucket: string) => ({
        upload: (path: string, file: any) => {
          const response = mockResponses[`${bucket}_upload`] || { 
            data: { path }, 
            error: null 
          };
          return Promise.resolve(response);
        },
        download: (path: string) => {
          const response = mockResponses[`${bucket}_download`] || { 
            data: null, 
            error: null 
          };
          return Promise.resolve(response);
        }
      })
    }
  };
}

/**
 * Creates a legacy-compatible error response
 * that mimics the old error format but uses the new error service internally
 */
export function createLegacyErrorResponse(
  message: string,
  code: string = 'ERROR',
  details: Record<string, any> = {}
) {
  const appError = ErrorService.createAppError(message, {
    code,
    details,
    severity: ErrorSeverity.ERROR,
    category: ErrorCategory.UNKNOWN
  });
  
  // Convert to the old error format
  return {
    message: appError.message,
    code: appError.code,
    details: appError.details
  };
}

/**
 * Adapter for getUserWithRole function to maintain backward compatibility
 */
export async function legacyGetUserWithRole(
  userId: string,
  mockData?: any
) {
  // If mock data is provided, return it directly
  if (mockData) {
    return Promise.resolve({
      data: mockData,
      error: null
    });
  }
  
  try {
    // Import the actual function dynamically to avoid circular dependencies
    const { getUserWithRole } = await import('../auth');
    
    // Call the actual function
    const result = await getUserWithRole(userId);
    
    // Return in the legacy format
    return {
      data: result.data,
      error: result.error
    };
  } catch (error) {
    // Handle any unexpected errors
    console.error('Error in legacyGetUserWithRole:', error);
    
    return {
      data: null,
      error: createLegacyErrorResponse(
        error instanceof Error ? error.message : 'Unknown error in getUserWithRole',
        'AUTH_ERROR'
      )
    };
  }
}

/**
 * Helper to update existing tests to use the new architecture
 */
export function updateTestToUseNewArchitecture(testFn: Function) {
  return async (...args: any[]) => {
    try {
      // Set a flag to indicate we're running with the new architecture
      (global as any).__USING_NEW_ARCHITECTURE__ = true;
      
      // Run the test function
      await testFn(...args);
    } finally {
      // Clean up
      delete (global as any).__USING_NEW_ARCHITECTURE__;
    }
  };
}