'use client'

import { useAuth } from '@/lib/auth-context'
import { usePermissions } from '@/lib/use-permissions'
import { useI18n } from '@/lib/i18n/context'
import AuthGuard from '@/components/auth/auth-guard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { 
  Users, 
  Settings, 
  FileText, 
  HelpCircle, 
  UserPlus, 
  Shield,
  Building,
  Mail,
  FolderOpen,
  Monitor,
  Key,
  Layers
} from 'lucide-react'
import { KBIntegrationWidget } from '@/components/knowledge-base/kb-integration-widget'
import { ChatbotWidget } from '@/components/chatbot/chatbot-widget'
import { MultiChannelNotifications } from '@/components/notifications/multi-channel-notifications'
import { RealtimeDashboard } from '@/components/real-time'

export default function DashboardPage() {
  const { user, staff, signOut } = useAuth()
  const { t, locale } = useI18n()
  const { 
    isGlobalAdmin, 
    isSystemAdmin, 
    isDepartmentAdmin, 
    isHRStaff, 
    isITSupport, 
    isRegularUser,
    canManageUsers,
    canViewAllRequests,
    canCreateRequests,
    canAccessHRFunctions,
    canAccessSystemSettings
  } = usePermissions()

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">ITSync</h1>
                <p className="text-sm text-gray-600">
                  {locale === 'ja' ? staff?.name_jp : staff?.name_en || user?.email} - {staff?.role?.name || 'User'}
                </p>
              </div>
              <div className="flex items-center gap-4">
                <MultiChannelNotifications />
                <LanguageSwitcher />
                <Button onClick={handleSignOut} variant="outline">
                  {t('common.logout')}
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Real-time Dashboard - Full Width */}
            <div className="mb-8">
              <RealtimeDashboard />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              
              {/* Request Services */}
              {canCreateRequests() && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="mr-2 h-5 w-5" />
                      {t('navigation.requests')}
                    </CardTitle>
                    <CardDescription>
                      {locale === 'ja' ? '新しいITサポ�Eトリクエストを作�E' : 'Create new IT support requests'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button 
                        className="w-full justify-start" 
                        variant="default"
                        onClick={() => window.location.href = '/request'}
                      >
                        <FileText className="mr-2 h-4 w-4" />
                        {t('common.create')} {t('navigation.requests')}
                      </Button>
                      <Button 
                        className="w-full justify-start" 
                        variant="outline"
                        onClick={() => window.location.href = '/group-mail-management'}
                      >
                        <Mail className="mr-2 h-4 w-4" />
                        {t('services.groupMail.title')}
                      </Button>
                      <Button 
                        className="w-full justify-start" 
                        variant="outline"
                        onClick={() => window.location.href = '/dashboard/it-helpdesk/pc-admin-requests'}
                      >
                        <Monitor className="mr-2 h-4 w-4" />
                        {t('services.pcAdmin.title')}
                      </Button>
                      <Button 
                        className="w-full justify-start" 
                        variant="outline"
                        onClick={() => window.location.href = '/dashboard/it-helpdesk/password-reset'}
                      >
                        <Key className="mr-2 h-4 w-4" />
                        {t('services.passwordReset.title')}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Batch Processing */}
              {(canCreateRequests() || isITSupport()) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Layers className="mr-2 h-5 w-5" />
                      バッチ�E琁E/ Batch Processing
                    </CardTitle>
                    <CardDescription>
                      褁E��リクエスト�E一括処琁E                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      className="w-full justify-start" 
                      variant="default"
                      onClick={() => window.location.href = '/batch-processing'}
                    >
                      <Layers className="mr-2 h-4 w-4" />
                      バッチ�E琁E��開姁E/ Start Batch Processing
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* User Management */}
              {canManageUsers() && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="mr-2 h-5 w-5" />
                      ユーザー管琁E/ User Management
                    </CardTitle>
                    <CardDescription>
                      スタチE��とユーザーの管琁E                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button className="w-full justify-start" variant="outline">
                        <UserPlus className="mr-2 h-4 w-4" />
                        Add New User
                      </Button>
                      <Button 
                        className="w-full justify-start" 
                        variant="outline"
                        onClick={() => window.location.href = '/admin/roles'}
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        Manage Roles
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* HR Functions */}
              {canAccessHRFunctions() && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Building className="mr-2 h-5 w-5" />
                      人事管琁E/ HR Management
                    </CardTitle>
                    <CardDescription>
                      入社・退社手続きの管琁E                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button className="w-full justify-start" variant="outline">
                        入社手続き / Onboarding
                      </Button>
                      <Button className="w-full justify-start" variant="outline">
                        退社手続き / Offboarding
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* System Settings */}
              {canAccessSystemSettings() && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Settings className="mr-2 h-5 w-5" />
                      シスチE��設宁E/ System Settings
                    </CardTitle>
                    <CardDescription>
                      シスチE��全体�E設定と管琁E                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button className="w-full justify-start" variant="outline">
                        General Settings
                      </Button>
                      <Button className="w-full justify-start" variant="outline">
                        Security Settings
                      </Button>
                      <Button 
                        className="w-full justify-start" 
                        variant="outline"
                        onClick={() => window.location.href = '/dashboard/workflows/monitoring'}
                      >
                        Workflow Monitoring
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Request Status */}
              {canViewAllRequests() && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="mr-2 h-5 w-5" />
                      リクエスト状況E/ Request Status
                    </CardTitle>
                    <CardDescription>
                      すべてのリクエスト�E状況確誁E                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button className="w-full justify-start" variant="outline">
                        View All Requests
                      </Button>
                      <Button className="w-full justify-start" variant="outline">
                        Pending Approvals
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Knowledge Base */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <HelpCircle className="mr-2 h-5 w-5" />
                    ナレチE��ベ�Eス / Knowledge Base
                  </CardTitle>
                  <CardDescription>
                    AIを活用したサポ�Eト情報
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button className="w-full justify-start" variant="outline">
                      Search Knowledge Base
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      AI Assistant
                    </Button>
                  </div>
                </CardContent>
              </Card>

            </div>

            {/* Knowledge Base Widget - Full Width */}
            <div className="mt-8">
              <KBIntegrationWidget
                context={{
                  serviceCategory: 'dashboard',
                  currentStep: 'main',
                  formType: 'general'
                }}
                className="w-full"
              />
            </div>
          </div>
        </main>
      </div>
      
      {/* Chatbot Widget */}
      <ChatbotWidget
        currentPage="dashboard"
        defaultOpen={false}
      />
    </AuthGuard>
  )
}
