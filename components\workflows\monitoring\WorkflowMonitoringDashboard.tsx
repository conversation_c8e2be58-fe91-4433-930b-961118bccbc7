"use client"

import React, { useEffect, useState } from 'react'
import {
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  TrendingUp,
  Users,
  XCircle,
  RefreshCw,
  Filter,
  Download,
  Eye,
  Workflow
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'
import { ja } from 'date-fns/locale'
import { WorkflowStatus, WorkflowPriority, SLAStatus } from '@/lib/types/workflow'
import {
  <PERSON><PERSON>hart,
  Line,
  <PERSON>C<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'

interface WorkflowMetrics {
  total: number
  active: number
  completed: number
  failed: number
  onHold: number
  pendingApproval: number
  overdue: number
  avgCompletionTime: number
  slaCompliance: number
  throughput: {
    daily: number
    weekly: number
    monthly: number
  }
}

interface WorkflowInstance {
  id: string
  title: string
  titleJp: string
  status: WorkflowStatus
  priority: WorkflowPriority
  createdAt: string
  updatedAt: string
  completedAt?: string
  currentStep: number
  totalSteps: number
  slaStatus: SLAStatus
  assignedTo?: string
  department: string
  category: string
}

interface TrendData {
  date: string
  completed: number
  failed: number
  created: number
}

interface CategoryDistribution {
  category: string
  count: number
  percentage: number
}

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#6366f1'
}

export default function WorkflowMonitoringDashboard() {
  const [metrics, setMetrics] = useState<WorkflowMetrics | null>(null)
  const [workflows, setWorkflows] = useState<WorkflowInstance[]>([])
  const [trendData, setTrendData] = useState<TrendData[]>([])
  const [categoryData, setCategoryData] = useState<CategoryDistribution[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [timeRange, setTimeRange] = useState('7d')
  const [statusFilter, setStatusFilter] = useState<WorkflowStatus | 'all'>('all')
  const [departmentFilter, setDepartmentFilter] = useState<string>('all')
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null)

  useEffect(() => {
    loadData()
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadData(true)
    }, 30000)
    return () => clearInterval(interval)
  }, [timeRange, statusFilter, departmentFilter])

  const loadData = async (isRefresh = false) => {
    if (isRefresh) setRefreshing(true)
    else setLoading(true)

    try {
      // Load metrics
      const metricsResponse = await fetch(`/api/workflows/monitoring/metrics?timeRange=${timeRange}`)
      const metricsData = await metricsResponse.json()
      setMetrics(metricsData)

      // Load active workflows
      const workflowsResponse = await fetch(
        `/api/workflows/monitoring/instances?status=${statusFilter}&department=${departmentFilter}`
      )
      const workflowsData = await workflowsResponse.json()
      setWorkflows(workflowsData)

      // Load trend data
      const trendResponse = await fetch(`/api/workflows/monitoring/trends?timeRange=${timeRange}`)
      const trendData = await trendResponse.json()
      setTrendData(trendData)

      // Load category distribution
      const categoryResponse = await fetch(`/api/workflows/monitoring/categories?timeRange=${timeRange}`)
      const categoryData = await categoryResponse.json()
      setCategoryData(categoryData)
    } catch (error) {
      console.error('Failed to load monitoring data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleExport = async () => {
    try {
      const response = await fetch(
        `/api/workflows/monitoring/export?timeRange=${timeRange}&format=csv`
      )
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `workflow-monitoring-${new Date().toISOString()}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Failed to export data:', error)
    }
  }

  const getStatusIcon = (status: WorkflowStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'in_progress':
        return <Activity className="h-4 w-4 text-blue-500" />
      case 'pending_approval':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'on_hold':
        return <AlertCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: WorkflowStatus) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'pending_approval':
        return 'bg-yellow-100 text-yellow-800'
      case 'on_hold':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  const getSLAStatusColor = (status: SLAStatus) => {
    switch (status) {
      case 'on_track':
        return 'text-green-600'
      case 'at_risk':
        return 'text-yellow-600'
      case 'breached':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Workflow Monitoring Dashboard
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            ワークフロー監視ダッシュボード
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">24 Hours</SelectItem>
              <SelectItem value="7d">7 Days</SelectItem>
              <SelectItem value="30d">30 Days</SelectItem>
              <SelectItem value="90d">90 Days</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadData(true)}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active: {metrics?.active || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SLA Compliance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.slaCompliance || 0}%
            </div>
            <Progress value={metrics?.slaCompliance || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.pendingApproval || 0}</div>
            <p className="text-xs text-muted-foreground">
              Overdue: {metrics?.overdue || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Throughput</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.throughput.daily || 0}</div>
            <p className="text-xs text-muted-foreground">
              Per day (avg)
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active Workflows</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Active Workflows Tab */}
        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Active Workflows</CardTitle>
                <div className="flex items-center space-x-2">
                  <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="pending_approval">Pending Approval</SelectItem>
                      <SelectItem value="on_hold">On Hold</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Filter by department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="IT Systems Department">IT Systems</SelectItem>
                      <SelectItem value="Human Resources Department">HR</SelectItem>
                      <SelectItem value="Finance and Accounting Department">Finance</SelectItem>
                      <SelectItem value="Corporate Planning Department">Planning</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {workflows.map((workflow) => (
                  <div
                    key={workflow.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                    onClick={() => setSelectedWorkflow(workflow.id)}
                  >
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(workflow.status)}
                      <div>
                        <div className="font-medium">{workflow.title}</div>
                        <div className="text-sm text-gray-500">{workflow.titleJp}</div>
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                          <span>{workflow.department}</span>
                          <span>•</span>
                          <span>{workflow.category}</span>
                          <span>•</span>
                          <span>{formatDistanceToNow(new Date(workflow.createdAt), { addSuffix: true, locale: ja })}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="text-sm">
                          Step {workflow.currentStep} of {workflow.totalSteps}
                        </div>
                        <Progress
                          value={(workflow.currentStep / workflow.totalSteps) * 100}
                          className="w-24 h-2 mt-1"
                        />
                      </div>
                      <Badge className={cn(getSLAStatusColor(workflow.slaStatus))}>
                        {workflow.slaStatus.replace('_', ' ')}
                      </Badge>
                      <Badge variant="outline" className={cn(getStatusColor(workflow.status))}>
                        {workflow.status.replace('_', ' ')}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Trends</CardTitle>
              <CardDescription>Workflow creation and completion trends</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="created"
                    stroke={COLORS.primary}
                    name="Created"
                  />
                  <Line
                    type="monotone"
                    dataKey="completed"
                    stroke={COLORS.success}
                    name="Completed"
                  />
                  <Line
                    type="monotone"
                    dataKey="failed"
                    stroke={COLORS.danger}
                    name="Failed"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Distribution Tab */}
        <TabsContent value="distribution">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Category Distribution</CardTitle>
                <CardDescription>Workflow distribution by category</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }) => `${category} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={Object.values(COLORS)[index % Object.values(COLORS).length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Status Distribution</CardTitle>
                <CardDescription>Current workflow status breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={[
                      { status: 'Active', count: metrics?.active || 0 },
                      { status: 'Completed', count: metrics?.completed || 0 },
                      { status: 'Failed', count: metrics?.failed || 0 },
                      { status: 'On Hold', count: metrics?.onHold || 0 },
                      { status: 'Pending', count: metrics?.pendingApproval || 0 }
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="status" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill={COLORS.primary} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Average Completion Time</CardTitle>
                <CardDescription>By workflow category (hours)</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Password Reset</span>
                    <span className="font-medium">2.5 hrs</span>
                  </div>
                  <Progress value={25} />
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Group Mail Management</span>
                    <span className="font-medium">4.8 hrs</span>
                  </div>
                  <Progress value={48} />
                  <div className="flex items-center justify-between">
                    <span className="text-sm">SharePoint Access</span>
                    <span className="font-medium">6.2 hrs</span>
                  </div>
                  <Progress value={62} />
                  <div className="flex items-center justify-between">
                    <span className="text-sm">PC Admin Rights</span>
                    <span className="font-medium">8.0 hrs</span>
                  </div>
                  <Progress value={80} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">First Response Time</div>
                      <div className="text-sm text-gray-500">Average time to first action</div>
                    </div>
                    <div className="text-2xl font-bold">15 min</div>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Approval Time</div>
                      <div className="text-sm text-gray-500">Average approval duration</div>
                    </div>
                    <div className="text-2xl font-bold">3.2 hrs</div>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Automation Rate</div>
                      <div className="text-sm text-gray-500">Fully automated workflows</div>
                    </div>
                    <div className="text-2xl font-bold">68%</div>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Error Rate</div>
                      <div className="text-sm text-gray-500">Failed workflows</div>
                    </div>
                    <div className="text-2xl font-bold text-red-600">2.3%</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
