(()=>{var e={};e.id=3576,e.ids=[3576],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},10391:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>a,serverHooks:()=>p,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var o=r(42706),n=r(28203),s=r(45994),i=r(55982);let a=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/instances/route",pathname:"/api/workflows/monitoring/instances",filename:"route",bundlePath:"app/api/workflows/monitoring/instances/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:p}=a;function u(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},55982:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Unterminated string constant\n    ,-[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts\x1b[0m:93:1]\n \x1b[2m90\x1b[0m |       return {\n \x1b[2m91\x1b[0m |         id: workflow.id,\n \x1b[2m92\x1b[0m |         title: requestForm?.title || category?.name_en || 'Untitled',\n \x1b[2m93\x1b[0m |         titleJp: category?.name_jp || requestForm?.title || '無顁E,\n    : \x1b[35;1m                                                            ^^^^^^^\x1b[0m\n \x1b[2m94\x1b[0m |         status: workflow.status,\n \x1b[2m95\x1b[0m |         priority: workflow.priority || 'medium',\n \x1b[2m95\x1b[0m |         createdAt: workflow.created_at,\r\n    `----\n  \x1b[31mx\x1b[0m Expected ',', got 'status'\n    ,-[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts\x1b[0m:94:1]\n \x1b[2m91\x1b[0m |         id: workflow.id,\n \x1b[2m92\x1b[0m |         title: requestForm?.title || category?.name_en || 'Untitled',\n \x1b[2m93\x1b[0m |         titleJp: category?.name_jp || requestForm?.title || '無顁E,\n \x1b[2m94\x1b[0m |         status: workflow.status,\n    : \x1b[35;1m        ^^^^^^\x1b[0m\n \x1b[2m95\x1b[0m |         priority: workflow.priority || 'medium',\n \x1b[2m96\x1b[0m |         createdAt: workflow.created_at,\n \x1b[2m96\x1b[0m |         updatedAt: workflow.updated_at,\r\n    `----\n\n\nCaused by:\n    Syntax Error")},42706:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[5994],()=>r(10391));module.exports=o})();