"use strict";(()=>{var e={};e.id=3576,e.ids=[3576],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82733:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(42706),n=r(28203),o=r(45994),i=r(39187),d=r(73865),p=r(44512);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status")||"all",s=t.get("department")||"all",a=await (0,p.UL)(),n=(0,d.createServerSupabase)(a).from("workflow_instances").select(`
        id,
        workflow_definition_id,
        current_state,
        status,
        priority,
        context,
        created_at,
        updated_at,
        completed_at,
        request_forms!workflow_instances_context_fkey (
          id,
          title,
          requester_id,
          staff!request_forms_requester_id_fkey (
            id,
            name_en,
            name_jp,
            divisions!staff_division_id_fkey (
              id,
              name_en,
              name_jp
            )
          ),
          service_categories!request_forms_service_category_id_fkey (
            id,
            name_en,
            name_jp
          )
        ),
        workflow_tasks (
          id,
          task_type,
          status,
          assigned_to
        ),
        sla_tracking (
          id,
          sla_status,
          target_date
        )
      `).order("created_at",{ascending:!1}).limit(100);"all"!==r&&(n=n.eq("status",r)),"all"!==s&&(n=n.eq("request_forms.staff.divisions.name_en",s));let{data:o,error:u}=await n;if(u)throw u;let l=o?.map(e=>{let t=e.request_forms?.[0],r=t?.staff?.[0],s=r?.divisions?.[0],a=t?.service_categories?.[0],n=e.sla_tracking?.[0],o=e.workflow_tasks||[],i=o.filter(e=>"completed"===e.status).length,d=o.length;return{id:e.id,title:t?.title||a?.name_en||"Untitled",titleJp:a?.name_jp||t?.title||"無題",status:e.status,priority:e.priority||"medium",createdAt:e.created_at,updatedAt:e.updated_at,completedAt:e.completed_at,currentStep:i,totalSteps:d||1,slaStatus:n?.sla_status||"on_track",assignedTo:o.find(e=>"in_progress"===e.status)?.assigned_to,department:s?.name_en||"Unknown",category:a?.name_en||"Unknown"}})||[];return i.NextResponse.json(l)}catch(e){return console.error("Error fetching workflow instances:",e),i.NextResponse.json({error:"Failed to fetch workflow instances"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/instances/route",pathname:"/api/workflows/monitoring/instances",filename:"route",bundlePath:"app/api/workflows/monitoring/instances/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:_,serverHooks:f}=l;function m(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:_})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>r(82733));module.exports=s})();