(()=>{var e={};e.id=3576,e.ids=[3576],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},23944:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(42706),n=r(28203),i=r(45994),o=r(39187),u=r(73865),d=r(44512);async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status")||"all",s=t.get("department")||"all",a=await (0,d.UL)(),n=(0,u.createServerSupabase)(a).from("workflow_instances").select(`
        id,
        workflow_definition_id,
        current_state,
        status,
        priority,
        context,
        created_at,
        updated_at,
        completed_at,
        request_forms!workflow_instances_context_fkey (
          id,
          title,
          requester_id,
          staff!request_forms_requester_id_fkey (
            id,
            name_en,
            name_jp,
            divisions!staff_division_id_fkey (
              id,
              name_en,
              name_jp
            )
          ),
          service_categories!request_forms_service_category_id_fkey (
            id,
            name_en,
            name_jp
          )
        ),
        workflow_tasks (
          id,
          task_type,
          status,
          assigned_to
        ),
        sla_tracking (
          id,
          sla_status,
          target_date
        )
      `).order("created_at",{ascending:!1}).limit(100);"all"!==r&&(n=n.eq("status",r)),"all"!==s&&(n=n.eq("request_forms.staff.divisions.name_en",s));let{data:i,error:p}=await n;if(p)throw p;let c=i?.map(e=>{let t=e.request_forms?.[0],r=t?.staff?.[0],s=r?.divisions?.[0],a=t?.service_categories?.[0],n=e.sla_tracking?.[0],i=e.workflow_tasks||[],o=i.filter(e=>"completed"===e.status).length,u=i.length;return{id:e.id,title:t?.title||a?.name_en||"Untitled",titleJp:a?.name_jp||t?.title||"無題",status:e.status,priority:e.priority||"medium",createdAt:e.created_at,updatedAt:e.updated_at,completedAt:e.completed_at,currentStep:o,totalSteps:u||1,slaStatus:n?.sla_status||"on_track",assignedTo:i.find(e=>"in_progress"===e.status)?.assigned_to,department:s?.name_en||"Unknown",category:a?.name_en||"Unknown"}})||[];return o.NextResponse.json(c)}catch(e){return console.error("Error fetching workflow instances:",e),o.NextResponse.json({error:"Failed to fetch workflow instances"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/monitoring/instances/route",pathname:"/api/workflows/monitoring/instances",filename:"route",bundlePath:"app/api/workflows/monitoring/instances/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:_,serverHooks:f}=c;function m(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:_})}},96487:()=>{},78335:()=>{},73865:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,5452,4512],()=>r(23944));module.exports=s})();