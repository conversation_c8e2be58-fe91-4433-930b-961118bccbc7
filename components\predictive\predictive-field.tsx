'use client'

import React, { useState } from 'react'
import { DynamicField } from '@/components/forms/dynamic-field'
import { PredictiveSuggestions } from '@/components/predictive/predictive-suggestions'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>rk<PERSON>, Settings } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { FormField } from '@/lib/form-types'
import { usePredictiveForm } from '@/lib/hooks/use-predictive-form'

interface PredictiveFieldProps {
  field: FormField
  value: any
  onChange: (value: any) => void
  formData: Record<string, any>
  formType: string
  disabled?: boolean
  className?: string
}

export function PredictiveField({
  field,
  value,
  onChange,
  formData,
  formType,
  disabled,
  className
}: PredictiveFieldProps) {
  const [showPredictions, setShowPredictions] = useState(true)
  const { 
    predictions, 
    preferences,
    getPredictions,
    acceptPrediction 
  } = usePredictiveForm({
    formType,
    onPredictionAccepted: (fieldName, value) => {
      if (fieldName === field.name) {
        onChange(value)
      }
    }
  })

  // Get predictions when field is focused and empty
  React.useEffect(() => {
    if (!value && preferences?.enablePredictions && showPredictions) {
      getPredictions(field.name, formData, false)
    }
  }, [field.name, value, formData, preferences, showPredictions, getPredictions])

  const handleAcceptSuggestion = (suggestedValue: string) => {
    acceptPrediction(field.name, suggestedValue)
  }

  const fieldPredictions = predictions.find(p => p.fieldName === field.name)
  const hasPredictions = fieldPredictions && fieldPredictions.suggestions.length > 0

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {hasPredictions && !value && (
            <Badge variant="secondary" className="text-xs">
              <Sparkles className="h-3 w-3 mr-1" />
              予測あり
            </Badge>
          )}
        </div>
        {preferences?.enablePredictions && (
          <Button
            size="icon"
            variant="ghost"
            className="h-6 w-6"
            onClick={() => setShowPredictions(!showPredictions)}
            title={showPredictions ? '予測を非表示' : '予測を表示'}
          >
            <Settings className="h-3 w-3" />
          </Button>
        )}
      </div>

      <DynamicField
        field={field}
        value={value}
        onChange={onChange}
        disabled={disabled}
      />

      {preferences?.enablePredictions && 
       showPredictions && 
       !value && 
       hasPredictions && (
        <PredictiveSuggestions
          formType={formType}
          fieldName={field.name}
          currentValue={value || ''}
          formData={formData}
          onAcceptSuggestion={handleAcceptSuggestion}
        />
      )}
    </div>
  )
}
