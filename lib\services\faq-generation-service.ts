import { supabase } from '@/lib/supabase';
import { AIAPIService } from './ai-api-service';

interface FAQ {
  question_ja: string;
  question_en: string;
  answer_ja: string;
  answer_en: string;
  category: string;
  keywords: string[];
  source_article_ids: string[];
  confidence_score: number;
}

interface GenerationOptions {
  minConfidence?: number;
  maxFAQsPerArticle?: number;
  categories?: string[];
  language?: 'ja' | 'en' | 'both';
}

export class FAQGenerationService {
  private static instance: FAQGenerationService;
  private aiService: AIAPIService;

  private constructor() {
    this.aiService = AIAPIService.getInstance();
  }

  static getInstance(): FAQGenerationService {
    if (!this.instance) {
      this.instance = new FAQGenerationService();
    }
    return this.instance;
  }

  /**
   * Generate FAQs from knowledge base articles
   */
  async generateFAQsFromKB(options: GenerationOptions = {}): Promise<FAQ[]> {
    const {
      minConfidence = 0.7,
      maxFAQsPerArticle = 3,
      categories = [],
      language = 'both'
    } = options;

    try {
      // Fetch articles from knowledge base
      let query = supabase
        .from('kb_articles')
        .select('*')
        .eq('is_published', true);

      if (categories.length > 0) {
        query = query.in('category', categories);
      }

      const { data: articles, error } = await query;
      if (error) throw error;

      const allFAQs: FAQ[] = [];

      // Process each article
      for (const article of articles || []) {
        const faqs = await this.generateFAQsFromArticle(article, {
          maxFAQs: maxFAQsPerArticle,
          language
        });

        // Filter by confidence
        const filteredFAQs = faqs.filter(faq => faq.confidence_score >= minConfidence);
        allFAQs.push(...filteredFAQs);
      }

      // Remove duplicates and merge similar FAQs
      const uniqueFAQs = await this.deduplicateAndMergeFAQs(allFAQs);

      return uniqueFAQs;
    } catch (error) {
      console.error('Error generating FAQs from KB:', error);
      throw error;
    }
  }

  /**
   * Generate FAQs from a single article
   */
  private async generateFAQsFromArticle(
    article: any,
    options: { maxFAQs: number; language: 'ja' | 'en' | 'both' }
  ): Promise<FAQ[]> {
    const { maxFAQs, language } = options;

    const prompt = `
Analyze the following knowledge base article and generate ${maxFAQs} frequently asked questions (FAQs) that users might have about this topic.

Article Title (English): ${article.title_en}
Article Title (Japanese): ${article.title_jp}
Category: ${article.category}
Content (English): ${article.content_en}
Content (Japanese): ${article.content_jp}

For each FAQ, provide:
1. A natural question that users would ask
2. A concise, helpful answer extracted from the article
3. Relevant keywords for search
4. A confidence score (0-1) indicating how likely this FAQ is to be useful

${language === 'both' ? 'Provide questions and answers in both Japanese and English.' : 
  language === 'ja' ? 'Provide questions and answers in Japanese only.' : 
  'Provide questions and answers in English only.'}

Format the response as a JSON array of FAQ objects with the following structure:
{
  "question_ja": "Japanese question",
  "question_en": "English question",
  "answer_ja": "Japanese answer",
  "answer_en": "English answer",
  "keywords": ["keyword1", "keyword2"],
  "confidence_score": 0.85
}

Focus on questions that:
- Address common user concerns or confusion points
- Cover important procedures or requirements
- Explain technical terms or concepts
- Provide practical guidance
`;

    try {
      const response = await this.aiService.complete(prompt, { temperature: 0.3 });
      const faqs = JSON.parse(response);

      // Add article reference and category
      return faqs.map((faq: any) => ({
        ...faq,
        category: article.category,
        source_article_ids: [article.id],
        keywords: [...(faq.keywords || []), article.category]
      }));
    } catch (error) {
      console.error('Error generating FAQs from article:', error);
      return [];
    }
  }

  /**
   * Deduplicate and merge similar FAQs
   */
  private async deduplicateAndMergeFAQs(faqs: FAQ[]): Promise<FAQ[]> {
    const uniqueFAQs: FAQ[] = [];
    const processedQuestions = new Set<string>();

    for (const faq of faqs) {
      const questionKey = `${faq.question_en.toLowerCase()}|${faq.question_ja}`;
      
      if (!processedQuestions.has(questionKey)) {
        // Check for similar FAQs already in the unique list
        const similarFAQ = uniqueFAQs.find(
          existing => this.calculateSimilarity(existing.question_en, faq.question_en) > 0.8
        );

        if (similarFAQ) {
          // Merge FAQs
          similarFAQ.source_article_ids = [
            ...new Set([...similarFAQ.source_article_ids, ...faq.source_article_ids])
          ];
          similarFAQ.keywords = [
            ...new Set([...similarFAQ.keywords, ...faq.keywords])
          ];
          similarFAQ.confidence_score = Math.max(similarFAQ.confidence_score, faq.confidence_score);
        } else {
          uniqueFAQs.push(faq);
          processedQuestions.add(questionKey);
        }
      }
    }

    return uniqueFAQs;
  }

  /**
   * Calculate similarity between two strings (simple implementation)
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = new Set(str1.toLowerCase().split(/\s+/));
    const words2 = new Set(str2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * Generate FAQs from user search patterns and failed queries
   */
  async generateFAQsFromSearchPatterns(
    timeRange: { start: Date; end: Date }
  ): Promise<FAQ[]> {
    try {
      // Fetch search patterns and content gaps
      const { data: contentGaps, error: gapsError } = await supabase
        .from('kb_content_gaps')
        .select('*')
        .gte('created_at', timeRange.start.toISOString())
        .lte('created_at', timeRange.end.toISOString())
        .order('occurrence_count', { ascending: false })
        .limit(20);

      if (gapsError) throw gapsError;

      const { data: searchHistory, error: historyError } = await supabase
        .from('kb_search_history')
        .select('*')
        .gte('created_at', timeRange.start.toISOString())
        .lte('created_at', timeRange.end.toISOString())
        .eq('results_count', 0)
        .order('created_at', { ascending: false })
        .limit(50);

      if (historyError) throw historyError;

      // Group failed searches by similarity
      const groupedQueries = this.groupSimilarQueries([
        ...(contentGaps || []).map(gap => ({ query: gap.gap_description, count: gap.occurrence_count })),
        ...(searchHistory || []).map(search => ({ query: search.query, count: 1 }))
      ]);

      // Generate FAQs for top query groups
      const faqs: FAQ[] = [];
      for (const group of groupedQueries.slice(0, 10)) {
        const faq = await this.generateFAQFromQuery(group);
        if (faq) {
          faqs.push(faq);
        }
      }

      return faqs;
    } catch (error) {
      console.error('Error generating FAQs from search patterns:', error);
      throw error;
    }
  }

  /**
   * Group similar queries together
   */
  private groupSimilarQueries(
    queries: { query: string; count: number }[]
  ): { representative: string; queries: string[]; totalCount: number }[] {
    const groups: { representative: string; queries: string[]; totalCount: number }[] = [];

    for (const { query, count } of queries) {
      const existingGroup = groups.find(
        group => this.calculateSimilarity(group.representative, query) > 0.7
      );

      if (existingGroup) {
        existingGroup.queries.push(query);
        existingGroup.totalCount += count;
      } else {
        groups.push({
          representative: query,
          queries: [query],
          totalCount: count
        });
      }
    }

    return groups.sort((a, b) => b.totalCount - a.totalCount);
  }

  /**
   * Generate FAQ from a query group
   */
  private async generateFAQFromQuery(
    queryGroup: { representative: string; queries: string[]; totalCount: number }
  ): Promise<FAQ | null> {
    const prompt = `
Based on the following user queries that didn't find good results in our knowledge base, generate an FAQ that would help these users.

Representative Query: ${queryGroup.representative}
Similar Queries: ${queryGroup.queries.slice(0, 5).join(', ')}
Total Occurrences: ${queryGroup.totalCount}

Generate a helpful FAQ that:
1. Addresses the user's likely intent
2. Provides a clear, actionable answer
3. Includes relevant keywords for search

Return a JSON object with:
{
  "question_ja": "Japanese question",
  "question_en": "English question", 
  "answer_ja": "Japanese answer",
  "answer_en": "English answer",
  "category": "most appropriate category",
  "keywords": ["keyword1", "keyword2"],
  "confidence_score": 0.8
}

If you cannot generate a meaningful FAQ from these queries, return null.
`;

    try {
      const response = await this.aiService.complete(prompt, { temperature: 0.3 });
      const faq = JSON.parse(response);
      
      if (!faq || faq === null) return null;

      return {
        ...faq,
        source_article_ids: []
      };
    } catch (error) {
      console.error('Error generating FAQ from query:', error);
      return null;
    }
  }

  /**
   * Save generated FAQs to database
   */
  async saveFAQs(faqs: FAQ[]): Promise<void> {
    try {
      // Check for existing FAQs to avoid duplicates
      const { data: existingFAQs, error: fetchError } = await supabase
        .from('chatbot_faq')
        .select('question_en, question_ja');

      if (fetchError) throw fetchError;

      const existingQuestions = new Set(
        (existingFAQs || []).map(faq => `${faq.question_en}|${faq.question_ja}`)
      );

      // Filter out duplicates
      const newFAQs = faqs.filter(
        faq => !existingQuestions.has(`${faq.question_en}|${faq.question_ja}`)
      );

      if (newFAQs.length === 0) {
        console.log('No new FAQs to save');
        return;
      }

      // Insert new FAQs
      const { error: insertError } = await supabase
        .from('chatbot_faq')
        .insert(
          newFAQs.map(faq => ({
            question_ja: faq.question_ja,
            question_en: faq.question_en,
            answer_ja: faq.answer_ja,
            answer_en: faq.answer_en,
            category: faq.category,
            keywords: faq.keywords,
            is_active: true
          }))
        );

      if (insertError) throw insertError;

      console.log(`Successfully saved ${newFAQs.length} new FAQs`);
    } catch (error) {
      console.error('Error saving FAQs:', error);
      throw error;
    }
  }

  /**
   * Update existing FAQs based on usage patterns
   */
  async updateFAQsBasedOnUsage(): Promise<void> {
    try {
      // Fetch FAQs with low usage
      const { data: lowUsageFAQs, error } = await supabase
        .from('chatbot_faq')
        .select('*')
        .lt('usage_count', 5)
        .eq('is_active', true)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (error) throw error;

      // Consider deactivating very low usage FAQs
      const toDeactivate = (lowUsageFAQs || []).filter(
        faq => faq.usage_count === 0 && 
        new Date(faq.created_at).getTime() < Date.now() - 14 * 24 * 60 * 60 * 1000
      );

      if (toDeactivate.length > 0) {
        const { error: updateError } = await supabase
          .from('chatbot_faq')
          .update({ is_active: false })
          .in('id', toDeactivate.map(faq => faq.id));

        if (updateError) throw updateError;
        console.log(`Deactivated ${toDeactivate.length} low-usage FAQs`);
      }
    } catch (error) {
      console.error('Error updating FAQs based on usage:', error);
      throw error;
    }
  }

  /**
   * Generate category-specific FAQs
   */
  async generateCategoryFAQs(category: string): Promise<FAQ[]> {
    return this.generateFAQsFromKB({
      categories: [category],
      maxFAQsPerArticle: 5,
      minConfidence: 0.8
    });
  }
}