import { createClient } from '@supabase/supabase-js';
import * as speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { Database } from '../database.types';
import { encrypt, decrypt } from './encryption-service';

export interface MFAConfiguration {
  id: string;
  user_id: string;
  method: 'totp' | 'sms' | 'email';
  is_primary: boolean;
  is_verified: boolean;
  secret_encrypted?: string;
  phone_number?: string;
  backup_codes?: string[];
  created_at: string;
  updated_at: string;
  verified_at?: string;
  last_used_at?: string;
}

export interface MFASession {
  id: string;
  user_id: string;
  session_token: string;
  is_verified: boolean;
  challenge_code?: string;
  method: 'totp' | 'sms' | 'email';
  attempts: number;
  expires_at: string;
  created_at: string;
}

export class MFAService {
  private supabase: ReturnType<typeof createClient<Database>>;
  private readonly MAX_ATTEMPTS = 5;
  private readonly SESSION_DURATION = 10 * 60 * 1000; // 10 minutes
  private readonly CODE_LENGTH = 6;
  private readonly BACKUP_CODE_LENGTH = 8;
  private readonly BACKUP_CODE_COUNT = 10;

  constructor(supabaseClient: ReturnType<typeof createClient<Database>>) {
    this.supabase = supabaseClient;
  }

  /**
   * Initialize TOTP setup for a user
   */
  async setupTOTP(userId: string, userEmail: string): Promise<{
    secret: string;
    qrCode: string;
    backupCodes: string[];
  }> {
    try {
      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `ITSync (${userEmail})`,
        issuer: 'ITSync',
        length: 32
      });

      // Generate QR code
      const otpauthUrl = speakeasy.otpauthURL({
        secret: secret.ascii,
        label: `ITSync:${userEmail}`,
        issuer: 'ITSync',
        algorithm: 'sha256'
      });

      const qrCode = await QRCode.toDataURL(otpauthUrl);

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Store configuration (unverified)
      const encryptedSecret = await encrypt(secret.base32);
      const encryptedBackupCodes = await Promise.all(
        backupCodes.map(code => encrypt(code))
      );

      await this.supabase.from('mfa_configurations').upsert({
        user_id: userId,
        method: 'totp',
        secret_encrypted: encryptedSecret,
        backup_codes: encryptedBackupCodes,
        is_verified: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      // Log setup initiation
      await this.logMFAEvent(userId, 'setup_initiated', 'totp', true);

      return {
        secret: secret.base32,
        qrCode,
        backupCodes
      };
    } catch (error) {
      await this.logMFAEvent(userId, 'setup_initiated', 'totp', false, error.message);
      throw error;
    }
  }

  /**
   * Verify TOTP setup with user-provided code
   */
  async verifyTOTPSetup(userId: string, code: string): Promise<boolean> {
    try {
      const { data: config } = await this.supabase
        .from('mfa_configurations')
        .select('*')
        .eq('user_id', userId)
        .eq('method', 'totp')
        .single();

      if (!config || !config.secret_encrypted) {
        throw new Error('TOTP configuration not found');
      }

      const secret = await decrypt(config.secret_encrypted);
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token: code,
        algorithm: 'sha256',
        window: 2
      });

      if (verified) {
        // Mark as verified and primary
        await this.supabase
          .from('mfa_configurations')
          .update({
            is_verified: true,
            is_primary: true,
            verified_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', config.id);

        // Enable MFA for user
        await this.supabase
          .from('profiles')
          .update({
            mfa_enabled: true,
            updated_at: new Date().toISOString()
          })
          .eq('auth_id', userId);

        await this.logMFAEvent(userId, 'setup_completed', 'totp', true);
        return true;
      }

      await this.logMFAEvent(userId, 'setup_failed', 'totp', false, 'Invalid code');
      return false;
    } catch (error) {
      await this.logMFAEvent(userId, 'setup_failed', 'totp', false, error.message);
      throw error;
    }
  }

  /**
   * Create MFA challenge session
   */
  async createChallenge(userId: string): Promise<{
    sessionToken: string;
    methods: Array<{ method: string; isPrimary: boolean }>;
  }> {
    try {
      // Get user's MFA methods
      const { data: configs } = await this.supabase
        .from('mfa_configurations')
        .select('method, is_primary')
        .eq('user_id', userId)
        .eq('is_verified', true);

      if (!configs || configs.length === 0) {
        throw new Error('No MFA methods configured');
      }

      // Create session
      const sessionToken = this.generateSessionToken();
      const expiresAt = new Date(Date.now() + this.SESSION_DURATION);

      await this.supabase.from('mfa_sessions').insert({
        user_id: userId,
        session_token: sessionToken,
        method: configs.find(c => c.is_primary)?.method || configs[0].method,
        expires_at: expiresAt.toISOString(),
        attempts: 0,
        is_verified: false
      });

      return {
        sessionToken,
        methods: configs.map(c => ({
          method: c.method,
          isPrimary: c.is_primary
        }))
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Verify MFA code
   */
  async verifyCode(sessionToken: string, code: string): Promise<{
    success: boolean;
    attemptsRemaining?: number;
    lockedOut?: boolean;
  }> {
    try {
      // Get session
      const { data: session } = await this.supabase
        .from('mfa_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .single();

      if (!session) {
        throw new Error('Invalid session');
      }

      // Check expiration
      if (new Date(session.expires_at) < new Date()) {
        throw new Error('Session expired');
      }

      // Check lockout
      if (session.attempts >= this.MAX_ATTEMPTS) {
        await this.logMFAEvent(session.user_id, 'verify_failed', session.method, false, 'Locked out');
        return { success: false, lockedOut: true };
      }

      // Verify based on method
      let verified = false;
      
      if (session.method === 'totp') {
        verified = await this.verifyTOTP(session.user_id, code);
      } else if (session.method === 'sms' || session.method === 'email') {
        verified = await this.verifyOTP(session, code);
      }

      if (verified) {
        // Mark session as verified
        await this.supabase
          .from('mfa_sessions')
          .update({ is_verified: true })
          .eq('id', session.id);

        // Update last used
        await this.supabase
          .from('mfa_configurations')
          .update({ last_used_at: new Date().toISOString() })
          .eq('user_id', session.user_id)
          .eq('method', session.method);

        await this.logMFAEvent(session.user_id, 'verify_success', session.method, true);
        return { success: true };
      } else {
        // Increment attempts
        const newAttempts = session.attempts + 1;
        await this.supabase
          .from('mfa_sessions')
          .update({ attempts: newAttempts })
          .eq('id', session.id);

        await this.logMFAEvent(session.user_id, 'verify_failed', session.method, false);
        return {
          success: false,
          attemptsRemaining: this.MAX_ATTEMPTS - newAttempts
        };
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Verify TOTP code
   */
  private async verifyTOTP(userId: string, code: string): Promise<boolean> {
    const { data: config } = await this.supabase
      .from('mfa_configurations')
      .select('secret_encrypted')
      .eq('user_id', userId)
      .eq('method', 'totp')
      .eq('is_verified', true)
      .single();

    if (!config || !config.secret_encrypted) {
      return false;
    }

    const secret = await decrypt(config.secret_encrypted);
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token: code,
      algorithm: 'sha256',
      window: 2
    });
  }

  /**
   * Verify OTP code (SMS/Email)
   */
  private async verifyOTP(session: MFASession, code: string): Promise<boolean> {
    if (!session.challenge_code) {
      return false;
    }

    const storedCode = await decrypt(session.challenge_code);
    return storedCode === code;
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < this.BACKUP_CODE_COUNT; i++) {
      codes.push(this.generateRandomCode(this.BACKUP_CODE_LENGTH));
    }
    return codes;
  }

  /**
   * Generate random code
   */
  private generateRandomCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  }

  /**
   * Generate session token
   */
  private generateSessionToken(): string {
    return `mfa_${Date.now()}_${this.generateRandomCode(32)}`;
  }

  /**
   * Log MFA event
   */
  private async logMFAEvent(
    userId: string,
    eventType: string,
    method: string,
    success: boolean,
    errorMessage?: string,
    metadata?: any
  ): Promise<void> {
    try {
      await this.supabase.from('mfa_audit_logs').insert({
        user_id: userId,
        event_type: eventType,
        method,
        success,
        error_message: errorMessage,
        metadata,
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log MFA event:', error);
    }
  }

  /**
   * Check if user has MFA enabled
   */
  async checkMFAStatus(userId: string): Promise<{
    enabled: boolean;
    required: boolean;
    methods: string[];
  }> {
    const { data: profile } = await this.supabase
      .from('profiles')
      .select('mfa_enabled, require_mfa_for_role')
      .eq('auth_id', userId)
      .single();

    const { data: configs } = await this.supabase
      .from('mfa_configurations')
      .select('method')
      .eq('user_id', userId)
      .eq('is_verified', true);

    return {
      enabled: profile?.mfa_enabled || false,
      required: profile?.require_mfa_for_role || false,
      methods: configs?.map(c => c.method) || []
    };
  }
}
