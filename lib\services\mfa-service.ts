import { createClient } from '@supabase/supabase-js';
import * as speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { Database } from '../database.types';
import { encrypt, decrypt } from './encryption-service';

export interface MFAConfiguration {
  id: string;
  user_id: string;
  method: 'totp' | 'sms' | 'email';
  is_primary: boolean;
  is_verified: boolean;
  secret_encrypted?: string;
  phone_number?: string;
  backup_codes?: string[];
  created_at: string;
  updated_at: string;
  verified_at?: string;
  last_used_at?: string;
}

export interface MFASession {
  id: string;
  user_id: string;
  session_token: string;
  is_verified: boolean;
  challenge_code?: string;
  method: 'totp' | 'sms' | 'email';
  attempts: number;
  expires_at: string;
  created_at: string;
}

export class MFAService {
  private supabase: ReturnType<typeof createClient<Database>>;
  private readonly MAX_ATTEMPTS = 5;
  private readonly SESSION_DURATION = 10 * 60 * 1000; // 10 minutes
  private readonly CODE_LENGTH = 6;
  private readonly BACKUP_CODE_LENGTH = 8;
  private readonly BACKUP_CODE_COUNT = 10;

  constructor(supabaseClient: ReturnType<typeof createClient<Database>>) {
    this.supabase = supabaseClient;
    console.log('[MFA_SERVICE] MFAService instantiated.');
  }

  /**
   * Initialize TOTP setup for a user
   */
  async setupTOTP(userId: string, userEmail: string): Promise<{
    secret: string;
    qrCode: string;
    backupCodes: string[];
  }> {
    try {
      console.log(`[MFA_SERVICE] setupTOTP initiated for userId: ${userId}, email: ${userEmail}`);
      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `ITSync (${userEmail})`,
        issuer: 'ITSync',
        length: 32
      });

      // Generate QR code
      const otpauthUrl = speakeasy.otpauthURL({
        secret: secret.ascii,
        label: `ITSync:${userEmail}`,
        issuer: 'ITSync',
        algorithm: 'sha256'
      });

      const qrCode = await QRCode.toDataURL(otpauthUrl);
      console.log(`[MFA_SERVICE] TOTP Secret (Base32) generated for userId ${userId}: ${secret.base32.substring(0, 4)}... (length: ${secret.base32.length})`);

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();
      console.log(`[MFA_SERVICE] Generated ${backupCodes.length} backup codes for userId: ${userId}`);

      // Store configuration (unverified)
      const encryptedSecret = await encrypt(secret.base32);
      const encryptedBackupCodes = await Promise.all(
        backupCodes.map(code => encrypt(code))
      );

      const { error: upsertError } = await this.supabase.from('mfa_configurations').upsert({
        user_id: userId,
        method: 'totp',
        secret_encrypted: encryptedSecret,
        backup_codes: encryptedBackupCodes,
        is_verified: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      if (upsertError) {
        console.error(`[MFA_SERVICE_ERROR] Error upserting MFA configuration for userId ${userId}:`, upsertError);
        await this.logMFAEvent(userId, 'setup_initiated', 'totp', false, upsertError.message);
        throw upsertError;
      }
      console.log(`[MFA_SERVICE] Stored unverified TOTP configuration for userId: ${userId}`);

      // Log setup initiation
      await this.logMFAEvent(userId, 'setup_initiated', 'totp', true);

      return {
        secret: secret.base32, // For display to user, not for long-term storage unencrypted
        qrCode,
        backupCodes
      };
    } catch (error) {
      console.error(`[MFA_SERVICE_ERROR] Error in setupTOTP for userId ${userId}:`, error instanceof Error ? error.message : error);
      await this.logMFAEvent(userId, 'setup_initiated', 'totp', false, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Verify TOTP setup with user-provided code
   */
  async verifyTOTPSetup(userId: string, code: string): Promise<boolean> {
    try {
      console.log(`[MFA_SERVICE] verifyTOTPSetup initiated for userId: ${userId} with code (length: ${code?.length})`);
      const { data: config, error: configError } = await this.supabase
        .from('mfa_configurations')
        .select('*')
        .eq('user_id', userId)
        .eq('method', 'totp')
        .single();

      if (configError) {
        console.error(`[MFA_SERVICE_ERROR] Error fetching TOTP config for userId ${userId} during setup verification:`, configError);
        throw configError;
      }

      if (!config || !config.secret_encrypted) {
        console.warn(`[MFA_SERVICE_WARN] TOTP configuration not found for userId: ${userId} during setup verification.`);
        await this.logMFAEvent(userId, 'setup_failed', 'totp', false, 'Configuration not found');
        throw new Error('TOTP configuration not found');
      }
      console.log(`[MFA_SERVICE] Found TOTP config for userId: ${userId} to verify setup.`);

      const secret = await decrypt(config.secret_encrypted);
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token: code,
        algorithm: 'sha256',
        window: 2 // Allow for a 2*30s time window
      });
      console.log(`[MFA_SERVICE] TOTP setup verification for userId ${userId} with code (length: ${code?.length}) result: ${verified}`);

      if (verified) {
        console.log(`[MFA_SERVICE] TOTP setup successfully verified for userId: ${userId}. Updating records.`);
        // Mark as verified and primary
        const { error: updateConfigError } = await this.supabase
          .from('mfa_configurations')
          .update({
            is_verified: true,
            is_primary: true,
            verified_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', config.id);
        
        if (updateConfigError) {
          console.error(`[MFA_SERVICE_ERROR] Error updating MFA config to verified for userId ${userId}:`, updateConfigError);
          // Log event but proceed to throw, as this is a critical step
          await this.logMFAEvent(userId, 'setup_completed', 'totp', false, `Failed to update mfa_configurations: ${updateConfigError.message}`);
          throw updateConfigError;
        }

        // Enable MFA for user
        const { error: updateProfileError } = await this.supabase
          .from('profiles')
          .update({
            mfa_enabled: true,
            updated_at: new Date().toISOString()
          })
          .eq('auth_id', userId);

        if (updateProfileError) {
          console.error(`[MFA_SERVICE_ERROR] Error updating profile mfa_enabled for userId ${userId}:`, updateProfileError);
          // Log event but proceed to throw
          await this.logMFAEvent(userId, 'setup_completed', 'totp', false, `Failed to update profiles: ${updateProfileError.message}`);
          throw updateProfileError;
        }
        console.log(`[MFA_SERVICE] Profile mfa_enabled updated for userId: ${userId}`);

        await this.logMFAEvent(userId, 'setup_completed', 'totp', true);
        return true;
      }

      console.warn(`[MFA_SERVICE_WARN] TOTP setup verification failed for userId: ${userId}. Code invalid.`);
      await this.logMFAEvent(userId, 'setup_failed', 'totp', false, 'Invalid code');
      return false;
    } catch (error) {
      console.error(`[MFA_SERVICE_ERROR] Error in verifyTOTPSetup for userId ${userId}:`, error instanceof Error ? error.message : error);
      await this.logMFAEvent(userId, 'setup_failed', 'totp', false, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Create MFA challenge session
   */
  async createChallenge(userId: string): Promise<{
    sessionToken: string;
    methods: Array<{ method: string; isPrimary: boolean }>;
  }> {
    try {
      console.log(`[MFA_SERVICE] createChallenge initiated for userId: ${userId}`);
      // Get user's MFA methods
      const { data: configs, error: configsError } = await this.supabase
        .from('mfa_configurations')
        .select('method, is_primary')
        .eq('user_id', userId)
        .eq('is_verified', true);

      if (configsError) {
        console.error(`[MFA_SERVICE_ERROR] Error fetching MFA configurations for userId ${userId} in createChallenge:`, configsError);
        throw configsError;
      }

      if (!configs || configs.length === 0) {
        console.warn(`[MFA_SERVICE_WARN] No verified MFA methods found for userId: ${userId} in createChallenge.`);
        throw new Error('No MFA methods configured');
      }
      console.log(`[MFA_SERVICE] Found ${configs.length} verified MFA methods for userId: ${userId}. Primary: ${configs.find(c => c.is_primary)?.method || 'N/A'}`);

      // Create session
      const sessionToken = this.generateSessionToken();
      const expiresAt = new Date(Date.now() + this.SESSION_DURATION);
      const primaryMethod = configs.find(c => c.is_primary)?.method || configs[0].method;

      const { error: insertSessionError } = await this.supabase.from('mfa_sessions').insert({
        user_id: userId,
        session_token: sessionToken,
        method: primaryMethod, // Use determined primary method
        expires_at: expiresAt.toISOString(),
        attempts: 0,
        is_verified: false
      });

      if (insertSessionError) {
        console.error(`[MFA_SERVICE_ERROR] Error inserting MFA session for userId ${userId}:`, insertSessionError);
        throw insertSessionError;
      }
      console.log(`[MFA_SERVICE] MFA challenge session created for userId: ${userId}. Token (length: ${sessionToken?.length}), Method: ${primaryMethod}`);

      return {
        sessionToken,
        methods: configs.map(c => ({
          method: c.method,
          isPrimary: c.is_primary
        }))
      };
    } catch (error) {
      console.error(`[MFA_SERVICE_ERROR] Error in createChallenge for userId ${userId}:`, error instanceof Error ? error.message : error);
      throw error;
    }
  }

  /**
   * Verify MFA code
   */
  async verifyCode(sessionToken: string, code: string): Promise<{
    success: boolean;
    attemptsRemaining?: number;
    lockedOut?: boolean;
  }> {
    try {
      console.log(`[MFA_SERVICE] verifyCode initiated for sessionToken (length: ${sessionToken?.length}) with code (length: ${code?.length})`);
      // Get session
      const { data: session, error: sessionError } = await this.supabase
        .from('mfa_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .single();

      if (sessionError) {
        console.error(`[MFA_SERVICE_ERROR] Error fetching MFA session for token (length: ${sessionToken?.length}):`, sessionError);
        throw sessionError;
      }

      if (!session) {
        console.warn(`[MFA_SERVICE_WARN] Invalid MFA session token provided (length: ${sessionToken?.length}).`);
        await this.logMFAEvent('unknown_user_session_token', 'verify_failed', 'unknown', false, 'Invalid session token'); // Log with a placeholder user ID if session is not found
        throw new Error('Invalid session');
      }
      console.log(`[MFA_SERVICE] Found MFA session for userId: ${session.user_id}, method: ${session.method}`);

      // Check expiration
      if (new Date(session.expires_at) < new Date()) {
        console.warn(`[MFA_SERVICE_WARN] MFA session expired for userId: ${session.user_id}. ExpiresAt: ${session.expires_at}`);
        await this.logMFAEvent(session.user_id, 'verify_failed', session.method, false, 'Session expired');
        throw new Error('Session expired');
      }

      // Check lockout
      if (session.attempts >= this.MAX_ATTEMPTS) {
        console.warn(`[MFA_SERVICE_WARN] MFA locked out for userId: ${session.user_id}. Attempts: ${session.attempts}`);
        await this.logMFAEvent(session.user_id, 'verify_failed', session.method, false, 'Locked out');
        return { success: false, lockedOut: true };
      }
      console.log(`[MFA_SERVICE] MFA session checks passed (not expired, not locked). UserID: ${session.user_id}, Attempts: ${session.attempts}`);

      // Verify based on method
      let verified = false;
      console.log(`[MFA_SERVICE] Verifying code using method: ${session.method} for userId: ${session.user_id}`);
      
      if (session.method === 'totp') {
        verified = await this.verifyTOTP(session.user_id, code);
      } else if (session.method === 'sms' || session.method === 'email') {
        // Assuming verifyOTP handles logging internally if needed for specific OTP logic
        verified = await this.verifyOTP(session, code);
      } else {
        console.error(`[MFA_SERVICE_ERROR] Unknown MFA method in session: ${session.method} for userId: ${session.user_id}`);
        await this.logMFAEvent(session.user_id, 'verify_failed', session.method, false, 'Unknown MFA method');
        throw new Error('Unknown MFA method in session');
      }
      console.log(`[MFA_SERVICE] Code verification result for userId ${session.user_id}, method ${session.method}: ${verified}`);

      if (verified) {
        console.log(`[MFA_SERVICE] MFA code successfully verified for userId: ${session.user_id}. Updating session and config.`);
        // Mark session as verified
        const { error: updateSessionError } = await this.supabase
          .from('mfa_sessions')
          .update({ is_verified: true })
          .eq('id', session.id);
        if (updateSessionError) {
           console.error(`[MFA_SERVICE_ERROR] Error marking MFA session verified for userId ${session.user_id}:`, updateSessionError);
           // Log and potentially throw or handle gracefully
        }

        // Update last used
        const { error: updateConfigError } = await this.supabase
          .from('mfa_configurations')
          .update({ last_used_at: new Date().toISOString() })
          .eq('user_id', session.user_id)
          .eq('method', session.method);
        if (updateConfigError) {
          console.error(`[MFA_SERVICE_ERROR] Error updating last_used_at for MFA config. UserID: ${session.user_id}, Method: ${session.method}:`, updateConfigError);
        }

        await this.logMFAEvent(session.user_id, 'verify_success', session.method, true);
        return { success: true };
      } else {
        const newAttempts = session.attempts + 1;
        console.warn(`[MFA_SERVICE_WARN] MFA code verification failed for userId: ${session.user_id}. Incrementing attempts to ${newAttempts}.`);
        await this.supabase
          .from('mfa_sessions')
          .update({ attempts: newAttempts })
          .eq('id', session.id);
        // The logMFAEvent for failure is already called by the specific verifyTOTP or verifyOTP if they fail internally,
        // but we add one here for the general verifyCode failure.
        await this.logMFAEvent(session.user_id, 'verify_failed', session.method, false, 'Invalid code provided to verifyCode');
        return {
          success: false,
          attemptsRemaining: this.MAX_ATTEMPTS - newAttempts,
          lockedOut: newAttempts >= this.MAX_ATTEMPTS
        };
      }
    } catch (error) {
      console.error(`[MFA_SERVICE_ERROR] Error in verifyCode for sessionToken (length: ${sessionToken?.length}):`, error instanceof Error ? error.message : error);
      // Attempt to get userId from sessionToken if possible for logging, otherwise use a placeholder
      // This part is tricky as the session might be invalid.
      // For now, we rely on the initial log if session is found, or the placeholder if not.
      const userIdForErrorLog = 'unknown_user_verify_code_exception'; // Placeholder
      await this.logMFAEvent(userIdForErrorLog, 'verify_exception', 'unknown', false, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Verify TOTP code
   */
  private async verifyTOTP(userId: string, code: string): Promise<boolean> {
    console.log(`[MFA_SERVICE_INTERNAL] verifyTOTP called for userId: ${userId}, code (length: ${code?.length})`);
    
    // Always perform the same operations to prevent timing attacks
    let verified = false;
    let secret = '';
    
    try {
      const { data: config, error: configError } = await this.supabase
        .from('mfa_configurations')
        .select('secret_encrypted')
        .eq('user_id', userId)
        .eq('method', 'totp')
        .eq('is_verified', true)
        .single();

      if (configError || !config || !config.secret_encrypted) {
        // Use a dummy secret to maintain constant time
        secret = 'JBSWY3DPEHPK3PXP'; // Dummy base32 secret
        console.error(`[MFA_SERVICE_ERROR] Error fetching TOTP config in verifyTOTP for userId ${userId}:`, configError);
        await this.logMFAEvent(userId, 'verify_failed_internal', 'totp', false, configError ? `Error fetching config: ${configError.message}` : 'No verified config/secret');
      } else {
        secret = await decrypt(config.secret_encrypted);
      }

      // Always perform verification to maintain constant time
      const totpResult = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token: code,
        algorithm: 'sha256',
        window: 2
      });
      verified = Boolean(totpResult && config && !configError);

      console.log(`[MFA_SERVICE_INTERNAL] speakeasy.totp.verify result for userId ${userId}: ${verified}`);
      if(!verified) {
        await this.logMFAEvent(userId, 'verify_failed_internal_code', 'totp', false, 'Speakeasy verification failed');
      }
    } catch (error) {
      console.error(`[MFA_SERVICE_ERROR] Exception in verifyTOTP for userId ${userId}:`, error);
      await this.logMFAEvent(userId, 'verify_failed_internal', 'totp', false, error instanceof Error ? error.message : 'Unknown error');
    }
    
    return verified;
  }

  /**
   * Verify OTP code (SMS/Email)
   */
  private async verifyOTP(session: MFASession, code: string): Promise<boolean> {
    console.log(`[MFA_SERVICE_INTERNAL] verifyOTP called for userId: ${session.user_id}, method: ${session.method}, code (length: ${code?.length})`);
    if (!session.challenge_code) {
      console.warn(`[MFA_SERVICE_WARN] No challenge_code in session for verifyOTP. UserID: ${session.user_id}`);
      await this.logMFAEvent(session.user_id, 'verify_failed_internal', session.method, false, 'No challenge code in session');
      return false;
    }

    const storedCode = await decrypt(session.challenge_code);
    const verified = storedCode === code;
    console.log(`[MFA_SERVICE_INTERNAL] OTP comparison result for userId ${session.user_id}: ${verified} (Provided: ${code?.length}, Stored (decrypted): ${storedCode?.length})`);
     if(!verified) {
      await this.logMFAEvent(session.user_id, 'verify_failed_internal_code', session.method, false, 'OTP mismatch');
    }
    return verified;
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < this.BACKUP_CODE_COUNT; i++) {
      codes.push(this.generateRandomCode(this.BACKUP_CODE_LENGTH));
    }
    return codes;
  }

  /**
   * Generate random code
   */
  private generateRandomCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  }

  /**
   * Generate session token
   */
  private generateSessionToken(): string {
    return `mfa_${Date.now()}_${this.generateRandomCode(32)}`;
  }

  /**
   * Log MFA event
   */
  private async logMFAEvent(
    userId: string,
    eventType: string,
    method: string,
    success: boolean,
    errorMessage?: string,
    metadata?: any
  ): Promise<void> {
    try {
      await this.supabase.from('mfa_audit_logs').insert({
        user_id: userId,
        event_type: eventType,
        method,
        success,
        error_message: errorMessage,
        metadata,
        created_at: new Date().toISOString()
      });
    } catch (dbError) { // Renamed to avoid conflict with outer scope 'error' if any
      console.error('[MFA_SERVICE_ERROR] Failed to log MFA event to mfa_audit_logs:', dbError);
    }
  }

  /**
   * Check if user has MFA enabled
   */
  async checkMFAStatus(userId: string): Promise<{
    enabled: boolean;
    required: boolean;
    methods: string[];
  }> {
    console.log(`[MFA_SERVICE] checkMFAStatus called for userId: ${userId}`);
    const { data: profile, error: profileError } = await this.supabase
      .from('profiles')
      .select('mfa_enabled, require_mfa_for_role')
      .eq('auth_id', userId)
      .single();

    if (profileError) {
      console.error(`[MFA_SERVICE_ERROR] Error fetching profile for MFA status check. UserID: ${userId}:`, profileError);
      // Return a default state indicating MFA might be required or status unknown
      return { enabled: false, required: false, methods: [] };
    }

    const { data: configs, error: configsError } = await this.supabase
      .from('mfa_configurations')
      .select('method')
      .eq('user_id', userId)
      .eq('is_verified', true);

    if (configsError) {
      console.error(`[MFA_SERVICE_ERROR] Error fetching MFA configurations for status check. UserID: ${userId}:`, configsError);
    }
    
    const mfaStatus = {
      enabled: profile?.mfa_enabled || false,
      required: profile?.require_mfa_for_role || false,
      methods: configs?.map(c => c.method) || []
    };
    console.log(`[MFA_SERVICE] MFA Status for userId ${userId}:`, mfaStatus);
    return mfaStatus;
  }
}
