-- Enhanced SLA definitions table
ALTER TABLE sla_definitions 
ADD COLUMN IF NOT EXISTS escalation_time_minutes INTEGER,
ADD COLUMN IF NOT EXISTS notification_template VARCHAR(100),
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES staff(id),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create default SLA definitions if not exists
INSERT INTO sla_definitions (name, service_category_id, priority, response_time_minutes, resolution_time_minutes, business_hours_only, escalation_time_minutes)
VALUES 
  ('Critical - Generic', NULL, 'critical', 30, 240, TRUE, 15),
  ('High - Generic', NULL, 'high', 60, 480, TRUE, 30),
  ('Medium - Generic', NULL, 'medium', 240, 1440, TRUE, 120),
  ('Low - Generic', NULL, 'low', 480, 2880, TRUE, 240)
ON CONFLICT DO NOTHING;

-- Create SLA definition templates for specific service categories
DO $$
DECLARE
  v_service_category RECORD;
BEGIN
  FOR v_service_category IN 
    SELECT id, name_en FROM service_categories WHERE is_active = TRUE
  LOOP
    -- Critical priority
    INSERT INTO sla_definitions (
      name, 
      service_category_id, 
      priority, 
      response_time_minutes, 
      resolution_time_minutes, 
      business_hours_only,
      escalation_time_minutes
    ) VALUES (
      v_service_category.name_en || ' - Critical',
      v_service_category.id,
      'critical',
      CASE 
        WHEN v_service_category.name_en LIKE '%Password%' THEN 15
        WHEN v_service_category.name_en LIKE '%PC Admin%' THEN 30
        ELSE 30
      END,
      CASE 
        WHEN v_service_category.name_en LIKE '%Password%' THEN 60
        WHEN v_service_category.name_en LIKE '%PC Admin%' THEN 120
        ELSE 240
      END,
      TRUE,
      15
    ) ON CONFLICT DO NOTHING;
    
    -- High priority
    INSERT INTO sla_definitions (
      name, 
      service_category_id, 
      priority, 
      response_time_minutes, 
      resolution_time_minutes, 
      business_hours_only,
      escalation_time_minutes
    ) VALUES (
      v_service_category.name_en || ' - High',
      v_service_category.id,
      'high',
      CASE 
        WHEN v_service_category.name_en LIKE '%Password%' THEN 30
        WHEN v_service_category.name_en LIKE '%PC Admin%' THEN 60
        ELSE 60
      END,
      CASE 
        WHEN v_service_category.name_en LIKE '%Password%' THEN 240
        WHEN v_service_category.name_en LIKE '%PC Admin%' THEN 480
        ELSE 480
      END,
      TRUE,
      30
    ) ON CONFLICT DO NOTHING;
    
    -- Medium priority
    INSERT INTO sla_definitions (
      name, 
      service_category_id, 
      priority, 
      response_time_minutes, 
      resolution_time_minutes, 
      business_hours_only,
      escalation_time_minutes
    ) VALUES (
      v_service_category.name_en || ' - Medium',
      v_service_category.id,
      'medium',
      240,
      1440,
      TRUE,
      120
    ) ON CONFLICT DO NOTHING;
    
    -- Low priority
    INSERT INTO sla_definitions (
      name, 
      service_category_id, 
      priority, 
      response_time_minutes, 
      resolution_time_minutes, 
      business_hours_only,
      escalation_time_minutes
    ) VALUES (
      v_service_category.name_en || ' - Low',
      v_service_category.id,
      'low',
      480,
      2880,
      TRUE,
      240
    ) ON CONFLICT DO NOTHING;
  END LOOP;
END $$;

-- Add additional columns to sla_tracking if not exists
ALTER TABLE sla_tracking
ADD COLUMN IF NOT EXISTS escalation_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_escalation_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS assigned_to UUID REFERENCES staff(id),
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_sla_tracking_status_dates 
ON sla_tracking(status, target_response_date, target_resolution_date);

CREATE INDEX IF NOT EXISTS idx_sla_tracking_workflow_instance 
ON sla_tracking(workflow_instance_id);

-- Create view for SLA performance dashboard
CREATE OR REPLACE VIEW sla_performance_summary AS
WITH sla_metrics AS (
  SELECT 
    st.id,
    st.workflow_instance_id,
    st.status,
    st.created_at,
    st.actual_response_date,
    st.actual_resolution_date,
    st.target_response_date,
    st.target_resolution_date,
    sd.priority,
    sd.service_category_id,
    sd.name as sla_name,
    wi.request_id,
    rf.department_id,
    rf.priority as request_priority,
    CASE 
      WHEN st.actual_response_date IS NOT NULL THEN
        EXTRACT(EPOCH FROM (st.actual_response_date - st.created_at)) / 60
      ELSE NULL
    END as actual_response_minutes,
    CASE 
      WHEN st.actual_resolution_date IS NOT NULL THEN
        EXTRACT(EPOCH FROM (st.actual_resolution_date - st.created_at)) / 60
      ELSE NULL
    END as actual_resolution_minutes,
    sd.response_time_minutes as target_response_minutes,
    sd.resolution_time_minutes as target_resolution_minutes
  FROM sla_tracking st
  JOIN sla_definitions sd ON st.sla_definition_id = sd.id
  JOIN workflow_instances wi ON st.workflow_instance_id = wi.id
  LEFT JOIN request_forms rf ON wi.request_id = rf.id
)
SELECT 
  DATE_TRUNC('day', created_at) as date,
  priority,
  COUNT(*) as total_requests,
  COUNT(CASE WHEN status != 'breached' THEN 1 END) as met_sla,
  COUNT(CASE WHEN status = 'breached' THEN 1 END) as breached_sla,
  COUNT(CASE WHEN status = 'at_risk' THEN 1 END) as at_risk,
  AVG(actual_response_minutes) as avg_response_minutes,
  AVG(actual_resolution_minutes) as avg_resolution_minutes,
  CASE 
    WHEN COUNT(*) > 0 THEN 
      (COUNT(CASE WHEN status != 'breached' THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
    ELSE 0
  END as compliance_rate
FROM sla_metrics
GROUP BY DATE_TRUNC('day', created_at), priority;

-- Create function to check and update SLA status
CREATE OR REPLACE FUNCTION check_sla_status() RETURNS void AS $$
DECLARE
  v_tracking RECORD;
  v_new_status VARCHAR(20);
  v_now TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
  -- Check all active SLA trackings
  FOR v_tracking IN 
    SELECT st.*, sd.escalation_time_minutes
    FROM sla_tracking st
    JOIN sla_definitions sd ON st.sla_definition_id = sd.id
    WHERE st.actual_resolution_date IS NULL
  LOOP
    v_new_status := v_tracking.status;
    
    -- Check response SLA
    IF v_tracking.actual_response_date IS NULL THEN
      IF v_now > v_tracking.target_response_date THEN
        v_new_status := 'breached';
      ELSIF v_now > (v_tracking.target_response_date - INTERVAL '1 hour') THEN
        v_new_status := 'at_risk';
      END IF;
    END IF;
    
    -- Check resolution SLA
    IF v_new_status != 'breached' AND v_tracking.actual_resolution_date IS NULL THEN
      IF v_now > v_tracking.target_resolution_date THEN
        v_new_status := 'breached';
      ELSIF v_now > (v_tracking.target_resolution_date - INTERVAL '2 hours') THEN
        v_new_status := 'at_risk';
      END IF;
    END IF;
    
    -- Update status if changed
    IF v_new_status != v_tracking.status THEN
      UPDATE sla_tracking 
      SET status = v_new_status,
          updated_at = v_now
      WHERE id = v_tracking.id;
      
      -- Log status change
      INSERT INTO audit_logs (
        user_id,
        action,
        details,
        created_at
      ) VALUES (
        '00000000-0000-0000-0000-000000000000'::UUID, -- System user
        'SLA_STATUS_CHANGED',
        jsonb_build_object(
          'sla_tracking_id', v_tracking.id,
          'old_status', v_tracking.status,
          'new_status', v_new_status
        ),
        v_now
      );
    END IF;
    
    -- Check for escalation
    IF v_tracking.escalation_time_minutes IS NOT NULL AND 
       (v_tracking.last_escalation_at IS NULL OR 
        v_now > v_tracking.last_escalation_at + (v_tracking.escalation_time_minutes || ' minutes')::INTERVAL) THEN
      
      UPDATE sla_tracking
      SET escalation_count = escalation_count + 1,
          last_escalation_at = v_now
      WHERE id = v_tracking.id;
      
      -- Trigger escalation (this would integrate with notification system)
      INSERT INTO audit_logs (
        user_id,
        action,
        details,
        created_at
      ) VALUES (
        '00000000-0000-0000-0000-000000000000'::UUID,
        'SLA_ESCALATED',
        jsonb_build_object(
          'sla_tracking_id', v_tracking.id,
          'escalation_count', v_tracking.escalation_count + 1
        ),
        v_now
      );
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to check SLA status (this would be run by a cron job or pg_cron)
-- In production, you would use pg_cron or an external scheduler
-- Example: SELECT cron.schedule('check-sla-status', '*/5 * * * *', 'SELECT check_sla_status();');

-- Grant permissions
GRANT SELECT ON sla_performance_summary TO authenticated;
GRANT EXECUTE ON FUNCTION check_sla_status() TO authenticated;
