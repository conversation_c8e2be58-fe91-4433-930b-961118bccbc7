import { supabase } from '@/lib/supabase'
import type { Database } from '@/lib/database.types'

type UserPattern = Database['public']['Tables']['user_form_patterns']['Row']
type PatternSequence = Database['public']['Tables']['pattern_sequences']['Row']
type DepartmentPattern = Database['public']['Tables']['department_patterns']['Row']

export interface PredictionSuggestion {
  value: string
  confidence: number
  source: 'personal' | 'department' | 'ai' | 'sequence'
}

export interface FieldPrediction {
  fieldName: string
  suggestions: PredictionSuggestion[]
}

export interface SequencePrediction {
  fields: Record<string, string>
  confidence: number
}

export interface PredictionPreferences {
  enablePredictions: boolean
  enableDepartmentPatterns: boolean
  enablePersonalPatterns: boolean
  minConfidenceThreshold: number
}

export class PredictiveFormService {
  private static instance: PredictiveFormService
  private cache = new Map<string, { data: any; timestamp: number }>()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  private constructor() {}

  static getInstance(): PredictiveFormService {
    if (!PredictiveFormService.instance) {
      PredictiveFormService.instance = new PredictiveFormService()
    }
    return PredictiveFormService.instance
  }

  async getPredictions(
    formType: string,
    currentField: string,
    formData: Record<string, any>,
    includeSequences = false
  ): Promise<{
    predictions: FieldPrediction[]
    sequences: SequencePrediction[]
  }> {
    const cacheKey = `${formType}-${currentField}-${JSON.stringify(formData)}`
    const cached = this.cache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }

    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session) {
        throw new Error('User not authenticated')
      }

      const response = await supabase.functions.invoke('predict-form-completion', {
        body: {
          formType,
          currentField,
          formData,
          includeSequences
        }
      })

      if (response.error) {
        throw response.error
      }

      const result = response.data as {
        predictions: FieldPrediction[]
        sequences: SequencePrediction[]
      }

      this.cache.set(cacheKey, { data: result, timestamp: Date.now() })
      return result
    } catch (error) {
      console.error('Prediction service error:', error)
      return { predictions: [], sequences: [] }
    }
  }

  async recordFieldUsage(
    formType: string,
    fieldName: string,
    fieldValue: string,
    wasAccepted: boolean
  ) {
    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session) return

      // Update or create pattern
      const { data: existingPattern } = await supabase
        .from('user_form_patterns')
        .select('*')
        .eq('user_id', session.session.user.id)
        .eq('form_type', formType)
        .eq('field_name', fieldName)
        .eq('field_value', fieldValue)
        .single()

      if (existingPattern) {
        await supabase
          .from('user_form_patterns')
          .update({
            usage_count: existingPattern.usage_count + 1,
            last_used: new Date().toISOString(),
            confidence_score: wasAccepted 
              ? Math.min(existingPattern.confidence_score + 0.05, 1.0)
              : Math.max(existingPattern.confidence_score - 0.1, 0.0)
          })
          .eq('id', existingPattern.id)
      } else {
        await supabase
          .from('user_form_patterns')
          .insert({
            user_id: session.session.user.id,
            form_type: formType,
            field_name: fieldName,
            field_value: fieldValue,
            confidence_score: wasAccepted ? 0.7 : 0.3
          })
      }
    } catch (error) {
      console.error('Error recording field usage:', error)
    }
  }

  async recordSequenceUsage(
    formType: string,
    fieldSequence: Record<string, string>
  ) {
    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session || Object.keys(fieldSequence).length < 2) return

      const sequenceHash = await this.calculateSequenceHash(fieldSequence)

      const { data: existingSequence } = await supabase
        .from('pattern_sequences')
        .select('*')
        .eq('user_id', session.session.user.id)
        .eq('form_type', formType)
        .eq('sequence_hash', sequenceHash)
        .single()

      if (existingSequence) {
        await supabase
          .from('pattern_sequences')
          .update({
            occurrence_count: existingSequence.occurrence_count + 1,
            last_occurred: new Date().toISOString(),
            confidence_score: Math.min(
              existingSequence.confidence_score + 0.02,
              1.0
            )
          })
          .eq('id', existingSequence.id)
      } else {
        await supabase
          .from('pattern_sequences')
          .insert({
            user_id: session.session.user.id,
            form_type: formType,
            sequence_hash: sequenceHash,
            field_sequence: fieldSequence,
            confidence_score: 0.5
          })
      }
    } catch (error) {
      console.error('Error recording sequence usage:', error)
    }
  }

  async getPreferences(): Promise<PredictionPreferences> {
    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session) {
        return this.getDefaultPreferences()
      }

      const { data } = await supabase
        .from('prediction_preferences')
        .select('*')
        .eq('user_id', session.session.user.id)
        .single()

      if (!data) {
        return this.getDefaultPreferences()
      }

      return {
        enablePredictions: data.enable_predictions,
        enableDepartmentPatterns: data.enable_department_patterns,
        enablePersonalPatterns: data.enable_personal_patterns,
        minConfidenceThreshold: data.min_confidence_threshold
      }
    } catch (error) {
      console.error('Error getting preferences:', error)
      return this.getDefaultPreferences()
    }
  }

  async updatePreferences(preferences: Partial<PredictionPreferences>) {
    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session) return

      await supabase
        .from('prediction_preferences')
        .upsert({
          user_id: session.session.user.id,
          enable_predictions: preferences.enablePredictions,
          enable_department_patterns: preferences.enableDepartmentPatterns,
          enable_personal_patterns: preferences.enablePersonalPatterns,
          min_confidence_threshold: preferences.minConfidenceThreshold
        })
    } catch (error) {
      console.error('Error updating preferences:', error)
      throw error
    }
  }

  async submitFeedback(
    patternId: string,
    predictionType: 'single_field' | 'sequence',
    wasAccepted: boolean,
    alternativeValue?: string
  ) {
    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session) return

      await supabase
        .from('prediction_feedback')
        .insert({
          user_id: session.session.user.id,
          pattern_id: patternId,
          prediction_type: predictionType,
          was_accepted: wasAccepted,
          alternative_value: alternativeValue
        })
    } catch (error) {
      console.error('Error submitting feedback:', error)
    }
  }

  private getDefaultPreferences(): PredictionPreferences {
    return {
      enablePredictions: true,
      enableDepartmentPatterns: true,
      enablePersonalPatterns: true,
      minConfidenceThreshold: 0.7
    }
  }

  private async calculateSequenceHash(data: Record<string, any>): Promise<string> {
    const sortedData = Object.keys(data).sort().reduce((acc, key) => {
      acc[key] = data[key]
      return acc
    }, {} as Record<string, any>)
    
    const encoder = new TextEncoder()
    const buffer = encoder.encode(JSON.stringify(sortedData))
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  clearCache() {
    this.cache.clear()
  }
}

export const predictiveFormService = PredictiveFormService.getInstance()
