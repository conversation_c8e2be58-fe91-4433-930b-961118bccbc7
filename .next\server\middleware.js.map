{"version": 3, "file": "middleware.js", "mappings": "oFAAA,4DCAA,yLCYO,eAAeA,EAAWC,CAAgB,EAC/C,IAAMC,EAAMC,EAAAA,EAAYA,CAACC,IAAI,SAQ3B,mBAAgD,GAAxCC,OAAO,CAACC,QAAQ,CACfH,EAAAA,EAAYA,CAACI,QAAQ,CAAC,IAAIC,IAAI,kBAAmBP,EAAIQ,GAAG,GAE1DP,CA2FX,CAEO,IAAMQ,EAAS,CACpBC,QAAS,CAQP,oFAEJ,EAAC,OC1HD,OACA,GAAO,CAAI,EAEX,0BACA,gBACA,wBACA,+BAAuC,EAAK,2DAkC7B,cACf,MAAW,OAAO,EAClB,KACA,OACA,cAjCA,QACA,IACA,oBACA,CAAU,SASV,WACA,iBACA,qBAWA,OAVA,MAAkB,QAAiC,IACnD,OACA,gBACA,+CACA,CAAa,EACb,0BACA,wBACA,uBACA,uBACA,CAAa,EACb,CACA,CACA,CAOA,CAAK,CACL", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./middleware.ts", "webpack://_N_E/"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'\nimport { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\n// Session timeout configuration (in milliseconds)\nconst SESSION_TIMEOUTS = {\n  ADMIN: 2 * 60 * 60 * 1000, // 2 hours for admin roles\n  REGULAR: 24 * 60 * 60 * 1000, // 24 hours for regular users\n};\n\nconst ADMIN_ROLES = ['Global Administrator', 'Web App System Administrator', 'Department Administrator'];\n\nexport async function middleware(req: NextRequest) {\n  const res = NextResponse.next()\n  \n  // Check if Supabase is properly configured\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n  \n  if (!supabaseUrl || !supabaseKey || supabaseUrl === 'your_supabase_project_url') {\n    // If Supabase is not configured, show error page\n    if (req.nextUrl.pathname !== '/setup-required') {\n      return NextResponse.redirect(new URL('/setup-required', req.url))\n    }\n    return res\n  }\n\n  const supabase = createMiddlewareClient({ req, res })\n\n  const {\n    data: { session },\n  } = await supabase.auth.getSession()\n\n  // Public routes that don't require authentication\n  const publicRoutes = ['/login', '/mfa-verify', '/api/auth/mfa/verify', '/api/auth/mfa/challenge', '/setup-required']\n  const isPublicRoute = publicRoutes.some(route => req.nextUrl.pathname.startsWith(route))\n\n  // If no session and trying to access protected routes, redirect to login\n  if (!session && !isPublicRoute) {\n    return NextResponse.redirect(new URL('/login', req.url))\n  }\n\n  // If session exists\n  if (session) {\n    // Check session timeout based on role\n    const { data: userRole } = await supabase\n      .from('staff')\n      .select('roles!inner(name)')\n      .eq('auth_id', session.user.id)\n      .single();\n\n    const isAdmin = userRole?.roles?.name && ADMIN_ROLES.includes(userRole.roles.name);\n    const maxSessionAge = isAdmin ? SESSION_TIMEOUTS.ADMIN : SESSION_TIMEOUTS.REGULAR;\n    \n    // Check if session is expired\n    const sessionAge = Date.now() - new Date(session.created_at).getTime();\n    if (sessionAge > maxSessionAge) {\n      // Log session timeout\n      await supabase.from('audit_logs').insert({\n        action: 'SESSION_TIMEOUT',\n        action_details: { \n          role: userRole?.roles?.name,\n          session_age_minutes: Math.floor(sessionAge / 60000)\n        },\n        user_id: session.user.id,\n      });\n\n      // Sign out user\n      await supabase.auth.signOut();\n      return NextResponse.redirect(new URL('/login?expired=true', req.url));\n    }\n\n    // Check if MFA is required but not verified\n    const mfaSession = req.cookies.get('mfa_session')\n    const mfaVerified = req.cookies.get('mfa_verified')\n    \n    // Get user's MFA status from database\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('mfa_enabled, require_mfa_for_role')\n      .eq('auth_id', session.user.id)\n      .single()\n\n    const requiresMFA = profile?.mfa_enabled || profile?.require_mfa_for_role\n    \n    // If MFA is required but not verified, and not on MFA verification page\n    if (requiresMFA && !mfaVerified && !req.nextUrl.pathname.startsWith('/mfa-verify')) {\n      // Store the original URL they were trying to access\n      const redirectUrl = new URL('/mfa-verify', req.url)\n      redirectUrl.searchParams.set('redirect', req.nextUrl.pathname)\n      return NextResponse.redirect(redirectUrl)\n    }\n\n    // If trying to access login while authenticated, redirect to dashboard\n    if (req.nextUrl.pathname === '/login') {\n      return NextResponse.redirect(new URL('/dashboard', req.url))\n    }\n  }\n\n  // Enhanced Security headers\n  res.headers.set('X-Content-Type-Options', 'nosniff')\n  res.headers.set('X-Frame-Options', 'DENY')\n  res.headers.set('X-XSS-Protection', '1; mode=block')\n  res.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')\n  res.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')\n  res.headers.set(\n    'Content-Security-Policy',\n    \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none';\"\n  )\n  res.headers.set(\n    'Permissions-Policy',\n    'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), accelerometer=(), gyroscope=()'\n  )\n\n  return res\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n", "import \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\n// Import the userland code.\nimport * as _mod from \"private-next-root-dir/middleware.ts\";\nimport { edgeInstrumentationOnRequestError } from \"next/dist/server/web/globals\";\nimport { isNextRouterError } from \"next/dist/client/components/is-next-router-error\";\nconst mod = {\n    ..._mod\n};\nconst handler = mod.middleware || mod.default;\nconst page = \"/middleware\";\nif (typeof handler !== 'function') {\n    throw new Error(`The Middleware \"${page}\" must export a \\`middleware\\` or a \\`default\\` function`);\n}\n// Middleware will only sent out the FetchEvent to next server,\n// so load instrumentation module here and track the error inside middleware module.\nfunction errorHandledHandler(fn) {\n    return async (...args)=>{\n        try {\n            return await fn(...args);\n        } catch (err) {\n            // In development, error the navigation API usage in runtime,\n            // since it's not allowed to be used in middleware as it's outside of react component tree.\n            if (process.env.NODE_ENV !== 'production') {\n                if (isNextRouterError(err)) {\n                    err.message = `Next.js navigation API is not allowed to be used in Middleware.`;\n                    throw err;\n                }\n            }\n            const req = args[0];\n            const url = new URL(req.url);\n            const resource = url.pathname + url.search;\n            await edgeInstrumentationOnRequestError(err, {\n                path: resource,\n                method: req.method,\n                headers: Object.fromEntries(req.headers.entries())\n            }, {\n                routerKind: 'Pages Router',\n                routePath: '/middleware',\n                routeType: 'middleware',\n                revalidateReason: undefined\n            });\n            throw err;\n        }\n    };\n}\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        page,\n        handler: errorHandledHandler(handler)\n    });\n}\n\n//# sourceMappingURL=middleware.js.map"], "names": ["middleware", "req", "res", "NextResponse", "next", "nextUrl", "pathname", "redirect", "URL", "url", "config", "matcher"], "sourceRoot": "", "ignoreList": []}