# Staff Data CSV Analysis & Improvement Recommendations

## Current Issues Identified

### 1. Data Quality Problems
- **Inconsistent formatting**: Mix of full-width (　) and half-width spaces
- **Missing data**: Many empty fields, especially emails for retired staff
- **Inconsistent department naming**: Mix of Japanese and romanized names
- **Character encoding issues**: Inconsistent use of full-width vs half-width characters
- **Invalid data**: Some entries have missing birth dates or entry dates

### 2. Structural Problems
- **Mixed data types**: Department field contains status information (退職者, 海上籍)
- **Denormalized structure**: Repeated department and position names
- **Inconsistent email formats**: Some emails have typos (molgorup.com vs molgroup.com)
- **No data validation**: Invalid dates and inconsistent ID formats

### 3. Security & Privacy Concerns
- **Exposed PII**: Birth dates and personal information visible
- **No data masking**: Sensitive information not protected
- **Audit trail missing**: No tracking of data changes

## Recommended Improvements

### 1. Normalize Database Structure

```sql
-- Departments table
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    name_jp VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Positions table  
CREATE TABLE positions (
    id SERIAL PRIMARY KEY,
    title_jp VARCHAR(100) NOT NULL,
    title_en VARCHAR(100),
    level INTEGER,
    is_active BOOLEAN DEFAULT true
);

-- Branches table
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name_jp VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    location VARCHAR(200),
    is_active BOOLEAN DEFAULT true
);

-- Employment statuses table
CREATE TABLE employment_statuses (
    id SERIAL PRIMARY KEY,
    status_jp VARCHAR(50) NOT NULL,
    status_en VARCHAR(50),
    is_active BOOLEAN DEFAULT true
);

-- Main staff table (normalized)
CREATE TABLE staff (
    id SERIAL PRIMARY KEY,
    staff_id VARCHAR(20) UNIQUE NOT NULL,
    department_id INTEGER REFERENCES departments(id),
    position_id INTEGER REFERENCES positions(id),
    branch_id INTEGER REFERENCES branches(id),
    employment_status_id INTEGER REFERENCES employment_statuses(id),
    full_name_jp VARCHAR(100) NOT NULL,
    furigana_jp VARCHAR(100),
    full_name_en VARCHAR(100),
    sex CHAR(1) CHECK (sex IN ('M', 'F')),
    date_of_birth DATE,
    date_of_entry DATE,
    date_of_exit DATE,
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Data Cleaning Script

```python
import pandas as pd
import re
from datetime import datetime

def clean_staff_data(csv_file):
    """Clean and normalize staff data"""
    df = pd.read_csv(csv_file)
    
    # Clean whitespace and normalize characters
    df = df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)
    
    # Normalize full-width spaces to half-width
    df = df.replace('　', ' ', regex=True)
    
    # Clean email addresses
    df['email'] = df['email'].str.replace('molgorup.com', 'molgroup.com')
    df['email'] = df['email'].str.lower()
    
    # Standardize sex field
    df['sex'] = df['sex'].map({'男': 'M', '女': 'F'})
    
    # Parse and validate dates
    df['date_of_birth'] = pd.to_datetime(df['date_of_birth'], errors='coerce')
    df['date_of_entry'] = pd.to_datetime(df['date_of_entry'], errors='coerce')
    
    # Separate status from department
    df['employment_status'] = df['department_id'].apply(extract_status)
    df['department_clean'] = df['department_id'].apply(clean_department)
    
    # Validate staff IDs
    df['staff_id'] = df['staff_id'].astype(str).str.zfill(4)
    
    return df

def extract_status(dept_field):
    """Extract employment status from department field"""
    if '退職者' in str(dept_field):
        return '退職者'
    elif '海上籍' in str(dept_field):
        return '海上籍'
    else:
        return '在職'

def clean_department(dept_field):
    """Clean department name"""
    dept = str(dept_field)
    # Remove status indicators
    dept = re.sub(r'[　\s]*退職者[　\s]*', '', dept)
    dept = re.sub(r'[　\s]*海上籍[　\s]*', '', dept)
    return dept.strip()
```

### 3. Data Validation Rules

```python
def validate_staff_data(df):
    """Validate staff data quality"""
    errors = []
    
    # Check required fields
    required_fields = ['staff_id', 'full_name_jp', 'full_name_en']
    for field in required_fields:
        if df[field].isnull().any():
            errors.append(f"Missing values in required field: {field}")
    
    # Validate email format
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    invalid_emails = df[~df['email'].str.match(email_pattern, na=False) & df['email'].notna()]
    if not invalid_emails.empty:
        errors.append(f"Invalid email formats found: {len(invalid_emails)} records")
    
    # Check date consistency
    invalid_dates = df[df['date_of_entry'] < df['date_of_birth']]
    if not invalid_dates.empty:
        errors.append(f"Entry date before birth date: {len(invalid_dates)} records")
    
    # Check duplicate staff IDs
    duplicates = df[df.duplicated('staff_id', keep=False)]
    if not duplicates.empty:
        errors.append(f"Duplicate staff IDs found: {len(duplicates)} records")
    
    return errors
```

### 4. Security Improvements

```python
import hashlib
from cryptography.fernet import Fernet

class DataSecurity:
    def __init__(self, encryption_key=None):
        self.key = encryption_key or Fernet.generate_key()
        self.cipher = Fernet(self.key)
    
    def mask_personal_data(self, df):
        """Mask sensitive personal information"""
        df_masked = df.copy()
        
        # Mask birth dates (show only year)
        df_masked['date_of_birth_masked'] = df_masked['date_of_birth'].dt.year
        
        # Hash email addresses for non-active employees
        inactive_mask = df_masked['employment_status'] == '退職者'
        df_masked.loc[inactive_mask, 'email'] = df_masked.loc[inactive_mask, 'email'].apply(
            lambda x: hashlib.sha256(str(x).encode()).hexdigest()[:8] if pd.notna(x) else x
        )
        
        return df_masked
    
    def encrypt_sensitive_fields(self, data):
        """Encrypt sensitive data fields"""
        if isinstance(data, str):
            return self.cipher.encrypt(data.encode()).decode()
        return data
```

### 5. Recommended CSV Structure (Improved)

```csv
staff_id,department_code,position_code,branch_code,employment_status,full_name_jp,furigana_jp,full_name_en,sex,date_of_birth,date_of_entry,date_of_exit,email_hash,is_active,created_at,updated_at
0001,EIGYO,KACHO,TOKYO,ACTIVE,田中太郎,たなか たろう,TANAKA TARO,M,1980-01-01,2020-04-01,,abc123def,true,2024-01-01,2024-01-01
```

### 6. Implementation Steps

1. **Data Audit**: Run validation scripts to identify all data quality issues
2. **Create Lookup Tables**: Establish normalized department, position, and branch tables
3. **Data Migration**: Clean and migrate existing data to new structure
4. **Implement Validation**: Add data validation rules and constraints
5. **Security Layer**: Implement data masking and encryption for sensitive fields
6. **Audit Trail**: Add logging for all data changes
7. **Access Control**: Implement role-based access to sensitive information

### 7. Benefits of Improvements

- **Data Integrity**: Consistent, validated data structure
- **Security**: Protected sensitive information with proper access controls
- **Maintainability**: Normalized structure easier to maintain and update
- **Scalability**: Better performance with proper indexing and relationships
- **Compliance**: Better adherence to data protection regulations
- **Analytics**: Cleaner data enables better reporting and analysis

### 8. Migration Considerations

- **Backup**: Full backup of existing data before migration
- **Gradual Migration**: Phase migration to minimize disruption
- **Data Validation**: Extensive testing of migrated data
- **User Training**: Train users on new data entry procedures
- **Documentation**: Comprehensive documentation of new structure and processes
