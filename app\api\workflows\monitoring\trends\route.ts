import { NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'
import { format, startOfDay, endOfDay, eachDayOfInterval } from 'date-fns'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'
    
    const cookieStore = await cookies()
    const supabase = createServerSupabase(cookieStore)

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case '24h':
        startDate.setDate(now.getDate() - 1)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      default:
        startDate.setDate(now.getDate() - 7)
    }

    // Get all days in the range
    const days = eachDayOfInterval({ start: startDate, end: now })

    // Fetch workflow data
    const { data: workflows } = await supabase
      .from('workflow_instances')
      .select('created_at, completed_at, status')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', now.toISOString())

    // Process data by day
    const trendData = days.map(day => {
      const dayStart = startOfDay(day)
      const dayEnd = endOfDay(day)

      const dayWorkflows = workflows?.filter(wf => {
        const createdAt = new Date(wf.created_at)
        return createdAt >= dayStart && createdAt <= dayEnd
      }) || []

      const completed = dayWorkflows.filter(wf => 
        wf.status === 'completed' && 
        wf.completed_at && 
        new Date(wf.completed_at) >= dayStart && 
        new Date(wf.completed_at) <= dayEnd
      ).length

      const failed = dayWorkflows.filter(wf => wf.status === 'failed').length
      const created = dayWorkflows.length

      return {
        date: format(day, 'MMM dd'),
        created,
        completed,
        failed
      }
    })

    return NextResponse.json(trendData)
  } catch (error) {
    console.error('Error fetching workflow trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch workflow trends' },
      { status: 500 }
    )
  }
}
