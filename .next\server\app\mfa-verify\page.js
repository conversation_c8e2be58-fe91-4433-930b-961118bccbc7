(()=>{var e={};e.id=9031,e.ids=[9031],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},33873:e=>{"use strict";e.exports=require("path")},28354:e=>{"use strict";e.exports=require("util")},50358:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var r=a(70260),s=a(28203),i=a(25155),n=a.n(i),o=a(67292),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);a.d(t,c);let l=["",{children:["mfa-verify",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,32291)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mfa-verify\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mfa-verify\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/mfa-verify/page",pathname:"/mfa-verify",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},12237:(e,t,a)=>{Promise.resolve().then(a.bind(a,32291))},36205:(e,t,a)=>{Promise.resolve().then(a.bind(a,36365))},36365:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>T});var r=a(45512),s=a(58009),i=a(79334),n=a(87021),o=a(97643),c=a(25409),l=a(47699),d=a(75339),u=a(86235),m=a(70384),f=a(1734);let h={en:{"auth.login":"Login","auth.logout":"Logout","auth.email":"Email","auth.password":"Password","auth.mfa.title":"Two-Factor Authentication","auth.mfa.instruction":"Enter the verification code from your authenticator app","auth.mfa.code":"Verification Code","auth.mfa.verify":"Verify","auth.mfa.resend":"Resend Code","common.submit":"Submit","common.cancel":"Cancel","common.save":"Save","common.delete":"Delete","common.edit":"Edit","common.search":"Search","common.loading":"Loading...","common.error":"An error occurred","error.required":"This field is required","error.invalid_email":"Invalid email address","error.invalid_password":"Password must be at least 8 characters","error.invalid_code":"Invalid verification code"},ja:{"auth.login":"ログイン","auth.logout":"ログアウト","auth.email":"メールアドレス","auth.password":"パスワード","auth.mfa.title":"二要素認証","auth.mfa.instruction":"認証アプリから確認コードを入力してください","auth.mfa.code":"確認コード","auth.mfa.verify":"確認","auth.mfa.resend":"コードを再送信","common.submit":"送信","common.cancel":"キャンセル","common.save":"保存","common.delete":"削除","common.edit":"編集","common.search":"検索","common.loading":"読み込み中...","common.error":"エラーが発生しました","error.required":"この項目は必須です","error.invalid_email":"無効なメールアドレスです","error.invalid_password":"パスワードは8文字以上である必要があります","error.invalid_code":"無効な確認コードです"}};var p=a(37133),_=a(55511);let y=(0,a(28354).promisify)(_.scrypt),v="aes-256-gcm",g=async()=>{let e=process.env.ENCRYPTION_KEY||process.env.SUPABASE_ANON_KEY;if(!e)throw Error("Encryption key not configured");let t=Buffer.from("itsync-mfa-salt-v1","utf8");return await y(e,t,32)};async function w(e){try{let t=await g(),a=(0,_.randomBytes)(16),r=(0,_.createCipheriv)(v,t,a),s=Buffer.concat([r.update(e,"utf8"),r.final()]),i=r.getAuthTag();return Buffer.concat([a,i,s]).toString("base64")}catch(e){throw Error(`Encryption failed: ${e.message}`)}}async function x(e){try{let t=await g(),a=Buffer.from(e,"base64"),r=a.slice(0,16),s=a.slice(16,32),i=a.slice(32),n=(0,_.createDecipheriv)(v,t,r);return n.setAuthTag(s),Buffer.concat([n.update(i),n.final()]).toString("utf8")}catch(e){throw Error(`Decryption failed: ${e.message}`)}}(function(){var e=Error("Cannot find module 'speakeasy'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module 'qrcode'");throw e.code="MODULE_NOT_FOUND",e}();class b{constructor(e){this.MAX_ATTEMPTS=5,this.SESSION_DURATION=6e5,this.CODE_LENGTH=6,this.BACKUP_CODE_LENGTH=8,this.BACKUP_CODE_COUNT=10,this.supabase=e}async setupTOTP(e,t){try{let a=Object(function(){var e=Error("Cannot find module 'speakeasy'");throw e.code="MODULE_NOT_FOUND",e}())({name:`ITSync (${t})`,issuer:"ITSync",length:32}),r=Object(function(){var e=Error("Cannot find module 'speakeasy'");throw e.code="MODULE_NOT_FOUND",e}())({secret:a.ascii,label:`ITSync:${t}`,issuer:"ITSync",algorithm:"sha256"}),s=await Object(function(){var e=Error("Cannot find module 'qrcode'");throw e.code="MODULE_NOT_FOUND",e}())(r),i=this.generateBackupCodes(),n=await w(a.base32),o=await Promise.all(i.map(e=>w(e)));return await this.supabase.from("mfa_configurations").upsert({user_id:e,method:"totp",secret_encrypted:n,backup_codes:o,is_verified:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),await this.logMFAEvent(e,"setup_initiated","totp",!0),{secret:a.base32,qrCode:s,backupCodes:i}}catch(t){throw await this.logMFAEvent(e,"setup_initiated","totp",!1,t.message),t}}async verifyTOTPSetup(e,t){try{let{data:a}=await this.supabase.from("mfa_configurations").select("*").eq("user_id",e).eq("method","totp").single();if(!a||!a.secret_encrypted)throw Error("TOTP configuration not found");let r=await x(a.secret_encrypted);if(Object(function(){var e=Error("Cannot find module 'speakeasy'");throw e.code="MODULE_NOT_FOUND",e}()).verify({secret:r,encoding:"base32",token:t,algorithm:"sha256",window:2}))return await this.supabase.from("mfa_configurations").update({is_verified:!0,is_primary:!0,verified_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",a.id),await this.supabase.from("profiles").update({mfa_enabled:!0,updated_at:new Date().toISOString()}).eq("auth_id",e),await this.logMFAEvent(e,"setup_completed","totp",!0),!0;return await this.logMFAEvent(e,"setup_failed","totp",!1,"Invalid code"),!1}catch(t){throw await this.logMFAEvent(e,"setup_failed","totp",!1,t.message),t}}async createChallenge(e){try{let{data:t}=await this.supabase.from("mfa_configurations").select("method, is_primary").eq("user_id",e).eq("is_verified",!0);if(!t||0===t.length)throw Error("No MFA methods configured");let a=this.generateSessionToken(),r=new Date(Date.now()+this.SESSION_DURATION);return await this.supabase.from("mfa_sessions").insert({user_id:e,session_token:a,method:t.find(e=>e.is_primary)?.method||t[0].method,expires_at:r.toISOString(),attempts:0,is_verified:!1}),{sessionToken:a,methods:t.map(e=>({method:e.method,isPrimary:e.is_primary}))}}catch(e){throw e}}async verifyCode(e,t){try{let{data:a}=await this.supabase.from("mfa_sessions").select("*").eq("session_token",e).single();if(!a)throw Error("Invalid session");if(new Date(a.expires_at)<new Date)throw Error("Session expired");if(a.attempts>=this.MAX_ATTEMPTS)return await this.logMFAEvent(a.user_id,"verify_failed",a.method,!1,"Locked out"),{success:!1,lockedOut:!0};let r=!1;if("totp"===a.method?r=await this.verifyTOTP(a.user_id,t):("sms"===a.method||"email"===a.method)&&(r=await this.verifyOTP(a,t)),r)return await this.supabase.from("mfa_sessions").update({is_verified:!0}).eq("id",a.id),await this.supabase.from("mfa_configurations").update({last_used_at:new Date().toISOString()}).eq("user_id",a.user_id).eq("method",a.method),await this.logMFAEvent(a.user_id,"verify_success",a.method,!0),{success:!0};{let e=a.attempts+1;return await this.supabase.from("mfa_sessions").update({attempts:e}).eq("id",a.id),await this.logMFAEvent(a.user_id,"verify_failed",a.method,!1),{success:!1,attemptsRemaining:this.MAX_ATTEMPTS-e}}}catch(e){throw e}}async verifyTOTP(e,t){let{data:a}=await this.supabase.from("mfa_configurations").select("secret_encrypted").eq("user_id",e).eq("method","totp").eq("is_verified",!0).single();if(!a||!a.secret_encrypted)return!1;let r=await x(a.secret_encrypted);return Object(function(){var e=Error("Cannot find module 'speakeasy'");throw e.code="MODULE_NOT_FOUND",e}()).verify({secret:r,encoding:"base32",token:t,algorithm:"sha256",window:2})}async verifyOTP(e,t){return!!e.challenge_code&&await x(e.challenge_code)===t}generateBackupCodes(){let e=[];for(let t=0;t<this.BACKUP_CODE_COUNT;t++)e.push(this.generateRandomCode(this.BACKUP_CODE_LENGTH));return e}generateRandomCode(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",a="";for(let r=0;r<e;r++)a+=t.charAt(Math.floor(Math.random()*t.length));return a}generateSessionToken(){return`mfa_${Date.now()}_${this.generateRandomCode(32)}`}async logMFAEvent(e,t,a,r,s,i){try{await this.supabase.from("mfa_audit_logs").insert({user_id:e,event_type:t,method:a,success:r,error_message:s,metadata:i,created_at:new Date().toISOString()})}catch(e){console.error("Failed to log MFA event:",e)}}async checkMFAStatus(e){let{data:t}=await this.supabase.from("profiles").select("mfa_enabled, require_mfa_for_role").eq("auth_id",e).single(),{data:a}=await this.supabase.from("mfa_configurations").select("method").eq("user_id",e).eq("is_verified",!0);return{enabled:t?.mfa_enabled||!1,required:t?.require_mfa_for_role||!1,methods:a?.map(e=>e.method)||[]}}}function C(){let{supabase:e}=(0,p.useSupabase)(),[t,a]=(0,s.useState)(!1),[r,i]=(0,s.useState)(null),n=new b(e),o=(0,s.useCallback)(async e=>{try{return await n.checkMFAStatus(e)}catch(e){throw i(e.message),e}},[n]),c=(0,s.useCallback)(async(e,t)=>{a(!0),i(null);try{return await n.setupTOTP(e,t)}catch(e){throw i(e.message),e}finally{a(!1)}},[n]),l=(0,s.useCallback)(async(e,t)=>{a(!0),i(null);try{return await n.verifyTOTPSetup(e,t)}catch(e){throw i(e.message),e}finally{a(!1)}},[n]),d=(0,s.useCallback)(async e=>{a(!0),i(null);try{return await n.createChallenge(e)}catch(e){throw i(e.message),e}finally{a(!1)}},[n]);return{loading:t,error:r,checkMFAStatus:o,setupTOTP:c,verifyTOTPSetup:l,createChallenge:d,verifyCode:(0,s.useCallback)(async(e,t)=>{a(!0),i(null);try{return await n.verifyCode(e,t)}catch(e){throw i(e.message),e}finally{a(!1)}},[n]),setupSMS:(0,s.useCallback)(async(e,t)=>{a(!0),i(null);try{throw Error("SMS setup not yet implemented")}catch(e){throw i(e.message),e}finally{a(!1)}},[]),setupEmail:(0,s.useCallback)(async e=>{a(!0),i(null);try{throw Error("Email setup not yet implemented")}catch(e){throw i(e.message),e}finally{a(!1)}},[])}}function E({sessionToken:e,method:t,onSuccess:a,onCancel:i}){let{t:p}=function(e="ja"){let t=h[e]||h.en;return{t:(e,a)=>{let r=t[e]||h.en[e]||e;return a&&Object.entries(a).forEach(([e,t])=>{r=r.replace(`{{${e}}}`,t)}),r},locale:e}}(),{verifyCode:_,loading:y,error:v}=C(),[g,w]=(0,s.useState)(""),[x,b]=(0,s.useState)(null),[E,O]=(0,s.useState)(null),S=async()=>{if(!g||6!==g.length){b(p("mfa.errors.invalidCode"));return}b(null);try{let t=await _(e,g);t.success?a():(t.lockedOut?b(p("mfa.errors.lockedOut")):(b(p("mfa.errors.verificationFailed")),void 0!==t.attemptsRemaining&&O(t.attemptsRemaining)),w(""))}catch(e){b(e.message||p("mfa.errors.verificationFailed"))}};return(0,r.jsxs)(o.Zp,{className:"w-full max-w-md mx-auto",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(()=>{switch(t){case"totp":return(0,r.jsx)(m.A,{className:"h-5 w-5"});case"sms":case"email":return(0,r.jsx)(f.A,{className:"h-5 w-5"})}})(),(()=>{switch(t){case"totp":return p("mfa.verify.totp.title");case"sms":return p("mfa.verify.sms.title");case"email":return p("mfa.verify.email.title")}})()]}),(0,r.jsx)(o.BT,{children:(()=>{switch(t){case"totp":return p("mfa.verify.totp.description");case"sms":return p("mfa.verify.sms.description");case"email":return p("mfa.verify.email.description")}})()})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(x||v)&&(0,r.jsx)(d.Fc,{variant:"destructive",children:(0,r.jsxs)(d.TN,{children:[x||v,null!==E&&(0,r.jsx)("span",{className:"block mt-1",children:p("mfa.errors.attemptsRemaining",{count:E})})]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"mfa-code",children:p("mfa.verify.codeLabel")}),(0,r.jsx)(c.p,{id:"mfa-code",type:"text",inputMode:"numeric",pattern:"[0-9]*",maxLength:6,value:g,onChange:e=>w(e.target.value.replace(/\D/g,"")),placeholder:"000000",className:"text-center text-2xl font-mono",autoComplete:"one-time-code",autoFocus:!0}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:p("mfa.verify.codeHelp")})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[i&&(0,r.jsx)(n.$,{variant:"outline",onClick:i,disabled:y,className:"flex-1",children:p("common.cancel")}),(0,r.jsx)(n.$,{onClick:S,disabled:y||6!==g.length,className:"flex-1",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),p("common.verifying")]}):p("mfa.verify.submit")})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(n.$,{variant:"link",className:"text-sm",onClick:()=>{console.log("Use backup code")},children:p("mfa.verify.useBackupCode")})})]})]})}var O=a(43433),S=a(88502);function T(){let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{user:a}=(0,O.A)(),{createChallenge:n,loading:o}=C(),[c,l]=(0,s.useState)(null),[d,u]=(0,s.useState)("totp"),[m,f]=(0,s.useState)(null),h=t.get("redirect")||"/dashboard";return o||!c?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(S.k,{className:"h-8 w-8 animate-spin"})}):m?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold mb-2",children:"Error"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:m}),(0,r.jsx)("button",{onClick:()=>e.push("/login"),className:"mt-4 text-primary hover:underline",children:"Back to login"})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsx)(E,{sessionToken:c,method:d,onSuccess:()=>{document.cookie="mfa_verified=true; path=/; max-age=43200",e.push(h)},onCancel:()=>{e.push("/api/auth/logout")}})})}},32291:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\mfa-verify\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mfa-verify\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8096,2076],()=>a(50358));module.exports=r})();