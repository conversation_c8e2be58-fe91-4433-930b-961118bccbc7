(()=>{var e={};e.id=3662,e.ids=[3662],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},56646:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>a,serverHooks:()=>d,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var n=t(42706),s=t(28203),i=t(45994),o=t(52440);let a=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/integrations/[integration]/route",pathname:"/api/integrations/[integration]",filename:"route",bundlePath:"app/api/integrations/[integration]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\integrations\\[integration]\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:d}=a;function x(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}},52440:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Unexpected eof\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\integrations\\[integration]\\route.ts\x1b[0m:52:1]\n \x1b[2m49\x1b[0m │   if (data.action === 'create_request') {\n \x1b[2m50\x1b[0m │     const { data: request, error } = await supabase\n \x1b[2m51\x1b[0m │       .from('request_forms')\n \x1b[2m52\x1b[0m │       .insert({\n    ╰────\n\n\nCaused by:\n    Syntax Error")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8096,2076],()=>t(56646));module.exports=n})();