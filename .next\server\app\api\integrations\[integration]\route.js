(()=>{var e={};e.id=3662,e.ids=[3662],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},15712:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>c});var n=t(42706),a=t(28203),i=t(45994),o=t(39187),u=t(73865);async function c(e,{params:r}){let t=r.integration,s=e.headers.get("x-api-key");if(!s||s!==process.env.INTEGRATION_API_KEY)return o.NextResponse.json({error:"Unauthorized"},{status:401});try{let r=await e.json();switch(t){case"servicenow":return p(r);case"backlog":return handleBacklogIntegration(r);default:return o.NextResponse.json({error:"Unknown integration"},{status:404})}}catch(e){return console.error("Integration error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){let r=(0,u.createClient)();if("create_request"===e.action){let{data:t,error:s}=await r.from("request_forms").insert({title:e.title||"ServiceNow Integration Request",description:e.description||"",priority:e.priority||"medium",category:e.category||"general",status:"open",external_id:e.external_id,integration_source:"servicenow"}).select().single();if(s)throw Error(`Failed to create request: ${s.message}`);return{success:!0,request:t}}return{success:!1,error:"Unknown action"}}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/integrations/[integration]/route",pathname:"/api/integrations/[integration]",filename:"route",bundlePath:"app/api/integrations/[integration]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\integrations\\[integration]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:x}=d;function v(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},73865:(e,r,t)=>{"use strict";t.d(r,{N:()=>s});let s=(0,t(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452],()=>t(15712));module.exports=s})();