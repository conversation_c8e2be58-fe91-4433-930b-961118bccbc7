'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/lib/LanguageContext'
import { useFilteredArticles } from '@/hooks/use-kb-permissions'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { KnowledgeBaseSearch } from './kb-search'
import { ArticleCard } from './article-card'
import { CategoryFilter } from './category-filter'
import { createClient } from '@/lib/supabase/client'
import { BookOpen, FileText, HelpCircle, Loader2 } from 'lucide-react'

export function KnowledgeBaseHome() {
  const { language } = useLanguage()
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [categories, setCategories] = useState<any[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(true)

  const supabase = createClient()

  // Use the filtered articles hook which respects permissions
  const { 
    articles, 
    loading: articlesLoading, 
    error: articlesError,
    refetch 
  } = useFilteredArticles({
    categoryId: selectedCategory === 'all' ? undefined : selectedCategory,
    searchQuery: searchQuery || undefined,
    status: 'published'
  })

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    setCategoriesLoading(true)
    try {
      const { data: categoriesData } = await supabase
        .from('kb_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order')

      if (categoriesData) {
        setCategories(categoriesData)
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    } finally {
      setCategoriesLoading(false)
    }
  }

  // Filter articles for featured and popular tabs
  const featuredArticles = articles.filter(article => article.is_featured)
  const popularArticles = [...articles].sort((a, b) => (b.view_count || 0) - (a.view_count || 0)).slice(0, 5)

  const loading = articlesLoading || categoriesLoading

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold mb-4">
          {language === 'en' ? 'Knowledge Base' : 'ナレッジベース'}
        </h1>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          {language === 'en' 
            ? 'Find answers, guides, and resources to help you use our IT services effectively.'
            : 'ITサービスを効果的に使用するための回答、ガイド、リソースを見つけてください。'}
        </p>
        <div className="max-w-2xl mx-auto">
          <KnowledgeBaseSearch onSearch={setSearchQuery} />
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-4">
        <div className="lg:col-span-1">
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onSelectCategory={setSelectedCategory}
          />
        </div>

        <div className="lg:col-span-3">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">
                {language === 'en' ? 'All Articles' : 'すべての記事'}
              </TabsTrigger>
              <TabsTrigger value="featured">
                {language === 'en' ? 'Featured' : '注目'}
              </TabsTrigger>
              <TabsTrigger value="popular">
                {language === 'en' ? 'Popular' : '人気'}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-6">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  {language === 'en' ? 'Loading...' : '読み込み中...'}
                </div>
              ) : articlesError ? (
                <Card className="p-8 text-center">
                  <p className="text-destructive">
                    {language === 'en' 
                      ? 'Error loading articles. Please try again.'
                      : '記事の読み込みエラー。もう一度お試しください。'}
                  </p>
                </Card>
              ) : articles.length === 0 ? (
                <Card className="p-8 text-center">
                  <HelpCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {language === 'en' 
                      ? 'No articles found. Try a different search or category.'
                      : '記事が見つかりません。別の検索またはカテゴリを試してください。'}
                  </p>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {articles.map(article => (
                    <ArticleCard key={article.id} article={article} />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="featured" className="mt-6">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  {language === 'en' ? 'Loading...' : '読み込み中...'}
                </div>
              ) : featuredArticles.length === 0 ? (
                <Card className="p-8 text-center">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {language === 'en' 
                      ? 'No featured articles available.'
                      : '注目記事はありません。'}
                  </p>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {featuredArticles.map(article => (
                    <ArticleCard key={article.id} article={article} />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="popular" className="mt-6">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  {language === 'en' ? 'Loading...' : '読み込み中...'}
                </div>
              ) : popularArticles.length === 0 ? (
                <Card className="p-8 text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {language === 'en' 
                      ? 'No popular articles yet.'
                      : '人気記事はまだありません。'}
                  </p>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {popularArticles.map(article => (
                    <ArticleCard key={article.id} article={article} />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
