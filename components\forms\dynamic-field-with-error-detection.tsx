'use client'

import React, { useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import { FieldType } from '@/lib/types/form-types'
import { SearchableSelect } from './searchable-select'
import { MultiSelect } from './multi-select'
import { useErrorDetection } from '@/lib/hooks/use-error-detection'
import { ErrorIndicator, FieldWithErrorDetection } from './error-indicator'
import { Button } from '@/components/ui/button'

interface DynamicFieldWithErrorDetectionProps {
  field: FieldType
  value: any
  onChange: (value: any) => void
  error?: string
  departmentId?: string
  language?: 'ja' | 'en'
}

export function DynamicFieldWithErrorDetection({
  field,
  value,
  onChange,
  error,
  departmentId,
  language = 'ja'
}: DynamicFieldWithErrorDetectionProps) {
  const {
    setValue,
    errors,
    isChecking,
    suggestions,
    hasErrors,
    fieldStats,
    acceptCorrection,
    checkErrorsManually
  } = useErrorDetection({
    fieldName: field.name,
    fieldType: field.type,
    departmentId,
    language,
    enableRealTime: true
  })

  // Update error detection value when field value changes
  useEffect(() => {
    if (typeof value === 'string') {
      setValue(value)
    }
  }, [value, setValue])

  const handleAcceptSuggestion = (suggestion: string) => {
    onChange(suggestion)
    setValue(suggestion)
  }

  const renderField = () => {
    switch (field.type) {
      case 'text':
      case 'email':
      case 'staff_id':
      case 'pc_id':
        return (
          <Input
            id={field.name}
            type={field.type === 'email' ? 'email' : 'text'}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        )

      case 'textarea':
        return (
          <Textarea
            id={field.name}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
            rows={4}
          />
        )

      case 'select':
        return (
          <Select
            value={value || ''}
            onValueChange={onChange}
            disabled={field.disabled}
          >
            <SelectTrigger id={field.name}>
              <SelectValue placeholder={field.placeholder || '選択してください'} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'search':
        return (
          <SearchableSelect
            value={value}
            onChange={onChange}
            placeholder={field.placeholder}
            disabled={field.disabled}
            source={field.source}
            departmentId={departmentId}
          />
        )

      case 'multiselect':
        return (
          <MultiSelect
            value={value || []}
            onChange={onChange}
            placeholder={field.placeholder}
            disabled={field.disabled}
            options={field.options || []}
          />
        )

      default:
        return (
          <Input
            id={field.name}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        )
    }
  }

  const shouldShowErrorDetection = 
    ['text', 'email', 'staff_id', 'pc_id', 'textarea'].includes(field.type)

  return (
    <div className="space-y-2">
      <Label htmlFor={field.name} className="text-sm font-medium">
        {field.labelJp || field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {shouldShowErrorDetection ? (
        <>
          <FieldWithErrorDetection
            errors={errors}
            isChecking={isChecking}
            hasErrors={hasErrors}
            errorRate={fieldStats?.error_rate}
            showStats={false}
          >
            {renderField()}
          </FieldWithErrorDetection>
          
          <ErrorIndicator
            errors={errors}
            isChecking={isChecking}
            onAcceptSuggestion={handleAcceptSuggestion}
          />

          {suggestions.length > 0 && !hasErrors && (
            <div className="flex flex-wrap gap-1">
              <span className="text-xs text-muted-foreground">候補:</span>
              {suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs"
                  onClick={() => handleAcceptSuggestion(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          )}
        </>
      ) : (
        renderField()
      )}
      
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  )
}
