// lib/services/error-detection-service.ts
// Client-side service for AI-powered error detection and correction

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

export interface DetectedError {
  type: string
  message: string
  confidence: number
  suggestions: string[]
}

export interface ErrorDetectionResult {
  errors: DetectedError[]
  hasErrors: boolean
}

export interface ErrorCorrectionFeedback {
  correctionId: string
  feedbackType: 'helpful' | 'not_helpful' | 'incorrect'
  userFeedback?: string
  improvedSuggestion?: string
}

export class ErrorDetectionService {
  private supabase = createClientComponentClient()
  private detectionCache = new Map<string, ErrorDetectionResult>()

  async detectErrors(
    fieldName: string,
    fieldType: string,
    value: string,
    departmentId?: string,
    context?: Record<string, any>,
    language: 'ja' | 'en' = 'ja'
  ): Promise<ErrorDetectionResult> {
    // Check cache first
    const cacheKey = `${fieldName}-${value}-${language}`
    if (this.detectionCache.has(cacheKey)) {
      return this.detectionCache.get(cacheKey)!
    }

    try {
      const { data, error } = await this.supabase.functions.invoke('detect-form-errors', {
        body: {
          fieldName,
          fieldType,
          value,
          departmentId,
          context,
          language
        }
      })

      if (error) throw error

      // Cache the result
      this.detectionCache.set(cacheKey, data)

      // Clear cache after 5 minutes
      setTimeout(() => {
        this.detectionCache.delete(cacheKey)
      }, 5 * 60 * 1000)

      return data
    } catch (error) {
      console.error('Error detection failed:', error)
      return { errors: [], hasErrors: false }
    }
  }

  async getFieldErrorStats(fieldName: string, departmentId?: string) {
    const query = this.supabase
      .from('field_error_stats')
      .select('*')
      .eq('field_name', fieldName)

    if (departmentId) {
      query.eq('department_id', departmentId)
    }

    const { data, error } = await query.single()

    if (error && error.code !== 'PGRST116') {
      console.error('Failed to get field error stats:', error)
    }

    return data
  }

  async getCommonErrors(fieldType: string, departmentId?: string) {
    const query = this.supabase
      .from('error_patterns')
      .select('*')
      .eq('field_type', fieldType)
      .order('frequency', { ascending: false })
      .limit(5)

    if (departmentId) {
      query.or(`department_id.eq.${departmentId},department_id.is.null`)
    }

    const { data, error } = await query

    if (error) {      console.error('Failed to get common errors:', error)
      return []
    }

    return data || []
  }

  async acceptCorrection(
    correctionId: string,
    acceptedValue: string
  ) {
    const { error } = await this.supabase
      .from('error_corrections')
      .update({
        accepted_correction: acceptedValue,
        correction_accepted: true
      })
      .eq('id', correctionId)

    if (error) {
      console.error('Failed to accept correction:', error)
      throw error
    }
  }

  async provideFeedback(feedback: ErrorCorrectionFeedback) {
    const { error } = await this.supabase
      .from('error_correction_feedback')
      .insert({
        correction_id: feedback.correctionId,
        feedback_type: feedback.feedbackType,
        user_feedback: feedback.userFeedback,
        improved_suggestion: feedback.improvedSuggestion
      })

    if (error) {
      console.error('Failed to provide feedback:', error)
      throw error
    }
  }

  async getAutocorrectSuggestions(
    fieldType: string,
    value: string,
    language: 'ja' | 'en' = 'ja'
  ) {
    const { data, error } = await this.supabase
      .from('autocorrect_mappings')
      .select('*')
      .eq('field_type', fieldType)      .eq('language', language)
      .ilike('incorrect_value', `%${value}%`)
      .limit(3)

    if (error) {
      console.error('Failed to get autocorrect suggestions:', error)
      return []
    }

    return data || []
  }

  // Learning function to improve error detection
  async learnFromCorrection(
    fieldType: string,
    originalValue: string,
    correctedValue: string,
    errorType: string
  ) {
    // Update autocorrect mappings if it's a new pattern
    const { error } = await this.supabase
      .from('autocorrect_mappings')
      .upsert({
        field_type: fieldType,
        incorrect_value: originalValue,
        correct_value: correctedValue,
        usage_count: 1
      }, {
        onConflict: 'incorrect_value,correct_value,field_type',
        ignoreDuplicates: false
      })

    if (error) {
      console.error('Failed to learn from correction:', error)
    }

    // Update error pattern frequency
    await this.supabase.rpc('increment_error_pattern_frequency', {
      p_field_type: fieldType,
      p_error_type: errorType
    })
  }

  // Get real-time error suggestions as user types
  async getRealTimeSuggestions(
    fieldType: string,
    partialValue: string,
    language: 'ja' | 'en' = 'ja'
  ) {
    if (partialValue.length < 2) return []
    const { data, error } = await this.supabase
      .from('autocorrect_mappings')
      .select('correct_value')
      .eq('field_type', fieldType)
      .eq('language', language)
      .ilike('correct_value', `${partialValue}%`)
      .order('usage_count', { ascending: false })
      .limit(5)

    if (error) {
      console.error('Failed to get real-time suggestions:', error)
      return []
    }

    return data?.map(item => item.correct_value) || []
  }
}

// Export singleton instance
export const errorDetectionService = new ErrorDetectionService()
