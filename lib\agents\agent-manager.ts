import { GroupMailAgent, SharePointAgent, PasswordResetAgent } from './specialized-agents'
import { PCAdminAgent } from './pc-admin-agent'
import { MailboxAgent } from './mailbox-agent'
import { BaseAIAgent } from './base-agent'
import { supabase } from '@/lib/supabase'

interface AgentStatus {
  agentId: string
  category: string
  status: 'active' | 'idle' | 'error'
  lastActivity: Date
  articlesCreated: number
  articlesUpdated: number
  currentTask?: string
}

export class AIAgentManager {
  private static instance: AIAgentManager
  private agents: Map<string, BaseAIAgent>
  private agentStatuses: Map<string, AgentStatus>
  private isRunning: boolean = false

  private constructor() {
    this.agents = new Map()
    this.agentStatuses = new Map()
    this.initializeAgents()
  }

  static getInstance(): AIAgentManager {
    if (!AIAgentManager.instance) {
      AIAgentManager.instance = new AIAgentManager()
    }
    return AIAgentManager.instance
  }

  private initializeAgents() {
    // Create specialized agents for each service category
    const agents = [
      new GroupMailAgent(),
      new SharePointAgent(),
      new PasswordResetAgent(),
      new PCAdminAgent(),
      new MailboxAgent()
    ]

    agents.forEach(agent => {
      this.agents.set(agent['agentId'], agent)
      this.agentStatuses.set(agent['agentId'], {
        agentId: agent['agentId'],
        category: agent['specialization'].serviceCategory,
        status: 'idle',
        lastActivity: new Date(),
        articlesCreated: 0,
        articlesUpdated: 0
      })
    })
  }

  /**
   * Start all AI agents
   */
  async startAllAgents(): Promise<void> {
    if (this.isRunning) {
      console.log('AI Agent Manager is already running')
      return
    }

    this.isRunning = true
    console.log('Starting AI Agent Manager with', this.agents.size, 'agents')

    // Start monitoring
    this.startMonitoring()

    // Start each agent in parallel
    const agentPromises = Array.from(this.agents.entries()).map(([id, agent]) => {
      return this.startAgent(id, agent)
    })

    await Promise.all(agentPromises)
  }

  /**
   * Start individual agent
   */
  private async startAgent(agentId: string, agent: BaseAIAgent): Promise<void> {
    try {
      const status = this.agentStatuses.get(agentId)
      if (status) {
        status.status = 'active'
        status.lastActivity = new Date()
      }

      // Run agent loop
      await agent.runAgentLoop()
    } catch (error) {
      console.error(`Error in agent ${agentId}:`, error)
      const status = this.agentStatuses.get(agentId)
      if (status) {
        status.status = 'error'
      }
    }
  }

  /**
   * Stop all agents
   */
  async stopAllAgents(): Promise<void> {
    this.isRunning = false
    console.log('Stopping all AI agents')
    
    // In a real implementation, we'd have a way to interrupt the agent loops
    this.agents.forEach((agent, id) => {
      const status = this.agentStatuses.get(id)
      if (status) {
        status.status = 'idle'
      }
    })
  }

  /**
   * Get status of all agents
   */
  getAgentStatuses(): AgentStatus[] {
    return Array.from(this.agentStatuses.values())
  }

  /**
   * Get status of specific agent
   */
  getAgentStatus(agentId: string): AgentStatus | undefined {
    return this.agentStatuses.get(agentId)
  }

  /**
   * Monitor agent activities
   */
  private async startMonitoring(): Promise<void> {
    setInterval(async () => {
      if (!this.isRunning) return

      // Update agent statistics
      for (const [agentId, status] of this.agentStatuses) {
        await this.updateAgentStats(agentId, status)
      }

      // Log overall status
      await this.logSystemStatus()
    }, 60000) // Every minute
  }

  /**
   * Update statistics for an agent
   */
  private async updateAgentStats(agentId: string, status: AgentStatus): Promise<void> {
    try {
      // Get articles created by this agent
      const { data: created } = await supabase
        .from('kb_articles')
        .select('id')
        .eq('metadata->>createdByAgent', agentId)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

      // Get articles updated by this agent
      const { data: updated } = await supabase
        .from('kb_articles')
        .select('id')
        .eq('metadata->>lastUpdatedByAgent', agentId)
        .gte('updated_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

      status.articlesCreated = created?.length || 0
      status.articlesUpdated = updated?.length || 0
    } catch (error) {
      console.error(`Error updating stats for agent ${agentId}:`, error)
    }
  }

  /**
   * Log system status
   */
  private async logSystemStatus(): Promise<void> {
    const activeAgents = Array.from(this.agentStatuses.values())
      .filter(s => s.status === 'active').length

    const totalArticlesCreated = Array.from(this.agentStatuses.values())
      .reduce((sum, s) => sum + s.articlesCreated, 0)

    const totalArticlesUpdated = Array.from(this.agentStatuses.values())
      .reduce((sum, s) => sum + s.articlesUpdated, 0)

    console.log(`[AI Agent Manager] Status Update:
      - Active Agents: ${activeAgents}/${this.agents.size}
      - Articles Created (24h): ${totalArticlesCreated}
      - Articles Updated (24h): ${totalArticlesUpdated}
    `)

    // Log to database
    await supabase
      .from('agent_system_logs')
      .insert({
        active_agents: activeAgents,
        total_agents: this.agents.size,
        articles_created_24h: totalArticlesCreated,
        articles_updated_24h: totalArticlesUpdated,
        created_at: new Date().toISOString()
      })
  }

  /**
   * Trigger specific agent to research a topic
   */
  async triggerAgentResearch(category: string, topic: string): Promise<void> {
    const agent = Array.from(this.agents.values()).find(
      a => a['specialization'].serviceCategory === category
    )

    if (agent) {
      console.log(`Triggering ${category} agent to research: ${topic}`)
      // In a real implementation, we'd have a way to inject topics into the agent
    } else {
      console.error(`No agent found for category: ${category}`)
    }
  }

  /**
   * Get agent performance metrics
   */
  async getAgentMetrics(timeRange: 'day' | 'week' | 'month' = 'day'): Promise<any> {
    const startDate = new Date()
    switch (timeRange) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1)
        break
      case 'week':
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1)
        break
    }

    const metrics = new Map<string, any>()

    for (const [agentId, agent] of this.agents) {
      const { data: activities } = await supabase
        .from('agent_activity_logs')
        .select('*')
        .eq('agent_id', agentId)
        .gte('created_at', startDate.toISOString())

      const { data: articles } = await supabase
        .from('kb_articles')
        .select('id, metadata')
        .or(`metadata->>createdByAgent.eq.${agentId},metadata->>lastUpdatedByAgent.eq.${agentId}`)
        .gte('created_at', startDate.toISOString())

      metrics.set(agentId, {
        activities: activities?.length || 0,
        articlesCreated: articles?.filter(a => a.metadata?.createdByAgent === agentId).length || 0,
        articlesUpdated: articles?.filter(a => a.metadata?.lastUpdatedByAgent === agentId).length || 0,
        category: agent['specialization'].serviceCategory
      })
    }

    return Object.fromEntries(metrics)
  }
}

// Export singleton instance
export const aiAgentManager = AIAgentManager.getInstance()
