import { NextRequest, NextResponse } from 'next/server'
import { embeddingsService } from '@/lib/services/embeddings-service'

export const runtime = 'nodejs'

export async function POST(request: NextRequest) {
  try {
    const { query, language, matchThreshold, matchCount } = await request.json()

    if (!query || !language) {
      return NextResponse.json(
        { error: 'Query and language are required' },
        { status: 400 }
      )
    }

    const results = await embeddingsService.semanticSearch(
      query,
      language,
      { matchThreshold, matchCount }
    )

    return NextResponse.json({
      success: true,
      results,
      count: results.length
    })
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Search failed'
      },
      { status: 500 }
    )
  }
}
