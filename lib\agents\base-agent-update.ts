// Update imports in base-agent.ts to include web scraping service
protected async performWebSearch(query: string): Promise<any[]> {
  const { webScrapingService } = await import('@/lib/services/web-scraping-service')
  
  const results = await webScrapingService.searchWeb(query, {
    count: 10,
    freshness: 'week',
    language: this.specialization.languages.includes('jp') ? 'jp' : 'en'
  })
  
  return results
}

protected async scrapeUrl(url: string): Promise<string | null> {
  const { webScrapingService } = await import('@/lib/services/web-scraping-service')
  
  const content = await webScrapingService.scrapeUrl(url)
  return content ? content.content : null
}
