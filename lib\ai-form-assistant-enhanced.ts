// Enhanced AI Service Integration with Real-time Validation
import { <PERSON><PERSON>ield, FormData } from '@/lib/form-types'
import { SelectedUser } from '@/lib/request-types'
import OpenAI from 'openai'

interface AIContext {
  userId?: string
  departmentId?: string
  userRole?: string
  previousFormData?: FormData
  selectedUsers?: SelectedUser[]
}

interface AISuggestion {
  fieldId: string
  suggestedValue: any
  confidence: number
  reasoning: string
}

export interface AIValidationResult {
  isValid: boolean
  message?: string
  type: 'success' | 'warning' | 'error' | 'info'
  aiInsight?: string
  suggestion?: any
}

export class EnhancedAIFormAssistant {
  private static instance: EnhancedAIFormAssistant
  private openai: OpenAI | null = null
  private apiKey: string | null = null
  private validationCache: Map<string, AIValidationResult> = new Map()

  private constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || null
    if (this.apiKey) {
      this.openai = new OpenAI({
        apiKey: this.apiKey,
        dangerouslyAllowBrowser: true // For client-side usage
      })
    }
  }

  public static getInstance(): EnhancedAIFormAssistant {
    if (!EnhancedAIFormAssistant.instance) {
      EnhancedAIFormAssistant.instance = new EnhancedAIFormAssistant()
    }
    return EnhancedAIFormAssistant.instance
  }

  /**
   * Validate field with AI-powered insights
   */
  async validateFieldWithAI(
    field: FormField,
    value: any,
    context?: AIContext
  ): Promise<AIValidationResult> {
    // Check cache first
    const cacheKey = `${field.id}-${JSON.stringify(value)}`
    if (this.validationCache.has(cacheKey)) {
      return this.validationCache.get(cacheKey)!
    }

    try {
      // Basic validation first
      const basicResult = this.performBasicValidation(field, value)
      if (!basicResult.isValid) {
        return basicResult
      }

      // AI-powered validation if API key is available
      if (this.openai && value && value.toString().trim() !== '') {
        const aiResult = await this.performAIValidation(field, value, context)
        this.validationCache.set(cacheKey, aiResult)
        return aiResult
      }

      return basicResult
    } catch (error) {
      console.error('AI validation error:', error)
      return {
        isValid: true,
        type: 'warning',
        message: 'AI検証は利用できませんが、基本検証に合格しました / AI validation unavailable but basic validation passed'
      }
    }
  }

  /**
   * Perform basic validation without AI
   */
  private performBasicValidation(field: FormField, value: any): AIValidationResult {
    // Required field validation
    if (field.required && (!value || value.toString().trim() === '')) {
      return {
        isValid: false,
        type: 'error',
        message: `${field.labelJp || field.label}は必須です / ${field.label} is required`
      }
    }

    // Empty field - no validation needed
    if (!value || value.toString().trim() === '') {
      return {
        isValid: true,
        type: 'info'
      }
    }

    // Type-specific validation
    switch (field.type) {
      case 'text':
        if (field.validation === 'email') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(value)) {
            return {
              isValid: false,
              type: 'error',
              message: '有効なメールアドレスを入力してください / Please enter a valid email address',
              suggestion: this.generateEmailSuggestion(value)
            }
          }
        }
        
        if (field.validation === 'pc_id') {
          const pcIdRegex = /^[A-Z]{2}\d{8}$/
          if (!pcIdRegex.test(value)) {
            return {
              isValid: false,
              type: 'warning',
              message: 'PC IDは通常「XX12345678」の形式です / PC ID should be in format "XX12345678"',
              suggestion: this.generatePCIdSuggestion(value)
            }
          }
        }
        break

      case 'search':
        if (field.required && (!value || !value.id)) {
          return {
            isValid: false,
            type: 'error',
            message: `${field.labelJp || field.label}から選択してください / Please select from ${field.label}`
          }
        }
        break

      case 'multiselect':
        if (field.required && (!value || !Array.isArray(value) || value.length === 0)) {
          return {
            isValid: false,
            type: 'error',
            message: `${field.labelJp || field.label}を少なくとも1つ選択してください / Please select at least one ${field.label}`
          }
        }
        break
    }

    return {
      isValid: true,
      type: 'success',
      message: '入力内容は正常です / Input is valid'
    }
  }

  /**
   * Perform AI-powered validation using OpenAI
   */
  private async performAIValidation(
    field: FormField,
    value: any,
    context?: AIContext
  ): Promise<AIValidationResult> {
    if (!this.openai) {
      return this.performBasicValidation(field, value)
    }

    try {
      const prompt = this.buildValidationPrompt(field, value, context)
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant helping with IT helpdesk form validation in a Japanese enterprise environment. 
            Validate the input and provide helpful insights. Respond in JSON format with the following structure:
            {
              "isValid": boolean,
              "type": "success" | "warning" | "error" | "info",
              "message": "Bilingual message in format: Japanese / English",
              "aiInsight": "Additional context or suggestion",
              "suggestion": "Corrected or improved value (if applicable)"
            }`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 200,
        response_format: { type: "json_object" }
      })

      const response = completion.choices[0].message.content
      if (!response) {
        return this.performBasicValidation(field, value)
      }

      const result = JSON.parse(response) as AIValidationResult
      return result
    } catch (error) {
      console.error('OpenAI API error:', error)
      return this.performBasicValidation(field, value)
    }
  }

  /**
   * Build validation prompt for OpenAI
   */
  private buildValidationPrompt(field: FormField, value: any, context?: AIContext): string {
    let prompt = `Field: ${field.label} (${field.labelJp})\n`
    prompt += `Type: ${field.type}\n`
    prompt += `Value: ${value}\n`
    
    if (field.validation) {
      prompt += `Validation Rule: ${field.validation}\n`
    }
    
    if (context?.departmentId) {
      prompt += `Department Context: ${context.departmentId}\n`
    }
    
    if (field.id === 'pc_id') {
      prompt += `Note: PC IDs should follow format like LT24123101 (Laptop), DT24123101 (Desktop), etc.\n`
    }
    
    if (field.id === 'reason' || field.id === 'description') {
      prompt += `Note: Reasons should be clear and include both Japanese and English when possible.\n`
    }
    
    prompt += `\nValidate this input and provide helpful feedback.`
    
    return prompt
  }

  /**
   * Generate AI suggestions for form fields based on context
   */
  async generateFieldSuggestions(
    field: FormField, 
    currentValue: any, 
    context: AIContext
  ): Promise<AISuggestion | null> {
    try {
      // Use AI if available, otherwise fall back to rule-based
      if (this.openai && field.type === 'text') {
        return await this.generateAISuggestion(field, currentValue, context)
      }
      
      return this.generateRuleBasedSuggestion(field, currentValue, context)
    } catch (error) {
      console.error('AI suggestion failed:', error)
      return this.generateRuleBasedSuggestion(field, currentValue, context)
    }
  }

  /**
   * Generate AI-powered suggestions using OpenAI
   */
  private async generateAISuggestion(
    field: FormField,
    currentValue: any,
    context: AIContext
  ): Promise<AISuggestion | null> {
    if (!this.openai || !currentValue || currentValue.toString().trim().length < 2) {
      return null
    }

    try {
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant helping with IT helpdesk form completion in a Japanese enterprise. 
            Suggest improvements or completions for form fields. Respond in JSON format:
            {
              "suggestedValue": "improved value",
              "confidence": 0.0-1.0,
              "reasoning": "explanation"
            }`
          },
          {
            role: 'user',
            content: `Field: ${field.label} (${field.labelJp})\nCurrent Value: ${currentValue}\nSuggest an improvement.`
          }
        ],
        temperature: 0.5,
        max_tokens: 150,
        response_format: { type: "json_object" }
      })

      const response = completion.choices[0].message.content
      if (!response) return null

      const result = JSON.parse(response)
      return {
        fieldId: field.id,
        suggestedValue: result.suggestedValue,
        confidence: result.confidence,
        reasoning: result.reasoning
      }
    } catch (error) {
      console.error('OpenAI suggestion error:', error)
      return null
    }
  }

  /**
   * Auto-populate form fields using AI
   */
  async autoPopulateFields(
    fields: FormField[],
    context: AIContext
  ): Promise<FormData> {
    const populatedData: FormData = {}

    for (const field of fields) {
      const suggestion = await this.generateFieldSuggestions(field, null, context)
      if (suggestion && suggestion.confidence > 0.7) {
        populatedData[field.id] = suggestion.suggestedValue
      }
    }

    return populatedData
  }

  /**
   * Validate form data using AI
   */
  async validateFormData(
    fields: FormField[],
    formData: FormData,
    context: AIContext
  ): Promise<Record<string, string>> {
    const errors: Record<string, string> = {}

    for (const field of fields) {
      const result = await this.validateFieldWithAI(field, formData[field.id], context)
      if (!result.isValid && result.message) {
        errors[field.id] = result.message
      }
    }

    return errors
  }

  /**
   * Generate smart suggestions for PC ID based on context
   */
  private generatePCIdSuggestion(input: string): string {
    const now = new Date()
    const year = now.getFullYear().toString().slice(-2)
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    const rand = Math.floor(Math.random() * 100).toString().padStart(2, '0')
    
    const lowerInput = input.toLowerCase()
    
    if (lowerInput.includes('laptop') || lowerInput.includes('ノート')) {
      return `LT${year}${month}${day}${rand}`
    }
    
    if (lowerInput.includes('desktop') || lowerInput.includes('デスク')) {
      return `DT${year}${month}${day}${rand}`
    }
    
    if (lowerInput.includes('server') || lowerInput.includes('サーバ')) {
      return `SV${year}${month}${day}${rand}`
    }
    
    return `PC${year}${month}${day}${rand}`
  }

  /**
   * Generate email suggestions based on context
   */
  private generateEmailSuggestion(input: string): string {
    if (input.includes('@')) return input
    
    const cleanInput = input.toLowerCase().replace(/[^a-z0-9]/g, '')
    return `${cleanInput}@company.com`
  }

  /**
   * Generate reason/description suggestions
   */
  private generateReasonSuggestion(input: string): string {
    const lowerInput = input.toLowerCase()
    
    const suggestions = {
      'software': '業務に必要なソフトウェアのインストールのため / Required for business software installation',
      'access': 'プロジェクト作業のためアクセス権限が必要 / Access permission needed for project work',
      'permission': 'システム管理作業のため管理者権限が必要 / Administrator permission needed for system management',
      'email': 'メール配信リストへの参加のため / To join mailing list',
      'document': '文書共有のためのアクセス権限 / Access permission for document sharing',
      'meeting': '会議資料共有のため / For meeting material sharing'
    }

    for (const [key, suggestion] of Object.entries(suggestions)) {
      if (lowerInput.includes(key)) {
        return suggestion
      }
    }

    return input.charAt(0).toUpperCase() + input.slice(1)
  }

  /**
   * Rule-based suggestion generation (fallback)
   */
  private generateRuleBasedSuggestion(
    field: FormField, 
    currentValue: any, 
    context: AIContext
  ): AISuggestion | null {
    const input = currentValue?.toString() || ''
    
    switch (field.id) {
      case 'pc_id':
        if (input.length > 2) {
          return {
            fieldId: field.id,
            suggestedValue: this.generatePCIdSuggestion(input),
            confidence: 0.9,
            reasoning: 'PC ID generated based on device type and current date'
          }
        }
        break
      
      case 'email':
        if (input && !input.includes('@')) {
          return {
            fieldId: field.id,
            suggestedValue: this.generateEmailSuggestion(input),
            confidence: 0.8,
            reasoning: 'Added company domain to email address'
          }
        }
        break
      
      case 'reason':
      case 'description':
        if (input && input.length > 3) {
          return {
            fieldId: field.id,
            suggestedValue: this.generateReasonSuggestion(input),
            confidence: 0.7,
            reasoning: 'Enhanced description with bilingual format'
          }
        }
        break
    }

    return null
  }

  /**
   * Get contextual help text for a field
   */
  getContextualHelp(field: FormField, context?: AIContext): string {
    const helpTexts: Record<string, string> = {
      'pc_id': 'PC ID は機器種別 + 日付 + 連番で構成されます (例: LT241231001)',
      'email': '会社のメールアドレスを入力してください (@company.com)',
      'reason': '具体的な理由や目的を日本語と英語で記載してください',
      'group_mail': '部署に関連するグループメールを選択してください',
      'sharepoint_library': 'アクセスが必要なSharePointライブラリを選択してください'
    }

    return helpTexts[field.id] || field.helpTextJp || field.helpText || ''
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.validationCache.clear()
  }
}

// Export singleton instance
export const enhancedAIFormAssistant = EnhancedAIFormAssistant.getInstance()
