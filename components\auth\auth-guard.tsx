'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { usePermissions } from '@/lib/use-permissions'
import { UserRole } from '@/lib/auth'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
  requiredRole?: UserRole
  requiredPermission?: {
    resource: string
    action: string
    scope?: string
  }
  fallback?: React.ReactNode
}

export default function AuthGuard({ 
  children, 
  requiredRole, 
  requiredPermission,
  fallback 
}: AuthGuardProps) {
  const { user, loading } = useAuth()
  const { checkPermission } = usePermissions()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>読み込み中... / Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // Check role-based access
  if (requiredRole && user) {
    // This would need to check against user's actual role from database
    // For now, we'll just allow access
  }

  // Check permission-based access
  if (requiredPermission) {
    const hasAccess = checkPermission(
      requiredPermission.resource,
      requiredPermission.action,
      requiredPermission.scope
    )
    
    if (!hasAccess) {
      return fallback || (
        <div className="flex min-h-screen items-center justify-center">
          <Card>
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">アクセス拒否 / Access Denied</h2>
              <p className="text-gray-600">
                このページにアクセスする権限がありません。
                <br />
                You don't have permission to access this page.
              </p>
            </CardContent>
          </Card>
        </div>
      )
    }
  }

  return <>{children}</>
}
