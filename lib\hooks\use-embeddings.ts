import { useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'

export interface SearchResult {
  article_id: string
  title: string
  summary: string
  content: string
  similarity: number
}

interface UseEmbeddingsOptions {
  matchThreshold?: number
  matchCount?: number
}

export function useEmbeddings(options: UseEmbeddingsOptions = {}) {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const { toast } = useToast()

  const generateEmbedding = useCallback(
    async (articleId: string, language?: 'en' | 'jp' | 'both') => {
      setLoading(true)
      try {
        const { data, error } = await supabase.functions.invoke('generate-article-embeddings', {
          body: { articleId, language }
        })

        if (error) {
          throw error
        }

        toast({
          title: '成功',
          description: 'Embeddings generated successfully',
        })

        return data
      } catch (error) {
        console.error('Error generating embeddings:', error)
        toast({
          title: 'エラー',
          description: error instanceof Error ? error.message : 'Failed to generate embeddings',
          variant: 'destructive'
        })
        throw error
      } finally {
        setLoading(false)
      }
    },
    [toast]
  )

  const semanticSearch = useCallback(
    async (query: string, language: 'en' | 'jp') => {
      setLoading(true)
      try {
        // Generate embedding for the query
        const response = await fetch('/api/embeddings/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query,
            language,
            matchThreshold: options.matchThreshold,
            matchCount: options.matchCount
          })
        })

        if (!response.ok) {
          throw new Error('Search failed')
        }

        const data = await response.json()
        setResults(data.results || [])
        
        // Log search history
        await supabase.from('kb_search_history').insert({
          query,
          language,
          results_count: data.results?.length || 0,
          search_type: 'semantic'
        })

        return data.results
      } catch (error) {
        console.error('Semantic search error:', error)
        toast({
          title: 'エラー',
          description: error instanceof Error ? error.message : 'Search failed',
          variant: 'destructive'
        })
        throw error
      } finally {
        setLoading(false)
      }
    },
    [options.matchThreshold, options.matchCount, toast]
  )

  const updateStaleEmbeddings = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/embeddings/update-stale', {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Update failed')
      }

      const data = await response.json()
      
      toast({
        title: '成功',
        description: `Updated ${data.updated} embeddings`,
      })

      return data
    } catch (error) {
      console.error('Error updating embeddings:', error)
      toast({
        title: 'エラー',
        description: error instanceof Error ? error.message : 'Failed to update embeddings',
        variant: 'destructive'
      })
      throw error
    } finally {
      setLoading(false)
    }
  }, [toast])

  const findRelatedArticles = useCallback(
    async (articleId: string, language: 'en' | 'jp', limit: number = 5) => {
      setLoading(true)
      try {
        const response = await fetch('/api/embeddings/related', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            articleId,
            language,
            limit
          })
        })

        if (!response.ok) {
          throw new Error('Failed to find related articles')
        }

        const data = await response.json()
        return data.related || []
      } catch (error) {
        console.error('Error finding related articles:', error)
        toast({
          title: 'エラー',
          description: error instanceof Error ? error.message : 'Failed to find related articles',
          variant: 'destructive'
        })
        throw error
      } finally {
        setLoading(false)
      }
    },
    [toast]
  )

  return {
    loading,
    results,
    generateEmbedding,
    semanticSearch,
    updateStaleEmbeddings,
    findRelatedArticles
  }
}
