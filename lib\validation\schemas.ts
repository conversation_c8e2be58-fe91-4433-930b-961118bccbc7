/**
 * Validation Schemas
 * 
 * This module provides Zod schemas for validating input data
 * across the application. These schemas help ensure data integrity
 * and prevent security issues like injection attacks.
 */

import { z } from 'zod';

// Common validation patterns
const PATTERNS = {
  // Only allow alphanumeric characters, spaces, and common punctuation
  SAFE_STRING: /^[a-zA-Z0-9\s.,!?@#$%^&*()_+\-=\[\]{}|;:'"`<>\/\\]+$/,
  
  // Email validation pattern (basic)
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // Password requirements
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  
  // UUID v4 pattern
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  
  // ISO date format
  ISO_DATE: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/
};

// Error messages
const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Invalid email address',
  INVALID_PASSWORD: 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character',
  INVALID_UUID: 'Invalid ID format',
  INVALID_DATE: 'Invalid date format',
  STRING_TOO_SHORT: (min: number) => `Must be at least ${min} characters`,
  STRING_TOO_LONG: (max: number) => `Must be at most ${max} characters`,
  UNSAFE_STRING: 'Contains invalid characters'
};

// Base schemas for common types
export const baseSchemas = {
  id: z.string().regex(PATTERNS.UUID, { message: ERROR_MESSAGES.INVALID_UUID }),
  
  email: z.string()
    .min(5, { message: ERROR_MESSAGES.STRING_TOO_SHORT(5) })
    .max(255, { message: ERROR_MESSAGES.STRING_TOO_LONG(255) })
    .regex(PATTERNS.EMAIL, { message: ERROR_MESSAGES.INVALID_EMAIL })
    .toLowerCase(),
  
  password: z.string()
    .min(8, { message: ERROR_MESSAGES.STRING_TOO_SHORT(8) })
    .max(100, { message: ERROR_MESSAGES.STRING_TOO_LONG(100) })
    .regex(PATTERNS.PASSWORD, { message: ERROR_MESSAGES.INVALID_PASSWORD }),
  
  safeString: z.string()
    .regex(PATTERNS.SAFE_STRING, { message: ERROR_MESSAGES.UNSAFE_STRING }),
  
  name: z.string()
    .min(1, { message: ERROR_MESSAGES.STRING_TOO_SHORT(1) })
    .max(100, { message: ERROR_MESSAGES.STRING_TOO_LONG(100) })
    .regex(PATTERNS.SAFE_STRING, { message: ERROR_MESSAGES.UNSAFE_STRING }),
  
  date: z.string()
    .regex(PATTERNS.ISO_DATE, { message: ERROR_MESSAGES.INVALID_DATE })
    .or(z.date())
};

// Authentication schemas
export const authSchemas = {
  login: z.object({
    email: baseSchemas.email,
    password: z.string().min(1, { message: ERROR_MESSAGES.REQUIRED })
  }),
  
  register: z.object({
    email: baseSchemas.email,
    password: baseSchemas.password,
    firstName: baseSchemas.name,
    lastName: baseSchemas.name
  }),
  
  resetPassword: z.object({
    email: baseSchemas.email
  }),
  
  changePassword: z.object({
    currentPassword: z.string().min(1, { message: ERROR_MESSAGES.REQUIRED }),
    newPassword: baseSchemas.password,
    confirmPassword: baseSchemas.password
  }).refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"]
  })
};

// User schemas
export const userSchemas = {
  create: z.object({
    email: baseSchemas.email,
    firstName: baseSchemas.name,
    lastName: baseSchemas.name,
    roleId: baseSchemas.id.optional(),
    divisionId: baseSchemas.id.optional(),
    groupId: baseSchemas.id.optional()
  }),
  
  update: z.object({
    email: baseSchemas.email.optional(),
    firstName: baseSchemas.name.optional(),
    lastName: baseSchemas.name.optional(),
    roleId: baseSchemas.id.optional(),
    divisionId: baseSchemas.id.optional(),
    groupId: baseSchemas.id.optional(),
    isActive: z.boolean().optional()
  })
};

// Request schemas
export const requestSchemas = {
  create: z.object({
    title: baseSchemas.safeString
      .min(5, { message: ERROR_MESSAGES.STRING_TOO_SHORT(5) })
      .max(200, { message: ERROR_MESSAGES.STRING_TOO_LONG(200) }),
    description: baseSchemas.safeString
      .max(2000, { message: ERROR_MESSAGES.STRING_TOO_LONG(2000) })
      .optional(),
    priority: z.enum(['low', 'medium', 'high']),
    categoryId: baseSchemas.id
  }),
  
  update: z.object({
    title: baseSchemas.safeString
      .min(5, { message: ERROR_MESSAGES.STRING_TOO_SHORT(5) })
      .max(200, { message: ERROR_MESSAGES.STRING_TOO_LONG(200) })
      .optional(),
    description: baseSchemas.safeString
      .max(2000, { message: ERROR_MESSAGES.STRING_TOO_LONG(2000) })
      .optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
    status: z.enum(['draft', 'submitted', 'in_progress', 'completed', 'cancelled']).optional()
  })
};

// Export all schemas
export const schemas = {
  base: baseSchemas,
  auth: authSchemas,
  user: userSchemas,
  request: requestSchemas
};

export default schemas;