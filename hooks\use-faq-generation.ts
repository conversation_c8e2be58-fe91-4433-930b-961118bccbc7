import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { FAQGenerationService } from '@/lib/services/faq-generation-service';

interface GenerateFAQOptions {
  articleIds?: string[];
  categories?: string[];
  fromSearchPatterns?: boolean;
  timeRange?: { start: Date; end: Date };
  minConfidence?: number;
  maxFAQsPerArticle?: number;
  language?: 'ja' | 'en' | 'both';
}

export function useFAQGeneration() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedFAQs, setGeneratedFAQs] = useState<any[]>([]);
  const { toast } = useToast();
  const faqService = FAQGenerationService.getInstance();

  const generateFAQs = async (options: GenerateFAQOptions) => {
    setIsGenerating(true);
    
    try {
      let faqs: any[] = [];

      if (options.fromSearchPatterns && options.timeRange) {
        // Generate from search patterns
        faqs = await faqService.generateFAQsFromSearchPatterns(options.timeRange);
        toast({
          title: 'FAQs Generated from Search Patterns',
          description: `Generated ${faqs.length} FAQs from user search patterns`
        });
      } else {
        // Generate from KB articles
        faqs = await faqService.generateFAQsFromKB({
          categories: options.categories,
          minConfidence: options.minConfidence,
          maxFAQsPerArticle: options.maxFAQsPerArticle,
          language: options.language
        });
        toast({
          title: 'FAQs Generated from Knowledge Base',
          description: `Generated ${faqs.length} FAQs from knowledge base articles`
        });
      }

      setGeneratedFAQs(faqs);
      return faqs;
    } catch (error) {
      console.error('Error generating FAQs:', error);
      toast({
        title: 'Error Generating FAQs',
        description: 'Failed to generate FAQs. Please try again.',
        variant: 'destructive'
      });
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  const saveFAQs = async (faqs?: any[]) => {
    const faqsToSave = faqs || generatedFAQs;
    
    if (faqsToSave.length === 0) {
      toast({
        title: 'No FAQs to Save',
        description: 'Please generate FAQs first',
        variant: 'destructive'
      });
      return;
    }

    try {
      await faqService.saveFAQs(faqsToSave);
      toast({
        title: 'FAQs Saved',
        description: `Successfully saved ${faqsToSave.length} FAQs to the database`
      });
    } catch (error) {
      console.error('Error saving FAQs:', error);
      toast({
        title: 'Error Saving FAQs',
        description: 'Failed to save FAQs. Please try again.',
        variant: 'destructive'
      });
      throw error;
    }
  };

  const generateCategoryFAQs = async (category: string) => {
    setIsGenerating(true);
    
    try {
      const faqs = await faqService.generateCategoryFAQs(category);
      setGeneratedFAQs(faqs);
      toast({
        title: 'Category FAQs Generated',
        description: `Generated ${faqs.length} FAQs for category: ${category}`
      });
      return faqs;
    } catch (error) {
      console.error('Error generating category FAQs:', error);
      toast({
        title: 'Error Generating FAQs',
        description: 'Failed to generate category FAQs. Please try again.',
        variant: 'destructive'
      });
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  const updateFAQUsage = async () => {
    try {
      await faqService.updateFAQsBasedOnUsage();
      toast({
        title: 'FAQ Usage Updated',
        description: 'Successfully updated FAQ usage statistics'
      });
    } catch (error) {
      console.error('Error updating FAQ usage:', error);
      toast({
        title: 'Error Updating Usage',
        description: 'Failed to update FAQ usage. Please try again.',
        variant: 'destructive'
      });
      throw error;
    }
  };

  return {
    isGenerating,
    generatedFAQs,
    generateFAQs,
    saveFAQs,
    generateCategoryFAQs,
    updateFAQUsage
  };
}