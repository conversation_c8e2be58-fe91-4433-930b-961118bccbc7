'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/components/providers/supabase-provider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { format } from 'date-fns'
import { Key, RefreshCw, Shield, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import { useDepartmentAccess } from '@/lib/auth/hooks'
import { PasswordResetDialog } from '@/components/password-reset/reset-dialog'
import { PasswordResetList } from '@/components/password-reset/reset-list'
import { Loading } from '@/components/ui/loading'

export interface PasswordResetRequest {
  id: string
  submission_id: string
  service_type: string
  action_type: string
  parameters: {
    password_type: 'M365 Office' | 'Multi Factor Authenticator' | 'Windows'
    temporary_password?: string
    mfa_reset_code?: string
    reset_method?: string
  }
  affected_users: string[]
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
  execution_status?: any
  form_submission?: {
    id: string
    approval_status: string
    assigned_to?: string
    processed_by?: string
  }
  affected_staff?: Array<{
    id: string
    name_jp: string
    name_en: string
    email: string
    staff_id: string
  }>
}

export default function PasswordResetPage() {
  const [requests, setRequests] = useState<PasswordResetRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { supabase, user } = useSupabase()
  const { departmentId, hasFullAccess } = useDepartmentAccess()

  useEffect(() => {
    fetchRequests()
  }, [departmentId, selectedStatus, selectedType])

  const fetchRequests = async () => {
    try {
      setLoading(true)
      
      let query = supabase
        .from('request_items')
        .select(\`
          *,
          form_submission:form_submissions!inner(
            id,
            approval_status,
            assigned_to,
            processed_by
          )
        \`)
        .eq('service_type', 'Password Reset')
        .order('created_at', { ascending: false })

      // Apply status filter
      if (selectedStatus !== 'all') {
        query = query.eq('status', selectedStatus)
      }

      // Apply type filter
      if (selectedType !== 'all') {
        query = query.contains('parameters', { password_type: selectedType })
      }

      const { data, error } = await query

      if (error) throw error

      // Fetch affected users details
      const requestsWithUsers = await Promise.all(
        (data || []).map(async (request) => {
          if (request.affected_users && request.affected_users.length > 0) {
            const { data: staffData } = await supabase
              .from('staff')
              .select('id, name_jp, name_en, email, staff_id')
              .in('id', request.affected_users)

            return {
              ...request,
              affected_staff: staffData || []
            }
          }
          return {
            ...request,
            affected_staff: []
          }
        })
      )

      setRequests(requestsWithUsers)
    } catch (error) {
      console.error('Error fetching password reset requests:', error)
      toast.error('Failed to fetch password reset requests')
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (requestId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('request_items')
        .update({ 
          status: newStatus,
          completed_at: newStatus === 'completed' ? new Date().toISOString() : null
        })
        .eq('id', requestId)

      if (error) throw error

      toast.success('Request status updated successfully')
      fetchRequests()
    } catch (error) {
      console.error('Error updating request status:', error)
      toast.error('Failed to update request status')
    }
  }

  const generateTemporaryPassword = () => {
    const length = 12
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    return password
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      pending: { color: 'secondary', icon: Clock },
      processing: { color: 'default', icon: RefreshCw },
      completed: { color: 'success', icon: CheckCircle },
      failed: { color: 'destructive', icon: AlertCircle }
    }

    const variant = variants[status] || variants.pending
    const Icon = variant.icon

    return (
      <Badge variant={variant.color}>
        <Icon className="mr-1 h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getPasswordTypeBadge = (type: string) => {
    const variants: Record<string, any> = {
      'M365 Office': { color: 'default', icon: Key },
      'Multi Factor Authenticator': { color: 'secondary', icon: Shield },
      'Windows': { color: 'outline', icon: Key }
    }

    const variant = variants[type] || variants['M365 Office']
    const Icon = variant.icon

    return (
      <Badge variant={variant.color}>
        <Icon className="mr-1 h-3 w-3" />
        {type}
      </Badge>
    )
  }

  if (loading) {
    return <Loading />
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Password Reset Management</h1>
        <p className="text-muted-foreground">
          Manage password reset requests for M365 Office, Multi-Factor Authenticator, and Windows
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Password Reset Requests</CardTitle>
                <CardDescription>
                  Process and track password reset requests
                </CardDescription>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="type-filter">Type:</Label>
                  <Select
                    value={selectedType}
                    onValueChange={setSelectedType}
                  >
                    <SelectTrigger id="type-filter" className="w-[180px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="M365 Office">M365 Office</SelectItem>
                      <SelectItem value="Multi Factor Authenticator">MFA</SelectItem>
                      <SelectItem value="Windows">Windows</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="status-filter">Status:</Label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger id="status-filter" className="w-[150px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Requests</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={() => setIsDialogOpen(true)}>
                  <Key className="mr-2 h-4 w-4" />
                  New Request
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <PasswordResetList
              requests={requests}
              onStatusUpdate={handleStatusUpdate}
              getStatusBadge={getStatusBadge}
              getPasswordTypeBadge={getPasswordTypeBadge}
              generateTemporaryPassword={generateTemporaryPassword}
            />
          </CardContent>
        </Card>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <Key className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{requests.length}</div>
              <p className="text-xs text-muted-foreground">
                All password reset requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {requests.filter(r => r.status === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Awaiting processing
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processing</CardTitle>
              <RefreshCw className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {requests.filter(r => r.status === 'processing').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently being reset
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {requests.filter(r => r.status === 'completed').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Successfully reset
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <PasswordResetDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSuccess={() => {
          setIsDialogOpen(false)
          fetchRequests()
        }}
      />
    </div>
  )
}
