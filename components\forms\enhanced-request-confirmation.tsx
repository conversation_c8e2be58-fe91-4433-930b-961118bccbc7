'use client'

import { useState } from 'react'
import { RequestConfirmation, RequestItem } from '@/lib/request-types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Loader2, CheckCircle, AlertTriangle, Users, Settings, Mail, FolderOpen, Monitor, Key } from 'lucide-react'

interface RequestConfirmationProps {
  confirmation: RequestConfirmation
  onSubmit: () => void
  onEdit: () => void
  onCancel: () => void
  loading?: boolean
}

const serviceIcons: Record<string, any> = {
  'GM': Mail,
  'MB': Mail,
  'SPO': FolderOpen,
  'PCA': Monitor,
  'PWR': Key,
  'default': Settings
}

export default function RequestConfirmation({
  confirmation,
  onSubmit,
  onEdit,
  onCancel,
  loading = false
}: RequestConfirmationProps) {
  const [activeTab, setActiveTab] = useState<string>(
    confirmation.items.length > 0 ? confirmation.items[0].serviceCategory.code : ''
  )

  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case 'add':
        return 'default'
      case 'remove':
        return 'destructive'
      case 'update':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getActionText = (action: string) => {
    switch (action) {
      case 'add':
        return '追加 / Add'
      case 'remove':
        return '削除 / Remove'
      case 'update':
        return '更新 / Update'
      default:
        return action
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
          リクエスト確認 / Request Confirmation
        </CardTitle>
        <CardDescription>
          以下の内容でリクエストを送信します。内容を確認してください。
          <br />
          Please review the following request details before submission.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{confirmation.totalServices}</div>
            <div className="text-xs text-gray-600">サービス / Services</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{confirmation.totalUsers}</div>
            <div className="text-xs text-gray-600">ユーザー / Users</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {confirmation.items.reduce((sum, item) => sum + item.users.length, 0)}
            </div>
            <div className="text-xs text-gray-600">総アクション / Total Actions</div>
          </div>
          <div className="text-center">
            <Badge 
              variant={confirmation.status === 'ready' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {confirmation.status === 'ready' ? '送信準備完了 / Ready' : 'ドラフト / Draft'}
            </Badge>
          </div>
        </div>

        {/* Service Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-1">
            {confirmation.items.map((item) => {
              const IconComponent = serviceIcons[item.serviceCategory.code] || serviceIcons.default
              return (
                <TabsTrigger
                  key={item.serviceCategory.id}
                  value={item.serviceCategory.code}
                  className="flex items-center space-x-2 text-xs"
                >
                  <IconComponent className="h-4 w-4" />
                  <span className="truncate">{item.serviceCategory.name_jp}</span>
                  <Badge variant="outline" className="text-xs ml-1">
                    {item.users.length}
                  </Badge>
                </TabsTrigger>
              )
            })}
          </TabsList>

          {confirmation.items.map((item) => (
            <TabsContent 
              key={item.serviceCategory.id} 
              value={item.serviceCategory.code}
              className="space-y-4"
            >
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    {React.createElement(serviceIcons[item.serviceCategory.code] || serviceIcons.default, {
                      className: "mr-2 h-5 w-5"
                    })}
                    {item.serviceCategory.name_jp}
                  </CardTitle>
                  <CardDescription>
                    {item.serviceCategory.name_en}
                    <br />
                    アクション: <Badge variant={getActionBadgeVariant(item.action)} className="text-xs ml-1">
                      {getActionText(item.action)}
                    </Badge>
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Form Data Details */}
                  {Object.keys(item.formData).length > 0 && (
                    <div>
                      <h4 className="font-medium text-sm mb-2">
                        リクエスト詳細 / Request Details
                      </h4>
                      <div className="bg-gray-50 p-3 rounded text-sm space-y-1">
                        {Object.entries(item.formData).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="font-medium">{key}:</span>
                            <span className="text-gray-600">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <Separator />

                  {/* Affected Users */}
                  <div>
                    <h4 className="font-medium text-sm mb-3 flex items-center">
                      <Users className="mr-2 h-4 w-4" />
                      対象ユーザー / Affected Users ({item.users.length})
                    </h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {item.users.map((user) => (
                        <div
                          key={user.id}
                          className="p-3 border rounded-lg bg-white hover:bg-gray-50 transition-colors"
                        >
                          <div className="font-medium text-sm">{user.name_jp}</div>
                          <div className="text-xs text-gray-600">{user.email}</div>
                          <div className="mt-1 flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {user.division.name_jp}
                            </Badge>
                            {user.group && (
                              <Badge variant="secondary" className="text-xs">
                                {user.group.name_jp}
                              </Badge>
                            )}
                            {user.pc_id && (
                              <Badge variant="outline" className="text-xs">
                                PC: {user.pc_id}
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Warnings and Validation */}
        {confirmation.status !== 'ready' && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              リクエストに問題があります。編集して修正してください。
              <br />
              There are issues with the request. Please edit to fix them.
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={onEdit}
            disabled={loading}
          >
            編集 / Edit
          </Button>

          <div className="flex space-x-2">
            <Button
              variant="ghost"
              onClick={onCancel}
              disabled={loading}
            >
              キャンセル / Cancel
            </Button>

            <Button
              onClick={onSubmit}
              disabled={loading || confirmation.status !== 'ready'}
              className="min-w-32"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              リクエスト送信 / Submit Request
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
