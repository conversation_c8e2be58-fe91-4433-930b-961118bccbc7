"use strict";(()=>{var e={};e.id=1297,e.ids=[1297],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70715:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>k,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>f});var o=t(42706),a=t(28203),n=t(45994),i=t(39187),u=t(61487),d=t(2924),p=t(98837);let l=p.z.object({workflow_definition_id:p.z.string().uuid(),request_id:p.z.string().uuid().optional(),context_data:p.z.record(p.z.any()).optional()});async function c(e){try{let r=await (0,u.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=e.nextUrl.searchParams,a=o.get("status"),n=parseInt(o.get("limit")||"50"),d=parseInt(o.get("offset")||"0"),p=r.from("workflow_instances").select(`
        *,
        workflow_definition:workflow_definitions(*),
        request:request_forms(*),
        created_by_user:staff(name_en, name_jp)
      `).order("started_at",{ascending:!1}).range(d,d+n-1);a&&(p=p.eq("status",a));let{data:l,error:c}=await p;if(c)return i.NextResponse.json({error:c.message},{status:500});return i.NextResponse.json({data:l})}catch(e){return console.error("Error fetching workflow instances:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let r=await (0,u.createServerClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("*").eq("auth_id",t.id).single();if(!o)return i.NextResponse.json({error:"User not found"},{status:404});let a=await e.json(),n=l.safeParse(a);if(!n.success)return i.NextResponse.json({error:"Invalid request data",details:n.error.errors},{status:400});let{workflow_definition_id:p,request_id:c,context_data:f}=n.data,w=new d.I;try{let e=await w.startWorkflow({workflowDefinitionId:p,requestId:c,contextData:f||{},userId:o.id});return i.NextResponse.json({data:e},{status:201})}catch(e){return console.error("Error starting workflow:",e),i.NextResponse.json({error:e.message||"Failed to start workflow"},{status:400})}}catch(e){return console.error("Error in workflow start:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/instances/route",pathname:"/api/workflows/instances",filename:"route",bundlePath:"app/api/workflows/instances/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:k}=w;function j(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(70715));module.exports=s})();