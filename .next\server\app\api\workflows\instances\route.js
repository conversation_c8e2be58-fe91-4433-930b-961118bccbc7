"use strict";(()=>{var e={};e.id=1297,e.ids=[1297],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70715:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>f,serverHooks:()=>g,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>c});var o=t(42706),n=t(28203),a=t(45994),i=t(39187),u=t(52054),d=t(59913);!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let l=d.z.object({workflow_definition_id:d.z.string().uuid(),request_id:d.z.string().uuid().optional(),context_data:d.z.record(d.z.any()).optional()});async function p(e){try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=e.nextUrl.searchParams,n=o.get("status"),a=parseInt(o.get("limit")||"50"),u=parseInt(o.get("offset")||"0"),d=r.from("workflow_instances").select(`
        *,
        workflow_definition:workflow_definitions(*),
        request:request_forms(*),
        created_by_user:staff(name_en, name_jp)
      `).order("started_at",{ascending:!1}).range(u,u+a-1);n&&(d=d.eq("status",n));let{data:l,error:p}=await d;if(p)return i.NextResponse.json({error:p.message},{status:500});return i.NextResponse.json({data:l})}catch(e){return console.error("Error fetching workflow instances:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e){try{let r=await Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o}=await r.from("staff").select("*").eq("auth_id",t.id).single();if(!o)return i.NextResponse.json({error:"User not found"},{status:404});let n=await e.json(),a=l.safeParse(n);if(!a.success)return i.NextResponse.json({error:"Invalid request data",details:a.error.errors},{status:400});let{workflow_definition_id:d,request_id:p,context_data:c}=a.data,f=new u.I;try{let e=await f.startWorkflow({workflowDefinitionId:d,requestId:p,contextData:c||{},userId:o.id});return i.NextResponse.json({data:e},{status:201})}catch(e){return console.error("Error starting workflow:",e),i.NextResponse.json({error:e.message||"Failed to start workflow"},{status:400})}}catch(e){return console.error("Error in workflow start:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflows/instances/route",pathname:"/api/workflows/instances",filename:"route",bundlePath:"app/api/workflows/instances/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:x,serverHooks:g}=f;function j(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(70715));module.exports=s})();