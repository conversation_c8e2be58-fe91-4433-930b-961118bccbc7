// Batch Processing Types

export interface BatchOperation {
  id: string;
  batch_id: string;
  requester_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partially_completed';
  operation_type: 'multi_user_single_service' | 'multi_user_multi_service' | 'single_user_multi_service' | 'mixed_operations';
  total_items: number;
  completed_items: number;
  failed_items: number;
  error_details: ErrorDetail[];
  progress_percentage: number;
  started_at: string | null;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface BatchItem {
  id: string;
  batch_operation_id: string;
  request_item_id: string;
  item_order: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  error_message: string | null;
  processed_at: string | null;
  created_at: string;
}

export interface BatchValidationRule {
  id: string;
  service_category_id: string;
  rule_type: 'permission_check' | 'resource_availability' | 'duplicate_check';
  rule_config: Record<string, any>;
  is_active: boolean;
  created_at: string;
}

export interface BatchProgressLog {
  id: string;
  batch_operation_id: string;
  log_level: 'info' | 'warning' | 'error';
  message: string;
  details: Record<string, any>;
  created_at: string;
}

export interface ErrorDetail {
  item_id: string;
  error_code: string;
  error_message: string;
  timestamp: string;
}

export interface BatchRequest {
  operation_type: BatchOperation['operation_type'];
  items: BatchRequestItem[];
}

export interface BatchRequestItem {
  service_category_id: string;
  affected_users: string[];
  action_type: 'add' | 'remove' | 'update';
  target_resources: string[];
  request_details: Record<string, any>;
}

export interface BatchValidationResult {
  valid: boolean;
  errors: BatchValidationError[];
}

export interface BatchValidationError {
  item: BatchRequestItem;
  error: string;
}

export interface BatchProcessingResult {
  success: boolean;
  message: string;
  batch_id: string;
  processed_items: number;
  failed_items: number;
  errors?: ErrorDetail[];
}

export interface BatchStatusUpdate {
  batch_id: string;
  status: BatchOperation['status'];
  progress_percentage: number;
  completed_items: number;
  total_items: number;
  current_item?: string;
}

// Batch Processing Scenarios
export type BatchScenario = 
  | 'single_user_multiple_requests_add'
  | 'multiple_users_multiple_requests_add'
  | 'multiple_users_single_request_add'
  | 'single_user_multiple_requests_delete'
  | 'single_user_multiple_requests_mixed'
  | 'single_user_multiple_category_mixed';

export interface BatchScenarioConfig {
  scenario: BatchScenario;
  users: string[];
  services: {
    category_id: string;
    action: 'add' | 'remove' | 'update';
    resources: string[];
  }[];
}
