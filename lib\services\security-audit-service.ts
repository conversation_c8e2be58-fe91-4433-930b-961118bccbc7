import { createClient } from '@supabase/supabase-js';
import { Database } from '../database.types';

export type SecurityEventType = 
  | 'login_attempt' | 'login_success' | 'login_failed' | 'logout'
  | 'password_changed' | 'password_reset_requested' | 'password_reset_completed'
  | 'mfa_enabled' | 'mfa_disabled' | 'mfa_challenge_issued' | 'mfa_verified' | 'mfa_failed'
  | 'permission_granted' | 'permission_revoked' | 'role_changed'
  | 'sensitive_data_accessed' | 'sensitive_data_exported' | 'bulk_data_export'
  | 'security_setting_changed' | 'api_key_created' | 'api_key_revoked'
  | 'brute_force_detected' | 'suspicious_activity' | 'anomaly_detected';

export type SecuritySeverity = 'info' | 'warning' | 'high' | 'critical';

export interface SecurityEvent {
  eventType: SecurityEventType;
  userId?: string;
  targetUserId?: string;
  severity: SecuritySeverity;
  details?: Record<string, any>;
  resourceType?: string;
  resourceId?: string;
  riskScore?: number;
}

export interface SecurityAnomaly {
  id: string;
  userId: string;
  eventType: string;
  anomalyType: 'new_ip_address' | 'unusual_time' | 'high_volume_access';
  riskScore: number;
  createdAt: string;
}

export class SecurityAuditService {
  private supabase: ReturnType<typeof createClient<Database>>;

  constructor(supabaseClient: ReturnType<typeof createClient<Database>>) {
    this.supabase = supabaseClient;
  }

  /**
   * Log a security event
   */
  async logSecurityEvent(event: SecurityEvent): Promise<string> {
    try {
      // Get current request context
      const ipAddress = this.getClientIp();
      const userAgent = this.getUserAgent();
      const sessionId = this.getSessionId();

      // Set context for database function
      await this.setRequestContext(ipAddress, userAgent, sessionId);

      const { data, error } = await this.supabase.rpc('log_security_event', {
        p_event_type: event.eventType,
        p_user_id: event.userId || null,
        p_severity: event.severity,
        p_details: event.details || {},
        p_resource_type: event.resourceType || null,
        p_resource_id: event.resourceId || null,
        p_target_user_id: event.targetUserId || null,
        p_risk_score: event.riskScore || 0
      });

      if (error) throw error;
      return data as string;
    } catch (error) {
      console.error('Failed to log security event:', error);
      throw error;
    }
  }

  /**
   * Log login attempt
   */
  async logLoginAttempt(userId: string | null, success: boolean, email?: string): Promise<void> {
    await this.logSecurityEvent({
      eventType: success ? 'login_success' : 'login_failed',
      userId: success ? userId : null,
      severity: success ? 'info' : 'warning',
      details: { email },
      riskScore: success ? 0 : 20
    });
  }

  /**
   * Log MFA event
   */
  async logMFAEvent(
    userId: string,
    eventType: 'mfa_enabled' | 'mfa_disabled' | 'mfa_verified' | 'mfa_failed',
    method?: string
  ): Promise<void> {
    await this.logSecurityEvent({
      eventType,
      userId,
      severity: eventType === 'mfa_failed' ? 'warning' : 'info',
      details: { method },
      riskScore: eventType === 'mfa_failed' ? 30 : 0
    });
  }

  /**
   * Log sensitive data access
   */
  async logSensitiveDataAccess(
    userId: string,
    resourceType: string,
    resourceId: string,
    accessedFields: string[]
  ): Promise<void> {
    await this.logSecurityEvent({
      eventType: 'sensitive_data_accessed',
      userId,
      severity: 'info',
      resourceType,
      resourceId,
      details: { accessedFields },
      riskScore: 10
    });
  }

  /**
   * Log permission change
   */
  async logPermissionChange(
    targetUserId: string,
    changedBy: string,
    oldRole: string,
    newRole: string
  ): Promise<void> {
    const isPrivilegedRole = ['Global Administrator', 'Web App System Administrator'].includes(newRole);
    
    await this.logSecurityEvent({
      eventType: 'role_changed',
      userId: changedBy,
      targetUserId,
      severity: isPrivilegedRole ? 'high' : 'warning',
      details: { oldRole, newRole },
      riskScore: isPrivilegedRole ? 60 : 30
    });
  }

  /**
   * Get security anomalies
   */
  async getSecurityAnomalies(limit: number = 50): Promise<SecurityAnomaly[]> {
    try {
      const { data, error } = await this.supabase
        .from('security_anomalies')
        .select('*')
        .limit(limit)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get security anomalies:', error);
      throw error;
    }
  }

  /**
   * Get security compliance report
   */
  async getComplianceReport(days: number = 30) {
    try {
      const { data, error } = await this.supabase
        .from('security_compliance_report')
        .select('*')
        .gte('date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get compliance report:', error);
      throw error;
    }
  }

  /**
   * Generate security report
   */
  async generateSecurityReport(startDate: Date, endDate: Date) {
    try {
      const { data, error } = await this.supabase.rpc('generate_security_report', {
        p_start_date: startDate.toISOString(),
        p_end_date: endDate.toISOString()
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to generate security report:', error);
      throw error;
    }
  }

  /**
   * Check for brute force attempts
   */
  async checkBruteForce(email: string): Promise<{
    isBlocked: boolean;
    attemptCount: number;
    blockExpiry?: Date;
  }> {
    try {
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      
      const { data, error } = await this.supabase
        .from('security_audit_logs')
        .select('*')
        .eq('event_type', 'login_failed')
        .contains('details', { email })
        .gte('created_at', fifteenMinutesAgo.toISOString());

      if (error) throw error;

      const attemptCount = data?.length || 0;
      const isBlocked = attemptCount >= 5;
      
      return {
        isBlocked,
        attemptCount,
        blockExpiry: isBlocked ? new Date(Date.now() + 30 * 60 * 1000) : undefined
      };
    } catch (error) {
      console.error('Failed to check brute force:', error);
      return { isBlocked: false, attemptCount: 0 };
    }
  }

  /**
   * Get user security events
   */
  async getUserSecurityEvents(userId: string, limit: number = 20) {
    try {
      const { data, error } = await this.supabase
        .from('security_audit_logs')
        .select('*')
        .eq('user_id', userId)
        .in('event_type', ['login_success', 'password_changed', 'mfa_enabled', 'mfa_disabled'])
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get user security events:', error);
      throw error;
    }
  }

  /**
   * Resolve security alert
   */
  async resolveSecurityAlert(alertId: string, resolvedBy: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('security_audit_logs')
        .update({
          resolved: true,
          resolved_by: resolvedBy,
          resolved_at: new Date().toISOString()
        })
        .eq('id', alertId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to resolve security alert:', error);
      throw error;
    }
  }

  /**
   * Archive old security logs
   */
  async archiveOldLogs(): Promise<number> {
    try {
      const { data, error } = await this.supabase.rpc('archive_old_security_logs');
      if (error) throw error;
      return data as number;
    } catch (error) {
      console.error('Failed to archive old logs:', error);
      throw error;
    }
  }

  // Helper methods
  private getClientIp(): string {
    // In a real implementation, this would get the actual client IP
    // from the request headers (X-Forwarded-For, etc.)
    return '0.0.0.0';
  }

  private getUserAgent(): string {
    // In a real implementation, this would get the User-Agent from headers
    return typeof window !== 'undefined' ? window.navigator.userAgent : 'Server';
  }

  private getSessionId(): string {
    // In a real implementation, this would get the session ID
    return 'session_' + Date.now();
  }

  private async setRequestContext(ip: string, userAgent: string, sessionId: string): Promise<void> {
    // In a real implementation, this would set PostgreSQL session variables
    // that the database functions can access
    // For now, this is a placeholder
  }
}
