/**
 * AI Cost Management API
 * Comprehensive API for AI usage tracking, cost analysis, and budget management
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getUsageTracker } from '@/lib/ai/usage-tracker'
import { getCostAnalyzer } from '@/lib/ai/cost-analyzer'
import { getReliabilityManager } from '@/lib/ai/reliability-manager'
import { getContentFilter } from '@/lib/ai/content-filter'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, organization_id')
      .eq('user_id', user.id)
      .single()

    if (!profile || !['admin', 'system_admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const url = new URL(request.url)
    const section = url.searchParams.get('section') || 'overview'
    const entityType = url.searchParams.get('entityType') || 'organization'
    const entityId = url.searchParams.get('entityId') || profile.organization_id
    const period = url.searchParams.get('period') || 'month'
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')

    const usageTracker = getUsageTracker()
    const costAnalyzer = getCostAnalyzer()
    const reliabilityManager = getReliabilityManager()
    const contentFilter = getContentFilter()

    let data: any = {}

    switch (section) {
      case 'overview':
        data = await getOverview(usageTracker, costAnalyzer, entityType as any, entityId, period as any)
        break
      
      case 'usage':
        data = await getUsageAnalytics(usageTracker, entityType as any, entityId, period as any)
        break
      
      case 'costs':
        data = await getCostAnalytics(costAnalyzer, entityType as any, entityId, startDate, endDate)
        break
      
      case 'quotas':
        data = await getQuotaManagement(usageTracker, entityType as any, entityId)
        break
      
      case 'predictions':
        data = await getCostPredictions(costAnalyzer, entityType as any, entityId, period as any)
        break
      
      case 'optimizations':
        data = await getOptimizationRecommendations(costAnalyzer, entityType as any, entityId)
        break
      
      case 'reliability':
        data = await getReliabilityMetrics(reliabilityManager)
        break
      
      case 'filtering':
        data = await getFilteringAnalytics(contentFilter, period as any)
        break
      
      case 'models':
        data = await getModelComparison(costAnalyzer, entityType as any, entityId, period as any)
        break
      
      case 'alerts':
        data = await getBudgetAlerts(usageTracker, entityType as any, entityId)
        break
      
      default:
        return NextResponse.json({ error: 'Invalid section' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      section,
      data,
      metadata: {
        entityType,
        entityId,
        period,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('AI Cost API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Check authentication and authorization
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, organization_id')
      .eq('user_id', user.id)
      .single()

    if (!profile || !['admin', 'system_admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const { action, ...params } = body

    const usageTracker = getUsageTracker()
    const reliabilityManager = getReliabilityManager()
    const contentFilter = getContentFilter()

    let result: any = {}

    switch (action) {
      case 'set_quota':
        result = await setQuota(usageTracker, params)
        break
      
      case 'reset_circuit_breaker':
        result = await resetCircuitBreaker(reliabilityManager, params)
        break
      
      case 'update_content_policy':
        result = await updateContentPolicy(contentFilter, params)
        break
      
      case 'update_fallback_strategy':
        result = await updateFallbackStrategy(reliabilityManager, params)
        break
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      action,
      result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI Cost API POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper functions for different sections
async function getOverview(
  usageTracker: any,
  costAnalyzer: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string,
  period: 'day' | 'week' | 'month' | 'year'
) {
  const [usageStats, quotas, alerts] = await Promise.all([
    usageTracker.getUsageStats(entityType, entityId, period),
    usageTracker.getQuotaUsage(entityType, entityId),
    usageTracker.getBudgetAlerts(entityType, entityId)
  ])

  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 30)

  const costBreakdown = await costAnalyzer.getCostBreakdown(entityType, entityId, startDate, endDate)

  return {
    summary: {
      totalRequests: usageStats.totalRequests,
      totalCost: usageStats.totalCost,
      totalTokens: usageStats.totalTokens,
      successRate: usageStats.successRate,
      avgResponseTime: usageStats.avgResponseTime
    },
    quotas: quotas.map(quota => ({
      ...quota,
      usagePercentage: quota.limit_value > 0 ? (quota.current_usage / quota.limit_value) * 100 : 0,
      status: getQuotaStatus(quota)
    })),
    recentAlerts: alerts.slice(0, 5),
    costBreakdown: {
      byProvider: costBreakdown.byProvider,
      byFeature: costBreakdown.byFeature,
      total: costBreakdown.total
    },
    trends: usageStats.costTrend
  }
}

async function getUsageAnalytics(
  usageTracker: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string,
  period: 'day' | 'week' | 'month' | 'year'
) {
  const stats = await usageTracker.getUsageStats(entityType, entityId, period)
  
  return {
    overview: {
      totalRequests: stats.totalRequests,
      successfulRequests: Math.round(stats.totalRequests * (stats.successRate / 100)),
      failedRequests: stats.totalRequests - Math.round(stats.totalRequests * (stats.successRate / 100)),
      totalTokens: stats.totalTokens,
      totalCost: stats.totalCost,
      avgResponseTime: stats.avgResponseTime
    },
    topModels: stats.topModels,
    topFeatures: stats.topFeatures,
    trends: stats.costTrend,
    performance: {
      successRate: stats.successRate,
      avgResponseTime: stats.avgResponseTime,
      cacheHitRate: 0 // Would be calculated from actual data
    }
  }
}

async function getCostAnalytics(
  costAnalyzer: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string,
  startDate?: string,
  endDate?: string
) {
  const end = endDate ? new Date(endDate) : new Date()
  const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)

  const [breakdown, trends, comparison] = await Promise.all([
    costAnalyzer.getCostBreakdown(entityType, entityId, start, end),
    costAnalyzer.getCostTrends(entityType, entityId, start, end),
    costAnalyzer.compareModelCosts(entityType, entityId, 'month')
  ])

  return {
    breakdown,
    trends,
    modelComparison: comparison,
    insights: {
      mostExpensive: Object.entries(breakdown.byProvider)
        .sort(([,a], [,b]) => (b as number) - (a as number))[0],
      fastestGrowing: trends.length > 1 ? 
        trends[trends.length - 1].changePercentage : 0,
      efficiency: comparison.models.length > 0 ? 
        comparison.models.reduce((sum, m) => sum + m.efficiency, 0) / comparison.models.length : 0
    }
  }
}

async function getQuotaManagement(
  usageTracker: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string
) {
  const quotas = await usageTracker.getQuotaUsage(entityType, entityId)
  
  return {
    quotas: quotas.map(quota => ({
      ...quota,
      usagePercentage: quota.limit_value > 0 ? (quota.current_usage / quota.limit_value) * 100 : 0,
      status: getQuotaStatus(quota),
      timeUntilReset: new Date(quota.reset_at).getTime() - Date.now()
    })),
    recommendations: generateQuotaRecommendations(quotas)
  }
}

async function getCostPredictions(
  costAnalyzer: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string,
  period: 'daily' | 'weekly' | 'monthly' | 'yearly'
) {
  const prediction = await costAnalyzer.predictCosts(entityType, entityId, period)
  
  return {
    prediction,
    scenarios: {
      conservative: {
        ...prediction,
        predictedCost: prediction.predictedCost * 0.8
      },
      aggressive: {
        ...prediction,
        predictedCost: prediction.predictedCost * 1.3
      }
    },
    recommendations: generatePredictionRecommendations(prediction)
  }
}

async function getOptimizationRecommendations(
  costAnalyzer: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string
) {
  const optimizations = await costAnalyzer.getOptimizationRecommendations(entityType, entityId)
  
  const totalSavings = optimizations.reduce((sum, opt) => sum + opt.savings, 0)
  const implementationEffort = optimizations.reduce((sum, opt) => {
    const effortScore = opt.effort === 'low' ? 1 : opt.effort === 'medium' ? 2 : 3
    return sum + effortScore
  }, 0)

  return {
    optimizations,
    summary: {
      totalPotentialSavings: totalSavings,
      averageImplementationEffort: implementationEffort / optimizations.length,
      quickWins: optimizations.filter(opt => opt.effort === 'low' && opt.impact !== 'low'),
      highImpact: optimizations.filter(opt => opt.impact === 'high')
    }
  }
}

async function getReliabilityMetrics(reliabilityManager: any) {
  const [serviceHealth, circuitBreakers] = await Promise.all([
    reliabilityManager.getServiceHealth(),
    reliabilityManager.getCircuitBreakerStates()
  ])

  return {
    serviceHealth,
    circuitBreakers,
    summary: {
      healthyServices: serviceHealth.filter(s => s.status === 'healthy').length,
      degradedServices: serviceHealth.filter(s => s.status === 'degraded').length,
      unhealthyServices: serviceHealth.filter(s => s.status === 'unhealthy').length,
      openCircuitBreakers: circuitBreakers.filter(cb => cb.state === 'open').length
    }
  }
}

async function getFilteringAnalytics(contentFilter: any, period: 'day' | 'week' | 'month') {
  const stats = await contentFilter.getFilteringStats(period)
  
  return {
    stats,
    insights: {
      topViolationCategory: Object.entries(stats.byCategory)
        .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0] || 'none',
      filteringRate: stats.totalFiltered > 0 ? 
        (stats.totalFiltered / (stats.totalFiltered + 1000)) * 100 : 0, // Assuming 1000 total requests
      severityDistribution: stats.bySeverity
    }
  }
}

async function getModelComparison(
  costAnalyzer: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string,
  period: 'week' | 'month' | 'quarter'
) {
  const comparison = await costAnalyzer.compareModelCosts(entityType, entityId, period)
  
  return {
    ...comparison,
    insights: {
      mostCostEffective: comparison.models.length > 0 ? 
        comparison.models.reduce((best, current) => 
          current.avgCostPerToken < best.avgCostPerToken ? current : best
        ) : null,
      leastEfficient: comparison.models.length > 0 ? 
        comparison.models.reduce((worst, current) => 
          current.efficiency < worst.efficiency ? current : worst
        ) : null
    }
  }
}

async function getBudgetAlerts(
  usageTracker: any,
  entityType: 'user' | 'organization' | 'global',
  entityId: string
) {
  const alerts = await usageTracker.getBudgetAlerts(entityType, entityId)
  
  return {
    alerts,
    summary: {
      total: alerts.length,
      critical: alerts.filter(a => a.alert_type === 'exceeded').length,
      warnings: alerts.filter(a => a.alert_type === 'warning').length,
      recent: alerts.filter(a => 
        new Date(a.created_at).getTime() > Date.now() - 24 * 60 * 60 * 1000
      ).length
    }
  }
}

// Action handlers
async function setQuota(usageTracker: any, params: any) {
  const { entityType, entityId, period, quotaType, limitValue } = params
  
  await usageTracker.setQuota(entityType, entityId, period, quotaType, limitValue)
  
  return { message: 'Quota updated successfully' }
}

async function resetCircuitBreaker(reliabilityManager: any, params: any) {
  const { provider, model } = params
  
  reliabilityManager.resetCircuitBreaker(provider, model)
  
  return { message: 'Circuit breaker reset successfully' }
}

async function updateContentPolicy(contentFilter: any, params: any) {
  const { category, policy } = params
  
  contentFilter.updateContentPolicy(category, policy)
  
  return { message: 'Content policy updated successfully' }
}

async function updateFallbackStrategy(reliabilityManager: any, params: any) {
  const { feature, strategy } = params
  
  reliabilityManager.updateFallbackStrategy(feature, strategy)
  
  return { message: 'Fallback strategy updated successfully' }
}

// Helper functions
function getQuotaStatus(quota: any): string {
  const usagePercentage = quota.limit_value > 0 ? (quota.current_usage / quota.limit_value) * 100 : 0
  const isExpired = new Date(quota.reset_at) <= new Date()
  
  if (isExpired) return 'expired'
  if (usagePercentage >= 100) return 'exceeded'
  if (usagePercentage >= 90) return 'warning'
  return 'normal'
}

function generateQuotaRecommendations(quotas: any[]): string[] {
  const recommendations: string[] = []
  
  for (const quota of quotas) {
    const usagePercentage = quota.limit_value > 0 ? (quota.current_usage / quota.limit_value) * 100 : 0
    
    if (usagePercentage > 80) {
      recommendations.push(`Consider increasing ${quota.quota_type} quota for ${quota.entity_type}`)
    }
    
    if (usagePercentage < 20) {
      recommendations.push(`${quota.quota_type} quota for ${quota.entity_type} may be too high`)
    }
  }
  
  return recommendations
}

function generatePredictionRecommendations(prediction: any): string[] {
  const recommendations: string[] = []
  
  if (prediction.confidence < 0.7) {
    recommendations.push('Prediction confidence is low - consider gathering more historical data')
  }
  
  if (prediction.factors.trend > 0.2) {
    recommendations.push('Costs are trending upward - consider optimization strategies')
  }
  
  if (prediction.factors.growth > 0.3) {
    recommendations.push('High growth rate detected - review budget allocations')
  }
  
  return recommendations
}
