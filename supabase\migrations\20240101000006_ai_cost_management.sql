-- AI Cost Management Database Schema
-- Comprehensive tracking of AI usage, costs, quotas, and analytics

-- AI Usage Logs Table
CREATE TABLE ai_usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    provider TEXT NOT NULL CHECK (provider IN ('openai', 'anthropic', 'google', 'azure', 'huggingface')),
    model TEXT NOT NULL,
    feature TEXT NOT NULL,
    request_type TEXT NOT NULL CHECK (request_type IN ('chat', 'completion', 'embedding', 'image', 'audio', 'moderation')),
    input_tokens INTEGER NOT NULL DEFAULT 0,
    output_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER GENERATED ALWAYS AS (input_tokens + output_tokens) STORED,
    estimated_cost DECIMAL(10,6) NOT NULL DEFAULT 0,
    actual_cost DECIMAL(10,6),
    request_duration INTEGER NOT NULL DEFAULT 0, -- milliseconds
    success BOOLEAN NOT NULL DEFAULT true,
    error_type TEXT,
    error_message TEXT,
    cache_hit BOOLEAN NOT NULL DEFAULT false,
    from_fallback BOOLEAN NOT NULL DEFAULT false,
    degraded_mode BOOLEAN NOT NULL DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_ai_usage_logs_user_id ON ai_usage_logs(user_id);
CREATE INDEX idx_ai_usage_logs_organization_id ON ai_usage_logs(organization_id);
CREATE INDEX idx_ai_usage_logs_provider_model ON ai_usage_logs(provider, model);
CREATE INDEX idx_ai_usage_logs_feature ON ai_usage_logs(feature);
CREATE INDEX idx_ai_usage_logs_created_at ON ai_usage_logs(created_at DESC);
CREATE INDEX idx_ai_usage_logs_cost ON ai_usage_logs(estimated_cost DESC);
CREATE INDEX idx_ai_usage_logs_success ON ai_usage_logs(success, created_at DESC);

-- Composite indexes for common queries
CREATE INDEX idx_ai_usage_logs_user_date ON ai_usage_logs(user_id, created_at DESC);
CREATE INDEX idx_ai_usage_logs_org_date ON ai_usage_logs(organization_id, created_at DESC);
CREATE INDEX idx_ai_usage_logs_provider_date ON ai_usage_logs(provider, created_at DESC);

-- AI Usage Quotas Table
CREATE TABLE ai_usage_quotas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type TEXT NOT NULL CHECK (entity_type IN ('user', 'organization', 'global')),
    entity_id TEXT NOT NULL, -- user_id, organization_id, or 'global'
    period TEXT NOT NULL CHECK (period IN ('daily', 'weekly', 'monthly', 'yearly')),
    quota_type TEXT NOT NULL CHECK (quota_type IN ('requests', 'tokens', 'cost')),
    limit_value DECIMAL(15,6) NOT NULL,
    current_usage DECIMAL(15,6) NOT NULL DEFAULT 0,
    reset_at TIMESTAMP WITH TIME ZONE NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique quota per entity/period/type
    UNIQUE(entity_type, entity_id, period, quota_type)
);

-- Indexes for quota management
CREATE INDEX idx_ai_usage_quotas_entity ON ai_usage_quotas(entity_type, entity_id);
CREATE INDEX idx_ai_usage_quotas_reset_at ON ai_usage_quotas(reset_at);
CREATE INDEX idx_ai_usage_quotas_enabled ON ai_usage_quotas(enabled);

-- AI Budget Alerts Table
CREATE TABLE ai_budget_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type TEXT NOT NULL CHECK (entity_type IN ('user', 'organization', 'global')),
    entity_id TEXT NOT NULL,
    alert_type TEXT NOT NULL CHECK (alert_type IN ('warning', 'limit', 'exceeded')),
    threshold_percentage INTEGER NOT NULL CHECK (threshold_percentage BETWEEN 1 AND 100),
    current_percentage DECIMAL(5,2) NOT NULL,
    period TEXT NOT NULL CHECK (period IN ('daily', 'weekly', 'monthly')),
    quota_type TEXT NOT NULL CHECK (quota_type IN ('requests', 'tokens', 'cost')),
    current_value DECIMAL(15,6) NOT NULL,
    limit_value DECIMAL(15,6) NOT NULL,
    notification_sent BOOLEAN NOT NULL DEFAULT false,
    notification_channels TEXT[] DEFAULT ARRAY['email'],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for alert management
CREATE INDEX idx_ai_budget_alerts_entity ON ai_budget_alerts(entity_type, entity_id);
CREATE INDEX idx_ai_budget_alerts_type ON ai_budget_alerts(alert_type);
CREATE INDEX idx_ai_budget_alerts_notification ON ai_budget_alerts(notification_sent, created_at DESC);
CREATE INDEX idx_ai_budget_alerts_created_at ON ai_budget_alerts(created_at DESC);

-- AI Cost Models Table (for pricing updates)
CREATE TABLE ai_cost_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider TEXT NOT NULL,
    model TEXT NOT NULL,
    input_cost_per_1k DECIMAL(10,8) NOT NULL,
    output_cost_per_1k DECIMAL(10,8) NOT NULL,
    request_cost DECIMAL(10,8) DEFAULT 0,
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deprecated_date TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique model per provider at any given time
    UNIQUE(provider, model, effective_date)
);

-- Indexes for cost model lookups
CREATE INDEX idx_ai_cost_models_provider_model ON ai_cost_models(provider, model);
CREATE INDEX idx_ai_cost_models_effective_date ON ai_cost_models(effective_date DESC);
CREATE INDEX idx_ai_cost_models_active ON ai_cost_models(provider, model, effective_date DESC) 
    WHERE deprecated_date IS NULL;

-- AI Service Health Table
CREATE TABLE ai_service_health (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider TEXT NOT NULL,
    model TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('healthy', 'degraded', 'unhealthy', 'maintenance')),
    response_time_avg INTEGER, -- milliseconds
    error_rate DECIMAL(5,4), -- percentage as decimal (0.05 = 5%)
    success_rate DECIMAL(5,4), -- percentage as decimal (0.95 = 95%)
    consecutive_failures INTEGER DEFAULT 0,
    last_success_at TIMESTAMP WITH TIME ZONE,
    last_failure_at TIMESTAMP WITH TIME ZONE,
    last_error_message TEXT,
    circuit_breaker_state TEXT CHECK (circuit_breaker_state IN ('closed', 'open', 'half-open')),
    next_retry_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique health record per provider/model
    UNIQUE(provider, model)
);

-- Indexes for service health monitoring
CREATE INDEX idx_ai_service_health_provider ON ai_service_health(provider);
CREATE INDEX idx_ai_service_health_status ON ai_service_health(status);
CREATE INDEX idx_ai_service_health_circuit_breaker ON ai_service_health(circuit_breaker_state);
CREATE INDEX idx_ai_service_health_updated_at ON ai_service_health(updated_at DESC);

-- AI Content Filter Logs Table
CREATE TABLE ai_content_filter_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    filter_type TEXT NOT NULL CHECK (filter_type IN ('input', 'output')),
    action_taken TEXT NOT NULL CHECK (action_taken IN ('allowed', 'blocked', 'sanitized', 'warned')),
    violation_categories TEXT[] DEFAULT ARRAY[]::TEXT[],
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    confidence DECIMAL(3,2) CHECK (confidence BETWEEN 0 AND 1),
    original_content_hash TEXT NOT NULL, -- Hash of original content for privacy
    sanitized_content_hash TEXT, -- Hash of sanitized content
    feature TEXT NOT NULL,
    provider TEXT,
    model TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for content filter analytics
CREATE INDEX idx_ai_content_filter_logs_user_id ON ai_content_filter_logs(user_id);
CREATE INDEX idx_ai_content_filter_logs_organization_id ON ai_content_filter_logs(organization_id);
CREATE INDEX idx_ai_content_filter_logs_action ON ai_content_filter_logs(action_taken);
CREATE INDEX idx_ai_content_filter_logs_severity ON ai_content_filter_logs(severity);
CREATE INDEX idx_ai_content_filter_logs_created_at ON ai_content_filter_logs(created_at DESC);
CREATE INDEX idx_ai_content_filter_logs_categories ON ai_content_filter_logs USING GIN(violation_categories);

-- AI Feature Analytics Table
CREATE TABLE ai_feature_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    feature TEXT NOT NULL,
    date DATE NOT NULL,
    total_requests INTEGER NOT NULL DEFAULT 0,
    successful_requests INTEGER NOT NULL DEFAULT 0,
    failed_requests INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    total_cost DECIMAL(10,6) NOT NULL DEFAULT 0,
    avg_response_time INTEGER NOT NULL DEFAULT 0, -- milliseconds
    cache_hit_rate DECIMAL(5,4), -- percentage as decimal
    user_satisfaction_score DECIMAL(3,2), -- 1-5 scale
    estimated_time_saved INTEGER DEFAULT 0, -- minutes
    estimated_value DECIMAL(10,2) DEFAULT 0, -- dollars
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique analytics per org/feature/date
    UNIQUE(organization_id, feature, date)
);

-- Indexes for feature analytics
CREATE INDEX idx_ai_feature_analytics_org_feature ON ai_feature_analytics(organization_id, feature);
CREATE INDEX idx_ai_feature_analytics_date ON ai_feature_analytics(date DESC);
CREATE INDEX idx_ai_feature_analytics_cost ON ai_feature_analytics(total_cost DESC);

-- Functions for automatic quota management
CREATE OR REPLACE FUNCTION reset_expired_quotas()
RETURNS void AS $$
BEGIN
    UPDATE ai_usage_quotas 
    SET current_usage = 0,
        reset_at = CASE 
            WHEN period = 'daily' THEN (CURRENT_DATE + INTERVAL '1 day')::timestamp with time zone
            WHEN period = 'weekly' THEN (date_trunc('week', CURRENT_DATE) + INTERVAL '1 week')::timestamp with time zone
            WHEN period = 'monthly' THEN (date_trunc('month', CURRENT_DATE) + INTERVAL '1 month')::timestamp with time zone
            WHEN period = 'yearly' THEN (date_trunc('year', CURRENT_DATE) + INTERVAL '1 year')::timestamp with time zone
        END,
        updated_at = NOW()
    WHERE reset_at <= NOW() AND enabled = true;
END;
$$ LANGUAGE plpgsql;

-- Function to check quota before AI request
CREATE OR REPLACE FUNCTION check_ai_quota(
    p_entity_type TEXT,
    p_entity_id TEXT,
    p_estimated_cost DECIMAL,
    p_estimated_tokens INTEGER
)
RETURNS TABLE(
    allowed BOOLEAN,
    quota_type TEXT,
    current_usage DECIMAL,
    limit_value DECIMAL,
    reason TEXT
) AS $$
BEGIN
    -- Reset expired quotas first
    PERFORM reset_expired_quotas();
    
    -- Check each quota type
    FOR quota_type, current_usage, limit_value IN
        SELECT q.quota_type, q.current_usage, q.limit_value
        FROM ai_usage_quotas q
        WHERE q.entity_type = p_entity_type 
        AND q.entity_id = p_entity_id 
        AND q.enabled = true
        AND q.reset_at > NOW()
    LOOP
        CASE quota_type
            WHEN 'cost' THEN
                IF current_usage + p_estimated_cost > limit_value THEN
                    allowed := false;
                    reason := 'Cost quota exceeded';
                    RETURN NEXT;
                END IF;
            WHEN 'tokens' THEN
                IF current_usage + p_estimated_tokens > limit_value THEN
                    allowed := false;
                    reason := 'Token quota exceeded';
                    RETURN NEXT;
                END IF;
            WHEN 'requests' THEN
                IF current_usage + 1 > limit_value THEN
                    allowed := false;
                    reason := 'Request quota exceeded';
                    RETURN NEXT;
                END IF;
        END CASE;
    END LOOP;
    
    -- If we get here, all quotas are within limits
    allowed := true;
    reason := 'All quotas within limits';
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Function to update quota usage after AI request
CREATE OR REPLACE FUNCTION update_ai_quota_usage(
    p_entity_type TEXT,
    p_entity_id TEXT,
    p_cost DECIMAL,
    p_tokens INTEGER
)
RETURNS void AS $$
BEGIN
    -- Update cost quota
    UPDATE ai_usage_quotas 
    SET current_usage = current_usage + p_cost,
        updated_at = NOW()
    WHERE entity_type = p_entity_type 
    AND entity_id = p_entity_id 
    AND quota_type = 'cost'
    AND enabled = true;
    
    -- Update token quota
    UPDATE ai_usage_quotas 
    SET current_usage = current_usage + p_tokens,
        updated_at = NOW()
    WHERE entity_type = p_entity_type 
    AND entity_id = p_entity_id 
    AND quota_type = 'tokens'
    AND enabled = true;
    
    -- Update request quota
    UPDATE ai_usage_quotas 
    SET current_usage = current_usage + 1,
        updated_at = NOW()
    WHERE entity_type = p_entity_type 
    AND entity_id = p_entity_id 
    AND quota_type = 'requests'
    AND enabled = true;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update quota usage when logging AI usage
CREATE OR REPLACE FUNCTION trigger_update_quota_usage()
RETURNS TRIGGER AS $$
BEGIN
    -- Update quotas for user, organization, and global
    PERFORM update_ai_quota_usage('user', NEW.user_id::text, NEW.estimated_cost, NEW.total_tokens);
    PERFORM update_ai_quota_usage('organization', NEW.organization_id::text, NEW.estimated_cost, NEW.total_tokens);
    PERFORM update_ai_quota_usage('global', 'global', NEW.estimated_cost, NEW.total_tokens);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ai_usage_quota_update
    AFTER INSERT ON ai_usage_logs
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_quota_usage();

-- Views for common analytics queries
CREATE VIEW ai_usage_summary AS
SELECT 
    DATE(created_at) as date,
    provider,
    model,
    feature,
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE success = true) as successful_requests,
    COUNT(*) FILTER (WHERE success = false) as failed_requests,
    SUM(total_tokens) as total_tokens,
    SUM(estimated_cost) as total_cost,
    AVG(request_duration) as avg_response_time,
    COUNT(*) FILTER (WHERE cache_hit = true)::DECIMAL / COUNT(*) as cache_hit_rate
FROM ai_usage_logs
GROUP BY DATE(created_at), provider, model, feature;

CREATE VIEW ai_cost_by_organization AS
SELECT 
    o.name as organization_name,
    o.id as organization_id,
    DATE(l.created_at) as date,
    SUM(l.estimated_cost) as daily_cost,
    SUM(l.total_tokens) as daily_tokens,
    COUNT(*) as daily_requests
FROM ai_usage_logs l
JOIN organizations o ON l.organization_id = o.id
GROUP BY o.id, o.name, DATE(l.created_at);

CREATE VIEW ai_quota_status AS
SELECT 
    q.*,
    CASE 
        WHEN q.limit_value > 0 THEN (q.current_usage / q.limit_value * 100)
        ELSE 0 
    END as usage_percentage,
    CASE 
        WHEN q.reset_at <= NOW() THEN 'expired'
        WHEN q.current_usage >= q.limit_value THEN 'exceeded'
        WHEN q.current_usage >= q.limit_value * 0.9 THEN 'warning'
        ELSE 'normal'
    END as status
FROM ai_usage_quotas q;

-- RLS Policies
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_usage_quotas ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_budget_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_content_filter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_feature_analytics ENABLE ROW LEVEL SECURITY;

-- Users can see their own usage logs
CREATE POLICY "Users can view own AI usage logs" ON ai_usage_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Organization admins can see organization usage
CREATE POLICY "Organization admins can view org AI usage" ON ai_usage_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles up
            WHERE up.user_id = auth.uid()
            AND up.organization_id = ai_usage_logs.organization_id
            AND up.role IN ('admin', 'system_admin')
        )
    );

-- System admins can see all usage
CREATE POLICY "System admins can view all AI usage" ON ai_usage_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles up
            WHERE up.user_id = auth.uid()
            AND up.role = 'system_admin'
        )
    );

-- Similar policies for other tables
CREATE POLICY "Users can view own quotas" ON ai_usage_quotas
    FOR SELECT USING (
        (entity_type = 'user' AND entity_id = auth.uid()::text) OR
        (entity_type = 'organization' AND EXISTS (
            SELECT 1 FROM user_profiles up
            WHERE up.user_id = auth.uid()
            AND up.organization_id::text = entity_id
        )) OR
        (entity_type = 'global' AND EXISTS (
            SELECT 1 FROM user_profiles up
            WHERE up.user_id = auth.uid()
            AND up.role IN ('admin', 'system_admin')
        ))
    );

-- Schedule quota reset job (would be handled by cron or external scheduler)
-- This is a placeholder for the actual scheduling mechanism
COMMENT ON FUNCTION reset_expired_quotas() IS 'Should be called periodically (e.g., every hour) to reset expired quotas';

-- Insert default cost models
INSERT INTO ai_cost_models (provider, model, input_cost_per_1k, output_cost_per_1k, request_cost) VALUES
('openai', 'gpt-4', 0.03, 0.06, 0),
('openai', 'gpt-4-turbo', 0.01, 0.03, 0),
('openai', 'gpt-3.5-turbo', 0.0015, 0.002, 0),
('anthropic', 'claude-3-opus', 0.015, 0.075, 0),
('anthropic', 'claude-3-sonnet', 0.003, 0.015, 0),
('anthropic', 'claude-3-haiku', 0.00025, 0.00125, 0),
('google', 'gemini-pro', 0.0005, 0.0015, 0);

-- Insert default global quotas (can be customized per deployment)
INSERT INTO ai_usage_quotas (entity_type, entity_id, period, quota_type, limit_value, reset_at) VALUES
('global', 'global', 'daily', 'cost', 100.00, (CURRENT_DATE + INTERVAL '1 day')::timestamp with time zone),
('global', 'global', 'monthly', 'cost', 2000.00, (date_trunc('month', CURRENT_DATE) + INTERVAL '1 month')::timestamp with time zone);

COMMENT ON TABLE ai_usage_logs IS 'Comprehensive logging of all AI API usage with cost tracking';
COMMENT ON TABLE ai_usage_quotas IS 'Configurable quotas for AI usage by user, organization, or globally';
COMMENT ON TABLE ai_budget_alerts IS 'Alert system for budget thresholds and quota violations';
COMMENT ON TABLE ai_cost_models IS 'Current pricing models for different AI providers and models';
COMMENT ON TABLE ai_service_health IS 'Health monitoring and circuit breaker state for AI services';
COMMENT ON TABLE ai_content_filter_logs IS 'Logging of content filtering actions for compliance and analytics';
COMMENT ON TABLE ai_feature_analytics IS 'Aggregated analytics for AI feature usage and ROI calculation';
