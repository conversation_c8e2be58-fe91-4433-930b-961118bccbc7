(()=>{var e={};e.id=1713,e.ids=[1713],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},66642:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>t.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var r=a(70260),i=a(28203),l=a(25155),t=a.n(l),n=a(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c=["",{children:["test-ai-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94627)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-api\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-api\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-ai-api/page",pathname:"/test-ai-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76397:(e,s,a)=>{Promise.resolve().then(a.bind(a,94627))},57837:(e,s,a)=>{Promise.resolve().then(a.bind(a,42581))},42581:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var r=a(45512),i=a(58009),l=a(97643),t=a(87021),n=a(48859),d=a(4890),c=a(47699),o=a(77252),x=a(75339),p=a(69193),h=a(11871),u=a(46583),m=a(86235),j=a(97832);function v(){let{config:e,isConfigured:s,errors:a,testConnection:v}=(0,h.OD)(),{complete:g}=(0,h.U3)(),{validateField:f}=(0,h.fi)(),{translate:N}=(0,h.do)(),[y,b]=(0,i.useState)({}),[C,w]=(0,i.useState)(null),[T,A]=(0,i.useState)(""),[P,I]=(0,i.useState)(null),[_,E]=(0,i.useState)(!1),[S,V]=(0,i.useState)("email"),[k,q]=(0,i.useState)(""),[F,J]=(0,i.useState)(null),[R,Z]=(0,i.useState)(""),[B,D]=(0,i.useState)("ja"),[U,G]=(0,i.useState)("en"),[W,z]=(0,i.useState)(""),M=async e=>{w(e);let s=await v(e);b(a=>({...a,[e]:s})),w(null)},X=async()=>{if(T){E(!0);try{let e=await g(T,{systemPrompt:"You are a helpful IT support assistant.",temperature:.7,maxTokens:500});I(e)}catch(e){console.error("Completion error:",e),I({error:e.message})}finally{E(!1)}}},$=async()=>{if(k){E(!0);try{let e=await f(S,k);J(e)}catch(e){console.error("Validation error:",e),J({error:e.message})}finally{E(!1)}}},O=async()=>{if(R){E(!0);try{let e=await N(R,B,U);z(e)}catch(e){console.error("Translation error:",e),z("Translation failed")}finally{E(!1)}}};return(0,r.jsx)("div",{className:"container mx-auto py-10 max-w-6xl",children:(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"AI API Integration Test"}),(0,r.jsx)(l.BT,{children:"Test and verify AI API connections and functionality"})]}),(0,r.jsxs)(l.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Configuration Status"}),s?(0,r.jsxs)(x.Fc,{className:"border-green-500",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)(x.TN,{children:"AI APIs are configured and ready to use"})]}):(0,r.jsxs)(x.Fc,{variant:"destructive",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),(0,r.jsxs)(x.TN,{children:["AI APIs are not properly configured. Errors:",(0,r.jsx)("ul",{className:"mt-2 list-disc list-inside",children:a.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:["openai","anthropic"].map(s=>(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium capitalize",children:s}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e?.providers[s]?"Configured":"Not configured"})]}),(0,r.jsx)(t.$,{size:"sm",onClick:()=>M(s),disabled:!e?.providers[s]||C===s,children:C===s?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):!0===y[s]?(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-500"}):!1===y[s]?(0,r.jsx)(j.A,{className:"h-4 w-4 text-red-500"}):"Test"})]})})},s))})]}),s&&(0,r.jsxs)(p.tU,{defaultValue:"completion",className:"w-full",children:[(0,r.jsxs)(p.j7,{className:"grid w-full grid-cols-3",children:[(0,r.jsx)(p.Xi,{value:"completion",children:"Completion"}),(0,r.jsx)(p.Xi,{value:"validation",children:"Validation"}),(0,r.jsx)(p.Xi,{value:"translation",children:"Translation"})]}),(0,r.jsx)(p.av,{value:"completion",className:"space-y-4",children:(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Test AI Completion"}),(0,r.jsx)(l.BT,{children:"Test basic text generation capabilities"})]}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{children:"Prompt"}),(0,r.jsx)(n.T,{value:T,onChange:e=>A(e.target.value),placeholder:"Enter a prompt for the AI...",rows:3})]}),(0,r.jsx)(t.$,{onClick:X,disabled:!T||_,className:"w-full",children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Processing..."]}):"Generate Completion"}),P&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"Result:"}),(0,r.jsx)("div",{className:"p-4 bg-muted rounded-lg",children:P.error?(0,r.jsxs)("p",{className:"text-red-500",children:["Error: ",P.error]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{className:"whitespace-pre-wrap",children:P.text}),P.usage&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("p",{children:["Provider: ",P.provider]}),(0,r.jsxs)("p",{children:["Model: ",P.model]}),(0,r.jsxs)("p",{children:["Tokens: ",P.usage.totalTokens]})]})]})})]})]})]})}),(0,r.jsx)(p.av,{value:"validation",className:"space-y-4",children:(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Test Form Field Validation"}),(0,r.jsx)(l.BT,{children:"Test AI-powered form field validation"})]}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{children:"Field Type"}),(0,r.jsxs)(d.l6,{value:S,onValueChange:V,children:[(0,r.jsx)(d.bq,{children:(0,r.jsx)(d.yv,{})}),(0,r.jsxs)(d.gC,{children:[(0,r.jsx)(d.eb,{value:"email",children:"Email"}),(0,r.jsx)(d.eb,{value:"staff_id",children:"Staff ID"}),(0,r.jsx)(d.eb,{value:"pc_id",children:"PC ID"}),(0,r.jsx)(d.eb,{value:"name_jp",children:"Japanese Name"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{children:"Value to Validate"}),(0,r.jsx)(n.T,{value:k,onChange:e=>q(e.target.value),placeholder:"Enter value to validate...",rows:1})]})]}),(0,r.jsx)(t.$,{onClick:$,disabled:!k||_,className:"w-full",children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Validating..."]}):"Validate Field"}),F&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"Validation Result:"}),(0,r.jsx)("div",{className:"p-4 bg-muted rounded-lg space-y-2",children:F.error?(0,r.jsxs)("p",{className:"text-red-500",children:["Error: ",F.error]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"Valid:"}),(0,r.jsx)(o.E,{variant:F.isValid?"default":"destructive",children:F.isValid?"Yes":"No"})]}),F.issues?.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Issues:"}),(0,r.jsx)("ul",{className:"list-disc list-inside mt-1",children:F.issues.map((e,s)=>(0,r.jsx)("li",{className:"text-sm",children:e},s))})]}),F.suggestions?.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Suggestions:"}),(0,r.jsx)("ul",{className:"list-disc list-inside mt-1",children:F.suggestions.map((e,s)=>(0,r.jsx)("li",{className:"text-sm",children:e},s))})]})]})})]})]})]})}),(0,r.jsx)(p.av,{value:"translation",className:"space-y-4",children:(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Test Translation"}),(0,r.jsx)(l.BT,{children:"Test AI-powered translation between Japanese and English"})]}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{children:"Text to Translate"}),(0,r.jsx)(n.T,{value:R,onChange:e=>Z(e.target.value),placeholder:"Enter text to translate...",rows:3})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{children:"From"}),(0,r.jsxs)(d.l6,{value:B,onValueChange:e=>D(e),children:[(0,r.jsx)(d.bq,{children:(0,r.jsx)(d.yv,{})}),(0,r.jsxs)(d.gC,{children:[(0,r.jsx)(d.eb,{value:"ja",children:"Japanese"}),(0,r.jsx)(d.eb,{value:"en",children:"English"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{children:"To"}),(0,r.jsxs)(d.l6,{value:U,onValueChange:e=>G(e),children:[(0,r.jsx)(d.bq,{children:(0,r.jsx)(d.yv,{})}),(0,r.jsxs)(d.gC,{children:[(0,r.jsx)(d.eb,{value:"ja",children:"Japanese"}),(0,r.jsx)(d.eb,{value:"en",children:"English"})]})]})]})]}),(0,r.jsx)(t.$,{onClick:O,disabled:!R||_||B===U,className:"w-full",children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Translating..."]}):"Translate"}),W&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:"Translation:"}),(0,r.jsx)("div",{className:"p-4 bg-muted rounded-lg",children:(0,r.jsx)("p",{className:"whitespace-pre-wrap",children:W})})]})]})]})})]})]})]})})}},94627:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-ai-api\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-api\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[8096,2076],()=>a(66642));module.exports=r})();