import { WorkflowInstance, WorkflowState } from './types';

export class StateMachine {
  /**
   * Validate state transition
   */
  isValidTransition(
    currentState: string,
    nextState: string,
    workflowDef: any
  ): boolean {
    const state = workflowDef.states[currentState];
    
    if (!state || !state.transitions) {
      return false;
    }

    return state.transitions.some((t: any) => t.to === nextState);
  }

  /**
   * Get available transitions from current state
   */
  getAvailableTransitions(
    currentState: string,
    workflowDef: any
  ): string[] {
    const state = workflowDef.states[currentState];
    
    if (!state || !state.transitions) {
      return [];
    }

    return state.transitions.map((t: any) => t.to);
  }

  /**
   * Check if state is terminal
   */
  isTerminalState(stateName: string, workflowDef: any): boolean {
    const state = workflowDef.states[stateName];
    return state && state.type === 'end';
  }

  /**
   * Get state metadata
   */
  getStateMetadata(stateName: string, workflowDef: any): WorkflowState | null {
    return workflowDef.states[stateName] || null;
  }

  /**
   * Validate workflow definition
   */
  validateWorkflowDefinition(workflowDef: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for required fields
    if (!workflowDef.id) {
      errors.push('Workflow definition must have an id');
    }

    if (!workflowDef.states) {
      errors.push('Workflow definition must have states');
    }

    // Check for initial state
    if (!workflowDef.states?.initial) {
      errors.push('Workflow must have an initial state');
    }

    // Validate each state
    for (const [stateName, state] of Object.entries(workflowDef.states || {})) {
      const stateObj = state as WorkflowState;
      
      // Check for required state fields
      if (!stateObj.type) {
        errors.push(`State '${stateName}' must have a type`);
      }

      // Validate transitions
      if (stateObj.transitions) {
        for (const transition of stateObj.transitions) {
          if (!transition.to) {
            errors.push(`Transition in state '${stateName}' must have a 'to' field`);
          }
          
          // Check if target state exists
          if (transition.to && !workflowDef.states[transition.to]) {
            errors.push(`State '${stateName}' has transition to non-existent state '${transition.to}'`);
          }
        }
      }

      // Validate state-specific requirements
      switch (stateObj.type) {
        case 'approval':
          if (!stateObj.assignee && !stateObj.assigned_role) {
            errors.push(`Approval state '${stateName}' must have an assignee configuration`);
          }
          break;
        
        case 'task':
          if (!stateObj.handler && !stateObj.assignee && !stateObj.assigned_role) {
            errors.push(`Task state '${stateName}' must have a handler or assignee`);
          }
          break;
      }
    }

    // Check for at least one end state
    const hasEndState = Object.values(workflowDef.states || {}).some(
      (state: any) => state.type === 'end'
    );
    
    if (!hasEndState) {
      errors.push('Workflow must have at least one end state');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
