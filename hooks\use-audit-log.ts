import { useEffect } from 'react';
import { auditService, auditHelpers, AuditLogEntry } from '@/lib/services/enhanced-audit-service';

export function useAuditLog() {
  useEffect(() => {
    // Log page view on mount
    const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
    
    auditService.log({
      event_type: 'DATA_ACCESSED',
      severity: 'INFO',
      action: 'Page viewed',
      description: `User accessed ${currentPath}`,
      metadata: {
        url: currentPath,
        referrer: typeof document !== 'undefined' ? document.referrer : null
      }
    });
  }, []);

  return {
    logAction: (entry: AuditLogEntry) => auditService.log(entry),
    logLogin: auditHelpers.logLogin,
    logLogout: auditHelpers.logLogout,
    logRequestCreated: auditHelpers.logRequestCreated,
    logRequestUpdated: auditHelpers.logRequestUpdated,
    logError: auditHelpers.logError,
    logSecurityEvent: auditHelpers.logSecurityEvent,
    
    // Custom logging helpers
    logDataExport: (entityType: string, format: string, count: number) => 
      auditService.log({
        event_type: 'DATA_EXPORTED',
        severity: 'INFO',
        action: 'Data exported',
        description: `Exported ${count} ${entityType} records as ${format}`,
        entity_type: entityType,
        metadata: { format, count }
      }),
    
    logPermissionChange: (userId: string, permission: string, action: 'grant' | 'revoke') => 
      auditService.log({
        event_type: action === 'grant' ? 'PERMISSION_GRANTED' : 'PERMISSION_REVOKED',
        severity: 'WARNING',
        action: `Permission ${action}ed`,
        description: `${permission} permission ${action}ed for user`,
        entity_type: 'user',
        entity_id: userId,
        metadata: { permission, action }
      }),
    
    logConfigChange: (setting: string, oldValue: any, newValue: any) => 
      auditService.log({
        event_type: 'CONFIGURATION_CHANGED',
        severity: 'WARNING',
        action: 'Configuration updated',
        description: `Changed ${setting} configuration`,
        entity_type: 'system_config',
        old_value: oldValue,
        new_value: newValue,
        metadata: { setting }
      })
  };
}