// Language Provider for Japanese-First UI
'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Locale, defaultLocale, loadDictionary } from './config';

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string) => string;
  dictionary: any;
}

const LanguageContext = createContext<LanguageContextType>({
  locale: defaultLocale,
  setLocale: () => {},
  t: (key: string) => key,
  dictionary: {}
});

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocale] = useState<Locale>(defaultLocale);
  const [dictionary, setDictionary] = useState<any>({});

  useEffect(() => {
    // Load dictionary when locale changes
    loadDictionary(locale).then(setDictionary);
  }, [locale]);

  useEffect(() => {
    // Persist locale preference
    localStorage.setItem('locale', locale);
  }, [locale]);

  // Translation function
  const t = (key: string): string => {
    const keys = key.split('.');
    let value = dictionary;
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  return (
    <LanguageContext.Provider value={{ locale, setLocale, t, dictionary }}>
      {children}
    </LanguageContext.Provider>
  );
}

export const useLanguage = () => useContext(LanguageContext);