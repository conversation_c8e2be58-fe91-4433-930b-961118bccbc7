'use client'

import React from 'react'
import { ChatbotWidget } from '@/components/chatbot/chatbot-widget'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageCircle, HelpCircle, BookOpen, ThumbsUp } from 'lucide-react'

export default function ChatbotTestPage() {
  const [currentForm, setCurrentForm] = React.useState<string>('')

  const testScenarios = [
    {
      title: 'パスワードリセット申請',
      form: 'password-reset',
      description: 'パスワードリセットフォームでのアシスタント'
    },
    {
      title: 'グループメール管理',
      form: 'group-mail',
      description: 'グループメール追加・削除のサポート'
    },
    {
      title: '一般的な質問',
      form: '',
      description: '一般的なITサポートの質問'
    }
  ]

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">AI チャットボットテスト</h1>
        <p className="text-muted-foreground">
          ITヘルプデスクアシスタントのチャットボット機能をテストします。
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              リアルタイムサポート
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              ユーザーの質問に対してリアルタイムで回答を提供します。
            </p>            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <ThumbsUp className="h-4 w-4 mt-0.5 text-green-500" />
                <span>24時間対応</span>
              </li>
              <li className="flex items-start gap-2">
                <ThumbsUp className="h-4 w-4 mt-0.5 text-green-500" />
                <span>日本語・英語対応</span>
              </li>
              <li className="flex items-start gap-2">
                <ThumbsUp className="h-4 w-4 mt-0.5 text-green-500" />
                <span>コンテキスト認識</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              FAQ統合
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              よくある質問を即座に検索して回答します。
            </p>
            <ul className="space-y-2 text-sm">
              <li>• パスワードリセット方法</li>
              <li>• グループメール管理</li>
              <li>• SharePointアクセス申請</li>
              <li>• PC管理者権限</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              チュートリアル
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              ステップバイステップのガイドを提供します。
            </p>
            <ul className="space-y-2 text-sm">
              <li>• 新入社員IT設定</li>              <li>• フォーム入力ガイド</li>
              <li>• トラブルシューティング</li>
              <li>• ベストプラクティス</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>テストシナリオ</CardTitle>
          <CardDescription>
            異なるコンテキストでチャットボットをテストできます
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {testScenarios.map((scenario, index) => (
              <Button
                key={index}
                variant={currentForm === scenario.form ? 'default' : 'outline'}
                onClick={() => setCurrentForm(scenario.form)}
              >
                {scenario.title}
              </Button>
            ))}
          </div>
          {currentForm && (
            <Badge className="mt-4" variant="secondary">
              現在のコンテキスト: {currentForm || 'なし'}
            </Badge>
          )}
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>サンプル質問</CardTitle>
          <CardDescription>
            以下の質問を試してみてください
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-semibold mb-2">一般的な質問</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• パスワードをリセットしたいです</li>
                <li>• グループメールに追加してください</li>                <li>• SharePointにアクセスできません</li>
                <li>• 新しいPCの設定を教えてください</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">フォーム関連の質問</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• このフォームの入力方法を教えて</li>
                <li>• 必須項目は何ですか？</li>
                <li>• 申請の承認にはどれくらいかかりますか？</li>
                <li>• 複数のユーザーを一度に申請できますか？</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chatbot Widget */}
      <ChatbotWidget
        defaultOpen={true}
        currentForm={currentForm}
        currentPage="test-chatbot"
      />
    </div>
  )
}