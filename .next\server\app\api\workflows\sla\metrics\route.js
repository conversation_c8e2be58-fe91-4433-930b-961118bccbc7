"use strict";(()=>{var e={};e.id=5645,e.ids=[5645],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66039:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>d});var a=t(42706),o=t(28203),n=t(45994),i=t(39187),p=t(61487),u=t(92537);async function d(e){try{let r=(0,p.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let a=e.nextUrl.searchParams,o=a.get("startDate"),n=a.get("endDate"),d=a.get("departmentId");if(!o||!n)return i.NextResponse.json({error:"Start date and end date are required"},{status:400});let c=await u.i.getSLAMetrics(new Date(o),new Date(n),d||void 0);return i.NextResponse.json(c)}catch(e){return console.error("Error fetching SLA metrics:",e),i.NextResponse.json({error:"Failed to fetch SLA metrics"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/sla/metrics/route",pathname:"/api/workflows/sla/metrics",filename:"route",bundlePath:"app/api/workflows/sla/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\metrics\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,5452,4512,9064,3744,9389],()=>t(66039));module.exports=s})();