"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_i18n_dictionaries_en_json";
exports.ids = ["_ssr_lib_i18n_dictionaries_en_json"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/dictionaries/en.json":
/*!***************************************!*\
  !*** ./lib/i18n/dictionaries/en.json ***!
  \***************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"common":{"appName":"ITSync - IT Helpdesk","welcome":"Welcome","loading":"Loading...","error":"Error","success":"Success","cancel":"Cancel","confirm":"Confirm","save":"Save","delete":"Delete","edit":"Edit","search":"Search","filter":"Filter","reset":"Reset","back":"Back","next":"Next","previous":"Previous","submit":"Submit","close":"Close","yes":"Yes","no":"No"},"auth":{"login":"Login","logout":"Logout","email":"Email Address","password":"Password","forgotPassword":"Forgot Password?","rememberMe":"Remember Me","loginError":"Login failed","unauthorized":"Unauthorized"},"dashboard":{"title":"Dashboard","overview":"Overview","recentRequests":"Recent Requests","pendingApprovals":"Pending Approvals","statistics":"Statistics","quickActions":"Quick Actions"},"request":{"title":"IT Support Request","newRequest":"New Request","requestType":"Request Type","serviceCategory":"Service Category","selectService":"Select Service","description":"Description","priority":"Priority"}}');

/***/ })

};
;