import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { auditLogger } from '@/lib/services/audit';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const { taskId, toUserId, reason } = await request.json();

    if (!taskId || !toUserId || !reason) {
      return NextResponse.json(
        { error: 'Task ID, target user, and reason are required' },
        { status: 400 }
      );
    }

    // Execute delegation using the database function
    const { data, error } = await supabase.rpc('handle_approval_delegation', {
      p_task_id: taskId,
      p_from_user_id: user.id,
      p_to_user_id: toUserId,
      p_reason: reason,
    });

    if (error) {
      throw error;
    }

    // Log the action
    await auditLogger.log({
      action: 'APPROVAL_DELEGATED',
      details: {
        task_id: taskId,
        delegated_to: toUserId,
        reason,
      },
      user_id: user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'Approval delegated successfully',
    });
  } catch (error) {
    console.error('Error delegating approval:', error);
    return NextResponse.json(
      { error: 'Failed to delegate approval' },
      { status: 500 }
    );
  }
}
