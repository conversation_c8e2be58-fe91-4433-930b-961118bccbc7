-- Group mail addresses table
CREATE TABLE group_mail_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email_address TEXT NOT NULL UNIQUE,
  description TEXT,
  division_id UUID REFERENCES divisions(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Group mail members junction table
CREATE TABLE group_mail_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  group_mail_id UUID REFERENCES group_mail_addresses(id) ON DELETE CASCADE,
  staff_id UUID REFERENCES staff(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(group_mail_id, staff_id)
);

-- Mailbox addresses table
CREATE TABLE mailbox_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email_address TEXT NOT NULL UNIQUE,
  description TEXT,
  division_id UUID REFERENCES divisions(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mailbox members junction table
CREATE TABLE mailbox_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mailbox_id UUID REFERENCES mailbox_addresses(id) ON DELETE CASCADE,
  staff_id UUID REFERENCES staff(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(mailbox_id, staff_id)
);

-- SharePoint libraries table
CREATE TABLE sharepoint_libraries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name_jp TEXT NOT NULL,
  name_en TEXT,
  description TEXT,
  division_id UUID REFERENCES divisions(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SharePoint access junction table
CREATE TABLE sharepoint_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  library_id UUID REFERENCES sharepoint_libraries(id) ON DELETE CASCADE,
  staff_id UUID REFERENCES staff(id) ON DELETE CASCADE,
  access_level VARCHAR(20) NOT NULL, -- 'read', 'contribute', 'full'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(library_id, staff_id)
);
