# ITSync Testing Guide

## 🧪 Overview

This document outlines the comprehensive testing strategy for ITSync, including unit tests, integration tests, security tests, and performance tests.

## 📋 Testing Strategy

### Test Pyramid

```
    /\
   /  \     E2E Tests (Manual/Automated)
  /____\
 /      \   Integration Tests
/________\
|        |  Unit Tests
|________|
```

- **Unit Tests (70%)**: Test individual components and functions
- **Integration Tests (20%)**: Test API endpoints and database operations
- **End-to-End Tests (10%)**: Test complete user workflows

## 🔧 Test Setup

### Prerequisites

```bash
# Install dependencies
npm install

# Set up test environment
cp .env.template .env.test
# Edit .env.test with test-specific values
```

### Environment Variables for Testing

```bash
# Test Database (use separate Supabase project for testing)
NEXT_PUBLIC_SUPABASE_URL=https://test-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=test-anon-key
SUPABASE_SERVICE_ROLE_KEY=test-service-key

# Test API Keys (use test/sandbox keys)
OPENAI_API_KEY=sk-test-openai-key
ANTHROPIC_API_KEY=sk-ant-test-anthropic-key

# Test Encryption Keys
ENCRYPTION_KEY=test-encryption-key-32-characters
JWT_SECRET=test-jwt-secret-32-characters-long
```

## 🚀 Running Tests

### All Tests

```bash
# Run all tests with coverage
npm run test:all

# Run tests in CI mode
npm run test:ci
```

### Specific Test Types

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# Security tests
npm run test:security

# Performance tests
npm run test:performance
```

### Development Testing

```bash
# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage
```

## 📊 Test Coverage

### Coverage Requirements

| Component | Minimum Coverage |
|-----------|------------------|
| Security modules | 90% |
| Backup system | 85% |
| API endpoints | 80% |
| Core utilities | 75% |
| Overall project | 70% |

### Coverage Reports

- **HTML Report**: `coverage/lcov-report/index.html`
- **JSON Summary**: `coverage/coverage-summary.json`
- **LCOV Format**: `coverage/lcov.info`

## 🔒 Security Testing

### Automated Security Tests

```bash
# Run security test suite
npm run test:security

# Security audit
npm audit --audit-level=moderate
```

### Security Test Categories

1. **Authentication & Authorization**
   - JWT token validation
   - Role-based access control
   - Session management
   - Privilege escalation prevention

2. **Input Validation**
   - SQL injection prevention
   - XSS attack prevention
   - Path traversal protection
   - File upload validation

3. **Rate Limiting**
   - Brute force protection
   - API abuse prevention
   - Distributed attack handling

4. **Environment Security**
   - Configuration validation
   - Secret management
   - Production hardening

### Manual Security Testing

```bash
# Test with malicious inputs
curl -X POST http://localhost:3000/api/test \
  -H "Content-Type: application/json" \
  -d '{"input": "<script>alert(\"xss\")</script>"}'

# Test rate limiting
for i in {1..10}; do
  curl http://localhost:3000/api/auth/login
done

# Test authentication bypass
curl -H "Authorization: Bearer invalid.jwt.token" \
  http://localhost:3000/api/admin/backup
```

## ⚡ Performance Testing

### Performance Test Categories

1. **Response Time Tests**
   - API endpoint response times
   - Database query performance
   - Health check latency

2. **Load Testing**
   - Concurrent request handling
   - Memory usage under load
   - Rate limiter performance

3. **Resource Usage**
   - Memory leak detection
   - CPU usage monitoring
   - Database connection pooling

### Performance Benchmarks

| Endpoint | Target Response Time |
|----------|---------------------|
| `/api/health/simple` | < 100ms |
| `/api/health` | < 1000ms |
| `/api/metrics` | < 500ms |
| Database queries | < 200ms |

### Running Performance Tests

```bash
# Performance test suite
npm run test:performance

# With detailed output
npm run test:performance -- --verbose

# Memory profiling
node --inspect=0.0.0.0:9229 node_modules/.bin/jest __tests__/performance
```

## 🔗 Integration Testing

### API Integration Tests

```bash
# Test all API endpoints
npm run test:integration

# Test specific API
jest __tests__/integration/api/health.test.ts
```

### Database Integration Tests

- Connection testing
- Query performance
- Transaction handling
- Migration testing

### External Service Integration

- Supabase connectivity
- AI service integration
- Rate limiting validation

## 🧪 Unit Testing

### Test Structure

```
__tests__/
├── unit/
│   ├── lib/
│   │   ├── security/
│   │   │   ├── rate-limiter.test.ts
│   │   │   └── encryption.test.ts
│   │   ├── config/
│   │   │   └── env-validator.test.ts
│   │   └── backup/
│   │       └── backup-manager.test.ts
│   └── components/
│       └── ui/
│           └── button.test.tsx
├── integration/
│   └── api/
│       ├── health.test.ts
│       └── backup.test.ts
├── security/
│   └── security-tests.test.ts
└── performance/
    └── performance-tests.test.ts
```

### Writing Unit Tests

```typescript
// Example unit test
describe('Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should handle success case', () => {
    // Arrange
    const input = 'test'
    
    // Act
    const result = functionUnderTest(input)
    
    // Assert
    expect(result).toBe('expected')
  })

  it('should handle error case', () => {
    expect(() => {
      functionUnderTest(null)
    }).toThrow('Expected error message')
  })
})
```

## 🔄 Continuous Integration

### GitHub Actions Workflow

The CI pipeline includes:

1. **Environment Validation**
   - Configuration checks
   - Dependency installation
   - Environment setup

2. **Code Quality**
   - TypeScript compilation
   - ESLint checks
   - Prettier formatting

3. **Test Execution**
   - Unit tests
   - Integration tests
   - Security tests
   - Performance tests

4. **Coverage Analysis**
   - Coverage thresholds
   - Coverage reports
   - PR comments

5. **Security Scanning**
   - Dependency audit
   - Docker image scanning
   - SARIF reporting

### Required Secrets

Configure these secrets in GitHub:

```
NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY
ENCRYPTION_KEY
JWT_SECRET
OPENAI_API_KEY
ANTHROPIC_API_KEY
TEST_SUPABASE_URL
TEST_SUPABASE_ANON_KEY
TEST_SUPABASE_SERVICE_ROLE_KEY
```

## 🐛 Debugging Tests

### Common Issues

1. **Mock Issues**
   ```bash
   # Clear Jest cache
   npx jest --clearCache
   
   # Run with verbose output
   npm test -- --verbose
   ```

2. **Environment Issues**
   ```bash
   # Validate environment
   npm run validate-env
   
   # Check test environment
   NODE_ENV=test npm test
   ```

3. **Async Issues**
   ```typescript
   // Use proper async/await
   it('should handle async operation', async () => {
     const result = await asyncFunction()
     expect(result).toBeDefined()
   })
   ```

### Test Debugging Tools

```bash
# Run specific test file
npx jest __tests__/unit/lib/security/rate-limiter.test.ts

# Run with debugger
node --inspect-brk node_modules/.bin/jest --runInBand

# Watch specific test
npx jest --watch __tests__/unit/
```

## 📈 Test Metrics

### Key Metrics

- **Test Coverage**: > 70% overall
- **Test Execution Time**: < 5 minutes
- **Security Test Pass Rate**: 100%
- **Performance Test Pass Rate**: > 95%

### Monitoring

- Coverage trends over time
- Test execution performance
- Flaky test identification
- Security vulnerability detection

## 🔧 Best Practices

### Test Writing

1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **Use Descriptive Names**: Test names should explain what is being tested
3. **Test One Thing**: Each test should verify one specific behavior
4. **Mock External Dependencies**: Isolate units under test
5. **Clean Up**: Reset state between tests

### Test Organization

1. **Group Related Tests**: Use `describe` blocks effectively
2. **Shared Setup**: Use `beforeEach` and `afterEach` appropriately
3. **Test Data**: Use factories or fixtures for test data
4. **Async Testing**: Handle promises and async operations correctly

### Performance

1. **Parallel Execution**: Run tests in parallel when possible
2. **Selective Testing**: Run only affected tests during development
3. **Mock Heavy Operations**: Mock database calls and external APIs
4. **Clean Mocks**: Reset mocks between tests

## 📞 Support

For testing questions or issues:

- **Documentation**: Check this guide first
- **Team Chat**: #testing channel
- **Issues**: Create GitHub issue with `testing` label
- **Code Review**: Include test coverage in PR reviews

---

**Remember**: Good tests are the foundation of reliable software!
