// lib/services/ai-api-service.ts
// Unified service for integrating with external AI APIs (OpenAI and Anthropic)

interface AIProvider {
  name: 'openai' | 'anthropic'
  apiKey: string
  baseUrl?: string
}

interface AICompletionRequest {
  prompt: string
  systemPrompt?: string
  temperature?: number
  maxTokens?: number
  model?: string
  responseFormat?: 'text' | 'json'
}

interface AICompletionResponse {
  text: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  provider: string
  model: string
}

interface AIEmbeddingRequest {
  text: string | string[]
  model?: string
}

interface AIEmbeddingResponse {
  embeddings: number[][]
  usage?: {
    totalTokens: number
  }
}

export class AIAPIService {
  private providers: Map<string, AIProvider> = new Map()
  private defaultProvider: string = 'openai'
  
  constructor() {
    // Initialize providers from environment variables
    this.initializeProviders()
  }

  private initializeProviders() {
    // OpenAI configuration
    const openaiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || process.env.OPENAI_API_KEY
    if (openaiKey) {
      this.providers.set('openai', {
        name: 'openai',
        apiKey: openaiKey,
        baseUrl: 'https://api.openai.com/v1'
      })
    }

    // Anthropic configuration
    const anthropicKey = process.env.NEXT_PUBLIC_ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY
    if (anthropicKey) {
      this.providers.set('anthropic', {
        name: 'anthropic',
        apiKey: anthropicKey,
        baseUrl: 'https://api.anthropic.com/v1'
      })
    }
  }

  setDefaultProvider(provider: 'openai' | 'anthropic') {
    if (this.providers.has(provider)) {
      this.defaultProvider = provider
    } else {
      throw new Error(`Provider ${provider} is not configured`)
    }
  }

  async completion(
    request: AICompletionRequest,
    provider?: 'openai' | 'anthropic'
  ): Promise<AICompletionResponse> {
    const targetProvider = provider || this.defaultProvider
    const providerConfig = this.providers.get(targetProvider)
    
    if (!providerConfig) {
      throw new Error(`Provider ${targetProvider} is not configured`)
    }

    switch (targetProvider) {
      case 'openai':
        return this.openAICompletion(request, providerConfig)
      case 'anthropic':
        return this.anthropicCompletion(request, providerConfig)
      default:
        throw new Error(`Unsupported provider: ${targetProvider}`)
    }
  }

  private async openAICompletion(
    request: AICompletionRequest,
    provider: AIProvider
  ): Promise<AICompletionResponse> {
    const model = request.model || 'gpt-4-turbo-preview'
    
    const body: any = {
      model,
      messages: [
        ...(request.systemPrompt ? [{ role: 'system', content: request.systemPrompt }] : []),
        { role: 'user', content: request.prompt }
      ],
      temperature: request.temperature ?? 0.7,
      max_tokens: request.maxTokens ?? 1000
    }

    if (request.responseFormat === 'json') {
      body.response_format = { type: 'json_object' }
    }

    const response = await fetch(`${provider.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`)
    }

    const data = await response.json()
    
    return {
      text: data.choices[0].message.content,
      usage: data.usage ? {
        promptTokens: data.usage.prompt_tokens,
        completionTokens: data.usage.completion_tokens,
        totalTokens: data.usage.total_tokens
      } : undefined,
      provider: 'openai',
      model
    }
  }

  private async anthropicCompletion(
    request: AICompletionRequest,
    provider: AIProvider
  ): Promise<AICompletionResponse> {
    const model = request.model || 'claude-3-opus-20240229'
    
    const body = {
      model,
      messages: [
        { role: 'user', content: request.prompt }
      ],
      system: request.systemPrompt,
      temperature: request.temperature ?? 0.7,
      max_tokens: request.maxTokens ?? 1000
    }

    const response = await fetch(`${provider.baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'x-api-key': provider.apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    })

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`)
    }

    const data = await response.json()
    
    return {
      text: data.content[0].text,
      usage: data.usage ? {
        promptTokens: data.usage.input_tokens,
        completionTokens: data.usage.output_tokens,
        totalTokens: data.usage.input_tokens + data.usage.output_tokens
      } : undefined,
      provider: 'anthropic',
      model
    }
  }

  async embedding(
    request: AIEmbeddingRequest,
    provider: 'openai' = 'openai'
  ): Promise<AIEmbeddingResponse> {
    const providerConfig = this.providers.get(provider)
    
    if (!providerConfig) {
      throw new Error(`Provider ${provider} is not configured`)
    }

    if (provider !== 'openai') {
      throw new Error('Embeddings are currently only supported with OpenAI')
    }

    const model = request.model || 'text-embedding-3-small'
    const input = Array.isArray(request.text) ? request.text : [request.text]

    const response = await fetch(`${providerConfig.baseUrl}/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${providerConfig.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        input
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI Embeddings API error: ${response.statusText}`)
    }

    const data = await response.json()
    
    return {
      embeddings: data.data.map((item: any) => item.embedding),
      usage: data.usage ? {
        totalTokens: data.usage.total_tokens
      } : undefined
    }
  }

  // Specialized methods for IT Helpdesk use cases

  async validateFormField(
    fieldType: string,
    value: string,
    context?: Record<string, any>
  ): Promise<{ isValid: boolean; issues: string[]; suggestions: string[] }> {
    const systemPrompt = `You are validating form fields for a Japanese IT helpdesk system.
Field type: ${fieldType}
Context: ${JSON.stringify(context || {})}

Validate the input and provide:
1. Whether it's valid (true/false)
2. List of issues if invalid
3. Suggestions for correction

Return as JSON with keys: isValid, issues, suggestions`

    const response = await this.completion({
      systemPrompt,
      prompt: `Validate this value: "${value}"`,
      temperature: 0.3,
      responseFormat: 'json'
    })

    return JSON.parse(response.text)
  }

  async generateFormSuggestions(
    fieldType: string,
    partialValue: string,
    context?: Record<string, any>
  ): Promise<string[]> {
    const systemPrompt = `Generate autocomplete suggestions for a Japanese IT helpdesk form field.
Field type: ${fieldType}
Context: ${JSON.stringify(context || {})}

Provide 3-5 relevant suggestions based on the partial input.
Return as JSON array of strings.`

    const response = await this.completion({
      systemPrompt,
      prompt: `Partial value: "${partialValue}"`,
      temperature: 0.5,
      responseFormat: 'json'
    })

    return JSON.parse(response.text)
  }

  async classifyRequest(
    requestText: string,
    availableCategories: string[]
  ): Promise<{ category: string; confidence: number }> {
    const systemPrompt = `Classify IT helpdesk requests into categories.
Available categories: ${availableCategories.join(', ')}

Return JSON with: category (exact match from list), confidence (0-1)`

    const response = await this.completion({
      systemPrompt,
      prompt: requestText,
      temperature: 0.3,
      responseFormat: 'json'
    })

    return JSON.parse(response.text)
  }

  async translateText(
    text: string,
    from: 'ja' | 'en',
    to: 'ja' | 'en'
  ): Promise<string> {
    if (from === to) return text

    const response = await this.completion({
      systemPrompt: `Translate the following text from ${from} to ${to}. 
Maintain technical terminology appropriate for IT helpdesk context.
Return only the translated text.`,
      prompt: text,
      temperature: 0.3
    })

    return response.text
  }

  // Cost estimation utilities

  estimateCost(usage: { promptTokens: number; completionTokens: number }, model: string): number {
    const pricing: Record<string, { prompt: number; completion: number }> = {
      'gpt-4-turbo-preview': { prompt: 0.01, completion: 0.03 },
      'gpt-4': { prompt: 0.03, completion: 0.06 },
      'gpt-3.5-turbo': { prompt: 0.0005, completion: 0.0015 },
      'claude-3-opus-20240229': { prompt: 0.015, completion: 0.075 },
      'claude-3-sonnet-20240229': { prompt: 0.003, completion: 0.015 }
    }

    const modelPricing = pricing[model] || { prompt: 0.01, completion: 0.03 }
    
    return (
      (usage.promptTokens / 1000) * modelPricing.prompt +
      (usage.completionTokens / 1000) * modelPricing.completion
    )
  }

  isConfigured(provider?: 'openai' | 'anthropic'): boolean {
    if (provider) {
      return this.providers.has(provider)
    }
    return this.providers.size > 0
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys())
  }
}

// Export singleton instance
export const aiAPIService = new AIAPIService()
