/**
 * Redis Client Configuration and Connection Management
 * Provides connection pooling, error handling, and performance monitoring
 */

import Redis, { RedisOptions } from 'ioredis'
import { metrics } from '@/lib/monitoring/metrics'

interface CacheConfig {
  host: string
  port: number
  password?: string
  db: number
  maxRetriesPerRequest: number
  retryDelayOnFailover: number
  enableReadyCheck: boolean
  maxRetriesPerRequest: number
  lazyConnect: boolean
  keepAlive: number
  family: number
  keyPrefix: string
}

interface CacheMetrics {
  hits: number
  misses: number
  sets: number
  deletes: number
  errors: number
  connectionTime: number
}

class RedisClient {
  private client: Redis | null = null
  private isConnected: boolean = false
  private config: CacheConfig
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    connectionTime: 0
  }

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'itsync:',
      ...config
    }

    this.initializeClient()
  }

  private initializeClient(): void {
    if (!process.env.REDIS_URL && !this.config.host) {
      console.warn('Redis not configured, using memory cache fallback')
      return
    }

    const redisOptions: RedisOptions = {
      host: this.config.host,
      port: this.config.port,
      password: this.config.password,
      db: this.config.db,
      maxRetriesPerRequest: this.config.maxRetriesPerRequest,
      retryDelayOnFailover: this.config.retryDelayOnFailover,
      enableReadyCheck: this.config.enableReadyCheck,
      lazyConnect: this.config.lazyConnect,
      keepAlive: this.config.keepAlive,
      family: this.config.family,
      keyPrefix: this.config.keyPrefix,
      // Connection pool settings
      maxLoadingTimeout: 5000,
      // Retry strategy
      retryStrategy: (times: number) => {
        const delay = Math.min(times * 50, 2000)
        return delay
      },
      // Reconnect on error
      reconnectOnError: (err: Error) => {
        const targetError = 'READONLY'
        return err.message.includes(targetError)
      }
    }

    // Use Redis URL if provided, otherwise use individual config
    if (process.env.REDIS_URL) {
      this.client = new Redis(process.env.REDIS_URL, redisOptions)
    } else {
      this.client = new Redis(redisOptions)
    }

    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    if (!this.client) return

    this.client.on('connect', () => {
      console.log('Redis client connected')
      this.isConnected = true
      metrics.gauge('redis.connected', 1)
    })

    this.client.on('ready', () => {
      console.log('Redis client ready')
      metrics.gauge('redis.ready', 1)
    })

    this.client.on('error', (err: Error) => {
      console.error('Redis client error:', err)
      this.metrics.errors++
      this.isConnected = false
      metrics.increment('redis.errors')
      metrics.gauge('redis.connected', 0)
    })

    this.client.on('close', () => {
      console.log('Redis client connection closed')
      this.isConnected = false
      metrics.gauge('redis.connected', 0)
    })

    this.client.on('reconnecting', () => {
      console.log('Redis client reconnecting')
      metrics.increment('redis.reconnections')
    })
  }

  async connect(): Promise<void> {
    if (!this.client) {
      throw new Error('Redis client not initialized')
    }

    const startTime = Date.now()
    
    try {
      await this.client.connect()
      this.metrics.connectionTime = Date.now() - startTime
      metrics.timing('redis.connection_time', this.metrics.connectionTime)
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.connection_errors')
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.disconnect()
      this.isConnected = false
    }
  }

  async get<T = string>(key: string): Promise<T | null> {
    if (!this.client || !this.isConnected) {
      return null
    }

    const startTime = Date.now()

    try {
      const value = await this.client.get(key)
      const duration = Date.now() - startTime

      if (value !== null) {
        this.metrics.hits++
        metrics.increment('redis.hits')
        metrics.timing('redis.get_time', duration)
        
        try {
          return JSON.parse(value) as T
        } catch {
          return value as T
        }
      } else {
        this.metrics.misses++
        metrics.increment('redis.misses')
        return null
      }
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.get_errors')
      console.error('Redis GET error:', error)
      return null
    }
  }

  async set(
    key: string, 
    value: any, 
    ttl?: number
  ): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false
    }

    const startTime = Date.now()

    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
      
      let result: string | null
      if (ttl) {
        result = await this.client.setex(key, ttl, serializedValue)
      } else {
        result = await this.client.set(key, serializedValue)
      }

      const duration = Date.now() - startTime
      this.metrics.sets++
      metrics.increment('redis.sets')
      metrics.timing('redis.set_time', duration)

      return result === 'OK'
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.set_errors')
      console.error('Redis SET error:', error)
      return false
    }
  }

  async del(key: string | string[]): Promise<number> {
    if (!this.client || !this.isConnected) {
      return 0
    }

    const startTime = Date.now()

    try {
      const result = await this.client.del(key)
      const duration = Date.now() - startTime
      
      this.metrics.deletes++
      metrics.increment('redis.deletes')
      metrics.timing('redis.del_time', duration)

      return result
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.del_errors')
      console.error('Redis DEL error:', error)
      return 0
    }
  }

  async exists(key: string): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false
    }

    try {
      const result = await this.client.exists(key)
      return result === 1
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.exists_errors')
      console.error('Redis EXISTS error:', error)
      return false
    }
  }

  async expire(key: string, ttl: number): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false
    }

    try {
      const result = await this.client.expire(key, ttl)
      return result === 1
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.expire_errors')
      console.error('Redis EXPIRE error:', error)
      return false
    }
  }

  async ttl(key: string): Promise<number> {
    if (!this.client || !this.isConnected) {
      return -1
    }

    try {
      return await this.client.ttl(key)
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.ttl_errors')
      console.error('Redis TTL error:', error)
      return -1
    }
  }

  async flushPattern(pattern: string): Promise<number> {
    if (!this.client || !this.isConnected) {
      return 0
    }

    try {
      const keys = await this.client.keys(pattern)
      if (keys.length === 0) return 0
      
      return await this.client.del(...keys)
    } catch (error) {
      this.metrics.errors++
      metrics.increment('redis.flush_errors')
      console.error('Redis FLUSH PATTERN error:', error)
      return 0
    }
  }

  async ping(): Promise<boolean> {
    if (!this.client) {
      return false
    }

    try {
      const result = await this.client.ping()
      return result === 'PONG'
    } catch (error) {
      console.error('Redis PING error:', error)
      return false
    }
  }

  getMetrics(): CacheMetrics {
    return { ...this.metrics }
  }

  getHitRate(): number {
    const total = this.metrics.hits + this.metrics.misses
    return total > 0 ? (this.metrics.hits / total) * 100 : 0
  }

  isHealthy(): boolean {
    return this.isConnected && this.client !== null
  }

  async getInfo(): Promise<any> {
    if (!this.client || !this.isConnected) {
      return null
    }

    try {
      const info = await this.client.info()
      return this.parseRedisInfo(info)
    } catch (error) {
      console.error('Redis INFO error:', error)
      return null
    }
  }

  private parseRedisInfo(info: string): any {
    const lines = info.split('\r\n')
    const result: any = {}
    let section = ''

    for (const line of lines) {
      if (line.startsWith('#')) {
        section = line.substring(2).toLowerCase()
        result[section] = {}
      } else if (line.includes(':')) {
        const [key, value] = line.split(':')
        if (section) {
          result[section][key] = isNaN(Number(value)) ? value : Number(value)
        }
      }
    }

    return result
  }
}

// Singleton instance
let redisClient: RedisClient | null = null

export const getRedisClient = (): RedisClient => {
  if (!redisClient) {
    redisClient = new RedisClient()
  }
  return redisClient
}

export { RedisClient }
export type { CacheConfig, CacheMetrics }
