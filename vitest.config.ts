import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    // Environment setup
    environment: 'jsdom',
    setupFiles: ['./jest.setup.js'],
    
    // Test file patterns
    include: [
      '__tests__/**/*.test.ts',
      '__tests__/**/*.test.tsx',
      '__tests__/**/*.spec.ts',
      '__tests__/**/*.spec.tsx'
    ],
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      exclude: [
        'node_modules/',
        'dist/',
        '.next/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx}',
        '**/*.spec.{ts,tsx}',
        '**/mocks/**'
      ],
      thresholds: {
        statements: 70,
        branches: 60,
        functions: 70,
        lines: 70
      }
    },
    
    // Global test configuration
    globals: true,
    
    // Timeout settings - increased to fix timeout issues
    testTimeout: 60000,
    hookTimeout: 60000,
    
    // Retry failed tests
    retry: 1,
    
    // Parallel execution
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
      },
    },
    
    // Reporting
    reporters: ['default', 'html'],
    outputFile: {
      html: './test-results/html/index.html'
    }
  },
  
  // Path aliases
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@lib': resolve(__dirname, './lib'),
      '@components': resolve(__dirname, './components'),
      '@hooks': resolve(__dirname, './hooks'),
      '@contexts': resolve(__dirname, './contexts'),
      '@tests': resolve(__dirname, './__tests__')
    }
  }
});