"use client";

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  PlayCircle,
  PauseCircle,
  Loader2
} from 'lucide-react';
import type { BatchOperation, BatchItem, BatchStatusUpdate } from '@/lib/batch-processing-types';
import { batchProcessingService } from '@/lib/services/batch-processing-service';
import { cn } from '@/lib/utils';

interface BatchStatusMonitorProps {
  batchId: string;
  onComplete?: () => void;
}

export function BatchStatusMonitor({ batchId, onComplete }: BatchStatusMonitorProps) {
  const [batchOperation, setBatchOperation] = useState<BatchOperation | null>(null);
  const [batchItems, setBatchItems] = useState<BatchItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadBatchData();

    // Subscribe to real-time updates
    const unsubscribe = batchProcessingService.subscribeToBatchUpdates(
      batchId,
      handleStatusUpdate
    );

    return () => {
      unsubscribe();
    };
  }, [batchId]);

  const loadBatchData = async () => {
    setIsLoading(true);
    try {
      const operation = await batchProcessingService.getBatchStatus(batchId);
      if (operation) {
        setBatchOperation(operation);
        const items = await batchProcessingService.getBatchItems(operation.id);
        setBatchItems(items);
      }
    } finally {
      setIsLoading(false);
    }
  };
  const handleStatusUpdate = (update: BatchStatusUpdate) => {
    setBatchOperation(prev => prev ? {
      ...prev,
      status: update.status,
      progress_percentage: update.progress_percentage,
      completed_items: update.completed_items,
      total_items: update.total_items
    } : null);

    if (update.status === 'completed' && onComplete) {
      onComplete();
    }
  };

  const handleRetryFailed = async () => {
    if (batchOperation) {
      await batchProcessingService.retryFailedItems(batchOperation.id);
      loadBatchData();
    }
  };

  const handleCancel = async () => {
    if (batchOperation && batchOperation.status === 'pending') {
      await batchProcessingService.cancelBatch(batchOperation.batch_id);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-5 h-5 text-green-600" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'processing':
        return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-gray-600" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      completed: "default",
      failed: "destructive",
      processing: "secondary",
      pending: "outline"
    };

    const labels: Record<string, string> = {
      completed: "完了",
      failed: "失敗",
      processing: "処理中",
      pending: "待機中",
      partially_completed: "部分完了"
    };

    return (
      <Badge variant={variants[status] || "outline"}>
        {labels[status] || status}
      </Badge>
    );
  };
  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (!batchOperation) {
    return (
      <Card>
        <CardContent className="text-center p-8">
          <p className="text-muted-foreground">バッチ操作が見つかりません</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>バッチ処理状況</CardTitle>
            <CardDescription>
              バッチID: {batchOperation.batch_id}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(batchOperation.status)}
            {batchOperation.status === 'pending' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
              >
                <XCircle className="w-4 h-4 mr-1" />
                キャンセル
              </Button>
            )}
            {batchOperation.failed_items > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetryFailed}
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                失敗項目を再試行
              </Button>
            )}
          </div>
        </div>
      </CardHeader>      <CardContent className="space-y-6">
        {/* Progress Overview */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>進捗状況</span>
            <span>{batchOperation.progress_percentage.toFixed(1)}%</span>
          </div>
          <Progress value={batchOperation.progress_percentage} />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>完了: {batchOperation.completed_items}</span>
            <span>失敗: {batchOperation.failed_items}</span>
            <span>合計: {batchOperation.total_items}</span>
          </div>
        </div>

        <Separator />

        {/* Batch Items */}
        <div className="space-y-2">
          <h4 className="font-medium">処理項目</h4>
          <ScrollArea className="h-[300px]">
            <div className="space-y-2">
              {batchItems.map((item, index) => (
                <div
                  key={item.id}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border",
                    item.status === 'completed' && "bg-green-50 border-green-200",
                    item.status === 'failed' && "bg-red-50 border-red-200",
                    item.status === 'processing' && "bg-blue-50 border-blue-200",
                    item.status === 'pending' && "bg-gray-50 border-gray-200"
                  )}
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(item.status)}
                    <div>
                      <p className="font-medium">項目 {index + 1}</p>
                      {item.error_message && (
                        <p className="text-sm text-red-600">{item.error_message}</p>
                      )}
                    </div>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {item.processed_at ? 
                      new Date(item.processed_at).toLocaleString('ja-JP') : 
                      '未処理'
                    }
                  </span>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
}