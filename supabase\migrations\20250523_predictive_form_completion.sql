-- Predictive Form Completion Schema
-- ==================================

-- User behavior patterns table
CREATE TABLE user_form_patterns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  form_type VARCHAR(100) NOT NULL,
  field_name VARCHAR(100) NOT NULL,
  field_value TEXT NOT NULL,
  usage_count INTEGER DEFAULT 1,
  last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  confidence_score DECIMAL(3,2) DEFAULT 0.5,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pattern sequences table (for multi-field predictions)
CREATE TABLE pattern_sequences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  form_type VARCHAR(100) NOT NULL,
  sequence_hash VARCHAR(64) NOT NULL, -- Hash of field combinations
  field_sequence JSONB NOT NULL, -- Array of {field_name, field_value} pairs
  occurrence_count INTEGER DEFAULT 1,
  confidence_score DECIMAL(3,2) DEFAULT 0.5,
  last_occurred TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Department-wide patterns
CREATE TABLE department_patterns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  department_id UUID NOT NULL,
  form_type VARCHAR(100) NOT NULL,
  field_name VARCHAR(100) NOT NULL,
  common_values JSONB NOT NULL DEFAULT '[]', -- Array of {value, count, percentage}
  total_submissions INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences for predictive features
CREATE TABLE prediction_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  enable_predictions BOOLEAN DEFAULT TRUE,
  enable_department_patterns BOOLEAN DEFAULT TRUE,
  enable_personal_patterns BOOLEAN DEFAULT TRUE,
  min_confidence_threshold DECIMAL(3,2) DEFAULT 0.7,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Prediction feedback table
CREATE TABLE prediction_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  pattern_id UUID, -- Can reference user_form_patterns or pattern_sequences
  prediction_type VARCHAR(50) NOT NULL, -- 'single_field' or 'sequence'
  was_accepted BOOLEAN NOT NULL,
  alternative_value TEXT,
  feedback_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_user_form_patterns_lookup ON user_form_patterns(user_id, form_type, field_name);
CREATE INDEX idx_user_form_patterns_usage ON user_form_patterns(usage_count DESC, confidence_score DESC);
CREATE INDEX idx_pattern_sequences_lookup ON pattern_sequences(user_id, form_type, sequence_hash);
CREATE INDEX idx_department_patterns_lookup ON department_patterns(department_id, form_type, field_name);
CREATE INDEX idx_prediction_feedback_user ON prediction_feedback(user_id, created_at DESC);

-- Enable RLS
ALTER TABLE user_form_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE pattern_sequences ENABLE ROW LEVEL SECURITY;
ALTER TABLE department_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE prediction_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE prediction_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user patterns
CREATE POLICY "Users can view own patterns"
  ON user_form_patterns FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own patterns"
  ON user_form_patterns FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own patterns"
  ON user_form_patterns FOR UPDATE
  USING (auth.uid() = user_id);

-- RLS Policies for pattern sequences
CREATE POLICY "Users can view own sequences"
  ON pattern_sequences FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own sequences"
  ON pattern_sequences FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for department patterns (view only for department members)
CREATE POLICY "Department members can view patterns"
  ON department_patterns FOR SELECT
  USING (
    department_id IN (
      SELECT department_id FROM staff WHERE auth_id = auth.uid()
    )
  );

-- RLS Policies for preferences
CREATE POLICY "Users can manage own preferences"
  ON prediction_preferences FOR ALL
  USING (auth.uid() = user_id);

-- RLS Policies for feedback
CREATE POLICY "Users can create own feedback"
  ON prediction_feedback FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Function to update pattern confidence based on feedback
CREATE OR REPLACE FUNCTION update_pattern_confidence()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.prediction_type = 'single_field' AND NEW.pattern_id IS NOT NULL THEN
    UPDATE user_form_patterns
    SET 
      confidence_score = CASE
        WHEN NEW.was_accepted THEN LEAST(confidence_score + 0.05, 1.0)
        ELSE GREATEST(confidence_score - 0.1, 0.0)
      END,
      updated_at = NOW()
    WHERE id = NEW.pattern_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_pattern_confidence
  AFTER INSERT ON prediction_feedback
  FOR EACH ROW
  EXECUTE FUNCTION update_pattern_confidence();

-- Function to calculate pattern hash
CREATE OR REPLACE FUNCTION calculate_sequence_hash(field_sequence JSONB)
RETURNS VARCHAR(64) AS $$
BEGIN
  RETURN encode(digest(field_sequence::text, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Updated_at triggers
CREATE TRIGGER update_user_form_patterns_updated_at
  BEFORE UPDATE ON user_form_patterns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_department_patterns_updated_at
  BEFORE UPDATE ON department_patterns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prediction_preferences_updated_at
  BEFORE UPDATE ON prediction_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
