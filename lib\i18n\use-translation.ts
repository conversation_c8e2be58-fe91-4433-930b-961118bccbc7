/**
 * Simple translation hook for internationalization
 */

type Translations = {
  [key: string]: {
    [key: string]: string;
  };
};

// Default translations (English and Japanese)
const translations: Translations = {
  en: {
    // Auth
    'auth.login': 'Login',
    'auth.logout': 'Logout',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.mfa.title': 'Two-Factor Authentication',
    'auth.mfa.instruction': 'Enter the verification code from your authenticator app',
    'auth.mfa.code': 'Verification Code',
    'auth.mfa.verify': 'Verify',
    'auth.mfa.resend': 'Resend Code',
    
    // Common
    'common.submit': 'Submit',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.search': 'Search',
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    
    // Errors
    'error.required': 'This field is required',
    'error.invalid_email': 'Invalid email address',
    'error.invalid_password': 'Password must be at least 8 characters',
    'error.invalid_code': 'Invalid verification code',
  },
  ja: {
    // Auth
    'auth.login': 'ログイン',
    'auth.logout': 'ログアウト',
    'auth.email': 'メールアドレス',
    'auth.password': 'パスワード',
    'auth.mfa.title': '二要素認証',
    'auth.mfa.instruction': '認証アプリから確認コードを入力してください',
    'auth.mfa.code': '確認コード',
    'auth.mfa.verify': '確認',
    'auth.mfa.resend': 'コードを再送信',
    
    // Common
    'common.submit': '送信',
    'common.cancel': 'キャンセル',
    'common.save': '保存',
    'common.delete': '削除',
    'common.edit': '編集',
    'common.search': '検索',
    'common.loading': '読み込み中...',
    'common.error': 'エラーが発生しました',
    
    // Errors
    'error.required': 'この項目は必須です',
    'error.invalid_email': '無効なメールアドレスです',
    'error.invalid_password': 'パスワードは8文字以上である必要があります',
    'error.invalid_code': '無効な確認コードです',
  },
};

/**
 * Hook to use translations in components
 * @param locale Current locale (defaults to 'ja')
 * @returns Translation functions
 */
export function useTranslation(locale: string = 'ja') {
  // Get translations for the current locale, fallback to English
  const currentTranslations = translations[locale] || translations.en;
  
  /**
   * Translate a key
   * @param key Translation key
   * @param params Optional parameters for interpolation
   * @returns Translated string
   */
  const t = (key: string, params?: Record<string, string>): string => {
    let translation = currentTranslations[key] || translations.en[key] || key;
    
    // Simple parameter interpolation
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        translation = translation.replace(`{{${paramKey}}}`, paramValue);
      });
    }
    
    return translation;
  };
  
  return {
    t,
    locale,
  };
}

export default useTranslation;
