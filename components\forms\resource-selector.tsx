"use client";

import React, { useState, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { createClient } from '@/lib/supabase';

interface ResourceSelectorProps {
  serviceCategory: string;
  selectedResources: string[];
  onChange: (resources: string[]) => void;
}

export function ResourceSelector({ 
  serviceCategory, 
  selectedResources, 
  onChange 
}: ResourceSelectorProps) {
  const [resources, setResources] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    if (serviceCategory) {
      loadResources();
    }
  }, [serviceCategory]);

  const loadResources = async () => {
    setIsLoading(true);
    try {
      // Determine which table to query based on service category
      const { data: category } = await supabase
        .from('service_categories')
        .select('code')
        .eq('id', serviceCategory)
        .single();

      if (!category) return;

      let resourceData: any[] = [];

      switch (category.code) {
        case 'GM':
          const { data: groupMail } = await supabase
            .from('group_mail_addresses')
            .select('id, email_address as name, description');
          resourceData = groupMail || [];
          break;        case 'MB':
          const { data: mailbox } = await supabase
            .from('mailbox_addresses')
            .select('id, email_address as name, description');
          resourceData = mailbox || [];
          break;
        case 'SPO':
          const { data: sharepoint } = await supabase
            .from('sharepoint_libraries')
            .select('id, name_jp as name, name_en as description');
          resourceData = sharepoint || [];
          break;
        default:
          resourceData = [];
      }

      setResources(resourceData);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredResources = resources.filter(resource =>
    resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (resource.description && resource.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const toggleResource = (resourceId: string) => {
    if (selectedResources.includes(resourceId)) {
      onChange(selectedResources.filter(id => id !== resourceId));
    } else {
      onChange([...selectedResources, resourceId]);
    }
  };

  if (!serviceCategory) {
    return (
      <div className="text-sm text-muted-foreground p-4 text-center">
        サービスカテゴリを選択してください
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="リソースを検索..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-8"
        />
      </div>
      <ScrollArea className="h-[200px] border rounded-md p-2">
        {isLoading ? (
          <div className="text-center p-4 text-sm text-muted-foreground">
            読み込み中...
          </div>
        ) : filteredResources.length === 0 ? (
          <div className="text-center p-4 text-sm text-muted-foreground">
            リソースが見つかりません
          </div>
        ) : (
          <div className="space-y-2">
            {filteredResources.map((resource) => (
              <div key={resource.id} className="flex items-center space-x-2">
                <Checkbox
                  id={resource.id}
                  checked={selectedResources.includes(resource.id)}
                  onCheckedChange={() => toggleResource(resource.id)}
                />
                <Label
                  htmlFor={resource.id}
                  className="flex-1 cursor-pointer text-sm"
                >
                  <span className="font-medium">{resource.name}</span>
                  {resource.description && (
                    <span className="text-muted-foreground ml-2">
                      ({resource.description})
                    </span>
                  )}
                </Label>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>

      <div className="text-xs text-muted-foreground">
        {selectedResources.length} 個のリソースが選択されています
      </div>
    </div>
  );
}