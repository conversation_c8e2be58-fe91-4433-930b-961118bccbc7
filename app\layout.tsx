import type { Metadata } from 'next'
import { Inter, Noto_Sans_JP } from 'next/font/google'
import { AuthProvider } from '@/lib/auth-context'
import { AIProvider } from '@/components/providers/ai-provider'
import { I18nProvider } from '@/lib/i18n/context'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })
const notoSansJP = Noto_Sans_JP({ 
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-noto-sans-jp'
})

export const metadata: Metadata = {
  title: 'ITSync - Enterprise IT Helpdesk',
  description: 'AI-powered IT Helpdesk & Support Platform',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="ja" suppressHydrationWarning>
      <body className={`${inter.className} ${notoSansJP.variable} font-sans`}>
        <AuthProvider>
          <AIProvider>
            <I18nProvider>
              {children}
            </I18nProvider>
          </AIProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
