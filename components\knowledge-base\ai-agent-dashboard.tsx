'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Bot, 
  Brain, 
  FileSearch, 
  Globe, 
  Activity,
  BookOpen,
  Users,
  Zap,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { aiAgentManager } from '@/lib/agents/agent-manager'

interface AgentStatus {
  agentId: string
  category: string
  name: string
  status: 'active' | 'idle' | 'error'
  lastActivity: Date
  articlesCreated: number
  articlesUpdated: number
  currentTask?: string
}

interface AgentMetrics {
  articles_created: number
  articles_updated: number
  educational_content_created: number
  web_pages_scraped: number
  active_time_percentage: number
}

export function AIAgentDashboard() {
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([])
  const [agentMetrics, setAgentMetrics] = useState<Record<string, AgentMetrics>>({})
  const [isSystemRunning, setIsSystemRunning] = useState(false)
  const [loading, setLoading] = useState(true)
  const [selectedTimeRange, setSelectedTimeRange] = useState<'day' | 'week' | 'month'>('day')

  useEffect(() => {
    fetchAgentData()
    const interval = setInterval(fetchAgentData, 30000) // Update every 30 seconds
    return () => clearInterval(interval)
  }, [selectedTimeRange])

  const fetchAgentData = async () => {
    try {
      // Get agent statuses
      const { data: agents } = await supabase
        .from('ai_agents')
        .select('*')
        .order('category')

      if (agents) {
        setAgentStatuses(agents.map(agent => ({
          agentId: agent.agent_id,
          category: agent.category,
          name: agent.name,
          status: agent.status as any,
          lastActivity: new Date(agent.last_active_at || agent.created_at),
          articlesCreated: 0,
          articlesUpdated: 0
        })))

        setIsSystemRunning(agents.some(a => a.status === 'active'))
      }

      // Get performance metrics
      const { data: metrics } = await supabase
        .rpc('get_agent_performance_metrics', {
          p_time_range: selectedTimeRange === 'day' ? '1 day' : 
                       selectedTimeRange === 'week' ? '7 days' : '30 days'
        })

      if (metrics) {
        const metricsMap: Record<string, AgentMetrics> = {}
        metrics.forEach((m: any) => {
          metricsMap[m.agent_id] = {
            articles_created: m.articles_created || 0,
            articles_updated: m.articles_updated || 0,
            educational_content_created: m.educational_content_created || 0,
            web_pages_scraped: m.web_pages_scraped || 0,
            active_time_percentage: m.active_time_percentage || 0
          }
        })
        setAgentMetrics(metricsMap)
      }
    } catch (error) {
      console.error('Error fetching agent data:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleAgentSystem = async () => {
    try {
      if (isSystemRunning) {
        await aiAgentManager.stopAllAgents()
        setIsSystemRunning(false)
      } else {
        await aiAgentManager.startAllAgents()
        setIsSystemRunning(true)
      }
      fetchAgentData()
    } catch (error) {
      console.error('Error toggling agent system:', error)
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'group_mail': return '📧'
      case 'sharepoint': return '📁'
      case 'password_reset': return '🔐'
      case 'pc_admin': return '💻'
      case 'mailbox': return '📮'
      case 'general': return '🧠'
      default: return '🤖'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'idle': return 'secondary'
      case 'error': return 'destructive'
      default: return 'default'
    }
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading AI Agent System...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Brain className="h-8 w-8" />
            AI Agent Knowledge System
          </h1>
          <p className="text-muted-foreground mt-2">
            Specialized AI agents continuously learn and create educational content for each IT service
          </p>
        </div>
        <Button
          size="lg"
          variant={isSystemRunning ? "destructive" : "default"}
          onClick={toggleAgentSystem}
        >
          {isSystemRunning ? (
            <>
              <Zap className="h-4 w-4 mr-2" />
              Stop All Agents
            </>
          ) : (
            <>
              <Zap className="h-4 w-4 mr-2" />
              Start All Agents
            </>
          )}
        </Button>
      </div>

      {/* System Status Alert */}
      <Alert variant={isSystemRunning ? "default" : "warning"}>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>System Status</AlertTitle>
        <AlertDescription>
          {isSystemRunning ? (
            <>AI Agent System is <strong>ACTIVE</strong>. Agents are continuously scraping web content and creating educational materials.</>
          ) : (
            <>AI Agent System is <strong>INACTIVE</strong>. Start the system to enable automatic content generation.</>
          )}
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="agents">Agent Status</TabsTrigger>
          <TabsTrigger value="content">Generated Content</TabsTrigger>
          <TabsTrigger value="learning">Learning Paths</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Time Range Selector */}
          <div className="flex justify-end gap-2">
            <Button
              variant={selectedTimeRange === 'day' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTimeRange('day')}
            >
              24 Hours
            </Button>
            <Button
              variant={selectedTimeRange === 'week' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTimeRange('week')}
            >
              7 Days
            </Button>
            <Button
              variant={selectedTimeRange === 'month' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTimeRange('month')}
            >
              30 Days
            </Button>
          </div>

          {/* Overall Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {agentStatuses.filter(a => a.status === 'active').length}/{agentStatuses.length}
                </div>
                <Progress 
                  value={(agentStatuses.filter(a => a.status === 'active').length / agentStatuses.length) * 100}
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Articles Created</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.values(agentMetrics).reduce((sum, m) => sum + m.articles_created, 0)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  In the last {selectedTimeRange}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Educational Content</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.values(agentMetrics).reduce((sum, m) => sum + m.educational_content_created, 0)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Interactive guides created
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Web Pages Analyzed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.values(agentMetrics).reduce((sum, m) => sum + m.web_pages_scraped, 0)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  For content generation
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Agent Performance Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {agentStatuses.map(agent => {
              const metrics = agentMetrics[agent.agentId] || {}
              return (
                <Card key={agent.agentId}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl">{getCategoryIcon(agent.category)}</span>
                        <div>
                          <CardTitle className="text-lg">{agent.name}</CardTitle>
                          <CardDescription>{agent.category}</CardDescription>
                        </div>
                      </div>
                      <Badge variant={getStatusColor(agent.status) as any}>
                        {agent.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Articles Created:</span>
                        <span className="font-medium">{metrics.articles_created || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Content Generated:</span>
                        <span className="font-medium">{metrics.educational_content_created || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Web Pages Scraped:</span>
                        <span className="font-medium">{metrics.web_pages_scraped || 0}</span>
                      </div>
                      <div className="mt-2">
                        <div className="flex justify-between mb-1">
                          <span>Active Time:</span>
                          <span className="font-medium">{metrics.active_time_percentage || 0}%</span>
                        </div>
                        <Progress value={metrics.active_time_percentage || 0} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          {/* Detailed agent status and controls */}
          <div className="space-y-4">
            {agentStatuses.map(agent => (
              <Card key={agent.agentId}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-2xl">
                        {getCategoryIcon(agent.category)}
                      </div>
                      <div>
                        <CardTitle>{agent.name}</CardTitle>
                        <CardDescription>
                          Agent ID: {agent.agentId} | Category: {agent.category}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusColor(agent.status) as any}>
                        {agent.status}
                      </Badge>
                      {agent.status === 'active' && (
                        <Activity className="h-4 w-4 text-green-500 animate-pulse" />
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm">
                      <span className="text-muted-foreground">Last Active:</span>{' '}
                      {agent.lastActivity.toLocaleString()}
                    </div>
                    {agent.currentTask && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Current Task:</span>{' '}
                        {agent.currentTask}
                      </div>
                    )}
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        View Logs
                      </Button>
                      <Button size="sm" variant="outline">
                        Trigger Research
                      </Button>
                      <Button size="sm" variant="outline">
                        Configure
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Alert>
            <BookOpen className="h-4 w-4" />
            <AlertTitle>Educational Content Library</AlertTitle>
            <AlertDescription>
              AI agents have generated interactive guides, tutorials, and troubleshooting wizards
              tailored for non-technical staff. Content is available in both English and Japanese.
            </AlertDescription>
          </Alert>

          {/* Content statistics would go here */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Educational Content</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                View and manage AI-generated educational content across all service categories.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="learning" className="space-y-4">
          <Alert>
            <Users className="h-4 w-4" />
            <AlertTitle>Interactive Learning Paths</AlertTitle>
            <AlertDescription>
              AI agents create personalized learning journeys for staff members based on their
              role and knowledge level. Track progress and completion rates.
            </AlertDescription>
          </Alert>

          {/* Learning paths would go here */}
          <Card>
            <CardHeader>
              <CardTitle>Learning Path Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Monitor user engagement with AI-generated learning materials.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
