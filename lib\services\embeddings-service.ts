import { supabase } from '@/lib/supabase'
import { createHash } from 'crypto'

// OpenAI configuration for embeddings
const OPENAI_API_KEY = process.env.OPENAI_API_KEY
const EMBEDDING_MODEL = 'text-embedding-ada-002'
const EMBEDDING_DIMENSION = 1536

export interface EmbeddingResult {
  embedding: number[]
  model: string
  usage: {
    prompt_tokens: number
    total_tokens: number
  }
}

export interface ArticleEmbedding {
  article_id: string
  language: 'en' | 'jp'
  embedding: number[]
  content_hash: string
  model_version: string
}

export class EmbeddingsService {
  private static instance: EmbeddingsService
  
  private constructor() {}
  
  static getInstance(): EmbeddingsService {
    if (!EmbeddingsService.instance) {
      EmbeddingsService.instance = new EmbeddingsService()
    }
    return EmbeddingsService.instance
  }

  /**
   * Generate embeddings for text using OpenAI
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    if (!OPENAI_API_KEY) {
      throw new Error('OpenAI API key is not configured')
    }

    // Clean and prepare text
    const cleanedText = this.preprocessText(text)
    
    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: EMBEDDING_MODEL,
          input: cleanedText
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(`OpenAI API error: ${error.error?.message || 'Unknown error'}`)
      }

      const data = await response.json()
      
      return {
        embedding: data.data[0].embedding,
        model: data.model,
        usage: data.usage
      }
    } catch (error) {
      console.error('Error generating embedding:', error)
      throw error
    }
  }

  /**
   * Generate embeddings for an article and store in database
   */
  async generateArticleEmbeddings(
    articleId: string,
    contentEn: string,
    contentJp: string
  ): Promise<void> {
    try {
      // Generate embeddings for both languages
      const [embeddingEn, embeddingJp] = await Promise.all([
        this.generateEmbedding(contentEn),
        this.generateEmbedding(contentJp)
      ])

      // Calculate content hashes
      const hashEn = this.calculateContentHash(contentEn)
      const hashJp = this.calculateContentHash(contentJp)

      // Store embeddings in database
      await Promise.all([
        this.storeEmbedding({
          article_id: articleId,
          language: 'en',
          embedding: embeddingEn.embedding,
          content_hash: hashEn,
          model_version: embeddingEn.model
        }),
        this.storeEmbedding({
          article_id: articleId,
          language: 'jp',
          embedding: embeddingJp.embedding,
          content_hash: hashJp,
          model_version: embeddingJp.model
        })
      ])
    } catch (error) {
      console.error('Error generating article embeddings:', error)
      throw error
    }
  }

  /**
   * Store embedding in database
   */
  private async storeEmbedding(data: ArticleEmbedding): Promise<void> {
    const { error } = await supabase
      .from('kb_embeddings')
      .upsert({
        article_id: data.article_id,
        language: data.language,
        embedding: `[${data.embedding.join(',')}]`, // pgvector format
        content_hash: data.content_hash,
        model_version: data.model_version,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'article_id,language'
      })

    if (error) {
      console.error('Error storing embedding:', error)
      throw new Error(`Failed to store embedding: ${error.message}`)
    }
  }

  /**
   * Search for similar articles using semantic search
   */
  async semanticSearch(
    query: string,
    language: 'en' | 'jp',
    options: {
      matchThreshold?: number
      matchCount?: number
    } = {}
  ) {
    const { matchThreshold = 0.7, matchCount = 10 } = options

    try {
      // Generate embedding for query
      const queryEmbedding = await this.generateEmbedding(query)
      
      // Call the semantic search function
      const { data, error } = await supabase
        .rpc('kb_semantic_search', {
          query_embedding: `[${queryEmbedding.embedding.join(',')}]`,
          search_language: language,
          match_threshold: matchThreshold,
          match_count: matchCount
        })

      if (error) {
        console.error('Semantic search error:', error)
        throw new Error(`Search failed: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error performing semantic search:', error)
      throw error
    }
  }

  /**
   * Update embeddings for articles that have changed
   */
  async updateStaleEmbeddings(): Promise<void> {
    try {
      // Get articles with outdated embeddings
      const { data: articles, error } = await supabase
        .from('kb_articles')
        .select(`
          id,
          content_en,
          content_jp,
          kb_embeddings!inner(
            article_id,
            language,
            content_hash
          )
        `)
        .eq('status', 'published')

      if (error) {
        throw new Error(`Failed to fetch articles: ${error.message}`)
      }

      if (!articles || articles.length === 0) {
        console.log('No articles to update')
        return
      }

      // Check each article for stale embeddings
      const updates = []
      for (const article of articles) {
        const currentHashEn = this.calculateContentHash(article.content_en)
        const currentHashJp = this.calculateContentHash(article.content_jp)
        
        const embeddingEn = article.kb_embeddings.find((e: any) => e.language === 'en')
        const embeddingJp = article.kb_embeddings.find((e: any) => e.language === 'jp')
        
        if (!embeddingEn || embeddingEn.content_hash !== currentHashEn) {
          updates.push(this.generateAndStoreEmbedding(article.id, article.content_en, 'en'))
        }
        
        if (!embeddingJp || embeddingJp.content_hash !== currentHashJp) {
          updates.push(this.generateAndStoreEmbedding(article.id, article.content_jp, 'jp'))
        }
      }

      if (updates.length > 0) {
        await Promise.all(updates)
        console.log(`Updated ${updates.length} embeddings`)
      } else {
        console.log('All embeddings are up to date')
      }
    } catch (error) {
      console.error('Error updating stale embeddings:', error)
      throw error
    }
  }

  /**
   * Generate and store a single embedding
   */
  private async generateAndStoreEmbedding(
    articleId: string,
    content: string,
    language: 'en' | 'jp'
  ): Promise<void> {
    const embedding = await this.generateEmbedding(content)
    const contentHash = this.calculateContentHash(content)
    
    await this.storeEmbedding({
      article_id: articleId,
      language,
      embedding: embedding.embedding,
      content_hash: contentHash,
      model_version: embedding.model
    })
  }

  /**
   * Preprocess text for embedding
   */
  private preprocessText(text: string): string {
    // Remove excessive whitespace
    let processed = text.replace(/\s+/g, ' ').trim()
    
    // Remove special characters that might affect embedding quality
    processed = processed.replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
    
    // Limit length to avoid token limits (8191 tokens for ada-002)
    // Rough estimate: 1 token ≈ 4 characters
    const maxChars = 30000
    if (processed.length > maxChars) {
      processed = processed.substring(0, maxChars) + '...'
    }
    
    return processed
  }

  /**
   * Calculate content hash for change detection
   */
  private calculateContentHash(content: string): string {
    return createHash('sha256').update(content).digest('hex')
  }

  /**
   * Get similar articles based on an existing article
   */
  async findRelatedArticles(
    articleId: string,
    language: 'en' | 'jp',
    limit: number = 5
  ) {
    try {
      // Get the article's embedding
      const { data: embedding, error: embError } = await supabase
        .from('kb_embeddings')
        .select('embedding')
        .eq('article_id', articleId)
        .eq('language', language)
        .single()

      if (embError || !embedding) {
        throw new Error('Article embedding not found')
      }

      // Find similar articles
      const { data, error } = await supabase
        .rpc('kb_semantic_search', {
          query_embedding: embedding.embedding,
          search_language: language,
          match_threshold: 0.5,
          match_count: limit + 1 // +1 to exclude self
        })

      if (error) {
        throw new Error(`Related articles search failed: ${error.message}`)
      }

      // Filter out the original article
      const related = (data || []).filter((item: any) => item.article_id !== articleId)
      
      // Store relationships
      if (related.length > 0) {
        const relationships = related.map((item: any) => ({
          article_id: articleId,
          related_article_id: item.article_id,
          relevance_score: item.similarity,
          relation_type: 'similar'
        }))

        await supabase
          .from('kb_related_articles')
          .upsert(relationships, {
            onConflict: 'article_id,related_article_id'
          })
      }

      return related.slice(0, limit)
    } catch (error) {
      console.error('Error finding related articles:', error)
      throw error
    }
  }
}

// Export singleton instance
export const embeddingsService = EmbeddingsService.getInstance()
