import { BaseAIAgent, AgentSpecialization, EducationalContent, EducationalStep } from './base-agent'

/**
 * Specialized AI Agent for Group Mail Management
 */
export class GroupMailAgent extends BaseAIAgent {
  constructor() {
    const specialization: AgentSpecialization = {
      serviceCategory: 'group_mail',
      expertise: [
        'email distribution lists',
        'group mail management',
        'Microsoft Exchange',
        'email permissions',
        'distribution group best practices'
      ],
      languages: ['en', 'jp'],
      targetAudience: 'non-technical staff',
      capabilities: {
        webScraping: true,
        contentTranslation: true,
        interactiveGuides: true,
        videoTutorials: false,
        stepByStepWizards: true,
        troubleshooting: true,
        realTimeAssistance: true
      }
    }
    
    super('group-mail-agent', specialization)
  }

  protected buildEducationalPrompt(topic: string, content: string): string {
    return `
You are an expert in email and group mail management, specializing in making complex concepts simple for non-technical users.

Topic: ${topic}

Reference Content:
${content}

Create an educational guide that:
1. Explains the concept in simple, non-technical terms
2. Provides step-by-step instructions with clear actions
3. Includes common mistakes to avoid
4. Offers troubleshooting tips for typical issues
5. Uses analogies that office workers can relate to

Format the response as JSON:
{
  "type": "guide" | "tutorial" | "wizard",
  "title": "English title",
  "titleJp": "Japanese title",
  "content": "Main content in simple English",
  "contentJp": "Main content in simple Japanese",
  "difficulty": "beginner",
  "estimatedTime": 10,
  "interactive": true,
  "steps": [
    {
      "order": 1,
      "title": "Step title",
      "titleJp": "Japanese step title",
      "description": "What to do",
      "descriptionJp": "Japanese description",
      "userAction": "Click on...",
      "troubleshooting": ["If you see X, try Y"]
    }
  ],
  "relatedTopics": ["topic1", "topic2"]
}

Focus on making it accessible for someone who has never managed email groups before.
`
  }

  protected parseEducationalContent(aiResponse: string, topic: string): EducationalContent {
    try {
      const parsed = JSON.parse(aiResponse)
      return {
        type: parsed.type || 'guide',
        title: parsed.title || topic,
        titleJp: parsed.titleJp || topic,
        content: parsed.content || '',
        contentJp: parsed.contentJp || '',
        difficulty: 'beginner',
        estimatedTime: parsed.estimatedTime || 15,
        interactive: true,
        steps: parsed.steps || [],
        relatedTopics: parsed.relatedTopics || []
      }
    } catch (error) {
      // Fallback content
      return {
        type: 'guide',
        title: `Guide: ${topic}`,
        titleJp: `ガイド: ${topic}`,
        content: `Learn about ${topic}`,
        contentJp: `${topic}について学ぶ`,
        difficulty: 'beginner',
        estimatedTime: 10,
        interactive: false
      }
    }
  }

  protected shouldHaveVisual(step: EducationalStep): boolean {
    // Group mail steps benefit from screenshots
    const visualKeywords = ['click', 'select', 'button', 'menu', 'screen']
    return visualKeywords.some(keyword => 
      step.description.toLowerCase().includes(keyword)
    )
  }

  protected async generateVisualAid(step: EducationalStep): Promise<string> {
    // In production, this would generate or retrieve relevant screenshots
    // For now, return a placeholder
    return `/images/group-mail/step-${step.order}.png`
  }

  protected async generateValidation(step: EducationalStep): Promise<string> {
    // Generate validation rules based on the step
    if (step.userAction?.includes('email address')) {
      return 'Check if email address format is valid'
    }
    if (step.userAction?.includes('select')) {
      return 'Verify at least one option is selected'
    }
    return 'Confirm action was completed'
  }

  protected async generateTroubleshooting(step: EducationalStep): Promise<string[]> {
    const tips: string[] = []
    
    if (step.description.includes('permission')) {
      tips.push('If you see "Access Denied", contact your department administrator')
    }
    if (step.description.includes('email')) {
      tips.push('If the email address is not found, check the spelling')
      tips.push('For external emails, you may need special approval')
    }
    if (step.description.includes('group')) {
      tips.push('If the group doesn\'t appear, it may not be assigned to your department')
    }
    
    return tips
  }

  protected analyzeFeedbackPatterns(feedback: any[]): Map<string, any> {
    const patterns = new Map<string, any>()
    
    // Analyze common issues
    const issues = feedback.map(f => f.feedback_type)
    const issueCount = new Map<string, number>()
    
    issues.forEach(issue => {
      issueCount.set(issue, (issueCount.get(issue) || 0) + 1)
    })
    
    patterns.set('commonIssues', Array.from(issueCount.entries()))
    patterns.set('totalFeedback', feedback.length)
    
    // Analyze suggestions
    const suggestions = feedback
      .filter(f => f.suggestion)
      .map(f => f.suggestion)
    
    patterns.set('userSuggestions', suggestions)
    
    return patterns
  }

  protected async adjustBehavior(patterns: Map<string, any>): Promise<void> {
    const commonIssues = patterns.get('commonIssues') || []
    
    // Adjust content generation based on feedback
    for (const [issue, count] of commonIssues) {
      if (issue === 'outdated' && count > 3) {
        // Increase web scraping frequency
        console.log(`[${this.agentId}] Increasing update frequency due to outdated content feedback`)
      }
      if (issue === 'not_helpful' && count > 5) {
        // Adjust content difficulty level
        console.log(`[${this.agentId}] Simplifying content due to not helpful feedback`)
      }
    }
  }
}

/**
 * Specialized AI Agent for SharePoint Management
 */
export class SharePointAgent extends BaseAIAgent {
  constructor() {
    const specialization: AgentSpecialization = {
      serviceCategory: 'sharepoint',
      expertise: [
        'SharePoint Online',
        'document libraries',
        'permissions management',
        'SharePoint sites',
        'Microsoft 365 integration'
      ],
      languages: ['en', 'jp'],
      targetAudience: 'non-technical staff',
      capabilities: {
        webScraping: true,
        contentTranslation: true,
        interactiveGuides: true,
        videoTutorials: true,
        stepByStepWizards: true,
        troubleshooting: true,
        realTimeAssistance: true
      }
    }
    
    super('sharepoint-agent', specialization)
  }

  protected buildEducationalPrompt(topic: string, content: string): string {
    return `
You are a SharePoint expert who specializes in teaching non-technical users.

Topic: ${topic}

Reference Content:
${content}

Create an educational guide that:
1. Demystifies SharePoint concepts using office analogies (folders, filing cabinets, etc.)
2. Provides visual step-by-step instructions
3. Includes Japanese translations for all UI elements
4. Covers common permission scenarios
5. Explains best practices for document organization

Include specific examples relevant to Japanese corporate culture.

Format as JSON with the same structure as before, ensuring all content is beginner-friendly.
`
  }

  // Similar implementations for other methods...
  protected parseEducationalContent(aiResponse: string, topic: string): EducationalContent {
    // Implementation similar to GroupMailAgent
    try {
      const parsed = JSON.parse(aiResponse)
      return {
        type: parsed.type || 'tutorial',
        title: parsed.title || topic,
        titleJp: parsed.titleJp || topic,
        content: parsed.content || '',
        contentJp: parsed.contentJp || '',
        difficulty: 'beginner',
        estimatedTime: parsed.estimatedTime || 20,
        interactive: true,
        steps: parsed.steps || [],
        relatedTopics: parsed.relatedTopics || []
      }
    } catch (error) {
      return this.createFallbackContent(topic)
    }
  }

  private createFallbackContent(topic: string): EducationalContent {
    return {
      type: 'tutorial',
      title: `SharePoint Tutorial: ${topic}`,
      titleJp: `SharePointチュートリアル: ${topic}`,
      content: `Learn how to use SharePoint for ${topic}`,
      contentJp: `SharePointを使用して${topic}を行う方法を学ぶ`,
      difficulty: 'beginner',
      estimatedTime: 15,
      interactive: false
    }
  }

  protected shouldHaveVisual(step: EducationalStep): boolean {
    // SharePoint benefits greatly from visuals
    return true
  }

  protected async generateVisualAid(step: EducationalStep): Promise<string> {
    // Generate descriptive alt text for accessibility
    return `/images/sharepoint/step-${step.order}-${step.title.toLowerCase().replace(/\s+/g, '-')}.png`
  }

  protected async generateValidation(step: EducationalStep): Promise<string> {
    if (step.userAction?.includes('permission')) {
      return 'Verify permission level is set correctly'
    }
    if (step.userAction?.includes('upload')) {
      return 'Check if file was uploaded successfully'
    }
    if (step.userAction?.includes('create')) {
      return 'Confirm new item was created'
    }
    return 'Verify action completed successfully'
  }

  protected async generateTroubleshooting(step: EducationalStep): Promise<string[]> {
    const tips: string[] = []
    
    tips.push('If you cannot see the SharePoint site, check with your administrator')
    tips.push('For upload errors, check file size limits (usually 250GB)')
    tips.push('If permissions don\'t work, wait 5 minutes for changes to apply')
    
    return tips
  }

  protected analyzeFeedbackPatterns(feedback: any[]): Map<string, any> {
    const patterns = new Map<string, any>()
    
    // Track specific SharePoint issues
    const permissionIssues = feedback.filter(f => 
      f.suggestion?.toLowerCase().includes('permission')
    ).length
    
    const navigationIssues = feedback.filter(f => 
      f.suggestion?.toLowerCase().includes('find') || 
      f.suggestion?.toLowerCase().includes('navigate')
    ).length
    
    patterns.set('permissionIssues', permissionIssues)
    patterns.set('navigationIssues', navigationIssues)
    patterns.set('totalFeedback', feedback.length)
    
    return patterns
  }

  protected async adjustBehavior(patterns: Map<string, any>): Promise<void> {
    const permissionIssues = patterns.get('permissionIssues') || 0
    const navigationIssues = patterns.get('navigationIssues') || 0
    
    if (permissionIssues > 3) {
      console.log(`[${this.agentId}] Creating more permission-focused content`)
      // Adjust future content to focus more on permissions
    }
    
    if (navigationIssues > 3) {
      console.log(`[${this.agentId}] Creating more navigation guides`)
      // Create more visual navigation guides
    }
  }
}

/**
 * Specialized AI Agent for Password Reset Services
 */
export class PasswordResetAgent extends BaseAIAgent {
  constructor() {
    const specialization: AgentSpecialization = {
      serviceCategory: 'password_reset',
      expertise: [
        'password management',
        'account recovery',
        'multi-factor authentication',
        'security best practices',
        'Microsoft 365 accounts'
      ],
      languages: ['en', 'jp'],
      targetAudience: 'all staff',
      capabilities: {
        webScraping: true,
        contentTranslation: true,
        interactiveGuides: true,
        videoTutorials: true,
        stepByStepWizards: true,
        troubleshooting: true,
        realTimeAssistance: true
      }
    }
    
    super('password-reset-agent', specialization)
  }

  protected buildEducationalPrompt(topic: string, content: string): string {
    return `
You are a security expert who helps non-technical users with password and account issues.

Topic: ${topic}

Create a guide that:
1. Emphasizes security without causing anxiety
2. Uses simple language and clear instructions
3. Includes memory tips for passwords
4. Covers MFA setup in detail
5. Provides emergency contact information

Make it reassuring and helpful, not intimidating.
`
  }

  // Other methods implementation...
  protected parseEducationalContent(aiResponse: string, topic: string): EducationalContent {
    try {
      return JSON.parse(aiResponse)
    } catch {
      return this.createFallbackContent(topic)
    }
  }

  private createFallbackContent(topic: string): EducationalContent {
    return {
      type: 'wizard',
      title: `Password Help: ${topic}`,
      titleJp: `パスワードヘルプ: ${topic}`,
      content: 'Step-by-step password assistance',
      contentJp: 'ステップバイステップのパスワード支援',
      difficulty: 'beginner',
      estimatedTime: 5,
      interactive: true
    }
  }

  protected shouldHaveVisual(step: EducationalStep): boolean {
    return true // Always show visuals for security procedures
  }

  protected async generateVisualAid(step: EducationalStep): Promise<string> {
    return `/images/password/step-${step.order}-secure.png`
  }

  protected async generateValidation(step: EducationalStep): Promise<string> {
    return 'Verify security requirements are met'
  }

  protected async generateTroubleshooting(step: EducationalStep): Promise<string[]> {
    return [
      'If you\'re locked out, wait 30 minutes before trying again',
      'For immediate help, contact IT support at extension 1234',
      'Keep your recovery email up to date'
    ]
  }

  protected analyzeFeedbackPatterns(feedback: any[]): Map<string, any> {
    return new Map<string, any>()
  }

  protected async adjustBehavior(patterns: Map<string, any>): Promise<void> {
    // Adjust based on feedback
  }
}
