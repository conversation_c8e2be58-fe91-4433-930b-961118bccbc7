"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Plus, 
  X, 
  Users, 
  Settings, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Loader2
} from 'lucide-react';
import type { 
  BatchRequest, 
  BatchRequestItem,
  BatchScenario 
} from '@/lib/batch-processing-types';
import { UserSearchInput } from '@/components/forms/user-search-input';
import { ServiceCategorySelect } from '@/components/forms/service-category-select';
import { ResourceSelector } from '@/components/forms/resource-selector';

interface BatchRequestBuilderProps {
  onSubmit: (request: BatchRequest) => void;
  isProcessing?: boolean;
}

export function BatchRequestBuilder({ onSubmit, isProcessing }: BatchRequestBuilderProps) {
  const [scenario, setScenario] = useState<BatchScenario>('single_user_multiple_requests_add');
  const [items, setItems] = useState<BatchRequestItem[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const scenarios: { value: BatchScenario; label: string; description: string }[] = [
    {
      value: 'single_user_multiple_requests_add',
      label: '単一ユーザー・複数リクエスト（追加）',
      description: '一人のユーザーに複数のサービスを追加'
    },
    {
      value: 'multiple_users_multiple_requests_add',
      label: '複数ユーザー・複数リクエスト（追加）',
      description: '複数のユーザーに複数のサービスを追加'
    },    {
      value: 'multiple_users_single_request_add',
      label: '複数ユーザー・単一リクエスト（追加）',
      description: '複数のユーザーに同じサービスを追加'
    },
    {
      value: 'single_user_multiple_requests_delete',
      label: '単一ユーザー・複数リクエスト（削除）',
      description: '一人のユーザーから複数のサービスを削除'
    },
    {
      value: 'single_user_multiple_requests_mixed',
      label: '単一ユーザー・複数リクエスト（混合）',
      description: '一人のユーザーのサービスを追加・削除'
    },
    {
      value: 'single_user_multiple_category_mixed',
      label: '単一ユーザー・複数カテゴリ（混合）',
      description: '一人のユーザーの複数カテゴリを操作'
    }
  ];

  const addItem = () => {
    const newItem: BatchRequestItem = {
      service_category_id: '',
      affected_users: [],
      action_type: 'add',
      target_resources: [],
      request_details: {}
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, updates: Partial<BatchRequestItem>) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], ...updates };
    setItems(updatedItems);
  };

  const validateRequest = (): boolean => {
    const errors: string[] = [];

    if (selectedUsers.length === 0) {
      errors.push('少なくとも1人のユーザーを選択してください。');
    }
    if (items.length === 0) {
      errors.push('少なくとも1つのサービスリクエストを追加してください。');
    }

    items.forEach((item, index) => {
      if (!item.service_category_id) {
        errors.push(`リクエスト ${index + 1}: サービスカテゴリを選択してください。`);
      }
      if (item.target_resources.length === 0) {
        errors.push(`リクエスト ${index + 1}: 対象リソースを選択してください。`);
      }
    });

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleSubmit = () => {
    if (!validateRequest()) return;

    const request: BatchRequest = {
      operation_type: getOperationType(scenario),
      items: items.map(item => ({
        ...item,
        affected_users: selectedUsers.map(u => u.id)
      }))
    };

    onSubmit(request);
  };

  const getOperationType = (scenario: BatchScenario): BatchRequest['operation_type'] => {
    switch (scenario) {
      case 'single_user_multiple_requests_add':
      case 'single_user_multiple_requests_delete':
      case 'single_user_multiple_requests_mixed':
      case 'single_user_multiple_category_mixed':
        return 'single_user_multi_service';
      case 'multiple_users_multiple_requests_add':
        return 'multi_user_multi_service';
      case 'multiple_users_single_request_add':
        return 'multi_user_single_service';
      default:
        return 'mixed_operations';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>        <CardTitle>バッチリクエストビルダー</CardTitle>
        <CardDescription>
          複数のユーザーやサービスに対する一括処理リクエストを作成します
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Scenario Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">処理シナリオ</label>
          <Tabs value={scenario} onValueChange={(v) => setScenario(v as BatchScenario)}>
            <TabsList className="grid grid-cols-2 gap-2 h-auto">
              {scenarios.map((s) => (
                <TabsTrigger
                  key={s.value}
                  value={s.value}
                  className="flex flex-col items-start p-3 h-auto"
                >
                  <span className="font-medium">{s.label}</span>
                  <span className="text-xs text-muted-foreground">{s.description}</span>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>

        {/* User Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">対象ユーザー</label>
          <UserSearchInput
            multiple={scenario.includes('multiple_users')}
            onSelect={setSelectedUsers}
            placeholder="ユーザーを検索..."
          />
          <div className="flex flex-wrap gap-2 mt-2">
            {selectedUsers.map((user) => (
              <Badge key={user.id} variant="secondary">
                <Users className="w-3 h-3 mr-1" />
                {user.name_jp || user.name_en}
              </Badge>
            ))}
          </div>
        </div>

        {/* Service Requests */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">サービスリクエスト</label>
            <Button
              type="button"
              variant="outline"              size="sm"
              onClick={addItem}
            >
              <Plus className="w-4 h-4 mr-1" />
              追加
            </Button>
          </div>

          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {items.map((item, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-start justify-between mb-4">
                    <h4 className="font-medium">リクエスト {index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="space-y-3">
                    <ServiceCategorySelect
                      value={item.service_category_id}
                      onChange={(value) => updateItem(index, { service_category_id: value })}
                    />

                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant={item.action_type === 'add' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateItem(index, { action_type: 'add' })}
                      >
                        追加
                      </Button>
                      <Button
                        type="button"
                        variant={item.action_type === 'remove' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateItem(index, { action_type: 'remove' })}
                      >
                        削除
                      </Button>
                      <Button
                        type="button"
                        variant={item.action_type === 'update' ? 'default' : 'outline'}
                        size="sm"                        onClick={() => updateItem(index, { action_type: 'update' })}
                      >
                        更新
                      </Button>
                    </div>

                    <ResourceSelector
                      serviceCategory={item.service_category_id}
                      selectedResources={item.target_resources}
                      onChange={(resources) => updateItem(index, { target_resources: resources })}
                    />
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside">
                {validationErrors.map((error, i) => (
                  <li key={i}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          disabled={isProcessing}
          className="w-full"
        >
          {isProcessing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              処理中...
            </>
          ) : (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              バッチリクエストを送信
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}