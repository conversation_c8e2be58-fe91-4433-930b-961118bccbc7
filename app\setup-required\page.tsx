'use client'

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { CheckCircle2, <PERSON><PERSON><PERSON>cle, Co<PERSON>, ExternalLink } from 'lucide-react'
import { useState } from 'react'

export default function SetupRequiredPage() {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const envExample = `# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# API Keys for AI Integration
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key`

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950 p-4">
      <div className="max-w-4xl mx-auto space-y-6 pt-10">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">ITSync Setup Required</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400">
            Your environment is not properly configured. Follow the steps below to get started.
          </p>
        </div>

        <Alert className="border-red-500 bg-red-50 dark:bg-red-950">
          <XCircle className="h-4 w-4 text-red-600" />
          <AlertTitle>Environment Configuration Missing</AlertTitle>
          <AlertDescription>
            The Supabase environment variables are not configured. This is required for the application to function.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Step 1: Create a Supabase Project</CardTitle>
            <CardDescription>
              If you haven't already, create a free Supabase project
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2">
              <li>Go to <a href="https://app.supabase.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline inline-flex items-center gap-1">
                app.supabase.com <ExternalLink className="h-3 w-3" />
              </a></li>
              <li>Sign up or log in to your account</li>
              <li>Click "New Project" and fill in the details</li>
              <li>Wait for the project to be created (this may take a few minutes)</li>
            </ol>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Step 2: Get Your Project Credentials</CardTitle>
            <CardDescription>
              Once your project is created, get the required credentials
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2">
              <li>Go to Settings → API in your Supabase project</li>
              <li>Copy the "Project URL" - this is your NEXT_PUBLIC_SUPABASE_URL</li>
              <li>Copy the "anon public" key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY</li>
              <li>Copy the "service_role" key - this is your SUPABASE_SERVICE_ROLE_KEY</li>
            </ol>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Step 3: Create Environment File</CardTitle>
            <CardDescription>
              Create a .env.local file in your project root with your credentials
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative">
              <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                <code>{envExample}</code>
              </pre>
              <Button
                size="sm"
                variant="secondary"
                className="absolute top-2 right-2"
                onClick={() => copyToClipboard(envExample)}
              >
                {copied ? <CheckCircle2 className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
            <Alert>
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Replace the placeholder values with your actual credentials from Supabase.
                Never commit the .env.local file to version control.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Step 4: Run Database Migrations</CardTitle>
            <CardDescription>
              Set up the database schema for ITSync
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ol className="list-decimal list-inside space-y-2">
              <li>Open the SQL Editor in your Supabase project</li>
              <li>Copy and run the migration scripts from the /supabase/migrations folder</li>
              <li>Verify that all tables are created successfully</li>
            </ol>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Step 5: Restart the Application</CardTitle>
            <CardDescription>
              After configuring the environment, restart your development server
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg">
              <code>npm run dev</code>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              The application should now connect to your Supabase project successfully.
            </p>
          </CardContent>
        </Card>

        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Need help? Check out the documentation or contact support.
          </p>
          <div className="flex gap-4 justify-center">
            <Button variant="outline" asChild>
              <a href="https://supabase.com/docs" target="_blank" rel="noopener noreferrer">
                Supabase Docs
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/docs" target="_blank" rel="noopener noreferrer">
                ITSync Docs
              </a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
