-- Insert groups (referencing divisions by name_en)
WITH division_ids AS (
  SELECT id, name_en FROM divisions
)
INSERT INTO groups (division_id, name_jp, name_en) VALUES
-- Corporate Planning Groups
((SELECT id FROM division_ids WHERE name_en = 'Corporate Planning Division'), '企画・総務グループ', 'Planning and Administration Group'),
((SELECT id FROM division_ids WHERE name_en = 'Corporate Planning Division'), 'プロジェクト・決裁管理グループ', 'Project and Approval Management Group'),

-- Human Resources Groups
((SELECT id FROM division_ids WHERE name_en = 'Human Resources Division'), '人事グループ', 'Human Resources Group'),
((SELECT id FROM division_ids WHERE name_en = 'Human Resources Division'), '財務・経理グループ', 'Finance and Accounting Group'),

-- Legal Groups
((SELECT id FROM division_ids WHERE name_en = 'Legal, Ethics and Compliance Division'), '法務グループ', 'Legal Group'),
((SELECT id FROM division_ids WHERE name_en = 'Legal, Ethics and Compliance Division'), 'コンプライアンスグループ', 'Ethics and Compliance Group'),

-- Fleet Operations Groups
((SELECT id FROM division_ids WHERE name_en = 'Fleet Operations Division'), '安全運航グループ', 'Safe Operation Group'),
((SELECT id FROM division_ids WHERE name_en = 'Fleet Operations Division'), 'ポートオペレーショングループ', 'Port Operation Group'),

-- Technical Management Groups
((SELECT id FROM division_ids WHERE name_en = 'Technical Management Division'), '工務グループ', 'Technical Management Group'),

-- IT Systems Groups
((SELECT id FROM division_ids WHERE name_en = 'IT Systems Division'), 'ITシステムグループ', 'IT Systems Group'),

-- Commercial Strategies Groups
((SELECT id FROM division_ids WHERE name_en = 'Commercial Strategies Division'), '営業グループ', 'Commercial Group'),
((SELECT id FROM division_ids WHERE name_en = 'Commercial Strategies Division'), '販売グループ', 'Sales Group'),
((SELECT id FROM division_ids WHERE name_en = 'Commercial Strategies Division'), 'マーケティンググループ', 'Marketing Group'),
((SELECT id FROM division_ids WHERE name_en = 'Commercial Strategies Division'), 'デスティネーションエクスピリエンスグループ', 'Destination Experience Group'),
((SELECT id FROM division_ids WHERE name_en = 'Commercial Strategies Division'), 'カスタマーリレーショングループ', 'Customer Relations Group'),

-- Guest Experience Groups
((SELECT id FROM division_ids WHERE name_en = 'Guest Experience Division'), 'ホテルオペレーショングループ', 'Hotel Operation Group'),
((SELECT id FROM division_ids WHERE name_en = 'Guest Experience Division'), 'ホテルサービスグループ', 'Hotel Service Group');
