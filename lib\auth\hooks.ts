/**
 * Authentication Hooks
 * Custom hooks for authentication state management
 */

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export interface AuthUser extends User {
  staff?: {
    id: string
    name_en?: string
    name_jp?: string
    email: string
    role?: string
    department?: string
  }
}

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const supabase = createClient()

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          setError(error.message)
          return
        }

        if (session?.user) {
          // Fetch staff details
          const { data: staff } = await supabase
            .from('staff')
            .select('id, name_en, name_jp, email, roles(name)')
            .eq('auth_id', session.user.id)
            .single()

          setUser({
            ...session.user,
            staff: staff ? {
              id: staff.id,
              name_en: staff.name_en,
              name_jp: staff.name_jp,
              email: staff.email,
              role: staff.roles?.name
            } : undefined
          })
        } else {
          setUser(null)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Authentication error')
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          // Fetch staff details
          const { data: staff } = await supabase
            .from('staff')
            .select('id, name_en, name_jp, email, roles(name)')
            .eq('auth_id', session.user.id)
            .single()

          setUser({
            ...session.user,
            staff: staff ? {
              id: staff.id,
              name_en: staff.name_en,
              name_jp: staff.name_jp,
              email: staff.email,
              role: staff.roles?.name
            } : undefined
          })
        } else {
          setUser(null)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signOut = async () => {
    const supabase = createClient()
    const { error } = await supabase.auth.signOut()
    if (error) {
      setError(error.message)
    }
  }

  return {
    user,
    loading,
    error,
    signOut,
    isAuthenticated: !!user
  }
}

export function useSupabase() {
  return createClient()
}
