import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export type AuditEventType = 
  | 'USER_LOGIN' | 'USER_LOGOUT' | 'USER_CREATED' | 'USER_UPDATED' | 'USER_DELETED'
  | 'PASSWORD_CHANGED' | 'PASSWORD_RESET' | 'ROLE_ASSIGNED' | 'ROLE_REMOVED'
  | 'REQUEST_CREATED' | 'REQUEST_UPDATED' | 'REQUEST_DELETED'
  | 'REQUEST_APPROVED' | 'REQUEST_REJECTED' | 'REQUEST_COMPLETED' | 'REQUEST_CANCELLED'
  | 'REQUEST_ASSIGNED' | 'REQUEST_ESCALATED'
  | 'DATA_ACCESSED' | 'DATA_EXPORTED' | 'DATA_IMPORTED'
  | 'PERMISSION_GRANTED' | 'PERMISSION_REVOKED'
  | 'CONFIGURATION_CHANGED' | 'SETTINGS_UPDATED'
  | 'SYSTEM_ERROR' | 'SYSTEM_WARNING' | 'SYSTEM_INFO'
  | 'INTEGRATION_SUCCESS' | 'INTEGRATION_FAILURE'
  | 'SCHEDULED_TASK_RUN' | 'SCHEDULED_TASK_FAILED'
  | 'SERVICE_STARTED' | 'SERVICE_STOPPED';

export type AuditSeverity = 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';

export interface AuditLogEntry {
  id?: string;
  event_type: AuditEventType;
  severity: AuditSeverity;
  action: string;
  description?: string;
  created_at?: string;
  occurred_at?: string;
  user_id?: string | null;
  staff_id?: string | null;
  role_name?: string;
  department_id?: string | null;
  department_name?: string;
  entity_type?: string;
  entity_id?: string;
  entity_name?: string;
  old_value?: any;
  new_value?: any;
  changes_summary?: any;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  request_id?: string;
  trace_id?: string;
  metadata?: Record<string, any>;
  tags?: string[];
  retention_until?: string;
  compliance_flags?: string[];
  is_sensitive?: boolean;
  checksum?: string;
}

export interface AuditSearchParams {
  event_types?: AuditEventType[];
  severity?: AuditSeverity[];
  user_id?: string;
  staff_id?: string;
  department_id?: string;
  entity_type?: string;
  entity_id?: string;
  start_date?: Date;
  end_date?: Date;
  search_text?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
  order_by?: 'created_at' | 'severity' | 'event_type';
  order_direction?: 'asc' | 'desc';
}

export interface AuditStatistics {
  total_events: number;
  events_by_type: Record<string, number>;
  events_by_severity: Record<string, number>;
  unique_users: number;
  departments_affected: number;
  time_range: {
    start: Date;
    end: Date;
  };
}

export interface ComplianceReport {
  report_id: string;
  generated_at: Date;
  period: {
    start: Date;
    end: Date;
  };
  summary: AuditStatistics;
  compliance_status: Record<string, boolean>;
  findings: Array<{
    type: string;
    severity: AuditSeverity;
    description: string;
    recommendation: string;
  }>;
}

class EnhancedAuditLogService {
  private static instance: EnhancedAuditLogService;
  private queue: AuditLogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 50;
  private readonly FLUSH_INTERVAL = 5000;

  private constructor() {
    this.startBatchProcessing();
    
    // Set up beforeunload handler to flush queue
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flush();
      });
    }
  }

  public static getInstance(): EnhancedAuditLogService {
    if (!EnhancedAuditLogService.instance) {
      EnhancedAuditLogService.instance = new EnhancedAuditLogService();
    }
    return EnhancedAuditLogService.instance;
  }

  /**
   * Start batch processing for performance
   */
  private startBatchProcessing(): void {
    this.flushTimer = setInterval(() => {
      if (this.queue.length > 0) {
        this.flush();
      }
    }, this.FLUSH_INTERVAL);
  }

  /**
   * Log an audit event
   */
  public async log(entry: AuditLogEntry): Promise<void> {
    try {
      // Add metadata
      const enrichedEntry = await this.enrichLogEntry(entry);
      
      // Add to queue
      this.queue.push(enrichedEntry);
      
      // Flush if batch size reached
      if (this.queue.length >= this.BATCH_SIZE) {
        await this.flush();
      }
      
      // For critical events, flush immediately
      if (entry.severity === 'CRITICAL' || entry.severity === 'ERROR') {
        await this.flush();
      }
    } catch (error) {
      console.error('Failed to log audit event:', error);
    }
  }

  /**
   * Enrich log entry with additional context
   */
  private async enrichLogEntry(entry: AuditLogEntry): Promise<AuditLogEntry> {
    const enriched = { ...entry };
    
    // Add timestamp if not provided
    if (!enriched.occurred_at) {
      enriched.occurred_at = new Date().toISOString();
    }
    
    // Get user context
    const { data: { user } } = await supabase.auth.getUser();
    if (user && !enriched.user_id) {
      enriched.user_id = user.id;
      
      // Get staff details
      const { data: staff } = await supabase
        .from('staff')
        .select('id, division_id, role_id, roles!inner(name), divisions!inner(name_en)')
        .eq('auth_id', user.id)
        .single();
      
      if (staff) {
        enriched.staff_id = staff.id;
        enriched.role_name = staff.roles?.name;
        enriched.department_id = staff.division_id;
        enriched.department_name = staff.divisions?.name_en;
      }
    }
    
    // Add technical context
    if (typeof window !== 'undefined') {
      enriched.user_agent = navigator.userAgent;
      // Note: IP address should be added server-side
    }
    
    // Generate request ID if not provided
    if (!enriched.request_id) {
      enriched.request_id = crypto.randomUUID();
    }
    
    // Calculate changes summary for updates
    if (enriched.old_value && enriched.new_value) {
      enriched.changes_summary = this.calculateChanges(
        enriched.old_value,
        enriched.new_value
      );
    }
    
    return enriched;
  }

  /**
   * Calculate changes between old and new values
   */
  private calculateChanges(oldValue: any, newValue: any): any {
    const changes: Record<string, any> = {};
    
    // Get all keys from both objects
    const allKeys = new Set([
      ...Object.keys(oldValue || {}),
      ...Object.keys(newValue || {})
    ]);
    
    // Compare each key
    allKeys.forEach(key => {
      const oldVal = oldValue?.[key];
      const newVal = newValue?.[key];
      
      if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
        changes[key] = {
          old: oldVal,
          new: newVal
        };
      }
    });
    
    return changes;
  }

  /**
   * Flush queued logs to database
   */
  private async flush(): Promise<void> {
    if (this.queue.length === 0) return;
    
    const logsToFlush = [...this.queue];
    this.queue = [];
    
    try {
      // Call edge function to handle server-side processing
      const { error } = await supabase.functions.invoke('process-audit-logs', {
        body: { logs: logsToFlush }
      });
      
      if (error) {
        console.error('Failed to flush audit logs:', error);
        // Re-queue failed logs
        this.queue.unshift(...logsToFlush);
      }
    } catch (error) {
      console.error('Failed to flush audit logs:', error);
      // Re-queue failed logs
      this.queue.unshift(...logsToFlush);
    }
  }

  /**
   * Search audit logs
   */
  public async search(params: AuditSearchParams): Promise<{
    data: AuditLogEntry[];
    count: number;
    error?: any;
  }> {
    try {
      let query = supabase
        .from('audit_logs_user_view')
        .select('*', { count: 'exact' });
      
      // Apply filters
      if (params.event_types?.length) {
        query = query.in('event_type', params.event_types);
      }
      
      if (params.severity?.length) {
        query = query.in('severity', params.severity);
      }
      
      if (params.user_id) {
        query = query.eq('user_id', params.user_id);
      }
      
      if (params.staff_id) {
        query = query.eq('staff_id', params.staff_id);
      }
      
      if (params.department_id) {
        query = query.eq('department_id', params.department_id);
      }
      
      if (params.entity_type) {
        query = query.eq('entity_type', params.entity_type);
      }
      
      if (params.entity_id) {
        query = query.eq('entity_id', params.entity_id);
      }
      
      if (params.start_date) {
        query = query.gte('created_at', params.start_date.toISOString());
      }
      
      if (params.end_date) {
        query = query.lte('created_at', params.end_date.toISOString());
      }
      
      if (params.search_text) {
        query = query.or(`action.ilike.%${params.search_text}%,description.ilike.%${params.search_text}%`);
      }
      
      if (params.tags?.length) {
        query = query.contains('tags', params.tags);
      }
      
      // Apply ordering
      const orderBy = params.order_by || 'created_at';
      const orderDirection = params.order_direction || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });
      
      // Apply pagination
      if (params.limit) {
        query = query.limit(params.limit);
      }
      
      if (params.offset) {
        query = query.range(params.offset, params.offset + (params.limit || 50) - 1);
      }
      
      const { data, count, error } = await query;
      
      // Log access to audit logs
      if (!error && data) {
        await this.logAuditAccess('SEARCH', params, data.length);
      }
      
      return { data: data || [], count: count || 0, error };
    } catch (error) {
      console.error('Failed to search audit logs:', error);
      return { data: [], count: 0, error };
    }
  }

  /**
   * Log access to audit logs
   */
  private async logAuditAccess(
    accessType: 'VIEW' | 'EXPORT' | 'SEARCH',
    queryParams: any,
    resultCount: number
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      const { data: staff } = await supabase
        .from('staff')
        .select('id')
        .eq('auth_id', user.id)
        .single();
      
      if (!staff) return;
      
      await supabase.from('audit_log_access').insert({
        accessed_by: staff.id,
        access_type: accessType,
        query_params: queryParams,
        result_count: resultCount,
        user_agent: typeof window !== 'undefined' ? navigator.userAgent : null
      });
    } catch (error) {
      console.error('Failed to log audit access:', error);
    }
  }

  /**
   * Get audit statistics
   */
  public async getStatistics(
    startDate: Date,
    endDate: Date,
    departmentId?: string
  ): Promise<AuditStatistics | null> {
    try {
      const { data, error } = await supabase.rpc('get_audit_statistics', {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        department_id: departmentId
      });
      
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Failed to get audit statistics:', error);
      return null;
    }
  }

  /**
   * Export audit logs
   */
  public async exportLogs(
    params: AuditSearchParams,
    format: 'CSV' | 'JSON' | 'PDF' | 'XLSX',
    templateId?: string
  ): Promise<{ url: string; error?: any }> {
    try {
      // Log the export action
      await this.log({
        event_type: 'DATA_EXPORTED',
        severity: 'INFO',
        action: 'Export audit logs',
        description: `Exported audit logs in ${format} format`,
        metadata: { params, format, templateId }
      });
      
      // Call edge function to generate export
      const { data, error } = await supabase.functions.invoke('export-audit-logs', {
        body: { params, format, templateId }
      });
      
      if (error) throw error;
      
      // Log export access
      await this.logAuditAccess('EXPORT', params, data.count || 0);
      
      return { url: data.url, error: null };
    } catch (error) {
      console.error('Failed to export audit logs:', error);
      return { url: '', error };
    }
  }

  /**
   * Generate compliance report
   */
  public async generateComplianceReport(
    startDate: Date,
    endDate: Date,
    reportType: string
  ): Promise<ComplianceReport | null> {
    try {
      // Log report generation
      await this.log({
        event_type: 'DATA_EXPORTED',
        severity: 'INFO',
        action: 'Generate compliance report',
        description: `Generated ${reportType} compliance report`,
        metadata: { startDate, endDate, reportType }
      });
      
      const { data, error } = await supabase.functions.invoke('generate-compliance-report', {
        body: { startDate, endDate, reportType }
      });
      
      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Failed to generate compliance report:', error);
      return null;
    }
  }

  /**
   * Clean up and stop batch processing
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    this.flush();
  }
}

// Export singleton instance
export const auditService = EnhancedAuditLogService.getInstance();

// Helper functions for common audit events
export const auditHelpers = {
  logLogin: () => auditService.log({
    event_type: 'USER_LOGIN',
    severity: 'INFO',
    action: 'User logged in',
    description: 'Successful authentication'
  }),
  
  logLogout: () => auditService.log({
    event_type: 'USER_LOGOUT',
    severity: 'INFO',
    action: 'User logged out',
    description: 'Session terminated'
  }),
  
  logRequestCreated: (requestId: string, requestType: string) => auditService.log({
    event_type: 'REQUEST_CREATED',
    severity: 'INFO',
    action: 'Created new request',
    description: `Created ${requestType} request`,
    entity_type: 'request_forms',
    entity_id: requestId
  }),
  
  logRequestUpdated: (requestId: string, changes: any) => auditService.log({
    event_type: 'REQUEST_UPDATED',
    severity: 'INFO',
    action: 'Updated request',
    entity_type: 'request_forms',
    entity_id: requestId,
    changes_summary: changes
  }),
  
  logError: (error: any, context?: any) => auditService.log({
    event_type: 'SYSTEM_ERROR',
    severity: 'ERROR',
    action: 'System error occurred',
    description: error.message || 'Unknown error',
    metadata: { error: error.toString(), stack: error.stack, context }
  }),
  
  logSecurityEvent: (action: string, severity: AuditSeverity = 'WARNING') => auditService.log({
    event_type: 'SYSTEM_WARNING',
    severity,
    action,
    compliance_flags: ['SECURITY']
  })
};
