[{"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\audit\\page.tsx": "1", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\performance\\page.tsx": "2", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\roles\\page.tsx": "3", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\ai-cost\\route.ts": "4", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\route.ts": "5", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\[id]\\restore\\route.ts": "6", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\performance\\optimize\\route.ts": "7", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\performance\\route.ts": "8", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\ai-agents\\route.ts": "9", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\challenge\\route.ts": "10", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\verify\\route.ts": "11", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\password-reset\\route.ts": "12", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\related\\route.ts": "13", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\search\\route.ts": "14", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\update-stale\\route.ts": "15", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\route.ts": "16", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\simple\\route.ts": "17", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\integrations\\[integration]\\route.ts": "18", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\faq\\route.ts": "19", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\update-content\\route.ts": "20", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\metrics\\route.ts": "21", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approval-chains\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\decision\\route.ts": "23", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\delegate\\route.ts": "24", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\route.ts": "25", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\[id]\\route.ts": "26", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\route.ts": "27", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\trigger\\route.ts": "28", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\[id]\\route.ts": "29", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\route.ts": "30", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\cancel\\route.ts": "31", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\route.ts": "32", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\categories\\route.ts": "33", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\export\\route.ts": "34", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts": "35", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\metrics\\route.ts": "36", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\trends\\route.ts": "37", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\rules\\route.ts": "38", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\at-risk\\route.ts": "39", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\export\\route.ts": "40", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\metrics\\route.ts": "41", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\route.ts": "42", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\tasks\\route.ts": "43", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\apply\\route.ts": "44", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\route.ts": "45", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\[id]\\route.ts": "46", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\batch-processing\\page.tsx": "47", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\password-reset\\page.tsx": "48", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\pc-admin-requests\\page.tsx": "49", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\page.tsx": "50", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\workflows\\monitoring\\page.tsx": "51", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\group-mail-management\\page.tsx": "52", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\knowledge-base\\article\\[slug]\\page.tsx": "53", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\knowledge-base\\layout.tsx": "54", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\knowledge-base\\page.tsx": "55", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx": "56", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\login\\page.tsx": "57", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mailbox-management\\page.tsx": "58", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mfa-verify\\page.tsx": "59", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\page.tsx": "60", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\pc-admin\\page.tsx": "61", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\enhanced\\page.tsx": "62", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\page.tsx": "63", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\requests\\[id]\\page.tsx": "64", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\setup-required\\page.tsx": "65", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\sharepoint-management\\page.tsx": "66", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test\\page.tsx": "67", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-agents\\page.tsx": "68", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-api\\page.tsx": "69", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-validation\\page.tsx": "70", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-audit-trail\\page.tsx": "71", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-auto-update\\page.tsx": "72", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-batch-processing\\page.tsx": "73", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-chatbot\\page.tsx": "74", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-embeddings\\page.tsx": "75", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-error-detection\\page.tsx": "76", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-faq-generation\\page.tsx": "77", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-historical-analysis\\page.tsx": "78", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-japanese\\page.tsx": "79", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-nlp\\page.tsx": "80", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-notifications\\page.tsx": "81", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-predictive-form\\page.tsx": "82", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\api\\docs.ts": "83", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\api\\users\\index.ts": "84", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\auth-docs.tsx": "85", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\docs.tsx": "86", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\admin\\ai-cost-dashboard.tsx": "87", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\audit\\audit-log-viewer.tsx": "88", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\audit\\enhanced-audit-log-viewer.tsx": "89", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\auth-guard.tsx": "90", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\login-form.tsx": "91", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\mfa-setup.tsx": "92", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\mfa-verify.tsx": "93", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\batch-processing\\batch-request-builder.tsx": "94", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\batch-processing\\batch-status-monitor.tsx": "95", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\chatbot\\chatbot-widget.tsx": "96", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\dynamic-field-with-error-detection.tsx": "97", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\dynamic-field.tsx": "98", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\dynamic-form.tsx": "99", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-dynamic-field-with-validation.tsx": "100", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-dynamic-field.tsx": "101", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-dynamic-form-with-validation.tsx": "102", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-request-confirmation.tsx": "103", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\error-indicator.tsx": "104", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\historical-suggestions-field.tsx": "105", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\localized-request-form.tsx": "106", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\multi-select.tsx": "107", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\nlp-input.tsx": "108", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\request-confirmation.tsx": "109", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\resource-selector.tsx": "110", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\searchable-select.tsx": "111", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\service-category-select.tsx": "112", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\service-selection.tsx": "113", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\user-search-input.tsx": "114", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\user-selection.tsx": "115", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\ai-agent-dashboard.tsx": "116", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-card.tsx": "117", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-feedback.tsx": "118", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-permissions.tsx": "119", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-view.tsx": "120", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\auto-update-manager.tsx": "121", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\category-filter.tsx": "122", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\faq-generation-manager.tsx": "123", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\faq-list.tsx": "124", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\helpdesk-kb-integration.tsx": "125", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-home.tsx": "126", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-integration-widget.tsx": "127", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-layout.tsx": "128", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-search.tsx": "129", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\related-articles.tsx": "130", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\notifications\\multi-channel-notifications.tsx": "131", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\password-reset\\reset-dialog.tsx": "132", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\pc-admin-request-form.tsx": "133", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\pc-admin-requests-list.tsx": "134", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\request-dialog.tsx": "135", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\request-list.tsx": "136", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\predictive\\predictive-field.tsx": "137", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\predictive\\predictive-suggestions.tsx": "138", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx": "139", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\audit-provider.tsx": "140", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\supabase-provider.tsx": "141", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\rbac\\permission-gate.tsx": "142", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\rbac\\role-management.tsx": "143", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\index.ts": "144", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\realtime-dashboard.tsx": "145", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\realtime-notifications.tsx": "146", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\request-status-tracker.tsx": "147", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\alert.tsx": "148", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\badge.tsx": "149", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\button.tsx": "150", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\calendar.tsx": "151", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\card.tsx": "152", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\checkbox.tsx": "153", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\command.tsx": "154", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\date-range-picker.tsx": "155", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\dialog.tsx": "156", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\dropdown-menu.tsx": "157", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\input.tsx": "158", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\japanese-date-picker.tsx": "159", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\label.tsx": "160", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\language-switcher.tsx": "161", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\loading.tsx": "162", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\popover.tsx": "163", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\progress.tsx": "164", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\scroll-area.tsx": "165", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\select.tsx": "166", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\separator.tsx": "167", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\skeleton.tsx": "168", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\switch.tsx": "169", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\table.tsx": "170", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\tabs.tsx": "171", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\textarea.tsx": "172", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\tooltip.tsx": "173", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\use-toast.ts": "174", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\user-multi-select.tsx": "175", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflow\\workflow-status-dashboard.tsx": "176", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\escalation\\escalation-manager.tsx": "177", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\escalation\\escalation-rule-form.tsx": "178", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\monitoring\\WorkflowMonitoringDashboard.tsx": "179", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\multi-level-approval.tsx": "180", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\sla-monitoring.tsx": "181", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\workflow-template-gallery.tsx": "182", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\agent-manager.ts": "183", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\base-agent-update.ts": "184", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\base-agent.ts": "185", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\mailbox-agent.ts": "186", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\pc-admin-agent.ts": "187", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\specialized-agents.ts": "188", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\ai-performance-optimizer.ts": "189", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\anthropic-client.ts": "190", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\content-filter.ts": "191", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\cost-analyzer.ts": "192", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\openai-client.ts": "193", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\reliability-manager.ts": "194", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\usage-tracker.ts": "195", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai-form-assistant-enhanced.ts": "196", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai-form-assistant-integrated.ts": "197", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai-form-assistant.ts": "198", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\api-documentation-page.tsx": "199", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\auth-documentation.tsx": "200", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\error-handler.ts": "201", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\openapi-documentation.ts": "202", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\redoc-ui.tsx": "203", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\swagger-ui.tsx": "204", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth\\hooks.ts": "205", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth-context.tsx": "206", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth.ts": "207", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\backup\\backup-manager.ts": "208", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\batch-processing-types.ts": "209", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\cache\\cache-service.ts": "210", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\cache\\redis-client.ts": "211", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\config\\ai-config.ts": "212", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\config\\env-validator.ts": "213", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\database\\query-optimizer.ts": "214", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\database.types.enhanced.ts": "215", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\database.types.ts": "216", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\email\\password-reset.ts": "217", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\env.ts": "218", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\form-types.ts": "219", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\form-utils.ts": "220", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\historical-data-service.ts": "221", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-chatbot.ts": "222", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-content-gaps.ts": "223", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-debounce.ts": "224", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-embeddings.ts": "225", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-error-detection.ts": "226", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-mfa.ts": "227", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-nlp-form.ts": "228", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-predictive-form.ts": "229", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-real-time-validation-enhanced.ts": "230", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-real-time-validation.ts": "231", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-realtime.ts": "232", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-toast.ts": "233", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\config.ts": "234", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\context.tsx": "235", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\language-provider.tsx": "236", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\mfa-translations.ts": "237", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\translations.ts": "238", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\use-translation.ts": "239", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\jobs\\job-processor.ts": "240", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\LanguageContext.tsx": "241", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\middleware\\response-optimizer.ts": "242", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\middleware\\security-headers.ts": "243", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\middleware\\validate.ts": "244", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\distributed-tracing.ts": "245", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\metrics-dashboard.ts": "246", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\metrics.ts": "247", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\monitoring-integration.ts": "248", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\pc-admin-types.ts": "249", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\bundle-analyzer.ts": "250", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\connection-pooling.ts": "251", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\database-config.ts": "252", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\database-optimization.ts": "253", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\frontend-optimization.ts": "254", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\lazy-loading.tsx": "255", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\optimization-hooks.ts": "256", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\pool-manager.ts": "257", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\redis-cache.ts": "258", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\web-vitals-monitor.ts": "259", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\rbac-utils.ts": "260", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\request-types.ts": "261", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\security\\rate-limiter.ts": "262", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\ai-api-service.ts": "263", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\audit-log-service.ts": "264", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\audit.ts": "265", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\batch-processing-service.ts": "266", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\cache-service.ts": "267", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\chatbot-service.ts": "268", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\content-auto-update-service.ts": "269", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\data-encryption-service.ts": "270", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\embeddings-service.ts": "271", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\encryption-service.ts": "272", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\enhanced-audit-service.ts": "273", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\enhanced-realtime-service.ts": "274", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\error-detection-service.ts": "275", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\error-service.ts": "276", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\faq-generation-service.ts": "277", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\kb-chatbot-integration.ts": "278", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\kb-permissions-service.ts": "279", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\log-collector-service.ts": "280", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\mfa-service.ts": "281", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\nlp-form-service.ts": "282", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\notifications\\notification-service.ts": "283", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\pc-admin-service.ts": "284", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\performance\\utils.ts": "285", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\predictive-form-service.ts": "286", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\query-optimizer.ts": "287", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\realtime-service.ts": "288", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\secrets-manager.ts": "289", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\security-audit-service.ts": "290", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\web-scraping-service.ts": "291", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\approval-engine.ts": "292", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\escalation\\escalation-engine.ts": "293", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\escalation\\escalation-rules-manager.ts": "294", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\escalation-engine.ts": "295", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\notification-integration.ts": "296", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\rule-engine.ts": "297", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\sla-manager.ts": "298", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\state-machine.ts": "299", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-template-manager.ts": "300", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-templates.ts": "301", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates.ts": "302", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\types.ts": "303", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\workflow-engine.ts": "304", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\workflow-template-manager.ts": "305", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase\\client.ts": "306", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase\\index.ts": "307", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase\\server.ts": "308", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase.ts": "309", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\test-utils\\legacy-adapters.ts": "310", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\types\\workflow.ts": "311", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\use-department-filter.ts": "312", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\use-permissions.ts": "313", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\utils.ts": "314", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\validation\\schemas.ts": "315", "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\validation.ts": "316"}, {"size": 4992, "mtime": 1748046540809, "results": "317", "hashOfConfig": "318"}, {"size": 6712, "mtime": 1748077838082, "results": "319", "hashOfConfig": "318"}, {"size": 2828, "mtime": 1747989553738, "results": "320", "hashOfConfig": "318"}, {"size": 15742, "mtime": 1748103866196, "results": "321", "hashOfConfig": "318"}, {"size": 2438, "mtime": 1748101171993, "results": "322", "hashOfConfig": "318"}, {"size": 1990, "mtime": 1748101184987, "results": "323", "hashOfConfig": "318"}, {"size": 2426, "mtime": 1748077878700, "results": "324", "hashOfConfig": "318"}, {"size": 11784, "mtime": 1748102893286, "results": "325", "hashOfConfig": "318"}, {"size": 3315, "mtime": 1748011553104, "results": "326", "hashOfConfig": "318"}, {"size": 881, "mtime": 1748053475240, "results": "327", "hashOfConfig": "318"}, {"size": 2297, "mtime": 1748166618400, "results": "328", "hashOfConfig": "318"}, {"size": 3171, "mtime": 1748134874457, "results": "329", "hashOfConfig": "318"}, {"size": 935, "mtime": 1748005849436, "results": "330", "hashOfConfig": "318"}, {"size": 935, "mtime": 1748005820121, "results": "331", "hashOfConfig": "318"}, {"size": 638, "mtime": 1748005835274, "results": "332", "hashOfConfig": "318"}, {"size": 9264, "mtime": 1748101029891, "results": "333", "hashOfConfig": "318"}, {"size": 620, "mtime": 1748101040409, "results": "334", "hashOfConfig": "318"}, {"size": 1888, "mtime": 1748134474542, "results": "335", "hashOfConfig": "318"}, {"size": 3143, "mtime": 1748038375799, "results": "336", "hashOfConfig": "318"}, {"size": 3566, "mtime": 1748008924693, "results": "337", "hashOfConfig": "318"}, {"size": 5523, "mtime": 1748101102681, "results": "338", "hashOfConfig": "318"}, {"size": 915, "mtime": 1748062726543, "results": "339", "hashOfConfig": "318"}, {"size": 1774, "mtime": 1748135187420, "results": "340", "hashOfConfig": "318"}, {"size": 1561, "mtime": 1748135187425, "results": "341", "hashOfConfig": "318"}, {"size": 4166, "mtime": 1748135187436, "results": "342", "hashOfConfig": "318"}, {"size": 5334, "mtime": 1748061141278, "results": "343", "hashOfConfig": "318"}, {"size": 2912, "mtime": 1748135544064, "results": "344", "hashOfConfig": "318"}, {"size": 2164, "mtime": 1748135187479, "results": "345", "hashOfConfig": "318"}, {"size": 3241, "mtime": 1748135135634, "results": "346", "hashOfConfig": "318"}, {"size": 3740, "mtime": 1748135187522, "results": "347", "hashOfConfig": "318"}, {"size": 1509, "mtime": 1748061208238, "results": "348", "hashOfConfig": "318"}, {"size": 1309, "mtime": 1748061191290, "results": "349", "hashOfConfig": "318"}, {"size": 2327, "mtime": 1748135187602, "results": "350", "hashOfConfig": "318"}, {"size": 3673, "mtime": 1748135187609, "results": "351", "hashOfConfig": "318"}, {"size": 3447, "mtime": 1748135923850, "results": "352", "hashOfConfig": "318"}, {"size": 4915, "mtime": 1748135187623, "results": "353", "hashOfConfig": "318"}, {"size": 2428, "mtime": 1748135187632, "results": "354", "hashOfConfig": "318"}, {"size": 2600, "mtime": 1748135187637, "results": "355", "hashOfConfig": "318"}, {"size": 853, "mtime": 1748135187649, "results": "356", "hashOfConfig": "318"}, {"size": 1675, "mtime": 1748135187654, "results": "357", "hashOfConfig": "318"}, {"size": 1350, "mtime": 1748135187656, "results": "358", "hashOfConfig": "318"}, {"size": 2585, "mtime": 1748135187642, "results": "359", "hashOfConfig": "318"}, {"size": 1795, "mtime": 1748135187664, "results": "360", "hashOfConfig": "318"}, {"size": 2215, "mtime": 1748135445985, "results": "361", "hashOfConfig": "318"}, {"size": 1475, "mtime": 1748135187671, "results": "362", "hashOfConfig": "318"}, {"size": 1238, "mtime": 1748135366087, "results": "363", "hashOfConfig": "318"}, {"size": 6407, "mtime": 1747998561290, "results": "364", "hashOfConfig": "318"}, {"size": 11692, "mtime": 1748135960686, "results": "365", "hashOfConfig": "318"}, {"size": 10716, "mtime": 1748136022424, "results": "366", "hashOfConfig": "318"}, {"size": 12557, "mtime": 1748075567559, "results": "367", "hashOfConfig": "318"}, {"size": 2483, "mtime": 1748075938491, "results": "368", "hashOfConfig": "318"}, {"size": 13448, "mtime": 1748167121717, "results": "369", "hashOfConfig": "318"}, {"size": 250, "mtime": 1748005003438, "results": "370", "hashOfConfig": "318"}, {"size": 237, "mtime": 1748004884375, "results": "371", "hashOfConfig": "318"}, {"size": 151, "mtime": 1748004890865, "results": "372", "hashOfConfig": "318"}, {"size": 536, "mtime": 1748132687214, "results": "373", "hashOfConfig": "318"}, {"size": 117, "mtime": 1747985433429, "results": "374", "hashOfConfig": "318"}, {"size": 1886, "mtime": 1748000316565, "results": "375", "hashOfConfig": "318"}, {"size": 2638, "mtime": 1748053445158, "results": "376", "hashOfConfig": "318"}, {"size": 2237, "mtime": 1748159097344, "results": "377", "hashOfConfig": "318"}, {"size": 1931, "mtime": 1747999859185, "results": "378", "hashOfConfig": "318"}, {"size": 13426, "mtime": 1748039288225, "results": "379", "hashOfConfig": "318"}, {"size": 13619, "mtime": 1748040031200, "results": "380", "hashOfConfig": "318"}, {"size": 3100, "mtime": 1748043966151, "results": "381", "hashOfConfig": "318"}, {"size": 6704, "mtime": 1748082826558, "results": "382", "hashOfConfig": "318"}, {"size": 3925, "mtime": 1748000353878, "results": "383", "hashOfConfig": "318"}, {"size": 880, "mtime": 1747981972289, "results": "384", "hashOfConfig": "318"}, {"size": 7648, "mtime": 1748011521634, "results": "385", "hashOfConfig": "318"}, {"size": 17938, "mtime": 1747995290011, "results": "386", "hashOfConfig": "318"}, {"size": 6525, "mtime": 1747992535824, "results": "387", "hashOfConfig": "318"}, {"size": 10166, "mtime": 1748046956756, "results": "388", "hashOfConfig": "318"}, {"size": 4052, "mtime": 1748009071671, "results": "389", "hashOfConfig": "318"}, {"size": 9160, "mtime": 1747998907425, "results": "390", "hashOfConfig": "318"}, {"size": 6482, "mtime": 1747996527708, "results": "391", "hashOfConfig": "318"}, {"size": 10438, "mtime": 1748005903765, "results": "392", "hashOfConfig": "318"}, {"size": 4332, "mtime": 1747994507544, "results": "393", "hashOfConfig": "318"}, {"size": 9631, "mtime": 1748038477965, "results": "394", "hashOfConfig": "318"}, {"size": 8148, "mtime": 1747993330855, "results": "395", "hashOfConfig": "318"}, {"size": 2375, "mtime": 1748078532741, "results": "396", "hashOfConfig": "318"}, {"size": 6702, "mtime": 1747994821419, "results": "397", "hashOfConfig": "318"}, {"size": 5848, "mtime": 1748052203400, "results": "398", "hashOfConfig": "318"}, {"size": 8067, "mtime": 1747997044619, "results": "399", "hashOfConfig": "318"}, {"size": 1201, "mtime": 1748158368126, "results": "400", "hashOfConfig": "318"}, {"size": 5471, "mtime": 1748158765320, "results": "401", "hashOfConfig": "318"}, {"size": 2037, "mtime": 1748158566671, "results": "402", "hashOfConfig": "318"}, {"size": 1073, "mtime": 1748158529546, "results": "403", "hashOfConfig": "318"}, {"size": 20446, "mtime": 1748104840460, "results": "404", "hashOfConfig": "318"}, {"size": 1785, "mtime": 1748044312042, "results": "405", "hashOfConfig": "318"}, {"size": 14691, "mtime": 1748046419991, "results": "406", "hashOfConfig": "318"}, {"size": 2343, "mtime": 1747985409111, "results": "407", "hashOfConfig": "318"}, {"size": 4331, "mtime": 1747985390626, "results": "408", "hashOfConfig": "318"}, {"size": 12298, "mtime": 1748053064557, "results": "409", "hashOfConfig": "318"}, {"size": 5045, "mtime": 1748053392149, "results": "410", "hashOfConfig": "318"}, {"size": 10807, "mtime": 1747998376052, "results": "411", "hashOfConfig": "318"}, {"size": 7515, "mtime": 1747998439280, "results": "412", "hashOfConfig": "318"}, {"size": 10457, "mtime": 1747996259704, "results": "413", "hashOfConfig": "318"}, {"size": 5454, "mtime": 1747994474276, "results": "414", "hashOfConfig": "318"}, {"size": 5931, "mtime": 1747990632020, "results": "415", "hashOfConfig": "318"}, {"size": 9762, "mtime": 1748104819476, "results": "416", "hashOfConfig": "318"}, {"size": 12520, "mtime": 1747992324272, "results": "417", "hashOfConfig": "318"}, {"size": 5999, "mtime": 1747990608390, "results": "418", "hashOfConfig": "318"}, {"size": 8523, "mtime": 1748105196049, "results": "419", "hashOfConfig": "318"}, {"size": 9693, "mtime": 1747990937961, "results": "420", "hashOfConfig": "318"}, {"size": 4667, "mtime": 1747994443596, "results": "421", "hashOfConfig": "318"}, {"size": 5752, "mtime": 1747993277141, "results": "422", "hashOfConfig": "318"}, {"size": 4212, "mtime": 1748078507251, "results": "423", "hashOfConfig": "318"}, {"size": 6325, "mtime": 1748094106615, "results": "424", "hashOfConfig": "318"}, {"size": 7324, "mtime": 1747994776885, "results": "425", "hashOfConfig": "318"}, {"size": 9241, "mtime": 1747988969891, "results": "426", "hashOfConfig": "318"}, {"size": 4690, "mtime": 1747998493483, "results": "427", "hashOfConfig": "318"}, {"size": 4617, "mtime": 1748094077959, "results": "428", "hashOfConfig": "318"}, {"size": 1522, "mtime": 1747998454365, "results": "429", "hashOfConfig": "318"}, {"size": 6540, "mtime": 1747988866415, "results": "430", "hashOfConfig": "318"}, {"size": 3380, "mtime": 1748093384725, "results": "431", "hashOfConfig": "318"}, {"size": 8689, "mtime": 1747988916567, "results": "432", "hashOfConfig": "318"}, {"size": 16262, "mtime": 1748011475377, "results": "433", "hashOfConfig": "318"}, {"size": 1965, "mtime": 1748004956865, "results": "434", "hashOfConfig": "318"}, {"size": 5836, "mtime": 1748009016607, "results": "435", "hashOfConfig": "318"}, {"size": 10861, "mtime": 1748013938218, "results": "436", "hashOfConfig": "318"}, {"size": 11659, "mtime": 1748014081243, "results": "437", "hashOfConfig": "318"}, {"size": 10585, "mtime": 1748008977408, "results": "438", "hashOfConfig": "318"}, {"size": 1484, "mtime": 1748004969446, "results": "439", "hashOfConfig": "318"}, {"size": 2061, "mtime": 1748131681804, "results": "440", "hashOfConfig": "318"}, {"size": 7578, "mtime": 1748037933145, "results": "441", "hashOfConfig": "318"}, {"size": 9405, "mtime": 1748039996819, "results": "442", "hashOfConfig": "318"}, {"size": 7432, "mtime": 1748014012146, "results": "443", "hashOfConfig": "318"}, {"size": 12476, "mtime": 1748039104712, "results": "444", "hashOfConfig": "318"}, {"size": 2913, "mtime": 1748004991473, "results": "445", "hashOfConfig": "318"}, {"size": 5653, "mtime": 1748131739192, "results": "446", "hashOfConfig": "318"}, {"size": 3398, "mtime": 1748007134152, "results": "447", "hashOfConfig": "318"}, {"size": 20893, "mtime": 1748051473717, "results": "448", "hashOfConfig": "318"}, {"size": 6003, "mtime": 1748136224312, "results": "449", "hashOfConfig": "318"}, {"size": 12064, "mtime": 1747999751877, "results": "450", "hashOfConfig": "318"}, {"size": 9716, "mtime": 1747999815698, "results": "451", "hashOfConfig": "318"}, {"size": 6910, "mtime": 1748136259429, "results": "452", "hashOfConfig": "318"}, {"size": 4958, "mtime": 1748001485007, "results": "453", "hashOfConfig": "318"}, {"size": 3012, "mtime": 1747996918689, "results": "454", "hashOfConfig": "318"}, {"size": 6061, "mtime": 1747996870413, "results": "455", "hashOfConfig": "318"}, {"size": 3861, "mtime": 1747995216502, "results": "456", "hashOfConfig": "318"}, {"size": 1612, "mtime": 1748046674444, "results": "457", "hashOfConfig": "318"}, {"size": 849, "mtime": 1748136103964, "results": "458", "hashOfConfig": "318"}, {"size": 1579, "mtime": 1747986722532, "results": "459", "hashOfConfig": "318"}, {"size": 1859, "mtime": 1747986756586, "results": "460", "hashOfConfig": "318"}, {"size": 189, "mtime": 1748043849301, "results": "461", "hashOfConfig": "318"}, {"size": 5867, "mtime": 1748043840286, "results": "462", "hashOfConfig": "318"}, {"size": 6447, "mtime": 1748043731019, "results": "463", "hashOfConfig": "318"}, {"size": 4680, "mtime": 1748043771710, "results": "464", "hashOfConfig": "318"}, {"size": 1584, "mtime": 1747989356508, "results": "465", "hashOfConfig": "318"}, {"size": 1128, "mtime": 1747989341228, "results": "466", "hashOfConfig": "318"}, {"size": 1902, "mtime": 1747981949967, "results": "467", "hashOfConfig": "318"}, {"size": 2974, "mtime": 1748133337307, "results": "468", "hashOfConfig": "318"}, {"size": 1847, "mtime": 1747981986740, "results": "469", "hashOfConfig": "318"}, {"size": 1056, "mtime": 1747989395250, "results": "470", "hashOfConfig": "318"}, {"size": 4921, "mtime": 1748131206716, "results": "471", "hashOfConfig": "318"}, {"size": 1811, "mtime": 1748046437813, "results": "472", "hashOfConfig": "318"}, {"size": 3875, "mtime": 1748131174141, "results": "473", "hashOfConfig": "318"}, {"size": 7644, "mtime": 1748131777573, "results": "474", "hashOfConfig": "318"}, {"size": 824, "mtime": 1747989366875, "results": "475", "hashOfConfig": "318"}, {"size": 2240, "mtime": 1748078485381, "results": "476", "hashOfConfig": "318"}, {"size": 710, "mtime": 1747989376094, "results": "477", "hashOfConfig": "318"}, {"size": 1103, "mtime": 1748078465795, "results": "478", "hashOfConfig": "318"}, {"size": 804, "mtime": 1748136079882, "results": "479", "hashOfConfig": "318"}, {"size": 1356, "mtime": 1748131304797, "results": "480", "hashOfConfig": "318"}, {"size": 791, "mtime": 1747989157133, "results": "481", "hashOfConfig": "318"}, {"size": 1647, "mtime": 1748093354884, "results": "482", "hashOfConfig": "318"}, {"size": 5614, "mtime": 1747989448633, "results": "483", "hashOfConfig": "318"}, {"size": 770, "mtime": 1747989171595, "results": "484", "hashOfConfig": "318"}, {"size": 261, "mtime": 1748094251615, "results": "485", "hashOfConfig": "318"}, {"size": 1152, "mtime": 1748094244233, "results": "486", "hashOfConfig": "318"}, {"size": 2765, "mtime": 1747989469332, "results": "487", "hashOfConfig": "318"}, {"size": 1883, "mtime": 1747989484784, "results": "488", "hashOfConfig": "318"}, {"size": 772, "mtime": 1747989404858, "results": "489", "hashOfConfig": "318"}, {"size": 1267, "mtime": 1748131358860, "results": "490", "hashOfConfig": "318"}, {"size": 944, "mtime": 1748093396507, "results": "491", "hashOfConfig": "318"}, {"size": 5412, "mtime": 1748001517459, "results": "492", "hashOfConfig": "318"}, {"size": 2744, "mtime": 1748059708385, "results": "493", "hashOfConfig": "318"}, {"size": 11367, "mtime": 1748073511792, "results": "494", "hashOfConfig": "318"}, {"size": 13141, "mtime": 1748073572323, "results": "495", "hashOfConfig": "318"}, {"size": 22285, "mtime": 1748075349086, "results": "496", "hashOfConfig": "318"}, {"size": 15755, "mtime": 1748062627302, "results": "497", "hashOfConfig": "318"}, {"size": 11205, "mtime": 1748066118446, "results": "498", "hashOfConfig": "318"}, {"size": 13902, "mtime": 1748074092494, "results": "499", "hashOfConfig": "318"}, {"size": 7709, "mtime": 1748010894209, "results": "500", "hashOfConfig": "318"}, {"size": 677, "mtime": 1748011613752, "results": "501", "hashOfConfig": "318"}, {"size": 12914, "mtime": 1748010765859, "results": "502", "hashOfConfig": "318"}, {"size": 1419, "mtime": 1748148642498, "results": "503", "hashOfConfig": "318"}, {"size": 13045, "mtime": 1748010972870, "results": "504", "hashOfConfig": "318"}, {"size": 13981, "mtime": 1748010849815, "results": "505", "hashOfConfig": "318"}, {"size": 13138, "mtime": 1748103957489, "results": "506", "hashOfConfig": "318"}, {"size": 2121, "mtime": 1748104954012, "results": "507", "hashOfConfig": "318"}, {"size": 21650, "mtime": 1748104897037, "results": "508", "hashOfConfig": "318"}, {"size": 23746, "mtime": 1748103539669, "results": "509", "hashOfConfig": "318"}, {"size": 2379, "mtime": 1748104935059, "results": "510", "hashOfConfig": "318"}, {"size": 17663, "mtime": 1748103615108, "results": "511", "hashOfConfig": "318"}, {"size": 16827, "mtime": 1748103435790, "results": "512", "hashOfConfig": "318"}, {"size": 15155, "mtime": 1747992231296, "results": "513", "hashOfConfig": "318"}, {"size": 8976, "mtime": 1747993238624, "results": "514", "hashOfConfig": "318"}, {"size": 7971, "mtime": 1747991874773, "results": "515", "hashOfConfig": "318"}, {"size": 10482, "mtime": 1748157494404, "results": "516", "hashOfConfig": "318"}, {"size": 14275, "mtime": 1748157755625, "results": "517", "hashOfConfig": "318"}, {"size": 3651, "mtime": 1748057614406, "results": "518", "hashOfConfig": "318"}, {"size": 20029, "mtime": 1748157192567, "results": "519", "hashOfConfig": "318"}, {"size": 9434, "mtime": 1748157407381, "results": "520", "hashOfConfig": "318"}, {"size": 11839, "mtime": 1748157324187, "results": "521", "hashOfConfig": "318"}, {"size": 2883, "mtime": 1748136095168, "results": "522", "hashOfConfig": "318"}, {"size": 7947, "mtime": 1748137430430, "results": "523", "hashOfConfig": "318"}, {"size": 11993, "mtime": 1748152291321, "results": "524", "hashOfConfig": "318"}, {"size": 12437, "mtime": 1748104875979, "results": "525", "hashOfConfig": "318"}, {"size": 2826, "mtime": 1747998094857, "results": "526", "hashOfConfig": "318"}, {"size": 9892, "mtime": 1748102491513, "results": "527", "hashOfConfig": "318"}, {"size": 9787, "mtime": 1748102446181, "results": "528", "hashOfConfig": "318"}, {"size": 2928, "mtime": 1747995150550, "results": "529", "hashOfConfig": "318"}, {"size": 7210, "mtime": 1748151241087, "results": "530", "hashOfConfig": "318"}, {"size": 12298, "mtime": 1748102547336, "results": "531", "hashOfConfig": "318"}, {"size": 11044, "mtime": 1748152186390, "results": "532", "hashOfConfig": "318"}, {"size": 6905, "mtime": 1747986638476, "results": "533", "hashOfConfig": "318"}, {"size": 2025, "mtime": 1748134437995, "results": "534", "hashOfConfig": "318"}, {"size": 2481, "mtime": 1748100180616, "results": "535", "hashOfConfig": "318"}, {"size": 1415, "mtime": 1747991134663, "results": "536", "hashOfConfig": "318"}, {"size": 2462, "mtime": 1747991176017, "results": "537", "hashOfConfig": "318"}, {"size": 4585, "mtime": 1747993181426, "results": "538", "hashOfConfig": "318"}, {"size": 2423, "mtime": 1747996545655, "results": "539", "hashOfConfig": "318"}, {"size": 2381, "mtime": 1748009035191, "results": "540", "hashOfConfig": "318"}, {"size": 856, "mtime": 1748133404572, "results": "541", "hashOfConfig": "318"}, {"size": 4675, "mtime": 1748005784591, "results": "542", "hashOfConfig": "318"}, {"size": 4262, "mtime": 1747994412292, "results": "543", "hashOfConfig": "318"}, {"size": 2930, "mtime": 1748053362517, "results": "544", "hashOfConfig": "318"}, {"size": 2831, "mtime": 1747994844477, "results": "545", "hashOfConfig": "318"}, {"size": 3911, "mtime": 1747996896108, "results": "546", "hashOfConfig": "318"}, {"size": 4257, "mtime": 1747992259651, "results": "547", "hashOfConfig": "318"}, {"size": 7205, "mtime": 1747991856783, "results": "548", "hashOfConfig": "318"}, {"size": 1630, "mtime": 1748043628043, "results": "549", "hashOfConfig": "318"}, {"size": 2307, "mtime": 1748131480676, "results": "550", "hashOfConfig": "318"}, {"size": 2500, "mtime": 1748078388436, "results": "551", "hashOfConfig": "318"}, {"size": 2813, "mtime": 1748003812834, "results": "552", "hashOfConfig": "318"}, {"size": 1378, "mtime": 1748078452118, "results": "553", "hashOfConfig": "318"}, {"size": 5074, "mtime": 1748053559422, "results": "554", "hashOfConfig": "318"}, {"size": 12245, "mtime": 1748003791729, "results": "555", "hashOfConfig": "318"}, {"size": 3137, "mtime": 1748094053305, "results": "556", "hashOfConfig": "318"}, {"size": 11212, "mtime": 1748102839264, "results": "557", "hashOfConfig": "318"}, {"size": 5804, "mtime": 1748094198884, "results": "558", "hashOfConfig": "318"}, {"size": 13371, "mtime": 1748102602622, "results": "559", "hashOfConfig": "318"}, {"size": 7535, "mtime": 1748153488660, "results": "560", "hashOfConfig": "318"}, {"size": 4407, "mtime": 1748153412195, "results": "561", "hashOfConfig": "318"}, {"size": 17547, "mtime": 1748156761427, "results": "562", "hashOfConfig": "318"}, {"size": 17200, "mtime": 1748156627914, "results": "563", "hashOfConfig": "318"}, {"size": 7600, "mtime": 1748101075241, "results": "564", "hashOfConfig": "318"}, {"size": 13776, "mtime": 1748156460284, "results": "565", "hashOfConfig": "318"}, {"size": 2042, "mtime": 1747999617485, "results": "566", "hashOfConfig": "318"}, {"size": 12366, "mtime": 1748102662215, "results": "567", "hashOfConfig": "318"}, {"size": 1817, "mtime": 1748078317239, "results": "568", "hashOfConfig": "318"}, {"size": 2427, "mtime": 1748076933330, "results": "569", "hashOfConfig": "318"}, {"size": 3461, "mtime": 1748077733098, "results": "570", "hashOfConfig": "318"}, {"size": 1546, "mtime": 1748077765255, "results": "571", "hashOfConfig": "318"}, {"size": 3605, "mtime": 1748076881564, "results": "572", "hashOfConfig": "318"}, {"size": 4585, "mtime": 1748076911988, "results": "573", "hashOfConfig": "318"}, {"size": 1970, "mtime": 1748078334469, "results": "574", "hashOfConfig": "318"}, {"size": 3451, "mtime": 1748078293234, "results": "575", "hashOfConfig": "318"}, {"size": 10700, "mtime": 1748102711056, "results": "576", "hashOfConfig": "318"}, {"size": 2838, "mtime": 1747986806334, "results": "577", "hashOfConfig": "318"}, {"size": 902, "mtime": 1747988736004, "results": "578", "hashOfConfig": "318"}, {"size": 4385, "mtime": 1748100290442, "results": "579", "hashOfConfig": "318"}, {"size": 9662, "mtime": 1747994936962, "results": "580", "hashOfConfig": "318"}, {"size": 6583, "mtime": 1748044207031, "results": "581", "hashOfConfig": "318"}, {"size": 3448, "mtime": 1748134457297, "results": "582", "hashOfConfig": "318"}, {"size": 8848, "mtime": 1747998143019, "results": "583", "hashOfConfig": "318"}, {"size": 6043, "mtime": 1748076807550, "results": "584", "hashOfConfig": "318"}, {"size": 4362, "mtime": 1747996111919, "results": "585", "hashOfConfig": "318"}, {"size": 19722, "mtime": 1748008580436, "results": "586", "hashOfConfig": "318"}, {"size": 7977, "mtime": 1748053687649, "results": "587", "hashOfConfig": "318"}, {"size": 9791, "mtime": 1748005682943, "results": "588", "hashOfConfig": "318"}, {"size": 3412, "mtime": 1748166583152, "results": "589", "hashOfConfig": "318"}, {"size": 14755, "mtime": 1748045528940, "results": "590", "hashOfConfig": "318"}, {"size": 6538, "mtime": 1748043611546, "results": "591", "hashOfConfig": "318"}, {"size": 5726, "mtime": 1747994387276, "results": "592", "hashOfConfig": "318"}, {"size": 9266, "mtime": 1748151933358, "results": "593", "hashOfConfig": "318"}, {"size": 12423, "mtime": 1748037767220, "results": "594", "hashOfConfig": "318"}, {"size": 7451, "mtime": 1748039153785, "results": "595", "hashOfConfig": "318"}, {"size": 9620, "mtime": 1748013273878, "results": "596", "hashOfConfig": "318"}, {"size": 9977, "mtime": 1748046660112, "results": "597", "hashOfConfig": "318"}, {"size": 21709, "mtime": 1748166676758, "results": "598", "hashOfConfig": "318"}, {"size": 8483, "mtime": 1747994665271, "results": "599", "hashOfConfig": "318"}, {"size": 15239, "mtime": 1748049388348, "results": "600", "hashOfConfig": "318"}, {"size": 6724, "mtime": 1747999656445, "results": "601", "hashOfConfig": "318"}, {"size": 1202, "mtime": 1748138606428, "results": "602", "hashOfConfig": "318"}, {"size": 8485, "mtime": 1747996721542, "results": "603", "hashOfConfig": "318"}, {"size": 7776, "mtime": 1748076766102, "results": "604", "hashOfConfig": "318"}, {"size": 1487, "mtime": 1747998159957, "results": "605", "hashOfConfig": "318"}, {"size": 13214, "mtime": 1748153815450, "results": "606", "hashOfConfig": "318"}, {"size": 8897, "mtime": 1748054132001, "results": "607", "hashOfConfig": "318"}, {"size": 6932, "mtime": 1748011600092, "results": "608", "hashOfConfig": "318"}, {"size": 17041, "mtime": 1748062245093, "results": "609", "hashOfConfig": "318"}, {"size": 25366, "mtime": 1748070935335, "results": "610", "hashOfConfig": "318"}, {"size": 10372, "mtime": 1748070995675, "results": "611", "hashOfConfig": "318"}, {"size": 6318, "mtime": 1748059514451, "results": "612", "hashOfConfig": "318"}, {"size": 12770, "mtime": 1748076006329, "results": "613", "hashOfConfig": "318"}, {"size": 5979, "mtime": 1748059036104, "results": "614", "hashOfConfig": "318"}, {"size": 16825, "mtime": 1748062863831, "results": "615", "hashOfConfig": "318"}, {"size": 3342, "mtime": 1748059059764, "results": "616", "hashOfConfig": "318"}, {"size": 10682, "mtime": 1748073912529, "results": "617", "hashOfConfig": "318"}, {"size": 17195, "mtime": 1748073704107, "results": "618", "hashOfConfig": "318"}, {"size": 14745, "mtime": 1748059596014, "results": "619", "hashOfConfig": "318"}, {"size": 3765, "mtime": 1748058996561, "results": "620", "hashOfConfig": "318"}, {"size": 15978, "mtime": 1748076095996, "results": "621", "hashOfConfig": "318"}, {"size": 7709, "mtime": 1748135651792, "results": "622", "hashOfConfig": "318"}, {"size": 425, "mtime": 1748131431441, "results": "623", "hashOfConfig": "318"}, {"size": 4531, "mtime": 1748152211061, "results": "624", "hashOfConfig": "318"}, {"size": 893, "mtime": 1748131450672, "results": "625", "hashOfConfig": "318"}, {"size": 534, "mtime": 1748151494355, "results": "626", "hashOfConfig": "318"}, {"size": 7239, "mtime": 1748153171973, "results": "627", "hashOfConfig": "318"}, {"size": 1246, "mtime": 1748076040724, "results": "628", "hashOfConfig": "318"}, {"size": 1811, "mtime": 1747991007040, "results": "629", "hashOfConfig": "318"}, {"size": 2018, "mtime": 1747985338796, "results": "630", "hashOfConfig": "318"}, {"size": 166, "mtime": 1747981585093, "results": "631", "hashOfConfig": "318"}, {"size": 5042, "mtime": 1748153329745, "results": "632", "hashOfConfig": "318"}, {"size": 1752, "mtime": 1747985249633, "results": "633", "hashOfConfig": "318"}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "15elhjq", {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1372", "messages": "1373", "suppressedMessages": "1374", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1423", "messages": "1424", "suppressedMessages": "1425", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1426", "messages": "1427", "suppressedMessages": "1428", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1429", "messages": "1430", "suppressedMessages": "1431", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1432", "messages": "1433", "suppressedMessages": "1434", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1435", "messages": "1436", "suppressedMessages": "1437", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1438", "messages": "1439", "suppressedMessages": "1440", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1441", "messages": "1442", "suppressedMessages": "1443", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1444", "messages": "1445", "suppressedMessages": "1446", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1447", "messages": "1448", "suppressedMessages": "1449", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1450", "messages": "1451", "suppressedMessages": "1452", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1453", "messages": "1454", "suppressedMessages": "1455", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1456", "messages": "1457", "suppressedMessages": "1458", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1459", "messages": "1460", "suppressedMessages": "1461", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1462", "messages": "1463", "suppressedMessages": "1464", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1465", "messages": "1466", "suppressedMessages": "1467", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1468", "messages": "1469", "suppressedMessages": "1470", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1471", "messages": "1472", "suppressedMessages": "1473", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1474", "messages": "1475", "suppressedMessages": "1476", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1477", "messages": "1478", "suppressedMessages": "1479", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1480", "messages": "1481", "suppressedMessages": "1482", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1483", "messages": "1484", "suppressedMessages": "1485", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1486", "messages": "1487", "suppressedMessages": "1488", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1489", "messages": "1490", "suppressedMessages": "1491", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1492", "messages": "1493", "suppressedMessages": "1494", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1495", "messages": "1496", "suppressedMessages": "1497", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1498", "messages": "1499", "suppressedMessages": "1500", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1501", "messages": "1502", "suppressedMessages": "1503", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1504", "messages": "1505", "suppressedMessages": "1506", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1507", "messages": "1508", "suppressedMessages": "1509", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1510", "messages": "1511", "suppressedMessages": "1512", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1513", "messages": "1514", "suppressedMessages": "1515", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1516", "messages": "1517", "suppressedMessages": "1518", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1519", "messages": "1520", "suppressedMessages": "1521", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1522", "messages": "1523", "suppressedMessages": "1524", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1525", "messages": "1526", "suppressedMessages": "1527", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1528", "messages": "1529", "suppressedMessages": "1530", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1531", "messages": "1532", "suppressedMessages": "1533", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1534", "messages": "1535", "suppressedMessages": "1536", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1537", "messages": "1538", "suppressedMessages": "1539", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1540", "messages": "1541", "suppressedMessages": "1542", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1543", "messages": "1544", "suppressedMessages": "1545", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1546", "messages": "1547", "suppressedMessages": "1548", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1549", "messages": "1550", "suppressedMessages": "1551", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1552", "messages": "1553", "suppressedMessages": "1554", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1555", "messages": "1556", "suppressedMessages": "1557", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1558", "messages": "1559", "suppressedMessages": "1560", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1561", "messages": "1562", "suppressedMessages": "1563", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1564", "messages": "1565", "suppressedMessages": "1566", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1567", "messages": "1568", "suppressedMessages": "1569", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1570", "messages": "1571", "suppressedMessages": "1572", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1573", "messages": "1574", "suppressedMessages": "1575", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1576", "messages": "1577", "suppressedMessages": "1578", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1579", "messages": "1580", "suppressedMessages": "1581", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\audit\\page.tsx", ["1582", "1583", "1584"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\performance\\page.tsx", ["1585", "1586"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\admin\\roles\\page.tsx", ["1587", "1588", "1589"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\ai-cost\\route.ts", ["1590", "1591"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\route.ts", ["1592", "1593"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\backup\\[id]\\restore\\route.ts", ["1594", "1595"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\performance\\optimize\\route.ts", ["1596", "1597"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\admin\\performance\\route.ts", ["1598", "1599"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\ai-agents\\route.ts", ["1600", "1601"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\challenge\\route.ts", ["1602", "1603"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\mfa\\verify\\route.ts", ["1604", "1605"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\auth\\password-reset\\route.ts", ["1606", "1607"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\related\\route.ts", ["1608", "1609"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\search\\route.ts", ["1610", "1611"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\embeddings\\update-stale\\route.ts", ["1612", "1613"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\route.ts", ["1614", "1615"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\health\\simple\\route.ts", ["1616", "1617"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\integrations\\[integration]\\route.ts", ["1618", "1619"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\faq\\route.ts", ["1620", "1621"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\knowledge-base\\update-content\\route.ts", ["1622", "1623"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\metrics\\route.ts", ["1624", "1625"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approval-chains\\[id]\\route.ts", ["1626", "1627"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\decision\\route.ts", ["1628", "1629"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\delegate\\route.ts", ["1630", "1631"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\route.ts", ["1632", "1633"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\definitions\\[id]\\route.ts", ["1634", "1635"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\route.ts", ["1636", "1637"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\trigger\\route.ts", ["1638", "1639"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\escalation\\[id]\\route.ts", ["1640", "1641"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\route.ts", ["1642", "1643"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\cancel\\route.ts", ["1644", "1645"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\instances\\[id]\\route.ts", ["1646", "1647"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\categories\\route.ts", ["1648", "1649"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\export\\route.ts", ["1650", "1651"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\instances\\route.ts", ["1652", "1653"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\metrics\\route.ts", ["1654", "1655"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\monitoring\\trends\\route.ts", ["1656", "1657"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\rules\\route.ts", ["1658", "1659"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\at-risk\\route.ts", ["1660", "1661"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\export\\route.ts", ["1662", "1663"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\metrics\\route.ts", ["1664", "1665"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\sla\\route.ts", ["1666", "1667"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\tasks\\route.ts", ["1668", "1669"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\apply\\route.ts", ["1670", "1671"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\route.ts", ["1672", "1673"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\templates\\[id]\\route.ts", ["1674", "1675"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\batch-processing\\page.tsx", ["1676", "1677"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\password-reset\\page.tsx", ["1678", "1679", "1680"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\it-helpdesk\\pc-admin-requests\\page.tsx", ["1681", "1682", "1683"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\page.tsx", ["1684", "1685"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\workflows\\monitoring\\page.tsx", ["1686", "1687"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\group-mail-management\\page.tsx", ["1688", "1689", "1690", "1691"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\knowledge-base\\article\\[slug]\\page.tsx", ["1692", "1693"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\knowledge-base\\layout.tsx", ["1694", "1695"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\knowledge-base\\page.tsx", ["1696", "1697"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx", ["1698", "1699"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\login\\page.tsx", ["1700", "1701"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mailbox-management\\page.tsx", ["1702"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\mfa-verify\\page.tsx", ["1703", "1704"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\page.tsx", ["1705", "1706"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\pc-admin\\page.tsx", ["1707"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\enhanced\\page.tsx", ["1708", "1709", "1710"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\request\\page.tsx", ["1711", "1712", "1713"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\requests\\[id]\\page.tsx", ["1714", "1715", "1716"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\setup-required\\page.tsx", ["1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\sharepoint-management\\page.tsx", ["1728"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test\\page.tsx", ["1729", "1730"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-agents\\page.tsx", ["1731", "1732"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-api\\page.tsx", ["1733", "1734"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-validation\\page.tsx", ["1735", "1736", "1737", "1738", "1739", "1740"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-audit-trail\\page.tsx", ["1741", "1742"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-auto-update\\page.tsx", ["1743", "1744", "1745"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-batch-processing\\page.tsx", ["1746"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-chatbot\\page.tsx", ["1747", "1748"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-embeddings\\page.tsx", ["1749", "1750"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-error-detection\\page.tsx", ["1751", "1752"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-faq-generation\\page.tsx", ["1753", "1754"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-historical-analysis\\page.tsx", ["1755", "1756"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-japanese\\page.tsx", ["1757", "1758"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-nlp\\page.tsx", ["1759", "1760"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-notifications\\page.tsx", ["1761"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-predictive-form\\page.tsx", ["1762"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\api\\docs.ts", ["1763", "1764"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\api\\users\\index.ts", ["1765", "1766"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\auth-docs.tsx", ["1767", "1768"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\pages\\docs.tsx", ["1769", "1770"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\admin\\ai-cost-dashboard.tsx", ["1771", "1772", "1773"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\audit\\audit-log-viewer.tsx", ["1774"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\audit\\enhanced-audit-log-viewer.tsx", ["1775", "1776", "1777"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\auth-guard.tsx", ["1778", "1779", "1780"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\login-form.tsx", ["1781", "1782"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\mfa-setup.tsx", ["1783", "1784"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\auth\\mfa-verify.tsx", ["1785", "1786"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\batch-processing\\batch-request-builder.tsx", ["1787", "1788"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\batch-processing\\batch-status-monitor.tsx", ["1789", "1790", "1791"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\chatbot\\chatbot-widget.tsx", ["1792", "1793"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\dynamic-field-with-error-detection.tsx", ["1794", "1795"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\dynamic-field.tsx", ["1796", "1797"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\dynamic-form.tsx", ["1798", "1799"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-dynamic-field-with-validation.tsx", ["1800", "1801"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-dynamic-field.tsx", ["1802", "1803"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-dynamic-form-with-validation.tsx", ["1804", "1805"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\enhanced-request-confirmation.tsx", ["1806", "1807"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\error-indicator.tsx", ["1808", "1809"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\historical-suggestions-field.tsx", ["1810", "1811"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\localized-request-form.tsx", ["1812", "1813"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\multi-select.tsx", ["1814", "1815"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\nlp-input.tsx", ["1816", "1817"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\request-confirmation.tsx", ["1818", "1819"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\resource-selector.tsx", ["1820", "1821", "1822"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\searchable-select.tsx", ["1823", "1824"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\service-category-select.tsx", ["1825", "1826", "1827"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\service-selection.tsx", ["1828", "1829", "1830"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\user-search-input.tsx", ["1831", "1832"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\forms\\user-selection.tsx", ["1833", "1834", "1835", "1836"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\ai-agent-dashboard.tsx", ["1837", "1838", "1839"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-card.tsx", ["1840", "1841"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-feedback.tsx", ["1842", "1843"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-permissions.tsx", ["1844", "1845"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\article-view.tsx", ["1846", "1847", "1848"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\auto-update-manager.tsx", ["1849", "1850", "1851"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\category-filter.tsx", ["1852", "1853"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\faq-generation-manager.tsx", ["1854", "1855"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\faq-list.tsx", ["1856", "1857"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\helpdesk-kb-integration.tsx", ["1858", "1859", "1860"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-home.tsx", ["1861", "1862", "1863"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-integration-widget.tsx", ["1864", "1865", "1866"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-layout.tsx", ["1867", "1868", "1869"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\kb-search.tsx", ["1870", "1871"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\knowledge-base\\related-articles.tsx", ["1872", "1873"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\notifications\\multi-channel-notifications.tsx", ["1874", "1875", "1876"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\password-reset\\reset-dialog.tsx", ["1877", "1878"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\pc-admin-request-form.tsx", ["1879", "1880", "1881"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\pc-admin-requests-list.tsx", ["1882", "1883", "1884"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\request-dialog.tsx", ["1885", "1886"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\pc-admin\\request-list.tsx", ["1887", "1888"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\predictive\\predictive-field.tsx", ["1889", "1890"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\predictive\\predictive-suggestions.tsx", ["1891", "1892"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\ai-provider.tsx", ["1893", "1894"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\audit-provider.tsx", ["1895", "1896"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\providers\\supabase-provider.tsx", ["1897", "1898"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\rbac\\permission-gate.tsx", ["1899", "1900"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\rbac\\role-management.tsx", ["1901"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\index.ts", ["1902", "1903"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\realtime-dashboard.tsx", ["1904", "1905", "1906", "1907"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\realtime-notifications.tsx", ["1908", "1909", "1910"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\real-time\\request-status-tracker.tsx", ["1911", "1912"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\alert.tsx", ["1913", "1914"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\badge.tsx", ["1915", "1916"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\button.tsx", ["1917", "1918"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\calendar.tsx", ["1919", "1920"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\card.tsx", ["1921", "1922"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\checkbox.tsx", ["1923", "1924"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\command.tsx", ["1925", "1926"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\date-range-picker.tsx", ["1927", "1928"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\dialog.tsx", ["1929", "1930"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\dropdown-menu.tsx", ["1931", "1932"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\input.tsx", ["1933", "1934"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\japanese-date-picker.tsx", ["1935", "1936"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\label.tsx", ["1937", "1938"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\language-switcher.tsx", ["1939", "1940"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\loading.tsx", ["1941", "1942"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\popover.tsx", ["1943", "1944"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\progress.tsx", ["1945", "1946"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\scroll-area.tsx", ["1947", "1948"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\select.tsx", ["1949", "1950"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\separator.tsx", ["1951", "1952"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\skeleton.tsx", ["1953", "1954"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\switch.tsx", ["1955", "1956"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\table.tsx", ["1957", "1958"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\tabs.tsx", ["1959", "1960"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\textarea.tsx", ["1961", "1962"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\tooltip.tsx", ["1963", "1964"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\use-toast.ts", ["1965", "1966"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\ui\\user-multi-select.tsx", ["1967"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflow\\workflow-status-dashboard.tsx", ["1968"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\escalation\\escalation-manager.tsx", ["1969", "1970", "1971"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\escalation\\escalation-rule-form.tsx", ["1972", "1973"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\monitoring\\WorkflowMonitoringDashboard.tsx", ["1974", "1975", "1976"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\multi-level-approval.tsx", ["1977", "1978", "1979"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\sla-monitoring.tsx", ["1980"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\components\\workflows\\workflow-template-gallery.tsx", ["1981", "1982", "1983", "1984"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\agent-manager.ts", ["1985", "1986"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\base-agent-update.ts", ["1987"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\base-agent.ts", ["1988", "1989"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\mailbox-agent.ts", ["1990", "1991"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\pc-admin-agent.ts", ["1992", "1993"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\agents\\specialized-agents.ts", ["1994", "1995"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\ai-performance-optimizer.ts", ["1996", "1997"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\anthropic-client.ts", ["1998", "1999"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\content-filter.ts", ["2000", "2001"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\cost-analyzer.ts", ["2002", "2003"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\openai-client.ts", ["2004", "2005"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\reliability-manager.ts", ["2006", "2007"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai\\usage-tracker.ts", ["2008", "2009"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai-form-assistant-enhanced.ts", ["2010", "2011"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai-form-assistant-integrated.ts", ["2012", "2013"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\ai-form-assistant.ts", ["2014", "2015"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\api-documentation-page.tsx", ["2016", "2017"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\auth-documentation.tsx", ["2018", "2019"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\error-handler.ts", ["2020", "2021"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\openapi-documentation.ts", ["2022", "2023"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\redoc-ui.tsx", ["2024", "2025"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\api\\swagger-ui.tsx", ["2026", "2027"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth\\hooks.ts", ["2028", "2029"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth-context.tsx", ["2030", "2031"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\auth.ts", ["2032", "2033"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\backup\\backup-manager.ts", ["2034", "2035"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\batch-processing-types.ts", ["2036", "2037"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\cache\\cache-service.ts", ["2038", "2039"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\cache\\redis-client.ts", ["2040", "2041"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\config\\ai-config.ts", ["2042", "2043"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\config\\env-validator.ts", ["2044", "2045"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\database\\query-optimizer.ts", ["2046", "2047"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\database.types.enhanced.ts", ["2048", "2049"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\database.types.ts", ["2050", "2051"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\email\\password-reset.ts", ["2052", "2053"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\env.ts", ["2054", "2055"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\form-types.ts", ["2056", "2057"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\form-utils.ts", ["2058", "2059"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\historical-data-service.ts", ["2060", "2061"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-chatbot.ts", ["2062", "2063"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-content-gaps.ts", ["2064", "2065"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-debounce.ts", ["2066", "2067"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-embeddings.ts", ["2068", "2069"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-error-detection.ts", ["2070", "2071"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-mfa.ts", ["2072", "2073", "2074", "2075", "2076", "2077", "2078"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-nlp-form.ts", ["2079", "2080"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-predictive-form.ts", ["2081", "2082"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-real-time-validation-enhanced.ts", ["2083", "2084", "2085"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-real-time-validation.ts", ["2086", "2087", "2088"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-realtime.ts", ["2089", "2090"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\hooks\\use-toast.ts", ["2091", "2092"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\config.ts", ["2093", "2094"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\context.tsx", ["2095", "2096"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\language-provider.tsx", ["2097", "2098"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\mfa-translations.ts", ["2099", "2100"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\translations.ts", ["2101", "2102"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\i18n\\use-translation.ts", ["2103", "2104"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\jobs\\job-processor.ts", ["2105", "2106"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\LanguageContext.tsx", ["2107", "2108"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\middleware\\response-optimizer.ts", ["2109", "2110"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\middleware\\security-headers.ts", ["2111", "2112"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\middleware\\validate.ts", ["2113", "2114"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\distributed-tracing.ts", ["2115", "2116"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\metrics-dashboard.ts", ["2117", "2118"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\metrics.ts", ["2119", "2120"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\monitoring\\monitoring-integration.ts", ["2121", "2122"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\pc-admin-types.ts", ["2123", "2124"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\bundle-analyzer.ts", ["2125", "2126", "2127"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\connection-pooling.ts", ["2128", "2129"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\database-config.ts", ["2130", "2131"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\database-optimization.ts", ["2132", "2133"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\frontend-optimization.ts", ["2134"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\lazy-loading.tsx", ["2135", "2136", "2137", "2138"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\optimization-hooks.ts", ["2139", "2140", "2141", "2142"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\pool-manager.ts", ["2143", "2144"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\redis-cache.ts", ["2145", "2146"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\performance\\web-vitals-monitor.ts", ["2147", "2148"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\rbac-utils.ts", ["2149", "2150"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\request-types.ts", ["2151", "2152"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\security\\rate-limiter.ts", ["2153", "2154"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\ai-api-service.ts", ["2155", "2156"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\audit-log-service.ts", ["2157", "2158"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\audit.ts", ["2159", "2160"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\batch-processing-service.ts", ["2161", "2162"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\cache-service.ts", ["2163", "2164"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\chatbot-service.ts", ["2165", "2166"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\content-auto-update-service.ts", ["2167", "2168"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\data-encryption-service.ts", ["2169", "2170"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\embeddings-service.ts", ["2171", "2172"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\encryption-service.ts", ["2173", "2174"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\enhanced-audit-service.ts", ["2175", "2176"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\enhanced-realtime-service.ts", ["2177", "2178"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\error-detection-service.ts", ["2179", "2180"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\error-service.ts", ["2181", "2182"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\faq-generation-service.ts", ["2183", "2184"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\kb-chatbot-integration.ts", ["2185", "2186"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\kb-permissions-service.ts", ["2187", "2188"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\log-collector-service.ts", ["2189", "2190"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\mfa-service.ts", ["2191", "2192"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\nlp-form-service.ts", ["2193", "2194"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\notifications\\notification-service.ts", ["2195", "2196"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\pc-admin-service.ts", ["2197", "2198"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\performance\\utils.ts", ["2199", "2200"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\predictive-form-service.ts", ["2201", "2202"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\query-optimizer.ts", ["2203", "2204"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\realtime-service.ts", ["2205", "2206"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\secrets-manager.ts", ["2207", "2208"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\security-audit-service.ts", ["2209", "2210"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\web-scraping-service.ts", ["2211", "2212"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\approval-engine.ts", ["2213", "2214"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\escalation\\escalation-engine.ts", ["2215", "2216"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\escalation\\escalation-rules-manager.ts", ["2217", "2218"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\escalation-engine.ts", ["2219", "2220"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\notification-integration.ts", ["2221", "2222"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\rule-engine.ts", ["2223", "2224"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\sla-manager.ts", ["2225", "2226"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\state-machine.ts", ["2227", "2228"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-template-manager.ts", ["2229"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates\\workflow-templates.ts", ["2230", "2231"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\templates.ts", ["2232", "2233"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\types.ts", ["2234", "2235"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\workflow-engine.ts", ["2236", "2237"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\services\\workflow\\workflow-template-manager.ts", ["2238", "2239"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase\\client.ts", ["2240", "2241"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase\\index.ts", ["2242", "2243"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase\\server.ts", ["2244", "2245"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\supabase.ts", ["2246", "2247"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\test-utils\\legacy-adapters.ts", ["2248", "2249"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\types\\workflow.ts", ["2250", "2251"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\use-department-filter.ts", ["2252", "2253"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\use-permissions.ts", ["2254", "2255"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\utils.ts", ["2256", "2257"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\validation\\schemas.ts", ["2258", "2259"], [], "C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\lib\\validation.ts", ["2260", "2261"], [], {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2267", "line": 20, "column": 6, "nodeType": "2268", "endLine": 20, "endColumn": 17, "suggestions": "2269"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2270", "severity": 2, "message": "2271", "line": 23, "column": 26, "nodeType": "2272", "messageId": "2273", "suggestions": "2274"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2275", "line": 60, "column": 6, "nodeType": "2268", "endLine": 60, "endColumn": 50, "suggestions": "2276"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2275", "line": 60, "column": 6, "nodeType": "2268", "endLine": 60, "endColumn": 36, "suggestions": "2277"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2278", "line": 65, "column": 6, "nodeType": "2268", "endLine": 65, "endColumn": 8, "suggestions": "2279"}, {"ruleId": "2266", "severity": 1, "message": "2280", "line": 71, "column": 6, "nodeType": "2268", "endLine": 71, "endColumn": 25, "suggestions": "2281"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2282", "line": 61, "column": 34, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2283", "line": 51, "column": 11, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2284", "line": 60, "column": 6, "nodeType": "2268", "endLine": 60, "endColumn": 33, "suggestions": "2285"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2284", "line": 49, "column": 6, "nodeType": "2268", "endLine": 49, "endColumn": 33, "suggestions": "2286"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2287", "line": 23, "column": 6, "nodeType": "2268", "endLine": 23, "endColumn": 17, "suggestions": "2288"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2270", "severity": 2, "message": "2271", "line": 49, "column": 27, "nodeType": "2272", "messageId": "2273", "suggestions": "2289"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 58, "column": 25, "nodeType": "2272", "messageId": "2273", "suggestions": "2291"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 58, "column": 37, "nodeType": "2272", "messageId": "2273", "suggestions": "2292"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 74, "column": 28, "nodeType": "2272", "messageId": "2273", "suggestions": "2293"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 74, "column": 40, "nodeType": "2272", "messageId": "2273", "suggestions": "2294"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 75, "column": 28, "nodeType": "2272", "messageId": "2273", "suggestions": "2295"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 75, "column": 40, "nodeType": "2272", "messageId": "2273", "suggestions": "2296"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 76, "column": 28, "nodeType": "2272", "messageId": "2273", "suggestions": "2297"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 76, "column": 41, "nodeType": "2272", "messageId": "2273", "suggestions": "2298"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2282", "line": 134, "column": 4, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 132, "column": 55, "nodeType": "2272", "messageId": "2273", "suggestions": "2299"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 132, "column": 62, "nodeType": "2272", "messageId": "2273", "suggestions": "2300"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 133, "column": 49, "nodeType": "2272", "messageId": "2273", "suggestions": "2301"}, {"ruleId": "2270", "severity": 2, "message": "2290", "line": 133, "column": 59, "nodeType": "2272", "messageId": "2273", "suggestions": "2302"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2270", "severity": 2, "message": "2271", "line": 90, "column": 38, "nodeType": "2272", "messageId": "2273", "suggestions": "2303"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2304", "line": 112, "column": 6, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2304", "line": 1, "column": 14, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2305", "line": 54, "column": 9, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2306", "line": 73, "column": 6, "nodeType": "2268", "endLine": 73, "endColumn": 33, "suggestions": "2307"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2308", "line": 49, "column": 9, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2309", "severity": 2, "message": "2310", "line": 229, "column": 16, "nodeType": "2311", "messageId": "2312", "endLine": 229, "endColumn": 35}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2270", "severity": 2, "message": "2271", "line": 80, "column": 24, "nodeType": "2272", "messageId": "2273", "suggestions": "2313"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2314", "line": 46, "column": 6, "nodeType": "2268", "endLine": 46, "endColumn": 15, "suggestions": "2315"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2316", "line": 31, "column": 6, "nodeType": "2268", "endLine": 31, "endColumn": 23, "suggestions": "2317"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2318", "line": 26, "column": 6, "nodeType": "2268", "endLine": 26, "endColumn": 8, "suggestions": "2319"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2320", "line": 44, "column": 6, "nodeType": "2268", "endLine": 44, "endColumn": 13, "suggestions": "2321"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2322", "line": 37, "column": 6, "nodeType": "2268", "endLine": 37, "endColumn": 24, "suggestions": "2323"}, {"ruleId": "2266", "severity": 1, "message": "2324", "line": 41, "column": 6, "nodeType": "2268", "endLine": 41, "endColumn": 25, "suggestions": "2325"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2326", "line": 55, "column": 6, "nodeType": "2268", "endLine": 55, "endColumn": 25, "suggestions": "2327"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2328", "line": 59, "column": 6, "nodeType": "2268", "endLine": 59, "endColumn": 12, "suggestions": "2329"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2330", "line": 53, "column": 6, "nodeType": "2268", "endLine": 53, "endColumn": 8, "suggestions": "2331"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2332", "line": 60, "column": 6, "nodeType": "2268", "endLine": 60, "endColumn": 36, "suggestions": "2333"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2318", "line": 38, "column": 6, "nodeType": "2268", "endLine": 38, "endColumn": 8, "suggestions": "2334"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2335", "line": 74, "column": 6, "nodeType": "2268", "endLine": 74, "endColumn": 15, "suggestions": "2336"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2318", "line": 23, "column": 6, "nodeType": "2268", "endLine": 23, "endColumn": 8, "suggestions": "2337"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2338", "line": 77, "column": 6, "nodeType": "2268", "endLine": 77, "endColumn": 8, "suggestions": "2339"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2340", "line": 65, "column": 6, "nodeType": "2268", "endLine": 65, "endColumn": 18, "suggestions": "2341"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2342", "line": 51, "column": 6, "nodeType": "2268", "endLine": 51, "endColumn": 22, "suggestions": "2343"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2282", "line": 62, "column": 0, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2344", "line": 40, "column": 6, "nodeType": "2268", "endLine": 40, "endColumn": 8, "suggestions": "2345"}, {"ruleId": "2309", "severity": 2, "message": "2346", "line": 159, "column": 20, "nodeType": "2311", "messageId": "2312", "endLine": 159, "endColumn": 25}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2344", "line": 46, "column": 6, "nodeType": "2268", "endLine": 46, "endColumn": 8, "suggestions": "2347"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2348", "line": 65, "column": 16, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2282", "line": 109, "column": 0, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2349", "line": 61, "column": 6, "nodeType": "2268", "endLine": 61, "endColumn": 8, "suggestions": "2350"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2351", "line": 119, "column": 6, "nodeType": "2268", "endLine": 119, "endColumn": 49, "suggestions": "2352"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2353", "line": 71, "column": 6, "nodeType": "2268", "endLine": 71, "endColumn": 15, "suggestions": "2354"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2355", "line": 1, "column": 17, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2356", "line": 73, "column": 6, "nodeType": "2268", "endLine": 73, "endColumn": 25, "suggestions": "2357"}, {"ruleId": "2266", "severity": 1, "message": "2358", "line": 77, "column": 6, "nodeType": "2268", "endLine": 77, "endColumn": 62, "suggestions": "2359"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2304", "line": 2, "column": 0, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2360", "line": 18, "column": 9, "nodeType": "2361", "endLine": 18, "endColumn": 46}, {"ruleId": "2266", "severity": 1, "message": "2362", "line": 18, "column": 9, "nodeType": "2361", "endLine": 18, "endColumn": 46}, {"ruleId": "2266", "severity": 1, "message": "2363", "line": 18, "column": 9, "nodeType": "2361", "endLine": 18, "endColumn": 46}, {"ruleId": "2266", "severity": 1, "message": "2364", "line": 18, "column": 9, "nodeType": "2361", "endLine": 18, "endColumn": 46}, {"ruleId": "2266", "severity": 1, "message": "2365", "line": 18, "column": 9, "nodeType": "2361", "endLine": 18, "endColumn": 46}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2366", "line": 43, "column": 33, "nodeType": "2367", "endLine": 43, "endColumn": 40}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2366", "line": 43, "column": 33, "nodeType": "2367", "endLine": 43, "endColumn": 40}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2368", "severity": 2, "message": "2369", "line": 164, "column": 10, "nodeType": "2370", "endLine": 164, "endColumn": 22}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2371", "line": 11, "column": 24, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2372", "severity": 1, "message": "2373", "line": 147, "column": 9, "nodeType": "2374", "endLine": 153, "endColumn": 11}, {"ruleId": "2372", "severity": 1, "message": "2373", "line": 156, "column": 9, "nodeType": "2374", "endLine": 156, "endColumn": 66}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2266", "severity": 1, "message": "2375", "line": 202, "column": 6, "nodeType": "2367", "endLine": 202, "endColumn": 10}, {"ruleId": "2266", "severity": 1, "message": "2376", "line": 202, "column": 6, "nodeType": "2367", "endLine": 202, "endColumn": 10, "suggestions": "2377"}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2304", "line": 2, "column": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2262", "message": "2263", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "2264", "message": "2265", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, "@typescript-eslint/no-unused-vars", "Definition for rule '@typescript-eslint/no-unused-vars' was not found.", "@typescript-eslint/no-explicit-any", "Definition for rule '@typescript-eslint/no-explicit-any' was not found.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStatistics'. Either include it or remove the dependency array.", "ArrayExpression", ["2378"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["2379", "2380", "2381", "2382"], "React Hook useEffect has a missing dependency: 'fetchRequests'. Either include it or remove the dependency array.", ["2383"], ["2384"], "React Hook useEffect has a missing dependency: 'loadGroupMails'. Either include it or remove the dependency array.", ["2385"], "React Hook useEffect has a missing dependency: 'loadGroupMembers'. Either include it or remove the dependency array.", ["2386"], "Parsing error: '}' expected.", "Parsing error: JSX element 'TabsContent' has no corresponding closing tag.", "React Hook useEffect has a missing dependency: 'buildConfirmation'. Either include it or remove the dependency array.", ["2387"], ["2388"], "React Hook useEffect has a missing dependency: 'fetchRequestDetails'. Either include it or remove the dependency array.", ["2389"], ["2390", "2391", "2392", "2393"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["2394", "2395", "2396", "2397"], ["2398", "2399", "2400", "2401"], ["2402", "2403", "2404", "2405"], ["2406", "2407", "2408", "2409"], ["2410", "2411", "2412", "2413"], ["2414", "2415", "2416", "2417"], ["2418", "2419", "2420", "2421"], ["2422", "2423", "2424", "2425"], ["2426", "2427", "2428", "2429"], ["2430", "2431", "2432", "2433"], ["2434", "2435", "2436", "2437"], ["2438", "2439", "2440", "2441"], ["2442", "2443", "2444", "2445"], "Parsing error: Declaration or statement expected.", "Parsing error: ',' expected.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["2446"], "Parsing error: JSX element 'div' has no corresponding closing tag.", "react/jsx-no-undef", "'DatePickerWithRange' is not defined.", "JSXIdentifier", "undefined", ["2447", "2448", "2449", "2450"], "React Hook useEffect has missing dependencies: 'handleStatusUpdate' and 'loadBatchData'. Either include them or remove the dependency array.", ["2451"], "React Hook useEffect has a missing dependency: 'loadResources'. Either include it or remove the dependency array.", ["2452"], "React Hook useEffect has a missing dependency: 'loadCategories'. Either include it or remove the dependency array.", ["2453"], "React Hook useEffect has a missing dependency: 'loadServices'. Either include it or remove the dependency array.", ["2454"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["2455"], "React Hook useEffect has a missing dependency: 'filterUsers'. Either include it or remove the dependency array.", ["2456"], "React Hook useEffect has a missing dependency: 'fetchAgentData'. Either include it or remove the dependency array.", ["2457"], "React Hook useEffect has a missing dependency: 'loadArticle'. Either include it or remove the dependency array.", ["2458"], "React Hook useEffect has a missing dependency: 'fetchUpdateStatus'. Either include it or remove the dependency array.", ["2459"], "React Hook useEffect has a missing dependency: 'loadContent'. Either include it or remove the dependency array.", ["2460"], ["2461"], "React Hook useEffect has a missing dependency: 'loadContextualContent'. Either include it or remove the dependency array.", ["2462"], ["2463"], "React Hook useEffect has missing dependencies: 'loadNotificationHistory', 'loadPreferences', 'subscribeToNotifications', and 'supabase'. Either include them or remove the dependency array.", ["2464"], "React Hook useEffect has a missing dependency: 'searchPc'. Either include it or remove the dependency array.", ["2465"], "React Hook useEffect has a missing dependency: 'loadRequests'. Either include it or remove the dependency array.", ["2466"], "React Hook useEffect has a missing dependency: 'handleRequestUpdate'. Either include it or remove the dependency array.", ["2467"], "'Badge' is not defined.", ["2468"], "Parsing error: Invalid character.", "React Hook useEffect has a missing dependency: 'fetchRules'. Either include it or remove the dependency array.", ["2469"], "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["2470"], "React Hook useEffect has a missing dependency: 'fetchApprovalChainStatus'. Either include it or remove the dependency array.", ["2471"], "Parsing error: ';' expected.", "React Hook useEffect has a missing dependency: 'fetchTemplates'. Either include it or remove the dependency array.", ["2472"], "React Hook useEffect has a missing dependency: 'filterTemplates'. Either include it or remove the dependency array.", ["2473"], "The 'mfaService' object construction makes the dependencies of useCallback Hook (at line 27) change on every render. To fix this, wrap the initialization of 'mfaService' in its own useMemo() Hook.", "VariableDeclarator", "The 'mfaService' object construction makes the dependencies of useCallback Hook (at line 40) change on every render. To fix this, wrap the initialization of 'mfaService' in its own useMemo() Hook.", "The 'mfaService' object construction makes the dependencies of useCallback Hook (at line 53) change on every render. To fix this, wrap the initialization of 'mfaService' in its own useMemo() Hook.", "The 'mfaService' object construction makes the dependencies of useCallback Hook (at line 66) change on every render. To fix this, wrap the initialization of 'mfaService' in its own useMemo() Hook.", "The 'mfaService' object construction makes the dependencies of useCallback Hook (at line 79) change on every render. To fix this, wrap the initialization of 'mfaService' in its own useMemo() Hook.", "The ref value 'timeoutRefs.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'timeoutRefs.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "@next/next/no-assign-module-variable", "Do not assign to the variable `module`. See: https://nextjs.org/docs/messages/no-assign-module-variable", "VariableDeclaration", "Parsing error: '>' expected.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has a missing dependency: 'fetchFn'. Either include it or remove the dependency array. If 'fetchFn' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2474"], {"desc": "2475", "fix": "2476"}, {"messageId": "2477", "data": "2478", "fix": "2479", "desc": "2480"}, {"messageId": "2477", "data": "2481", "fix": "2482", "desc": "2483"}, {"messageId": "2477", "data": "2484", "fix": "2485", "desc": "2486"}, {"messageId": "2477", "data": "2487", "fix": "2488", "desc": "2489"}, {"desc": "2490", "fix": "2491"}, {"desc": "2492", "fix": "2493"}, {"desc": "2494", "fix": "2495"}, {"desc": "2496", "fix": "2497"}, {"desc": "2498", "fix": "2499"}, {"desc": "2498", "fix": "2500"}, {"desc": "2501", "fix": "2502"}, {"messageId": "2477", "data": "2503", "fix": "2504", "desc": "2480"}, {"messageId": "2477", "data": "2505", "fix": "2506", "desc": "2483"}, {"messageId": "2477", "data": "2507", "fix": "2508", "desc": "2486"}, {"messageId": "2477", "data": "2509", "fix": "2510", "desc": "2489"}, {"messageId": "2477", "data": "2511", "fix": "2512", "desc": "2513"}, {"messageId": "2477", "data": "2514", "fix": "2515", "desc": "2516"}, {"messageId": "2477", "data": "2517", "fix": "2518", "desc": "2519"}, {"messageId": "2477", "data": "2520", "fix": "2521", "desc": "2522"}, {"messageId": "2477", "data": "2523", "fix": "2524", "desc": "2513"}, {"messageId": "2477", "data": "2525", "fix": "2526", "desc": "2516"}, {"messageId": "2477", "data": "2527", "fix": "2528", "desc": "2519"}, {"messageId": "2477", "data": "2529", "fix": "2530", "desc": "2522"}, {"messageId": "2477", "data": "2531", "fix": "2532", "desc": "2513"}, {"messageId": "2477", "data": "2533", "fix": "2534", "desc": "2516"}, {"messageId": "2477", "data": "2535", "fix": "2536", "desc": "2519"}, {"messageId": "2477", "data": "2537", "fix": "2538", "desc": "2522"}, {"messageId": "2477", "data": "2539", "fix": "2540", "desc": "2513"}, {"messageId": "2477", "data": "2541", "fix": "2542", "desc": "2516"}, {"messageId": "2477", "data": "2543", "fix": "2544", "desc": "2519"}, {"messageId": "2477", "data": "2545", "fix": "2546", "desc": "2522"}, {"messageId": "2477", "data": "2547", "fix": "2548", "desc": "2513"}, {"messageId": "2477", "data": "2549", "fix": "2550", "desc": "2516"}, {"messageId": "2477", "data": "2551", "fix": "2552", "desc": "2519"}, {"messageId": "2477", "data": "2553", "fix": "2554", "desc": "2522"}, {"messageId": "2477", "data": "2555", "fix": "2556", "desc": "2513"}, {"messageId": "2477", "data": "2557", "fix": "2558", "desc": "2516"}, {"messageId": "2477", "data": "2559", "fix": "2560", "desc": "2519"}, {"messageId": "2477", "data": "2561", "fix": "2562", "desc": "2522"}, {"messageId": "2477", "data": "2563", "fix": "2564", "desc": "2513"}, {"messageId": "2477", "data": "2565", "fix": "2566", "desc": "2516"}, {"messageId": "2477", "data": "2567", "fix": "2568", "desc": "2519"}, {"messageId": "2477", "data": "2569", "fix": "2570", "desc": "2522"}, {"messageId": "2477", "data": "2571", "fix": "2572", "desc": "2513"}, {"messageId": "2477", "data": "2573", "fix": "2574", "desc": "2516"}, {"messageId": "2477", "data": "2575", "fix": "2576", "desc": "2519"}, {"messageId": "2477", "data": "2577", "fix": "2578", "desc": "2522"}, {"messageId": "2477", "data": "2579", "fix": "2580", "desc": "2513"}, {"messageId": "2477", "data": "2581", "fix": "2582", "desc": "2516"}, {"messageId": "2477", "data": "2583", "fix": "2584", "desc": "2519"}, {"messageId": "2477", "data": "2585", "fix": "2586", "desc": "2522"}, {"messageId": "2477", "data": "2587", "fix": "2588", "desc": "2513"}, {"messageId": "2477", "data": "2589", "fix": "2590", "desc": "2516"}, {"messageId": "2477", "data": "2591", "fix": "2592", "desc": "2519"}, {"messageId": "2477", "data": "2593", "fix": "2594", "desc": "2522"}, {"messageId": "2477", "data": "2595", "fix": "2596", "desc": "2513"}, {"messageId": "2477", "data": "2597", "fix": "2598", "desc": "2516"}, {"messageId": "2477", "data": "2599", "fix": "2600", "desc": "2519"}, {"messageId": "2477", "data": "2601", "fix": "2602", "desc": "2522"}, {"messageId": "2477", "data": "2603", "fix": "2604", "desc": "2513"}, {"messageId": "2477", "data": "2605", "fix": "2606", "desc": "2516"}, {"messageId": "2477", "data": "2607", "fix": "2608", "desc": "2519"}, {"messageId": "2477", "data": "2609", "fix": "2610", "desc": "2522"}, {"messageId": "2477", "data": "2611", "fix": "2612", "desc": "2480"}, {"messageId": "2477", "data": "2613", "fix": "2614", "desc": "2483"}, {"messageId": "2477", "data": "2615", "fix": "2616", "desc": "2486"}, {"messageId": "2477", "data": "2617", "fix": "2618", "desc": "2489"}, {"desc": "2619", "fix": "2620"}, {"messageId": "2477", "data": "2621", "fix": "2622", "desc": "2480"}, {"messageId": "2477", "data": "2623", "fix": "2624", "desc": "2483"}, {"messageId": "2477", "data": "2625", "fix": "2626", "desc": "2486"}, {"messageId": "2477", "data": "2627", "fix": "2628", "desc": "2489"}, {"desc": "2629", "fix": "2630"}, {"desc": "2631", "fix": "2632"}, {"desc": "2633", "fix": "2634"}, {"desc": "2635", "fix": "2636"}, {"desc": "2637", "fix": "2638"}, {"desc": "2639", "fix": "2640"}, {"desc": "2641", "fix": "2642"}, {"desc": "2643", "fix": "2644"}, {"desc": "2645", "fix": "2646"}, {"desc": "2647", "fix": "2648"}, {"desc": "2633", "fix": "2649"}, {"desc": "2650", "fix": "2651"}, {"desc": "2633", "fix": "2652"}, {"desc": "2653", "fix": "2654"}, {"desc": "2655", "fix": "2656"}, {"desc": "2657", "fix": "2658"}, {"desc": "2659", "fix": "2660"}, {"desc": "2659", "fix": "2661"}, {"desc": "2662", "fix": "2663"}, {"desc": "2664", "fix": "2665"}, {"desc": "2666", "fix": "2667"}, {"desc": "2668", "fix": "2669"}, {"desc": "2670", "fix": "2671"}, {"desc": "2672", "fix": "2673"}, "Update the dependencies array to be: [fetchStatistics, timeRange]", {"range": "2674", "text": "2675"}, "replaceWithAlt", {"alt": "2676"}, {"range": "2677", "text": "2678"}, "Replace with `&apos;`.", {"alt": "2679"}, {"range": "2680", "text": "2681"}, "Replace with `&lsquo;`.", {"alt": "2682"}, {"range": "2683", "text": "2684"}, "Replace with `&#39;`.", {"alt": "2685"}, {"range": "2686", "text": "2687"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [departmentId, fetchRequests, selectedStatus, selectedType]", {"range": "2688", "text": "2689"}, "Update the dependencies array to be: [departmentId, fetchRequests, selectedStatus]", {"range": "2690", "text": "2691"}, "Update the dependencies array to be: [loadGroupMails]", {"range": "2692", "text": "2693"}, "Update the dependencies array to be: [loadGroupMembers, selectedGroupMail]", {"range": "2694", "text": "2695"}, "Update the dependencies array to be: [buildConfirmation, currentStep, requestItems]", {"range": "2696", "text": "2697"}, {"range": "2698", "text": "2697"}, "Update the dependencies array to be: [fetchRequestDetails, requestId]", {"range": "2699", "text": "2700"}, {"alt": "2676"}, {"range": "2701", "text": "2702"}, {"alt": "2679"}, {"range": "2703", "text": "2704"}, {"alt": "2682"}, {"range": "2705", "text": "2706"}, {"alt": "2685"}, {"range": "2707", "text": "2708"}, {"alt": "2709"}, {"range": "2710", "text": "2711"}, "Replace with `&quot;`.", {"alt": "2712"}, {"range": "2713", "text": "2714"}, "Replace with `&ldquo;`.", {"alt": "2715"}, {"range": "2716", "text": "2717"}, "Replace with `&#34;`.", {"alt": "2718"}, {"range": "2719", "text": "2720"}, "Replace with `&rdquo;`.", {"alt": "2709"}, {"range": "2721", "text": "2722"}, {"alt": "2712"}, {"range": "2723", "text": "2724"}, {"alt": "2715"}, {"range": "2725", "text": "2726"}, {"alt": "2718"}, {"range": "2727", "text": "2728"}, {"alt": "2709"}, {"range": "2729", "text": "2730"}, {"alt": "2712"}, {"range": "2731", "text": "2732"}, {"alt": "2715"}, {"range": "2733", "text": "2734"}, {"alt": "2718"}, {"range": "2735", "text": "2736"}, {"alt": "2709"}, {"range": "2737", "text": "2738"}, {"alt": "2712"}, {"range": "2739", "text": "2740"}, {"alt": "2715"}, {"range": "2741", "text": "2742"}, {"alt": "2718"}, {"range": "2743", "text": "2744"}, {"alt": "2709"}, {"range": "2745", "text": "2746"}, {"alt": "2712"}, {"range": "2747", "text": "2748"}, {"alt": "2715"}, {"range": "2749", "text": "2750"}, {"alt": "2718"}, {"range": "2751", "text": "2752"}, {"alt": "2709"}, {"range": "2753", "text": "2754"}, {"alt": "2712"}, {"range": "2755", "text": "2756"}, {"alt": "2715"}, {"range": "2757", "text": "2758"}, {"alt": "2718"}, {"range": "2759", "text": "2760"}, {"alt": "2709"}, {"range": "2761", "text": "2762"}, {"alt": "2712"}, {"range": "2763", "text": "2764"}, {"alt": "2715"}, {"range": "2765", "text": "2766"}, {"alt": "2718"}, {"range": "2767", "text": "2768"}, {"alt": "2709"}, {"range": "2769", "text": "2770"}, {"alt": "2712"}, {"range": "2771", "text": "2772"}, {"alt": "2715"}, {"range": "2773", "text": "2774"}, {"alt": "2718"}, {"range": "2775", "text": "2776"}, {"alt": "2709"}, {"range": "2777", "text": "2778"}, {"alt": "2712"}, {"range": "2779", "text": "2780"}, {"alt": "2715"}, {"range": "2781", "text": "2782"}, {"alt": "2718"}, {"range": "2783", "text": "2784"}, {"alt": "2709"}, {"range": "2785", "text": "2786"}, {"alt": "2712"}, {"range": "2787", "text": "2788"}, {"alt": "2715"}, {"range": "2789", "text": "2790"}, {"alt": "2718"}, {"range": "2791", "text": "2792"}, {"alt": "2709"}, {"range": "2793", "text": "2794"}, {"alt": "2712"}, {"range": "2795", "text": "2796"}, {"alt": "2715"}, {"range": "2797", "text": "2798"}, {"alt": "2718"}, {"range": "2799", "text": "2800"}, {"alt": "2709"}, {"range": "2801", "text": "2802"}, {"alt": "2712"}, {"range": "2803", "text": "2804"}, {"alt": "2715"}, {"range": "2805", "text": "2806"}, {"alt": "2718"}, {"range": "2807", "text": "2808"}, {"alt": "2676"}, {"range": "2809", "text": "2810"}, {"alt": "2679"}, {"range": "2811", "text": "2812"}, {"alt": "2682"}, {"range": "2813", "text": "2814"}, {"alt": "2685"}, {"range": "2815", "text": "2816"}, "Update the dependencies array to be: [fetchDashboardData, organizationId, timeRange]", {"range": "2817", "text": "2818"}, {"alt": "2676"}, {"range": "2819", "text": "2820"}, {"alt": "2679"}, {"range": "2821", "text": "2822"}, {"alt": "2682"}, {"range": "2823", "text": "2824"}, {"alt": "2685"}, {"range": "2825", "text": "2826"}, "Update the dependencies array to be: [batchId, handleStatusUpdate, loadBatchData]", {"range": "2827", "text": "2828"}, "Update the dependencies array to be: [loadResources, serviceCategory]", {"range": "2829", "text": "2830"}, "Update the dependencies array to be: [loadCategories]", {"range": "2831", "text": "2832"}, "Update the dependencies array to be: [loadServices, staff]", {"range": "2833", "text": "2834"}, "Update the dependencies array to be: [departmentFilter, loadUsers]", {"range": "2835", "text": "2836"}, "Update the dependencies array to be: [users, searchTerm, filterUsers]", {"range": "2837", "text": "2838"}, "Update the dependencies array to be: [fetchAgentData, selectedTimeRange]", {"range": "2839", "text": "2840"}, "Update the dependencies array to be: [loadArticle, slug]", {"range": "2841", "text": "2842"}, "Update the dependencies array to be: [fetchUpdateStatus]", {"range": "2843", "text": "2844"}, "Update the dependencies array to be: [serviceCategory, currentStep, loadContent]", {"range": "2845", "text": "2846"}, {"range": "2847", "text": "2832"}, "Update the dependencies array to be: [context, loadContextualContent]", {"range": "2848", "text": "2849"}, {"range": "2850", "text": "2832"}, "Update the dependencies array to be: [loadNotificationHistory, loadPreferences, subscribeToNotifications, supabase]", {"range": "2851", "text": "2852"}, "Update the dependencies array to be: [searchPc, searchTerm]", {"range": "2853", "text": "2854"}, "Update the dependencies array to be: [staff, showAll, loadRequests]", {"range": "2855", "text": "2856"}, "Update the dependencies array to be: [handleRequestUpdate]", {"range": "2857", "text": "2858"}, {"range": "2859", "text": "2858"}, "Update the dependencies array to be: [fetchRules]", {"range": "2860", "text": "2861"}, "Update the dependencies array to be: [timeR<PERSON>e, statusFilter, departmentFilter, loadData]", {"range": "2862", "text": "2863"}, "Update the dependencies array to be: [chainId, fetchApprovalChainStatus]", {"range": "2864", "text": "2865"}, "Update the dependencies array to be: [fetchTemplates, serviceCategoryId]", {"range": "2866", "text": "2867"}, "Update the dependencies array to be: [templates, searchQuery, categoryFilter, priorityFilter, filterTemplates]", {"range": "2868", "text": "2869"}, "Update the dependencies array to be: [fetchFn]", {"range": "2870", "text": "2871"}, [943, 954], "[fetchStatistics, timeRange]", "&apos;", [857, 935], "\n                  You don&apos;t have permission to manage users.\n                ", "&lsquo;", [857, 935], "\n                  You don&lsquo;t have permission to manage users.\n                ", "&#39;", [857, 935], "\n                  You don&#39;t have permission to manage users.\n                ", "&rsquo;", [857, 935], "\n                  You don&rsquo;t have permission to manage users.\n                ", [2102, 2146], "[departmentId, fetchRequests, selectedStatus, selectedType]", [2019, 2049], "[departmentId, fetchRequests, selectedStatus]", [1959, 1961], "[loadGroupMails]", [2071, 2090], "[loadGroupMem<PERSON>, selectedGroupMail]", [2669, 2696], "[buildConfirmation, currentStep, requestItems]", [2290, 2317], [828, 839], "[fetchRequestDetails, requestId]", [1912, 1994], "\n              If you haven&apos;t already, create a free Supabase project\n            ", [1912, 1994], "\n              If you haven&lsquo;t already, create a free Supabase project\n            ", [1912, 1994], "\n              If you haven&#39;t already, create a free Supabase project\n            ", [1912, 1994], "\n              If you haven&rsquo;t already, create a free Supabase project\n            ", "&quot;", [2491, 2534], "Click &quot;New Project\" and fill in the details", "&ldquo;", [2491, 2534], "Click &ldquo;New Project\" and fill in the details", "&#34;", [2491, 2534], "Click &#34;New Project\" and fill in the details", "&rdquo;", [2491, 2534], "Click &rdquo;New Project\" and fill in the details", [2491, 2534], "Click \"New Project&quot; and fill in the details", [2491, 2534], "Click \"New Project&ldquo; and fill in the details", [2491, 2534], "Click \"New Project&#34; and fill in the details", [2491, 2534], "Click \"New Project&rdquo; and fill in the details", [3153, 3215], "Copy the &quot;Project URL\" - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the &ldquo;Project URL\" - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the &#34;Project URL\" - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the &rdquo;Project URL\" - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the \"Project URL&quot; - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the \"Project URL&ldquo; - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the \"Project URL&#34; - this is your NEXT_PUBLIC_SUPABASE_URL", [3153, 3215], "Copy the \"Project URL&rdquo; - this is your NEXT_PUBLIC_SUPABASE_URL", [3239, 3310], "Copy the &quot;anon public\" key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the &ldquo;anon public\" key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the &#34;anon public\" key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the &rdquo;anon public\" key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the \"anon public&quot; key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the \"anon public&ldquo; key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the \"anon public&#34; key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3239, 3310], "Copy the \"anon public&rdquo; key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY", [3334, 3402], "Copy the &quot;service_role\" key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the &ldquo;service_role\" key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the &#34;service_role\" key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the &rdquo;service_role\" key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the \"service_role&quot; key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the \"service_role&ldquo; key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the \"service_role&#34; key - this is your SUPABASE_SERVICE_ROLE_KEY", [3334, 3402], "Copy the \"service_role&rdquo; key - this is your SUPABASE_SERVICE_ROLE_KEY", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click &quot;Accept\" to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click &ldquo;Accept\" to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click &#34;Accept\" to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click &rdquo;Accept\" to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click \"Accept&quot; to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click \"Accept&ldquo; to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click \"Accept&#34; to apply AI suggestions", [4409, 4477], "• AIの提案がある場合は「採用」ボタンで適用できます / Click \"Accept&rdquo; to apply AI suggestions", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use &quot;Auto-fill\" to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use &ldquo;Auto-fill\" to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use &#34;Auto-fill\" to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use &rdquo;Auto-fill\" to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use \"Auto-fill&quot; to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use \"Auto-fill&ldquo; to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use \"Auto-fill&#34; to complete empty fields", [4501, 4567], "• 「自動入力」ボタンで空欄を自動補完できます / Use \"Auto-fill&rdquo; to complete empty fields", [3756, 3925], "\n                  Articles that haven&apos;t been updated in 30 days automatically enter the\n                  review queue to ensure content stays current.\n                ", [3756, 3925], "\n                  Articles that haven&lsquo;t been updated in 30 days automatically enter the\n                  review queue to ensure content stays current.\n                ", [3756, 3925], "\n                  Articles that haven&#39;t been updated in 30 days automatically enter the\n                  review queue to ensure content stays current.\n                ", [3756, 3925], "\n                  Articles that haven&rsquo;t been updated in 30 days automatically enter the\n                  review queue to ensure content stays current.\n                ", [1614, 1641], "[fetchDashboardData, organizationId, timeRange]", [2090, 2168], "\n                You don&apos;t have permission to access this page.\n              ", [2090, 2168], "\n                You don&lsquo;t have permission to access this page.\n              ", [2090, 2168], "\n                You don&#39;t have permission to access this page.\n              ", [2090, 2168], "\n                You don&rsquo;t have permission to access this page.\n              ", [1404, 1413], "[batchId, handleStatusUpdate, loadBatchData]", [894, 911], "[loadResources, serviceCategory]", [695, 697], "[loadCategories]", [1402, 1409], "[loadServices, staff]", [1282, 1300], "[departmentF<PERSON>er, loadUsers]", [1346, 1365], "[users, searchTerm, filterUsers]", [1670, 1689], "[fetchAgentData, selectedTimeRange]", [1722, 1728], "[loadArticle, slug]", [1527, 1529], "[fetchUpdateStatus]", [1480, 1510], "[serviceCategory, currentStep, loadContent]", [1360, 1362], [2137, 2146], "[context, loadContextualContent]", [689, 691], [2147, 2149], "[loadNotificationHistory, loadPreferences, subscribeToNotifications, supabase]", [2187, 2199], "[searchPc, searchTerm]", [1623, 1639], "[staff, showAll, loadRequests]", [1154, 1156], "[handleRequestUpdate]", [1432, 1434], [1532, 1534], "[fetchRules]", [2899, 2942], "[time<PERSON><PERSON><PERSON>, statusFilter, departmentFilter, loadData]", [2421, 2430], "[chainId, fetchApprovalChainStatus]", [1881, 1900], "[fetchTemplates, serviceCategoryId]", [1952, 2008], "[templates, searchQuery, categoryFilter, priorityFilter, filterTemplates]", [4543, 4547], "[fetchFn]"]