import { useCallback, useEffect, useMemo, useRef } from 'react'
import { debounce, throttle } from 'lodash'

/**
 * Debounce hook
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback)
  
  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  return useMemo(
    () => debounce((...args) => callbackRef.current(...args), delay) as T,
    [delay]
  )
}

/**
 * Throttle hook
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback)
  
  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  return useMemo(
    () => throttle((...args) => callbackRef.current(...args), delay) as T,
    [delay]
  )
}

/**
 * Virtual scroll hook for large lists
 */
export function useVirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    startIndex,
    endIndex,
  }
}

/**
 * Memo with deep comparison
 */
import { DependencyList, useRef as useRefReact } from 'react'
import isEqual from 'lodash/isEqual'

export function useDeepMemo<T>(
  factory: () => T,
  deps: DependencyList
): T {
  const ref = useRefReact<{ value: T; deps: DependencyList }>()

  if (!ref.current || !isEqual(deps, ref.current.deps)) {
    ref.current = { value: factory(), deps }
  }

  return ref.current.value
}

/**
 * Request animation frame hook
 */
export function useAnimationFrame(callback: (deltaTime: number) => void) {
  const requestRef = useRef<number>()
  const previousTimeRef = useRef<number>()

  const animate = useCallback(
    (time: number) => {
      if (previousTimeRef.current !== undefined) {
        const deltaTime = time - previousTimeRef.current
        callback(deltaTime)
      }
      previousTimeRef.current = time
      requestRef.current = requestAnimationFrame(animate)
    },
    [callback]
  )

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate)
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current)
      }
    }
  }, [animate])
}

/**
 * Web Worker hook
 */
export function useWebWorker<T, R>(
  workerFunction: (data: T) => R
): [(data: T) => Promise<R>, boolean] {
  const [loading, setLoading] = useState(false)
  const workerRef = useRef<Worker>()

  useEffect(() => {
    const workerCode = `
      self.addEventListener('message', async (e) => {
        const result = await (${workerFunction.toString()})(e.data)
        self.postMessage(result)
      })
    `
    const blob = new Blob([workerCode], { type: 'application/javascript' })
    const workerUrl = URL.createObjectURL(blob)
    workerRef.current = new Worker(workerUrl)

    return () => {
      workerRef.current?.terminate()
      URL.revokeObjectURL(workerUrl)
    }
  }, [workerFunction])

  const execute = useCallback(
    (data: T): Promise<R> => {
      return new Promise((resolve, reject) => {
        if (!workerRef.current) {
          reject(new Error('Worker not initialized'))
          return
        }

        setLoading(true)
        
        workerRef.current.onmessage = (e) => {
          setLoading(false)
          resolve(e.data)
        }
        
        workerRef.current.onerror = (error) => {
          setLoading(false)
          reject(error)
        }
        
        workerRef.current.postMessage(data)
      })
    },
    []
  )

  return [execute, loading]
}

/**
 * Prefetch data hook
 */
export function usePrefetch<T>(
  fetchFn: () => Promise<T>,
  deps: DependencyList = []
): void {
  useEffect(() => {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    
    // Prefetch the data
    fetchFn().catch(console.error)
    
    return () => {
      document.head.removeChild(link)
    }
  }, deps)
}

import { useState } from 'react'
