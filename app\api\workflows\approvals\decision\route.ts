import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { approvalEngine } from '@/lib/services/workflow/approval-engine';
import { auditService } from '@/lib/services/audit';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const { taskId, decision, comments, attachments } = await request.json();

    if (!taskId || !decision) {
      return NextResponse.json(
        { error: 'Task ID and decision are required' },
        { status: 400 }
      );
    }

    // Validate decision
    if (!['approve', 'reject', 'escalate'].includes(decision)) {
      return NextResponse.json(
        { error: 'Invalid decision' },
        { status: 400 }
      );
    }

    // Process the approval decision
    await approvalEngine.processApprovalDecision(
      {
        taskId,
        decision,
        comments,
        attachments,
      },
      user.id
    );

    // Log the action
    await auditService.logWorkflowAction(user.id, taskId, 'approval_decision', {
      decision,
      comments_provided: !!comments,
    });

    return NextResponse.json({
      success: true,
      message: 'Approval decision processed successfully',
    });
  } catch (error) {
    console.error('Error processing approval decision:', error);
    return NextResponse.json(
      { error: 'Failed to process approval decision' },
      { status: 500 }
    );
  }
}
