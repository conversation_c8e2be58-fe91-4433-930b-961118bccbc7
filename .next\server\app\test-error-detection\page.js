(()=>{var e={};e.id=9558,e.ids=[9558],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},39528:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(70260),a=t(28203),l=t(25155),n=t.n(l),o=t(67292),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(r,i);let c=["",{children:["test-error-detection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78426)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-error-detection\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-error-detection\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-error-detection/page",pathname:"/test-error-detection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},84126:(e,r,t)=>{Promise.resolve().then(t.bind(t,78426))},66822:(e,r,t)=>{Promise.resolve().then(t.bind(t,96))},96:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>D});var s=t(45512),a=t(58009),l=t.n(a),n=t(97643),o=t(25409),i=t(47699),c=t(4890),d=t(48859),u=t(87021),p=t(98755),m=t(16873),h=t(44269);function x({options:e,value:r,onChange:t,placeholder:l="選択してください",disabled:n=!1,className:i="",error:c}){let[d,x]=(0,a.useState)(!1),[f,g]=(0,a.useState)(""),[b,j]=(0,a.useState)(e),v=(0,a.useRef)(null),y=e.find(e=>e.value===r),N=e=>{t(e.value),x(!1),g("")},w=()=>{t(""),g("")};return(0,s.jsxs)("div",{ref:v,className:`relative ${i}`,children:[(0,s.jsx)("div",{className:`flex items-center border rounded-md ${c?"border-red-500":"border-gray-300"} ${n?"bg-gray-100 opacity-70":""}`,children:(0,s.jsxs)("div",{className:"flex-grow px-3 py-2 cursor-pointer flex items-center justify-between",onClick:()=>!n&&x(!d),children:[y?(0,s.jsx)("span",{children:y.label}):(0,s.jsx)("span",{className:"text-gray-400",children:l}),(0,s.jsxs)("div",{className:"flex items-center",children:[y&&(0,s.jsx)(u.$,{type:"button",variant:"ghost",size:"icon",className:"h-4 w-4 mr-1",onClick:e=>{e.stopPropagation(),w()},disabled:n,children:(0,s.jsx)(h.A,{className:"h-3 w-3"})}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-500"})]})]})}),c&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:c}),d&&!n&&(0,s.jsxs)("div",{className:"absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg",children:[(0,s.jsx)("div",{className:"p-2 border-b",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-gray-400"}),(0,s.jsx)(o.p,{type:"text",placeholder:"検索...",value:f,onChange:e=>g(e.target.value),className:"pl-8",autoFocus:!0})]})}),(0,s.jsx)("div",{className:"max-h-60 overflow-y-auto",children:0===b.length?(0,s.jsx)("div",{className:"p-3 text-center text-gray-500",children:"結果がありません"}):(0,s.jsx)("ul",{children:b.map(e=>(0,s.jsx)("li",{className:`px-3 py-2 cursor-pointer hover:bg-gray-100 ${e.value===r?"bg-blue-50 text-blue-600":""}`,onClick:()=>N(e),children:e.label},e.value))})})]})]})}var f=t(24849);function g({options:e,value:r=[],onChange:t,placeholder:l="選択してください",disabled:n=!1,className:i="",error:c,maxItems:d}){let[x,g]=(0,a.useState)(!1),[b,j]=(0,a.useState)(""),[v,y]=(0,a.useState)(e),N=(0,a.useRef)(null),w=e.filter(e=>r.includes(e.value)),_=e=>{if(r.includes(e.value))t(r.filter(r=>r!==e.value));else{if(d&&r.length>=d)return;t([...r,e.value])}},C=()=>{t([])};return(0,s.jsxs)("div",{ref:N,className:`relative ${i}`,children:[(0,s.jsx)("div",{className:`flex items-center border rounded-md ${c?"border-red-500":"border-gray-300"} ${n?"bg-gray-100 opacity-70":""}`,children:(0,s.jsxs)("div",{className:"flex-grow px-3 py-2 cursor-pointer flex items-center justify-between",onClick:()=>!n&&g(!x),children:[w.length>0?(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:w.map(e=>(0,s.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:[e.label,(0,s.jsx)(u.$,{type:"button",variant:"ghost",size:"icon",className:"h-4 w-4 ml-1",onClick:r=>{r.stopPropagation(),_(e)},disabled:n,children:(0,s.jsx)(h.A,{className:"h-3 w-3"})})]},e.value))}):(0,s.jsx)("span",{className:"text-gray-400",children:l}),(0,s.jsxs)("div",{className:"flex items-center",children:[w.length>0&&(0,s.jsx)(u.$,{type:"button",variant:"ghost",size:"icon",className:"h-4 w-4 mr-1",onClick:e=>{e.stopPropagation(),C()},disabled:n,children:(0,s.jsx)(h.A,{className:"h-3 w-3"})}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-500"})]})]})}),c&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:c}),x&&!n&&(0,s.jsxs)("div",{className:"absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg",children:[(0,s.jsx)("div",{className:"p-2 border-b",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-gray-400"}),(0,s.jsx)(o.p,{type:"text",placeholder:"検索...",value:b,onChange:e=>j(e.target.value),className:"pl-8",autoFocus:!0})]})}),(0,s.jsx)("div",{className:"max-h-60 overflow-y-auto",children:0===v.length?(0,s.jsx)("div",{className:"p-3 text-center text-gray-500",children:"結果がありません"}):(0,s.jsx)("ul",{children:v.map(e=>{let t=r.includes(e.value),a=d&&r.length>=d&&!t;return(0,s.jsxs)("li",{className:`px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between ${t?"bg-blue-50 text-blue-600":""} ${a?"opacity-50 cursor-not-allowed":""}`,onClick:()=>!a&&_(e),children:[(0,s.jsx)("span",{children:e.label}),t&&(0,s.jsx)(f.A,{className:"h-4 w-4"})]},e.value)})})}),d&&(0,s.jsxs)("div",{className:"p-2 text-xs text-gray-500 border-t",children:[r.length," / ",d," 選択済み"]})]})]})}var b=t(91274);class j{async detectErrors(e,r,t,s,a,l="ja"){let n=`${e}-${t}-${l}`;if(this.detectionCache.has(n))return this.detectionCache.get(n);try{let{data:o,error:i}=await this.supabase.functions.invoke("detect-form-errors",{body:{fieldName:e,fieldType:r,value:t,departmentId:s,context:a,language:l}});if(i)throw i;return this.detectionCache.set(n,o),setTimeout(()=>{this.detectionCache.delete(n)},3e5),o}catch(e){return console.error("Error detection failed:",e),{errors:[],hasErrors:!1}}}async getFieldErrorStats(e,r){let t=this.supabase.from("field_error_stats").select("*").eq("field_name",e);r&&t.eq("department_id",r);let{data:s,error:a}=await t.single();return a&&"PGRST116"!==a.code&&console.error("Failed to get field error stats:",a),s}async getCommonErrors(e,r){let t=this.supabase.from("error_patterns").select("*").eq("field_type",e).order("frequency",{ascending:!1}).limit(5);r&&t.or(`department_id.eq.${r},department_id.is.null`);let{data:s,error:a}=await t;return a?(console.error("Failed to get common errors:",a),[]):s||[]}async acceptCorrection(e,r){let{error:t}=await this.supabase.from("error_corrections").update({accepted_correction:r,correction_accepted:!0}).eq("id",e);if(t)throw console.error("Failed to accept correction:",t),t}async provideFeedback(e){let{error:r}=await this.supabase.from("error_correction_feedback").insert({correction_id:e.correctionId,feedback_type:e.feedbackType,user_feedback:e.userFeedback,improved_suggestion:e.improvedSuggestion});if(r)throw console.error("Failed to provide feedback:",r),r}async getAutocorrectSuggestions(e,r,t="ja"){let{data:s,error:a}=await this.supabase.from("autocorrect_mappings").select("*").eq("field_type",e).eq("language",t).ilike("incorrect_value",`%${r}%`).limit(3);return a?(console.error("Failed to get autocorrect suggestions:",a),[]):s||[]}async learnFromCorrection(e,r,t,s){let{error:a}=await this.supabase.from("autocorrect_mappings").upsert({field_type:e,incorrect_value:r,correct_value:t,usage_count:1},{onConflict:"incorrect_value,correct_value,field_type",ignoreDuplicates:!1});a&&console.error("Failed to learn from correction:",a),await this.supabase.rpc("increment_error_pattern_frequency",{p_field_type:e,p_error_type:s})}async getRealTimeSuggestions(e,r,t="ja"){if(r.length<2)return[];let{data:s,error:a}=await this.supabase.from("autocorrect_mappings").select("correct_value").eq("field_type",e).eq("language",t).ilike("correct_value",`${r}%`).order("usage_count",{ascending:!1}).limit(5);return a?(console.error("Failed to get real-time suggestions:",a),[]):s?.map(e=>e.correct_value)||[]}constructor(){this.supabase=(0,b.createClientComponentClient)(),this.detectionCache=new Map}}let v=new j;!function(){var e=Error("Cannot find module '@/lib/hooks/use-debounce'");throw e.code="MODULE_NOT_FOUND",e}();var y=t(67418),N=t(61594),w=t(86201),_=t(59462);function C({errors:e,isChecking:r,onAcceptSuggestion:t,className:a}){if(r)return(0,s.jsxs)("div",{className:(0,_.cn)("flex items-center gap-2 text-sm text-muted-foreground",a),children:[(0,s.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"}),(0,s.jsx)("span",{children:"検証中..."})]});if(0===e.length)return null;e.reduce((e,r)=>r.confidence>e.confidence?r:e);let l=e=>e>=.8?(0,s.jsx)(y.A,{className:"h-4 w-4 text-red-500"}):e>=.5?(0,s.jsx)(N.A,{className:"h-4 w-4 text-yellow-500"}):(0,s.jsx)(w.A,{className:"h-4 w-4 text-blue-500"});return(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsx)("div",{className:(0,_.cn)("space-y-2",a),children:e.map((e,r)=>(0,s.jsxs)("div",{className:(0,_.cn)("flex items-start gap-2 p-2 rounded-md",e.confidence>=.8?"bg-red-50 dark:bg-red-900/20":e.confidence>=.5?"bg-yellow-50 dark:bg-yellow-900/20":"bg-blue-50 dark:bg-blue-900/20"),children:[l(e.confidence),(0,s.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:e.message}),e.suggestions.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.suggestions.map((e,r)=>(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,s.jsx)(u.$,{variant:"outline",size:"sm",className:"h-6 text-xs",onClick:()=>t?.(e),children:e})}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsx)("p",{children:"クリックして適用"})})]},r))})]}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[Math.round(100*e.confidence),"%"]})]},r))})})}function k({children:e,errors:r,isChecking:t,hasErrors:a,errorRate:n=0,showStats:o=!1}){let i=a?r[0].confidence>=.8?"border-red-500":"border-yellow-500":"border-input";return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"relative",children:[l().cloneElement(e,{className:(0,_.cn)(e.props.className,i,a&&"animate-pulse-once")}),(a||t)&&(0,s.jsx)("div",{className:"absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none",children:t?(0,s.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"}):a?r[0].confidence>=.8?(0,s.jsx)(y.A,{className:"h-4 w-4 text-red-500"}):(0,s.jsx)(N.A,{className:"h-4 w-4 text-yellow-500"}):null})]}),o&&n>0&&(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["このフィールドのエラー率: ",n.toFixed(1),"%"]})]})}function q({field:e,value:r,onChange:t,error:l,departmentId:n,language:p="ja"}){let{setValue:m,errors:h,isChecking:f,suggestions:b,hasErrors:j,fieldStats:y,acceptCorrection:N,checkErrorsManually:w}=function({fieldName:e,fieldType:r,departmentId:t,context:s,language:l="ja",enableRealTime:n=!0,debounceMs:o=500}){let[i,c]=(0,a.useState)(""),[d,u]=(0,a.useState)([]),[p,m]=(0,a.useState)(!1),[h,x]=(0,a.useState)([]),[f,g]=(0,a.useState)(null);Object(function(){var e=Error("Cannot find module '@/lib/hooks/use-debounce'");throw e.code="MODULE_NOT_FOUND",e}())(i,o);let b=(0,a.useCallback)(async a=>{m(!0);try{let n=await v.detectErrors(e,r,a,t,s,l);return u(n.errors),n}catch(e){return console.error("Error detection failed:",e),{errors:[],hasErrors:!1}}finally{m(!1)}},[e,r,t,s,l]),j=(0,a.useCallback)(async(e,r)=>{await v.acceptCorrection(e,r),c(r),u([])},[]),y=(0,a.useCallback)(async(e,r,t,s)=>{await v.provideFeedback({correctionId:e,feedbackType:r,userFeedback:t,improvedSuggestion:s})},[]),N=(0,a.useCallback)(async(e,t,s)=>{await v.learnFromCorrection(r,e,t,s)},[r]);return{value:i,setValue:c,errors:d,isChecking:p,suggestions:h,fieldStats:f,hasErrors:d.length>0,checkErrorsManually:b,acceptCorrection:j,provideFeedback:y,learnFromCorrection:N,bestSuggestion:d.length>0?d[0].suggestions[0]:null,errorRate:f?.error_rate||0}}({fieldName:e.name,fieldType:e.type,departmentId:n,language:p,enableRealTime:!0}),_=e=>{t(e),m(e)},q=()=>{switch(e.type){case"text":case"email":case"staff_id":case"pc_id":return(0,s.jsx)(o.p,{id:e.name,type:"email"===e.type?"email":"text",value:r||"",onChange:e=>t(e.target.value),placeholder:e.placeholder,disabled:e.disabled});case"textarea":return(0,s.jsx)(d.T,{id:e.name,value:r||"",onChange:e=>t(e.target.value),placeholder:e.placeholder,disabled:e.disabled,rows:4});case"select":return(0,s.jsxs)(c.l6,{value:r||"",onValueChange:t,disabled:e.disabled,children:[(0,s.jsx)(c.bq,{id:e.name,children:(0,s.jsx)(c.yv,{placeholder:e.placeholder||"選択してください"})}),(0,s.jsx)(c.gC,{children:e.options?.map(e=>s.jsx(c.eb,{value:e.value,children:e.label},e.value))})]});case"search":return(0,s.jsx)(x,{value:r,onChange:t,placeholder:e.placeholder,disabled:e.disabled,source:e.source,departmentId:n});case"multiselect":return(0,s.jsx)(g,{value:r||[],onChange:t,placeholder:e.placeholder,disabled:e.disabled,options:e.options||[]});default:return(0,s.jsx)(o.p,{id:e.name,value:r||"",onChange:e=>t(e.target.value),placeholder:e.placeholder,disabled:e.disabled})}},E=["text","email","staff_id","pc_id","textarea"].includes(e.type);return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(i.J,{htmlFor:e.name,className:"text-sm font-medium",children:[e.labelJp||e.label,e.required&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),E?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k,{errors:h,isChecking:f,hasErrors:j,errorRate:y?.error_rate,showStats:!1,children:q()}),(0,s.jsx)(C,{errors:h,isChecking:f,onAcceptSuggestion:_}),b.length>0&&!j&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"候補:"}),b.map((e,r)=>(0,s.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-6 text-xs",onClick:()=>_(e),children:e},r))]})]}):q(),l&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:l})]})}!function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}();var E=t(46583);function D(){let[e,r]=(0,a.useState)({email:"",staffId:"",pcId:"",name_jp:"",department:""}),t=(e,t)=>{r(r=>({...r,[e]:t}))};return(0,s.jsx)("div",{className:"container mx-auto py-10 max-w-4xl",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"AI-Powered Error Detection Test"}),(0,s.jsx)(n.BT,{children:"Test the error detection and correction features with common IT Helpdesk form fields"})]}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Form submitted with data:",e)},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,s.jsxs)("h3",{className:"font-semibold mb-2 flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4"}),"Test Instructions"]}),(0,s.jsxs)("ul",{className:"text-sm space-y-1 text-muted-foreground",children:[(0,s.jsx)("li",{children:"• Try entering personal emails (gmail.com) to see domain correction"}),(0,s.jsx)("li",{children:"• Enter staff ID without leading zeros (e.g., R123)"}),(0,s.jsx)("li",{children:"• Type PC ID without proper format (e.g., 241234)"}),(0,s.jsx)("li",{children:"• Enter Japanese names without spaces"}),(0,s.jsx)("li",{children:"• Use department abbreviations (e.g., HR, IT)"})]})]}),[{name:"email",type:"email",label:"Email Address",labelJp:"メールアドレス",required:!0,placeholder:"<EMAIL>"},{name:"staffId",type:"staff_id",label:"Staff ID",labelJp:"スタッフID",required:!0,placeholder:"R000123"},{name:"pcId",type:"pc_id",label:"PC ID",labelJp:"PC ID",required:!0,placeholder:"M241234"},{name:"name_jp",type:"text",label:"Name (Japanese)",labelJp:"氏名（日本語）",required:!0,placeholder:"山田 太郎"},{name:"department",type:"text",label:"Department",labelJp:"部署",required:!0,placeholder:"人事部"}].map(r=>(0,s.jsx)(q,{field:r,value:e[r.name],onChange:e=>t(r.name,e),language:"ja"},r.name))]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>r({email:"",staffId:"",pcId:"",name_jp:"",department:""}),children:"Reset"}),(0,s.jsxs)(u.$,{type:"submit",children:[(0,s.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Submit Form"]})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-8 border-t",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",children:"Form Data Preview"}),(0,s.jsx)("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto",children:JSON.stringify(e,null,2)})]})]})]})})}},78426:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-error-detection\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-error-detection\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>t(39528));module.exports=s})();