'use client';

import { useState, useEffect, useCallback } from 'react';
import { Calendar, Filter, Download, Search, AlertCircle, Info, AlertTriangle, XCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import { auditService, AuditLogEntry, AuditSearchParams, AuditEventType, AuditSeverity } from '@/lib/services/enhanced-audit-service';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

interface AuditLogViewerProps {
  departmentId?: string;
  entityType?: string;
  entityId?: string;
  showExport?: boolean;
  showFilters?: boolean;
  maxHeight?: string;
}

const severityIcons = {
  INFO: <Info className="h-4 w-4 text-blue-500" />,
  WARNING: <AlertTriangle className="h-4 w-4 text-yellow-500" />,
  ERROR: <XCircle className="h-4 w-4 text-red-500" />,
  CRITICAL: <AlertCircle className="h-4 w-4 text-red-700" />
};

const severityColors = {
  INFO: 'bg-blue-100 text-blue-800',
  WARNING: 'bg-yellow-100 text-yellow-800',
  ERROR: 'bg-red-100 text-red-800',
  CRITICAL: 'bg-red-200 text-red-900'
};

export function AuditLogViewer({
  departmentId,
  entityType,
  entityId,
  showExport = true,
  showFilters = true,
  maxHeight = '600px'
}: AuditLogViewerProps) {
  const [logs, setLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [selectedEventTypes, setSelectedEventTypes] = useState<AuditEventType[]>([]);
  const [selectedSeverities, setSelectedSeverities] = useState<AuditSeverity[]>([]);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [currentPage, setCurrentPage] = useState(0);
  const [exportFormat, setExportFormat] = useState<'CSV' | 'JSON' | 'PDF' | 'XLSX'>('CSV');
  const pageSize = 50;

  // Event type categories for filtering
  const eventTypeCategories = {
    'User Events': [
      'USER_LOGIN', 'USER_LOGOUT', 'USER_CREATED', 'USER_UPDATED', 'USER_DELETED',
      'PASSWORD_CHANGED', 'PASSWORD_RESET', 'ROLE_ASSIGNED', 'ROLE_REMOVED'
    ],
    'Request Events': [
      'REQUEST_CREATED', 'REQUEST_UPDATED', 'REQUEST_DELETED',
      'REQUEST_APPROVED', 'REQUEST_REJECTED', 'REQUEST_COMPLETED', 'REQUEST_CANCELLED'
    ],
    'Data Events': [
      'DATA_ACCESSED', 'DATA_EXPORTED', 'DATA_IMPORTED',
      'PERMISSION_GRANTED', 'PERMISSION_REVOKED',
      'CONFIGURATION_CHANGED', 'SETTINGS_UPDATED'
    ],
    'System Events': [
      'SYSTEM_ERROR', 'SYSTEM_WARNING', 'SYSTEM_INFO',
      'INTEGRATION_SUCCESS', 'INTEGRATION_FAILURE',
      'SCHEDULED_TASK_RUN', 'SCHEDULED_TASK_FAILED'
    ]
  };

  const fetchLogs = useCallback(async () => {
    setLoading(true);
    try {
      const params: AuditSearchParams = {
        limit: pageSize,
        offset: currentPage * pageSize,
        search_text: searchText || undefined,
        event_types: selectedEventTypes.length > 0 ? selectedEventTypes : undefined,
        severity: selectedSeverities.length > 0 ? selectedSeverities : undefined,
        start_date: dateRange?.from,
        end_date: dateRange?.to,
        department_id: departmentId,
        entity_type: entityType,
        entity_id: entityId
      };

      const { data, count, error } = await auditService.search(params);
      
      if (error) {
        toast({
          title: 'Error loading audit logs',
          description: error.message,
          variant: 'destructive'
        });
      } else {
        setLogs(data);
        setTotalCount(count);
      }
    } catch (error) {
      toast({
        title: 'Error loading audit logs',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchText, selectedEventTypes, selectedSeverities, dateRange, departmentId, entityType, entityId]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  const handleExport = async () => {
    try {
      const params: AuditSearchParams = {
        search_text: searchText || undefined,
        event_types: selectedEventTypes.length > 0 ? selectedEventTypes : undefined,
        severity: selectedSeverities.length > 0 ? selectedSeverities : undefined,
        start_date: dateRange?.from,
        end_date: dateRange?.to,
        department_id: departmentId,
        entity_type: entityType,
        entity_id: entityId
      };

      const { url, error } = await auditService.exportLogs(params, exportFormat);
      
      if (error) {
        toast({
          title: 'Export failed',
          description: error.message,
          variant: 'destructive'
        });
      } else {
        // Download the exported file
        window.open(url, '_blank');
        toast({
          title: 'Export successful',
          description: `Audit logs exported as ${exportFormat}`
        });
      }
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    }
  };

  const formatLogDetails = (log: AuditLogEntry) => {
    const details: string[] = [];
    
    if (log.entity_type && log.entity_name) {
      details.push(`${log.entity_type}: ${log.entity_name}`);
    }
    
    if (log.department_name) {
      details.push(`Department: ${log.department_name}`);
    }
    
    if (log.role_name) {
      details.push(`Role: ${log.role_name}`);
    }
    
    return details.join(' • ');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl">Audit Trail</CardTitle>
            <CardDescription>
              System activity and compliance logging
            </CardDescription>
          </div>
          {showExport && (
            <div className="flex items-center gap-2">
              <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CSV">CSV</SelectItem>
                  <SelectItem value="JSON">JSON</SelectItem>
                  <SelectItem value="PDF">PDF</SelectItem>
                  <SelectItem value="XLSX">Excel</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleExport} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {showFilters && (
          <div className="space-y-4 mb-6">
            {/* Search and date range */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search logs..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <DatePickerWithRange
                date={dateRange}
                onDateChange={setDateRange}
                className="w-full sm:w-auto"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              {/* Severity filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Severity</label>
                <div className="flex gap-2">
                  {(['INFO', 'WARNING', 'ERROR', 'CRITICAL'] as AuditSeverity[]).map(severity => (
                    <label key={severity} className="flex items-center gap-2 cursor-pointer">
                      <Checkbox
                        checked={selectedSeverities.includes(severity)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedSeverities([...selectedSeverities, severity]);
                          } else {
                            setSelectedSeverities(selectedSeverities.filter(s => s !== severity));
                          }
                        }}
                      />
                      <Badge className={severityColors[severity]}>
                        {severityIcons[severity]}
                        <span className="ml-1">{severity}</span>
                      </Badge>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Event type filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Event Types</label>
              <Tabs defaultValue="all" className="w-full">
                <TabsList>
                  <TabsTrigger value="all" onClick={() => setSelectedEventTypes([])}>
                    All Events
                  </TabsTrigger>
                  {Object.keys(eventTypeCategories).map(category => (
                    <TabsTrigger key={category} value={category}>
                      {category}
                    </TabsTrigger>
                  ))}
                </TabsList>
                {Object.entries(eventTypeCategories).map(([category, types]) => (
                  <TabsContent key={category} value={category} className="mt-2">
                    <div className="flex flex-wrap gap-2">
                      {types.map(type => (
                        <label key={type} className="flex items-center gap-2 cursor-pointer">
                          <Checkbox
                            checked={selectedEventTypes.includes(type as AuditEventType)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedEventTypes([...selectedEventTypes, type as AuditEventType]);
                              } else {
                                setSelectedEventTypes(selectedEventTypes.filter(t => t !== type));
                              }
                            }}
                          />
                          <span className="text-sm">{type.replace(/_/g, ' ')}</span>
                        </label>
                      ))}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </div>
        )}

        {/* Logs table */}
        <ScrollArea className="w-full" style={{ height: maxHeight }}>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[180px]">Timestamp</TableHead>
                <TableHead className="w-[100px]">Severity</TableHead>
                <TableHead className="w-[200px]">Event</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Details</TableHead>
                <TableHead className="w-[150px]">User</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    Loading audit logs...
                  </TableCell>
                </TableRow>
              ) : logs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    No audit logs found
                  </TableCell>
                </TableRow>
              ) : (
                logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="font-mono text-sm">
                      {format(new Date(log.created_at!), 'yyyy-MM-dd HH:mm:ss')}
                    </TableCell>
                    <TableCell>
                      <Badge className={severityColors[log.severity]}>
                        {severityIcons[log.severity]}
                        <span className="ml-1">{log.severity}</span>
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {log.event_type.replace(/_/g, ' ')}
                    </TableCell>
                    <TableCell>{log.action}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {log.description || formatLogDetails(log)}
                    </TableCell>
                    <TableCell className="text-sm">
                      {log.role_name || 'System'}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </ScrollArea>

        {/* Pagination */}
        {totalCount > pageSize && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalCount)} of {totalCount} entries
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={(currentPage + 1) * pageSize >= totalCount}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}