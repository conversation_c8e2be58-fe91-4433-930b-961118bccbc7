# Multi-Factor Authentication (MFA) System Design

## Overview
This document outlines the design for implementing Multi-Factor Authentication in the ITSync platform using Supabase Auth and additional security measures.

## Architecture

### 1. MFA Methods Supported
1. **TOTP (Time-based One-Time Password)** - Primary method
   - Google Authenticator
   - Microsoft Authenticator
   - Authy
   
2. **SMS OTP** - Backup method
   - Via Twilio integration
   
3. **Email OTP** - Backup method
   - Via existing email service

## Database Schema

```sql
-- MFA Configuration Table
CREATE TABLE mfa_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  method VARCHAR(20) NOT NULL CHECK (method IN ('totp', 'sms', 'email')),
  is_primary BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  secret_encrypted TEXT, -- For TOTP
  phone_number TEXT, -- For SMS
  backup_codes TEXT[], -- Encrypted backup codes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE
);

-- MFA Sessions Table
CREATE TABLE mfa_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MFA Audit Log
CREATE TABLE mfa_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  event_type VARCHAR(50) NOT NULL,
  method VARCHAR(20),
  success BOOLEAN NOT NULL,
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_mfa_config_user ON mfa_configurations(user_id);
CREATE INDEX idx_mfa_sessions_token ON mfa_sessions(session_token);
CREATE INDEX idx_mfa_audit_user ON mfa_audit_logs(user_id);
CREATE INDEX idx_mfa_audit_created ON mfa_audit_logs(created_at);
```

## User Flow

### 1. MFA Enrollment Flow
```mermaid
graph TD
    A[User Login] --> B{MFA Enabled?}
    B -->|No| C[Prompt MFA Setup]
    B -->|Yes| D[Request MFA Code]
    C --> E[Choose MFA Method]
    E --> F[TOTP Setup]
    E --> G[SMS Setup]
    E --> H[Email Setup]
    F --> I[Generate QR Code]
    G --> J[Verify Phone]
    H --> K[Verify Email]
    I --> L[Verify Code]
    J --> L
    K --> L
    L --> M[Generate Backup Codes]
    M --> N[MFA Enabled]
```

### 2. MFA Authentication Flow
```mermaid
graph TD
    A[Username/Password] --> B[Valid Credentials?]
    B -->|No| C[Login Failed]
    B -->|Yes| D[Check MFA Status]
    D --> E{MFA Required?}
    E -->|No| F[Login Success]
    E -->|Yes| G[Request MFA Code]
    G --> H[User Enters Code]
    H --> I{Valid Code?}
    I -->|No| J[Retry/Fail]
    I -->|Yes| K[Login Success]
```

## Implementation Components

### 1. Frontend Components

```typescript
// components/auth/mfa-setup.tsx
interface MFASetupProps {
  userId: string;
  onComplete: () => void;
}

// components/auth/mfa-verify.tsx
interface MFAVerifyProps {
  method: 'totp' | 'sms' | 'email';
  onSuccess: () => void;
  onCancel: () => void;
}

// components/auth/backup-codes.tsx
interface BackupCodesProps {
  codes: string[];
  onConfirm: () => void;
}
```

### 2. API Endpoints

```typescript
// MFA Setup Endpoints
POST   /api/auth/mfa/setup/totp     - Initialize TOTP setup
POST   /api/auth/mfa/setup/sms      - Initialize SMS setup
POST   /api/auth/mfa/setup/email    - Initialize Email setup
POST   /api/auth/mfa/verify-setup   - Verify MFA setup
GET    /api/auth/mfa/backup-codes   - Get backup codes
POST   /api/auth/mfa/regenerate-codes - Regenerate backup codes

// MFA Authentication Endpoints
POST   /api/auth/mfa/challenge      - Request MFA challenge
POST   /api/auth/mfa/verify         - Verify MFA code
POST   /api/auth/mfa/verify-backup  - Verify backup code

// MFA Management Endpoints
GET    /api/auth/mfa/status         - Get MFA status
DELETE /api/auth/mfa/disable        - Disable MFA (requires verification)
PUT    /api/auth/mfa/update-method  - Update MFA method
```

### 3. Security Considerations

1. **Rate Limiting**
   - Max 5 verification attempts per 15 minutes
   - Progressive delays after failed attempts
   
2. **Code Generation**
   - TOTP: 30-second window, 6-digit codes
   - SMS/Email: 6-digit codes, 10-minute expiry
   - Backup codes: 10 codes, 8 characters each
   
3. **Storage Security**
   - TOTP secrets encrypted with AES-256
   - Backup codes hashed with bcrypt
   - Phone numbers partially masked in UI
   
4. **Session Management**
   - MFA session valid for 12 hours
   - Re-authentication required for sensitive operations
   - Device trust option for 30 days

## Integration with Existing Auth

### 1. Middleware Enhancement
```typescript
// middleware/auth-mfa.ts
export async function requireMFA(req: Request) {
  const session = await getSession(req);
  
  if (!session) {
    return redirectToLogin();
  }
  
  const mfaStatus = await checkMFAStatus(session.userId);
  
  if (mfaStatus.required && !mfaStatus.verified) {
    return redirectToMFAChallenge();
  }
  
  return session;
}
```

### 2. Supabase Auth Hook
```typescript
// hooks/use-mfa-auth.ts
export function useMFAAuth() {
  const { user } = useAuth();
  const [mfaRequired, setMFARequired] = useState(false);
  const [mfaVerified, setMFAVerified] = useState(false);
  
  // MFA verification logic
  const verifyMFA = async (code: string, method: string) => {
    // Implementation
  };
  
  return { mfaRequired, mfaVerified, verifyMFA };
}
```

## UI/UX Considerations

### 1. Japanese Language Support
- All MFA prompts in Japanese and English
- Cultural considerations for phone number formats
- Clear instructions with visual aids

### 2. Accessibility
- Screen reader friendly QR codes
- Alternative text descriptions
- Keyboard navigation support
- High contrast mode support

### 3. Mobile Responsiveness
- QR code sizing for mobile screens
- Touch-friendly input fields
- SMS auto-fill support

## Testing Strategy

### 1. Unit Tests
- Code generation algorithms
- Encryption/decryption functions
- Rate limiting logic

### 2. Integration Tests
- Full enrollment flow
- Authentication flow with various methods
- Backup code recovery

### 3. Security Tests
- Brute force protection
- Session hijacking prevention
- Timing attack resistance

## Rollout Plan

### Phase 1: Internal Testing (Week 1)
- Deploy to test environment
- Internal team testing
- Bug fixes and refinements

### Phase 2: Opt-in Beta (Week 2)
- Enable for volunteer users
- Gather feedback
- Performance monitoring

### Phase 3: Gradual Rollout (Week 3)
- 10% of users
- 50% of users
- 100% of users

### Phase 4: Mandatory Enforcement (Week 4)
- Require for admin accounts
- Require for all accounts
- Deprecate legacy auth

## Monitoring & Metrics

### Key Metrics
1. MFA adoption rate
2. Authentication success rate
3. Average setup time
4. Support ticket volume
5. Method preference distribution

### Alerts
1. High failure rate (>10%)
2. Unusual geographic access
3. Brute force attempts
4. System performance degradation

## Compliance Checklist
- [ ] GDPR: User consent for phone numbers
- [ ] CCPA: Data retention policies
- [ ] APPI: Japanese privacy requirements
- [ ] SOC 2: Access control requirements
- [ ] ISO 27001: Authentication standards
