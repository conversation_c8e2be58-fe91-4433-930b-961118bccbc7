'use client'

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/lib/LanguageContext'
import { ChevronRight } from 'lucide-react'

interface CategoryFilterProps {
  categories: any[]
  selectedCategory: string
  onSelectCategory: (categoryId: string) => void
}

export function CategoryFilter({ 
  categories, 
  selectedCategory, 
  onSelectCategory 
}: CategoryFilterProps) {
  const { language } = useLanguage()

  return (
    <Card className="p-4">
      <h3 className="font-semibold mb-4">
        {language === 'en' ? 'Categories' : 'カテゴリー'}
      </h3>
      <div className="space-y-1">
        <Button
          variant={selectedCategory === 'all' ? 'secondary' : 'ghost'}
          className="w-full justify-start"
          onClick={() => onSelectCategory('all')}
        >
          <ChevronRight className="h-4 w-4 mr-2" />
          {language === 'en' ? 'All Categories' : 'すべてのカテゴリー'}
        </Button>
        {categories.map(category => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? 'secondary' : 'ghost'}
            className="w-full justify-start"
            onClick={() => onSelectCategory(category.id)}
          >
            <ChevronRight className="h-4 w-4 mr-2" />
            {language === 'en' ? category.name_en : category.name_jp}
          </Button>
        ))}
      </div>
    </Card>
  )
}