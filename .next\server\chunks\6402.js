"use strict";exports.id=6402,exports.ids=[6402],exports.modules={35668:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},15907:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},4269:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},6394:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},86235:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},18741:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},12214:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},2112:(e,t,r)=>{r.d(t,{C1:()=>E,bL:()=>x});var n=r(58009),l=r(29952),a=r(6004),o=r(31412),i=r(13024),u=r(66582),c=r(38762),d=r(98060),s=r(30830),f=r(45512),p="Checkbox",[v,h]=(0,a.A)(p),[m,g]=v(p);function b(e){let{__scopeCheckbox:t,checked:r,children:l,defaultChecked:a,disabled:o,form:u,name:c,onCheckedChange:d,required:s,value:v="on",internal_do_not_use_render:h}=e,[g,b]=(0,i.i)({prop:r,defaultProp:a??!1,onChange:d,caller:p}),[k,y]=n.useState(null),[x,w]=n.useState(null),E=n.useRef(!1),C=!k||!!u||!!k.closest("form"),A={checked:g,disabled:o,setChecked:b,control:k,setControl:y,name:c,form:u,value:v,hasConsumerStoppedPropagationRef:E,required:s,defaultChecked:!R(a)&&a,isFormControl:C,bubbleInput:x,setBubbleInput:w};return(0,f.jsx)(m,{scope:t,...A,children:"function"==typeof h?h(A):l})}var k="CheckboxTrigger",y=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},i)=>{let{control:u,value:c,disabled:d,checked:p,required:v,setControl:h,setChecked:m,hasConsumerStoppedPropagationRef:b,isFormControl:y,bubbleInput:x}=g(k,e),w=(0,l.s)(i,h),E=n.useRef(p);return n.useEffect(()=>{let e=u?.form;if(e){let t=()=>m(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,m]),(0,f.jsx)(s.sG.button,{type:"button",role:"checkbox","aria-checked":R(p)?"mixed":p,"aria-required":v,"data-state":S(p),"data-disabled":d?"":void 0,disabled:d,value:c,...a,ref:w,onKeyDown:(0,o.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(r,e=>{m(e=>!!R(e)||!e),x&&y&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});y.displayName=k;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:l,defaultChecked:a,required:o,disabled:i,value:u,onCheckedChange:c,form:d,...s}=e;return(0,f.jsx)(b,{__scopeCheckbox:r,checked:l,defaultChecked:a,disabled:i,required:o,onCheckedChange:c,name:n,form:d,value:u,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(y,{...s,ref:t,__scopeCheckbox:r}),e&&(0,f.jsx)(A,{__scopeCheckbox:r})]})})});x.displayName=p;var w="CheckboxIndicator",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...l}=e,a=g(w,r);return(0,f.jsx)(d.C,{present:n||R(a.checked)||!0===a.checked,children:(0,f.jsx)(s.sG.span,{"data-state":S(a.checked),"data-disabled":a.disabled?"":void 0,...l,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=w;var C="CheckboxBubbleInput",A=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:o,checked:i,defaultChecked:d,required:p,disabled:v,name:h,value:m,form:b,bubbleInput:k,setBubbleInput:y}=g(C,e),x=(0,l.s)(r,y),w=(0,u.Z)(i),E=(0,c.X)(a);n.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!o.current;if(w!==i&&e){let r=new Event("click",{bubbles:t});k.indeterminate=R(i),e.call(k,!R(i)&&i),k.dispatchEvent(r)}},[k,w,i,o]);let A=n.useRef(!R(i)&&i);return(0,f.jsx)(s.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??A.current,required:p,disabled:v,name:h,value:m,form:b,...t,tabIndex:-1,ref:x,style:{...t.style,...E,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function S(e){return R(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=C},13490:(e,t,r)=>{r.d(t,{Mz:()=>V,UC:()=>U,ZL:()=>T,bL:()=>$,l9:()=>H});var n=r(58009),l=r(31412),a=r(29952),o=r(6004),i=r(41675),u=r(19632),c=r(82534),d=r(30096),s=r(53337),f=r(80707),p=r(98060),v=r(30830),h=r(12705),m=r(13024),g=r(72421),b=r(67783),k=r(45512),y="Popover",[x,w]=(0,o.A)(y,[s.Bk]),E=(0,s.Bk)(),[C,A]=x(y),R=e=>{let{__scopePopover:t,children:r,open:l,defaultOpen:a,onOpenChange:o,modal:i=!1}=e,u=E(t),c=n.useRef(null),[f,p]=n.useState(!1),[v,h]=(0,m.i)({prop:l,defaultProp:a??!1,onChange:o,caller:y});return(0,k.jsx)(s.bL,{...u,children:(0,k.jsx)(C,{scope:t,contentId:(0,d.B)(),triggerRef:c,open:v,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:i,children:r})})};R.displayName=y;var S="PopoverAnchor",I=n.forwardRef((e,t)=>{let{__scopePopover:r,...l}=e,a=A(S,r),o=E(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:u}=a;return n.useEffect(()=>(i(),()=>u()),[i,u]),(0,k.jsx)(s.Mz,{...o,...l,ref:t})});I.displayName=S;var P="PopoverTrigger",j=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=A(P,r),i=E(r),u=(0,a.s)(t,o.triggerRef),c=(0,k.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":K(o.open),...n,ref:u,onClick:(0,l.m)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?c:(0,k.jsx)(s.Mz,{asChild:!0,...i,children:c})});j.displayName=P;var M="PopoverPortal",[O,D]=x(M,{forceMount:void 0}),F=e=>{let{__scopePopover:t,forceMount:r,children:n,container:l}=e,a=A(M,t);return(0,k.jsx)(O,{scope:t,forceMount:r,children:(0,k.jsx)(p.C,{present:r||a.open,children:(0,k.jsx)(f.Z,{asChild:!0,container:l,children:n})})})};F.displayName=M;var L="PopoverContent",N=n.forwardRef((e,t)=>{let r=D(L,e.__scopePopover),{forceMount:n=r.forceMount,...l}=e,a=A(L,e.__scopePopover);return(0,k.jsx)(p.C,{present:n||a.open,children:a.modal?(0,k.jsx)(G,{...l,ref:t}):(0,k.jsx)(z,{...l,ref:t})})});N.displayName=L;var B=(0,h.TL)("PopoverContent.RemoveScroll"),G=n.forwardRef((e,t)=>{let r=A(L,e.__scopePopover),o=n.useRef(null),i=(0,a.s)(t,o),u=n.useRef(!1);return n.useEffect(()=>{let e=o.current;if(e)return(0,g.Eq)(e)},[]),(0,k.jsx)(b.A,{as:B,allowPinchZoom:!0,children:(0,k.jsx)(_,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),u.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;u.current=n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),z=n.forwardRef((e,t)=>{let r=A(L,e.__scopePopover),l=n.useRef(!1),a=n.useRef(!1);return(0,k.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(l.current||r.triggerRef.current?.focus(),t.preventDefault()),l.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(l.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),_=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:a,disableOutsidePointerEvents:o,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:v,...h}=e,m=A(L,r),g=E(r);return(0,u.Oh)(),(0,k.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,k.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:o,onInteractOutside:v,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onDismiss:()=>m.onOpenChange(!1),children:(0,k.jsx)(s.UC,{"data-state":K(m.open),role:"dialog",id:m.contentId,...g,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),q="PopoverClose";function K(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=A(q,r);return(0,k.jsx)(v.sG.button,{type:"button",...n,ref:t,onClick:(0,l.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=q,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,l=E(r);return(0,k.jsx)(s.i3,{...l,...n,ref:t})}).displayName="PopoverArrow";var $=R,V=I,H=j,T=F,U=N},39156:(e,t,r)=>{r.d(t,{b:()=>c});var n=r(58009),l=r(30830),a=r(45512),o="horizontal",i=["horizontal","vertical"],u=n.forwardRef((e,t)=>{let{decorative:r,orientation:n=o,...u}=e,c=i.includes(n)?n:o;return(0,a.jsx)(l.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});u.displayName="Separator";var c=u},85748:(e,t,r)=>{r.d(t,{uB:()=>O});var n=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,a=/[\s-]/,o=/[\s-]/g;function i(e){return e.toLowerCase().replace(o," ")}var u=r(27553),c=r(58009),d=r(30830),s=r(30096),f=r(29952),p='[cmdk-group=""]',v='[cmdk-group-items=""]',h='[cmdk-item=""]',m=`${h}:not([aria-disabled="true"])`,g="cmdk-item-select",b="data-value",k=(e,t,r)=>(function(e,t,r){return function e(t,r,i,u,c,d,s){if(d===r.length)return c===t.length?1:.99;var f=`${c},${d}`;if(void 0!==s[f])return s[f];for(var p,v,h,m,g=u.charAt(d),b=i.indexOf(g,c),k=0;b>=0;)(p=e(t,r,i,u,b+1,d+1,s))>k&&(b===c?p*=1:n.test(t.charAt(b-1))?(p*=.8,(h=t.slice(c,b-1).match(l))&&c>0&&(p*=Math.pow(.999,h.length))):a.test(t.charAt(b-1))?(p*=.9,(m=t.slice(c,b-1).match(o))&&c>0&&(p*=Math.pow(.999,m.length))):(p*=.17,c>0&&(p*=Math.pow(.999,b-c))),t.charAt(b)!==r.charAt(d)&&(p*=.9999)),(p<.1&&i.charAt(b-1)===u.charAt(d+1)||u.charAt(d+1)===u.charAt(d)&&i.charAt(b-1)!==u.charAt(d))&&.1*(v=e(t,r,i,u,b+1,d+2,s))>p&&(p=.1*v),p>k&&(k=p),b=i.indexOf(g,b+1);return s[f]=k,k}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,i(e),i(t),0,0,{})})(e,t,r),y=c.createContext(void 0),x=()=>c.useContext(y),w=c.createContext(void 0),E=()=>c.useContext(w),C=c.createContext(void 0),A=c.forwardRef((e,t)=>{let r=L(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=L(()=>new Set),l=L(()=>new Map),a=L(()=>new Map),o=L(()=>new Set),i=D(e),{label:u,children:f,value:x,onValueChange:E,filter:C,shouldFilter:A,loop:R,disablePointerSelection:S=!1,vimBindings:I=!0,...P}=e,j=(0,s.B)(),M=(0,s.B)(),O=(0,s.B)(),N=c.useRef(null),B=G();F(()=>{if(void 0!==x){let e=x.trim();r.current.value=e,q.emit()}},[x]),F(()=>{B(6,U)},[]);let q=c.useMemo(()=>({subscribe:e=>(o.current.add(e),()=>o.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var l,a,o,u;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)T(),V(),B(1,H);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(O);e?e.focus():null==(l=document.getElementById(j))||l.focus()}if(B(7,()=>{var e;r.current.selectedItemId=null==(e=Z())?void 0:e.id,q.emit()}),n||B(5,U),(null==(a=i.current)?void 0:a.value)!==void 0){null==(u=(o=i.current).onValueChange)||u.call(o,null!=t?t:"");return}}q.emit()}},emit:()=>{o.current.forEach(e=>e())}}),[]),K=c.useMemo(()=>({value:(e,t,n)=>{var l;t!==(null==(l=a.current.get(e))?void 0:l.value)&&(a.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,$(t,n)),B(2,()=>{V(),q.emit()}))},item:(e,t)=>(n.current.add(e),t&&(l.current.has(t)?l.current.get(t).add(e):l.current.set(t,new Set([e]))),B(3,()=>{T(),V(),r.current.value||H(),q.emit()}),()=>{a.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=Z();B(4,()=>{T(),(null==t?void 0:t.getAttribute("id"))===e&&H(),q.emit()})}),group:e=>(l.current.has(e)||l.current.set(e,new Set),()=>{a.current.delete(e),l.current.delete(e)}),filter:()=>i.current.shouldFilter,label:u||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:j,inputId:O,labelId:M,listInnerRef:N}),[]);function $(e,t){var n,l;let a=null!=(l=null==(n=i.current)?void 0:n.filter)?l:k;return e?a(e,r.current.search,t):0}function V(){if(!r.current.search||!1===i.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=l.current.get(r),a=0;n.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let n=N.current;W().sort((t,r)=>{var n,l;let a=t.getAttribute("id"),o=r.getAttribute("id");return(null!=(n=e.get(o))?n:0)-(null!=(l=e.get(a))?l:0)}).forEach(e=>{let t=e.closest(v);t?t.appendChild(e.parentElement===t?e:e.closest(`${v} > *`)):n.appendChild(e.parentElement===n?e:e.closest(`${v} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=N.current)?void 0:t.querySelector(`${p}[${b}="${encodeURIComponent(e[0])}"]`);null==r||r.parentElement.appendChild(r)})}function H(){let e=W().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);q.setState("value",t||void 0)}function T(){var e,t,o,u;if(!r.current.search||!1===i.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let c=0;for(let l of n.current){let n=$(null!=(t=null==(e=a.current.get(l))?void 0:e.value)?t:"",null!=(u=null==(o=a.current.get(l))?void 0:o.keywords)?u:[]);r.current.filtered.items.set(l,n),n>0&&c++}for(let[e,t]of l.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=c}function U(){var e,t,r;let n=Z();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(p))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function Z(){var e;return null==(e=N.current)?void 0:e.querySelector(`${h}[aria-selected="true"]`)}function W(){var e;return Array.from((null==(e=N.current)?void 0:e.querySelectorAll(m))||[])}function X(e){let t=W()[e];t&&q.setState("value",t.getAttribute(b))}function J(e){var t;let r=Z(),n=W(),l=n.findIndex(e=>e===r),a=n[l+e];null!=(t=i.current)&&t.loop&&(a=l+e<0?n[n.length-1]:l+e===n.length?n[0]:n[l+e]),a&&q.setState("value",a.getAttribute(b))}function Q(e){let t=Z(),r=null==t?void 0:t.closest(p),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,p):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,p))?void 0:r.querySelector(m);n?q.setState("value",n.getAttribute(b)):J(e)}let Y=()=>X(W().length-1),ee=e=>{e.preventDefault(),e.metaKey?Y():e.altKey?Q(1):J(1)},et=e=>{e.preventDefault(),e.metaKey?X(0):e.altKey?Q(-1):J(-1)};return c.createElement(d.sG.div,{ref:t,tabIndex:-1,...P,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=P.onKeyDown)||t.call(P,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":I&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":I&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),X(0);break;case"End":e.preventDefault(),Y();break;case"Enter":{e.preventDefault();let t=Z();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},c.createElement("label",{"cmdk-label":"",htmlFor:K.inputId,id:K.labelId,style:_},u),z(e,e=>c.createElement(w.Provider,{value:q},c.createElement(y.Provider,{value:K},e))))}),R=c.forwardRef((e,t)=>{var r,n;let l=(0,s.B)(),a=c.useRef(null),o=c.useContext(C),i=x(),u=D(e),p=null!=(n=null==(r=u.current)?void 0:r.forceMount)?n:null==o?void 0:o.forceMount;F(()=>{if(!p)return i.item(l,null==o?void 0:o.id)},[p]);let v=B(l,a,[e.value,e.children,a],e.keywords),h=E(),m=N(e=>e.value&&e.value===v.current),b=N(e=>!!p||!1===i.filter()||!e.search||e.filtered.items.get(l)>0);function k(){var e,t;y(),null==(t=(e=u.current).onSelect)||t.call(e,v.current)}function y(){h.setState("value",v.current,!0)}if(c.useEffect(()=>{let t=a.current;if(!(!t||e.disabled))return t.addEventListener(g,k),()=>t.removeEventListener(g,k)},[b,e.onSelect,e.disabled]),!b)return null;let{disabled:w,value:A,onSelect:R,forceMount:S,keywords:I,...P}=e;return c.createElement(d.sG.div,{ref:(0,f.t)(a,t),...P,id:l,"cmdk-item":"",role:"option","aria-disabled":!!w,"aria-selected":!!m,"data-disabled":!!w,"data-selected":!!m,onPointerMove:w||i.getDisablePointerSelection()?void 0:y,onClick:w?void 0:k},e.children)}),S=c.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:l,...a}=e,o=(0,s.B)(),i=c.useRef(null),u=c.useRef(null),p=(0,s.B)(),v=x(),h=N(e=>!!l||!1===v.filter()||!e.search||e.filtered.groups.has(o));F(()=>v.group(o),[]),B(o,i,[e.value,e.heading,u]);let m=c.useMemo(()=>({id:o,forceMount:l}),[l]);return c.createElement(d.sG.div,{ref:(0,f.t)(i,t),...a,"cmdk-group":"",role:"presentation",hidden:!h||void 0},r&&c.createElement("div",{ref:u,"cmdk-group-heading":"","aria-hidden":!0,id:p},r),z(e,e=>c.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?p:void 0},c.createElement(C.Provider,{value:m},e))))}),I=c.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,l=c.useRef(null),a=N(e=>!e.search);return r||a?c.createElement(d.sG.div,{ref:(0,f.t)(l,t),...n,"cmdk-separator":"",role:"separator"}):null}),P=c.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,l=null!=e.value,a=E(),o=N(e=>e.search),i=N(e=>e.selectedItemId),u=x();return c.useEffect(()=>{null!=e.value&&a.setState("search",e.value)},[e.value]),c.createElement(d.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":u.listId,"aria-labelledby":u.labelId,"aria-activedescendant":i,id:u.inputId,type:"text",value:l?e.value:o,onChange:e=>{l||a.setState("search",e.target.value),null==r||r(e.target.value)}})}),j=c.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...l}=e,a=c.useRef(null),o=c.useRef(null),i=N(e=>e.selectedItemId),u=x();return c.useEffect(()=>{if(o.current&&a.current){let e=o.current,t=a.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),c.createElement(d.sG.div,{ref:(0,f.t)(a,t),...l,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":i,"aria-label":n,id:u.listId},z(e,e=>c.createElement("div",{ref:(0,f.t)(o,u.listInnerRef),"cmdk-list-sizer":""},e)))}),M=c.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:l,contentClassName:a,container:o,...i}=e;return c.createElement(u.bL,{open:r,onOpenChange:n},c.createElement(u.ZL,{container:o},c.createElement(u.hJ,{"cmdk-overlay":"",className:l}),c.createElement(u.UC,{"aria-label":e.label,"cmdk-dialog":"",className:a},c.createElement(A,{ref:t,...i}))))}),O=Object.assign(A,{List:j,Item:R,Input:P,Group:S,Separator:I,Dialog:M,Empty:c.forwardRef((e,t)=>N(e=>0===e.filtered.count)?c.createElement(d.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:c.forwardRef((e,t)=>{let{progress:r,children:n,label:l="Loading...",...a}=e;return c.createElement(d.sG.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":l},z(e,e=>c.createElement("div",{"aria-hidden":!0},e)))})});function D(e){let t=c.useRef(e);return F(()=>{t.current=e}),t}var F=c.useEffect;function L(e){let t=c.useRef();return void 0===t.current&&(t.current=e()),t}function N(e){let t=E(),r=()=>e(t.snapshot());return c.useSyncExternalStore(t.subscribe,r,r)}function B(e,t,r,n=[]){let l=c.useRef(),a=x();return F(()=>{var o;let i=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():l.current}})(),u=n.map(e=>e.trim());a.value(e,i,u),null==(o=t.current)||o.setAttribute(b,i),l.current=i}),l}var G=()=>{let[e,t]=c.useState(),r=L(()=>new Map);return F(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function z({asChild:e,children:t},r){let n;return e&&c.isValidElement(t)?c.cloneElement("function"==typeof(n=t.type)?n(t.props):"render"in n?n.render(t.props):t,{ref:t.ref},r(t.props.children)):r(t)}var _={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}}};