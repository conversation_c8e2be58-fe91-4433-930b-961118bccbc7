'use client'

/**
 * Language Context
 * Provides language switching functionality across the application
 */

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

type Language = 'en' | 'ja'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, fallback?: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Simple translation dictionary
const translations: Record<Language, Record<string, string>> = {
  en: {
    'dashboard': 'Dashboard',
    'tickets': 'Tickets',
    'knowledge_base': 'Knowledge Base',
    'settings': 'Settings',
    'profile': 'Profile',
    'logout': 'Logout',
    'search': 'Search',
    'create_ticket': 'Create Ticket',
    'submit': 'Submit',
    'cancel': 'Cancel',
    'save': 'Save',
    'edit': 'Edit',
    'delete': 'Delete',
    'loading': 'Loading...',
    'error': 'Error',
    'success': 'Success',
    'warning': 'Warning',
    'info': 'Information'
  },
  ja: {
    'dashboard': 'ダッシュボード',
    'tickets': 'チケット',
    'knowledge_base': 'ナレッジベース',
    'settings': '設定',
    'profile': 'プロフィール',
    'logout': 'ログアウト',
    'search': '検索',
    'create_ticket': 'チケット作成',
    'submit': '送信',
    'cancel': 'キャンセル',
    'save': '保存',
    'edit': '編集',
    'delete': '削除',
    'loading': '読み込み中...',
    'error': 'エラー',
    'success': '成功',
    'warning': '警告',
    'info': '情報'
  }
}

interface LanguageProviderProps {
  children: ReactNode
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>('en')

  useEffect(() => {
    // Load language from localStorage or browser preference
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ja')) {
      setLanguage(savedLanguage)
    } else {
      // Detect browser language
      const browserLang = navigator.language.toLowerCase()
      if (browserLang.startsWith('ja')) {
        setLanguage('ja')
      }
    }
  }, [])

  useEffect(() => {
    // Save language to localStorage
    localStorage.setItem('language', language)
  }, [language])

  const t = (key: string, fallback?: string): string => {
    return translations[language][key] || fallback || key
  }

  const value: LanguageContextType = {
    language,
    setLanguage,
    t
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// Helper hook for translations
export function useTranslation() {
  const { t } = useLanguage()
  return { t }
}
