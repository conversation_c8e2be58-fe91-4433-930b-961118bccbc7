// lib/services/nlp-form-service.ts
// Natural Language Processing service for form inputs

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

interface ParsedFormData {
  intent: string
  entities: Record<string, any>
  confidence: number
  suggestedForm: string
  extractedData: Record<string, any>
}

interface NLPContext {
  userId?: string
  departmentId?: string
  language: 'ja' | 'en'
  availableForms: string[]
}

export class NLPFormService {
  private supabase = createClientComponentClient()
  private parseCache = new Map<string, ParsedFormData>()

  async parseNaturalLanguageInput(
    input: string,
    context: NLPContext
  ): Promise<ParsedFormData> {
    // Check cache
    const cacheKey = `${input}-${context.language}`
    if (this.parseCache.has(cacheKey)) {
      return this.parseCache.get(cacheKey)!
    }

    try {
      const { data, error } = await this.supabase.functions.invoke('parse-natural-language', {
        body: {
          input,
          context
        }
      })

      if (error) throw error

      // Cache result
      this.parseCache.set(cacheKey, data)
      
      // Clear cache after 10 minutes
      setTimeout(() => {
        this.parseCache.delete(cacheKey)
      }, 10 * 60 * 1000)

      return data
    } catch (error) {
      console.error('NLP parsing failed:', error)
      return {
        intent: 'unknown',
        entities: {},
        confidence: 0,
        suggestedForm: '',
        extractedData: {}
      }
    }
  }

  // Parse specific intents
  parseGroupMailRequest(input: string): Record<string, any> {
    const extractedData: Record<string, any> = {}
    
    // Japanese patterns
    const addPatterns = [
      /(.+)を(.+)グループメールに追加/,
      /(.+)さんを(.+)に登録/,
      /(.+)のグループメール登録/
    ]
    
    const removePatterns = [
      /(.+)を(.+)グループメールから削除/,
      /(.+)さんを(.+)から外す/,
      /(.+)のグループメール解除/
    ]

    // Check for add action
    for (const pattern of addPatterns) {
      const match = input.match(pattern)
      if (match) {
        extractedData.action = 'add'
        extractedData.userName = match[1]
        if (match[2]) {
          extractedData.groupMail = match[2]
        }
        break
      }
    }

    // Check for remove action
    if (!extractedData.action) {
      for (const pattern of removePatterns) {
        const match = input.match(pattern)
        if (match) {
          extractedData.action = 'remove'
          extractedData.userName = match[1]
          if (match[2]) {
            extractedData.groupMail = match[2]
          }
          break
        }
      }
    }

    return extractedData
  }

  parsePasswordResetRequest(input: string): Record<string, any> {
    const extractedData: Record<string, any> = {}
    
    // Patterns for password reset
    const patterns = [
      /(.+)のパスワードリセット/,
      /(.+)さんのパスワード再設定/,
      /(.+)の(M365|MFA|Windows)パスワード/,
      /reset password for (.+)/i,
      /(.+) password reset/i
    ]

    for (const pattern of patterns) {
      const match = input.match(pattern)
      if (match) {
        extractedData.userName = match[1]
        if (match[2]) {
          extractedData.passwordType = match[2]
        }
        break
      }
    }

    // Detect password type
    if (input.includes('M365') || input.includes('Office')) {
      extractedData.passwordType = 'M365 Office'
    } else if (input.includes('MFA') || input.includes('認証')) {
      extractedData.passwordType = 'Multi Factor Authenticator'
    } else if (input.includes('Windows')) {
      extractedData.passwordType = 'Windows'
    }

    return extractedData
  }

  parseSharePointRequest(input: string): Record<string, any> {
    const extractedData: Record<string, any> = {}
    
    // Patterns for SharePoint access
    const grantPatterns = [
      /(.+)に(.+)のアクセス権を付与/,
      /(.+)さんが(.+)にアクセス/,
      /(.+)への(.+)権限/
    ]
    
    const revokePatterns = [
      /(.+)から(.+)のアクセス権を削除/,
      /(.+)さんの(.+)アクセスを解除/
    ]

    // Check for grant action
    for (const pattern of grantPatterns) {
      const match = input.match(pattern)
      if (match) {
        extractedData.action = 'grant'
        extractedData.userName = match[1]
        extractedData.library = match[2]
        break
      }
    }

    // Check for revoke action
    if (!extractedData.action) {
      for (const pattern of revokePatterns) {
        const match = input.match(pattern)
        if (match) {
          extractedData.action = 'revoke'
          extractedData.userName = match[1]
          extractedData.library = match[2]
          break
        }
      }
    }

    // Detect access level
    if (input.includes('読み取り') || input.includes('read')) {
      extractedData.accessLevel = 'read'
    } else if (input.includes('編集') || input.includes('contribute')) {
      extractedData.accessLevel = 'contribute'
    } else if (input.includes('フル') || input.includes('full')) {
      extractedData.accessLevel = 'full'
    }

    return extractedData
  }

  // Suggest form based on input
  suggestFormType(input: string): { formType: string; confidence: number } {
    const lowerInput = input.toLowerCase()
    
    const formMappings = [
      { keywords: ['グループメール', 'group mail', 'メーリングリスト'], formType: 'group_mail' },
      { keywords: ['メールボックス', 'mailbox', '共有メール'], formType: 'mailbox' },
      { keywords: ['sharepoint', 'シェアポイント', '文書', 'ドキュメント'], formType: 'sharepoint' },
      { keywords: ['pc', '管理者', 'admin', '権限'], formType: 'pc_admin' },
      { keywords: ['パスワード', 'password', 'リセット', 'reset'], formType: 'password_reset' },
      { keywords: ['mxp', '登録'], formType: 'mxp_registration' },
      { keywords: ['ソフト', 'software', 'インストール'], formType: 'software_install' },
      { keywords: ['web', 'ウェブ', '閲覧', 'browsing'], formType: 'web_browsing' }
    ]

    for (const mapping of formMappings) {
      const matches = mapping.keywords.filter(keyword => lowerInput.includes(keyword))
      if (matches.length > 0) {
        return {
          formType: mapping.formType,
          confidence: Math.min(matches.length * 0.3, 0.9)
        }
      }
    }

    return { formType: 'unknown', confidence: 0 }
  }

  // Extract user names from input
  extractUserNames(input: string): string[] {
    const userNames: string[] = []
    
    // Japanese name patterns
    const jpNamePattern = /([一-龯ぁ-ゔァ-ヴー]{2,}\s*[一-龯ぁ-ゔァ-ヴー]{2,})/g
    const matches = input.match(jpNamePattern)
    
    if (matches) {
      userNames.push(...matches)
    }

    // Look for "さん" suffix
    const sanPattern = /([一-龯ぁ-ゔァ-ヴーa-zA-Z]+)さん/g
    const sanMatches = input.match(sanPattern)
    
    if (sanMatches) {
      userNames.push(...sanMatches.map(m => m.replace('さん', '')))
    }

    // Remove duplicates
    return [...new Set(userNames)]
  }

  // Extract dates from input
  extractDates(input: string): string[] {
    const dates: string[] = []
    
    // Various date patterns
    const patterns = [
      /\d{4}年\d{1,2}月\d{1,2}日/g,
      /\d{4}\/\d{1,2}\/\d{1,2}/g,
      /\d{4}-\d{1,2}-\d{1,2}/g,
      /\d{1,2}月\d{1,2}日/g
    ]

    for (const pattern of patterns) {
      const matches = input.match(pattern)
      if (matches) {
        dates.push(...matches)
      }
    }

    return dates
  }

  // Convert parsed data to form values
  mapToFormFields(
    parsedData: ParsedFormData,
    formSchema: any
  ): Record<string, any> {
    const formValues: Record<string, any> = {}
    
    // Map extracted entities to form fields
    for (const [key, value] of Object.entries(parsedData.extractedData)) {
      // Find matching field in schema
      const field = formSchema.fields?.find((f: any) => 
        f.name === key || 
        f.name.toLowerCase() === key.toLowerCase() ||
        f.label?.toLowerCase().includes(key.toLowerCase())
      )
      
      if (field) {
        formValues[field.name] = value
      }
    }

    return formValues
  }
}

// Export singleton instance
export const nlpFormService = new NLPFormService()
