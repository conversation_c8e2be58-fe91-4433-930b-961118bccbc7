# ITSync Production Deployment Guide

## 🚀 Overview

This guide covers the complete production deployment process for ITSync, including infrastructure setup, security configuration, monitoring, and maintenance procedures.

## 📋 Prerequisites

### System Requirements
- **Server**: Minimum 4GB RAM, 2 CPU cores, 50GB SSD
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **Network**: 1Gbps connection, ports 80/443/22 accessible
- **Domain**: Configured with DNS pointing to server
- **SSL**: Valid certificates for HTTPS

### Software Requirements
- Docker and Docker Compose installed
- Supabase project configured for production
- API keys for OpenAI and Anthropic (if using AI features)
- Git for deployment updates

### Security Requirements
- Firewall configured (UFW recommended)
- SSH key-based authentication
- Non-root user with sudo privileges
- Regular security updates enabled

## Environment Configuration

1. Copy `.env.local` to `.env.production`:
```bash
cp .env.local .env.production
```

2. Update production environment variables:
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME=ITSync
NODE_ENV=production
```

## Database Migration

1. Export current schema:
```bash
supabase db dump > schema.sql
```

2. Apply migrations to production:
```bash
supabase db push --db-url "****************************************/postgres"
```

## Docker Deployment

1. Build the Docker image:
```bash
docker build -t itsync:latest .
```

2. Run with Docker Compose:
```bash
docker-compose up -d
```

## SSL Configuration

1. Place SSL certificates in `./nginx/ssl/`
2. Update nginx configuration with your domain

## 📊 Monitoring & Health Checks

### Health Check Endpoints

The application provides multiple health check endpoints:

- **`/health`** - Simple nginx health check (returns "healthy")
- **`/api/health/simple`** - Basic application health (JSON response)
- **`/api/health`** - Comprehensive system health with detailed metrics
- **`/api/metrics`** - System metrics in JSON or Prometheus format (admin only)

### Monitoring Commands

```bash
# Check all services status
docker-compose ps

# View real-time logs
docker-compose logs -f app
docker-compose logs -f nginx
docker-compose logs -f redis

# Monitor resource usage
docker stats

# Test health endpoints
curl http://localhost/health
curl http://localhost/api/health/simple
curl -H "Authorization: Bearer $ADMIN_TOKEN" http://localhost/api/health
```

### Setting Up External Monitoring

```bash
# Example: Prometheus monitoring
curl http://localhost/api/metrics?format=prometheus

# Example: Uptime monitoring
curl -f http://localhost/api/health/simple || echo "Service down"
```

## 🔄 Backup & Recovery

### Automated Backup System

ITSync includes a comprehensive backup system accessible via API:

```bash
# Create manual backup
curl -X POST http://localhost/api/admin/backup \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"description":"Manual backup before update"}'

# List available backups
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost/api/admin/backup

# Restore from backup (dry run first)
curl -X POST http://localhost/api/admin/backup/BACKUP_ID/restore \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"dry_run":true,"verify_checksum":true}'
```

### Backup Components

- **Database**: All tables with row-level security
- **User Data**: Profiles, tickets, forms, submissions
- **Audit Logs**: Security and compliance records
- **Configuration**: System settings and preferences

### Backup Schedule

- **Daily**: Automated full backup at 2 AM
- **Retention**: 30 days (configurable)
- **Verification**: Automatic integrity checks
- **Encryption**: AES-256 for sensitive data

## Troubleshooting

Common issues and solutions:
- Port conflicts: Change ports in docker-compose.yml
- Memory issues: Increase Docker memory allocation
- SSL errors: Verify certificate paths in nginx config
