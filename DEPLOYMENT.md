# ITSync Production Deployment Guide

## Prerequisites
- Docker and Docker Compose installed
- Supabase project configured
- Domain name and SSL certificates
- API keys for OpenAI and Anthropic

## Environment Configuration

1. Copy `.env.local` to `.env.production`:
```bash
cp .env.local .env.production
```

2. Update production environment variables:
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME=ITSync
NODE_ENV=production
```

## Database Migration

1. Export current schema:
```bash
supabase db dump > schema.sql
```

2. Apply migrations to production:
```bash
supabase db push --db-url "****************************************/postgres"
```

## Docker Deployment

1. Build the Docker image:
```bash
docker build -t itsync:latest .
```

2. Run with Docker Compose:
```bash
docker-compose up -d
```

## SSL Configuration

1. Place SSL certificates in `./nginx/ssl/`
2. Update nginx configuration with your domain

## Monitoring Setup

1. Access logs: `docker logs itsync-app`
2. Monitor Redis: `docker exec -it itsync-redis redis-cli`
3. Check health: `https://your-domain.com/api/health`

## Backup Procedures

Daily backups are configured for:
- Supabase database (automated)
- Redis data (volume backup)
- Application logs (rotated daily)

## Troubleshooting

Common issues and solutions:
- Port conflicts: Change ports in docker-compose.yml
- Memory issues: Increase Docker memory allocation
- SSL errors: Verify certificate paths in nginx config
