'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getAIConfig, validateAIConfig, AIConfig } from '@/lib/config/ai-config'
import { aiAPIService } from '@/lib/services/ai-api-service'

interface AIContextValue {
  config: AIConfig | null
  isConfigured: boolean
  errors: string[]
  updateConfig: (newConfig: Partial<AIConfig>) => void
  testConnection: (provider: 'openai' | 'anthropic') => Promise<boolean>
}

const AIContext = createContext<AIContextValue | undefined>(undefined)

export function AIProvider({ children }: { children: React.ReactNode }) {
  const [config, setConfig] = useState<AIConfig | null>(null)
  const [isConfigured, setIsConfigured] = useState(false)
  const [errors, setErrors] = useState<string[]>([])

  useEffect(() => {
    const aiConfig = getAIConfig()
    const validation = validateAIConfig(aiConfig)
    
    setConfig(aiConfig)
    setIsConfigured(validation.isValid)
    setErrors(validation.errors)

    if (validation.isValid && aiConfig.defaultProvider) {
      aiAPIService.setDefaultProvider(aiConfig.defaultProvider)
    }
  }, [])

  const updateConfig = (newConfig: Partial<AIConfig>) => {
    if (!config) return

    const updatedConfig = { ...config, ...newConfig }
    const validation = validateAIConfig(updatedConfig)
    
    setConfig(updatedConfig)
    setIsConfigured(validation.isValid)
    setErrors(validation.errors)

    if (validation.isValid && updatedConfig.defaultProvider) {
      aiAPIService.setDefaultProvider(updatedConfig.defaultProvider)
    }
  }

  const testConnection = async (provider: 'openai' | 'anthropic'): Promise<boolean> => {    try {
      const response = await aiAPIService.completion({
        prompt: 'Hello, this is a test.',
        systemPrompt: 'Respond with "Connection successful"',
        maxTokens: 50,
        temperature: 0
      }, provider)

      return response.text.includes('successful')
    } catch (error) {
      console.error(`Failed to test ${provider} connection:`, error)
      return false
    }
  }

  return (
    <AIContext.Provider value={{ config, isConfigured, errors, updateConfig, testConnection }}>
      {children}
    </AIContext.Provider>
  )
}

export function useAI() {
  const context = useContext(AIContext)
  if (context === undefined) {
    throw new Error('useAI must be used within an AIProvider')
  }
  return context
}

// Hook for using AI completion
export function useAICompletion() {
  const { isConfigured } = useAI()
  
  const complete = async (
    prompt: string,
    options?: {
      systemPrompt?: string
      temperature?: number
      maxTokens?: number
      provider?: 'openai' | 'anthropic'
    }
  ) => {
    if (!isConfigured) {
      throw new Error('AI is not configured. Please check your API keys.')
    }

    return aiAPIService.completion({
      prompt,
      systemPrompt: options?.systemPrompt,
      temperature: options?.temperature,      maxTokens: options?.maxTokens
    }, options?.provider)
  }

  return { complete, isConfigured }
}

// Hook for form field validation
export function useAIFormValidation() {
  const { isConfigured } = useAI()
  
  const validateField = async (
    fieldType: string,
    value: string,
    context?: Record<string, any>
  ) => {
    if (!isConfigured) {
      return { isValid: true, issues: [], suggestions: [] }
    }

    return aiAPIService.validateFormField(fieldType, value, context)
  }

  return { validateField, isConfigured }
}

// Hook for text translation
export function useAITranslation() {
  const { isConfigured } = useAI()
  
  const translate = async (
    text: string,
    from: 'ja' | 'en',
    to: 'ja' | 'en'
  ) => {
    if (!isConfigured || from === to) {
      return text
    }

    return aiAPIService.translateText(text, from, to)
  }

  return { translate, isConfigured }
}