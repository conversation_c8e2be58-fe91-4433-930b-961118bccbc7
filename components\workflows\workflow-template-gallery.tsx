'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  FileText, 
  Clock, 
  Users, 
  Zap,
  Search,
  Filter,
  ChevronRight,
  Tag,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface WorkflowTemplate {
  id: string;
  name: string;
  nameJp: string;
  description: string;
  descriptionJp: string;
  category: string;
  serviceCategoryId?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  slaHours: number;
  requiredApprovals?: number;
  tags: string[];
  steps: any[];
}

interface WorkflowTemplateGalleryProps {
  requestId?: string;
  serviceCategoryId?: string;
  onTemplateApplied?: () => void;
}

export function WorkflowTemplateGallery({ 
  requestId, 
  serviceCategoryId,
  onTemplateApplied 
}: WorkflowTemplateGalleryProps) {
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const { toast } = useToast();

  useEffect(() => {
    fetchTemplates();
  }, [serviceCategoryId]);

  useEffect(() => {
    filterTemplates();
  }, [templates, searchQuery, categoryFilter, priorityFilter]);

  const fetchTemplates = async () => {
    try {
      const params = new URLSearchParams();
      if (serviceCategoryId) {
        params.append('serviceCategoryId', serviceCategoryId);
      }

      const response = await fetch(`/api/workflows/templates?${params}`);
      if (!response.ok) throw new Error('Failed to fetch templates');
      
      const data = await response.json();
      setTemplates(data.templates);
      setFilteredTemplates(data.templates);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to load workflow templates',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const filterTemplates = () => {
    let filtered = [...templates];

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(query) ||
        t.nameJp.includes(searchQuery) ||
        t.description.toLowerCase().includes(query) ||
        t.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(t => t.category === categoryFilter);
    }

    // Priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(t => t.priority === priorityFilter);
    }

    setFilteredTemplates(filtered);
  };

  const applyTemplate = async (template: WorkflowTemplate) => {
    if (!requestId) {
      toast({
        title: 'Error',
        description: 'No request selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch('/api/workflows/templates/apply', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requestId,
          templateId: template.id,
        }),
      });

      if (!response.ok) throw new Error('Failed to apply template');

      toast({
        title: 'Success',
        description: `Template "${template.name}" applied successfully`,
      });

      if (onTemplateApplied) {
        onTemplateApplied();
      }
    } catch (error) {
      console.error('Error applying template:', error);
      toast({
        title: 'Error',
        description: 'Failed to apply template',
        variant: 'destructive',
      });
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'password_reset':
        return '🔐';
      case 'email_management':
        return '📧';
      case 'sharepoint':
        return '📁';
      case 'pc_management':
        return '💻';
      case 'hr_processes':
        return '👥';
      case 'software':
        return '📦';
      case 'web_access':
        return '🌐';
      default:
        return '📋';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const categories = Array.from(new Set(templates.map(t => t.category)));

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[200px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(cat => (
              <SelectItem key={cat} value={cat}>
                {getCategoryIcon(cat)} {cat.replace('_', ' ')}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-[150px]">
            <AlertCircle className="mr-2 h-4 w-4" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priorities</SelectItem>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      {loading ? (
        <div className="text-center py-12">Loading templates...</div>
      ) : filteredTemplates.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          No templates found matching your criteria
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map((template) => (
            <Card 
              key={template.id} 
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => setSelectedTemplate(template)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="text-2xl">{getCategoryIcon(template.category)}</div>
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <p className="text-sm text-muted-foreground mt-1">
                        {template.nameJp}
                      </p>
                    </div>
                  </div>
                  <Badge className={getPriorityColor(template.priority)}>
                    {template.priority}
                  </Badge>
                </div>
                <CardDescription className="mt-2">
                  {template.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {template.slaHours}h SLA
                  </div>
                  <div className="flex items-center gap-1">
                    <Zap className="h-4 w-4" />
                    {template.steps.length} steps
                  </div>
                  {template.requiredApprovals && (
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {template.requiredApprovals} approvals
                    </div>
                  )}
                </div>
                <div className="flex flex-wrap gap-1 mt-3">
                  {template.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                {requestId && (
                  <Button 
                    className="w-full mt-4"
                    onClick={(e) => {
                      e.stopPropagation();
                      applyTemplate(template);
                    }}
                  >
                    Apply Template
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Template Detail Dialog */}
      <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          {selectedTemplate && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-3">
                  <span className="text-2xl">{getCategoryIcon(selectedTemplate.category)}</span>
                  {selectedTemplate.name}
                </DialogTitle>
                <DialogDescription>
                  {selectedTemplate.description}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6 mt-6">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium">Priority</p>
                    <Badge className={`${getPriorityColor(selectedTemplate.priority)} mt-1`}>
                      {selectedTemplate.priority}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium">SLA</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {selectedTemplate.slaHours} hours
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Approvals</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {selectedTemplate.requiredApprovals || 0} required
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-3">Workflow Steps</h4>
                  <div className="space-y-2">
                    {selectedTemplate.steps.map((step, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{step.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {step.description}
                          </p>
                          <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                            <span>Type: {step.type}</span>
                            <span>Duration: {step.estimatedDuration} min</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedTemplate.tags.map(tag => (
                      <Badge key={tag} variant="secondary">
                        <Tag className="mr-1 h-3 w-3" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {requestId && (
                  <Button 
                    className="w-full"
                    onClick={() => {
                      applyTemplate(selectedTemplate);
                      setSelectedTemplate(null);
                    }}
                  >
                    Apply This Template
                  </Button>
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
