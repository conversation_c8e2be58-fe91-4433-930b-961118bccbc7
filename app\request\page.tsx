'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import AuthGuard from '@/components/auth/auth-guard'
import ServiceSelection from '@/components/forms/service-selection'
import UserSelection from '@/components/forms/user-selection'
import DynamicForm from '@/components/forms/dynamic-form'
import { EnhancedRequestConfirmation } from '@/components/forms/enhanced-request-confirmation'
import { ServiceCategory, SelectedUser, RequestItem, RequestConfirmation } from '@/lib/request-types'
import { FormSchema, FormData } from '@/lib/form-types'
import { createFormSchemaFromServiceCategory } from '@/lib/form-utils'
import { useAuth } from '@/lib/auth-context'
import { supabase } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON> } from '@/components/ui/badge'
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert'
import { Loader2, ArrowLeft, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react'
import { KBIntegrationWidget } from '@/components/knowledge-base/kb-integration-widget'
import { HelpDeskKBIntegration } from '@/components/knowledge-base/helpdesk-kb-integration'
import { ChatbotWidget } from '@/components/chatbot/chatbot-widget'

enum WizardStep {
  SERVICE_SELECTION = 0,
  USER_SELECTION = 1,
  FORM_FILLING = 2,
  CONFIRMATION = 3
}

export default function RequestWizardPage() {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.SERVICE_SELECTION)
  const [selectedServices, setSelectedServices] = useState<ServiceCategory[]>([])
  const [selectedUsers, setSelectedUsers] = useState<SelectedUser[]>([])
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0)
  const [requestItems, setRequestItems] = useState<RequestItem[]>([])
  const [confirmation, setConfirmation] = useState<RequestConfirmation | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  
  const { user, staff } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (currentStep === WizardStep.CONFIRMATION) {
      buildConfirmation()
    }
  }, [currentStep, requestItems])

  const buildConfirmation = () => {
    const totalUsers = new Set(requestItems.flatMap(item => item.users.map(u => u.id))).size
    const totalServices = requestItems.length

    const confirmationData: RequestConfirmation = {
      items: requestItems,
      status: requestItems.length > 0 ? 'ready' : 'draft',
      totalUsers,
      totalServices
    }

    setConfirmation(confirmationData)
  }

  const handleServiceSelection = (services: ServiceCategory[]) => {
    setSelectedServices(services)
    setError('')
  }

  const handleUserSelection = (users: SelectedUser[]) => {
    setSelectedUsers(users)
    setError('')
  }

  const handleFormSubmit = (formData: FormData) => {
    const currentService = selectedServices[currentServiceIndex]
    
    const newRequestItem: RequestItem = {
      serviceCategory: currentService,
      users: selectedUsers,
      formData,
      action: 'add' // Default action, could be determined from form data
    }

    setRequestItems(prev => {
      const updated = [...prev]
      const existingIndex = updated.findIndex(item => 
        item.serviceCategory.id === currentService.id
      )
      
      if (existingIndex >= 0) {
        updated[existingIndex] = newRequestItem
      } else {
        updated.push(newRequestItem)
      }
      
      return updated
    })

    // Move to next service or confirmation
    if (currentServiceIndex < selectedServices.length - 1) {
      setCurrentServiceIndex(currentServiceIndex + 1)
    } else {
      setCurrentStep(WizardStep.CONFIRMATION)
    }
  }

  const handleNext = () => {
    switch (currentStep) {
      case WizardStep.SERVICE_SELECTION:
        if (selectedServices.length === 0) {
          setError('少なくとも1つのサービスを選択してください / Please select at least one service')
          return
        }
        setCurrentStep(WizardStep.USER_SELECTION)
        break

      case WizardStep.USER_SELECTION:
        if (selectedUsers.length === 0) {
          setError('少なくとも1人のユーザーを選択してください / Please select at least one user')
          return
        }
        setCurrentStep(WizardStep.FORM_FILLING)
        setCurrentServiceIndex(0)
        break

      case WizardStep.FORM_FILLING:
        // This is handled by form submission
        break

      case WizardStep.CONFIRMATION:
        // This is handled by final submission
        break
    }
  }

  const handlePrevious = () => {
    switch (currentStep) {
      case WizardStep.USER_SELECTION:
        setCurrentStep(WizardStep.SERVICE_SELECTION)
        break

      case WizardStep.FORM_FILLING:
        if (currentServiceIndex > 0) {
          setCurrentServiceIndex(currentServiceIndex - 1)
        } else {
          setCurrentStep(WizardStep.USER_SELECTION)
        }
        break

      case WizardStep.CONFIRMATION:
        setCurrentStep(WizardStep.FORM_FILLING)
        setCurrentServiceIndex(selectedServices.length - 1)
        break
    }
  }

  const handleFinalSubmit = async () => {
    if (!confirmation) return

    try {
      setLoading(true)
      setError('')

      // Create main request form
      const { data: requestForm, error: requestError } = await supabase
        .from('request_forms')
        .insert({
          requester_id: user?.id,
          title: `Multi-service request: ${selectedServices.map(s => s.name_jp).join(', ')}`,
          description: `Request for ${selectedUsers.length} users across ${selectedServices.length} services`,
          status: 'submitted',
          priority: 'medium'
        })
        .select()
        .single()

      if (requestError) throw requestError

      // Create request items for each service/user combination
      const requestItemsData = []
      
      for (const item of requestItems) {
        for (const user of item.users) {
          requestItemsData.push({
            request_form_id: requestForm.id,
            service_category_id: item.serviceCategory.id,
            affected_user_id: user.id,
            action_type: item.action,
            target_resource: JSON.stringify(item.formData),
            request_details: item.formData,
            status: 'pending'
          })
        }
      }

      const { error: itemsError } = await supabase
        .from('request_items')
        .insert(requestItemsData)

      if (itemsError) throw itemsError

      // Success - redirect to status page or dashboard
      router.push(`/dashboard?success=request_submitted&id=${requestForm.id}`)

    } catch (err: any) {
      setError('リクエストの送信に失敗しました / Failed to submit request: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleEditRequest = () => {
    setCurrentStep(WizardStep.SERVICE_SELECTION)
    setCurrentServiceIndex(0)
  }

  const handleCancelRequest = () => {
    router.push('/dashboard')
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case WizardStep.SERVICE_SELECTION:
        return 'サービス選択 / Service Selection'
      case WizardStep.USER_SELECTION:
        return 'ユーザー選択 / User Selection'
      case WizardStep.FORM_FILLING:
        return `詳細入力 / Form Details (${currentServiceIndex + 1}/${selectedServices.length})`
      case WizardStep.CONFIRMATION:
        return '確認 / Confirmation'
      default:
        return 'ITリクエスト / IT Request'
    }
  }

  const getStepDescription = () => {
    switch (currentStep) {
      case WizardStep.SERVICE_SELECTION:
        return 'リクエストするサービスを選択してください'
      case WizardStep.USER_SELECTION:
        return 'リクエストの対象となるユーザーを選択してください'
      case WizardStep.FORM_FILLING:
        return `${selectedServices[currentServiceIndex]?.name_jp}の詳細を入力してください`
      case WizardStep.CONFIRMATION:
        return 'リクエスト内容を確認して送信してください'
      default:
        return 'AIを活用したITサポートリクエストシステム'
    }
  }

  const currentService = selectedServices[currentServiceIndex]
  const progress = ((currentStep + (currentStep === WizardStep.FORM_FILLING ? currentServiceIndex / selectedServices.length : 0)) / 4) * 100

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-3xl font-bold text-gray-900">
                  {getStepTitle()}
                </h1>
                <Badge variant="outline">
                  ステップ {currentStep + 1} / 4
                </Badge>
              </div>
              <p className="text-gray-600 mb-4">
                {getStepDescription()}
              </p>
              <Progress value={progress} className="w-full h-2" />
            </div>

            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Step Content */}
            <div className="space-y-6">
              {currentStep === WizardStep.SERVICE_SELECTION && (
                <ServiceSelection
                  onServiceSelect={handleServiceSelection}
                  selectedServices={selectedServices}
                  disabled={loading}
                />
              )}

              {currentStep === WizardStep.USER_SELECTION && (
                <UserSelection
                  onUserSelect={handleUserSelection}
                  selectedUsers={selectedUsers}
                  disabled={loading}
                />
              )}

              {currentStep === WizardStep.FORM_FILLING && currentService && (
                <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                  <div className="xl:col-span-3 space-y-6">
                    <DynamicForm
                      schema={createFormSchemaFromServiceCategory(currentService)}
                      onSubmit={handleFormSubmit}
                      onCancel={() => setCurrentStep(WizardStep.USER_SELECTION)}
                      loading={loading}
                      aiEnabled={true}
                      departmentId={staff?.department_id}
                    />
                  </div>
                  <div className="xl:col-span-1 space-y-6">
                    <HelpDeskKBIntegration
                      serviceCategory={currentService.code}
                      currentStep="form_filling"
                      className="sticky top-6"
                    />
                    <KBIntegrationWidget
                      context={{
                        serviceCategory: currentService.code,
                        currentStep: 'form_filling',
                        formType: currentService.name_en
                      }}
                      className="sticky top-[360px]"
                    />
                  </div>
                </div>
              )}

              {currentStep === WizardStep.CONFIRMATION && confirmation && (
                <EnhancedRequestConfirmation
                  confirmation={confirmation}
                  onSubmit={handleFinalSubmit}
                  onEdit={handleEditRequest}
                  onCancel={handleCancelRequest}
                  loading={loading}
                />
              )}

              {/* Navigation */}
              {currentStep !== WizardStep.FORM_FILLING && currentStep !== WizardStep.CONFIRMATION && (
                <div className="flex justify-between pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrevious}
                    disabled={currentStep === WizardStep.SERVICE_SELECTION || loading}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    戻る / Previous
                  </Button>

                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleCancelRequest}
                      disabled={loading}
                    >
                      キャンセル / Cancel
                    </Button>

                    <Button
                      type="button"
                      onClick={handleNext}
                      disabled={loading}
                    >
                      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      次へ / Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Chatbot Widget */}
      <ChatbotWidget
        currentForm={currentService?.name_en}
        currentPage="request_wizard"
        defaultOpen={false}
      />
    </AuthGuard>
  )
}
