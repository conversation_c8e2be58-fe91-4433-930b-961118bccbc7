(()=>{var e={};e.id=3355,e.ids=[3355],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},64366:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(70260),a=t(28203),l=t(25155),n=t.n(l),i=t(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c=["",{children:["dashboard",{children:["workflows",{children:["monitoring",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,16219)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\workflows\\monitoring\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\workflows\\monitoring\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/workflows/monitoring/page",pathname:"/dashboard/workflows/monitoring",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},32357:(e,s,t)=>{Promise.resolve().then(t.bind(t,16219))},45509:(e,s,t)=>{Promise.resolve().then(t.bind(t,81738))},81738:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var r=t(45512),a=t(43433),l=t(22417),n=t(55131),i=t(58009),d=t(46583),c=t(97832),o=t(78896),x=t(4643),m=t(67418),h=t(92557),u=t(19473),j=t(61075),p=t(80832),f=t(21956),v=t(97643),g=t(87021),b=t(69193),w=t(77252),y=t(3328),N=t(54069),k=t(59462),A=t(16247),C=t(35884),R=t(23081),_=t(97981),P=t(26507),S=t(11452),D=t(1263),W=t(707),Z=t(39947),B=t(46879),T=t(73367),$=t(25197),F=t(98202),q=t(15980),E=t(27143);let U={primary:"#3b82f6",success:"#10b981",warning:"#f59e0b",danger:"#ef4444",info:"#6366f1"};function K(){let[e,s]=(0,i.useState)(null),[t,a]=(0,i.useState)([]),[l,n]=(0,i.useState)([]),[K,O]=(0,i.useState)([]),[M,z]=(0,i.useState)(!0),[H,L]=(0,i.useState)(!1),[G,I]=(0,i.useState)("7d"),[X,V]=(0,i.useState)("all"),[J,Y]=(0,i.useState)("all"),[Q,ee]=(0,i.useState)(null),es=async(e=!1)=>{e?L(!0):z(!0);try{let e=await fetch(`/api/workflows/monitoring/metrics?timeRange=${G}`),t=await e.json();s(t);let r=await fetch(`/api/workflows/monitoring/instances?status=${X}&department=${J}`),l=await r.json();a(l);let i=await fetch(`/api/workflows/monitoring/trends?timeRange=${G}`),d=await i.json();n(d);let c=await fetch(`/api/workflows/monitoring/categories?timeRange=${G}`),o=await c.json();O(o)}catch(e){console.error("Failed to load monitoring data:",e)}finally{z(!1),L(!1)}},et=async()=>{try{let e=await fetch(`/api/workflows/monitoring/export?timeRange=${G}&format=csv`),s=await e.blob(),t=window.URL.createObjectURL(s),r=document.createElement("a");r.href=t,r.download=`workflow-monitoring-${new Date().toISOString()}.csv`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(t),document.body.removeChild(r)}catch(e){console.error("Failed to export data:",e)}},er=e=>{switch(e){case"completed":return(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-500"});case"failed":return(0,r.jsx)(c.A,{className:"h-4 w-4 text-red-500"});case"in_progress":return(0,r.jsx)(o.A,{className:"h-4 w-4 text-blue-500"});case"pending_approval":return(0,r.jsx)(x.A,{className:"h-4 w-4 text-yellow-500"});case"on_hold":return(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-500"});default:return(0,r.jsx)(o.A,{className:"h-4 w-4 text-gray-400"})}},ea=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";case"in_progress":return"bg-blue-100 text-blue-800";case"pending_approval":return"bg-yellow-100 text-yellow-800";case"on_hold":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-600"}},el=e=>{switch(e){case"on_track":return"text-green-600";case"at_risk":return"text-yellow-600";case"breached":return"text-red-600";default:return"text-gray-600"}};return M?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsx)(h.A,{className:"h-8 w-8 animate-spin text-gray-400"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Workflow Monitoring Dashboard"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"ワークフロー監視ダッシュボード"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(N.l6,{value:G,onValueChange:I,children:[(0,r.jsx)(N.bq,{className:"w-32",children:(0,r.jsx)(N.yv,{})}),(0,r.jsxs)(N.gC,{children:[(0,r.jsx)(N.eb,{value:"24h",children:"24 Hours"}),(0,r.jsx)(N.eb,{value:"7d",children:"7 Days"}),(0,r.jsx)(N.eb,{value:"30d",children:"30 Days"}),(0,r.jsx)(N.eb,{value:"90d",children:"90 Days"})]})]}),(0,r.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>es(!0),disabled:H,children:[(0,r.jsx)(h.A,{className:(0,k.cn)("h-4 w-4 mr-2",H&&"animate-spin")}),"Refresh"]}),(0,r.jsxs)(g.$,{variant:"outline",size:"sm",onClick:et,children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(v.ZB,{className:"text-sm font-medium",children:"Total Workflows"}),(0,r.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(v.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e?.total||0}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Active: ",e?.active||0]})]})]}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(v.ZB,{className:"text-sm font-medium",children:"SLA Compliance"}),(0,r.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(v.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[e?.slaCompliance||0,"%"]}),(0,r.jsx)(y.k,{value:e?.slaCompliance||0,className:"mt-2"})]})]}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(v.ZB,{className:"text-sm font-medium",children:"Pending Approval"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(v.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e?.pendingApproval||0}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Overdue: ",e?.overdue||0]})]})]}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(v.ZB,{className:"text-sm font-medium",children:"Throughput"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(v.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e?.throughput.daily||0}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Per day (avg)"})]})]})]}),(0,r.jsxs)(b.tU,{defaultValue:"active",className:"space-y-4",children:[(0,r.jsxs)(b.j7,{children:[(0,r.jsx)(b.Xi,{value:"active",children:"Active Workflows"}),(0,r.jsx)(b.Xi,{value:"trends",children:"Trends"}),(0,r.jsx)(b.Xi,{value:"distribution",children:"Distribution"}),(0,r.jsx)(b.Xi,{value:"performance",children:"Performance"})]}),(0,r.jsx)(b.av,{value:"active",className:"space-y-4",children:(0,r.jsxs)(v.Zp,{children:[(0,r.jsx)(v.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(v.ZB,{children:"Active Workflows"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(N.l6,{value:X,onValueChange:e=>V(e),children:[(0,r.jsx)(N.bq,{className:"w-40",children:(0,r.jsx)(N.yv,{placeholder:"Filter by status"})}),(0,r.jsxs)(N.gC,{children:[(0,r.jsx)(N.eb,{value:"all",children:"All Status"}),(0,r.jsx)(N.eb,{value:"in_progress",children:"In Progress"}),(0,r.jsx)(N.eb,{value:"pending_approval",children:"Pending Approval"}),(0,r.jsx)(N.eb,{value:"on_hold",children:"On Hold"}),(0,r.jsx)(N.eb,{value:"completed",children:"Completed"}),(0,r.jsx)(N.eb,{value:"failed",children:"Failed"})]})]}),(0,r.jsxs)(N.l6,{value:J,onValueChange:Y,children:[(0,r.jsx)(N.bq,{className:"w-40",children:(0,r.jsx)(N.yv,{placeholder:"Filter by department"})}),(0,r.jsxs)(N.gC,{children:[(0,r.jsx)(N.eb,{value:"all",children:"All Departments"}),(0,r.jsx)(N.eb,{value:"IT Systems Department",children:"IT Systems"}),(0,r.jsx)(N.eb,{value:"Human Resources Department",children:"HR"}),(0,r.jsx)(N.eb,{value:"Finance and Accounting Department",children:"Finance"}),(0,r.jsx)(N.eb,{value:"Corporate Planning Department",children:"Planning"})]})]})]})]})}),(0,r.jsx)(v.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer",onClick:()=>ee(e.id),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[er(e.status),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.titleJp}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-xs text-gray-500",children:[(0,r.jsx)("span",{children:e.department}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:e.category}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:(0,A.m)(new Date(e.createdAt),{addSuffix:!0,locale:C.ja})})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-sm",children:["Step ",e.currentStep," of ",e.totalSteps]}),(0,r.jsx)(y.k,{value:e.currentStep/e.totalSteps*100,className:"w-24 h-2 mt-1"})]}),(0,r.jsx)(w.E,{className:(0,k.cn)(el(e.slaStatus)),children:e.slaStatus.replace("_"," ")}),(0,r.jsx)(w.E,{variant:"outline",className:(0,k.cn)(ea(e.status)),children:e.status.replace("_"," ")}),(0,r.jsx)(g.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})]},e.id))})})]})}),(0,r.jsx)(b.av,{value:"trends",children:(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)(v.ZB,{children:"Workflow Trends"}),(0,r.jsx)(v.BT,{children:"Workflow creation and completion trends"})]}),(0,r.jsx)(v.Wu,{children:(0,r.jsx)(R.u,{width:"100%",height:400,children:(0,r.jsxs)(_.b,{data:l,children:[(0,r.jsx)(P.d,{strokeDasharray:"3 3"}),(0,r.jsx)(S.W,{dataKey:"date"}),(0,r.jsx)(D.h,{}),(0,r.jsx)(W.m,{}),(0,r.jsx)(Z.s,{}),(0,r.jsx)(B.N,{type:"monotone",dataKey:"created",stroke:U.primary,name:"Created"}),(0,r.jsx)(B.N,{type:"monotone",dataKey:"completed",stroke:U.success,name:"Completed"}),(0,r.jsx)(B.N,{type:"monotone",dataKey:"failed",stroke:U.danger,name:"Failed"})]})})})]})}),(0,r.jsx)(b.av,{value:"distribution",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)(v.ZB,{children:"Category Distribution"}),(0,r.jsx)(v.BT,{children:"Workflow distribution by category"})]}),(0,r.jsx)(v.Wu,{children:(0,r.jsx)(R.u,{width:"100%",height:300,children:(0,r.jsxs)(T.r,{children:[(0,r.jsx)($.F,{data:K,cx:"50%",cy:"50%",labelLine:!1,label:({category:e,percentage:s})=>`${e} (${s}%)`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:K.map((e,s)=>(0,r.jsx)(F.f,{fill:Object.values(U)[s%Object.values(U).length]},`cell-${s}`))}),(0,r.jsx)(W.m,{})]})})})]}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)(v.ZB,{children:"Status Distribution"}),(0,r.jsx)(v.BT,{children:"Current workflow status breakdown"})]}),(0,r.jsx)(v.Wu,{children:(0,r.jsx)(R.u,{width:"100%",height:300,children:(0,r.jsxs)(q.E,{data:[{status:"Active",count:e?.active||0},{status:"Completed",count:e?.completed||0},{status:"Failed",count:e?.failed||0},{status:"On Hold",count:e?.onHold||0},{status:"Pending",count:e?.pendingApproval||0}],children:[(0,r.jsx)(P.d,{strokeDasharray:"3 3"}),(0,r.jsx)(S.W,{dataKey:"status"}),(0,r.jsx)(D.h,{}),(0,r.jsx)(W.m,{}),(0,r.jsx)(E.y,{dataKey:"count",fill:U.primary})]})})})]})]})}),(0,r.jsx)(b.av,{value:"performance",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)(v.ZB,{children:"Average Completion Time"}),(0,r.jsx)(v.BT,{children:"By workflow category (hours)"})]}),(0,r.jsx)(v.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Password Reset"}),(0,r.jsx)("span",{className:"font-medium",children:"2.5 hrs"})]}),(0,r.jsx)(y.k,{value:25}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Group Mail Management"}),(0,r.jsx)("span",{className:"font-medium",children:"4.8 hrs"})]}),(0,r.jsx)(y.k,{value:48}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"SharePoint Access"}),(0,r.jsx)("span",{className:"font-medium",children:"6.2 hrs"})]}),(0,r.jsx)(y.k,{value:62}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"PC Admin Rights"}),(0,r.jsx)("span",{className:"font-medium",children:"8.0 hrs"})]}),(0,r.jsx)(y.k,{value:80})]})})]}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)(v.ZB,{children:"Performance Metrics"}),(0,r.jsx)(v.BT,{children:"Key performance indicators"})]}),(0,r.jsx)(v.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"First Response Time"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Average time to first action"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:"15 min"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"Approval Time"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Average approval duration"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:"3.2 hrs"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"Automation Rate"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Fully automated workflows"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:"68%"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"Error Rate"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Failed workflows"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:"2.3%"})]})]})})]})]})})]})]})}var O=t(35668),M=t(79334);function z(){let{user:e}=(0,a.A)(),{isGlobalAdmin:s,isSystemAdmin:t,isDepartmentAdmin:i,isITSupport:d}=(0,l.S)(),c=(0,M.useRouter)();return s()||t()||i()||d()?(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-between items-center py-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.$,{variant:"ghost",size:"sm",className:"mr-4",onClick:()=>c.push("/dashboard"),children:(0,r.jsx)(O.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Workflow Monitoring"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"ワークフロー監視ダッシュボード"})]})]})})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsx)(K,{})})]})}):(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"You do not have permission to view workflow monitoring."}),(0,r.jsxs)(g.$,{onClick:()=>c.push("/dashboard"),children:[(0,r.jsx)(O.A,{className:"mr-2 h-4 w-4"}),"Back to Dashboard"]})]})})})}},16219:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\dashboard\\\\workflows\\\\monitoring\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\dashboard\\workflows\\monitoring\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8096,2076],()=>t(64366));module.exports=r})();