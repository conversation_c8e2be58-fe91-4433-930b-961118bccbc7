/**
 * Test Utilities
 * Common utilities and helpers for testing
 */

import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'
import { NextRequest } from 'next/server'

// Mock data generators
export const mockUser = (overrides: Partial<any> = {}) => ({
  id: '12345678-1234-1234-1234-123456789012',
  email: '<EMAIL>',
  user_metadata: {},
  app_metadata: {},
  aud: 'authenticated',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

export const mockProfile = (overrides: Partial<any> = {}) => ({
  user_id: '12345678-1234-1234-1234-123456789012',
  full_name: 'Test User',
  role: 'user',
  organization_id: '12345678-1234-1234-1234-123456789012',
  division_id: null,
  group_id: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

export const mockTicket = (overrides: Partial<any> = {}) => ({
  id: '12345678-1234-1234-1234-123456789012',
  title: 'Test Ticket',
  description: 'This is a test ticket',
  status: 'open',
  priority: 'medium',
  category: 'technical',
  requester_id: '12345678-1234-1234-1234-123456789012',
  assignee_id: null,
  organization_id: '12345678-1234-1234-1234-123456789012',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

export const mockOrganization = (overrides: Partial<any> = {}) => ({
  id: '12345678-1234-1234-1234-123456789012',
  name: 'Test Organization',
  domain: 'test.com',
  settings: {},
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

// Request builders
export const createMockRequest = (
  url: string,
  options: {
    method?: string
    headers?: Record<string, string>
    body?: any
    searchParams?: Record<string, string>
  } = {}
): NextRequest => {
  const { method = 'GET', headers = {}, body, searchParams } = options
  
  let fullUrl = url
  if (searchParams) {
    const params = new URLSearchParams(searchParams)
    fullUrl += `?${params.toString()}`
  }

  const requestInit: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  }

  if (body && method !== 'GET') {
    requestInit.body = typeof body === 'string' ? body : JSON.stringify(body)
  }

  return new NextRequest(fullUrl, requestInit)
}

export const createAuthenticatedRequest = (
  url: string,
  token: string = 'mock-jwt-token',
  options: Parameters<typeof createMockRequest>[1] = {}
): NextRequest => {
  return createMockRequest(url, {
    ...options,
    headers: {
      'Authorization': `Bearer ${token}`,
      ...options.headers
    }
  })
}

// Supabase mock helpers
export const createMockSupabaseClient = (overrides: any = {}) => ({
  auth: {
    getUser: jest.fn().mockResolvedValue({
      data: { user: mockUser() },
      error: null
    }),
    getSession: jest.fn().mockResolvedValue({
      data: { session: { user: mockUser() } },
      error: null
    }),
    signInWithPassword: jest.fn().mockResolvedValue({
      data: { user: mockUser() },
      error: null
    }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
    onAuthStateChange: jest.fn().mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } }
    }),
    ...overrides.auth
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    then: jest.fn((callback) => callback({ data: [], error: null })),
    ...overrides.from
  })),
  rpc: jest.fn().mockResolvedValue({ data: null, error: null }),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn().mockResolvedValue({ data: null, error: null }),
      download: jest.fn().mockResolvedValue({ data: null, error: null }),
      remove: jest.fn().mockResolvedValue({ data: null, error: null }),
      list: jest.fn().mockResolvedValue({ data: [], error: null }),
      getPublicUrl: jest.fn().mockReturnValue({
        data: { publicUrl: 'https://example.com/file.jpg' }
      })
    }))
  },
  ...overrides
})

// Test environment helpers
export const setupTestEnvironment = () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-project.supabase.co'
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key'
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'
  process.env.JWT_SECRET = 'test-jwt-secret-32-characters-long'
  process.env.OPENAI_API_KEY = 'sk-test-openai-key'
  process.env.ANTHROPIC_API_KEY = 'sk-ant-test-anthropic-key'
}

export const cleanupTestEnvironment = () => {
  // Reset mocks
  jest.clearAllMocks()
  
  // Clear any test data
  if (typeof window !== 'undefined') {
    window.localStorage.clear()
    window.sessionStorage.clear()
  }
}

// Performance testing helpers
export const measurePerformance = async <T>(
  operation: () => Promise<T> | T,
  name: string = 'operation'
): Promise<{ result: T; duration: number }> => {
  const start = performance.now()
  const result = await operation()
  const end = performance.now()
  const duration = end - start
  
  console.log(`${name} took ${duration.toFixed(2)}ms`)
  
  return { result, duration }
}

export const expectPerformance = (
  duration: number,
  maxDuration: number,
  operation: string = 'Operation'
) => {
  if (duration > maxDuration) {
    throw new Error(
      `${operation} took ${duration.toFixed(2)}ms, expected less than ${maxDuration}ms`
    )
  }
}

// Security testing helpers
export const createMaliciousPayloads = () => ({
  xss: [
    '<script>alert("xss")</script>',
    'javascript:alert("xss")',
    '<img src="x" onerror="alert(\'xss\')" />',
    '"><script>alert("xss")</script>'
  ],
  sqlInjection: [
    "'; DROP TABLE users; --",
    "' OR '1'='1",
    "1; DELETE FROM users WHERE 1=1; --",
    "' UNION SELECT * FROM users --"
  ],
  pathTraversal: [
    '../../../etc/passwd',
    '..\\..\\..\\windows\\system32\\config\\sam',
    '....//....//....//etc/passwd',
    '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
  ],
  oversizedInput: 'A'.repeat(10000),
  nullBytes: 'test\x00.txt',
  unicodeBypass: '\u003cscript\u003ealert("xss")\u003c/script\u003e'
})

export const testSecurityPayload = async (
  endpoint: (payload: string) => Promise<any>,
  payloads: string[],
  expectation: 'reject' | 'sanitize' = 'reject'
) => {
  for (const payload of payloads) {
    try {
      const result = await endpoint(payload)
      
      if (expectation === 'reject') {
        // Should have been rejected
        expect(result.status).toBeGreaterThanOrEqual(400)
      } else if (expectation === 'sanitize') {
        // Should be sanitized (not contain original payload)
        expect(JSON.stringify(result)).not.toContain(payload)
      }
    } catch (error) {
      // Errors are acceptable for malicious payloads
      expect(error).toBeDefined()
    }
  }
}

// Database testing helpers
export const createTestDatabase = () => {
  // This would set up a test database instance
  // For now, we'll use mocks
  return createMockSupabaseClient()
}

export const seedTestData = async (supabase: any) => {
  // Seed test data
  const org = mockOrganization()
  const user = mockUser()
  const profile = mockProfile({ organization_id: org.id })
  const ticket = mockTicket({ 
    organization_id: org.id, 
    requester_id: user.id 
  })

  // Mock the insertions
  supabase.from().insert.mockResolvedValueOnce({ data: org, error: null })
  supabase.from().insert.mockResolvedValueOnce({ data: profile, error: null })
  supabase.from().insert.mockResolvedValueOnce({ data: ticket, error: null })

  return { org, user, profile, ticket }
}

// Custom render function for React components
export const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return render(ui, {
    // Add any providers here (ThemeProvider, etc.)
    ...options
  })
}

// Wait utilities
export const waitFor = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export const waitForCondition = async (
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> => {
  const start = Date.now()
  
  while (Date.now() - start < timeout) {
    if (await condition()) {
      return
    }
    await waitFor(interval)
  }
  
  throw new Error(`Condition not met within ${timeout}ms`)
}

// Export everything for easy importing
export * from '@testing-library/react'
export * from '@testing-library/jest-dom'
export { customRender as render }
