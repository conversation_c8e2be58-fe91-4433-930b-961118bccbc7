// Service category and request types
export interface ServiceCategory {
  id: string
  name_jp: string
  name_en: string
  code: string
  description?: string
  is_department_specific: boolean
  form_schema: any // JSON schema
  is_active: boolean
}

// User selection types
export interface SelectedUser {
  id: string
  name_jp: string
  name_en: string
  email: string
  staff_id: string
  pc_id?: string
  division: {
    id: string
    name_jp: string
    name_en: string
  }
  group?: {
    id: string
    name_jp: string
    name_en: string
  }
}

// Request types
export interface RequestItem {
  serviceCategory: ServiceCategory
  users: SelectedUser[]
  formData: Record<string, any>
  action: 'add' | 'remove' | 'update'
}

export interface RequestConfirmation {
  items: RequestItem[]
  status: 'draft' | 'ready' | 'submitting' | 'submitted'
  totalUsers: number
  totalServices: number
}
