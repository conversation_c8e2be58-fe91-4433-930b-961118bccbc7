/**
 * Backup Restoration API
 * Handles restoration from specific backup
 */

import { NextRequest, NextResponse } from 'next/server'
import { backupManager } from '@/lib/backup/backup-manager'
import { createClient } from '@/lib/supabase/server'

interface RestoreParams {
  params: {
    id: string
  }
}

/**
 * Restore from backup
 */
export async function POST(request: NextRequest, { params }: RestoreParams): Promise<NextResponse> {
  try {
    // Check authentication and authorization
    const authResult = await checkAdminAuth()
    if (authResult) return authResult

    const backupId = params.id
    const body = await request.json()
    const { 
      verify_checksum = true, 
      dry_run = false, 
      tables 
    } = body

    // Perform restoration
    const result = await backupManager.restoreFromBackup(backupId, {
      verify_checksum,
      dry_run,
      tables
    })

    return NextResponse.json({
      success: result.success,
      message: result.message,
      restored_records: result.restored_records
    })

  } catch (error) {
    console.error('Backup restoration failed:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * Check admin authentication
 */
async function checkAdminAuth(): Promise<NextResponse | null> {
  const supabase = createClient()
  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    )
  }

  // Check if user has admin role
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('role')
    .eq('user_id', user.id)
    .single()

  if (!profile || !['admin', 'system_admin'].includes(profile.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }

  return null
}
