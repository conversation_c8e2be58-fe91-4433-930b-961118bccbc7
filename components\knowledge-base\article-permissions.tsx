'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/use-auth'
import { KnowledgeBasePermissionsService } from '@/lib/services/kb-permissions-service'
import { supabase } from '@/lib/supabase'
import { Loader2, Shield, Users, AlertCircle, Save } from 'lucide-react'
import type { Database } from '@/lib/database.types'

type Role = Database['public']['Tables']['roles']['Row']
type ArticleVisibility = 'public' | 'internal' | 'restricted'

interface ArticlePermissionsProps {
  articleId: string
  currentVisibility: ArticleVisibility
  onVisibilityChange?: (visibility: ArticleVisibility) => void
}

export function ArticlePermissions({ 
  articleId, 
  currentVisibility,
  onVisibilityChange 
}: ArticlePermissionsProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [visibility, setVisibility] = useState<ArticleVisibility>(currentVisibility)
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Record<string, { canView: boolean; canEdit: boolean }>>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [isDirty, setIsDirty] = useState(false)

  // Fetch roles and current permissions
  useEffect(() => {
    const fetchData = async () => {
      if (!user?.id) return

      try {
        // Fetch all roles
        const { data: rolesData, error: rolesError } = await supabase
          .from('roles')
          .select('*')
          .order('name')

        if (rolesError) throw rolesError

        setRoles(rolesData || [])

        // Fetch current permissions for this article
        const articlePerms = await KnowledgeBasePermissionsService.getArticlePermissions(articleId)
        
        const permsMap: Record<string, { canView: boolean; canEdit: boolean }> = {}
        articlePerms.forEach(perm => {
          permsMap[perm.role_id] = {
            canView: perm.can_view,
            canEdit: perm.can_edit
          }
        })
        
        setPermissions(permsMap)
      } catch (error) {
        console.error('Error fetching permissions data:', error)
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load permissions data'
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [articleId, user?.id, toast])

  const handleVisibilityChange = (newVisibility: ArticleVisibility) => {
    setVisibility(newVisibility)
    setIsDirty(true)
    
    // Clear permissions if changing to public or internal
    if (newVisibility !== 'restricted') {
      setPermissions({})
    }
  }

  const handlePermissionChange = (
    roleId: string, 
    type: 'canView' | 'canEdit', 
    value: boolean
  ) => {
    setPermissions(prev => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [type]: value,
        // If granting edit, also grant view
        canView: type === 'canEdit' && value ? true : (prev[roleId]?.canView ?? false)
      }
    }))
    setIsDirty(true)
  }

  const handleSave = async () => {
    if (!user?.id) return

    setSaving(true)
    try {
      // Update article visibility
      const { error: updateError } = await supabase
        .from('kb_articles')
        .update({ visibility })
        .eq('id', articleId)

      if (updateError) throw updateError

      // Update permissions if visibility is restricted
      if (visibility === 'restricted') {
        // Set permissions for each role
        const promises = Object.entries(permissions).map(([roleId, perms]) => 
          KnowledgeBasePermissionsService.setArticlePermissions(
            articleId,
            roleId,
            {
              can_view: perms.canView,
              can_edit: perms.canEdit
            }
          )
        )

        await Promise.all(promises)
      } else {
        // Clear all permissions if not restricted
        const deletePromises = roles.map(role => 
          KnowledgeBasePermissionsService.removeArticlePermissions(articleId, role.id)
        )
        await Promise.all(deletePromises)
      }

      toast({
        title: 'Success',
        description: 'Article permissions updated successfully'
      })

      setIsDirty(false)
      onVisibilityChange?.(visibility)
    } catch (error) {
      console.error('Error saving permissions:', error)
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save permissions'
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Article Permissions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-40 w-full" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Article Permissions
        </CardTitle>
        <CardDescription>
          Control who can view and edit this article
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Visibility Level */}
        <div className="space-y-2">
          <Label htmlFor="visibility">Visibility Level</Label>
          <Select
            value={visibility}
            onValueChange={(value) => handleVisibilityChange(value as ArticleVisibility)}
          >
            <SelectTrigger id="visibility">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="public">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Public</Badge>
                  <span>Visible to everyone</span>
                </div>
              </SelectItem>
              <SelectItem value="internal">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Internal</Badge>
                  <span>Visible to authenticated users</span>
                </div>
              </SelectItem>
              <SelectItem value="restricted">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Restricted</Badge>
                  <span>Visible to specific roles only</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Role-based Permissions (only for restricted visibility) */}
        {visibility === 'restricted' && (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>Configure permissions for each role</span>
            </div>

            <ScrollArea className="h-[300px] rounded-md border p-4">
              <div className="space-y-4">
                {roles.map(role => {
                  const rolePerms = permissions[role.id] || { canView: false, canEdit: false }
                  
                  return (
                    <div key={role.id} className="space-y-3 border-b pb-4 last:border-0">
                      <div className="font-medium">{role.name}</div>
                      {role.description && (
                        <p className="text-sm text-muted-foreground">
                          {role.description}
                        </p>
                      )}
                      
                      <div className="flex gap-6">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id={`${role.id}-view`}
                            checked={rolePerms.canView}
                            onCheckedChange={(checked) => 
                              handlePermissionChange(role.id, 'canView', checked)
                            }
                          />
                          <Label 
                            htmlFor={`${role.id}-view`}
                            className="text-sm font-normal cursor-pointer"
                          >
                            Can View
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            id={`${role.id}-edit`}
                            checked={rolePerms.canEdit}
                            onCheckedChange={(checked) => 
                              handlePermissionChange(role.id, 'canEdit', checked)
                            }
                          />
                          <Label 
                            htmlFor={`${role.id}-edit`}
                            className="text-sm font-normal cursor-pointer"
                          >
                            Can Edit
                          </Label>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </ScrollArea>

            {visibility === 'restricted' && Object.keys(permissions).length === 0 && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No permissions set</AlertTitle>
                <AlertDescription>
                  No roles have permission to view this article. Make sure to grant at least view permission to some roles.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={!isDirty || saving}
          >
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Permissions
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
