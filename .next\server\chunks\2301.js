"use strict";exports.id=2301,exports.ids=[2301],exports.modules={72301:(e,s,t)=>{t.d(s,{G:()=>k});var r=t(45512),a=t(58009),i=t(87021),n=t(25409),o=t(666),l=t(97643),c=t(77252),d=t(31575),u=t(44269),m=t(6841),f=t(46904),x=t(41441),h=t(86235),p=t(4269),v=t(15607),g=t(59462),b=t(37133);class j{constructor(){this.context={}}static getInstance(){return j.instance||(j.instance=new j),j.instance}setContext(e){this.context={...this.context,...e}}getContext(){return this.context}async sendMessage(e){try{let{data:s}=await b.N.auth.getSession();if(!s?.session)throw Error("User not authenticated");let t=await b.N.functions.invoke("chatbot-assistant",{body:{conversationId:this.conversationId,message:e,context:this.context}});if(t.error)throw t.error;let r=t.data;return this.conversationId=r.conversationId,r}catch(e){throw console.error("Chatbot service error:",e),e}}async getConversationHistory(){if(!this.conversationId)return[];let{data:e,error:s}=await b.N.from("chatbot_messages").select("*").eq("conversation_id",this.conversationId).order("created_at",{ascending:!0});return s?(console.error("Error fetching conversation history:",s),[]):e||[]}async searchFAQs(e){let{data:s,error:t}=await b.N.from("chatbot_faq").select("*").eq("is_active",!0).textSearch("keywords",e.split(" ").join(" | ")).limit(5);return t?(console.error("Error searching FAQs:",t),[]):s||[]}async getTutorials(e){let s=b.N.from("chatbot_tutorials").select("*").eq("is_active",!0);e&&(s=s.eq("category",e));let{data:t,error:r}=await s.limit(5);return r?(console.error("Error fetching tutorials:",r),[]):t||[]}async submitFeedback(e,s,t){let{data:r}=await b.N.auth.getSession();if(!r?.session)throw Error("User not authenticated");let{error:a}=await b.N.from("chatbot_feedback").insert({message_id:e,user_id:r.session.user.id,rating:s,feedback_text:t});if(a)throw console.error("Error submitting feedback:",a),a}async clearConversation(){this.conversationId=void 0,this.context={}}async archiveConversation(){if(!this.conversationId)return;let{error:e}=await b.N.from("chatbot_conversations").update({is_archived:!0}).eq("id",this.conversationId);if(e)throw console.error("Error archiving conversation:",e),e;this.conversationId=void 0}}let N=j.getInstance();var y=t(43433),w=t(69193),_=t(65518);function k({className:e,defaultOpen:s=!1,currentForm:t,currentPage:b}){let[j,k]=(0,a.useState)(s),[C,I]=(0,a.useState)([]),[A,R]=(0,a.useState)(""),[S,F]=(0,a.useState)(!1),[q,z]=(0,a.useState)([]),[E,U]=(0,a.useState)([]),[$,L]=(0,a.useState)([]),M=(0,a.useRef)(null),{user:V}=(0,y.A)(),X=async(e=A)=>{if(!e.trim()||S)return;let s={role:"user",content:e,timestamp:new Date};I(e=>[...e,s]),R(""),F(!0);try{let s=await N.sendMessage(e),t={role:"assistant",content:s.response,timestamp:new Date};I(e=>[...e,t]),z(s.suggestions||[]),U(s.faqs||[]),L(s.tutorials||[])}catch(e){console.error("Failed to send message:",e),(0,_.toast)({title:"エラー",description:"メッセージの送信に失敗しました。",variant:"destructive"})}finally{F(!1)}},B=e=>{X(e)},D=async(e,s)=>{(0,_.toast)({title:"フィードバックありがとうございます",description:"あなたのフィードバックは改善に役立ちます。"})};return j?(0,r.jsxs)(l.Zp,{className:(0,g.cn)("fixed bottom-4 right-4 w-96 h-[600px] shadow-xl","flex flex-col",e),children:[(0,r.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[(0,r.jsx)(l.ZB,{className:"text-lg font-semibold",children:"ITヘルプデスクアシスタント"}),(0,r.jsx)(i.$,{onClick:()=>k(!1),size:"icon",variant:"ghost",className:"h-8 w-8",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(l.Wu,{className:"flex-1 overflow-hidden p-0",children:(0,r.jsxs)(w.tU,{defaultValue:"chat",className:"h-full",children:[(0,r.jsxs)(w.j7,{className:"grid w-full grid-cols-3 px-4",children:[(0,r.jsx)(w.Xi,{value:"chat",children:"チャット"}),(0,r.jsx)(w.Xi,{value:"faq",children:"FAQ"}),(0,r.jsx)(w.Xi,{value:"tutorials",children:"チュートリアル"})]}),(0,r.jsxs)(w.av,{value:"chat",className:"h-[calc(100%-48px)] p-0",children:[(0,r.jsxs)(o.F,{className:"h-[calc(100%-120px)] px-4 py-2",children:[0===C.length&&(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,r.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),"                  ",(0,r.jsxs)("p",{className:"text-sm",children:["こんにちは！ITヘルプデスクアシスタントです。",(0,r.jsx)("br",{}),"何かお手伝いできることはありますか？"]})]}),C.map((e,s)=>(0,r.jsx)("div",{className:(0,g.cn)("mb-4 flex","user"===e.role?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:(0,g.cn)("rounded-lg px-4 py-2 max-w-[80%]","user"===e.role?"bg-primary text-primary-foreground":"bg-muted"),children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}),"assistant"===e.role&&(0,r.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,r.jsx)(i.$,{size:"icon",variant:"ghost",className:"h-6 w-6",onClick:()=>D(s,1),children:(0,r.jsx)(f.A,{className:"h-3 w-3"})}),(0,r.jsx)(i.$,{size:"icon",variant:"ghost",className:"h-6 w-6",onClick:()=>D(s,-1),children:(0,r.jsx)(x.A,{className:"h-3 w-3"})})]})]})},s)),S&&(0,r.jsx)("div",{className:"flex justify-start mb-4",children:(0,r.jsx)("div",{className:"bg-muted rounded-lg px-4 py-2",children:(0,r.jsx)(h.A,{className:"h-4 w-4 animate-spin"})})}),(0,r.jsx)("div",{ref:M})]}),q.length>0&&(0,r.jsxs)("div",{className:"px-4 py-2 border-t",children:[(0,r.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:"提案:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:q.map((e,s)=>(0,r.jsx)(c.E,{variant:"secondary",className:"cursor-pointer hover:bg-secondary/80",onClick:()=>B(e),children:e},s))})]})]}),(0,r.jsx)(w.av,{value:"faq",className:"h-[calc(100%-48px)] p-4",children:(0,r.jsx)(o.F,{className:"h-full",children:E.map((e,s)=>(0,r.jsx)(l.Zp,{className:"mb-3 cursor-pointer hover:bg-accent/50",children:(0,r.jsxs)(l.Wu,{className:"p-3",children:[(0,r.jsx)("p",{className:"font-medium text-sm mb-1",children:e.question_ja}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.answer_ja})]})},s))})}),(0,r.jsx)(w.av,{value:"tutorials",className:"h-[calc(100%-48px)] p-4",children:(0,r.jsx)(o.F,{className:"h-full",children:$.map((e,s)=>(0,r.jsxs)(l.Zp,{className:"mb-3",children:["                  ",(0,r.jsx)(l.Wu,{className:"p-3",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mt-0.5 text-primary"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.title_ja}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.description_ja}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["所要時間: ",e.estimated_time,"分"]})]})]})})]},s))})})]})}),(0,r.jsx)(l.wL,{className:"p-4 border-t",children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),X()},className:"flex w-full gap-2",children:[(0,r.jsx)(n.p,{value:A,onChange:e=>R(e.target.value),placeholder:"メッセージを入力...",disabled:S,className:"flex-1"}),(0,r.jsx)(i.$,{type:"submit",size:"icon",disabled:S||!A.trim(),children:S?(0,r.jsx)(h.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(v.A,{className:"h-4 w-4"})})]})})]}):(0,r.jsx)(i.$,{onClick:()=>k(!0),className:(0,g.cn)("fixed bottom-4 right-4 rounded-full w-14 h-14 shadow-lg","bg-primary hover:bg-primary/90",e),size:"icon",children:(0,r.jsx)(d.A,{className:"h-6 w-6"})})}},77252:(e,s,t)=>{t.d(s,{E:()=>o});var r=t(45512);t(58009);var a=t(21643),i=t(59462);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:s,...t}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},87021:(e,s,t)=>{t.d(s,{$:()=>c,r:()=>l});var r=t(45512),a=t(58009),i=t(12705),n=t(21643),o=t(59462);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...n},c)=>{let d=a?i.DX:"button";return(0,r.jsx)(d,{className:(0,o.cn)(l({variant:s,size:t,className:e})),ref:c,...n})});c.displayName="Button"},25409:(e,s,t)=>{t.d(s,{p:()=>n});var r=t(45512),a=t(58009),i=t(59462);let n=a.forwardRef(({className:e,type:s,...t},a)=>(0,r.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Input"},666:(e,s,t)=>{t.d(s,{F:()=>o});var r=t(45512),a=t(58009),i=t(53998),n=t(59462);let o=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.bL,{ref:a,className:(0,n.cn)("relative overflow-hidden",e),...t,children:[(0,r.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,r.jsx)(l,{}),(0,r.jsx)(i.OK,{})]}));o.displayName=i.bL.displayName;let l=a.forwardRef(({className:e,orientation:s="vertical",...t},a)=>(0,r.jsx)(i.VM,{ref:a,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 border-t border-t-transparent p-[1px]",e),...t,children:(0,r.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=i.VM.displayName},69193:(e,s,t)=>{t.d(s,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>o});var r=t(45512),a=t(58009),i=t(55613),n=t(59462);let o=i.bL,l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));l.displayName=i.B8.displayName;let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));c.displayName=i.l9.displayName;let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));d.displayName=i.UC.displayName},65518:(e,s,t)=>{t.d(s,{d:()=>i});var r=t(58009),a=t(91542);function i(){let[e,s]=(0,r.useState)(!1);return e?{toast:({title:e,description:s,action:t,variant:r,duration:i})=>("destructive"===r?a.oR.error:"success"===r?a.oR.success:a.oR)(e,{description:s,action:t,duration:i||5e3}),dismiss:a.oR.dismiss}:{toast:e=>{},dismiss:e=>{}}}},37133:(e,s,t)=>{t.d(s,{N:()=>r});let r=(0,t(93939).createClient)("your_supabase_project_url","your_supabase_anon_key")}};