(()=>{var e={};e.id=6746,e.ids=[6746],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},85928:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var t=s(70260),a=s(28203),i=s(25155),n=s.n(i),o=s(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let c=["",{children:["setup-required",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,91954)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\setup-required\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\setup-required\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/setup-required/page",pathname:"/setup-required",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74310:(e,r,s)=>{Promise.resolve().then(s.bind(s,91954))},44046:(e,r,s)=>{Promise.resolve().then(s.bind(s,75088))},75088:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(45512),a=s(97643),i=s(75339),n=s(87021),o=s(92056),l=s(12592),c=s(42163),d=s(97832),p=s(58009);function u(){let[e,r]=(0,p.useState)(!1),s=e=>{navigator.clipboard.writeText(e),r(!0),setTimeout(()=>r(!1),2e3)},u=`# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# API Keys for AI Integration
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key`;return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950 p-4",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6 pt-10",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold mb-2",children:"ITSync Setup Required"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400",children:"Your environment is not properly configured. Follow the steps below to get started."})]}),(0,t.jsxs)(i.Fc,{className:"border-red-500 bg-red-50 dark:bg-red-950",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(i.XL,{children:"Environment Configuration Missing"}),(0,t.jsx)(i.TN,{children:"The Supabase environment variables are not configured. This is required for the application to function."})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Step 1: Create a Supabase Project"}),(0,t.jsx)(a.BT,{children:"If you haven't already, create a free Supabase project"})]}),(0,t.jsx)(a.Wu,{className:"space-y-4",children:(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-2",children:[(0,t.jsxs)("li",{children:["Go to ",(0,t.jsxs)("a",{href:"https://app.supabase.com",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline inline-flex items-center gap-1",children:["app.supabase.com ",(0,t.jsx)(c.A,{className:"h-3 w-3"})]})]}),(0,t.jsx)("li",{children:"Sign up or log in to your account"}),(0,t.jsx)("li",{children:'Click "New Project" and fill in the details'}),(0,t.jsx)("li",{children:"Wait for the project to be created (this may take a few minutes)"})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Step 2: Get Your Project Credentials"}),(0,t.jsx)(a.BT,{children:"Once your project is created, get the required credentials"})]}),(0,t.jsx)(a.Wu,{className:"space-y-4",children:(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-2",children:[(0,t.jsx)("li",{children:"Go to Settings → API in your Supabase project"}),(0,t.jsx)("li",{children:'Copy the "Project URL" - this is your NEXT_PUBLIC_SUPABASE_URL'}),(0,t.jsx)("li",{children:'Copy the "anon public" key - this is your NEXT_PUBLIC_SUPABASE_ANON_KEY'}),(0,t.jsx)("li",{children:'Copy the "service_role" key - this is your SUPABASE_SERVICE_ROLE_KEY'})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Step 3: Create Environment File"}),(0,t.jsx)(a.BT,{children:"Create a .env.local file in your project root with your credentials"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("pre",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto",children:(0,t.jsx)("code",{children:u})}),(0,t.jsx)(n.$,{size:"sm",variant:"secondary",className:"absolute top-2 right-2",onClick:()=>s(u),children:e?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)(i.Fc,{children:[(0,t.jsx)(i.XL,{children:"Important"}),(0,t.jsx)(i.TN,{children:"Replace the placeholder values with your actual credentials from Supabase. Never commit the .env.local file to version control."})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Step 4: Run Database Migrations"}),(0,t.jsx)(a.BT,{children:"Set up the database schema for ITSync"})]}),(0,t.jsx)(a.Wu,{className:"space-y-4",children:(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-2",children:[(0,t.jsx)("li",{children:"Open the SQL Editor in your Supabase project"}),(0,t.jsx)("li",{children:"Copy and run the migration scripts from the /supabase/migrations folder"}),(0,t.jsx)("li",{children:"Verify that all tables are created successfully"})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Step 5: Restart the Application"}),(0,t.jsx)(a.BT,{children:"After configuring the environment, restart your development server"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg",children:(0,t.jsx)("code",{children:"npm run dev"})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"The application should now connect to your Supabase project successfully."})]})]}),(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Need help? Check out the documentation or contact support."}),(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,t.jsx)("a",{href:"https://supabase.com/docs",target:"_blank",rel:"noopener noreferrer",children:"Supabase Docs"})}),(0,t.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,t.jsx)("a",{href:"/docs",target:"_blank",rel:"noopener noreferrer",children:"ITSync Docs"})})]})]})]})})}},91954:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\setup-required\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\setup-required\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8096,2076],()=>s(85928));module.exports=t})();