import { createClient } from '@/lib/supabase';
import type { 
  Pc<PERSON>set, 
  PcAdminRequest, 
  PcSearchResult, 
  SoftwareCatalog,
  PcAdminRequestForm,
  ProcessRequestResult
} from '@/lib/pc-admin-types';

export class PcAdminService {
  private supabase = createClient();

  /**
   * Search for PC assets by ID
   */
  async searchPcById(searchTerm: string): Promise<PcSearchResult[]> {
    if (!searchTerm || searchTerm.length < 2) {
      return [];
    }

    const { data, error } = await this.supabase
      .rpc('search_pc_by_id', { search_term: searchTerm });

    if (error) {
      console.error('Error searching PC:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get all PC assets for a department
   */
  async getPcAssetsByDepartment(departmentId: string): Promise<PcAsset[]> {
    const { data, error } = await this.supabase
      .from('pc_assets')
      .select('*')
      .eq('department_id', departmentId)
      .eq('status', 'active')
      .order('pc_id');

    if (error) {
      console.error('Error fetching PC assets:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get PC asset details
   */
  async getPcAsset(pcId: string): Promise<PcAsset | null> {
    const { data, error } = await this.supabase
      .from('pc_assets')
      .select('*')
      .eq('pc_id', pcId)
      .single();

    if (error) {
      console.error('Error fetching PC asset:', error);
      return null;
    }

    return data;
  }

  /**
   * Create PC admin request
   */
  async createPcAdminRequest(
    requesterId: string,
    request: PcAdminRequestForm
  ): Promise<{ success: boolean; request?: PcAdminRequest; error?: string }> {
    try {
      // First, find the PC asset
      const pcAsset = await this.getPcAsset(request.pc_id_search);
      
      if (!pcAsset) {
        return {
          success: false,
          error: 'PC not found with ID: ' + request.pc_id_search
        };
      }

      // Check if user already has the requested status
      if (request.action_type === 'grant_admin' && pcAsset.admin_enabled) {
        return {
          success: false,
          error: 'This PC already has administrative privileges'
        };
      }

      if (request.action_type === 'revoke_admin' && !pcAsset.admin_enabled) {
        return {
          success: false,
          error: 'This PC does not have administrative privileges'
        };
      }

      // Create the request
      const { data, error } = await this.supabase
        .from('pc_admin_requests')
        .insert({
          requester_id: requesterId,
          pc_asset_id: pcAsset.id,
          pc_id_search: request.pc_id_search,
          action_type: request.action_type,
          reason: request.reason,
          software_to_install: request.software_to_install,
          priority: request.priority || 'medium',
          status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating PC admin request:', error);
        return {
          success: false,
          error: 'Failed to create request: ' + error.message
        };
      }

      return {
        success: true,
        request: data
      };
    } catch (error) {
      console.error('Exception creating PC admin request:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }

  /**
   * Get PC admin requests
   */
  async getPcAdminRequests(filter?: {
    requester_id?: string;
    status?: string;
    department_id?: string;
  }): Promise<PcAdminRequest[]> {
    let query = this.supabase
      .from('pc_admin_requests')
      .select(`
        *,
        pc_assets!pc_asset_id (
          pc_id,
          model,
          department_id
        ),
        staff!requester_id (
          name_jp,
          name_en,
          email
        )
      `)
      .order('created_at', { ascending: false });

    if (filter?.requester_id) {
      query = query.eq('requester_id', filter.requester_id);
    }
    if (filter?.status) {
      query = query.eq('status', filter.status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching PC admin requests:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Process PC admin request (approve/reject)
   */
  async processPcAdminRequest(
    requestId: string,
    approved: boolean,
    approverId: string,
    notes?: string
  ): Promise<ProcessRequestResult> {
    const { data, error } = await this.supabase
      .rpc('process_pc_admin_request', {
        p_request_id: requestId,
        p_approved: approved,
        p_approver_id: approverId,
        p_notes: notes
      });

    if (error) {
      console.error('Error processing request:', error);
      return {
        success: false,
        message: 'Failed to process request: ' + error.message
      };
    }

    return data;
  }

  /**
   * Get software catalog
   */
  async getSoftwareCatalog(requiresAdmin?: boolean): Promise<SoftwareCatalog[]> {
    let query = this.supabase
      .from('software_catalog')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (requiresAdmin !== undefined) {
      query = query.eq('requires_admin', requiresAdmin);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching software catalog:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get PC admin history
   */
  async getPcAdminHistory(pcAssetId: string) {
    const { data, error } = await this.supabase
      .from('pc_admin_history')
      .select(`
        *,
        staff!user_id (
          name_jp,
          name_en
        ),
        performed_by:staff!performed_by (
          name_jp,
          name_en
        )
      `)
      .eq('pc_asset_id', pcAssetId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching PC admin history:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Subscribe to PC admin request updates
   */
  subscribeToPcAdminRequests(
    callback: (payload: any) => void,
    filter?: { requester_id?: string }
  ) {
    const channel = this.supabase
      .channel('pc-admin-requests')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'pc_admin_requests',
          filter: filter?.requester_id ? `requester_id=eq.${filter.requester_id}` : undefined
        },
        callback
      )
      .subscribe();

    return () => {
      this.supabase.removeChannel(channel);
    };
  }
}

export const pcAdminService = new PcAdminService();
