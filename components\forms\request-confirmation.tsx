'use client'

import { useState } from 'react'
import { RequestItem, RequestConfirmation } from '@/lib/request-types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Loader2, CheckCircle, Users, FileText, Mail, FolderOpen, Monitor, Key } from 'lucide-react'

interface RequestConfirmationProps {
  confirmation: RequestConfirmation
  onSubmit: () => void
  onEdit: () => void
  onCancel: () => void
  loading?: boolean
}

const serviceIcons: Record<string, any> = {
  'GM': Mail,
  'MB': Mail,
  'SPO': FolderOpen,
  'PCA': Monitor,
  'PWR': Key,
  'default': FileText
}

export default function RequestConfirmationComponent({
  confirmation,
  onSubmit,
  onEdit,
  onCancel,
  loading = false
}: RequestConfirmationProps) {
  const [activeTab, setActiveTab] = useState<string>(
    confirmation.items.length > 0 ? confirmation.items[0].serviceCategory.id : ''
  )

  const renderFormData = (formData: Record<string, any>) => {
    return Object.entries(formData).map(([key, value]) => (
      <div key={key} className="flex justify-between py-1">
        <span className="text-sm text-gray-600 capitalize">
          {key.replace(/_/g, ' ')}:
        </span>
        <span className="text-sm font-medium">
          {Array.isArray(value) ? value.join(', ') : String(value)}
        </span>
      </div>
    ))
  }

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'add':
        return 'default'
      case 'remove':
        return 'destructive'
      case 'update':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getActionText = (action: string) => {
    switch (action) {
      case 'add':
        return '追加 / Add'
      case 'remove':
        return '削除 / Remove'
      case 'update':
        return '更新 / Update'
      default:
        return action
    }
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
          リクエスト確認 / Request Confirmation
        </CardTitle>
        <CardDescription>
          以下の内容でリクエストを送信してもよろしいですか？
          <br />
          Please review and confirm your request details below.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <FileText className="h-5 w-5 text-blue-500 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">サービス / Services</div>
                  <div className="font-semibold">{confirmation.totalServices}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-green-500 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">ユーザー / Users</div>
                  <div className="font-semibold">{confirmation.totalUsers}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-purple-500 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">ステータス / Status</div>
                  <div className="font-semibold capitalize">{confirmation.status}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Review */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {confirmation.items.map((item) => {
              const IconComponent = serviceIcons[item.serviceCategory.code] || serviceIcons.default
              return (
                <TabsTrigger 
                  key={item.serviceCategory.id} 
                  value={item.serviceCategory.id}
                  className="flex items-center space-x-2"
                >
                  <IconComponent className="h-4 w-4" />
                  <span className="hidden sm:inline">{item.serviceCategory.name_jp}</span>
                </TabsTrigger>
              )
            })}
          </TabsList>

          {confirmation.items.map((item) => (
            <TabsContent key={item.serviceCategory.id} value={item.serviceCategory.id}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      {(() => {
                        const IconComponent = serviceIcons[item.serviceCategory.code] || serviceIcons.default
                        return <IconComponent className="mr-2 h-5 w-5" />
                      })()}
                      {item.serviceCategory.name_jp}
                    </div>
                    <Badge variant={getActionBadgeColor(item.action)}>
                      {getActionText(item.action)}
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    {item.serviceCategory.name_en}
                    {item.serviceCategory.description && (
                      <div className="mt-1 text-sm">{item.serviceCategory.description}</div>
                    )}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Affected Users */}
                  <div>
                    <h4 className="font-medium mb-2">
                      対象ユーザー / Affected Users ({item.users.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {item.users.map((user) => (
                        <div key={user.id} className="p-3 bg-gray-50 rounded-lg">
                          <div className="font-medium text-sm">{user.name_jp}</div>
                          <div className="text-xs text-gray-600">{user.email}</div>
                          <div className="text-xs text-gray-500">
                            ID: {user.staff_id} | {user.division.name_jp}
                          </div>
                          {user.pc_id && (
                            <div className="text-xs text-gray-500">PC: {user.pc_id}</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Form Data */}
                  {Object.keys(item.formData).length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">
                        リクエスト詳細 / Request Details
                      </h4>
                      <div className="p-3 bg-gray-50 rounded-lg space-y-1">
                        {renderFormData(item.formData)}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Actions */}
        <div className="flex justify-between pt-6 border-t">
          <div className="space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onEdit}
              disabled={loading}
            >
              編集 / Edit
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={onCancel}
              disabled={loading}
            >
              キャンセル / Cancel
            </Button>
          </div>

          <Button
            type="button"
            onClick={onSubmit}
            disabled={loading || confirmation.status !== 'ready'}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            リクエスト送信 / Submit Request
          </Button>
        </div>

        {confirmation.status !== 'ready' && (
          <Alert>
            <AlertDescription>
              リクエストの準備が完了していません。すべての情報を確認してください。
              <br />
              Request is not ready for submission. Please review all information.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
