"use strict";(()=>{var e={};e.id=8229,e.ids=[8229],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55675:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var a={};t.r(a),t.d(a,{GET:()=>d});var s=t(42706),o=t(28203),n=t(45994),p=t(39187),i=t(61487),u=t(14724);async function d(e,{params:r}){try{let e=(0,i.U)(),{data:{user:t},error:a}=await e.auth.getUser();if(a||!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let s=await u.g.getApprovalChainStatus(r.id);return p.NextResponse.json(s)}catch(e){return console.error("Error fetching approval chain:",e),p.NextResponse.json({error:"Failed to fetch approval chain status"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/approval-chains/[id]/route",pathname:"/api/workflows/approval-chains/[id]",filename:"route",bundlePath:"app/api/workflows/approval-chains/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approval-chains\\[id]\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:h}=l;function v(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8096,2076],()=>t(55675));module.exports=a})();