"use strict";(()=>{var e={};e.id=8229,e.ids=[8229],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55675:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var a={};t.r(a),t.d(a,{GET:()=>u});var o=t(42706),s=t(28203),n=t(45994),i=t(39187),p=t(14724);async function u(e,{params:r}){try{let e=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:t},error:a}=await e.auth.getUser();if(a||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=await p.g.getApprovalChainStatus(r.id);return i.NextResponse.json(o)}catch(e){return console.error("Error fetching approval chain:",e),i.NextResponse.json({error:"Failed to fetch approval chain status"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}();let d=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/approval-chains/[id]/route",pathname:"/api/workflows/approval-chains/[id]",filename:"route",bundlePath:"app/api/workflows/approval-chains/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approval-chains\\[id]\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:v}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8096,2076],()=>t(55675));module.exports=a})();