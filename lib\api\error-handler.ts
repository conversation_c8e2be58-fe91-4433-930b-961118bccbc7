import { NextResponse } from 'next/server';
import { auditLogger } from '@/lib/services/audit';

export interface AppError extends <PERSON>rror {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class APIError extends Error implements AppError {
  statusCode: number;
  code: string;
  details?: any;

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR', details?: any) {
    super(message);
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

export function handleAPIError(error: unknown, userId?: string) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Log the full error internally
  console.error('API Error:', error);

  // Log to audit trail for security monitoring
  if (error instanceof Error) {
    auditLogger.logError({
      error: error.message,
      stack: error.stack,
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  // Handle known API errors
  if (error instanceof APIError) {
    return NextResponse.json(
      {
        error: {
          message: error.message,
          code: error.code,
          ...(isDevelopment && { details: error.details }),
        },
      },
      { status: error.statusCode }
    );
  }

  // Handle Supabase errors
  if (error && typeof error === 'object' && 'code' in error) {
    const supabaseError = error as { code: string; message: string };
    
    // Map Supabase error codes to user-friendly messages
    const errorMap: Record<string, { message: string; status: number }> = {
      '23505': { message: 'This record already exists', status: 409 },
      '23503': { message: 'Referenced record not found', status: 400 },
      '23502': { message: 'Required field missing', status: 400 },
      'PGRST301': { message: 'Unauthorized access', status: 401 },
      'PGRST204': { message: 'No rows found', status: 404 },
    };

    const mappedError = errorMap[supabaseError.code];
    if (mappedError) {
      return NextResponse.json(
        {
          error: {
            message: mappedError.message,
            code: supabaseError.code,
            ...(isDevelopment && { originalMessage: supabaseError.message }),
          },
        },
        { status: mappedError.status }
      );
    }
  }

  // Handle validation errors
  if (error && typeof error === 'object' && 'issues' in error) {
    return NextResponse.json(
      {
        error: {
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          ...(isDevelopment && { issues: (error as any).issues }),
        },
      },
      { status: 400 }
    );
  }

  // Default error response (sanitized for production)
  return NextResponse.json(
    {
      error: {
        message: 'An unexpected error occurred. Please try again later.',
        code: 'INTERNAL_ERROR',
        ...(isDevelopment && {
          details: error instanceof Error ? {
            message: error.message,
            stack: error.stack,
          } : error,
        }),
      },
    },
    { status: 500 }
  );
}

// Wrapper for API routes
export function withErrorHandler(
  handler: (req: Request, params?: any) => Promise<NextResponse>
) {
  return async (req: Request, params?: any) => {
    try {
      return await handler(req, params);
    } catch (error) {
      // Try to extract user ID from request if possible
      let userId: string | undefined;
      try {
        const { searchParams } = new URL(req.url);
        userId = searchParams.get('userId') || undefined;
      } catch {}

      return handleAPIError(error, userId);
    }
  };
}
