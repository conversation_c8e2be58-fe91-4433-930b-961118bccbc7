"use strict";exports.id=9362,exports.ids=[9362],exports.modules={19362:(e,t,s)=>{s.d(t,{g:()=>I});var a=s(45512),r=s(58009),i=s(86201),n=s(61594),l=s(97832),d=s(67418),o=s(19473),c=s(16873),u=s(97643),m=s(87021),f=s(25409),p=s(54069),h=s(77252),x=s(69193),g=s(59462);let y=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,g.cn)("w-full caption-bottom text-sm",e),...t})}));y.displayName="Table";let v=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("thead",{ref:s,className:(0,g.cn)("[&_tr]:border-b",e),...t}));v.displayName="TableHeader";let _=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("tbody",{ref:s,className:(0,g.cn)("[&_tr:last-child]:border-0",e),...t}));_.displayName="TableBody",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("tfoot",{ref:s,className:(0,g.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let N=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("tr",{ref:s,className:(0,g.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));N.displayName="TableRow";let b=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("th",{ref:s,className:(0,g.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));b.displayName="TableHead";let j=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("td",{ref:s,className:(0,g.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));j.displayName="TableCell",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("caption",{ref:s,className:(0,g.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";var E=s(666),w=s(90898),R=s(65518),S=s(86772),T=s(47085);let A={INFO:(0,a.jsx)(i.A,{className:"h-4 w-4 text-blue-500"}),WARNING:(0,a.jsx)(n.A,{className:"h-4 w-4 text-yellow-500"}),ERROR:(0,a.jsx)(l.A,{className:"h-4 w-4 text-red-500"}),CRITICAL:(0,a.jsx)(d.A,{className:"h-4 w-4 text-red-700"})},C={INFO:"bg-blue-100 text-blue-800",WARNING:"bg-yellow-100 text-yellow-800",ERROR:"bg-red-100 text-red-800",CRITICAL:"bg-red-200 text-red-900"};function I({departmentId:e,entityType:t,entityId:s,showExport:i=!0,showFilters:n=!0,maxHeight:l="600px"}){let[d,g]=(0,r.useState)([]),[I,O]=(0,r.useState)(!0),[D,U]=(0,r.useState)(0),[L,k]=(0,r.useState)(""),[q,F]=(0,r.useState)([]),[P,G]=(0,r.useState)([]),[M,H]=(0,r.useState)(),[V,$]=(0,r.useState)(0),[W,z]=(0,r.useState)("CSV"),B={"User Events":["USER_LOGIN","USER_LOGOUT","USER_CREATED","USER_UPDATED","USER_DELETED","PASSWORD_CHANGED","PASSWORD_RESET","ROLE_ASSIGNED","ROLE_REMOVED"],"Request Events":["REQUEST_CREATED","REQUEST_UPDATED","REQUEST_DELETED","REQUEST_APPROVED","REQUEST_REJECTED","REQUEST_COMPLETED","REQUEST_CANCELLED"],"Data Events":["DATA_ACCESSED","DATA_EXPORTED","DATA_IMPORTED","PERMISSION_GRANTED","PERMISSION_REVOKED","CONFIGURATION_CHANGED","SETTINGS_UPDATED"],"System Events":["SYSTEM_ERROR","SYSTEM_WARNING","SYSTEM_INFO","INTEGRATION_SUCCESS","INTEGRATION_FAILURE","SCHEDULED_TASK_RUN","SCHEDULED_TASK_FAILED"]};(0,r.useCallback)(async()=>{O(!0);try{let a={limit:50,offset:50*V,search_text:L||void 0,event_types:q.length>0?q:void 0,severity:P.length>0?P:void 0,start_date:M?.from,end_date:M?.to,department_id:e,entity_type:t,entity_id:s},{data:r,count:i,error:n}=await S.H.search(a);n?(0,R.toast)({title:"Error loading audit logs",description:n.message,variant:"destructive"}):(g(r),U(i))}catch(e){(0,R.toast)({title:"Error loading audit logs",description:"An unexpected error occurred",variant:"destructive"})}finally{O(!1)}},[V,L,q,P,M,e,t,s]);let X=async()=>{try{let a={search_text:L||void 0,event_types:q.length>0?q:void 0,severity:P.length>0?P:void 0,start_date:M?.from,end_date:M?.to,department_id:e,entity_type:t,entity_id:s},{url:r,error:i}=await S.H.exportLogs(a,W);i?(0,R.toast)({title:"Export failed",description:i.message,variant:"destructive"}):(window.open(r,"_blank"),(0,R.toast)({title:"Export successful",description:`Audit logs exported as ${W}`}))}catch(e){(0,R.toast)({title:"Export failed",description:"An unexpected error occurred",variant:"destructive"})}},Q=e=>{let t=[];return e.entity_type&&e.entity_name&&t.push(`${e.entity_type}: ${e.entity_name}`),e.department_name&&t.push(`Department: ${e.department_name}`),e.role_name&&t.push(`Role: ${e.role_name}`),t.join(" • ")};return(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.ZB,{className:"text-2xl",children:"Audit Trail"}),(0,a.jsx)(u.BT,{children:"System activity and compliance logging"})]}),i&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(p.l6,{value:W,onValueChange:e=>z(e),children:[(0,a.jsx)(p.bq,{className:"w-24",children:(0,a.jsx)(p.yv,{})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"CSV",children:"CSV"}),(0,a.jsx)(p.eb,{value:"JSON",children:"JSON"}),(0,a.jsx)(p.eb,{value:"PDF",children:"PDF"}),(0,a.jsx)(p.eb,{value:"XLSX",children:"Excel"})]})]}),(0,a.jsxs)(m.$,{onClick:X,size:"sm",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),(0,a.jsxs)(u.Wu,{children:[n&&(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(f.p,{placeholder:"Search logs...",value:L,onChange:e=>k(e.target.value),className:"pl-10"})]})}),(0,a.jsx)(DatePickerWithRange,{date:M,onDateChange:H,className:"w-full sm:w-auto"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Severity"}),(0,a.jsx)("div",{className:"flex gap-2",children:["INFO","WARNING","ERROR","CRITICAL"].map(e=>(0,a.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)(w.S,{checked:P.includes(e),onCheckedChange:t=>{t?G([...P,e]):G(P.filter(t=>t!==e))}}),(0,a.jsxs)(h.E,{className:C[e],children:[A[e],(0,a.jsx)("span",{className:"ml-1",children:e})]})]},e))})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Event Types"}),(0,a.jsxs)(x.tU,{defaultValue:"all",className:"w-full",children:[(0,a.jsxs)(x.j7,{children:[(0,a.jsx)(x.Xi,{value:"all",onClick:()=>F([]),children:"All Events"}),Object.keys(B).map(e=>(0,a.jsx)(x.Xi,{value:e,children:e},e))]}),Object.entries(B).map(([e,t])=>(0,a.jsx)(x.av,{value:e,className:"mt-2",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,a.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)(w.S,{checked:q.includes(e),onCheckedChange:t=>{t?F([...q,e]):F(q.filter(t=>t!==e))}}),(0,a.jsx)("span",{className:"text-sm",children:e.replace(/_/g," ")})]},e))})},e))]})]})]}),(0,a.jsx)(E.F,{className:"w-full",style:{height:l},children:(0,a.jsxs)(y,{children:[(0,a.jsx)(v,{children:(0,a.jsxs)(N,{children:[(0,a.jsx)(b,{className:"w-[180px]",children:"Timestamp"}),(0,a.jsx)(b,{className:"w-[100px]",children:"Severity"}),(0,a.jsx)(b,{className:"w-[200px]",children:"Event"}),(0,a.jsx)(b,{children:"Action"}),(0,a.jsx)(b,{children:"Details"}),(0,a.jsx)(b,{className:"w-[150px]",children:"User"})]})}),(0,a.jsx)(_,{children:I?(0,a.jsx)(N,{children:(0,a.jsx)(j,{colSpan:6,className:"text-center py-8 text-muted-foreground",children:"Loading audit logs..."})}):0===d.length?(0,a.jsx)(N,{children:(0,a.jsx)(j,{colSpan:6,className:"text-center py-8 text-muted-foreground",children:"No audit logs found"})}):d.map(e=>(0,a.jsxs)(N,{children:[(0,a.jsx)(j,{className:"font-mono text-sm",children:(0,T.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm:ss")}),(0,a.jsx)(j,{children:(0,a.jsxs)(h.E,{className:C[e.severity],children:[A[e.severity],(0,a.jsx)("span",{className:"ml-1",children:e.severity})]})}),(0,a.jsx)(j,{className:"font-medium",children:e.event_type.replace(/_/g," ")}),(0,a.jsx)(j,{children:e.action}),(0,a.jsx)(j,{className:"text-sm text-muted-foreground",children:e.description||Q(e)}),(0,a.jsx)(j,{className:"text-sm",children:e.role_name||"System"})]},e.id))})]})}),D>50&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",50*V+1," to ",Math.min((V+1)*50,D)," of ",D," entries"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(m.$,{variant:"outline",size:"sm",onClick:()=>$(Math.max(0,V-1)),disabled:0===V,children:"Previous"}),(0,a.jsx)(m.$,{variant:"outline",size:"sm",onClick:()=>$(V+1),disabled:(V+1)*50>=D,children:"Next"})]})]})]})]})}},77252:(e,t,s)=>{s.d(t,{E:()=>l});var a=s(45512);s(58009);var r=s(21643),i=s(59462);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},87021:(e,t,s)=>{s.d(t,{$:()=>o,r:()=>d});var a=s(45512),r=s(58009),i=s(12705),n=s(21643),l=s(59462);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...n},o)=>{let c=r?i.DX:"button";return(0,a.jsx)(c,{className:(0,l.cn)(d({variant:t,size:s,className:e})),ref:o,...n})});o.displayName="Button"},90898:(e,t,s)=>{s.d(t,{S:()=>d});var a=s(45512),r=s(58009),i=s(2112),n=s(24849),l=s(59462);let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.bL,{ref:s,className:(0,l.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,a.jsx)(i.C1,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}));d.displayName=i.bL.displayName},25409:(e,t,s)=>{s.d(t,{p:()=>n});var a=s(45512),r=s(58009),i=s(59462);let n=r.forwardRef(({className:e,type:t,...s},r)=>(0,a.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));n.displayName="Input"},666:(e,t,s)=>{s.d(t,{F:()=>l});var a=s(45512),r=s(58009),i=s(53998),n=s(59462);let l=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.bL,{ref:r,className:(0,n.cn)("relative overflow-hidden",e),...s,children:[(0,a.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,a.jsx)(d,{}),(0,a.jsx)(i.OK,{})]}));l.displayName=i.bL.displayName;let d=r.forwardRef(({className:e,orientation:t="vertical",...s},r)=>(0,a.jsx)(i.VM,{ref:r,orientation:t,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 border-t border-t-transparent p-[1px]",e),...s,children:(0,a.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})}));d.displayName=i.VM.displayName},54069:(e,t,s)=>{s.d(t,{bq:()=>m,eb:()=>x,gC:()=>h,l6:()=>c,yv:()=>u});var a=s(45512),r=s(58009),i=s(81477),n=s(98755),l=s(28638),d=s(24849),o=s(59462);let c=i.bL;i.YJ;let u=i.WT,m=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.l9.displayName;let f=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}));f.displayName=i.PP.displayName;let p=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=i.wn.displayName;let h=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(f,{}),(0,a.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(p,{})]})}));h.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let x=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:t})]}));x.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},69193:(e,t,s)=>{s.d(t,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>l});var a=s(45512),r=s(58009),i=s(55613),n=s(59462);let l=i.bL,d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=i.B8.displayName;let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));o.displayName=i.l9.displayName;let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.UC.displayName},65518:(e,t,s)=>{s.d(t,{d:()=>i});var a=s(58009),r=s(91542);function i(){let[e,t]=(0,a.useState)(!1);return e?{toast:({title:e,description:t,action:s,variant:a,duration:i})=>("destructive"===a?r.oR.error:"success"===a?r.oR.success:r.oR)(e,{description:t,action:s,duration:i||5e3}),dismiss:r.oR.dismiss}:{toast:e=>{},dismiss:e=>{}}}},86772:(e,t,s)=>{s.d(t,{H:()=>i,r:()=>n});let a=(0,s(93939).createClient)("your_supabase_project_url","your_supabase_anon_key");class r{constructor(){this.queue=[],this.flushTimer=null,this.BATCH_SIZE=50,this.FLUSH_INTERVAL=5e3,this.startBatchProcessing()}static getInstance(){return r.instance||(r.instance=new r),r.instance}startBatchProcessing(){this.flushTimer=setInterval(()=>{this.queue.length>0&&this.flush()},this.FLUSH_INTERVAL)}async log(e){try{let t=await this.enrichLogEntry(e);this.queue.push(t),this.queue.length>=this.BATCH_SIZE&&await this.flush(),("CRITICAL"===e.severity||"ERROR"===e.severity)&&await this.flush()}catch(e){console.error("Failed to log audit event:",e)}}async enrichLogEntry(e){let t={...e};t.occurred_at||(t.occurred_at=new Date().toISOString());let{data:{user:s}}=await a.auth.getUser();if(s&&!t.user_id){t.user_id=s.id;let{data:e}=await a.from("staff").select("id, division_id, role_id, roles!inner(name), divisions!inner(name_en)").eq("auth_id",s.id).single();e&&(t.staff_id=e.id,t.role_name=e.roles?.name,t.department_id=e.division_id,t.department_name=e.divisions?.name_en)}return t.request_id||(t.request_id=crypto.randomUUID()),t.old_value&&t.new_value&&(t.changes_summary=this.calculateChanges(t.old_value,t.new_value)),t}calculateChanges(e,t){let s={};return new Set([...Object.keys(e||{}),...Object.keys(t||{})]).forEach(a=>{let r=e?.[a],i=t?.[a];JSON.stringify(r)!==JSON.stringify(i)&&(s[a]={old:r,new:i})}),s}async flush(){if(0===this.queue.length)return;let e=[...this.queue];this.queue=[];try{let{error:t}=await a.functions.invoke("process-audit-logs",{body:{logs:e}});t&&(console.error("Failed to flush audit logs:",t),this.queue.unshift(...e))}catch(t){console.error("Failed to flush audit logs:",t),this.queue.unshift(...e)}}async search(e){try{let t=a.from("audit_logs_user_view").select("*",{count:"exact"});e.event_types?.length&&(t=t.in("event_type",e.event_types)),e.severity?.length&&(t=t.in("severity",e.severity)),e.user_id&&(t=t.eq("user_id",e.user_id)),e.staff_id&&(t=t.eq("staff_id",e.staff_id)),e.department_id&&(t=t.eq("department_id",e.department_id)),e.entity_type&&(t=t.eq("entity_type",e.entity_type)),e.entity_id&&(t=t.eq("entity_id",e.entity_id)),e.start_date&&(t=t.gte("created_at",e.start_date.toISOString())),e.end_date&&(t=t.lte("created_at",e.end_date.toISOString())),e.search_text&&(t=t.or(`action.ilike.%${e.search_text}%,description.ilike.%${e.search_text}%`)),e.tags?.length&&(t=t.contains("tags",e.tags));let s=e.order_by||"created_at",r=e.order_direction||"desc";t=t.order(s,{ascending:"asc"===r}),e.limit&&(t=t.limit(e.limit)),e.offset&&(t=t.range(e.offset,e.offset+(e.limit||50)-1));let{data:i,count:n,error:l}=await t;return!l&&i&&await this.logAuditAccess("SEARCH",e,i.length),{data:i||[],count:n||0,error:l}}catch(e){return console.error("Failed to search audit logs:",e),{data:[],count:0,error:e}}}async logAuditAccess(e,t,s){try{let{data:{user:r}}=await a.auth.getUser();if(!r)return;let{data:i}=await a.from("staff").select("id").eq("auth_id",r.id).single();if(!i)return;await a.from("audit_log_access").insert({accessed_by:i.id,access_type:e,query_params:t,result_count:s,user_agent:null})}catch(e){console.error("Failed to log audit access:",e)}}async getStatistics(e,t,s){try{let{data:r,error:i}=await a.rpc("get_audit_statistics",{start_date:e.toISOString(),end_date:t.toISOString(),department_id:s});if(i)throw i;return r}catch(e){return console.error("Failed to get audit statistics:",e),null}}async exportLogs(e,t,s){try{await this.log({event_type:"DATA_EXPORTED",severity:"INFO",action:"Export audit logs",description:`Exported audit logs in ${t} format`,metadata:{params:e,format:t,templateId:s}});let{data:r,error:i}=await a.functions.invoke("export-audit-logs",{body:{params:e,format:t,templateId:s}});if(i)throw i;return await this.logAuditAccess("EXPORT",e,r.count||0),{url:r.url,error:null}}catch(e){return console.error("Failed to export audit logs:",e),{url:"",error:e}}}async generateComplianceReport(e,t,s){try{await this.log({event_type:"DATA_EXPORTED",severity:"INFO",action:"Generate compliance report",description:`Generated ${s} compliance report`,metadata:{startDate:e,endDate:t,reportType:s}});let{data:r,error:i}=await a.functions.invoke("generate-compliance-report",{body:{startDate:e,endDate:t,reportType:s}});if(i)throw i;return r}catch(e){return console.error("Failed to generate compliance report:",e),null}}destroy(){this.flushTimer&&(clearInterval(this.flushTimer),this.flushTimer=null),this.flush()}}let i=r.getInstance(),n={logLogin:()=>i.log({event_type:"USER_LOGIN",severity:"INFO",action:"User logged in",description:"Successful authentication"}),logLogout:()=>i.log({event_type:"USER_LOGOUT",severity:"INFO",action:"User logged out",description:"Session terminated"}),logRequestCreated:(e,t)=>i.log({event_type:"REQUEST_CREATED",severity:"INFO",action:"Created new request",description:`Created ${t} request`,entity_type:"request_forms",entity_id:e}),logRequestUpdated:(e,t)=>i.log({event_type:"REQUEST_UPDATED",severity:"INFO",action:"Updated request",entity_type:"request_forms",entity_id:e,changes_summary:t}),logError:(e,t)=>i.log({event_type:"SYSTEM_ERROR",severity:"ERROR",action:"System error occurred",description:e.message||"Unknown error",metadata:{error:e.toString(),stack:e.stack,context:t}}),logSecurityEvent:(e,t="WARNING")=>i.log({event_type:"SYSTEM_WARNING",severity:t,action:e,compliance_flags:["SECURITY"]})}}};