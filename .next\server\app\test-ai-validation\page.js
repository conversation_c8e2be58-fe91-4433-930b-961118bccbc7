(()=>{var e={};e.id=5108,e.ids=[5108],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49312:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(70260),a=r(28203),i=r(25155),l=r.n(i),n=r(67292),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d=["",{children:["test-ai-validation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33796)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-validation\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-validation\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-ai-validation/page",pathname:"/test-ai-validation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25320:(e,t,r)=>{Promise.resolve().then(r.bind(r,33796))},95816:(e,t,r)=>{Promise.resolve().then(r.bind(r,62652))},62652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s={};r.r(s);var a=r(45512),i=r(58009),l=r(97643);r(87021),r(12784),r(3328);var n=r(77252);r(50236);var o=r(12214);r(59462);var d=r(91542);let c=[{id:"basic-info",title:"基本情報 / Basic Information",description:"AIがリアルタイムで入力を検証し、提案を行います / AI validates and suggests in real-time",fields:[{id:"email",type:"text",label:"Email Address",labelJp:"メールアドレス",validation:"email",required:!0,placeholder:"例: <EMAIL>",helpText:"Enter your company email address",helpTextJp:"会社のメールアドレスを入力してください"},{id:"pc_id",type:"text",label:"PC ID",labelJp:"PC ID",validation:"pc_id",required:!0,placeholder:"例: **********",helpText:"Enter PC ID (e.g., ********** for laptop)",helpTextJp:"PC IDを入力してください（例：ノートPCの場合 **********）"},{id:"reason",type:"textarea",label:"Reason for Request",labelJp:"申請理由",required:!0,placeholder:"申請理由を日本語と英語で記載してください / Please describe the reason in Japanese and English",helpText:"Provide a clear reason for your request",helpTextJp:"申請理由を明確に記載してください"}]},{id:"service-selection",title:"サービス選択 / Service Selection",description:"必要なサービスを選択してください / Select required services",fields:[{id:"service_type",type:"select",label:"Service Type",labelJp:"サービスタイプ",required:!0,options:[{value:"group_mail",label:"グループメール / Group Mail"},{value:"sharepoint",label:"SharePoint アクセス / SharePoint Access"},{value:"pc_admin",label:"PC管理者権限 / PC Admin Rights"},{value:"password_reset",label:"パスワードリセット / Password Reset"}]},{id:"urgency",type:"radio",label:"Urgency Level",labelJp:"緊急度",required:!0,options:[{value:"low",label:"低 / Low"},{value:"medium",label:"中 / Medium"},{value:"high",label:"高 / High"},{value:"critical",label:"緊急 / Critical"}]}]}];function p(){let[e,t]=(0,i.useState)(null);return(0,a.jsx)("div",{className:"container mx-auto p-6 max-w-4xl",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold flex items-center justify-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-blue-600"}),"AI リアルタイム検証デモ"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Real-time Form Validation with AI Feedback"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 justify-center",children:[(0,a.jsx)(n.E,{variant:"secondary",children:"リアルタイム検証 / Real-time Validation"}),(0,a.jsx)(n.E,{variant:"secondary",children:"AI提案 / AI Suggestions"}),(0,a.jsx)(n.E,{variant:"secondary",children:"視覚的フィードバック / Visual Feedback"}),(0,a.jsx)(n.E,{variant:"secondary",children:"コンテキスト認識 / Context Aware"})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"使い方 / How to Use"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:"• フィールドに入力すると、AIがリアルタイムで検証します / AI validates as you type"}),(0,a.jsx)("li",{children:"• 緑のチェックマークは正しい入力を示します / Green checkmark indicates valid input"}),(0,a.jsx)("li",{children:"• 黄色の警告は改善の余地があることを示します / Yellow warning suggests improvements"}),(0,a.jsx)("li",{children:'• AIの提案がある場合は「採用」ボタンで適用できます / Click "Accept" to apply AI suggestions'}),(0,a.jsx)("li",{children:'• 「自動入力」ボタンで空欄を自動補完できます / Use "Auto-fill" to complete empty fields'})]})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"テストフォーム / Test Form"}),(0,a.jsx)(l.BT,{children:"AIアシスタントが入力をサポートします / AI assistant helps with form completion"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)(s.EnhancedDynamicForm,{sections:c,onSubmit:e=>{t(e),d.oR.success("フォームが送信されました / Form submitted successfully")},onCancel:()=>{t(null),d.oR.info("フォームがキャンセルされました / Form cancelled")},context:{departmentId:"it-systems",userRole:"regular_user"}})})]}),e&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"送信されたデータ / Submitted Data"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("pre",{className:"bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-auto",children:JSON.stringify(e,null,2)})})]})]})})}},33796:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-ai-validation\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-ai-validation\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8096,2076],()=>r(49312));module.exports=s})();