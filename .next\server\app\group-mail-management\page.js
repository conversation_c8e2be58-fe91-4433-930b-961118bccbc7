(()=>{var e={};e.id=766,e.ids=[766],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},9724:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>c});var t=r(70260),a=r(28203),i=r(25155),n=r.n(i),l=r(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let c=["",{children:["group-mail-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29124)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\group-mail-management\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\group-mail-management\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/group-mail-management/page",pathname:"/group-mail-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},80320:(e,s,r)=>{Promise.resolve().then(r.bind(r,29124))},9696:(e,s,r)=>{Promise.resolve().then(r.bind(r,99352))},99352:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(45512),a=r(58009),i=r(97643),n=r(87021),l=r(25409),d=r(77252),c=r(666),o=r(69193),m=r(16873),p=r(86235),u=r(8866),x=r(64977),h=r(81943),f=r(69855),j=r(37133),g=r(43433),v=r(59245),N=r(91542),_=r(122);function b(){let{user:e,staff:s}=(0,g.A)(),{filterByDepartment:r}=(0,_.K)(),[b,y]=(0,a.useState)([]),[w,C]=(0,a.useState)(null),[P,A]=(0,a.useState)([]),[q,R]=(0,a.useState)(""),[k,E]=(0,a.useState)(!1),[S,U]=(0,a.useState)([]),D=(0,j.createClient)(),$=async e=>{try{let{data:s,error:r}=await D.from("group_mail_members").select(`
          *,
          staff:staff_id (
            id,
            name_jp,
            name_en,
            email,
            staff_id
          )
        `).eq("group_mail_id",e).order("created_at",{ascending:!1});if(r){N.oR.error("メンバーの読み込みに失敗しました");return}A(s||[])}catch(e){console.error("Error loading members:",e)}},B=async()=>{if(w&&0!==S.length){E(!0);try{let e=P.map(e=>e.staff_id),s=S.filter(s=>!e.includes(s.id));if(0===s.length){N.oR.warning("選択されたユーザーは既にメンバーです");return}let r=s.map(e=>({group_mail_id:w.id,staff_id:e.id})),{error:t}=await D.from("group_mail_members").insert(r);if(t){N.oR.error("メンバーの追加に失敗しました");return}N.oR.success(`${s.length}人のユーザーを追加しました`),U([]),$(w.id)}finally{E(!1)}}},F=async e=>{if(confirm("このユーザーをグループから削除しますか？")){E(!0);try{let{error:s}=await D.from("group_mail_members").delete().eq("id",e);if(s){N.oR.error("メンバーの削除に失敗しました");return}N.oR.success("メンバーを削除しました"),$(w.id)}finally{E(!1)}}},G=b.filter(e=>e.email_address.toLowerCase().includes(q.toLowerCase())||e.description&&e.description.toLowerCase().includes(q.toLowerCase()));return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"グループメール管理"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"グループメールアドレスのメンバー管理"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(i.Zp,{className:"lg:col-span-1",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"グループメールアドレス"}),(0,t.jsxs)(i.BT,{children:["全",b.length,"件のグループメール"]})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(l.p,{placeholder:"グループメールを検索...",value:q,onChange:e=>R(e.target.value),className:"pl-8"})]}),(0,t.jsx)(c.F,{className:"h-[600px]",children:(0,t.jsx)("div",{className:"space-y-2",children:k?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(p.A,{className:"h-6 w-6 animate-spin"})}):0===G.length?(0,t.jsx)("p",{className:"text-center text-muted-foreground p-4",children:"グループメールが見つかりません"}):G.map(e=>(0,t.jsxs)("div",{className:`p-3 rounded-lg border cursor-pointer transition-colors ${w?.id===e.id?"bg-primary/10 border-primary":"hover:bg-accent"}`,onClick:()=>C(e),children:["                        ",(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium truncate",children:e.email_address}),e.description&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:e.description})]})]})]},e.id))})})]})})]}),(0,t.jsxs)(i.Zp,{className:"lg:col-span-2",children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(i.ZB,{children:"メンバー管理"}),(0,t.jsx)(i.BT,{children:w?`${w.email_address} のメンバー`:"グループメールを選択してください"})]}),w&&(0,t.jsxs)(d.E,{variant:"outline",children:[P.length," メンバー"]})]})}),(0,t.jsx)(i.Wu,{children:w?(0,t.jsxs)(o.tU,{defaultValue:"members",className:"w-full",children:[(0,t.jsxs)(o.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(o.Xi,{value:"members",children:"現在のメンバー"}),(0,t.jsx)(o.Xi,{value:"add",children:"メンバー追加"})]}),(0,t.jsx)(o.av,{value:"members",className:"mt-6",children:(0,t.jsx)(c.F,{className:"h-[500px]",children:(0,t.jsx)("div",{className:"space-y-2",children:0===P.length?(0,t.jsx)("p",{className:"text-center text-muted-foreground p-8",children:"このグループにはメンバーがいません"}):P.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.staff?.name_jp||e.staff?.name_en}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.staff?.email," (",e.staff?.staff_id,")"]})]})]}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>F(e.id),disabled:k,children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]},e.id))})})}),(0,t.jsx)(o.av,{value:"add",className:"mt-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(v.M,{multiple:!0,onSelect:U,placeholder:"追加するユーザーを検索..."}),S.length>0&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{className:"text-sm font-medium",children:["選択されたユーザー (",S.length,"人)"]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:S.map(e=>(0,t.jsx)(d.E,{variant:"secondary",children:e.name_jp||e.name_en},e.id))})]}),(0,t.jsx)(n.$,{onClick:B,disabled:0===S.length||k,className:"w-full",children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"w-4 h-4 mr-2 animate-spin"}),"追加中..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"メンバーを追加"]})})]})})]}):(0,t.jsxs)("div",{className:"text-center py-12 text-muted-foreground",children:[(0,t.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 opacity-30"}),(0,t.jsx)("p",{children:"左側からグループメールを選択してください"})]})})]})]})]})}},29124:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\group-mail-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\group-mail-management\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8096,2076],()=>r(9724));module.exports=t})();