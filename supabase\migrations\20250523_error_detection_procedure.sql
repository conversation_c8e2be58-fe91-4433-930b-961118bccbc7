-- Create stored procedure for incrementing error pattern frequency
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION increment_error_pattern_frequency(
  p_field_type VARCHAR(50),
  p_error_type VARCHAR(50)
)
RETURNS VOID AS $$
BEGIN
  UPDATE error_patterns
  SET 
    frequency = frequency + 1,
    updated_at = NOW()
  WHERE 
    field_type = p_field_type 
    AND error_type = p_error_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
