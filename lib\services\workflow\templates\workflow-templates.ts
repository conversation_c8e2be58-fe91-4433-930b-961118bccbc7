import { WorkflowDefinition, WorkflowStep } from '../types';

export interface WorkflowTemplate {
  id: string;
  name: string;
  nameJp: string;
  description: string;
  descriptionJp: string;
  category: string;
  serviceCategoryId?: string;
  steps: WorkflowStep[];
  slaHours: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  requiredApprovals?: number;
  tags: string[];
}

export const workflowTemplates: WorkflowTemplate[] = [
  // Password Reset Templates
  {
    id: 'pwd-reset-standard',
    name: 'Standard Password Reset',
    nameJp: '標準パスワードリセット',
    description: 'Standard password reset workflow for M365/Windows accounts',
    descriptionJp: 'M365/Windows アカウントの標準パスワードリセットワークフロー',
    category: 'password_reset',
    serviceCategoryId: 'PWR',
    priority: 'high',
    slaHours: 2,
    tags: ['password', 'security', 'urgent'],
    steps: [
      {
        id: 'verify-identity',
        name: 'Verify User Identity',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Verify user identity via phone or secondary email',
        estimatedDuration: 15,
        requiredFields: ['verification_method', 'verification_result'],
      },
      {
        id: 'reset-password',
        name: 'Reset Password',
        type: 'automated',
        action: 'reset_password',
        description: 'System automatically generates temporary password',
        estimatedDuration: 5,
      },
      {
        id: 'notify-user',
        name: 'Send Credentials',
        type: 'automated',
        action: 'send_notification',
        description: 'Send temporary password via secure channel',
        estimatedDuration: 2,
      },
      {
        id: 'confirm-access',
        name: 'Confirm Access',
        type: 'manual',
        assignedTo: 'requester',
        description: 'User confirms successful login with new password',
        estimatedDuration: 30,
      },
    ],
  },
  {
    id: 'pwd-reset-mfa',
    name: 'MFA Password Reset',
    nameJp: 'MFA パスワードリセット',
    description: 'Password reset with Multi-Factor Authentication reset',
    descriptionJp: '多要素認証を含むパスワードリセット',
    category: 'password_reset',
    serviceCategoryId: 'PWR',
    priority: 'high',
    slaHours: 3,
    tags: ['password', 'mfa', 'security'],
    steps: [
      {
        id: 'verify-identity',
        name: 'Enhanced Identity Verification',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Verify user identity with manager confirmation',
        estimatedDuration: 30,
        requiredFields: ['manager_approval', 'verification_method'],
      },
      {
        id: 'reset-mfa',
        name: 'Reset MFA Device',
        type: 'automated',
        action: 'reset_mfa',
        description: 'Clear existing MFA devices',
        estimatedDuration: 5,
      },
      {
        id: 'reset-password',
        name: 'Reset Password',
        type: 'automated',
        action: 'reset_password',
        description: 'Generate temporary password',
        estimatedDuration: 5,
      },
      {
        id: 'setup-mfa',
        name: 'Setup New MFA',
        type: 'manual',
        assignedTo: 'requester',
        description: 'User sets up new MFA device',
        estimatedDuration: 30,
      },
    ],
  },

  // Group Mail Management Templates
  {
    id: 'group-mail-add',
    name: 'Add to Group Mail',
    nameJp: 'グループメール追加',
    description: 'Add users to group mail distribution lists',
    descriptionJp: 'グループメール配布リストへのユーザー追加',
    category: 'email_management',
    serviceCategoryId: 'GM',
    priority: 'medium',
    slaHours: 24,
    tags: ['email', 'group', 'access'],
    steps: [
      {
        id: 'validate-request',
        name: 'Validate Request',
        type: 'automated',
        action: 'validate_group_access',
        description: 'Check if users belong to authorized department',
        estimatedDuration: 5,
      },
      {
        id: 'manager-approval',
        name: 'Manager Approval',
        type: 'approval',
        assignedTo: 'manager',
        description: 'Department manager approves group mail access',
        estimatedDuration: 480, // 8 hours
        timeoutAction: 'escalate',
        timeoutHours: 24,
      },
      {
        id: 'add-to-group',
        name: 'Add to Group',
        type: 'automated',
        action: 'add_group_mail_member',
        description: 'Add approved users to group mail',
        estimatedDuration: 10,
      },
      {
        id: 'notify-users',
        name: 'Notify Users',
        type: 'automated',
        action: 'send_notification',
        description: 'Notify users of group mail access',
        estimatedDuration: 5,
      },
    ],
  },
  {
    id: 'group-mail-remove',
    name: 'Remove from Group Mail',
    nameJp: 'グループメール削除',
    description: 'Remove users from group mail distribution lists',
    descriptionJp: 'グループメール配布リストからのユーザー削除',
    category: 'email_management',
    serviceCategoryId: 'GM',
    priority: 'medium',
    slaHours: 24,
    tags: ['email', 'group', 'access'],
    steps: [
      {
        id: 'validate-request',
        name: 'Validate Request',
        type: 'automated',
        action: 'validate_removal_request',
        description: 'Verify removal authorization',
        estimatedDuration: 5,
      },
      {
        id: 'remove-from-group',
        name: 'Remove from Group',
        type: 'automated',
        action: 'remove_group_mail_member',
        description: 'Remove users from group mail',
        estimatedDuration: 10,
      },
      {
        id: 'notify-users',
        name: 'Notify Users',
        type: 'automated',
        action: 'send_notification',
        description: 'Notify users of removal',
        estimatedDuration: 5,
      },
    ],
  },

  // SharePoint Access Templates
  {
    id: 'spo-access-grant',
    name: 'Grant SharePoint Access',
    nameJp: 'SharePoint アクセス許可',
    description: 'Grant access to SharePoint document libraries',
    descriptionJp: 'SharePoint ドキュメントライブラリへのアクセス許可',
    category: 'sharepoint',
    serviceCategoryId: 'SPO',
    priority: 'medium',
    slaHours: 48,
    tags: ['sharepoint', 'documents', 'access'],
    requiredApprovals: 2,
    steps: [
      {
        id: 'check-prerequisites',
        name: 'Check Prerequisites',
        type: 'automated',
        action: 'check_spo_prerequisites',
        description: 'Verify user has necessary base permissions',
        estimatedDuration: 5,
      },
      {
        id: 'manager-approval',
        name: 'Manager Approval',
        type: 'approval',
        assignedTo: 'manager',
        description: 'Direct manager approval',
        estimatedDuration: 480,
        timeoutAction: 'escalate',
        timeoutHours: 24,
      },
      {
        id: 'data-owner-approval',
        name: 'Data Owner Approval',
        type: 'approval',
        assignedTo: 'data_owner',
        description: 'SharePoint library owner approval',
        estimatedDuration: 480,
        timeoutAction: 'escalate',
        timeoutHours: 48,
      },
      {
        id: 'grant-access',
        name: 'Grant Access',
        type: 'automated',
        action: 'grant_spo_access',
        description: 'Add user to SharePoint library',
        estimatedDuration: 15,
      },
      {
        id: 'verify-access',
        name: 'Verify Access',
        type: 'automated',
        action: 'verify_spo_access',
        description: 'Confirm user can access library',
        estimatedDuration: 10,
      },
    ],
  },

  // PC Admin Rights Templates
  {
    id: 'pc-admin-temporary',
    name: 'Temporary PC Admin Rights',
    nameJp: '一時的なPC管理者権限',
    description: 'Grant temporary administrative rights for software installation',
    descriptionJp: 'ソフトウェアインストール用の一時的な管理者権限',
    category: 'pc_management',
    serviceCategoryId: 'PCA',
    priority: 'high',
    slaHours: 4,
    tags: ['pc', 'admin', 'software'],
    steps: [
      {
        id: 'validate-pc',
        name: 'Validate PC',
        type: 'automated',
        action: 'validate_pc_id',
        description: 'Verify PC ID and ownership',
        estimatedDuration: 5,
      },
      {
        id: 'security-check',
        name: 'Security Check',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Verify software installation requirements',
        estimatedDuration: 30,
        requiredFields: ['software_name', 'business_justification'],
      },
      {
        id: 'grant-temp-rights',
        name: 'Grant Temporary Rights',
        type: 'automated',
        action: 'grant_admin_rights',
        description: 'Grant 4-hour admin window',
        estimatedDuration: 5,
        parameters: { duration_hours: 4 },
      },
      {
        id: 'monitor-usage',
        name: 'Monitor Usage',
        type: 'automated',
        action: 'monitor_admin_usage',
        description: 'Track admin privilege usage',
        estimatedDuration: 240, // 4 hours
      },
      {
        id: 'revoke-rights',
        name: 'Revoke Rights',
        type: 'automated',
        action: 'revoke_admin_rights',
        description: 'Automatically revoke admin rights',
        estimatedDuration: 5,
      },
    ],
  },

  // Onboarding Templates
  {
    id: 'employee-onboarding',
    name: 'New Employee Onboarding',
    nameJp: '新入社員オンボーディング',
    description: 'Complete IT setup for new employees',
    descriptionJp: '新入社員のIT環境セットアップ',
    category: 'hr_processes',
    priority: 'high',
    slaHours: 72,
    tags: ['onboarding', 'hr', 'new_employee'],
    requiredApprovals: 1,
    steps: [
      {
        id: 'hr-approval',
        name: 'HR Approval',
        type: 'approval',
        assignedTo: 'role:Human Resources Staff',
        description: 'HR confirms employee details',
        estimatedDuration: 480,
      },
      {
        id: 'create-accounts',
        name: 'Create User Accounts',
        type: 'automated',
        action: 'create_user_accounts',
        description: 'Create AD, email, and system accounts',
        estimatedDuration: 30,
      },
      {
        id: 'assign-equipment',
        name: 'Assign Equipment',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Assign PC and peripherals',
        estimatedDuration: 60,
        requiredFields: ['pc_id', 'monitor_id', 'accessories'],
      },
      {
        id: 'setup-email-groups',
        name: 'Setup Email Groups',
        type: 'automated',
        action: 'setup_default_groups',
        description: 'Add to department email groups',
        estimatedDuration: 15,
      },
      {
        id: 'grant-spo-access',
        name: 'Grant SharePoint Access',
        type: 'automated',
        action: 'grant_default_spo_access',
        description: 'Grant access to department libraries',
        estimatedDuration: 20,
      },
      {
        id: 'send-welcome',
        name: 'Send Welcome Package',
        type: 'automated',
        action: 'send_welcome_email',
        description: 'Send IT welcome guide and credentials',
        estimatedDuration: 5,
      },
    ],
  },

  // Offboarding Templates
  {
    id: 'employee-offboarding',
    name: 'Employee Offboarding',
    nameJp: '退職者オフボーディング',
    description: 'Complete IT deprovisioning for departing employees',
    descriptionJp: '退職者のIT環境解除',
    category: 'hr_processes',
    priority: 'critical',
    slaHours: 24,
    tags: ['offboarding', 'hr', 'security'],
    steps: [
      {
        id: 'hr-notification',
        name: 'HR Notification',
        type: 'manual',
        assignedTo: 'role:Human Resources Staff',
        description: 'HR confirms departure details',
        estimatedDuration: 30,
        requiredFields: ['departure_date', 'return_equipment'],
      },
      {
        id: 'backup-data',
        name: 'Backup User Data',
        type: 'automated',
        action: 'backup_user_data',
        description: 'Backup email and documents',
        estimatedDuration: 120,
      },
      {
        id: 'revoke-access',
        name: 'Revoke All Access',
        type: 'automated',
        action: 'revoke_all_access',
        description: 'Disable accounts and revoke permissions',
        estimatedDuration: 30,
      },
      {
        id: 'collect-equipment',
        name: 'Collect Equipment',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Collect all IT equipment',
        estimatedDuration: 60,
        requiredFields: ['equipment_returned', 'condition'],
      },
      {
        id: 'final-audit',
        name: 'Final Security Audit',
        type: 'automated',
        action: 'security_audit',
        description: 'Verify all access revoked',
        estimatedDuration: 30,
      },
    ],
  },

  // Software Installation Templates
  {
    id: 'software-install-standard',
    name: 'Standard Software Installation',
    nameJp: '標準ソフトウェアインストール',
    description: 'Install pre-approved software',
    descriptionJp: '事前承認済みソフトウェアのインストール',
    category: 'software',
    serviceCategoryId: 'SIR',
    priority: 'medium',
    slaHours: 48,
    tags: ['software', 'installation'],
    steps: [
      {
        id: 'check-license',
        name: 'Check License',
        type: 'automated',
        action: 'check_software_license',
        description: 'Verify license availability',
        estimatedDuration: 10,
      },
      {
        id: 'schedule-install',
        name: 'Schedule Installation',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Schedule installation with user',
        estimatedDuration: 30,
      },
      {
        id: 'remote-install',
        name: 'Remote Installation',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Install software remotely',
        estimatedDuration: 60,
      },
      {
        id: 'verify-install',
        name: 'Verify Installation',
        type: 'manual',
        assignedTo: 'requester',
        description: 'User confirms software works',
        estimatedDuration: 30,
      },
    ],
  },

  // Web Access Templates
  {
    id: 'web-access-request',
    name: 'Web Access Request',
    nameJp: 'Webアクセス申請',
    description: 'Request access to blocked websites',
    descriptionJp: 'ブロックされたWebサイトへのアクセス申請',
    category: 'web_access',
    serviceCategoryId: 'WBR',
    priority: 'low',
    slaHours: 72,
    tags: ['web', 'access', 'security'],
    requiredApprovals: 2,
    steps: [
      {
        id: 'security-review',
        name: 'Security Review',
        type: 'manual',
        assignedTo: 'role:IT Helpdesk Support',
        description: 'Review website for security risks',
        estimatedDuration: 60,
        requiredFields: ['security_assessment', 'risk_level'],
      },
      {
        id: 'manager-approval',
        name: 'Manager Approval',
        type: 'approval',
        assignedTo: 'manager',
        description: 'Business justification approval',
        estimatedDuration: 480,
        timeoutAction: 'auto_reject',
        timeoutHours: 72,
      },
      {
        id: 'security-approval',
        name: 'Security Team Approval',
        type: 'approval',
        assignedTo: 'role:IT Security',
        description: 'Final security approval',
        estimatedDuration: 480,
      },
      {
        id: 'grant-access',
        name: 'Grant Web Access',
        type: 'automated',
        action: 'update_web_filter',
        description: 'Update firewall rules',
        estimatedDuration: 15,
      },
    ],
  },
];

// Helper function to get template by ID
export function getWorkflowTemplate(templateId: string): WorkflowTemplate | undefined {
  return workflowTemplates.find(t => t.id === templateId);
}

// Helper function to get templates by category
export function getTemplatesByCategory(category: string): WorkflowTemplate[] {
  return workflowTemplates.filter(t => t.category === category);
}

// Helper function to get templates by service category
export function getTemplatesByServiceCategory(serviceCategoryId: string): WorkflowTemplate[] {
  return workflowTemplates.filter(t => t.serviceCategoryId === serviceCategoryId);
}

// Helper function to search templates
export function searchTemplates(query: string): WorkflowTemplate[] {
  const lowerQuery = query.toLowerCase();
  return workflowTemplates.filter(t => 
    t.name.toLowerCase().includes(lowerQuery) ||
    t.nameJp.includes(query) ||
    t.description.toLowerCase().includes(lowerQuery) ||
    t.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
  );
}
