import { useEffect, useRef, useCallback } from 'react';
import { enhancedRealtimeService } from '@/lib/services/enhanced-realtime-service';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

type Tables = Database['public']['Tables'];
type TableName = keyof Tables;

interface UseRealtimeOptions<T extends TableName> {
  table: T;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  enabled?: boolean;
}

export function useRealtime<T extends TableName>(
  options: UseRealtimeOptions<T>,
  callback: (payload: RealtimePostgresChangesPayload<Tables[T]['Row']>) => void
) {
  const subscriptionKeyRef = useRef<string | null>(null);
  const callbackIdRef = useRef<string>(`hook-${Math.random().toString(36).substr(2, 9)}`);

  const subscribe = useCallback(() => {
    if (!options.enabled) return;

    const subscriptionKey = enhancedRealtimeService.subscribe(
      {
        table: options.table,
        filter: options.filter,
        event: options.event
      },
      callbackIdRef.current,
      callback
    );

    subscriptionKeyRef.current = subscriptionKey;
  }, [options.table, options.filter, options.event, options.enabled, callback]);

  const unsubscribe = useCallback(() => {
    if (subscriptionKeyRef.current) {
      enhancedRealtimeService.unsubscribe(
        subscriptionKeyRef.current,
        callbackIdRef.current
      );
      subscriptionKeyRef.current = null;
    }
  }, []);

  useEffect(() => {
    subscribe();
    return unsubscribe;
  }, [subscribe, unsubscribe]);

  return { subscribe, unsubscribe };
}
