import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

// API endpoint for future ServiceNow integration
export async function POST(
  request: NextRequest,
  { params }: { params: { integration: string } }
) {
  const integration = params.integration;
  
  // Verify API key
  const apiKey = request.headers.get('x-api-key');
  if (!apiKey || apiKey !== process.env.INTEGRATION_API_KEY) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    
    switch (integration) {
      case 'servicenow':
        return handleServiceNowIntegration(body);
      
      case 'backlog':
        return handleBacklogIntegration(body);
      
      default:
        return NextResponse.json(
          { error: 'Unknown integration' },
          { status: 404 }
        );
    }
  } catch (error) {
    console.error('Integration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleServiceNowIntegration(data: any) {
  const supabase = createClient();
  
  // Example: Create IT request from ServiceNow ticket
  if (data.action === 'create_request') {
    const { data: request, error } = await supabase
      .from('request_forms')
      .insert({