exports.id=9152,exports.ids=[9152],exports.modules={96487:()=>{},78335:()=>{},56262:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var i=r(10669),s=r(68335);class n{constructor(){this.redis=(0,i.n)(),this.memoryCache=new Map,this.tagMap=new Map,this.defaultTTL=3600,this.maxMemoryCacheSize=1e3,this.version="1.0.0",setInterval(()=>this.cleanupMemoryCache(),3e5)}async get(e){let t=Date.now();try{let r=await this.redis.get(e);if(r){let i=Date.now()-t;if(s.qd.timing("cache.get_time",i),s.qd.increment("cache.redis_hits"),this.isValidCacheEntry(r))return r.data;await this.redis.del(e)}if(this.memoryCache.has(e)){let t=this.memoryCache.get(e);if(this.isValidCacheEntry(t))return s.qd.increment("cache.memory_hits"),t.data;this.memoryCache.delete(e)}return s.qd.increment("cache.misses"),null}catch(e){return console.error("Cache GET error:",e),s.qd.increment("cache.errors"),null}}async set(e,t,r={}){let{ttl:i=this.defaultTTL,tags:n=[],compress:c=!1,serialize:a=!0}=r,o={data:t,timestamp:Date.now(),ttl:i,tags:n,version:this.version},h=Date.now();try{let t=await this.redis.set(e,o,i);this.setMemoryCache(e,o),this.updateTagMappings(e,n);let r=Date.now()-h;return s.qd.timing("cache.set_time",r),s.qd.increment("cache.sets"),t}catch(t){return console.error("Cache SET error:",t),s.qd.increment("cache.errors"),this.setMemoryCache(e,o),!1}}async del(e){try{let t=await this.redis.del(e);return this.memoryCache.delete(e),this.removeFromTagMappings(e),s.qd.increment("cache.deletes"),t>0}catch(e){return console.error("Cache DEL error:",e),s.qd.increment("cache.errors"),!1}}async exists(e){try{if(await this.redis.exists(e))return!0;return this.memoryCache.has(e)}catch(t){return console.error("Cache EXISTS error:",t),this.memoryCache.has(e)}}async invalidateByTags(e){let t=0;for(let r of e){let e=this.tagMap.get(r);if(e){for(let r of e)await this.del(r),t++;this.tagMap.delete(r)}}return s.qd.increment("cache.tag_invalidations",{count:t}),t}async invalidateByPattern(e){try{let t=await this.redis.flushPattern(e);for(let[t]of this.memoryCache)this.matchesPattern(t,e)&&this.memoryCache.delete(t);return s.qd.increment("cache.pattern_invalidations",{count:t}),t}catch(e){return console.error("Cache pattern invalidation error:",e),0}}async getOrSet(e,t,r={}){let i=await this.get(e);if(null!==i)return i;let n=Date.now();try{let i=await t();await this.set(e,i,r);let c=Date.now()-n;return s.qd.timing("cache.fetch_time",c),s.qd.increment("cache.cache_misses_filled"),i}catch(e){throw s.qd.increment("cache.fetch_errors"),e}}async warmUp(e){let t=Date.now(),r=e.map(({key:e,value:t,options:r})=>this.set(e,t,r));await Promise.all(r);let i=Date.now()-t;s.qd.timing("cache.warmup_time",i),s.qd.increment("cache.warmups",{count:e.length})}async getStats(){let e=this.redis.getMetrics(),t=await this.redis.getInfo();return{redis:{connected:this.redis.isHealthy(),hitRate:this.redis.getHitRate(),metrics:e,info:t},memory:{size:this.memoryCache.size,maxSize:this.maxMemoryCacheSize},tags:{count:this.tagMap.size,mappings:Array.from(this.tagMap.entries()).map(([e,t])=>({tag:e,keyCount:t.size}))}}}async clear(){try{await this.redis.flushPattern("*"),this.memoryCache.clear(),this.tagMap.clear(),s.qd.increment("cache.clears")}catch(e){console.error("Cache clear error:",e)}}async healthCheck(){let e=await this.redis.ping(),t=this.memoryCache.size<this.maxMemoryCacheSize;return{healthy:e&&t,details:{redis:e,memory:{healthy:t,size:this.memoryCache.size,maxSize:this.maxMemoryCacheSize}}}}isValidCacheEntry(e){return!!e&&"object"==typeof e&&Date.now()<e.timestamp+1e3*e.ttl&&e.version===this.version}setMemoryCache(e,t){if(this.memoryCache.size>=this.maxMemoryCacheSize){let e=this.memoryCache.keys().next().value;e&&this.memoryCache.delete(e)}this.memoryCache.set(e,t)}updateTagMappings(e,t){for(let r of t)this.tagMap.has(r)||this.tagMap.set(r,new Set),this.tagMap.get(r).add(e)}removeFromTagMappings(e){for(let[t,r]of this.tagMap)r.delete(e),0===r.size&&this.tagMap.delete(t)}matchesPattern(e,t){return new RegExp(t.replace(/\*/g,".*")).test(e)}cleanupMemoryCache(){Date.now();let e=0;for(let[t,r]of this.memoryCache)!this.isValidCacheEntry(r)&&(this.memoryCache.delete(t),e++);e>0&&s.qd.increment("cache.memory_cleanup",{count:e})}}let c=null,a=()=>(c||(c=new n),c)},10669:(e,t,r)=>{"use strict";r.d(t,{n:()=>o});var i=r(50547),s=r.n(i),n=r(68335);class c{constructor(e){this.client=null,this.isConnected=!1,this.metrics={hits:0,misses:0,sets:0,deletes:0,errors:0,connectionTime:0},this.config={host:process.env.REDIS_HOST||"localhost",port:parseInt(process.env.REDIS_PORT||"6379"),password:process.env.REDIS_PASSWORD,db:parseInt(process.env.REDIS_DB||"0"),maxRetriesPerRequest:3,retryDelayOnFailover:100,enableReadyCheck:!0,lazyConnect:!0,keepAlive:3e4,family:4,keyPrefix:process.env.REDIS_KEY_PREFIX||"itsync:",...e},this.initializeClient()}initializeClient(){if(!process.env.REDIS_URL&&!this.config.host){console.warn("Redis not configured, using memory cache fallback");return}let e={host:this.config.host,port:this.config.port,password:this.config.password,db:this.config.db,maxRetriesPerRequest:this.config.maxRetriesPerRequest,retryDelayOnFailover:this.config.retryDelayOnFailover,enableReadyCheck:this.config.enableReadyCheck,lazyConnect:this.config.lazyConnect,keepAlive:this.config.keepAlive,family:this.config.family,keyPrefix:this.config.keyPrefix,maxLoadingTimeout:5e3,retryStrategy:e=>Math.min(50*e,2e3),reconnectOnError:e=>e.message.includes("READONLY")};process.env.REDIS_URL?this.client=new(s())(process.env.REDIS_URL,e):this.client=new(s())(e),this.setupEventHandlers()}setupEventHandlers(){this.client&&(this.client.on("connect",()=>{console.log("Redis client connected"),this.isConnected=!0,n.qd.gauge("redis.connected",1)}),this.client.on("ready",()=>{console.log("Redis client ready"),n.qd.gauge("redis.ready",1)}),this.client.on("error",e=>{console.error("Redis client error:",e),this.metrics.errors++,this.isConnected=!1,n.qd.increment("redis.errors"),n.qd.gauge("redis.connected",0)}),this.client.on("close",()=>{console.log("Redis client connection closed"),this.isConnected=!1,n.qd.gauge("redis.connected",0)}),this.client.on("reconnecting",()=>{console.log("Redis client reconnecting"),n.qd.increment("redis.reconnections")}))}async connect(){if(!this.client)throw Error("Redis client not initialized");let e=Date.now();try{await this.client.connect(),this.metrics.connectionTime=Date.now()-e,n.qd.timing("redis.connection_time",this.metrics.connectionTime)}catch(e){throw this.metrics.errors++,n.qd.increment("redis.connection_errors"),e}}async disconnect(){this.client&&(await this.client.disconnect(),this.isConnected=!1)}async get(e){if(!this.client||!this.isConnected)return null;let t=Date.now();try{let r=await this.client.get(e),i=Date.now()-t;if(null===r)return this.metrics.misses++,n.qd.increment("redis.misses"),null;this.metrics.hits++,n.qd.increment("redis.hits"),n.qd.timing("redis.get_time",i);try{return JSON.parse(r)}catch{return r}}catch(e){return this.metrics.errors++,n.qd.increment("redis.get_errors"),console.error("Redis GET error:",e),null}}async set(e,t,r){if(!this.client||!this.isConnected)return!1;let i=Date.now();try{let s;let c="string"==typeof t?t:JSON.stringify(t);s=r?await this.client.setex(e,r,c):await this.client.set(e,c);let a=Date.now()-i;return this.metrics.sets++,n.qd.increment("redis.sets"),n.qd.timing("redis.set_time",a),"OK"===s}catch(e){return this.metrics.errors++,n.qd.increment("redis.set_errors"),console.error("Redis SET error:",e),!1}}async del(e){if(!this.client||!this.isConnected)return 0;let t=Date.now();try{let r=await this.client.del(e),i=Date.now()-t;return this.metrics.deletes++,n.qd.increment("redis.deletes"),n.qd.timing("redis.del_time",i),r}catch(e){return this.metrics.errors++,n.qd.increment("redis.del_errors"),console.error("Redis DEL error:",e),0}}async exists(e){if(!this.client||!this.isConnected)return!1;try{let t=await this.client.exists(e);return 1===t}catch(e){return this.metrics.errors++,n.qd.increment("redis.exists_errors"),console.error("Redis EXISTS error:",e),!1}}async expire(e,t){if(!this.client||!this.isConnected)return!1;try{let r=await this.client.expire(e,t);return 1===r}catch(e){return this.metrics.errors++,n.qd.increment("redis.expire_errors"),console.error("Redis EXPIRE error:",e),!1}}async ttl(e){if(!this.client||!this.isConnected)return -1;try{return await this.client.ttl(e)}catch(e){return this.metrics.errors++,n.qd.increment("redis.ttl_errors"),console.error("Redis TTL error:",e),-1}}async flushPattern(e){if(!this.client||!this.isConnected)return 0;try{let t=await this.client.keys(e);if(0===t.length)return 0;return await this.client.del(...t)}catch(e){return this.metrics.errors++,n.qd.increment("redis.flush_errors"),console.error("Redis FLUSH PATTERN error:",e),0}}async ping(){if(!this.client)return!1;try{let e=await this.client.ping();return"PONG"===e}catch(e){return console.error("Redis PING error:",e),!1}}getMetrics(){return{...this.metrics}}getHitRate(){let e=this.metrics.hits+this.metrics.misses;return e>0?this.metrics.hits/e*100:0}isHealthy(){return this.isConnected&&null!==this.client}async getInfo(){if(!this.client||!this.isConnected)return null;try{let e=await this.client.info();return this.parseRedisInfo(e)}catch(e){return console.error("Redis INFO error:",e),null}}parseRedisInfo(e){let t=e.split("\r\n"),r={},i="";for(let e of t)if(e.startsWith("#"))r[i=e.substring(2).toLowerCase()]={};else if(e.includes(":")){let[t,s]=e.split(":");i&&(r[i][t]=isNaN(Number(s))?s:Number(s))}return r}}let a=null,o=()=>(a||(a=new c),a)},68335:(e,t,r)=>{"use strict";r.d(t,{I_:()=>n,hf:()=>c,ip:()=>a,qd:()=>s});class i{record(e,t,r,i){let s={name:e,value:t,timestamp:Date.now(),tags:r,unit:i};this.metrics.has(e)||this.metrics.set(e,[]);let n=this.metrics.get(e);n.push(s);let c=Date.now()-this.retentionPeriod,a=n.filter(e=>e.timestamp>c);a.length>this.maxMetricsPerType&&a.splice(0,a.length-this.maxMetricsPerType),this.metrics.set(e,a)}increment(e,t){this.record(e,1,t,"count")}timing(e,t,r){this.record(e,t,r,"ms")}gauge(e,t,r){this.record(e,t,r,"gauge")}getMetrics(e){return this.metrics.get(e)||[]}getMetricNames(){return Array.from(this.metrics.keys())}getAggregatedMetrics(e,t=6e4){let r=Date.now()-t,i=this.getMetrics(e).filter(e=>e.timestamp>r);if(0===i.length)return{count:0,sum:0,avg:0,min:0,max:0};let s=i.map(e=>e.value),n=s.reduce((e,t)=>e+t,0);return{count:i.length,sum:n,avg:n/i.length,min:Math.min(...s),max:Math.max(...s)}}getSystemMetrics(){let e=Date.now();return{requests:{total:this.getAggregatedMetrics("http.requests",36e5).count,success:this.getAggregatedMetrics("http.requests.success",36e5).count,errors:this.getAggregatedMetrics("http.requests.error",36e5).count,response_time_avg:this.getAggregatedMetrics("http.response_time",36e5).avg},database:{connections:this.getAggregatedMetrics("db.connections",36e5).avg,query_time_avg:this.getAggregatedMetrics("db.query_time",36e5).avg,slow_queries:this.getAggregatedMetrics("db.slow_queries",36e5).count},ai:{requests_total:this.getAggregatedMetrics("ai.requests",36e5).count,cost_estimate:this.getAggregatedMetrics("ai.cost",36e5).sum,errors:this.getAggregatedMetrics("ai.errors",36e5).count,response_time_avg:this.getAggregatedMetrics("ai.response_time",36e5).avg},security:{failed_logins:this.getAggregatedMetrics("auth.failed_logins",36e5).count,rate_limit_hits:this.getAggregatedMetrics("rate_limit.hits",36e5).count,mfa_failures:this.getAggregatedMetrics("mfa.failures",36e5).count},system:{memory_usage:this.getAggregatedMetrics("system.memory",3e5).avg,cpu_usage:this.getAggregatedMetrics("system.cpu",3e5).avg,uptime:e-(this.getMetrics("system.start_time")[0]?.timestamp||e)}}}cleanup(){let e=Date.now()-this.retentionPeriod;for(let[t,r]of this.metrics.entries()){let i=r.filter(t=>t.timestamp>e);this.metrics.set(t,i)}}constructor(){this.metrics=new Map,this.maxMetricsPerType=1e3,this.retentionPeriod=864e5}}let s=new i;function n(e,t,r){let i={operation:e,success:r.toString()};s.timing("db.query_time",t,i),s.increment("db.queries",i),t>1e3&&s.increment("db.slow_queries",i)}function c(e,t,r,i,n){let c={provider:e,model:t};s.increment("ai.requests",c),s.timing("ai.response_time",r,c),s.record("ai.tokens",i,c,"count"),s.record("ai.cost",n,c,"usd")}function a(){let e=Math.round(process.memoryUsage().heapUsed/1024/1024);s.gauge("system.memory",e)}s.record("system.start_time",Date.now()),setInterval(()=>{s.cleanup()},36e5)},61487:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var i=r(49064),s=r(44512);let n=()=>{let e=(0,s.UL)();return(0,i.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:i})=>e.set(t,r,i))}catch{}}}})}}};