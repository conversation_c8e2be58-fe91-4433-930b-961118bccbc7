import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';
import { z } from 'zod';

// Validation schema for starting a workflow
const StartWorkflowSchema = z.object({
  workflow_definition_id: z.string().uuid(),
  request_id: z.string().uuid().optional(),
  context_data: z.record(z.any()).optional(),
});

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('workflow_instances')
      .select(`
        *,
        workflow_definition:workflow_definitions(*),
        request:request_forms(*),
        created_by_user:staff(name_en, name_jp)
      `)
      .order('started_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error fetching workflow instances:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user data
    const { data: userData } = await supabase
      .from('staff')
      .select('*')
      .eq('auth_id', user.id)
      .single();

    if (!userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = StartWorkflowSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { workflow_definition_id, request_id, context_data } = validation.data;

    // Initialize workflow engine
    const engine = new WorkflowEngine();

    try {
      // Start the workflow
      const workflowInstance = await engine.startWorkflow({
        workflowDefinitionId: workflow_definition_id,
        requestId: request_id,
        contextData: context_data || {},
        userId: userData.id,
      });

      return NextResponse.json({ data: workflowInstance }, { status: 201 });
    } catch (error: any) {
      console.error('Error starting workflow:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to start workflow' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error in workflow start:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
