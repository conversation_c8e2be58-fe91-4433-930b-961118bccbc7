/**
 * Bundle Size Analyzer and Optimizer
 * Analyzes bundle sizes and provides optimization recommendations
 */

import { promises as fs } from 'fs'
import path from 'path'

interface BundleStats {
  name: string
  size: number
  gzipSize: number
  modules: ModuleInfo[]
  chunks: ChunkInfo[]
  assets: AssetInfo[]
}

interface ModuleInfo {
  name: string
  size: number
  chunks: string[]
  reasons: string[]
}

interface ChunkInfo {
  name: string
  size: number
  modules: string[]
  parents: string[]
  children: string[]
}

interface AssetInfo {
  name: string
  size: number
  type: string
  compressed?: boolean
}

interface OptimizationRecommendation {
  type: 'code-splitting' | 'tree-shaking' | 'compression' | 'lazy-loading' | 'dependency'
  severity: 'low' | 'medium' | 'high'
  description: string
  impact: string
  solution: string
  estimatedSavings: number
}

interface PerformanceMetrics {
  bundleSize: number
  gzipSize: number
  loadTime: number
  parseTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number
}

class BundleAnalyzer {
  private buildDir: string
  private analysisCache = new Map<string, BundleStats>()

  constructor(buildDir: string = '.next') {
    this.buildDir = buildDir
  }

  /**
   * Analyze bundle sizes and generate report
   */
  async analyzeBundles(): Promise<{
    stats: BundleStats[]
    recommendations: OptimizationRecommendation[]
    metrics: PerformanceMetrics
  }> {
    const stats = await this.getBundleStats()
    const recommendations = this.generateRecommendations(stats)
    const metrics = await this.calculatePerformanceMetrics(stats)

    return {
      stats,
      recommendations,
      metrics
    }
  }

  /**
   * Get detailed bundle statistics
   */
  async getBundleStats(): Promise<BundleStats[]> {
    const buildManifest = await this.loadBuildManifest()
    const stats: BundleStats[] = []

    // Analyze main bundles
    for (const [name, files] of Object.entries(buildManifest.pages || {})) {
      const bundleStats = await this.analyzeBundleFiles(name, files as string[])
      stats.push(bundleStats)
    }

    // Analyze static chunks
    const staticChunks = buildManifest.chunks || {}
    for (const [name, files] of Object.entries(staticChunks)) {
      const bundleStats = await this.analyzeBundleFiles(`chunk-${name}`, files as string[])
      stats.push(bundleStats)
    }

    return stats
  }

  /**
   * Generate optimization recommendations
   */
  generateRecommendations(stats: BundleStats[]): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = []

    // Check for large bundles
    for (const bundle of stats) {
      if (bundle.size > 500 * 1024) { // 500KB
        recommendations.push({
          type: 'code-splitting',
          severity: 'high',
          description: `Bundle ${bundle.name} is ${this.formatSize(bundle.size)} which is quite large`,
          impact: 'Slow initial page load, poor user experience',
          solution: 'Implement code splitting and lazy loading for non-critical components',
          estimatedSavings: bundle.size * 0.3 // Estimate 30% reduction
        })
      }

      // Check for duplicate modules
      const moduleNames = bundle.modules.map(m => m.name)
      const duplicates = moduleNames.filter((name, index) => 
        moduleNames.indexOf(name) !== index
      )

      if (duplicates.length > 0) {
        recommendations.push({
          type: 'dependency',
          severity: 'medium',
          description: `Found ${duplicates.length} duplicate modules in ${bundle.name}`,
          impact: 'Increased bundle size and redundant code',
          solution: 'Configure webpack to deduplicate modules or use dynamic imports',
          estimatedSavings: duplicates.length * 10 * 1024 // Estimate 10KB per duplicate
        })
      }

      // Check compression ratio
      const compressionRatio = bundle.gzipSize / bundle.size
      if (compressionRatio > 0.7) {
        recommendations.push({
          type: 'compression',
          severity: 'medium',
          description: `Poor compression ratio (${(compressionRatio * 100).toFixed(1)}%) for ${bundle.name}`,
          impact: 'Larger download sizes than necessary',
          solution: 'Enable better compression or optimize code structure',
          estimatedSavings: bundle.size * (compressionRatio - 0.3)
        })
      }
    }

    // Check for large dependencies
    const allModules = stats.flatMap(s => s.modules)
    const largeModules = allModules.filter(m => m.size > 100 * 1024) // 100KB

    for (const module of largeModules) {
      recommendations.push({
        type: 'dependency',
        severity: 'high',
        description: `Large dependency: ${module.name} (${this.formatSize(module.size)})`,
        impact: 'Significant contribution to bundle size',
        solution: 'Consider lighter alternatives or lazy loading this dependency',
        estimatedSavings: module.size * 0.5
      })
    }

    return recommendations.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 }
      return severityOrder[b.severity] - severityOrder[a.severity]
    })
  }

  /**
   * Calculate performance metrics
   */
  async calculatePerformanceMetrics(stats: BundleStats[]): Promise<PerformanceMetrics> {
    const totalSize = stats.reduce((sum, s) => sum + s.size, 0)
    const totalGzipSize = stats.reduce((sum, s) => sum + s.gzipSize, 0)

    // Estimate load times based on bundle sizes
    // These are rough estimates based on average connection speeds
    const estimatedLoadTime = this.estimateLoadTime(totalGzipSize)
    const estimatedParseTime = this.estimateParseTime(totalSize)

    return {
      bundleSize: totalSize,
      gzipSize: totalGzipSize,
      loadTime: estimatedLoadTime,
      parseTime: estimatedParseTime,
      firstContentfulPaint: estimatedLoadTime + estimatedParseTime + 200,
      largestContentfulPaint: estimatedLoadTime + estimatedParseTime + 500,
      cumulativeLayoutShift: 0.1, // This would be measured in real usage
      firstInputDelay: estimatedParseTime * 0.1
    }
  }

  /**
   * Generate bundle size report
   */
  async generateReport(): Promise<string> {
    const analysis = await this.analyzeBundles()
    
    let report = '# Bundle Size Analysis Report\n\n'
    
    // Summary
    report += '## Summary\n\n'
    report += `- Total Bundle Size: ${this.formatSize(analysis.metrics.bundleSize)}\n`
    report += `- Gzipped Size: ${this.formatSize(analysis.metrics.gzipSize)}\n`
    report += `- Estimated Load Time: ${analysis.metrics.loadTime.toFixed(0)}ms\n`
    report += `- Estimated Parse Time: ${analysis.metrics.parseTime.toFixed(0)}ms\n\n`

    // Bundle breakdown
    report += '## Bundle Breakdown\n\n'
    report += '| Bundle | Size | Gzipped | Modules |\n'
    report += '|--------|------|---------|----------|\n'
    
    for (const bundle of analysis.stats) {
      report += `| ${bundle.name} | ${this.formatSize(bundle.size)} | ${this.formatSize(bundle.gzipSize)} | ${bundle.modules.length} |\n`
    }
    report += '\n'

    // Recommendations
    report += '## Optimization Recommendations\n\n'
    
    if (analysis.recommendations.length === 0) {
      report += 'No major optimization opportunities found. Great job! 🎉\n\n'
    } else {
      for (const rec of analysis.recommendations) {
        const emoji = rec.severity === 'high' ? '🔴' : rec.severity === 'medium' ? '🟡' : '🟢'
        report += `### ${emoji} ${rec.type.toUpperCase()} - ${rec.severity.toUpperCase()}\n\n`
        report += `**Issue:** ${rec.description}\n\n`
        report += `**Impact:** ${rec.impact}\n\n`
        report += `**Solution:** ${rec.solution}\n\n`
        report += `**Estimated Savings:** ${this.formatSize(rec.estimatedSavings)}\n\n`
        report += '---\n\n'
      }
    }

    // Performance metrics
    report += '## Performance Metrics\n\n'
    report += `- **First Contentful Paint:** ${analysis.metrics.firstContentfulPaint.toFixed(0)}ms\n`
    report += `- **Largest Contentful Paint:** ${analysis.metrics.largestContentfulPaint.toFixed(0)}ms\n`
    report += `- **First Input Delay:** ${analysis.metrics.firstInputDelay.toFixed(0)}ms\n`
    report += `- **Cumulative Layout Shift:** ${analysis.metrics.cumulativeLayoutShift.toFixed(3)}\n\n`

    // Recommendations summary
    const totalSavings = analysis.recommendations.reduce((sum, rec) => sum + rec.estimatedSavings, 0)
    if (totalSavings > 0) {
      report += `## Potential Savings\n\n`
      report += `By implementing all recommendations, you could potentially save **${this.formatSize(totalSavings)}** `
      report += `(${((totalSavings / analysis.metrics.bundleSize) * 100).toFixed(1)}% reduction).\n\n`
    }

    return report
  }

  /**
   * Monitor bundle size changes over time
   */
  async trackBundleSize(): Promise<void> {
    const analysis = await this.analyzeBundles()
    const timestamp = new Date().toISOString()
    
    const record = {
      timestamp,
      totalSize: analysis.metrics.bundleSize,
      gzipSize: analysis.metrics.gzipSize,
      bundles: analysis.stats.map(s => ({
        name: s.name,
        size: s.size,
        gzipSize: s.gzipSize
      }))
    }

    // Save to tracking file
    const trackingFile = path.join(this.buildDir, 'bundle-tracking.json')
    let history: any[] = []
    
    try {
      const existing = await fs.readFile(trackingFile, 'utf-8')
      history = JSON.parse(existing)
    } catch {
      // File doesn't exist yet
    }

    history.push(record)
    
    // Keep only last 30 records
    if (history.length > 30) {
      history = history.slice(-30)
    }

    await fs.writeFile(trackingFile, JSON.stringify(history, null, 2))
  }

  // Private helper methods
  private async loadBuildManifest(): Promise<any> {
    try {
      const manifestPath = path.join(this.buildDir, 'build-manifest.json')
      const content = await fs.readFile(manifestPath, 'utf-8')
      return JSON.parse(content)
    } catch (error) {
      console.warn('Could not load build manifest:', error)
      return { pages: {}, chunks: {} }
    }
  }

  private async analyzeBundleFiles(name: string, files: string[]): Promise<BundleStats> {
    const modules: ModuleInfo[] = []
    const chunks: ChunkInfo[] = []
    const assets: AssetInfo[] = []
    let totalSize = 0
    let totalGzipSize = 0

    for (const file of files) {
      try {
        const filePath = path.join(this.buildDir, 'static', file)
        const stats = await fs.stat(filePath)
        const size = stats.size
        
        totalSize += size
        totalGzipSize += size * 0.3 // Estimate gzip compression

        assets.push({
          name: file,
          size,
          type: path.extname(file),
          compressed: false
        })

        // Simulate module analysis (in real implementation, you'd parse the bundle)
        modules.push({
          name: file,
          size,
          chunks: [name],
          reasons: ['entry']
        })

      } catch (error) {
        // File might not exist or be accessible
        console.warn(`Could not analyze file ${file}:`, error)
      }
    }

    chunks.push({
      name,
      size: totalSize,
      modules: files,
      parents: [],
      children: []
    })

    return {
      name,
      size: totalSize,
      gzipSize: totalGzipSize,
      modules,
      chunks,
      assets
    }
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`
  }

  private estimateLoadTime(gzipSize: number): number {
    // Estimate based on average 3G connection (1.6 Mbps)
    const bitsPerSecond = 1.6 * 1024 * 1024
    const bytesPerSecond = bitsPerSecond / 8
    return (gzipSize / bytesPerSecond) * 1000 // Convert to milliseconds
  }

  private estimateParseTime(size: number): number {
    // Rough estimate: 1ms per 1KB on average device
    return size / 1024
  }
}

export { BundleAnalyzer }
export type { 
  BundleStats, 
  ModuleInfo, 
  ChunkInfo, 
  AssetInfo, 
  OptimizationRecommendation, 
  PerformanceMetrics 
}
