'use client'

import AuthGuard from '@/components/auth/auth-guard'
import PermissionGate from '@/components/rbac/permission-gate'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield } from 'lucide-react'

export default function RolesManagementPage() {
  return (
    <AuthGuard>
      <PermissionGate 
        resource="users" 
        action="update" 
        fallback={
          <div className="flex min-h-screen items-center justify-center">
            <Card>
              <CardContent className="p-6 text-center">
                <Shield className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold mb-2">アクセス拒否 / Access Denied</h2>
                <p className="text-gray-600">
                  ユーザー管理にアクセスする権限がありません。
                  <br />
                  You don't have permission to manage users.
                </p>
              </CardContent>
            </Card>
          </div>
        }
      >
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div className="px-4 py-6 sm:px-0">
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-gray-900">
                  ロール管理 / Role Management
                </h1>
                <p className="text-gray-600">
                  スタッフのロールと権限を管理します / Manage staff roles and permissions
                </p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="mr-2 h-5 w-5" />
                    開発中 / Under Development
                  </CardTitle>
                  <CardDescription>
                    この機能は現在開発中です。後日実装予定です。
                    <br />
                    This feature is currently under development and will be implemented soon.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    Role management functionality will include:
                  </p>
                  <ul className="mt-2 text-sm text-gray-600 list-disc list-inside">
                    <li>User role assignment</li>
                    <li>Permission management</li>
                    <li>Department-based access control</li>
                    <li>Audit trail for role changes</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </PermissionGate>
    </AuthGuard>
  )
}
