-- Test batch processing functionality
-- This script creates test data and validates batch processing operations

-- Create test users for batch processing
INSERT INTO staff (auth_id, staff_id, name_jp, name_en, email, division_id, group_id, role_id)
SELECT 
  gen_random_uuid(),
  'TEST' || LPAD(generate_series::text, 3, '0'),
  'テストユーザー' || generate_series,
  'Test User ' || generate_series,
  'testuser' || generate_series || '@company.com',
  (SELECT id FROM divisions WHERE name_en = 'IT Systems Division'),
  (SELECT id FROM groups WHERE name_en = 'IT Systems Group'),
  (SELECT id FROM roles WHERE name = 'Regular User')
FROM generate_series(1, 5);

-- Test batch operation creation
DO $$
DECLARE
  v_requester_id UUID;
  v_batch_id UUID;
  v_result JSONB;
BEGIN
  -- Get a test requester
  SELECT id INTO v_requester_id 
  FROM staff 
  WHERE role_id = (SELECT id FROM roles WHERE name = 'IT Helpdesk Support')
  LIMIT 1;

  -- Create a batch operation
  INSERT INTO batch_operations (
    requester_id,
    operation_type,
    total_items,
    status
  ) VALUES (
    v_requester_id,
    'multi_user_single_service',
    5,
    'pending'
  ) RETURNING id INTO v_batch_id;

  -- Create request items
  -- Insert request items for each test user
  INSERT INTO request_items (
    service_category_id,
    affected_user_id,
    action_type,
    target_resource,
    request_details,
    status,
    batch_id
  )
  SELECT
    (SELECT id FROM service_categories LIMIT 1), -- Adjust as needed
    s.id,
    'grant_access', -- or another test action
    'Test Resource',
    jsonb_build_object('info', 'Batch test request'),
    'pending',
    v_batch_id
  FROM staff s
  WHERE s.staff_id LIKE 'TEST%';

  -- Optionally, return a summary (for debugging)
  RAISE NOTICE 'Batch operation % created with % items', v_batch_id, 5;
END;
$$;
  SELECT 
    (SELECT id FROM service_categories WHERE code = 'GM' LIMIT 1),
    s.id,
    'add',
    (SELECT id FROM group_mail_addresses LIMIT 1),
    '{"test": true}'::jsonb,
    'pending'
  FROM staff s
  WHERE s.staff_id LIKE 'TEST%'
  LIMIT 5;