import { useContext } from 'react';
import { AuthContext } from '@/lib/auth-context';

/**
 * Custom hook to access authentication context throughout the application
 * @returns Authentication context with user, profile, and auth methods
 */
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}
