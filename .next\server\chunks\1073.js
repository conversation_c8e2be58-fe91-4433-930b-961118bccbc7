exports.id=1073,exports.ids=[1073],exports.modules={41368:(t,e)=>{"use strict";var r=function(t,e){return e||(e={}),t.split("").forEach(function(t,r){t in e||(e[t]=r)}),e},i={alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",charmap:{0:14,1:8}};i.charmap=r(i.alphabet,i.charmap);var n={alphabet:"0123456789ABCDEFGHJKMNPQRSTVWXYZ",charmap:{O:0,I:1,L:1}};function o(t){if(this.buf=[],this.shift=8,this.carry=0,t){switch(t.type){case"rfc4648":this.charmap=e.rfc4648.charmap;break;case"crockford":this.charmap=e.crockford.charmap;break;default:throw Error("invalid type")}t.charmap&&(this.charmap=t.charmap)}}function s(t){if(this.buf="",this.shift=3,this.carry=0,t){switch(t.type){case"rfc4648":this.alphabet=e.rfc4648.alphabet;break;case"crockford":this.alphabet=e.crockford.alphabet;break;default:throw Error("invalid type")}t.alphabet?this.alphabet=t.alphabet:t.lc&&(this.alphabet=this.alphabet.toLowerCase())}}n.charmap=r(n.alphabet,n.charmap),o.prototype.charmap=i.charmap,o.prototype.write=function(t){var e=this.charmap,r=this.buf,i=this.shift,n=this.carry;return t.toUpperCase().split("").forEach(function(t){if("="!=t){var o=255&e[t];(i-=5)>0?n|=o<<i:i<0?(r.push(n|o>>-i),i+=8,n=o<<i&255):(r.push(n|o),i=8,n=0)}}),this.shift=i,this.carry=n,this},o.prototype.finalize=function(t){return t&&this.write(t),8!==this.shift&&0!==this.carry&&(this.buf.push(this.carry),this.shift=8,this.carry=0),this.buf},s.prototype.alphabet=i.alphabet,s.prototype.write=function(t){var e,r,i,n=this.shift,o=this.carry;for(i=0;i<t.length;i++)e=o|(r=t[i])>>n,this.buf+=this.alphabet[31&e],n>5&&(n-=5,e=r>>n,this.buf+=this.alphabet[31&e]),o=r<<(n=5-n),n=8-n;return this.shift=n,this.carry=o,this},s.prototype.finalize=function(t){return t&&this.write(t),3!==this.shift&&(this.buf+=this.alphabet[31&this.carry],this.shift=3,this.carry=0),this.buf},e.encode=function(t,e){return new s(e).finalize(t)},e.decode=function(t,e){return new o(e).finalize(t)},e.Decoder=o,e.Encoder=s,e.charmap=r,e.crockford=n,e.rfc4648=i},80778:(t,e,r)=>{"use strict";var i=r(41368),n=i.Decoder.prototype.finalize;i.Decoder.prototype.finalize=function(t){return new Buffer(n.call(this,t))},t.exports=i},21464:t=>{"use strict";var e={single_source_shortest_paths:function(t,r,i){var n,o,s,a,h,l,u,f={},c={};c[r]=0;var p=e.PriorityQueue.make();for(p.push(r,0);!p.empty();)for(s in o=(n=p.pop()).value,a=n.cost,h=t[o]||{})h.hasOwnProperty(s)&&(l=a+h[s],u=c[s],(void 0===c[s]||u>l)&&(c[s]=l,p.push(s,l),f[s]=o));if(void 0!==i&&void 0===c[i])throw Error(["Could not find a path from ",r," to ",i,"."].join(""));return f},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],i=e;i;)r.push(i),t[i],i=t[i];return r.reverse(),r},find_path:function(t,r,i){var n=e.single_source_shortest_paths(t,r,i);return e.extract_shortest_path_from_predecessor_list(n,i)},PriorityQueue:{make:function(t){var r,i=e.PriorityQueue,n={};for(r in t=t||{},i)i.hasOwnProperty(r)&&(n[r]=i[r]);return n.queue=[],n.sorter=t.sorter||i.default_sorter,n},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){this.queue.push({value:t,cost:e}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e},97305:(t,e,r)=>{"use strict";let i=r(2794),n=[function(){},function(t,e,r,i){if(i===e.length)throw Error("Ran out of data");let n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=255},function(t,e,r,i){if(i+1>=e.length)throw Error("Ran out of data");let n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=e[i+1]},function(t,e,r,i){if(i+2>=e.length)throw Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=255},function(t,e,r,i){if(i+3>=e.length)throw Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=e[i+3]}],o=[function(){},function(t,e,r,i){let n=e[0];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=i},function(t,e,r){let i=e[0];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=e[1]},function(t,e,r,i){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=i},function(t,e,r){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=e[3]}];e.dataToBitMap=function(t,e){let r,s,a,h,l=e.width,u=e.height,f=e.depth,c=e.bpp,p=e.interlace;if(8!==f){let e,i;e=[],i=0,r={get:function(r){for(;e.length<r;)!function(){let r,n,o,s,a,h,l,u;if(i===t.length)throw Error("Ran out of data");let c=t[i];switch(i++,f){default:throw Error("unrecognised depth");case 16:l=t[i],i++,e.push((c<<8)+l);break;case 4:l=15&c,u=c>>4,e.push(u,l);break;case 2:a=3&c,h=c>>2&3,l=c>>4&3,u=c>>6&3,e.push(u,l,h,a);break;case 1:r=1&c,n=c>>1&1,o=c>>2&1,s=c>>3&1,a=c>>4&1,h=c>>5&1,l=c>>6&1,u=c>>7&1,e.push(u,l,h,a,s,o,n,r)}}();let n=e.slice(0,r);return e=e.slice(r),n},resetAfterLine:function(){e.length=0},end:function(){if(i!==t.length)throw Error("extra data found")}}}s=f<=8?Buffer.alloc(l*u*4):new Uint16Array(l*u*4);let d=Math.pow(2,f)-1,g=0;if(p)a=i.getImagePasses(l,u),h=i.getInterlaceIterator(l,u);else{let t=0;h=function(){let e=t;return t+=4,e},a=[{width:l,height:u}]}for(let e=0;e<a.length;e++)8===f?g=function(t,e,r,i,o,s){let a=t.width,h=t.height,l=t.index;for(let t=0;t<h;t++)for(let h=0;h<a;h++){let a=r(h,t,l);n[i](e,o,a,s),s+=i}return s}(a[e],s,h,c,t,g):function(t,e,r,i,n,s){let a=t.width,h=t.height,l=t.index;for(let t=0;t<h;t++){for(let h=0;h<a;h++){let a=n.get(i),u=r(h,t,l);o[i](e,a,u,s)}n.resetAfterLine()}}(a[e],s,h,c,r,d);if(8===f){if(g!==t.length)throw Error("extra data found")}else r.end();return s}},65562:(t,e,r)=>{"use strict";let i=r(49758);t.exports=function(t,e,r,n){let o=-1!==[i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(n.colorType);if(n.colorType===n.inputColorType){let e;let r=(new DataView(e=new ArrayBuffer(2)).setInt16(0,256,!0),256!==new Int16Array(e)[0]);if(8===n.bitDepth||16===n.bitDepth&&r)return t}let s=16!==n.bitDepth?t:new Uint16Array(t.buffer),a=255,h=i.COLORTYPE_TO_BPP_MAP[n.inputColorType];4!==h||n.inputHasAlpha||(h=3);let l=i.COLORTYPE_TO_BPP_MAP[n.colorType];16===n.bitDepth&&(a=65535,l*=2);let u=Buffer.alloc(e*r*l),f=0,c=0,p=n.bgColor||{};void 0===p.red&&(p.red=a),void 0===p.green&&(p.green=a),void 0===p.blue&&(p.blue=a);for(let t=0;t<r;t++)for(let t=0;t<e;t++){let t=function(){let t,e,r;let h=a;switch(n.inputColorType){case i.COLORTYPE_COLOR_ALPHA:h=s[f+3],t=s[f],e=s[f+1],r=s[f+2];break;case i.COLORTYPE_COLOR:t=s[f],e=s[f+1],r=s[f+2];break;case i.COLORTYPE_ALPHA:h=s[f+1],e=t=s[f],r=t;break;case i.COLORTYPE_GRAYSCALE:e=t=s[f],r=t;break;default:throw Error("input color type:"+n.inputColorType+" is not supported at present")}return n.inputHasAlpha&&!o&&(h/=a,t=Math.min(Math.max(Math.round((1-h)*p.red+h*t),0),a),e=Math.min(Math.max(Math.round((1-h)*p.green+h*e),0),a),r=Math.min(Math.max(Math.round((1-h)*p.blue+h*r),0),a)),{red:t,green:e,blue:r,alpha:h}}(s,f);switch(n.colorType){case i.COLORTYPE_COLOR_ALPHA:case i.COLORTYPE_COLOR:8===n.bitDepth?(u[c]=t.red,u[c+1]=t.green,u[c+2]=t.blue,o&&(u[c+3]=t.alpha)):(u.writeUInt16BE(t.red,c),u.writeUInt16BE(t.green,c+2),u.writeUInt16BE(t.blue,c+4),o&&u.writeUInt16BE(t.alpha,c+6));break;case i.COLORTYPE_ALPHA:case i.COLORTYPE_GRAYSCALE:{let e=(t.red+t.green+t.blue)/3;8===n.bitDepth?(u[c]=e,o&&(u[c+1]=t.alpha)):(u.writeUInt16BE(e,c),o&&u.writeUInt16BE(t.alpha,c+2));break}default:throw Error("unrecognised color Type "+n.colorType)}f+=h,c+=l}return u}},43590:(t,e,r)=>{"use strict";let i=r(28354),n=r(27910),o=t.exports=function(){n.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};i.inherits(o,n),o.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick((function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}).bind(this))},o.prototype.write=function(t,e){let r;return this.writable?(Buffer.isBuffer(t)?r=t:r=Buffer.from(t,e||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",Error("Stream not writable")),!1)},o.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},o.prototype.destroySoon=o.prototype.end,o.prototype._end=function(){this._reads.length>0&&this.emit("error",Error("Unexpected end of input")),this.destroy()},o.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},o.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},o.prototype._processRead=function(t){this._reads.shift();let e=0,r=0,i=Buffer.alloc(t.length);for(;e<t.length;){let n=this._buffers[r++],o=Math.min(n.length,t.length-e);n.copy(i,e,0,o),e+=o,o!==n.length&&(this._buffers[--r]=n.slice(o))}r>0&&this._buffers.splice(0,r),this._buffered-=t.length,t.func.call(this,i)},o.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}},49758:t=>{"use strict";t.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:0x49484452,TYPE_IEND:0x49454e44,TYPE_IDAT:0x49444154,TYPE_PLTE:0x504c5445,TYPE_tRNS:0x74524e53,TYPE_gAMA:0x67414d41,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},81893:t=>{"use strict";let e=[];!function(){for(let t=0;t<256;t++){let r=t;for(let t=0;t<8;t++)1&r?r=0xedb88320^r>>>1:r>>>=1;e[t]=r}}();let r=t.exports=function(){this._crc=-1};r.prototype.write=function(t){for(let r=0;r<t.length;r++)this._crc=e[(this._crc^t[r])&255]^this._crc>>>8;return!0},r.prototype.crc32=function(){return -1^this._crc},r.crc32=function(t){let r=-1;for(let i=0;i<t.length;i++)r=e[(r^t[i])&255]^r>>>8;return -1^r}},15975:(t,e,r)=>{"use strict";let i=r(90116),n={0:function(t,e,r,i,n){for(let o=0;o<r;o++)i[n+o]=t[e+o]},1:function(t,e,r,i,n,o){for(let s=0;s<r;s++){let r=s>=o?t[e+s-o]:0,a=t[e+s]-r;i[n+s]=a}},2:function(t,e,r,i,n){for(let o=0;o<r;o++){let s=e>0?t[e+o-r]:0,a=t[e+o]-s;i[n+o]=a}},3:function(t,e,r,i,n,o){for(let s=0;s<r;s++){let a=s>=o?t[e+s-o]:0,h=e>0?t[e+s-r]:0,l=t[e+s]-(a+h>>1);i[n+s]=l}},4:function(t,e,r,n,o,s){for(let a=0;a<r;a++){let h=a>=s?t[e+a-s]:0,l=e>0?t[e+a-r]:0,u=e>0&&a>=s?t[e+a-(r+s)]:0,f=t[e+a]-i(h,l,u);n[o+a]=f}}},o={0:function(t,e,r){let i=0,n=e+r;for(let r=e;r<n;r++)i+=Math.abs(t[r]);return i},1:function(t,e,r,i){let n=0;for(let o=0;o<r;o++){let r=o>=i?t[e+o-i]:0;n+=Math.abs(t[e+o]-r)}return n},2:function(t,e,r){let i=0,n=e+r;for(let o=e;o<n;o++){let n=e>0?t[o-r]:0;i+=Math.abs(t[o]-n)}return i},3:function(t,e,r,i){let n=0;for(let o=0;o<r;o++){let s=o>=i?t[e+o-i]:0,a=e>0?t[e+o-r]:0;n+=Math.abs(t[e+o]-(s+a>>1))}return n},4:function(t,e,r,n){let o=0;for(let s=0;s<r;s++){let a=s>=n?t[e+s-n]:0,h=e>0?t[e+s-r]:0,l=e>0&&s>=n?t[e+s-(r+n)]:0;o+=Math.abs(t[e+s]-i(a,h,l))}return o}};t.exports=function(t,e,r,i,s){let a;if("filterType"in i&&-1!==i.filterType){if("number"==typeof i.filterType)a=[i.filterType];else throw Error("unrecognised filter types")}else a=[0,1,2,3,4];16===i.bitDepth&&(s*=2);let h=e*s,l=0,u=0,f=Buffer.alloc((h+1)*r),c=a[0];for(let e=0;e<r;e++){if(a.length>1){let e=1/0;for(let r=0;r<a.length;r++){let i=o[a[r]](t,u,h,s);i<e&&(c=a[r],e=i)}}f[l]=c,l++,n[c](t,u,h,f,l,s),l+=h,u+=h}return f}},61318:(t,e,r)=>{"use strict";let i=r(28354),n=r(43590),o=r(7715),s=t.exports=function(t){n.call(this);let e=[],r=this;this._filter=new o(t,{read:this.read.bind(this),write:function(t){e.push(t)},complete:function(){r.emit("complete",Buffer.concat(e))}}),this._filter.start()};i.inherits(s,n)},32267:(t,e,r)=>{"use strict";let i=r(6566),n=r(7715);e.process=function(t,e){let r=[],o=new i(t);return new n(e,{read:o.read.bind(o),write:function(t){r.push(t)},complete:function(){}}).start(),o.process(),Buffer.concat(r)}},7715:(t,e,r)=>{"use strict";let i=r(2794),n=r(90116);function o(t,e,r){let i=t*e;return 8!==r&&(i=Math.ceil(i/(8/r))),i}let s=t.exports=function(t,e){let r=t.width,n=t.height,s=t.interlace,a=t.bpp,h=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],s){let t=i.getImagePasses(r,n);for(let e=0;e<t.length;e++)this._images.push({byteWidth:o(t[e].width,a,h),height:t[e].height,lineIndex:0})}else this._images.push({byteWidth:o(r,a,h),height:n,lineIndex:0});8===h?this._xComparison=a:16===h?this._xComparison=2*a:this._xComparison=1};s.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},s.prototype._unFilterType1=function(t,e,r){let i=this._xComparison,n=i-1;for(let o=0;o<r;o++){let r=t[1+o],s=o>n?e[o-i]:0;e[o]=r+s}},s.prototype._unFilterType2=function(t,e,r){let i=this._lastLine;for(let n=0;n<r;n++){let r=t[1+n],o=i?i[n]:0;e[n]=r+o}},s.prototype._unFilterType3=function(t,e,r){let i=this._xComparison,n=i-1,o=this._lastLine;for(let s=0;s<r;s++){let r=t[1+s],a=o?o[s]:0,h=Math.floor(((s>n?e[s-i]:0)+a)/2);e[s]=r+h}},s.prototype._unFilterType4=function(t,e,r){let i=this._xComparison,o=i-1,s=this._lastLine;for(let a=0;a<r;a++){let r=t[1+a],h=s?s[a]:0,l=n(a>o?e[a-i]:0,h,a>o&&s?s[a-i]:0);e[a]=r+l}},s.prototype._reverseFilterLine=function(t){let e,r=t[0],i=this._images[this._imageIndex],n=i.byteWidth;if(0===r)e=t.slice(1,n+1);else switch(e=Buffer.alloc(n),r){case 1:this._unFilterType1(t,e,n);break;case 2:this._unFilterType2(t,e,n);break;case 3:this._unFilterType3(t,e,n);break;case 4:this._unFilterType4(t,e,n);break;default:throw Error("Unrecognised filter type - "+r)}this.write(e),i.lineIndex++,i.lineIndex>=i.height?(this._lastLine=null,this._imageIndex++,i=this._images[this._imageIndex]):this._lastLine=e,i?this.read(i.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},52561:t=>{"use strict";t.exports=function(t,e){let r=e.depth,i=e.width,n=e.height,o=e.colorType,s=e.transColor,a=e.palette,h=t;return 3===o?function(t,e,r,i,n){let o=0;for(let s=0;s<i;s++)for(let i=0;i<r;i++){let r=n[t[o]];if(!r)throw Error("index "+t[o]+" not in palette");for(let t=0;t<4;t++)e[o+t]=r[t];o+=4}}(t,h,i,n,a):(s&&function(t,e,r,i,n){let o=0;for(let s=0;s<i;s++)for(let i=0;i<r;i++){let r=!1;if(1===n.length?n[0]===t[o]&&(r=!0):n[0]===t[o]&&n[1]===t[o+1]&&n[2]===t[o+2]&&(r=!0),r)for(let t=0;t<4;t++)e[o+t]=0;o+=4}}(t,h,i,n,s),8!==r&&(16===r&&(h=Buffer.alloc(i*n*4)),function(t,e,r,i,n){let o=Math.pow(2,n)-1,s=0;for(let n=0;n<i;n++)for(let i=0;i<r;i++){for(let r=0;r<4;r++)e[s+r]=Math.floor(255*t[s+r]/o+.5);s+=4}}(t,h,i,n,r))),h}},2794:(t,e)=>{"use strict";let r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];e.getImagePasses=function(t,e){let i=[],n=t%8,o=e%8,s=(t-n)/8,a=(e-o)/8;for(let t=0;t<r.length;t++){let e=r[t],h=s*e.x.length,l=a*e.y.length;for(let t=0;t<e.x.length;t++)if(e.x[t]<n)h++;else break;for(let t=0;t<e.y.length;t++)if(e.y[t]<o)l++;else break;h>0&&l>0&&i.push({width:h,height:l,index:t})}return i},e.getInterlaceIterator=function(t){return function(e,i,n){let o=e%r[n].x.length,s=(e-o)/r[n].x.length*8+r[n].x[o],a=i%r[n].y.length;return 4*s+((i-a)/r[n].y.length*8+r[n].y[a])*t*4}}},24e3:(t,e,r)=>{"use strict";let i=r(28354),n=r(27910),o=r(49758),s=r(85977),a=t.exports=function(t){n.call(this),this._packer=new s(t||{}),this._deflate=this._packer.createDeflate(),this.readable=!0};i.inherits(a,n),a.prototype.pack=function(t,e,r,i){this.emit("data",Buffer.from(o.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,r)),i&&this.emit("data",this._packer.packGAMA(i));let n=this._packer.filterData(t,e,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",(function(t){this.emit("data",this._packer.packIDAT(t))}).bind(this)),this._deflate.on("end",(function(){this.emit("data",this._packer.packIEND()),this.emit("end")}).bind(this)),this._deflate.end(n)}},37193:(t,e,r)=>{"use strict";let i=!0,n=r(74075);n.deflateSync||(i=!1);let o=r(49758),s=r(85977);t.exports=function(t,e){if(!i)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r=new s(e||{}),a=[];a.push(Buffer.from(o.PNG_SIGNATURE)),a.push(r.packIHDR(t.width,t.height)),t.gamma&&a.push(r.packGAMA(t.gamma));let h=r.filterData(t.data,t.width,t.height),l=n.deflateSync(h,r.getDeflateOptions());if(h=null,!l||!l.length)throw Error("bad png - invalid compressed data response");return a.push(r.packIDAT(l)),a.push(r.packIEND()),Buffer.concat(a)}},85977:(t,e,r)=>{"use strict";let i=r(49758),n=r(81893),o=r(65562),s=r(15975),a=r(74075),h=t.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||a.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"==typeof t.colorType?t.colorType:i.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"==typeof t.inputColorType?t.inputColorType:i.COLORTYPE_COLOR_ALPHA,-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(t.colorType))throw Error("option color type:"+t.colorType+" is not supported at present");if(-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw Error("option bit depth:"+t.bitDepth+" is not supported at present")};h.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},h.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},h.prototype.filterData=function(t,e,r){let n=o(t,e,r,this._options),a=i.COLORTYPE_TO_BPP_MAP[this._options.colorType];return s(n,e,r,this._options,a)},h.prototype._packChunk=function(t,e){let r=e?e.length:0,i=Buffer.alloc(r+12);return i.writeUInt32BE(r,0),i.writeUInt32BE(t,4),e&&e.copy(i,8),i.writeInt32BE(n.crc32(i.slice(4,i.length-4)),i.length-4),i},h.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*i.GAMMA_DIVISION),0),this._packChunk(i.TYPE_gAMA,e)},h.prototype.packIHDR=function(t,e){let r=Buffer.alloc(13);return r.writeUInt32BE(t,0),r.writeUInt32BE(e,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(i.TYPE_IHDR,r)},h.prototype.packIDAT=function(t){return this._packChunk(i.TYPE_IDAT,t)},h.prototype.packIEND=function(){return this._packChunk(i.TYPE_IEND,null)}},90116:t=>{"use strict";t.exports=function(t,e,r){let i=t+e-r,n=Math.abs(i-t),o=Math.abs(i-e),s=Math.abs(i-r);return n<=o&&n<=s?t:o<=s?e:r}},82049:(t,e,r)=>{"use strict";let i=r(28354),n=r(74075),o=r(43590),s=r(61318),a=r(99976),h=r(97305),l=r(52561),u=t.exports=function(t){o.call(this),this._parser=new a(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};i.inherits(u,o),u.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},u.prototype._inflateData=function(t){if(!this._inflate){if(this._bitmapInfo.interlace)this._inflate=n.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let t=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,e=Math.max(t,n.Z_MIN_CHUNK);this._inflate=n.createInflate({chunkSize:e});let r=t,i=this.emit.bind(this,"error");this._inflate.on("error",function(t){r&&i(t)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(t){r&&(t.length>r&&(t=t.slice(0,r)),r-=t.length,o(t))}),this._inflate.on("end",this._filter.end.bind(this._filter))}}this._inflate.write(t)},u.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new s(this._bitmapInfo)},u.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},u.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},u.prototype._simpleTransparency=function(){this._metaData.alpha=!0},u.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},u.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},u.prototype._complete=function(t){let e;if(!this.errord){try{let r=h.dataToBitMap(t,this._bitmapInfo);e=l(r,this._bitmapInfo),r=null}catch(t){this._handleError(t);return}this.emit("parsed",e)}}},38118:(t,e,r)=>{"use strict";let i=!0,n=r(74075),o=r(46830);n.deflateSync||(i=!1);let s=r(6566),a=r(32267),h=r(99976),l=r(97305),u=r(52561);t.exports=function(t,e){let r,f,c,p;if(!i)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let d=[],g=new s(t);if(new h(e,{read:g.read.bind(g),error:function(t){r=t},metadata:function(t){f=t},gamma:function(t){c=t},palette:function(t){f.palette=t},transColor:function(t){f.transColor=t},inflateData:function(t){d.push(t)},simpleTransparency:function(){f.alpha=!0}}).start(),g.process(),r)throw r;let _=Buffer.concat(d);if(d.length=0,f.interlace)p=n.inflateSync(_);else{let t=((f.width*f.bpp*f.depth+7>>3)+1)*f.height;p=o(_,{chunkSize:t,maxLength:t})}if(_=null,!p||!p.length)throw Error("bad png - invalid inflate data response");let m=a.process(p,f);_=null;let y=l.dataToBitMap(m,f);m=null;let b=u(y,f);return f.data=b,f.gamma=c||0,f}},99976:(t,e,r)=>{"use strict";let i=r(49758),n=r(81893),o=t.exports=function(t,e){this._options=t,t.checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[i.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[i.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[i.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[i.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[i.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[i.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};o.prototype.start=function(){this.read(i.PNG_SIGNATURE.length,this._parseSignature.bind(this))},o.prototype._parseSignature=function(t){let e=i.PNG_SIGNATURE;for(let r=0;r<e.length;r++)if(t[r]!==e[r]){this.error(Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))},o.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),r=t.readUInt32BE(4),o="";for(let e=4;e<8;e++)o+=String.fromCharCode(t[e]);let s=!!(32&t[4]);if(!this._hasIHDR&&r!==i.TYPE_IHDR){this.error(Error("Expected IHDR on beggining"));return}if(this._crc=new n,this._crc.write(Buffer.from(o)),this._chunks[r])return this._chunks[r](e);if(!s){this.error(Error("Unsupported critical chunk type "+o));return}this.read(e+4,this._skipChunk.bind(this))},o.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},o.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),r=this._crc.crc32();if(this._options.checkCRC&&r!==e){this.error(Error("Crc error - "+e+" - "+r));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},o.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),r=t.readUInt32BE(4),n=t[8],o=t[9],s=t[10],a=t[11],h=t[12];if(8!==n&&4!==n&&2!==n&&1!==n&&16!==n){this.error(Error("Unsupported bit depth "+n));return}if(!(o in i.COLORTYPE_TO_BPP_MAP)){this.error(Error("Unsupported color type"));return}if(0!==s){this.error(Error("Unsupported compression method"));return}if(0!==a){this.error(Error("Unsupported filter method"));return}if(0!==h&&1!==h){this.error(Error("Unsupported interlace method"));return}this._colorType=o;let l=i.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:r,depth:n,interlace:!!h,palette:!!(o&i.COLORTYPE_PALETTE),color:!!(o&i.COLORTYPE_COLOR),alpha:!!(o&i.COLORTYPE_ALPHA),bpp:l,colorType:o}),this._handleChunkEnd()},o.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},o.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let r=0;r<e;r++)this._palette.push([t[3*r],t[3*r+1],t[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},o.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))},o.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===i.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length){this.error(Error("Transparency chunk must be after palette"));return}if(t.length>this._palette.length){this.error(Error("More transparent colors than palette size"));return}for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===i.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===i.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},o.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},o.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/i.GAMMA_DIVISION),this._handleChunkEnd()},o.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))},o.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===i.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw Error("Expected palette not found");this.inflateData(e);let r=t-e.length;r>0?this._handleIDAT(r):this._handleChunkEnd()},o.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},o.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},80294:(t,e,r)=>{"use strict";let i=r(38118),n=r(37193);e.read=function(t,e){return i(t,e||{})},e.write=function(t,e){return n(t,e)}},41960:(t,e,r)=>{"use strict";let i=r(28354),n=r(27910),o=r(82049),s=r(24e3),a=r(80294),h=e.O=function(t){n.call(this),t=t||{},this.width=0|t.width,this.height=0|t.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new o(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",(function(t){this.data=t,this.emit("parsed",t)}).bind(this)),this._packer=new s(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};i.inherits(h,n),h.sync=a,h.prototype.pack=function(){return this.data&&this.data.length?process.nextTick((function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}).bind(this)):this.emit("error","No data provided"),this},h.prototype.parse=function(t,e){if(e){let t,r;t=(function(t){this.removeListener("error",r),this.data=t,e(null,this)}).bind(this),r=(function(r){this.removeListener("parsed",t),e(r,null)}).bind(this),this.once("parsed",t),this.once("error",r)}return this.end(t),this},h.prototype.write=function(t){return this._parser.write(t),!0},h.prototype.end=function(t){this._parser.end(t)},h.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},h.prototype._gamma=function(t){this.gamma=t},h.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},h.bitblt=function(t,e,r,i,n,o,s,a){if(i|=0,n|=0,o|=0,s|=0,a|=0,(r|=0)>t.width||i>t.height||r+n>t.width||i+o>t.height)throw Error("bitblt reading outside image");if(s>e.width||a>e.height||s+n>e.width||a+o>e.height)throw Error("bitblt writing outside image");for(let h=0;h<o;h++)t.data.copy(e.data,(a+h)*e.width+s<<2,(i+h)*t.width+r<<2,(i+h)*t.width+r+n<<2)},h.prototype.bitblt=function(t,e,r,i,n,o,s){return h.bitblt(this,t,e,r,i,n,o,s),this},h.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let r=0;r<t.width;r++){let i=t.width*e+r<<2;for(let e=0;e<3;e++){let r=t.data[i+e]/255;r=Math.pow(r,1/2.2/t.gamma),t.data[i+e]=Math.round(255*r)}}t.gamma=0}},h.prototype.adjustGamma=function(){h.adjustGamma(this)}},46830:(t,e,r)=>{"use strict";let i=r(12412).ok,n=r(74075),o=r(28354),s=r(79428).kMaxLength;function a(t){if(!(this instanceof a))return new a(t);t&&t.chunkSize<n.Z_MIN_CHUNK&&(t.chunkSize=n.Z_MIN_CHUNK),n.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function h(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function l(t,e){return function(t,e){if("string"==typeof e&&(e=Buffer.from(e)),!(e instanceof Buffer))throw TypeError("Not a string or buffer");let r=t._finishFlushFlag;return null==r&&(r=n.Z_FINISH),t._processChunk(e,r)}(new a(e),t)}a.prototype._processChunk=function(t,e,r){let o,a;if("function"==typeof r)return n.Inflate._processChunk.call(this,t,e,r);let l=this,u=t&&t.length,f=this._chunkSize-this._offset,c=this._maxLength,p=0,d=[],g=0;this.on("error",function(t){o=t}),i(this._handle,"zlib binding closed");do a=(a=this._handle.writeSync(e,t,p,u,this._buffer,this._offset,f))||this._writeState;while(!this._hadError&&function(t,e){if(l._hadError)return;let r=f-e;if(i(r>=0,"have should not go down"),r>0){let t=l._buffer.slice(l._offset,l._offset+r);if(l._offset+=r,t.length>c&&(t=t.slice(0,c)),d.push(t),g+=t.length,0==(c-=t.length))return!1}return(0===e||l._offset>=l._chunkSize)&&(f=l._chunkSize,l._offset=0,l._buffer=Buffer.allocUnsafe(l._chunkSize)),0===e&&(p+=u-t,u=t,!0)}(a[0],a[1]));if(this._hadError)throw o;if(g>=s)throw h(this),RangeError("Cannot create final Buffer. It would be larger than 0x"+s.toString(16)+" bytes");let _=Buffer.concat(d,g);return h(this),_},o.inherits(a,n.Inflate),t.exports=e=l,e.Inflate=a,e.createInflate=function(t){return new a(t)},e.inflateSync=l},6566:t=>{"use strict";let e=t.exports=function(t){this._buffer=t,this._reads=[]};e.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},e.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}return this._reads.length>0?Error("There are some read requests waitng on finished stream"):this._buffer.length>0?Error("unrecognised content at end of stream"):void 0}},18139:(t,e,r)=>{let i=r(2541),n=r(23749),o=r(36735),s=r(388);function a(t,e,r,o,s){let a=[].slice.call(arguments,1),h=a.length,l="function"==typeof a[h-1];if(!l&&!i())throw Error("Callback required as last argument");if(l){if(h<2)throw Error("Too few arguments provided");2===h?(s=r,r=e,e=o=void 0):3===h&&(e.getContext&&void 0===s?(s=o,o=void 0):(s=o,o=r,r=e,e=void 0))}else{if(h<1)throw Error("Too few arguments provided");return 1===h?(r=e,e=o=void 0):2!==h||e.getContext||(o=r,r=e,e=void 0),new Promise(function(i,s){try{let s=n.create(r,o);i(t(s,e,o))}catch(t){s(t)}})}try{let i=n.create(r,o);s(null,t(i,e,o))}catch(t){s(t)}}n.create,e.toCanvas=a.bind(null,o.render),a.bind(null,o.renderToDataURL),a.bind(null,function(t,e,r){return s.render(t,r)})},2541:t=>{t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},31117:(t,e,r)=>{let i=r(77258).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];let e=Math.floor(t/7)+2,r=i(t),n=145===r?26:2*Math.ceil((r-13)/(2*e-2)),o=[r-7];for(let t=1;t<e-1;t++)o[t]=o[t-1]-n;return o.push(6),o.reverse()},e.getPositions=function(t){let r=[],i=e.getRowColCoords(t),n=i.length;for(let t=0;t<n;t++)for(let e=0;e<n;e++)(0!==t||0!==e)&&(0!==t||e!==n-1)&&(t!==n-1||0!==e)&&r.push([i[t],i[e]]);return r}},61097:(t,e,r)=>{let i=r(81016),n=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(t){this.mode=i.ALPHANUMERIC,this.data=t}o.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*n.indexOf(this.data[e]);r+=n.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(n.indexOf(this.data[e]),6)},t.exports=o},79663:t=>{function e(){this.buffer=[],this.length=0}e.prototype={get:function(t){let e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){let e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=e},25876:t=>{function e(t){if(!t||t<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}e.prototype.set=function(t,e,r,i){let n=t*this.size+e;this.data[n]=r,i&&(this.reservedBit[n]=!0)},e.prototype.get=function(t,e){return this.data[t*this.size+e]},e.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},e.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=e},67874:(t,e,r)=>{let i=r(81016);function n(t){this.mode=i.BYTE,"string"==typeof t?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}n.getBitsLength=function(t){return 8*t},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)},t.exports=n},10162:(t,e,r)=>{let i=r(84529),n=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case i.L:return n[(t-1)*4+0];case i.M:return n[(t-1)*4+1];case i.Q:return n[(t-1)*4+2];case i.H:return n[(t-1)*4+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case i.L:return o[(t-1)*4+0];case i.M:return o[(t-1)*4+1];case i.Q:return o[(t-1)*4+2];case i.H:return o[(t-1)*4+3];default:return}}},84529:(t,e)=>{e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw Error("Unknown EC Level: "+t)}}(t)}catch(t){return r}}},26860:(t,e,r)=>{let i=r(77258).getSymbolSize;e.getPositions=function(t){let e=i(t);return[[0,0],[e-7,0],[0,e-7]]}},64477:(t,e,r)=>{let i=r(77258),n=i.getBCHDigit(1335);e.getEncodedBits=function(t,e){let r=t.bit<<3|e,o=r<<10;for(;i.getBCHDigit(o)-n>=0;)o^=1335<<i.getBCHDigit(o)-n;return(r<<10|o)^21522}},16415:(t,e)=>{let r=new Uint8Array(512),i=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)r[e]=t,i[t]=e,256&(t<<=1)&&(t^=285);for(let t=255;t<512;t++)r[t]=r[t-255]})(),e.log=function(t){if(t<1)throw Error("log("+t+")");return i[t]},e.exp=function(t){return r[t]},e.mul=function(t,e){return 0===t||0===e?0:r[i[t]+i[e]]}},4181:(t,e,r)=>{let i=r(81016),n=r(77258);function o(t){this.mode=i.KANJI,this.data=t}o.getBitsLength=function(t){return 13*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=n.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),t.put(r,13)}},t.exports=o},24740:(t,e)=>{e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){let e=t.size,i=0,n=0,o=0,s=null,a=null;for(let h=0;h<e;h++){n=o=0,s=a=null;for(let l=0;l<e;l++){let e=t.get(h,l);e===s?n++:(n>=5&&(i+=r.N1+(n-5)),s=e,n=1),(e=t.get(l,h))===a?o++:(o>=5&&(i+=r.N1+(o-5)),a=e,o=1)}n>=5&&(i+=r.N1+(n-5)),o>=5&&(i+=r.N1+(o-5))}return i},e.getPenaltyN2=function(t){let e=t.size,i=0;for(let r=0;r<e-1;r++)for(let n=0;n<e-1;n++){let e=t.get(r,n)+t.get(r,n+1)+t.get(r+1,n)+t.get(r+1,n+1);(4===e||0===e)&&i++}return i*r.N2},e.getPenaltyN3=function(t){let e=t.size,i=0,n=0,o=0;for(let r=0;r<e;r++){n=o=0;for(let s=0;s<e;s++)n=n<<1&2047|t.get(r,s),s>=10&&(1488===n||93===n)&&i++,o=o<<1&2047|t.get(s,r),s>=10&&(1488===o||93===o)&&i++}return i*r.N3},e.getPenaltyN4=function(t){let e=0,i=t.data.length;for(let r=0;r<i;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/i/5)-10)*r.N4},e.applyMask=function(t,r){let i=r.size;for(let n=0;n<i;n++)for(let o=0;o<i;o++)r.isReserved(o,n)||r.xor(o,n,function(t,r,i){switch(t){case e.Patterns.PATTERN000:return(r+i)%2==0;case e.Patterns.PATTERN001:return r%2==0;case e.Patterns.PATTERN010:return i%3==0;case e.Patterns.PATTERN011:return(r+i)%3==0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(i/3))%2==0;case e.Patterns.PATTERN101:return r*i%2+r*i%3==0;case e.Patterns.PATTERN110:return(r*i%2+r*i%3)%2==0;case e.Patterns.PATTERN111:return(r*i%3+(r+i)%2)%2==0;default:throw Error("bad maskPattern:"+t)}}(t,o,n))},e.getBestMask=function(t,r){let i=Object.keys(e.Patterns).length,n=0,o=1/0;for(let s=0;s<i;s++){r(s),e.applyMask(s,t);let i=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(s,t),i<o&&(o=i,n=s)}return n}},81016:(t,e,r)=>{let i=r(11210),n=r(78356);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw Error("Invalid mode: "+t);if(!i.isValid(e))throw Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return n.testNumeric(t)?e.NUMERIC:n.testAlphanumeric(t)?e.ALPHANUMERIC:n.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw Error("Unknown mode: "+t)}}(t)}catch(t){return r}}},62301:(t,e,r)=>{let i=r(81016);function n(t){this.mode=i.NUMERIC,this.data=t.toString()}n.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(t){let e,r;for(e=0;e+3<=this.data.length;e+=3)r=parseInt(this.data.substr(e,3),10),t.put(r,10);let i=this.data.length-e;i>0&&(r=parseInt(this.data.substr(e),10),t.put(r,3*i+1))},t.exports=n},4569:(t,e,r)=>{let i=r(16415);e.mul=function(t,e){let r=new Uint8Array(t.length+e.length-1);for(let n=0;n<t.length;n++)for(let o=0;o<e.length;o++)r[n+o]^=i.mul(t[n],e[o]);return r},e.mod=function(t,e){let r=new Uint8Array(t);for(;r.length-e.length>=0;){let t=r[0];for(let n=0;n<e.length;n++)r[n]^=i.mul(e[n],t);let n=0;for(;n<r.length&&0===r[n];)n++;r=r.slice(n)}return r},e.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let n=0;n<t;n++)r=e.mul(r,new Uint8Array([1,i.exp(n)]));return r}},23749:(t,e,r)=>{let i=r(77258),n=r(84529),o=r(79663),s=r(25876),a=r(31117),h=r(26860),l=r(24740),u=r(10162),f=r(55052),c=r(73255),p=r(64477),d=r(81016),g=r(29497);function _(t,e,r){let i,n;let o=t.size,s=p.getEncodedBits(e,r);for(i=0;i<15;i++)n=(s>>i&1)==1,i<6?t.set(i,8,n,!0):i<8?t.set(i+1,8,n,!0):t.set(o-15+i,8,n,!0),i<8?t.set(8,o-i-1,n,!0):i<9?t.set(8,15-i-1+1,n,!0):t.set(8,15-i-1,n,!0);t.set(o-8,8,1,!0)}e.create=function(t,e){let r,p;if(void 0===t||""===t)throw Error("No input text");let m=n.M;return void 0!==e&&(m=n.from(e.errorCorrectionLevel,n.M),r=c.from(e.version),p=l.from(e.maskPattern),e.toSJISFunc&&i.setToSJISFunction(e.toSJISFunc)),function(t,e,r,n){let p;if(Array.isArray(t))p=g.fromArray(t);else if("string"==typeof t){let i=e;if(!i){let e=g.rawSplit(t);i=c.getBestVersionForData(e,r)}p=g.fromString(t,i||40)}else throw Error("Invalid data");let m=c.getBestVersionForData(p,r);if(!m)throw Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<m)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+m+".\n")}else e=m;let y=function(t,e,r){let n=new o;r.forEach(function(e){n.put(e.mode.bit,4),n.put(e.getLength(),d.getCharCountIndicator(e.mode,t)),e.write(n)});let s=(i.getSymbolTotalCodewords(t)-u.getTotalCodewordsCount(t,e))*8;for(n.getLengthInBits()+4<=s&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);let a=(s-n.getLengthInBits())/8;for(let t=0;t<a;t++)n.put(t%2?17:236,8);return function(t,e,r){let n,o;let s=i.getSymbolTotalCodewords(e),a=s-u.getTotalCodewordsCount(e,r),h=u.getBlocksCount(e,r),l=s%h,c=h-l,p=Math.floor(s/h),d=Math.floor(a/h),g=d+1,_=p-d,m=new f(_),y=0,b=Array(h),E=Array(h),w=0,T=new Uint8Array(t.buffer);for(let t=0;t<h;t++){let e=t<c?d:g;b[t]=T.slice(y,y+e),E[t]=m.encode(b[t]),y+=e,w=Math.max(w,e)}let C=new Uint8Array(s),I=0;for(n=0;n<w;n++)for(o=0;o<h;o++)n<b[o].length&&(C[I++]=b[o][n]);for(n=0;n<_;n++)for(o=0;o<h;o++)C[I++]=E[o][n];return C}(n,t,e)}(e,r,p),b=new s(i.getSymbolSize(e));return function(t,e){let r=t.size,i=h.getPositions(e);for(let e=0;e<i.length;e++){let n=i[e][0],o=i[e][1];for(let e=-1;e<=7;e++)if(!(n+e<=-1)&&!(r<=n+e))for(let i=-1;i<=7;i++)o+i<=-1||r<=o+i||(e>=0&&e<=6&&(0===i||6===i)||i>=0&&i<=6&&(0===e||6===e)||e>=2&&e<=4&&i>=2&&i<=4?t.set(n+e,o+i,!0,!0):t.set(n+e,o+i,!1,!0))}}(b,e),function(t){let e=t.size;for(let r=8;r<e-8;r++){let e=r%2==0;t.set(r,6,e,!0),t.set(6,r,e,!0)}}(b),function(t,e){let r=a.getPositions(e);for(let e=0;e<r.length;e++){let i=r[e][0],n=r[e][1];for(let e=-2;e<=2;e++)for(let r=-2;r<=2;r++)-2===e||2===e||-2===r||2===r||0===e&&0===r?t.set(i+e,n+r,!0,!0):t.set(i+e,n+r,!1,!0)}}(b,e),_(b,r,0),e>=7&&function(t,e){let r,i,n;let o=t.size,s=c.getEncodedBits(e);for(let e=0;e<18;e++)r=Math.floor(e/3),i=e%3+o-8-3,n=(s>>e&1)==1,t.set(r,i,n,!0),t.set(i,r,n,!0)}(b,e),function(t,e){let r=t.size,i=-1,n=r-1,o=7,s=0;for(let a=r-1;a>0;a-=2)for(6===a&&a--;;){for(let r=0;r<2;r++)if(!t.isReserved(n,a-r)){let i=!1;s<e.length&&(i=(e[s]>>>o&1)==1),t.set(n,a-r,i),-1==--o&&(s++,o=7)}if((n+=i)<0||r<=n){n-=i,i=-i;break}}}(b,y),isNaN(n)&&(n=l.getBestMask(b,_.bind(null,b,r))),l.applyMask(n,b),_(b,r,n),{modules:b,version:e,errorCorrectionLevel:r,maskPattern:n,segments:p}}(t,r,m,p)}},55052:(t,e,r)=>{let i=r(4569);function n(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}n.prototype.initialize=function(t){this.degree=t,this.genPoly=i.generateECPolynomial(this.degree)},n.prototype.encode=function(t){if(!this.genPoly)throw Error("Encoder not initialized");let e=new Uint8Array(t.length+this.degree);e.set(t);let r=i.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){let t=new Uint8Array(this.degree);return t.set(r,n),t}return r},t.exports=n},78356:(t,e)=>{let r="[0-9]+",i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",n="(?:(?![A-Z0-9 $%*+\\-./:]|"+(i=i.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=RegExp(i,"g"),e.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=RegExp(n,"g"),e.NUMERIC=RegExp(r,"g"),e.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let o=RegExp("^"+i+"$"),s=RegExp("^"+r+"$"),a=RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return o.test(t)},e.testNumeric=function(t){return s.test(t)},e.testAlphanumeric=function(t){return a.test(t)}},29497:(t,e,r)=>{let i=r(81016),n=r(62301),o=r(61097),s=r(67874),a=r(4181),h=r(78356),l=r(77258),u=r(21464);function f(t){return unescape(encodeURIComponent(t)).length}function c(t,e,r){let i;let n=[];for(;null!==(i=t.exec(r));)n.push({data:i[0],index:i.index,mode:e,length:i[0].length});return n}function p(t){let e,r;let n=c(h.NUMERIC,i.NUMERIC,t),o=c(h.ALPHANUMERIC,i.ALPHANUMERIC,t);return l.isKanjiModeEnabled()?(e=c(h.BYTE,i.BYTE,t),r=c(h.KANJI,i.KANJI,t)):(e=c(h.BYTE_KANJI,i.BYTE,t),r=[]),n.concat(o,e,r).sort(function(t,e){return t.index-e.index}).map(function(t){return{data:t.data,mode:t.mode,length:t.length}})}function d(t,e){switch(e){case i.NUMERIC:return n.getBitsLength(t);case i.ALPHANUMERIC:return o.getBitsLength(t);case i.KANJI:return a.getBitsLength(t);case i.BYTE:return s.getBitsLength(t)}}function g(t,e){let r;let h=i.getBestModeForData(t);if((r=i.from(e,h))!==i.BYTE&&r.bit<h.bit)throw Error('"'+t+'" cannot be encoded with mode '+i.toString(r)+".\n Suggested mode is: "+i.toString(h));switch(r!==i.KANJI||l.isKanjiModeEnabled()||(r=i.BYTE),r){case i.NUMERIC:return new n(t);case i.ALPHANUMERIC:return new o(t);case i.KANJI:return new a(t);case i.BYTE:return new s(t)}}e.fromArray=function(t){return t.reduce(function(t,e){return"string"==typeof e?t.push(g(e,null)):e.data&&t.push(g(e.data,e.mode)),t},[])},e.fromString=function(t,r){let n=function(t,e){let r={},n={start:{}},o=["start"];for(let s=0;s<t.length;s++){let a=t[s],h=[];for(let t=0;t<a.length;t++){let l=a[t],u=""+s+t;h.push(u),r[u]={node:l,lastCount:0},n[u]={};for(let t=0;t<o.length;t++){let s=o[t];r[s]&&r[s].node.mode===l.mode?(n[s][u]=d(r[s].lastCount+l.length,l.mode)-d(r[s].lastCount,l.mode),r[s].lastCount+=l.length):(r[s]&&(r[s].lastCount=l.length),n[s][u]=d(l.length,l.mode)+4+i.getCharCountIndicator(l.mode,e))}}o=h}for(let t=0;t<o.length;t++)n[o[t]].end=0;return{map:n,table:r}}(function(t){let e=[];for(let r=0;r<t.length;r++){let n=t[r];switch(n.mode){case i.NUMERIC:e.push([n,{data:n.data,mode:i.ALPHANUMERIC,length:n.length},{data:n.data,mode:i.BYTE,length:n.length}]);break;case i.ALPHANUMERIC:e.push([n,{data:n.data,mode:i.BYTE,length:n.length}]);break;case i.KANJI:e.push([n,{data:n.data,mode:i.BYTE,length:f(n.data)}]);break;case i.BYTE:e.push([{data:n.data,mode:i.BYTE,length:f(n.data)}])}}return e}(p(t,l.isKanjiModeEnabled())),r),o=u.find_path(n.map,"start","end"),s=[];for(let t=1;t<o.length-1;t++)s.push(n.table[o[t]].node);return e.fromArray(s.reduce(function(t,e){let r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?t[t.length-1].data+=e.data:t.push(e),t},[]))},e.rawSplit=function(t){return e.fromArray(p(t,l.isKanjiModeEnabled()))}},77258:(t,e)=>{let r;let i=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw Error('"version" cannot be null or undefined');if(t<1||t>40)throw Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return i[t]},e.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw Error('"toSJISFunc" is not a valid function.');r=t},e.isKanjiModeEnabled=function(){return void 0!==r},e.toSJIS=function(t){return r(t)}},11210:(t,e)=>{e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},73255:(t,e,r)=>{let i=r(77258),n=r(10162),o=r(84529),s=r(81016),a=r(11210),h=i.getBCHDigit(7973);function l(t,e){return s.getCharCountIndicator(t,e)+4}e.from=function(t,e){return a.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,r){if(!a.isValid(t))throw Error("Invalid QR Code version");void 0===r&&(r=s.BYTE);let o=(i.getSymbolTotalCodewords(t)-n.getTotalCodewordsCount(t,e))*8;if(r===s.MIXED)return o;let h=o-l(r,t);switch(r){case s.NUMERIC:return Math.floor(h/10*3);case s.ALPHANUMERIC:return Math.floor(h/11*2);case s.KANJI:return Math.floor(h/13);case s.BYTE:default:return Math.floor(h/8)}},e.getBestVersionForData=function(t,r){let i;let n=o.from(r,o.M);if(Array.isArray(t)){if(t.length>1)return function(t,r){for(let i=1;i<=40;i++)if(function(t,e){let r=0;return t.forEach(function(t){let i=l(t.mode,e);r+=i+t.getBitsLength()}),r}(t,i)<=e.getCapacity(i,r,s.MIXED))return i}(t,n);if(0===t.length)return 1;i=t[0]}else i=t;return function(t,r,i){for(let n=1;n<=40;n++)if(r<=e.getCapacity(n,i,t))return n}(i.mode,i.getLength(),n)},e.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw Error("Invalid QR Code version");let e=t<<12;for(;i.getBCHDigit(e)-h>=0;)e^=7973<<i.getBCHDigit(e)-h;return t<<12|e}},12959:(t,e,r)=>{"use strict";t.exports=r(74136)},36735:(t,e,r)=>{let i=r(9706);e.render=function(t,e,r){var n;let o=r,s=e;void 0!==o||e&&e.getContext||(o=e,e=void 0),e||(s=function(){try{return document.createElement("canvas")}catch(t){throw Error("You need to specify a canvas element")}}()),o=i.getOptions(o);let a=i.getImageWidth(t.modules.size,o),h=s.getContext("2d"),l=h.createImageData(a,a);return i.qrToImageData(l.data,t,o),n=s,h.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=a,n.width=a,n.style.height=a+"px",n.style.width=a+"px",h.putImageData(l,0,0),s},e.renderToDataURL=function(t,r,i){let n=i;void 0!==n||r&&r.getContext||(n=r,r=void 0),n||(n={});let o=e.render(t,r,n),s=n.type||"image/png",a=n.rendererOpts||{};return o.toDataURL(s,a.quality)}},56036:(t,e,r)=>{let i=r(29021),n=r(41960).O,o=r(9706);e.render=function(t,e){let r=o.getOptions(e),i=r.rendererOpts,s=o.getImageWidth(t.modules.size,r);i.width=s,i.height=s;let a=new n(i);return o.qrToImageData(a.data,t,r),a},e.renderToDataURL=function(t,r,i){void 0===i&&(i=r,r=void 0),e.renderToBuffer(t,r,function(t,e){t&&i(t);let r="data:image/png;base64,";r+=e.toString("base64"),i(null,r)})},e.renderToBuffer=function(t,r,i){void 0===i&&(i=r,r=void 0);let n=e.render(t,r),o=[];n.on("error",i),n.on("data",function(t){o.push(t)}),n.on("end",function(){i(null,Buffer.concat(o))}),n.pack()},e.renderToFile=function(t,r,n,o){void 0===o&&(o=n,n=void 0);let s=!1,a=(...t)=>{s||(s=!0,o.apply(null,t))},h=i.createWriteStream(t);h.on("error",a),h.on("close",a),e.renderToFileStream(h,r,n)},e.renderToFileStream=function(t,r,i){e.render(r,i).pack().pipe(t)}},388:(t,e,r)=>{let i=r(9706);function n(t,e){let r=t.a/255,i=e+'="'+t.hex+'"';return r<1?i+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':i}function o(t,e,r){let i=t+e;return void 0!==r&&(i+=" "+r),i}e.render=function(t,e,r){let s=i.getOptions(e),a=t.modules.size,h=t.modules.data,l=a+2*s.margin,u=s.color.light.a?"<path "+n(s.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",f="<path "+n(s.color.dark,"stroke")+' d="'+function(t,e,r){let i="",n=0,s=!1,a=0;for(let h=0;h<t.length;h++){let l=Math.floor(h%e),u=Math.floor(h/e);l||s||(s=!0),t[h]?(a++,h>0&&l>0&&t[h-1]||(i+=s?o("M",l+r,.5+u+r):o("m",n,0),n=0,s=!1),l+1<e&&t[h+1]||(i+=o("h",a),a=0)):n++}return i}(h,a,s.margin)+'"/>',c='<svg xmlns="http://www.w3.org/2000/svg" '+(s.width?'width="'+s.width+'" height="'+s.width+'" ':"")+('viewBox="0 0 '+l)+" "+l+'" shape-rendering="crispEdges">'+u+f+"</svg>\n";return"function"==typeof r&&r(null,c),c}},86297:(t,e,r)=>{let i=r(388);e.render=i.render,e.renderToFile=function(t,i,n,o){void 0===o&&(o=n,n=void 0);let s=r(29021),a=e.render(i,n);s.writeFile(t,'<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+a,o)}},77795:(t,e,r)=>{let i=r(80654),n=r(33104);e.render=function(t,e,r){return e&&e.small?n.render(t,e,r):i.render(t,e,r)}},33104:(t,e)=>{let r="\x1b[37m",i="\x1b[30m",n="\x1b[0m",o="\x1b[47m"+i,s="\x1b[40m"+r,a=function(t,e,r,i){let n=e+1;return r>=n||i>=n||i<-1||r<-1?"0":r>=e||i>=e||i<0||r<0?"1":t[i*e+r]?"2":"1"},h=function(t,e,r,i){return a(t,e,r,i)+a(t,e,r,i+1)};e.render=function(t,e,a){var l,u;let f=t.modules.size,c=t.modules.data,p=!!(e&&e.inverse),d=e&&e.inverse?s:o,g={"00":n+" "+d,"01":n+(l=p?i:r)+"▄"+d,"02":n+(u=p?r:i)+"▄"+d,10:n+l+"▀"+d,11:" ",12:"▄",20:n+u+"▀"+d,21:"▀",22:"█"},_=n+"\n"+d,m=d;for(let t=-1;t<f+1;t+=2){for(let e=-1;e<f;e++)m+=g[h(c,f,e,t)];m+=g[h(c,f,f,t)]+_}return m+=n,"function"==typeof a&&a(null,m),m}},80654:(t,e)=>{e.render=function(t,e,r){let i=t.modules.size,n=t.modules.data,o="\x1b[47m  \x1b[0m",s="",a=Array(i+3).join(o),h=[,,].join(o);s+=a+"\n";for(let t=0;t<i;++t){s+=o;for(let e=0;e<i;e++)s+=n[t*i+e]?"\x1b[40m  \x1b[0m":o;s+=h+"\n"}return s+=a+"\n","function"==typeof r&&r(null,s),s}},30068:(t,e,r)=>{let i=r(9706),n={WW:" ",WB:"▄",BB:"█",BW:"▀"},o={BB:" ",BW:"▄",WW:"█",WB:"▀"};e.render=function(t,e,r){let s=i.getOptions(e),a=n;("#ffffff"===s.color.dark.hex||"#000000"===s.color.light.hex)&&(a=o);let h=t.modules.size,l=t.modules.data,u="",f=Array(h+2*s.margin+1).join(a.WW);f=Array(s.margin/2+1).join(f+"\n");let c=Array(s.margin+1).join(a.WW);u+=f;for(let t=0;t<h;t+=2){u+=c;for(let e=0;e<h;e++){var p;let r=l[t*h+e],i=l[(t+1)*h+e];u+=(p=a,r&&i?p.BB:r&&!i?p.BW:!r&&i?p.WB:p.WW)}u+=c+"\n"}return u+=f.slice(0,-1),"function"==typeof r&&r(null,u),u},e.renderToFile=function(t,i,n,o){void 0===o&&(o=n,n=void 0);let s=r(29021),a=e.render(i,n);s.writeFile(t,a,o)}},9706:(t,e)=>{function r(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw Error("Invalid hex color: "+t);(3===e.length||4===e.length)&&(e=Array.prototype.concat.apply([],e.map(function(t){return[t,t]}))),6===e.length&&e.push("F","F");let r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});let e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,i=t.width&&t.width>=21?t.width:void 0,n=t.scale||4;return{width:i,scale:i?4:n,margin:e,color:{dark:r(t.color.dark||"#000000ff"),light:r(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,r){let i=e.getScale(t,r);return Math.floor((t+2*r.margin)*i)},e.qrToImageData=function(t,r,i){let n=r.modules.size,o=r.modules.data,s=e.getScale(n,i),a=Math.floor((n+2*i.margin)*s),h=i.margin*s,l=[i.color.light,i.color.dark];for(let e=0;e<a;e++)for(let r=0;r<a;r++){let u=(e*a+r)*4,f=i.color.light;e>=h&&r>=h&&e<a-h&&r<a-h&&(f=l[o[Math.floor((e-h)/s)*n+Math.floor((r-h)/s)]?1:0]),t[u++]=f.r,t[u++]=f.g,t[u++]=f.b,t[u]=f.a}}},74136:(t,e,r)=>{let i=r(2541),n=r(23749),o=r(56036),s=r(30068),a=r(77795),h=r(86297);function l(t,e,r){if(void 0===t)throw Error("String required as first argument");if(void 0===r&&(r=e,e={}),"function"!=typeof r){if(i())e=r||{},r=null;else throw Error("Callback required as last argument")}return{opts:e,cb:r}}function u(t){switch(t){case"svg":return h;case"txt":case"utf8":return s;default:return o}}function f(t,e,r){if(!r.cb)return new Promise(function(i,o){try{let s=n.create(e,r.opts);return t(s,r.opts,function(t,e){return t?o(t):i(e)})}catch(t){o(t)}});try{let i=n.create(e,r.opts);return t(i,r.opts,r.cb)}catch(t){r.cb(t)}}e.create=n.create,e.toCanvas=r(18139).toCanvas,e.toString=function(t,e,r){let i=l(t,e,r);return f(function(t){switch(t){case"svg":return h;case"terminal":return a;default:return s}}(i.opts?i.opts.type:void 0).render,t,i)},e.toDataURL=function(t,e,r){let i=l(t,e,r);return f(u(i.opts.type).renderToDataURL,t,i)},e.toBuffer=function(t,e,r){let i=l(t,e,r);return f(u(i.opts.type).renderToBuffer,t,i)},e.toFile=function(t,e,r,n){if("string"!=typeof t||!("string"==typeof e||"object"==typeof e))throw Error("Invalid argument");if(arguments.length<3&&!i())throw Error("Too few arguments provided");let o=l(e,r,n);return f(u(o.opts.type||t.slice((t.lastIndexOf(".")-1>>>0)+2).toLowerCase()).renderToFile.bind(null,t),e,o)},e.toFileStream=function(t,e,r){if(arguments.length<2)throw Error("Too few arguments provided");let i=l(e,r,t.emit.bind(t,"error"));f(u("png").renderToFileStream.bind(null,t),e,i)}},46063:(t,e,r)=>{"use strict";var i=r(80778),n=r(55511),o=r(79551),s=r(28354);e.digest=function(t){var e,r=t.secret,o=t.counter,s=t.encoding||"ascii",a=(t.algorithm||"sha1").toLowerCase();null!=t.key&&(console.warn("Speakeasy - Deprecation Notice - Specifying the secret using `key` is no longer supported. Use `secret` instead."),r=t.key),Buffer.isBuffer(r)||(r="base32"===s?i.decode(r):new Buffer(r,s));var h=new Buffer(8),l=o;for(e=0;e<8;e++)h[7-e]=255&l,l>>=8;var u=n.createHmac(a,r);return u.update(h),u.digest()},e.hotp=function(t){var r=(null!=t.digits?t.digits:t.length)||6;null!=t.length&&console.warn("Speakeasy - Deprecation Notice - Specifying token digits using `length` is no longer supported. Use `digits` instead.");var i=t.digest||e.digest(t),n=15&i[i.length-1],o=(127&i[n])<<24|(255&i[n+1])<<16|(255&i[n+2])<<8|255&i[n+3];return(o=Array(r+1).join("0")+o.toString(10)).substr(-r)},e.counter=e.hotp,e.hotp.verifyDelta=function(t){var r,i=String((t=Object.create(t)).token),n=parseInt(t.digits,10)||6,o=parseInt(t.window,10)||0,s=parseInt(t.counter,10)||0;if(!(i.length!==n||isNaN(i=parseInt(i,10)))){for(r=s;r<=s+o;++r)if(t.counter=r,parseInt(e.hotp(t),10)===i)return{delta:r-s}}},e.hotp.verify=function(t){return null!=e.hotp.verifyDelta(t)},e._counter=function(t){var e=t.step||30,r=null!=t.time?1e3*t.time:Date.now(),i=(null!=t.epoch?1e3*t.epoch:1e3*t.initial_time)||0;return null!=t.initial_time&&console.warn("Speakeasy - Deprecation Notice - Specifying the epoch using `initial_time` is no longer supported. Use `epoch` instead."),Math.floor((r-i)/e/1e3)},e.totp=function(t){return null==(t=Object.create(t)).counter&&(t.counter=e._counter(t)),this.hotp(t)},e.time=e.totp,e.totp.verifyDelta=function(t){var r=parseInt((t=Object.create(t)).window,10)||0;null==t.counter&&(t.counter=e._counter(t)),t.counter-=r,t.window+=r;var i=e.hotp.verifyDelta(t);return i&&(i.delta-=r),i},e.totp.verify=function(t){return null!=e.totp.verifyDelta(t)},e.generateSecret=function(t){t||(t={});var r=t.length||32,n=encodeURIComponent(t.name||"SecretKey"),o=t.qr_codes||!1,s=t.google_auth_qr||!1,a=null==t.otpauth_url||t.otpauth_url,h=!0;void 0!==t.symbols&&!1===t.symbols&&(h=!1);var l=this.generateSecretASCII(r,h),u={};return u.ascii=l,u.hex=Buffer(l,"ascii").toString("hex"),u.base32=i.encode(Buffer(l)).toString().replace(/=/g,""),o&&(console.warn("Speakeasy - Deprecation Notice - generateSecret() QR codes are deprecated and no longer supported. Please use your own QR code implementation."),u.qr_code_ascii="https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl="+encodeURIComponent(u.ascii),u.qr_code_hex="https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl="+encodeURIComponent(u.hex),u.qr_code_base32="https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl="+encodeURIComponent(u.base32)),a&&(u.otpauth_url=e.otpauthURL({secret:u.ascii,label:n})),s&&(console.warn("Speakeasy - Deprecation Notice - generateSecret() Google Auth QR code is deprecated and no longer supported. Please use your own QR code implementation."),u.google_auth_qr="https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl="+encodeURIComponent(e.otpauthURL({secret:u.base32,label:n}))),u},e.generate_key=s.deprecate(function(t){return e.generateSecret(t)},"Speakeasy - Deprecation Notice - `generate_key()` is depreciated, please use `generateSecret()` instead."),e.generateSecretASCII=function(t,e){var r=n.randomBytes(t||32),i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";e&&(i+="!@#$%^&*()<>?/[]{},.:;");for(var o="",s=0,a=r.length;s<a;s++)o+=i[Math.floor(r[s]/255*(i.length-1))];return o},e.generate_key_ascii=s.deprecate(function(t,r){return e.generateSecretASCII(t,r)},"Speakeasy - Deprecation Notice - `generate_key_ascii()` is depreciated, please use `generateSecretASCII()` instead."),e.otpauthURL=function(t){var e=t.secret,r=t.label,n=t.issuer,s=(t.type||"totp").toLowerCase(),a=t.counter,h=t.algorithm,l=t.digits,u=t.period,f=t.encoding||"ascii";switch(s){case"totp":case"hotp":break;default:throw Error("Speakeasy - otpauthURL - Invalid type `"+s+"`; must be `hotp` or `totp`")}if(!e)throw Error("Speakeasy - otpauthURL - Missing secret");if(!r)throw Error("Speakeasy - otpauthURL - Missing label");if("hotp"===s&&null==a)throw Error("Speakeasy - otpauthURL - Missing counter value for HOTP");"base32"!==f&&(e=new Buffer(e,f)),Buffer.isBuffer(e)&&(e=i.encode(e));var c={secret:e};if(n&&(c.issuer=n),null!=h){switch(h.toUpperCase()){case"SHA1":case"SHA256":case"SHA512":break;default:console.warn("Speakeasy - otpauthURL - Warning - Algorithm generally should be SHA1, SHA256, or SHA512")}c.algorithm=h.toUpperCase()}if(null!=l){if(isNaN(l))throw Error("Speakeasy - otpauthURL - Invalid digits `"+l+"`");switch(parseInt(l,10)){case 6:case 8:break;default:console.warn("Speakeasy - otpauthURL - Warning - Digits generally should be either 6 or 8")}c.digits=l}if(null!=u){if(~~(u=parseInt(u,10))!==u)throw Error("Speakeasy - otpauthURL - Invalid period `"+u+"`");c.period=u}return o.format({protocol:"otpauth",slashes:!0,hostname:s,pathname:r,query:c})}}};