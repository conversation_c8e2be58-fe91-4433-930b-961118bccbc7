/**
 * Multi-Channel Notification Service
 * Handles in-app, email, and SMS notifications
 */

import { createClient } from '@/lib/supabase/client';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

export type NotificationChannel = 'in-app' | 'email' | 'sms' | 'all';
export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical';

export interface NotificationData {
  id?: string;
  userId: string;
  title: string;
  titleJp?: string;
  message: string;
  messageJp?: string;
  type: NotificationType;
  priority: NotificationPriority;
  channels: NotificationChannel[];
  metadata?: Record<string, any>;
  actionUrl?: string;
  scheduledAt?: Date;
  expiresAt?: Date;
}

export interface NotificationPreferences {
  userId: string;
  email: boolean;
  sms: boolean;
  inApp: boolean;
  emailAddress?: string;
  phoneNumber?: string;
  language: 'ja' | 'en';
  quietHours?: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
}

export interface NotificationTemplate {
  id: string;
  name: string;
  channels: NotificationChannel[];
  titleTemplate: string;
  titleTemplateJp?: string;
  messageTemplate: string;
  messageTemplateJp?: string;
  metadata?: Record<string, any>;
}

class NotificationService {
  private static instance: NotificationService;
  private supabase = createClient();
  private emailQueue: NotificationData[] = [];
  private smsQueue: NotificationData[] = [];
  private processingEmail = false;
  private processingSms = false;

  private constructor() {
    // Initialize notification processing
    this.startNotificationProcessing();
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Send notification through specified channels
   */
  async sendNotification(notification: NotificationData): Promise<void> {
    try {
      // Get user preferences
      const preferences = await this.getUserPreferences(notification.userId);
      
      // Check quiet hours
      if (this.isInQuietHours(preferences)) {
        // Schedule for later
        await this.scheduleNotification(notification);
        return;
      }

      // Store notification record
      const { data: notificationRecord, error } = await this.supabase
        .from('notifications')
        .insert({
          user_id: notification.userId,
          title: notification.title,
          title_jp: notification.titleJp,
          message: notification.message,
          message_jp: notification.messageJp,
          type: notification.type,
          priority: notification.priority,
          channels: notification.channels,
          metadata: notification.metadata,
          action_url: notification.actionUrl,
          scheduled_at: notification.scheduledAt,
          expires_at: notification.expiresAt,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      notification.id = notificationRecord.id;

      // Send to requested channels based on user preferences
      const channels = notification.channels.includes('all') 
        ? ['in-app', 'email', 'sms'] 
        : notification.channels;

      for (const channel of channels) {
        await this.sendToChannel(channel as NotificationChannel, notification, preferences);
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  /**
   * Send notification to specific channel
   */
  private async sendToChannel(
    channel: NotificationChannel, 
    notification: NotificationData,
    preferences: NotificationPreferences
  ): Promise<void> {
    switch (channel) {
      case 'in-app':
        if (preferences.inApp) {
          await this.sendInAppNotification(notification);
        }
        break;
      case 'email':
        if (preferences.email && preferences.emailAddress) {
          await this.queueEmailNotification(notification, preferences);
        }
        break;
      case 'sms':
        if (preferences.sms && preferences.phoneNumber) {
          await this.queueSmsNotification(notification, preferences);
        }
        break;
    }
  }

  /**
   * Send in-app notification (real-time)
   */
  private async sendInAppNotification(notification: NotificationData): Promise<void> {
    const channel = this.supabase.channel(`notifications:${notification.userId}`);
    
    await channel.send({
      type: 'broadcast',
      event: 'new-notification',
      payload: notification
    });
  }

  /**
   * Queue email notification
   */
  private async queueEmailNotification(
    notification: NotificationData,
    preferences: NotificationPreferences
  ): Promise<void> {
    const emailData = {
      ...notification,
      emailAddress: preferences.emailAddress!,
      language: preferences.language
    };

    // Store in email queue table
    await this.supabase
      .from('notification_email_queue')
      .insert({
        notification_id: notification.id,
        to_email: preferences.emailAddress,
        subject: preferences.language === 'ja' && notification.titleJp 
          ? notification.titleJp 
          : notification.title,
        body: preferences.language === 'ja' && notification.messageJp 
          ? notification.messageJp 
          : notification.message,
        priority: notification.priority,
        metadata: notification.metadata,
        status: 'pending'
      });

    this.emailQueue.push(emailData);
  }

  /**
   * Queue SMS notification
   */
  private async queueSmsNotification(
    notification: NotificationData,
    preferences: NotificationPreferences
  ): Promise<void> {
    const smsData = {
      ...notification,
      phoneNumber: preferences.phoneNumber!,
      language: preferences.language
    };

    // Store in SMS queue table
    await this.supabase
      .from('notification_sms_queue')
      .insert({
        notification_id: notification.id,
        to_phone: preferences.phoneNumber,
        message: this.formatSmsMessage(notification, preferences.language),
        priority: notification.priority,
        metadata: notification.metadata,
        status: 'pending'
      });

    this.smsQueue.push(smsData);
  }

  /**
   * Format SMS message (limited to 160 characters)
   */
  private formatSmsMessage(notification: NotificationData, language: 'ja' | 'en'): string {
    const title = language === 'ja' && notification.titleJp 
      ? notification.titleJp 
      : notification.title;
    const message = language === 'ja' && notification.messageJp 
      ? notification.messageJp 
      : notification.message;

    const fullMessage = `${title}: ${message}`;
    return fullMessage.substring(0, 160);
  }

  /**
   * Get user notification preferences
   */
  private async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    const { data, error } = await this.supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      // Return default preferences
      return {
        userId,
        email: true,
        sms: false,
        inApp: true,
        language: 'ja'
      };
    }

    return data;
  }

  /**
   * Check if current time is in user's quiet hours
   */
  private isInQuietHours(preferences: NotificationPreferences): boolean {
    if (!preferences.quietHours?.enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = preferences.quietHours.start.split(':').map(Number);
    const [endHour, endMin] = preferences.quietHours.end.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Quiet hours span midnight
      return currentTime >= startTime || currentTime <= endTime;
    }
  }

  /**
   * Schedule notification for later
   */
  private async scheduleNotification(notification: NotificationData): Promise<void> {
    await this.supabase
      .from('scheduled_notifications')
      .insert({
        ...notification,
        scheduled_for: this.getNextAvailableTime(notification.userId)
      });
  }

  /**
   * Get next available time outside quiet hours
   */
  private async getNextAvailableTime(userId: string): Promise<Date> {
    const preferences = await this.getUserPreferences(userId);
    
    if (!preferences.quietHours?.enabled) {
      return new Date();
    }

    const now = new Date();
    const [endHour, endMin] = preferences.quietHours.end.split(':').map(Number);
    
    const nextAvailable = new Date(now);
    nextAvailable.setHours(endHour, endMin, 0, 0);
    
    if (nextAvailable <= now) {
      nextAvailable.setDate(nextAvailable.getDate() + 1);
    }
    
    return nextAvailable;
  }

  /**
   * Start notification processing loops
   */
  private startNotificationProcessing(): void {
    // Process email queue every 30 seconds
    setInterval(() => this.processEmailQueue(), 30000);
    
    // Process SMS queue every 30 seconds
    setInterval(() => this.processSmsQueue(), 30000);
    
    // Process scheduled notifications every minute
    setInterval(() => this.processScheduledNotifications(), 60000);
  }

  /**
   * Process email queue
   */
  private async processEmailQueue(): Promise<void> {
    if (this.processingEmail || this.emailQueue.length === 0) return;
    
    this.processingEmail = true;
    
    try {
      // Get pending emails from database
      const { data: pendingEmails } = await this.supabase
        .from('notification_email_queue')
        .select('*')
        .eq('status', 'pending')
        .order('priority', { ascending: false })
        .limit(10);

      if (pendingEmails && pendingEmails.length > 0) {
        // Process emails via edge function
        const { error } = await this.supabase.functions.invoke('send-email-notifications', {
          body: { emails: pendingEmails }
        });

        if (!error) {
          // Update status to sent
          const emailIds = pendingEmails.map(e => e.id);
          await this.supabase
            .from('notification_email_queue')
            .update({ status: 'sent', sent_at: new Date().toISOString() })
            .in('id', emailIds);
        }
      }
    } catch (error) {
      console.error('Error processing email queue:', error);
    } finally {
      this.processingEmail = false;
    }
  }

  /**
   * Process SMS queue
   */
  private async processSmsQueue(): Promise<void> {
    if (this.processingSms || this.smsQueue.length === 0) return;
    
    this.processingSms = true;
    
    try {
      // Get pending SMS from database
      const { data: pendingSms } = await this.supabase
        .from('notification_sms_queue')
        .select('*')
        .eq('status', 'pending')
        .order('priority', { ascending: false })
        .limit(10);

      if (pendingSms && pendingSms.length > 0) {
        // Process SMS via edge function
        const { error } = await this.supabase.functions.invoke('send-sms-notifications', {
          body: { messages: pendingSms }
        });

        if (!error) {
          // Update status to sent
          const smsIds = pendingSms.map(s => s.id);
          await this.supabase
            .from('notification_sms_queue')
            .update({ status: 'sent', sent_at: new Date().toISOString() })
            .in('id', smsIds);
        }
      }
    } catch (error) {
      console.error('Error processing SMS queue:', error);
    } finally {
      this.processingSms = false;
    }
  }

  /**
   * Process scheduled notifications
   */
  private async processScheduledNotifications(): Promise<void> {
    try {
      const { data: scheduled } = await this.supabase
        .from('scheduled_notifications')
        .select('*')
        .lte('scheduled_for', new Date().toISOString())
        .eq('status', 'pending');

      if (scheduled && scheduled.length > 0) {
        for (const notification of scheduled) {
          await this.sendNotification(notification);
          
          // Mark as processed
          await this.supabase
            .from('scheduled_notifications')
            .update({ status: 'sent' })
            .eq('id', notification.id);
        }
      }
    } catch (error) {
      console.error('Error processing scheduled notifications:', error);
    }
  }

  /**
   * Send notification from template
   */
  async sendFromTemplate(
    templateId: string,
    userId: string,
    variables: Record<string, any>
  ): Promise<void> {
    const { data: template } = await this.supabase
      .from('notification_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (!template) {
      throw new Error('Template not found');
    }

    const notification: NotificationData = {
      userId,
      title: this.interpolateTemplate(template.title_template, variables),
      titleJp: template.title_template_jp 
        ? this.interpolateTemplate(template.title_template_jp, variables)
        : undefined,
      message: this.interpolateTemplate(template.message_template, variables),
      messageJp: template.message_template_jp
        ? this.interpolateTemplate(template.message_template_jp, variables)
        : undefined,
      type: template.type || 'info',
      priority: template.priority || 'medium',
      channels: template.channels,
      metadata: { ...template.metadata, ...variables }
    };

    await this.sendNotification(notification);
  }

  /**
   * Interpolate variables in template
   */
  private interpolateTemplate(template: string, variables: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match;
    });
  }

  /**
   * Update user notification preferences
   */
  async updatePreferences(preferences: NotificationPreferences): Promise<void> {
    await this.supabase
      .from('notification_preferences')
      .upsert(preferences);
  }

  /**
   * Get notification history for user
   */
  async getNotificationHistory(userId: string, limit = 50): Promise<any[]> {
    const { data } = await this.supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    return data || [];
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    await this.supabase
      .from('notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('id', notificationId);
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    await this.supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);
  }
}

export const notificationService = NotificationService.getInstance();
