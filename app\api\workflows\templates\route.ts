import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { WorkflowTemplateManager } from '@/lib/services/workflow/templates/workflow-template-manager';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';
import { AuditService } from '@/lib/services/audit';

export async function GET(request: Request) {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const filters = {
      category: searchParams.get('category') || undefined,
      serviceCategoryId: searchParams.get('serviceCategoryId') || undefined,
      priority: searchParams.get('priority') || undefined,
    };

    const auditService = auditService;
    const workflowEngine = new WorkflowEngine(supabase);
    const templateManager = new WorkflowTemplateManager(supabase, auditService, workflowEngine);
    
    const templates = await templateManager.getTemplates(filters);

    return NextResponse.json({ templates });
  } catch (error) {
    console.error('Error fetching workflow templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow templates' },
      { status: 500 }
    );
  }
}
