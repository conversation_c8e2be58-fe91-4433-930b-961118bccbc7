'use client'

import { AIAgentDashboard } from '@/components/knowledge-base/ai-agent-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Brain, BookOpen, Globe, Zap } from 'lucide-react'

export default function TestAIAgentsPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold flex items-center justify-center gap-3">
          <Brain className="h-10 w-10 text-primary" />
          AI Agent Knowledge System
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Specialized AI agents that continuously learn from the web and create 
          interactive educational content for non-technical staff
        </p>
      </div>

      <Alert className="bg-primary/5 border-primary">
        <Zap className="h-4 w-4" />
        <AlertTitle>Revolutionary Learning System</AlertTitle>
        <AlertDescription>
          Each IT service category has its own dedicated AI agent that:
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Continuously scrapes the web for the latest information</li>
            <li>Transforms complex technical content into simple, interactive guides</li>
            <li>Creates step-by-step wizards in both English and Japanese</li>
            <li>Learns from user feedback to improve content quality</li>
            <li>Automatically updates content when new information is available</li>
          </ul>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">📧</span>
              Group Mail Agent
            </CardTitle>
            <CardDescription>
              Specializes in email distribution lists
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Creates easy-to-follow guides for managing group email addresses,
              adding/removing members, and understanding email permissions.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">📁</span>
              SharePoint Agent
            </CardTitle>
            <CardDescription>
              Expert in document management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Generates visual tutorials for SharePoint navigation, file uploads,
              permissions, and collaboration features using office analogies.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">🔐</span>
              Password Agent
            </CardTitle>
            <CardDescription>
              Security and account specialist
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Provides reassuring, step-by-step password reset guides and
              multi-factor authentication setup with security best practices.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">💻</span>
              PC Admin Agent
            </CardTitle>
            <CardDescription>
              Windows administration expert
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Creates safe, visual guides for software installation, admin rights
              requests, and troubleshooting common PC issues.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">📮</span>
              Mailbox Agent
            </CardTitle>
            <CardDescription>
              Email account configuration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Develops guides for email signatures, out-of-office settings,
              and mailbox organization following Japanese business etiquette.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">🧠</span>
              Master Agent
            </CardTitle>
            <CardDescription>
              Coordinates all knowledge
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Provides comprehensive overviews and helps users find the right
              service for their needs with intelligent routing.
            </p>
          </CardContent>
        </Card>
      </div>

      <Card className="border-2 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            How the AI Agent System Works
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">1. Continuous Web Learning</h3>
            <p className="text-sm text-muted-foreground">
              Agents continuously monitor the web for updates about their specialized topics,
              ensuring content stays current with the latest best practices and solutions.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">2. Content Transformation</h3>
            <p className="text-sm text-muted-foreground">
              Complex technical documentation is transformed into simple, relatable content
              using everyday analogies that non-technical staff can easily understand.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">3. Interactive Experience Creation</h3>
            <p className="text-sm text-muted-foreground">
              Static content becomes interactive wizards with visual aids, step-by-step
              validation, and built-in troubleshooting tips for common issues.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">4. Bilingual Support</h3>
            <p className="text-sm text-muted-foreground">
              All content is automatically available in both English and Japanese, with
              cultural considerations for Japanese business environments.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">5. Adaptive Learning</h3>
            <p className="text-sm text-muted-foreground">
              Agents learn from user feedback and search patterns to continuously improve
              content relevance and identify new topics that need coverage.
            </p>
          </div>
        </CardContent>
      </Card>

      <AIAgentDashboard />
    </div>
  )
}
