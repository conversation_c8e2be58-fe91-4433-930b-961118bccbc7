(()=>{var e={};e.id=9461,e.ids=[9461],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},38511:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(42706),o=r(28203),i=r(45994),n=r(39187),u=r(61487),c=r(68967);async function p(e){try{let t=(0,u.U)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{taskId:a,toUserId:o,reason:i}=await e.json();if(!a||!o||!i)return n.NextResponse.json({error:"Task ID, target user, and reason are required"},{status:400});let{data:p,error:l}=await t.rpc("handle_approval_delegation",{p_task_id:a,p_from_user_id:r.id,p_to_user_id:o,p_reason:i});if(l)throw l;return await c.H.logWorkflowAction(r.id,a,"approval_delegated",{delegated_to:o,reason:i}),n.NextResponse.json({success:!0,message:"Approval delegated successfully"})}catch(e){return console.error("Error delegating approval:",e),n.NextResponse.json({error:"Failed to delegate approval"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/approvals/delegate/route",pathname:"/api/workflows/approvals/delegate",filename:"route",bundlePath:"app/api/workflows/approvals/delegate/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\delegate\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:_}=l;function w(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},68967:(e,t,r)=>{"use strict";r.d(t,{H:()=>a});class s{async logAction(e){try{console.log("Audit log entry:",{...e,timestamp:e.timestamp||new Date}),await new Promise(e=>setTimeout(e,100))}catch(e){console.error("Failed to log audit entry:",e)}}async getAuditLogs(e={}){try{return await new Promise(e=>setTimeout(e,200)),[{id:"1",user_id:"user1",action:"login",resource_type:"auth",timestamp:new Date,ip_address:"***********"}]}catch(e){return console.error("Failed to fetch audit logs:",e),[]}}async getUserActivity(e,t=50){return this.getAuditLogs({userId:e,limit:t})}async getResourceActivity(e,t){return this.getAuditLogs({resourceType:e})}async logLogin(e,t,r){await this.logAction({user_id:e,action:"login",resource_type:"auth",ip_address:t,user_agent:r})}async logLogout(e,t){await this.logAction({user_id:e,action:"logout",resource_type:"auth",ip_address:t})}async logPasswordReset(e,t){await this.logAction({user_id:e,action:"password_reset",resource_type:"auth",ip_address:t})}async logTicketCreated(e,t){await this.logAction({user_id:e,action:"create",resource_type:"ticket",resource_id:t})}async logTicketUpdated(e,t,r){await this.logAction({user_id:e,action:"update",resource_type:"ticket",resource_id:t,details:r})}async logWorkflowAction(e,t,r,s){await this.logAction({user_id:e,action:r,resource_type:"workflow",resource_id:t,details:s})}}let a=new s},61487:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var s=r(49064),a=r(44512);let o=()=>{let e=(0,a.UL)();return(0,s.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:s})=>e.set(t,r,s))}catch{}}}})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,5452,4512,9064],()=>r(38511));module.exports=s})();