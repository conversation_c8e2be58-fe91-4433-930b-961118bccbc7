"use strict";(()=>{var e={};e.id=9461,e.ids=[9461],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},94951:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>u,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var a={};t.r(a),t.d(a,{POST:()=>d});var s=t(42706),o=t(28203),n=t(45994),i=t(39187),p=t(61487);async function d(e){try{let r=(0,p.U)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{taskId:s,toUserId:o,reason:n}=await e.json();if(!s||!o||!n)return i.NextResponse.json({error:"Task ID, target user, and reason are required"},{status:400});let{data:d,error:u}=await r.rpc("handle_approval_delegation",{p_task_id:s,p_from_user_id:t.id,p_to_user_id:o,p_reason:n});if(u)throw u;return await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"APPROVAL_DELEGATED",details:{task_id:s,delegated_to:o,reason:n},user_id:t.id}),i.NextResponse.json({success:!0,message:"Approval delegated successfully"})}catch(e){return console.error("Error delegating approval:",e),i.NextResponse.json({error:"Failed to delegate approval"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();let u=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/workflows/approvals/delegate/route",pathname:"/api/workflows/approvals/delegate",filename:"route",bundlePath:"app/api/workflows/approvals/delegate/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\delegate\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:v}=u;function g(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8096,2076],()=>t(94951));module.exports=a})();