// MFA translations to be added to the main translations file

export const mfaTranslations = {
  ja: {
    mfa: {
      setup: {
        title: '多要素認証の設定',
        description: 'アカウントのセキュリティを強化するため、多要素認証を設定してください。',
        continue: '続ける',
        complete: '設定完了'
      },
      methods: {
        totp: '認証アプリ',
        sms: 'SMS',
        email: 'メール'
      },
      totp: {
        description: '認証アプリを使用して、ワンタイムパスワードを生成します。',
        scanQR: 'QRコードをスキャン',
        scanInstructions: '認証アプリでこのQRコードをスキャンしてください。',
        manualEntry: '手動入力用のシークレットキー',
        enterCode: '認証アプリに表示される6桁のコードを入力してください'
      },
      sms: {
        description: '携帯電話番号にSMSでワンタイムパスワードを送信します。',
        phoneNumber: '携帯電話番号'
      },
      email: {
        description: '登録済みのメールアドレスにワンタイムパスワードを送信します。'
      },
      verify: {
        title: '認証コードの確認',
        totp: {
          title: '認証アプリの確認',
          description: '認証アプリに表示されている6桁のコードを入力してください。'
        },
        sms: {
          title: 'SMS認証',
          description: '携帯電話に送信された6桁のコードを入力してください。'
        },
        email: {
          title: 'メール認証',
          description: 'メールアドレスに送信された6桁のコードを入力してください。'
        },
        codeLabel: '認証コード',
        codeHelp: '6桁の数字を入力してください',
        submit: '確認',
        useBackupCode: 'バックアップコードを使用'
      },
      backup: {
        title: 'バックアップコード',
        description: 'これらのバックアップコードを安全な場所に保管してください。認証アプリにアクセスできない場合に使用できます。',
        warning: '各バックアップコードは一度しか使用できません。すべてのコードを使用した場合は、新しいコードを生成する必要があります。',
        download: 'コードをダウンロード'
      },
      errors: {
        invalidCode: '無効なコードです',
        verificationFailed: '認証に失敗しました',
        lockedOut: 'アカウントがロックされています。しばらく待ってから再試行してください。',
        attemptsRemaining: '残り{count}回の試行が可能です'
      }
    }
  },
  en: {
    mfa: {
      setup: {
        title: 'Set Up Multi-Factor Authentication',
        description: 'Set up multi-factor authentication to enhance your account security.',
        continue: 'Continue',
        complete: 'Complete Setup'
      },
      methods: {
        totp: 'Authenticator App',
        sms: 'SMS',
        email: 'Email'
      },
      totp: {
        description: 'Use an authenticator app to generate one-time passwords.',
        scanQR: 'Scan QR Code',
        scanInstructions: 'Scan this QR code with your authenticator app.',
        manualEntry: 'Secret key for manual entry',
        enterCode: 'Enter the 6-digit code from your authenticator app'
      },
      sms: {
        description: 'Receive one-time passwords via SMS to your mobile phone.',
        phoneNumber: 'Mobile Phone Number'
      },
      email: {
        description: 'Receive one-time passwords to your registered email address.'
      },
      verify: {
        title: 'Verify Authentication Code',
        totp: {
          title: 'Authenticator App Verification',
          description: 'Enter the 6-digit code displayed in your authenticator app.'
        },
        sms: {
          title: 'SMS Verification',
          description: 'Enter the 6-digit code sent to your mobile phone.'
        },
        email: {
          title: 'Email Verification',
          description: 'Enter the 6-digit code sent to your email address.'
        },
        codeLabel: 'Authentication Code',
        codeHelp: 'Enter the 6-digit number',
        submit: 'Verify',
        useBackupCode: 'Use Backup Code'
      },
      backup: {
        title: 'Backup Codes',
        description: 'Store these backup codes in a safe place. You can use them to access your account if you cannot use your authenticator app.',
        warning: 'Each backup code can only be used once. If you use all codes, you will need to generate new ones.',
        download: 'Download Codes'
      },
      errors: {
        invalidCode: 'Invalid code',
        verificationFailed: 'Verification failed',
        lockedOut: 'Account is locked. Please wait before trying again.',
        attemptsRemaining: '{count} attempts remaining'
      }
    }
  }
};
