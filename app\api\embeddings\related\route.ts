import { NextRequest, NextResponse } from 'next/server'
import { embeddingsService } from '@/lib/services/embeddings-service'

export const runtime = 'nodejs'

export async function POST(request: NextRequest) {
  try {
    const { articleId, language, limit } = await request.json()

    if (!articleId || !language) {
      return NextResponse.json(
        { error: 'Article ID and language are required' },
        { status: 400 }
      )
    }

    const related = await embeddingsService.findRelatedArticles(
      articleId,
      language,
      limit
    )

    return NextResponse.json({
      success: true,
      related,
      count: related.length
    })
  } catch (error) {
    console.error('Related articles error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to find related articles'
      },
      { status: 500 }
    )
  }
}
