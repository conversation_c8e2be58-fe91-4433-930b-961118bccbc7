'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { RequestStatusTracker } from '@/components/real-time'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2 } from 'lucide-react'
import AuthGuard from '@/components/auth/auth-guard'
import { Database } from '@/lib/database.types'

type RequestForm = Database['public']['Tables']['request_forms']['Row']

export default function RequestDetailsPage() {
  const params = useParams()
  const requestId = params.id as string
  const [request, setRequest] = useState<RequestForm | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchRequestDetails()
  }, [requestId])

  const fetchRequestDetails = async () => {
    const { data, error } = await supabase
      .from('request_forms')
      .select('*')
      .eq('id', requestId)
      .single()

    if (data) {
      setRequest(data)
    }
    setLoading(false)
  }

  if (loading) {
    return (
      <AuthGuard>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AuthGuard>
    )
  }

  if (!request) {
    return (
      <AuthGuard>
        <div className="container mx-auto p-6">
          <Card>
            <CardContent className="p-6">
              <p className="text-center text-muted-foreground">
                リクエストが見つかりません / Request not found
              </p>
            </CardContent>
          </Card>
        </div>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">
            リクエスト詳細 / Request Details
          </h1>
          <Badge variant="outline">ID: {request.id}</Badge>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>基本情報 / Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">タイトル / Title</p>
                <p className="font-medium">{request.title}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">説明 / Description</p>
                <p className="font-medium">{request.description || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">優先度 / Priority</p>
                <p className="font-medium capitalize">{request.priority}</p>
              </div>
            </CardContent>
          </Card>

          <RequestStatusTracker 
            requestId={requestId} 
            initialData={request}
          />
        </div>
      </div>
    </AuthGuard>
  )
}
