/**
 * Integration tests for Backup API
 * Tests backup creation, restoration, and management endpoints
 */

import { NextRequest } from 'next/server'
import { POST, GET } from '@/app/api/admin/backup/route'
import { POST as restoreBackup } from '@/app/api/admin/backup/[id]/restore/route'

// Mock dependencies
jest.mock('@/lib/supabase/server')
jest.mock('@/lib/backup/backup-manager')

describe('/api/admin/backup', () => {
  let mockRequest: NextRequest
  let mockSupabase: any

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock Supabase client
    mockSupabase = {
      auth: {
        getUser: jest.fn()
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn()
      }))
    }

    const { createClient } = require('@/lib/supabase/server')
    createClient.mockReturnValue(mockSupabase)

    // Create mock request
    mockRequest = new NextRequest('http://localhost:3000/api/admin/backup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
  })

  describe('Authentication and Authorization', () => {
    it('should reject unauthenticated requests', async () => {
      // Mock unauthenticated user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Not authenticated' }
      })

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should reject users without admin privileges', async () => {
      // Mock authenticated user without admin role
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'user' },
        error: null
      })

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('Insufficient permissions')
    })

    it('should allow admin users to access backup endpoints', async () => {
      // Mock authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'admin' },
        error: null
      })

      // Mock backup manager
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.createBackup.mockResolvedValue({
        id: 'backup_123',
        status: 'completed',
        timestamp: new Date().toISOString(),
        size_bytes: 1024,
        tables_count: 5,
        records_count: 100,
        checksum: 'abc123'
      })

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.backup).toBeDefined()
    })

    it('should allow system_admin users to access backup endpoints', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'sysadmin123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'system_admin' },
        error: null
      })

      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.createBackup.mockResolvedValue({
        id: 'backup_123',
        status: 'completed'
      })

      const response = await POST(mockRequest)

      expect(response.status).toBe(200)
    })
  })

  describe('POST /api/admin/backup (Create Backup)', () => {
    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'admin' },
        error: null
      })
    })

    it('should create backup successfully', async () => {
      const mockBackupResult = {
        id: 'backup_20240101_123456',
        status: 'completed',
        timestamp: '2024-01-01T12:34:56Z',
        size_bytes: 2048,
        tables_count: 10,
        records_count: 500,
        checksum: 'def456'
      }

      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.createBackup.mockResolvedValue(mockBackupResult)

      const requestWithBody = new NextRequest('http://localhost:3000/api/admin/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: 'Manual backup for testing' })
      })

      const response = await POST(requestWithBody)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.backup).toEqual(mockBackupResult)
      expect(backupManager.createBackup).toHaveBeenCalledWith('Manual backup for testing')
    })

    it('should handle backup creation failure', async () => {
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.createBackup.mockRejectedValue(new Error('Backup creation failed'))

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Backup creation failed')
    })

    it('should create backup without description', async () => {
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.createBackup.mockResolvedValue({
        id: 'backup_123',
        status: 'completed'
      })

      const requestWithoutDescription = new NextRequest('http://localhost:3000/api/admin/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })

      const response = await POST(requestWithoutDescription)

      expect(response.status).toBe(200)
      expect(backupManager.createBackup).toHaveBeenCalledWith(undefined)
    })
  })

  describe('GET /api/admin/backup (List Backups)', () => {
    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'admin' },
        error: null
      })
    })

    it('should list backups successfully', async () => {
      const mockBackups = [
        {
          id: 'backup_1',
          timestamp: '2024-01-01T00:00:00Z',
          status: 'completed',
          size_bytes: 1024
        },
        {
          id: 'backup_2',
          timestamp: '2024-01-02T00:00:00Z',
          status: 'completed',
          size_bytes: 2048
        }
      ]

      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.listBackups.mockResolvedValue(mockBackups)

      const getRequest = new NextRequest('http://localhost:3000/api/admin/backup')
      const response = await GET(getRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.backups).toEqual(mockBackups)
      expect(backupManager.listBackups).toHaveBeenCalledWith(50) // Default limit
    })

    it('should respect limit parameter', async () => {
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.listBackups.mockResolvedValue([])

      const getRequest = new NextRequest('http://localhost:3000/api/admin/backup?limit=10')
      await GET(getRequest)

      expect(backupManager.listBackups).toHaveBeenCalledWith(10)
    })

    it('should handle listing errors', async () => {
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.listBackups.mockRejectedValue(new Error('Database connection failed'))

      const getRequest = new NextRequest('http://localhost:3000/api/admin/backup')
      const response = await GET(getRequest)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Database connection failed')
    })
  })

  describe('POST /api/admin/backup/[id]/restore (Restore Backup)', () => {
    const mockBackupId = 'backup_20240101_123456'

    beforeEach(() => {
      // Setup authenticated admin user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'admin' },
        error: null
      })
    })

    it('should restore backup successfully', async () => {
      const mockRestoreResult = {
        success: true,
        message: 'Restore completed successfully. Restored 100 records',
        restored_records: 100
      }

      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.restoreFromBackup.mockResolvedValue(mockRestoreResult)

      const restoreRequest = new NextRequest(
        `http://localhost:3000/api/admin/backup/${mockBackupId}/restore`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            verify_checksum: true,
            dry_run: false
          })
        }
      )

      const response = await restoreBackup(restoreRequest, { params: { id: mockBackupId } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Restore completed successfully. Restored 100 records')
      expect(data.restored_records).toBe(100)
      expect(backupManager.restoreFromBackup).toHaveBeenCalledWith(mockBackupId, {
        verify_checksum: true,
        dry_run: false,
        tables: undefined
      })
    })

    it('should perform dry run restoration', async () => {
      const mockDryRunResult = {
        success: true,
        message: 'Dry run completed. Would restore 100 records',
        restored_records: 100
      }

      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.restoreFromBackup.mockResolvedValue(mockDryRunResult)

      const restoreRequest = new NextRequest(
        `http://localhost:3000/api/admin/backup/${mockBackupId}/restore`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            dry_run: true,
            verify_checksum: true
          })
        }
      )

      const response = await restoreBackup(restoreRequest, { params: { id: mockBackupId } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.message).toContain('Dry run completed')
      expect(backupManager.restoreFromBackup).toHaveBeenCalledWith(mockBackupId, {
        verify_checksum: true,
        dry_run: true,
        tables: undefined
      })
    })

    it('should restore specific tables only', async () => {
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.restoreFromBackup.mockResolvedValue({
        success: true,
        message: 'Partial restore completed',
        restored_records: 50
      })

      const restoreRequest = new NextRequest(
        `http://localhost:3000/api/admin/backup/${mockBackupId}/restore`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            tables: ['users', 'tickets'],
            verify_checksum: false
          })
        }
      )

      await restoreBackup(restoreRequest, { params: { id: mockBackupId } })

      expect(backupManager.restoreFromBackup).toHaveBeenCalledWith(mockBackupId, {
        verify_checksum: false,
        dry_run: false,
        tables: ['users', 'tickets']
      })
    })

    it('should handle restoration failure', async () => {
      const mockFailureResult = {
        success: false,
        message: 'Restore failed: Backup not found'
      }

      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.restoreFromBackup.mockResolvedValue(mockFailureResult)

      const restoreRequest = new NextRequest(
        `http://localhost:3000/api/admin/backup/${mockBackupId}/restore`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({})
        }
      )

      const response = await restoreBackup(restoreRequest, { params: { id: mockBackupId } })
      const data = await response.json()

      expect(response.status).toBe(200) // API returns 200 even for logical failures
      expect(data.success).toBe(false)
      expect(data.message).toBe('Restore failed: Backup not found')
    })

    it('should handle unexpected errors during restoration', async () => {
      const { backupManager } = require('@/lib/backup/backup-manager')
      backupManager.restoreFromBackup.mockRejectedValue(new Error('Unexpected error'))

      const restoreRequest = new NextRequest(
        `http://localhost:3000/api/admin/backup/${mockBackupId}/restore`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({})
        }
      )

      const response = await restoreBackup(restoreRequest, { params: { id: mockBackupId } })
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unexpected error')
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed JSON in request body', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin123' } },
        error: null
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'admin' },
        error: null
      })

      const malformedRequest = new NextRequest('http://localhost:3000/api/admin/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json'
      })

      const response = await POST(malformedRequest)

      expect(response.status).toBe(500)
    })

    it('should handle database connection errors during auth check', async () => {
      mockSupabase.auth.getUser.mockRejectedValue(new Error('Database connection failed'))

      const response = await POST(mockRequest)

      expect(response.status).toBe(500)
    })
  })
})
