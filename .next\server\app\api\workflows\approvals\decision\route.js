(()=>{var e={};e.id=5978,e.ids=[5978],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},50285:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>w,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>_});var r={};a.r(r),a.d(r,{POST:()=>d});var o=a(42706),s=a(28203),i=a(45994),n=a(39187),l=a(61487),c=a(14724),p=a(68967);async function d(e){try{let t=(0,l.U)(),{data:{user:a},error:r}=await t.auth.getUser();if(r||!a)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{taskId:o,decision:s,comments:i,attachments:d}=await e.json();if(!o||!s)return n.NextResponse.json({error:"Task ID and decision are required"},{status:400});if(!["approve","reject","escalate"].includes(s))return n.NextResponse.json({error:"Invalid decision"},{status:400});return await c.g.processApprovalDecision({taskId:o,decision:s,comments:i,attachments:d},a.id),await p.H.logWorkflowAction(a.id,o,"approval_decision",{decision:s,comments_provided:!!i}),n.NextResponse.json({success:!0,message:"Approval decision processed successfully"})}catch(e){return console.error("Error processing approval decision:",e),n.NextResponse.json({error:"Failed to process approval decision"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/approvals/decision/route",pathname:"/api/workflows/approvals/decision",filename:"route",bundlePath:"app/api/workflows/approvals/decision/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\decision\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:_,serverHooks:w}=u;function h(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:_})}},96487:()=>{},78335:()=>{},68967:(e,t,a)=>{"use strict";a.d(t,{H:()=>o});class r{async logAction(e){try{console.log("Audit log entry:",{...e,timestamp:e.timestamp||new Date}),await new Promise(e=>setTimeout(e,100))}catch(e){console.error("Failed to log audit entry:",e)}}async getAuditLogs(e={}){try{return await new Promise(e=>setTimeout(e,200)),[{id:"1",user_id:"user1",action:"login",resource_type:"auth",timestamp:new Date,ip_address:"***********"}]}catch(e){return console.error("Failed to fetch audit logs:",e),[]}}async getUserActivity(e,t=50){return this.getAuditLogs({userId:e,limit:t})}async getResourceActivity(e,t){return this.getAuditLogs({resourceType:e})}async logLogin(e,t,a){await this.logAction({user_id:e,action:"login",resource_type:"auth",ip_address:t,user_agent:a})}async logLogout(e,t){await this.logAction({user_id:e,action:"logout",resource_type:"auth",ip_address:t})}async logPasswordReset(e,t){await this.logAction({user_id:e,action:"password_reset",resource_type:"auth",ip_address:t})}async logTicketCreated(e,t){await this.logAction({user_id:e,action:"create",resource_type:"ticket",resource_id:t})}async logTicketUpdated(e,t,a){await this.logAction({user_id:e,action:"update",resource_type:"ticket",resource_id:t,details:a})}async logWorkflowAction(e,t,a,r){await this.logAction({user_id:e,action:a,resource_type:"workflow",resource_id:t,details:r})}}let o=new r},14724:(e,t,a)=>{"use strict";a.d(t,{a:()=>s,g:()=>i});var r=a(61487),o=a(68967);class s{async createApprovalChain(e,t,a){let s=(0,r.U)();try{let{data:r,error:i}=await s.from("approval_chains").insert({workflow_instance_id:e,chain_config:t,current_level:0,status:"pending",context_data:a}).select().single();if(i||!r)throw Error("Failed to create approval chain");await this.processApprovalLevel(r.id,0,a),await o.auditLogger.log({action:"APPROVAL_CHAIN_CREATED",details:{chain_id:r.id,workflow_instance_id:e,levels:t.levels.length}})}catch(e){throw console.error("Error creating approval chain:",e),e}}async processApprovalLevel(e,t,a){let o=(0,r.U)(),{data:s,error:i}=await o.from("approval_chains").select("*").eq("id",e).single();if(i||!s)throw Error("Approval chain not found");let n=s.chain_config.levels[t];if(!n){await this.completeApprovalChain(e,"approved");return}if(n.skipCondition&&this.evaluateCondition(n.skipCondition,a)){await this.processApprovalLevel(e,t+1,a);return}let l=await this.resolveApprovers(n.approvers,a),c=[];for(let r of l){let o=await this.createApprovalTask({chainId:e,level:t,approver:r,dueMinutes:n.escalationMinutes,requireAll:n.requireAll,context:a});c.push(o)}await o.from("approval_chains").update({current_level:t,current_level_tasks:c.map(e=>e.id)}).eq("id",e)}async createApprovalTask(e){let t=(0,r.U)(),{chainId:a,level:o,approver:s,dueMinutes:i,requireAll:n,context:l}=e,c=i?new Date(Date.now()+6e4*i).toISOString():void 0,{data:p,error:d}=await t.from("workflow_tasks").insert({workflow_instance_id:l.workflow_instance_id,task_type:"multi_level_approval",task_name:`Approval Required - Level ${o+1}`,assigned_to:s.userId,assigned_role:s.role,status:"pending",due_date:c,task_data:{approval_chain_id:a,approval_level:o,require_all:n,context:l}}).select().single();if(d||!p)throw Error("Failed to create approval task");return await this.sendApprovalNotification(p,o),p}async processApprovalDecision(e,t){let a=(0,r.U)();try{let{data:r,error:s}=await a.from("workflow_tasks").select("*").eq("id",e.taskId).single();if(s||!r)throw Error("Approval task not found");await a.from("workflow_tasks").update({status:"completed",completed_at:new Date().toISOString(),completed_by:t,task_data:{...r.task_data,decision:e.decision,comments:e.comments,attachments:e.attachments}}).eq("id",e.taskId);let i=r.task_data.approval_chain_id,{data:n}=await a.from("approval_chains").select("*").eq("id",i).single();if(!n)throw Error("Approval chain not found");switch(e.decision){case"approve":await this.handleApproval(n,r,t);break;case"reject":await this.handleRejection(n,r,t);break;case"escalate":await this.handleEscalation(n,r,t)}await o.auditLogger.log({action:"APPROVAL_DECISION",details:{task_id:e.taskId,decision:e.decision,level:r.task_data.approval_level,comments:e.comments},user_id:t})}catch(e){throw console.error("Error processing approval decision:",e),e}}async handleApproval(e,t,a){let o=(0,r.U)(),s=e.chain_config,i=e.current_level;if(s.levels[i].requireAll){let{data:a}=await o.from("workflow_tasks").select("*").eq("task_data->approval_chain_id",e.id).eq("task_data->approval_level",i);if(!a?.every(e=>e.id===t.id||"approve"===e.task_data.decision))return}i<s.levels.length-1?await this.processApprovalLevel(e.id,i+1,e.context_data):await this.completeApprovalChain(e.id,"approved")}async handleRejection(e,t,a){await this.completeApprovalChain(e.id,"rejected")}async handleEscalation(e,t,a){let o=(0,r.U)(),s=e.chain_config,i=e.current_level;i<s.levels.length-1?(await o.from("workflow_tasks").update({status:"cancelled"}).eq("task_data->approval_chain_id",e.id).eq("task_data->approval_level",i).eq("status","pending"),await this.processApprovalLevel(e.id,i+1,e.context_data)):await this.completeApprovalChain(e.id,"escalated")}async completeApprovalChain(e,t){let a=(0,r.U)(),{data:o,error:s}=await a.from("approval_chains").update({status:"completed",outcome:t,completed_at:new Date().toISOString()}).eq("id",e).select().single();if(s||!o)throw Error("Failed to complete approval chain");await a.from("workflow_instances").update({context_data:{...o.context_data,approval_outcome:t,approval_chain_id:e}}).eq("id",o.workflow_instance_id),await this.triggerWorkflowContinuation(o.workflow_instance_id,t)}async resolveApprovers(e,t){let a=[],o=(0,r.U)();for(let r of e)if(r.userId)a.push({userId:r.userId});else if(r.role){let e=r.department||t.department,{data:s}=await o.from("staff").select("id").eq("role_id",await this.getRoleId(r.role)).eq("division_id",e);s&&s.length>0?a.push({userId:s[0].id}):a.push({role:`${r.role}:${e}`})}else if(r.expression){let e=this.evaluateExpression(r.expression,t);e&&a.push({userId:e})}return a}async getRoleId(e){let t=(0,r.U)(),{data:a}=await t.from("roles").select("id").eq("name",e).single();return a?.id||null}evaluateCondition(e,t){try{return Function("context",`return ${e}`)(t)}catch(e){return console.error("Error evaluating condition:",e),!1}}evaluateExpression(e,t){try{return Function("context",`return ${e}`)(t)}catch(e){return console.error("Error evaluating expression:",e),null}}async sendApprovalNotification(e,t){console.log(`Sending approval notification for task ${e.id} at level ${t}`)}async triggerWorkflowContinuation(e,t){console.log(`Triggering workflow continuation for ${e} with outcome ${t}`)}async handleApprovalTimeout(e){let t=(0,r.U)(),{data:a}=await t.from("approval_chains").select("*").eq("id",e).single();if(!a)return;let o=a.chain_config;switch(o.timeoutAction){case"escalate":let s=a.current_level;s<o.levels.length-1?await this.processApprovalLevel(e,s+1,a.context_data):await this.completeApprovalChain(e,"timeout");break;case"auto_approve":await this.completeApprovalChain(e,"approved");break;case"auto_reject":await this.completeApprovalChain(e,"rejected")}}async getApprovalChainStatus(e){let t=(0,r.U)(),{data:a,error:o}=await t.from("approval_chains").select(`
        *,
        workflow_tasks (
          id,
          task_name,
          assigned_to,
          assigned_role,
          status,
          task_data,
          completed_at,
          completed_by
        )
      `).eq("id",e).single();if(o||!a)throw Error("Approval chain not found");let s=a.chain_config,i=s.levels.map((e,t)=>{let r=a.workflow_tasks.filter(e=>e.task_data.approval_level===t);return{level:t+1,name:e.name,status:this.getLevelStatus(r),approvers:r.map(e=>({taskId:e.id,assignedTo:e.assigned_to,assignedRole:e.assigned_role,status:e.status,decision:e.task_data.decision,completedAt:e.completed_at,completedBy:e.completed_by}))}});return{id:a.id,status:a.status,outcome:a.outcome,currentLevel:a.current_level+1,totalLevels:s.levels.length,levels:i,startedAt:a.created_at,completedAt:a.completed_at}}getLevelStatus(e){return 0===e.length?"pending":e.some(e=>"reject"===e.task_data.decision)?"rejected":e.every(e=>"completed"===e.status)?"approved":e.some(e=>"in_progress"===e.status)?"in_progress":"pending"}}let i=new s},61487:(e,t,a)=>{"use strict";a.d(t,{U:()=>s});var r=a(49064),o=a(44512);let s=()=>{let e=(0,o.UL)();return(0,r.createServerClient)("your_supabase_project_url","your_supabase_anon_key",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:a,options:r})=>e.set(t,a,r))}catch{}}}})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[5994,5452,4512,9064],()=>a(50285));module.exports=r})();