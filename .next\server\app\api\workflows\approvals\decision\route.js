"use strict";(()=>{var e={};e.id=5978,e.ids=[5978],e.modules={93939:e=>{e.exports=require("@supabase/supabase-js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73060:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>v});var o={};s.r(o),s.d(o,{POST:()=>u});var t=s(42706),a=s(28203),n=s(45994),i=s(39187),p=s(61487),d=s(14724);async function u(e){try{let r=(0,p.U)(),{data:{user:s},error:o}=await r.auth.getUser();if(o||!s)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{taskId:t,decision:a,comments:n,attachments:u}=await e.json();if(!t||!a)return i.NextResponse.json({error:"Task ID and decision are required"},{status:400});if(!["approve","reject","escalate"].includes(a))return i.NextResponse.json({error:"Invalid decision"},{status:400});return await d.g.processApprovalDecision({taskId:t,decision:a,comments:n,attachments:u},s.id),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"APPROVAL_DECISION_MADE",details:{task_id:t,decision:a,comments_provided:!!n},user_id:s.id}),i.NextResponse.json({success:!0,message:"Approval decision processed successfully"})}catch(e){return console.error("Error processing approval decision:",e),i.NextResponse.json({error:"Failed to process approval decision"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();let c=new t.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/approvals/decision/route",pathname:"/api/workflows/approvals/decision",filename:"route",bundlePath:"app/api/workflows/approvals/decision/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\decision\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:v,serverHooks:x}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:v})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),o=r.X(0,[8096,2076],()=>s(73060));module.exports=o})();