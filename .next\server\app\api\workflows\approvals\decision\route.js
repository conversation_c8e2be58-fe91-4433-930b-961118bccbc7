"use strict";(()=>{var e={};e.id=5978,e.ids=[5978],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73060:(e,r,o)=>{o.r(r),o.d(r,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>v,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};o.r(s),o.d(s,{POST:()=>d});var t=o(42706),a=o(28203),n=o(45994),i=o(39187),p=o(14724);async function d(e){try{let r=Object(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e}())(),{data:{user:o},error:s}=await r.auth.getUser();if(s||!o)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{taskId:t,decision:a,comments:n,attachments:d}=await e.json();if(!t||!a)return i.NextResponse.json({error:"Task ID and decision are required"},{status:400});if(!["approve","reject","escalate"].includes(a))return i.NextResponse.json({error:"Invalid decision"},{status:400});return await p.g.processApprovalDecision({taskId:t,decision:a,comments:n,attachments:d},o.id),await Object(function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}()).log({action:"APPROVAL_DECISION_MADE",details:{task_id:t,decision:a,comments_provided:!!n},user_id:o.id}),i.NextResponse.json({success:!0,message:"Approval decision processed successfully"})}catch(e){return console.error("Error processing approval decision:",e),i.NextResponse.json({error:"Failed to process approval decision"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/supabase/server'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/services/audit'");throw e.code="MODULE_NOT_FOUND",e}();let u=new t.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/approvals/decision/route",pathname:"/api/workflows/approvals/decision",filename:"route",bundlePath:"app/api/workflows/approvals/decision/route"},resolvedPagePath:"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\api\\workflows\\approvals\\decision\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:v}=u;function f(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),s=r.X(0,[8096,2076],()=>o(73060));module.exports=s})();