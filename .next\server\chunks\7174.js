exports.id=7174,exports.ids=[7174],exports.modules={19644:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},77788:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},42125:()=>{},66093:()=>{},75339:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>u,XL:()=>d});var o=r(45512),n=r(58009),s=r(21643),a=r(59462);let i=(0,s.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=n.forwardRef(({className:e,variant:t,...r},n)=>(0,o.jsx)("div",{ref:n,role:"alert",className:(0,a.cn)(i({variant:t}),e),...r}));l.displayName="Alert";let d=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("h5",{ref:r,className:(0,a.cn)("mb-1 font-medium leading-none tracking-tight",e),...t}));d.displayName="AlertTitle";let u=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,a.cn)("text-sm [&_p]:leading-relaxed",e),...t}));u.displayName="AlertDescription"},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var o=r(45512);r(58009);var n=r(21643),s=r(59462);let a=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,o.jsx)("div",{className:(0,s.cn)(a({variant:t}),e),...r})}},87021:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var o=r(45512),n=r(58009),s=r(12705),a=r(21643),i=r(59462);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...a},d)=>{let u=n?s.DX:"button";return(0,o.jsx)(u,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:d,...a})});d.displayName="Button"},97643:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>u,ZB:()=>l,Zp:()=>a,aR:()=>i,wL:()=>c});var o=r(45512),n=r(58009),s=r(59462);let a=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));a.displayName="Card";let i=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("h3",{ref:r,className:(0,s.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let u=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent";let c=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t}));c.displayName="CardFooter"},69193:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>d,av:()=>u,j7:()=>l,tU:()=>i});var o=r(45512),n=r(58009),s=r(55613),a=r(59462);let i=s.bL,l=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.B8,{ref:r,className:(0,a.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=s.B8.displayName;let d=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.l9,{ref:r,className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=s.l9.displayName;let u=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.UC,{ref:r,className:(0,a.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));u.displayName=s.UC.displayName},95379:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});class o{constructor(){this.providers=new Map,this.defaultProvider="openai",this.initializeProviders()}initializeProviders(){let e=process.env.NEXT_PUBLIC_OPENAI_API_KEY||process.env.OPENAI_API_KEY;e&&this.providers.set("openai",{name:"openai",apiKey:e,baseUrl:"https://api.openai.com/v1"});let t=process.env.NEXT_PUBLIC_ANTHROPIC_API_KEY||process.env.ANTHROPIC_API_KEY;t&&this.providers.set("anthropic",{name:"anthropic",apiKey:t,baseUrl:"https://api.anthropic.com/v1"})}setDefaultProvider(e){if(this.providers.has(e))this.defaultProvider=e;else throw Error(`Provider ${e} is not configured`)}async completion(e,t){let r=t||this.defaultProvider,o=this.providers.get(r);if(!o)throw Error(`Provider ${r} is not configured`);switch(r){case"openai":return this.openAICompletion(e,o);case"anthropic":return this.anthropicCompletion(e,o);default:throw Error(`Unsupported provider: ${r}`)}}async openAICompletion(e,t){let r=e.model||"gpt-4-turbo-preview",o={model:r,messages:[...e.systemPrompt?[{role:"system",content:e.systemPrompt}]:[],{role:"user",content:e.prompt}],temperature:e.temperature??.7,max_tokens:e.maxTokens??1e3};"json"===e.responseFormat&&(o.response_format={type:"json_object"});let n=await fetch(`${t.baseUrl}/chat/completions`,{method:"POST",headers:{Authorization:`Bearer ${t.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok)throw Error(`OpenAI API error: ${n.statusText}`);let s=await n.json();return{text:s.choices[0].message.content,usage:s.usage?{promptTokens:s.usage.prompt_tokens,completionTokens:s.usage.completion_tokens,totalTokens:s.usage.total_tokens}:void 0,provider:"openai",model:r}}async anthropicCompletion(e,t){let r=e.model||"claude-3-opus-20240229",o={model:r,messages:[{role:"user",content:e.prompt}],system:e.systemPrompt,temperature:e.temperature??.7,max_tokens:e.maxTokens??1e3},n=await fetch(`${t.baseUrl}/messages`,{method:"POST",headers:{"x-api-key":t.apiKey,"anthropic-version":"2023-06-01","Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok)throw Error(`Anthropic API error: ${n.statusText}`);let s=await n.json();return{text:s.content[0].text,usage:s.usage?{promptTokens:s.usage.input_tokens,completionTokens:s.usage.output_tokens,totalTokens:s.usage.input_tokens+s.usage.output_tokens}:void 0,provider:"anthropic",model:r}}async embedding(e,t="openai"){let r=this.providers.get(t);if(!r)throw Error(`Provider ${t} is not configured`);if("openai"!==t)throw Error("Embeddings are currently only supported with OpenAI");let o=e.model||"text-embedding-3-small",n=Array.isArray(e.text)?e.text:[e.text],s=await fetch(`${r.baseUrl}/embeddings`,{method:"POST",headers:{Authorization:`Bearer ${r.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({model:o,input:n})});if(!s.ok)throw Error(`OpenAI Embeddings API error: ${s.statusText}`);let a=await s.json();return{embeddings:a.data.map(e=>e.embedding),usage:a.usage?{totalTokens:a.usage.total_tokens}:void 0}}async validateFormField(e,t,r){let o=`You are validating form fields for a Japanese IT helpdesk system.
Field type: ${e}
Context: ${JSON.stringify(r||{})}

Validate the input and provide:
1. Whether it's valid (true/false)
2. List of issues if invalid
3. Suggestions for correction

Return as JSON with keys: isValid, issues, suggestions`;return JSON.parse((await this.completion({systemPrompt:o,prompt:`Validate this value: "${t}"`,temperature:.3,responseFormat:"json"})).text)}async generateFormSuggestions(e,t,r){let o=`Generate autocomplete suggestions for a Japanese IT helpdesk form field.
Field type: ${e}
Context: ${JSON.stringify(r||{})}

Provide 3-5 relevant suggestions based on the partial input.
Return as JSON array of strings.`;return JSON.parse((await this.completion({systemPrompt:o,prompt:`Partial value: "${t}"`,temperature:.5,responseFormat:"json"})).text)}async classifyRequest(e,t){let r=`Classify IT helpdesk requests into categories.
Available categories: ${t.join(", ")}

Return JSON with: category (exact match from list), confidence (0-1)`;return JSON.parse((await this.completion({systemPrompt:r,prompt:e,temperature:.3,responseFormat:"json"})).text)}async translateText(e,t,r){return t===r?e:(await this.completion({systemPrompt:`Translate the following text from ${t} to ${r}. 
Maintain technical terminology appropriate for IT helpdesk context.
Return only the translated text.`,prompt:e,temperature:.3})).text}estimateCost(e,t){let r={"gpt-4-turbo-preview":{prompt:.01,completion:.03},"gpt-4":{prompt:.03,completion:.06},"gpt-3.5-turbo":{prompt:5e-4,completion:.0015},"claude-3-opus-20240229":{prompt:.015,completion:.075},"claude-3-sonnet-20240229":{prompt:.003,completion:.015}}[t]||{prompt:.01,completion:.03};return e.promptTokens/1e3*r.prompt+e.completionTokens/1e3*r.completion}isConfigured(e){return e?this.providers.has(e):this.providers.size>0}getAvailableProviders(){return Array.from(this.providers.keys())}}let n=new o},59462:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var o=r(82281),n=r(94805);function s(...e){return(0,n.QP)((0,o.$)(e))}},19611:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var o=r(62740),n=r(61421),s=r.n(n);r(82704);let a={title:"ITSync - Enterprise IT Helpdesk",description:"AI-powered IT Helpdesk & Support Platform"};function i({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:s().className,children:e})})}},82704:()=>{},48305:(e,t,r)=>{"use strict";r.d(t,{RG:()=>w,bL:()=>C,q7:()=>F});var o=r(58009),n=r(31412),s=r(39217),a=r(29952),i=r(6004),l=r(30096),d=r(30830),u=r(92828),c=r(13024),p=r(59018),f=r(45512),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[h,b,y]=(0,s.N)(g),[x,w]=(0,i.A)(g,[y]),[N,P]=x(g),T=o.forwardRef((e,t)=>(0,f.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:t})})}));T.displayName=g;var j=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:s,loop:i=!1,dir:l,currentTabStopId:h,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:P=!1,...T}=e,j=o.useRef(null),k=(0,a.s)(t,j),I=(0,p.jH)(l),[A,C]=(0,c.i)({prop:h,defaultProp:y??null,onChange:x,caller:g}),[F,E]=o.useState(!1),_=(0,u.c)(w),S=b(r),$=o.useRef(!1),[O,D]=o.useState(0);return o.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(m,_),()=>e.removeEventListener(m,_)},[_]),(0,f.jsx)(N,{scope:r,orientation:s,dir:I,loop:i,currentTabStopId:A,onItemFocus:o.useCallback(e=>C(e),[C]),onItemShiftTab:o.useCallback(()=>E(!0),[]),onFocusableItemAdd:o.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>D(e=>e-1),[]),children:(0,f.jsx)(d.sG.div,{tabIndex:F||0===O?-1:0,"data-orientation":s,...T,ref:k,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{$.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!$.current;if(e.target===e.currentTarget&&t&&!F){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);R([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),P)}}$.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>E(!1))})})}),k="RovingFocusGroupItem",I=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:s=!0,active:a=!1,tabStopId:i,children:u,...c}=e,p=(0,l.B)(),m=i||p,v=P(k,r),g=v.currentTabStopId===m,y=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:N}=v;return o.useEffect(()=>{if(s)return x(),()=>w()},[s,x,w]),(0,f.jsx)(h.ItemSlot,{scope:r,id:m,focusable:s,active:a,children:(0,f.jsx)(d.sG.span,{tabIndex:g?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{s?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var o;let n=(o=e.key,"rtl"!==r?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return A[n]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let o=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,o)=>e[(t+o)%e.length])}(r,o+1):r.slice(o+1)}setTimeout(()=>R(r))}}),children:"function"==typeof u?u({isCurrentTabStop:g,hasTabStop:null!=N}):u})})});I.displayName=k;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function R(e,t=!1){let r=document.activeElement;for(let o of e)if(o===r||(o.focus({preventScroll:t}),document.activeElement!==r))return}var C=T,F=I},55613:(e,t,r)=>{"use strict";r.d(t,{B8:()=>R,UC:()=>F,bL:()=>A,l9:()=>C});var o=r(58009),n=r(31412),s=r(6004),a=r(48305),i=r(98060),l=r(30830),d=r(59018),u=r(13024),c=r(30096),p=r(45512),f="Tabs",[m,v]=(0,s.A)(f,[a.RG]),g=(0,a.RG)(),[h,b]=m(f),y=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,onValueChange:n,defaultValue:s,orientation:a="horizontal",dir:i,activationMode:m="automatic",...v}=e,g=(0,d.jH)(i),[b,y]=(0,u.i)({prop:o,onChange:n,defaultProp:s??"",caller:f});return(0,p.jsx)(h,{scope:r,baseId:(0,c.B)(),value:b,onValueChange:y,orientation:a,dir:g,activationMode:m,children:(0,p.jsx)(l.sG.div,{dir:g,"data-orientation":a,...v,ref:t})})});y.displayName=f;var x="TabsList",w=o.forwardRef((e,t)=>{let{__scopeTabs:r,loop:o=!0,...n}=e,s=b(x,r),i=g(r);return(0,p.jsx)(a.bL,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:o,children:(0,p.jsx)(l.sG.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:t})})});w.displayName=x;var N="TabsTrigger",P=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,disabled:s=!1,...i}=e,d=b(N,r),u=g(r),c=k(d.baseId,o),f=I(d.baseId,o),m=o===d.value;return(0,p.jsx)(a.q7,{asChild:!0,...u,focusable:!s,active:m,children:(0,p.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":f,"data-state":m?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:c,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(o)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(o)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;m||s||!e||d.onValueChange(o)})})})});P.displayName=N;var T="TabsContent",j=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:s,children:a,...d}=e,u=b(T,r),c=k(u.baseId,n),f=I(u.baseId,n),m=n===u.value,v=o.useRef(m);return o.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:s||m,children:({present:r})=>(0,p.jsx)(l.sG.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&a})})});function k(e,t){return`${e}-trigger-${t}`}function I(e,t){return`${e}-content-${t}`}j.displayName=T;var A=y,R=w,C=P,F=j}};