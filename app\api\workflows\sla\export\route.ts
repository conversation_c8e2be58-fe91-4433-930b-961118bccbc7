import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { slaManager } from '@/lib/services/workflow/sla-manager';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format') as 'csv' | 'json' || 'json';

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    // Export SLA report
    const report = await slaManager.exportSLAReport(
      new Date(startDate),
      new Date(endDate),
      format
    );

    // Return appropriate response based on format
    if (format === 'csv') {
      return new NextResponse(report, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="sla-report-${startDate}-${endDate}.csv"`,
        },
      });
    } else {
      return NextResponse.json(report);
    }
  } catch (error) {
    console.error('Error exporting SLA report:', error);
    return NextResponse.json(
      { error: 'Failed to export SLA report' },
      { status: 500 }
    );
  }
}
