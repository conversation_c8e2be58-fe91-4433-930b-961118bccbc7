'use client'

import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { FormField } from '@/lib/form-types'
import { Check, ChevronsUpDown, AlertCircle, CheckCircle, Info, AlertTriangle, Loader2, <PERSON>rk<PERSON> } from 'lucide-react'
import { enhancedAIFormAssistant } from '@/lib/ai-form-assistant-enhanced'
import { ValidationResult } from '@/lib/hooks/use-real-time-validation-enhanced'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface EnhancedDynamicFieldProps {
  field: FormField
  value: any
  onChange: (value: any) => void
  onSuggestionAccept?: (suggestion: any) => void
  validation?: ValidationResult
  isValidating?: boolean
  context?: {
    userId?: string
    departmentId?: string
    userRole?: string
  }
}

export function EnhancedDynamicField({ 
  field, 
  value, 
  onChange, 
  onSuggestionAccept,
  validation,
  isValidating,
  context
}: EnhancedDynamicFieldProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [showSuggestion, setShowSuggestion] = useState(false)
  const [aiSuggestion, setAiSuggestion] = useState<any>(null)

  // Get validation icon and color
  const getValidationIcon = () => {
    if (isValidating) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
    }
    
    if (!validation) return null

    switch (validation.type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  const getFieldBorderColor = () => {
    if (!validation || isValidating) return ''
    
    switch (validation.type) {
      case 'success':
        return 'border-green-500 focus:border-green-500'
      case 'error':
        return 'border-red-500 focus:border-red-500'
      case 'warning':
        return 'border-yellow-500 focus:border-yellow-500'
      case 'info':
        return 'border-blue-500 focus:border-blue-500'
      default:
        return ''
    }
  }

  // Handle AI suggestions
  useEffect(() => {
    if (validation?.suggestion && validation.suggestion !== value) {
      setAiSuggestion(validation.suggestion)
      setShowSuggestion(true)
    } else {
      setShowSuggestion(false)
    }
  }, [validation, value])

  const acceptSuggestion = () => {
    if (aiSuggestion) {
      onChange(aiSuggestion)
      if (onSuggestionAccept) {
        onSuggestionAccept(aiSuggestion)
      }
      setShowSuggestion(false)
    }
  }

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <div className="relative">
            <Input
              id={field.id}
              type={field.validation === 'email' ? 'email' : 'text'}
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              className={cn(
                "pr-10 transition-colors",
                getFieldBorderColor()
              )}
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2">
              {getValidationIcon()}
            </div>
          </div>
        )

      case 'textarea':
        return (
          <div className="relative">
            <Textarea
              id={field.id}
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              className={cn(
                "pr-10 transition-colors",
                getFieldBorderColor()
              )}
              rows={4}
            />
            <div className="absolute right-2 top-2">
              {getValidationIcon()}
            </div>
          </div>
        )

      case 'select':
        return (
          <div className="relative">
            <Select
              value={value || ''}
              onValueChange={onChange}
              required={field.required}
            >
              <SelectTrigger className={cn(
                "transition-colors",
                getFieldBorderColor()
              )}>
                <SelectValue placeholder={field.placeholder || '選択してください / Please select'} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="absolute right-10 top-1/2 -translate-y-1/2">
              {getValidationIcon()}
            </div>
          </div>
        )

      case 'search':
        return (
          <div className="relative">
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isOpen}
                  className={cn(
                    "w-full justify-between transition-colors",
                    getFieldBorderColor()
                  )}
                >
                  {value?.label || field.placeholder || 'Select...'}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput 
                    placeholder={`Search ${field.label}...`}
                    value={searchValue}
                    onValueChange={setSearchValue}
                  />
                  <CommandEmpty>No results found.</CommandEmpty>
                  <CommandGroup>
                    {field.options?.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        onSelect={() => {
                          onChange(option)
                          setIsOpen(false)
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value?.value === option.value ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {option.label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
            <div className="absolute right-10 top-1/2 -translate-y-1/2">
              {getValidationIcon()}
            </div>
          </div>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.id}
              checked={value || false}
              onCheckedChange={onChange}
              required={field.required}
            />
            <Label htmlFor={field.id} className="text-sm font-normal">
              {field.label}
            </Label>
            {getValidationIcon()}
          </div>
        )

      case 'radio':
        return (
          <RadioGroup
            value={value || ''}
            onValueChange={onChange}
            required={field.required}
          >
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value={option.value} id={`${field.id}-${option.value}`} />
                <Label htmlFor={`${field.id}-${option.value}`} className="font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )

      case 'multiselect':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`${field.id}-${option.value}`}
                  checked={(value || []).includes(option.value)}
                  onCheckedChange={(checked) => {
                    const currentValues = value || []
                    if (checked) {
                      onChange([...currentValues, option.value])
                    } else {
                      onChange(currentValues.filter((v: string) => v !== option.value))
                    }
                  }}
                />
                <Label htmlFor={`${field.id}-${option.value}`} className="font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
            <div className="flex items-center mt-2">
              {getValidationIcon()}
            </div>
          </div>
        )

      default:
        return <div>Unsupported field type: {field.type}</div>
    }
  }

  const helpText = enhancedAIFormAssistant.getContextualHelp(field, context)

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor={field.id}>
          {field.labelJp && (
            <>
              {field.labelJp} / {field.label}
            </>
          )}
          {!field.labelJp && field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {helpText && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p className="text-sm">{helpText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {renderField()}

      {/* Validation message */}
      {validation?.message && !isValidating && (
        <p className={cn(
          "text-sm",
          validation.type === 'error' && "text-red-500",
          validation.type === 'warning' && "text-yellow-600",
          validation.type === 'success' && "text-green-600",
          validation.type === 'info' && "text-blue-600"
        )}>
          {validation.message}
        </p>
      )}

      {/* AI Insight */}
      {validation?.aiInsight && !isValidating && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-2">
          <p className="text-sm text-blue-700 dark:text-blue-300 flex items-center gap-1">
            <Sparkles className="h-3 w-3" />
            {validation.aiInsight}
          </p>
        </div>
      )}

      {/* AI Suggestion */}
      {showSuggestion && aiSuggestion && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-2">
          <div className="flex items-center justify-between">
            <p className="text-sm text-green-700 dark:text-green-300">
              AI提案 / AI Suggestion: <strong>{aiSuggestion}</strong>
            </p>
            <Button
              size="sm"
              variant="ghost"
              onClick={acceptSuggestion}
              className="text-green-700 hover:text-green-800"
            >
              <Check className="h-4 w-4 mr-1" />
              採用 / Accept
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
