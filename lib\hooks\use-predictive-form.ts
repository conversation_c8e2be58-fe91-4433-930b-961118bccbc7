import { useState, useEffect, useCallback } from 'react'
import { 
  predictiveFormService,
  type FieldPrediction,
  type SequencePrediction,
  type PredictionPreferences
} from '@/lib/services/predictive-form-service'
import { toast } from '@/components/ui/use-toast'

export interface UsePredictiveFormOptions {
  formType: string
  onPredictionAccepted?: (fieldName: string, value: string) => void
  onSequenceAccepted?: (fields: Record<string, string>) => void
}

export function usePredictiveForm(options: UsePredictiveFormOptions) {
  const [predictions, setPredictions] = useState<FieldPrediction[]>([])
  const [sequences, setSequences] = useState<SequencePrediction[]>([])
  const [preferences, setPreferences] = useState<PredictionPreferences | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Load user preferences
    const loadPreferences = async () => {
      try {
        const prefs = await predictiveFormService.getPreferences()
        setPreferences(prefs)
      } catch (error) {
        console.error('Failed to load prediction preferences:', error)
      }
    }

    loadPreferences()
  }, [])

  const getPredictions = useCallback(
    async (
      currentField: string,
      formData: Record<string, any>,
      includeSequences = false
    ) => {
      if (!preferences?.enablePredictions) {
        return
      }

      setIsLoading(true)
      try {
        const result = await predictiveFormService.getPredictions(
          options.formType,
          currentField,
          formData,
          includeSequences
        )

        setPredictions(result.predictions)
        setSequences(result.sequences)
      } catch (error) {
        console.error('Failed to get predictions:', error)
        toast({
          title: 'エラー',
          description: '予測の取得に失敗しました。',
          variant: 'destructive'
        })
      } finally {
        setIsLoading(false)
      }
    },
    [options.formType, preferences]
  )

  const acceptPrediction = useCallback(
    async (fieldName: string, value: string) => {
      await predictiveFormService.recordFieldUsage(
        options.formType,
        fieldName,
        value,
        true
      )

      options.onPredictionAccepted?.(fieldName, value)

      toast({
        title: '予測を適用しました',
        description: `${fieldName} に ${value} を設定しました。`
      })
    },
    [options]
  )

  const acceptSequence = useCallback(
    async (sequence: SequencePrediction) => {
      await predictiveFormService.recordSequenceUsage(
        options.formType,
        sequence.fields
      )

      options.onSequenceAccepted?.(sequence.fields)

      toast({
        title: 'パターンを適用しました',
        description: `${Object.keys(sequence.fields).length} 個のフィールドを自動入力しました。`
      })
    },
    [options]
  )

  const updatePreferences = useCallback(
    async (newPreferences: Partial<PredictionPreferences>) => {
      try {
        await predictiveFormService.updatePreferences(newPreferences)
        setPreferences(prev => prev ? { ...prev, ...newPreferences } : null)
        
        toast({
          title: '設定を更新しました',
          description: '予測設定が保存されました。'
        })
      } catch (error) {
        console.error('Failed to update preferences:', error)
        toast({
          title: 'エラー',
          description: '設定の更新に失敗しました。',
          variant: 'destructive'
        })
      }
    },
    []
  )

  const clearPredictions = useCallback(() => {
    setPredictions([])
    setSequences([])
  }, [])

  return {
    predictions,
    sequences,
    preferences,
    isLoading,
    getPredictions,
    acceptPrediction,
    acceptSequence,
    updatePreferences,
    clearPredictions
  }
}
