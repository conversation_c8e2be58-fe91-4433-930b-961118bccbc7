import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { EscalationEngine } from '@/lib/services/workflow/escalation/escalation-engine';
import { NotificationService } from '@/lib/services/notifications/notification-service';
import { AuditService } from '@/lib/services/audit/audit-service';
import { WorkflowEngine } from '@/lib/services/workflow/workflow-engine';
import { SLAManager } from '@/lib/services/workflow/sla-manager';

export async function POST(request: Request) {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: staff } = await supabase
      .from('staff')
      .select('*')
      .eq('auth_id', user.id)
      .single();

    if (!staff) {
      return NextResponse.json({ error: 'Staff not found' }, { status: 404 });
    }

    const body = await request.json();
    const { requestId, ruleId, reason } = body;

    if (!requestId || !ruleId) {
      return NextResponse.json(
        { error: 'Request ID and Rule ID are required' },
        { status: 400 }
      );
    }

    // Initialize services
    const notificationService = new NotificationService(supabase);
    const auditService = new AuditService(supabase);
    const workflowEngine = new WorkflowEngine(supabase);
    const slaManager = new SLAManager(supabase);
    
    const escalationEngine = new EscalationEngine(
      supabase,
      notificationService,
      auditService,
      workflowEngine,
      slaManager
    );

    await escalationEngine.triggerManualEscalation(
      requestId,
      ruleId,
      staff.id,
      reason
    );

    return NextResponse.json({ 
      message: 'Escalation triggered successfully',
      requestId,
      ruleId 
    });
  } catch (error) {
    console.error('Error triggering manual escalation:', error);
    return NextResponse.json(
      { error: 'Failed to trigger escalation' },
      { status: 500 }
    );
  }
}
