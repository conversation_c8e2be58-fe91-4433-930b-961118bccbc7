'use client';

import { useState, useEffect } from 'react';
import { Shield, Activity, AlertTriangle, FileText, TrendingUp, Users, Lock, Database } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AuditLogViewer } from '@/components/audit/enhanced-audit-log-viewer';
import { auditService, AuditStatistics } from '@/lib/services/enhanced-audit-service';
import { useAuth } from '@/hooks/use-auth';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

export default function AuditDashboardPage() {
  const { user, profile } = useAuth();
  const [statistics, setStatistics] = useState<AuditStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState(7); // Days

  useEffect(() => {
    fetchStatistics();
  }, [timeRange]);

  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const endDate = endOfDay(new Date());
      const startDate = startOfDay(subDays(endDate, timeRange));
      
      const stats = await auditService.getStatistics(
        startDate,
        endDate,
        profile?.role?.name === 'Department Administrator' ? profile.division_id : undefined
      );
      
      setStatistics(stats);
    } catch (error) {
      console.error('Failed to fetch audit statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const statsCards = [
    {
      title: 'Total Events',
      value: statistics?.total_events || 0,
      icon: Activity,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Unique Users',
      value: statistics?.unique_users || 0,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Departments',
      value: statistics?.departments_affected || 0,
      icon: Database,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Security Events',
      value: statistics?.events_by_severity?.['CRITICAL'] || 0,
      icon: Shield,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Audit Trail Dashboard</h1>
          <p className="text-muted-foreground">
            System activity monitoring and compliance reporting
          </p>
        </div>
        <div className="flex items-center gap-2">
          <select
            className="px-3 py-2 border rounded-md"
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
          >
            <option value={1}>Last 24 hours</option>
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? '...' : stat.value.toLocaleString()}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Audit Log Viewer */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Events</TabsTrigger>
          <TabsTrigger value="security">Security Events</TabsTrigger>
          <TabsTrigger value="requests">Request Events</TabsTrigger>
          <TabsTrigger value="errors">Errors & Warnings</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <AuditLogViewer />
        </TabsContent>

        <TabsContent value="security">
          <AuditLogViewer
            showFilters={false}
            // Pass event types as props through a wrapper component
          />
        </TabsContent>

        <TabsContent value="requests">
          <AuditLogViewer
            showFilters={false}
            // Pass event types as props through a wrapper component
          />
        </TabsContent>

        <TabsContent value="errors">
          <AuditLogViewer
            showFilters={false}
            // Pass severities as props through a wrapper component
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}