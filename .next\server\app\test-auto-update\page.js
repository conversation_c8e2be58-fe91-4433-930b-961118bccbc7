(()=>{var e={};e.id=9357,e.ids=[9357],e.modules={93939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38094:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(70260),r=t(28203),i=t(25155),n=t.n(i),l=t(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c=["",{children:["test-auto-update",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98941)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-auto-update\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19611)),"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-auto-update\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-auto-update/page",pathname:"/test-auto-update",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94655:(e,s,t)=>{Promise.resolve().then(t.bind(t,98941))},31607:(e,s,t)=>{Promise.resolve().then(t.bind(t,6485))},6485:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(45512),r=t(58009),i=t(87021),n=t(97643),l=t(69193),d=t(77252),c=t(75339),o=t(61594),u=t(46583),x=t(4643),h=t(92557),p=t(97832),m=t(65518),j=t(33300),f=t(23657);function g(){let[e,s]=(0,r.useState)(!1),[t,g]=(0,r.useState)(!1),[v,y]=(0,r.useState)([]),[N,b]=(0,r.useState)([]),[w,k]=(0,r.useState)([]),{toast:_}=(0,m.d)(),C=async()=>{try{g(!0);let e=await fetch("/api/knowledge-base/update-content");if(!e.ok)throw Error("Failed to fetch update status");let s=await e.json();y(s.updateQueue),b(s.recentLogs),k(s.contentGaps)}catch(e){console.error("Error fetching update status:",e),_({title:"エラー",description:"更新ステータスの取得に失敗しました",variant:"destructive"})}finally{g(!1)}},A=async()=>{try{if(s(!0),!(await fetch("/api/knowledge-base/update-content",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})})).ok)throw Error("Failed to trigger periodic review");_({title:"成功",description:"定期レビューを開始しました"}),setTimeout(()=>C(),3e3)}catch(e){console.error("Error triggering review:",e),_({title:"エラー",description:"定期レビューの開始に失敗しました",variant:"destructive"})}finally{s(!1)}},T=e=>{switch(e){case"high":return"destructive";case"medium":return"default";case"low":return"secondary";default:return"outline"}},P=e=>{switch(e){case"approved":return(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-500"});case"rejected":return(0,a.jsx)(p.A,{className:"h-4 w-4 text-red-500"});case"pending":return(0,a.jsx)(x.A,{className:"h-4 w-4 text-yellow-500"});default:return(0,a.jsx)(o.A,{className:"h-4 w-4 text-gray-500"})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"コンテンツ自動更新管理"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"AI駆動のコンテンツ更新システムの管理"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:C,disabled:t,children:[(0,a.jsx)(h.A,{className:`h-4 w-4 mr-2 ${t?"animate-spin":""}`}),"更新"]}),(0,a.jsx)(i.$,{onClick:A,disabled:e,children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2 animate-spin"}),"処理中..."]}):"定期レビューを開始"})]})]}),(0,a.jsxs)(l.tU,{defaultValue:"queue",className:"space-y-4",children:[(0,a.jsxs)(l.j7,{children:[(0,a.jsxs)(l.Xi,{value:"queue",children:["更新キュー (",v.length,")"]}),(0,a.jsx)(l.Xi,{value:"logs",children:"最近の更新"}),(0,a.jsxs)(l.Xi,{value:"gaps",children:["コンテンツギャップ (",w.length,")"]})]}),(0,a.jsx)(l.av,{value:"queue",className:"space-y-4",children:0===v.length?(0,a.jsx)(c.Fc,{children:(0,a.jsx)(c.TN,{children:"現在、レビュー待ちの更新はありません"})}):v.map(e=>(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(n.ZB,{className:"text-lg",children:["記事ID: ",e.article_id]}),(0,a.jsx)(n.BT,{children:(0,j.G)(new Date(e.created_at),"yyyy年MM月dd日 HH:mm",{locale:f.ja})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(d.E,{variant:"outline",children:["信頼度: ",(100*e.confidence_score).toFixed(0),"%"]}),P(e.status)]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:["提案された更新: ",e.suggested_updates.length,"件"]}),(0,a.jsx)("div",{className:"space-y-1",children:e.suggested_updates.slice(0,3).map((e,s)=>(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["• ",e.description]},s))})]})})]},e.id))}),(0,a.jsx)(l.av,{value:"logs",className:"space-y-4",children:0===N.length?(0,a.jsx)(c.Fc,{children:(0,a.jsx)(c.TN,{children:"最近の更新ログはありません"})}):N.map(e=>(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(n.ZB,{className:"text-lg",children:["記事ID: ",e.article_id]}),(0,a.jsx)(n.BT,{children:(0,j.G)(new Date(e.created_at),"yyyy年MM月dd日 HH:mm",{locale:f.ja})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.E,{variant:e.approved?"default":"secondary",children:e.update_type}),e.approved?(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(x.A,{className:"h-4 w-4 text-yellow-500"})]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm",children:["変更内容: ",e.changes_made.length,"件"]}),(0,a.jsx)("div",{className:"space-y-1",children:e.changes_made.map((e,s)=>(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["• ",e]},s))})]})})]},e.id))}),(0,a.jsx)(l.av,{value:"gaps",className:"space-y-4",children:0===w.length?(0,a.jsx)(c.Fc,{children:(0,a.jsx)(c.TN,{children:"識別されたコンテンツギャップはありません"})}):w.map(e=>(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-lg",children:e.topic}),(0,a.jsxs)(n.BT,{children:["カテゴリー: ",e.suggested_category]})]}),(0,a.jsx)(d.E,{variant:T(e.priority),children:e.priority})]})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("p",{className:"text-sm",children:["検索クエリ数: ",e.query_count,"回"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[(0,j.G)(new Date(e.created_at),"yyyy年MM月dd日",{locale:f.ja}),"に識別"]})]})]},e.id))})]})]})}var v=t(48859),y=t(67418),N=t(41441),b=t(46904),w=t(37133),k=t(43433);function _({articleId:e,articleTitle:s}){let[t,l]=(0,r.useState)(null),[d,c]=(0,r.useState)(""),[o,u]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),{user:j}=(0,k.A)(),{toast:f}=(0,m.d)(),g=async s=>{if(!j){f({title:"ログインが必要です",description:"フィードバックを送信するにはログインしてください",variant:"destructive"});return}try{u(!0),l(s);let{data:t}=await w.N.from("staff").select("id").eq("auth_id",j.id).single();if(!t)throw Error("Staff record not found");let{error:a}=await w.N.from("kb_article_feedback").insert({article_id:e,user_id:t.id,feedback_type:s,suggestion:d||null,created_at:new Date().toISOString()});if(a)throw a;"helpful"!==s&&await fetch("/api/knowledge-base/update-content",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({articleId:e,updateType:"feedback"})}),p(!0),f({title:"フィードバックを送信しました",description:"ご協力ありがとうございます"}),setTimeout(()=>{p(!1),l(null),c("")},5e3)}catch(e){console.error("Error submitting feedback:",e),f({title:"エラー",description:"フィードバックの送信に失敗しました",variant:"destructive"})}finally{u(!1)}};return h?(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"py-8 text-center",children:[(0,a.jsx)("div",{className:"text-green-500 mb-2",children:(0,a.jsx)(b.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"フィードバックありがとうございました！"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"あなたのフィードバックは記事の改善に活用されます"})]})}):(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"この記事は役に立ちましたか？"}),(0,a.jsx)(n.BT,{children:"フィードバックをお寄せください"})]}),(0,a.jsx)(n.Wu,{className:"space-y-4",children:t?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm",children:"改善のためのご提案があればお聞かせください（任意）"}),(0,a.jsx)(v.T,{placeholder:"どのように改善できるか教えてください...",value:d,onChange:e=>c(e.target.value),rows:3}),(0,a.jsxs)("div",{className:"flex gap-2 justify-end",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>{l(null),c("")},disabled:o,children:"キャンセル"}),(0,a.jsx)(i.$,{onClick:()=>g(t),disabled:o,children:"送信"})]})]}):(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>g("helpful"),disabled:o,children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"はい"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>l("not_helpful"),disabled:o,children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"いいえ"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>l("outdated"),disabled:o,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"古い情報"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>l("incorrect"),disabled:o,children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"誤り"]})]})})]})}function C(){return(0,a.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Knowledge Base Auto-Update System Test"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"AI-powered content auto-updating and feedback system demonstration"})]}),(0,a.jsxs)(l.tU,{defaultValue:"manager",className:"space-y-4",children:[(0,a.jsxs)(l.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(l.Xi,{value:"manager",children:"Update Manager"}),(0,a.jsx)(l.Xi,{value:"feedback",children:"Article Feedback"})]}),(0,a.jsx)(l.av,{value:"manager",children:(0,a.jsx)(g,{})}),(0,a.jsxs)(l.av,{value:"feedback",className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Article Feedback Component Demo"})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"This demonstrates how users can provide feedback on knowledge base articles. The feedback triggers automatic content update analysis."}),(0,a.jsx)(_,{articleId:"123e4567-e89b-12d3-a456-426614174000",articleTitle:"How to Reset Your Password"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"How the Auto-Update System Works"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"1. Feedback Collection"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Users provide feedback on articles (helpful, not helpful, outdated, incorrect) with optional suggestions."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"2. AI Analysis"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"When negative feedback accumulates, the system uses AI to analyze the article content, feedback, and search patterns to suggest improvements."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"3. Confidence Scoring"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Each suggested update receives a confidence score. High-confidence updates (≥70%) are applied automatically, while lower scores require manual review."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"4. Content Gap Detection"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"The system analyzes unsuccessful searches to identify topics that need new articles, helping maintain comprehensive coverage."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"5. Periodic Reviews"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Articles that haven't been updated in 30 days automatically enter the review queue to ensure content stays current."})]})]})]})]})]})]})}},98941:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\itsync-project\\\\Claude\\\\Demo1\\\\app\\\\test-auto-update\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\itsync-project\\Claude\\Demo1\\app\\test-auto-update\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8096,2076],()=>t(38094));module.exports=a})();