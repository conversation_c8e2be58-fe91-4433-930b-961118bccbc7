# Supabase MCP Server - Demo Usage Examples

Once you've configured your personal access token, here are examples of how to use the Supabase MCP server tools:

## Project Management Tools

### List all your projects
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "list_projects"
- arguments: {}
```

### Get details for a specific project
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "get_project"
- arguments: {
    "project_id": "your-project-id"
  }
```

### List organizations
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "list_organizations"
- arguments: {}
```

## Database Operations

### List tables in your database
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "list_tables"
- arguments: {
    "project_ref": "your-project-ref",
    "schemas": ["public"]
  }
```

### Execute SQL query
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "execute_sql"
- arguments: {
    "project_ref": "your-project-ref",
    "sql": "SELECT * FROM your_table LIMIT 10;"
  }
```

### Apply a migration
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "apply_migration"
- arguments: {
    "project_ref": "your-project-ref",
    "sql": "CREATE TABLE new_table (id SERIAL PRIMARY KEY, name TEXT);"
  }
```

## Edge Functions

### List edge functions
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "list_edge_functions"
- arguments: {
    "project_ref": "your-project-ref"
  }
```

### Deploy an edge function
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "deploy_edge_function"
- arguments: {
    "project_ref": "your-project-ref",
    "function_name": "hello-world",
    "code": "export default async (req) => new Response('Hello World!')"
  }
```

## Configuration Tools

### Get project URL
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "get_project_url"
- arguments: {
    "project_ref": "your-project-ref"
  }
```

### Get anonymous key
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "get_anon_key"
- arguments: {
    "project_ref": "your-project-ref"
  }
```

## Development Tools

### Generate TypeScript types
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "generate_typescript_types"
- arguments: {
    "project_ref": "your-project-ref"
  }
```

## Monitoring

### Get logs
```
use_mcp_tool:
- server_name: "github.com/supabase-community/supabase-mcp"
- tool_name: "get_logs"
- arguments: {
    "project_ref": "your-project-ref",
    "service": "postgres",
    "limit": 100
  }
```

## Notes

- Replace `your-project-ref` with your actual project reference ID
- Replace `your-project-id` with your actual project ID
- The project reference can be found in your Supabase project settings
- Some operations require appropriate permissions in your Supabase account
- In read-only mode, write operations will be restricted