/**
 * Anthropic Client Configuration
 * Centralized Anthropic API client setup
 */

// Mock Anthropic client for development
// In production, replace with actual Anthropic SDK

interface AnthropicMessage {
  role: 'user' | 'assistant'
  content: string
}

interface AnthropicRequest {
  model: string
  max_tokens: number
  messages: AnthropicMessage[]
  temperature?: number
  top_p?: number
  top_k?: number
}

interface AnthropicUsage {
  input_tokens: number
  output_tokens: number
}

interface AnthropicContent {
  type: 'text'
  text: string
}

interface AnthropicResponse {
  id: string
  type: 'message'
  role: 'assistant'
  content: AnthropicContent[]
  model: string
  stop_reason: string
  stop_sequence: null
  usage: AnthropicUsage
}

class MockAnthropicClient {
  messages = {
    create: async (request: AnthropicRequest): Promise<AnthropicResponse> => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500))
      
      // Simulate occasional failures (5% rate)
      if (Math.random() < 0.05) {
        throw new Error('Anthropic API Error: Rate limit exceeded')
      }

      // Generate mock response
      const inputTokens = Math.floor(request.messages[0].content.length / 4)
      const outputTokens = Math.floor(Math.random() * 500) + 50
      
      return {
        id: `msg_${Math.random().toString(36).substr(2, 9)}`,
        type: 'message',
        role: 'assistant',
        content: [{
          type: 'text',
          text: `Mock response from ${request.model} for: "${request.messages[0].content.substring(0, 50)}..."`
        }],
        model: request.model,
        stop_reason: 'end_turn',
        stop_sequence: null,
        usage: {
          input_tokens: inputTokens,
          output_tokens: outputTokens
        }
      }
    }
  }
}

// Export mock client for development
export const anthropicClient = new MockAnthropicClient()

// In production, use this instead:
// import Anthropic from '@anthropic-ai/sdk'
// export const anthropicClient = new Anthropic({
//   apiKey: process.env.ANTHROPIC_API_KEY,
// })
