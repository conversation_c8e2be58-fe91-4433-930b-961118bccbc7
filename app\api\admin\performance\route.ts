/**
 * Performance Dashboard API
 * Provides comprehensive performance metrics and analytics
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { metrics } from '@/lib/monitoring/metrics'
import { getCacheService } from '@/lib/cache/cache-service'
import { getQueryOptimizer } from '@/lib/database/query-optimizer'
import { getAIOptimizer } from '@/lib/ai/ai-performance-optimizer'
import { getJobProcessor } from '@/lib/jobs/job-processor'
import { BundleAnalyzer } from '@/lib/performance/bundle-analyzer'
import { getWebVitalsMonitor } from '@/lib/performance/web-vitals-monitor'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Check authentication and authorization
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (!profile || !['admin', 'system_admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const url = new URL(request.url)
    const section = url.searchParams.get('section') || 'overview'

    let data: any = {}

    switch (section) {
      case 'overview':
        data = await getOverviewMetrics()
        break
      case 'database':
        data = await getDatabaseMetrics()
        break
      case 'cache':
        data = await getCacheMetrics()
        break
      case 'ai':
        data = await getAIMetrics()
        break
      case 'jobs':
        data = await getJobMetrics()
        break
      case 'frontend':
        data = await getFrontendMetrics()
        break
      case 'bundle':
        data = await getBundleMetrics()
        break
      default:
        return NextResponse.json({ error: 'Invalid section' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      section,
      data,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Performance API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getOverviewMetrics() {
  const cache = getCacheService()
  const queryOptimizer = getQueryOptimizer()
  const aiOptimizer = getAIOptimizer()
  const jobProcessor = getJobProcessor()

  const [
    systemMetrics,
    cacheStats,
    dbMetrics,
    aiMetrics,
    jobStats
  ] = await Promise.all([
    getSystemMetrics(),
    cache.getStats(),
    queryOptimizer.getPerformanceMetrics(),
    aiOptimizer.getPerformanceMetrics(),
    jobProcessor.getStats()
  ])

  return {
    system: systemMetrics,
    cache: {
      hitRate: cacheStats.redis?.hitRate || 0,
      connected: cacheStats.redis?.connected || false,
      memoryUsage: cacheStats.memory?.size || 0
    },
    database: {
      slowQueries: dbMetrics.slowQueries?.count || 0,
      avgQueryTime: calculateAverageQueryTime(),
      connectionPool: dbMetrics.connectionPool
    },
    ai: {
      requestsToday: aiMetrics.costs?.today || 0,
      costToday: aiMetrics.costs?.today || 0,
      cacheHitRate: aiMetrics.cache?.hitRate || 0
    },
    jobs: {
      pending: jobStats.pending,
      processing: jobStats.processing,
      failed: jobStats.failed
    },
    alerts: await getPerformanceAlerts()
  }
}

async function getDatabaseMetrics() {
  const queryOptimizer = getQueryOptimizer()
  const dbMetrics = await queryOptimizer.getPerformanceMetrics()
  
  return {
    slowQueries: dbMetrics.slowQueries?.queries || [],
    queryStats: {
      total: getMetricValue('database.queries'),
      slow: getMetricValue('database.slow_queries'),
      errors: getMetricValue('database.errors'),
      avgTime: calculateAverageQueryTime()
    },
    connectionPool: dbMetrics.connectionPool,
    tableStats: await getTableStats(),
    indexUsage: await getIndexUsage(),
    recommendations: generateDatabaseRecommendations(dbMetrics)
  }
}

async function getCacheMetrics() {
  const cache = getCacheService()
  const stats = await cache.getStats()
  
  return {
    redis: stats.redis,
    memory: stats.memory,
    tags: stats.tags,
    performance: {
      hitRate: stats.redis?.hitRate || 0,
      avgResponseTime: getMetricValue('cache.get_time'),
      operations: {
        gets: getMetricValue('cache.gets'),
        sets: getMetricValue('cache.sets'),
        deletes: getMetricValue('cache.deletes'),
        hits: getMetricValue('cache.hits'),
        misses: getMetricValue('cache.misses')
      }
    },
    recommendations: generateCacheRecommendations(stats)
  }
}

async function getAIMetrics() {
  const aiOptimizer = getAIOptimizer()
  const aiMetrics = await aiOptimizer.getPerformanceMetrics()
  const costAnalytics = aiOptimizer.getCostAnalytics()
  
  return {
    costs: costAnalytics,
    performance: {
      avgResponseTime: getMetricValue('ai.response_time'),
      cacheHitRate: aiMetrics.cache?.hitRate || 0,
      requestsToday: getMetricValue('ai.requests'),
      errorsToday: getMetricValue('ai.errors')
    },
    queue: aiMetrics.queue,
    usage: {
      byProvider: costAnalytics.byProvider,
      byModel: costAnalytics.byModel,
      trends: costAnalytics.trends
    },
    recommendations: generateAIRecommendations(aiMetrics, costAnalytics)
  }
}

async function getJobMetrics() {
  const jobProcessor = getJobProcessor()
  const stats = await jobProcessor.getStats()
  
  return {
    queue: stats,
    performance: {
      avgProcessingTime: getMetricValue('jobs.processing_time'),
      completionRate: calculateJobCompletionRate(),
      throughput: getMetricValue('jobs.completed')
    },
    jobTypes: getJobTypeStats(),
    recommendations: generateJobRecommendations(stats)
  }
}

async function getFrontendMetrics() {
  const webVitalsMonitor = getWebVitalsMonitor()
  const report = webVitalsMonitor.getReport()
  
  return {
    webVitals: report,
    performance: {
      pageLoadTime: getMetricValue('frontend.page_load_time'),
      resourceLoadTime: getMetricValue('frontend.resource_load_time'),
      jsErrors: getMetricValue('frontend.js_errors')
    },
    recommendations: report.summary.recommendations
  }
}

async function getBundleMetrics() {
  const bundleAnalyzer = new BundleAnalyzer()
  const analysis = await bundleAnalyzer.analyzeBundles()
  
  return {
    bundles: analysis.stats,
    metrics: analysis.metrics,
    recommendations: analysis.recommendations,
    trends: await getBundleTrends()
  }
}

// Helper functions
function getSystemMetrics() {
  const memoryUsage = process.memoryUsage()
  const uptime = process.uptime()
  
  return {
    memory: {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      rss: Math.round(memoryUsage.rss / 1024 / 1024) // MB
    },
    uptime: Math.round(uptime),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch
  }
}

function getMetricValue(metricName: string): number {
  const metricData = metrics.getMetrics(metricName)
  if (!metricData || metricData.length === 0) return 0
  
  const recent = metricData.filter(m => m.timestamp > Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
  return recent.reduce((sum, m) => sum + m.value, 0)
}

function calculateAverageQueryTime(): number {
  const queryTimes = metrics.getMetrics('database.query_time')
  if (!queryTimes || queryTimes.length === 0) return 0
  
  const recent = queryTimes.filter(m => m.timestamp > Date.now() - 60 * 60 * 1000) // Last hour
  if (recent.length === 0) return 0
  
  const sum = recent.reduce((total, m) => total + m.value, 0)
  return Math.round(sum / recent.length)
}

function calculateJobCompletionRate(): number {
  const completed = getMetricValue('jobs.completed')
  const failed = getMetricValue('jobs.failed')
  const total = completed + failed
  
  return total > 0 ? Math.round((completed / total) * 100) : 100
}

function getJobTypeStats(): any {
  // This would aggregate job statistics by type
  return {
    'backup:create': { completed: 5, failed: 0, avgTime: 30000 },
    'ai:batch_process': { completed: 25, failed: 1, avgTime: 5000 },
    'email:send': { completed: 100, failed: 2, avgTime: 1000 }
  }
}

async function getTableStats(): Promise<any> {
  // This would query database for table statistics
  return {
    tickets: { rows: 1500, size: '2.5MB', lastVacuum: '2024-01-01' },
    users: { rows: 250, size: '500KB', lastVacuum: '2024-01-01' },
    organizations: { rows: 10, size: '50KB', lastVacuum: '2024-01-01' }
  }
}

async function getIndexUsage(): Promise<any> {
  // This would query database for index usage statistics
  return {
    tickets_status_idx: { usage: 95, scans: 1000 },
    tickets_created_at_idx: { usage: 80, scans: 500 },
    users_email_idx: { usage: 100, scans: 200 }
  }
}

async function getBundleTrends(): Promise<any> {
  // This would load historical bundle size data
  return {
    '2024-01-01': { size: 500000, gzipSize: 150000 },
    '2024-01-02': { size: 505000, gzipSize: 152000 },
    '2024-01-03': { size: 498000, gzipSize: 149000 }
  }
}

async function getPerformanceAlerts(): Promise<any[]> {
  const alerts = []
  
  // Check for performance issues
  const avgQueryTime = calculateAverageQueryTime()
  if (avgQueryTime > 1000) {
    alerts.push({
      type: 'warning',
      category: 'database',
      message: `Average query time is ${avgQueryTime}ms (threshold: 1000ms)`,
      severity: 'medium'
    })
  }
  
  const memoryUsage = process.memoryUsage()
  const memoryUsedMB = memoryUsage.heapUsed / 1024 / 1024
  if (memoryUsedMB > 500) {
    alerts.push({
      type: 'warning',
      category: 'system',
      message: `High memory usage: ${Math.round(memoryUsedMB)}MB`,
      severity: 'high'
    })
  }
  
  return alerts
}

function generateDatabaseRecommendations(metrics: any): string[] {
  const recommendations = []
  
  if (metrics.slowQueries?.count > 10) {
    recommendations.push('Consider optimizing slow queries or adding indexes')
  }
  
  if (metrics.connectionPool?.active > metrics.connectionPool?.total * 0.8) {
    recommendations.push('Consider increasing database connection pool size')
  }
  
  return recommendations
}

function generateCacheRecommendations(stats: any): string[] {
  const recommendations = []
  
  if (stats.redis?.hitRate < 80) {
    recommendations.push('Cache hit rate is low, consider increasing TTL or cache more data')
  }
  
  if (!stats.redis?.connected) {
    recommendations.push('Redis is not connected, check Redis server status')
  }
  
  return recommendations
}

function generateAIRecommendations(metrics: any, costs: any): string[] {
  const recommendations = []
  
  if (costs.today > 10) {
    recommendations.push('Daily AI costs are high, consider implementing more aggressive caching')
  }
  
  if (metrics.cache?.hitRate < 50) {
    recommendations.push('AI cache hit rate is low, consider longer cache TTL for similar requests')
  }
  
  return recommendations
}

function generateJobRecommendations(stats: any): string[] {
  const recommendations = []
  
  if (stats.pending > 100) {
    recommendations.push('High number of pending jobs, consider increasing worker concurrency')
  }
  
  if (stats.failed > stats.completed * 0.1) {
    recommendations.push('High job failure rate, check job handlers and error handling')
  }
  
  return recommendations
}
